#
# NOTE: The order of the paths in this file is important for Sentry alerting. Sentry will assign
# ownership based on the _last_ matching rule in the CODEOWNERS list that matches _any_ part of the
# stack trace. This means that CODEOWNERS needs to be sorted from more generic to more specific.
#
# For more information, see the Sentry documentation:
# https://docs.sentry.io/product/issues/ownership-rules/#evaluation-flow
# https://github.com/getsentry/sentry/issues/47216
#

# Python Guild
# By default, all Python code is owned by the Python Guild if it has no other owner specified
*/ @Apella-Technology/python

# Platform Services core services
# Note: these are separate for Sentry ownership matching reasons (see comment at top of file)
api_server/logging/ @Apella-Technology/platform-services
api_server/tracing/ @Apella-Technology/platform-services
api_server/app_service_provider/ @Apella-Technology/platform-services
api_server/graphql/ @Apella-Technology/platform-services
auth/ @Apella-Technology/platform-services
databases/ @Apella-Technology/platform-services
pubsub/ @Apella-Technology/platform-services
scripts/ @Apella-Technology/platform-services

# Python Guild
alembic/ @Apella-Technology/python
apella_cli/ @Apella-Technology/python
apella_cloud_api/ @Apella-Technology/python
api_server/server/ @Apella-Technology/python
api_server/services/launch_darkly/ @Apella-Technology/python
config/ @Apella-Technology/python
locust/ @Apella-Technology/python
mocks/ @Apella-Technology/python
tests/ @Apella-Technology/python
tests_component/ @Apella-Technology/python
tools/ @Apella-Technology/python
utils/ @Apella-Technology/python

# EHR

## Anesthesia
api_server/services/anesthesia/ @Apella-Technology/ehr
tests_component/anesthesia/ @Apella-Technology/ehr

## Case
api_server/services/case/ @Apella-Technology/ehr
tests_component/case/ @Apella-Technology/ehr
tests/api_server/services/case/ @Apella-Technology/ehr

## Case Derived Properties
api_server/services/case_derived_properties/ @Apella-Technology/ehr
tests/api_server/services/case_derived_properties/ @Apella-Technology/ehr
tests_component/case_derived_properties/ @Apella-Technology/ehr

## EHR Interfaces
api_server/services/ehr_interfaces/ @Apella-Technology/ehr
tests/api_server/services/ehr_interfaces/ @Apella-Technology/ehr
tests_component/ehr_interfaces/ @Apella-Technology/ehr

## Observations
api_server/services/observations/ @Apella-Technology/ehr
tests/api_server/services/observations/ @Apella-Technology/ehr
tests_component/observations/ @Apella-Technology/ehr

## Patients
api_server/services/patients/ @Apella-Technology/ehr
tests/api_server/services/patient/ @Apella-Technology/ehr

## Procedures
api_server/services/procedures/ @Apella-Technology/ehr
tests_component/procedures/ @Apella-Technology/ehr

## Service Lines
api_server/services/service_lines/ @Apella-Technology/ehr
tests_component/service_lines/ @Apella-Technology/ehr

## Staff
api_server/services/staff/ @Apella-Technology/ehr
api_server/services/staff_role/ @Apella-Technology/ehr
tests/api_server/services/staff/ @Apella-Technology/ehr
tests_component/staff/ @Apella-Technology/ehr

## Mock EHR Scribe
mock_ehr_scribe_service/ @Apella-Technology/ehr

# ORca

## Annotation Tasks
api_server/services/annotation_tasks/ @Apella-Technology/orca
tests/api_server/services/annotation_task/ @Apella-Technology/orca
tests_component/annotation_tasks/ @Apella-Technology/orca

## Custom Phase Config
api_server/services/custom_phase_config/ @Apella-Technology/orca
tests/api_server/services/custom_phase_config/ @Apella-Technology/orca
tests_component/custom_phase_config/ @Apella-Technology/orca

## Day of Week Schema
api_server/services/day_of_week_schema/ @Apella-Technology/orca

## First Case Config
api_server/services/first_case_config/ @Apella-Technology/orca
tests_component/first_case_config/ @Apella-Technology/orca

## Highlights
api_server/services/highlights/ @Apella-Technology/orca
tests/api_server/services/highlights/ @Apella-Technology/orca
tests_component/highlights/ @Apella-Technology/orca

## Measurement Periods
api_server/services/measurement_periods/ @Apella-Technology/orca
tests/api_server/services/measurement_periods/ @Apella-Technology/orca
tests_component/measurement_periods/ @Apella-Technology/orca

## Terminal Cleans
api_server/services/terminal_cleans/ @Apella-Technology/orca
tests_component/terminal_cleans/ @Apella-Technology/orca

## User Filter Views
api_server/services/user_filter_views/ @Apella-Technology/orca
tests_component/user_filter_views/ @Apella-Technology/orca

# ORion

## Available Time Slot
api_server/services/available_time_slot/ @Apella-Technology/orion
tests/api_server/services/available_time_slot/ @Apella-Technology/orion
tests_component/available_time_slot/ @Apella-Technology/orion

## Block
api_server/services/block/ @Apella-Technology/orion
tests/api_server/services/block/ @Apella-Technology/orion
tests_component/block/ @Apella-Technology/orion

## Block Utilization
api_server/services/block_utilization/ @Apella-Technology/orion
tests_component/block_utilization/ @Apella-Technology/orion

## Case Duration
api_server/services/case_duration/ @Apella-Technology/orion
tests/api_server/services/case_duration/ @Apella-Technology/orion
tests_component/case_duration/ @Apella-Technology/orion

## Case to Block
api_server/services/case_to_block/ @Apella-Technology/orion
tests_component/case_to_block/ @Apella-Technology/orion

## Closures
api_server/services/closures/ @Apella-Technology/orion
tests_component/closures/ @Apella-Technology/orion

## Prime Time
api_server/services/prime_time/ @Apella-Technology/orion
tests_component/prime_time/ @Apella-Technology/orion

## Send Grid
api_server/services/send_grid/ @Apella-Technology/orion

## Schedule Assistant Email
api_server/services/schedule_assistant_email/ @Apella-Technology/orion
tests/api_server/services/schedule_assistant_email/ @Apella-Technology/orion
tests/api_server/services/schedule_assistant_email_builder/ @Apella-Technology/orion

# Platform Services

## Metrics
api_server/metrics/ @Apella-Technology/platform-services
api_server/services/metrics/ @Apella-Technology/platform-services

## Camera
api_server/services/camera/ @Apella-Technology/platform-services
tests/api_server/services/camera/ @Apella-Technology/platform-services
tests_component/camera/ @Apella-Technology/platform-services

## Cluster
api_server/services/cluster/ @Apella-Technology/platform-services
tests_component/cluster/ @Apella-Technology/platform-services

## Healthz
api_server/services/healthz/ @Apella-Technology/platform-services

## Media
api_server/services/media/ @Apella-Technology/platform-services
tests/api_server/services/media/ @Apella-Technology/platform-services
tests_component/media/ @Apella-Technology/platform-services
mock_media_asset_service/ @Apella-Technology/platform-services

## Meta
api_server/services/meta/ @Apella-Technology/platform-services

## Organization
api_server/services/organization/ @Apella-Technology/platform-services
tests/api_server/services/organization/ @Apella-Technology/platform-services
tests_component/organization/ @Apella-Technology/platform-services

## Room
api_server/services/room/ @Apella-Technology/platform-services
tests/api_server/services/room/ @Apella-Technology/platform-services
tests_component/room/ @Apella-Technology/platform-services

## Site
api_server/services/site/ @Apella-Technology/platform-services
tests/api_server/services/site/ @Apella-Technology/platform-services
tests_component/site/ @Apella-Technology/platform-services

## Users
api_server/services/users/ @Apella-Technology/platform-services
tests/api_server/services/users/ @Apella-Technology/platform-services
tests_component/users/ @Apella-Technology/platform-services

## Utils
api_server/services/utils/ @Apella-Technology/platform-services

## Auth0
mock_auth0/ @Apella-Technology/platform-services
tests/mock_auth0/ @Apella-Technology/platform-services
tests_component/auth0/ @Apella-Technology/platform-services

## Logging
tests/api_server/logging/ @Apella-Technology/platform-services
tests_component/logging/ @Apella-Technology/platform-services

## App Services Provider
tests/api_server/services/app_services_provider/ @Apella-Technology/platform-services

## Mock GCS
tests_component/mock_gcs/ @Apella-Technology/platform-services

# Real Time

## Apella Case
api_server/services/apella_case/ @Apella-Technology/realtime
tests/api_server/services/apella_case/ @Apella-Technology/realtime
tests_component/apella_cases/ @Apella-Technology/realtime

## Boards
api_server/services/boards/ @Apella-Technology/realtime
tests_component/boards/ @Apella-Technology/realtime

## Case Activity
api_server/services/case_activity/ @Apella-Technology/realtime
tests/api_server/services/case_activity/ @Apella-Technology/realtime

## Case Labels
api_server/services/case_labels/ @Apella-Technology/realtime
tests_component/case_labels/ @Apella-Technology/realtime

## Contact Information
api_server/services/contact_information/ @Apella-Technology/realtime
tests/api_server/services/contact_information/ @Apella-Technology/realtime
tests_component/contact_information/ @Apella-Technology/realtime

## Plan
api_server/services/plan/ @Apella-Technology/realtime
tests/api_server/services/plan/ @Apella-Technology/realtime
tests_component/plan/ @Apella-Technology/realtime

## Staffing Needs
api_server/services/staffing_needs/ @Apella-Technology/realtime
tests_component/staffing_needs/ @Apella-Technology/realtime

## Turnover
api_server/services/turnover/ @Apella-Technology/realtime
tests/api_server/services/turnover/ @Apella-Technology/realtime
tests_component/turnover/ @Apella-Technology/realtime

## Notification Daemon
notification_daemon/ @Apella-Technology/realtime

# DS / ML

## Case Forecasts
api_server/services/case_forecasts/ @Apella-Technology/ds-ml
tests/api_server/services/case_forecasts/ @Apella-Technology/ds-ml
tests_component/case_forecasts/ @Apella-Technology/ds-ml

## Events
api_server/services/events/ @Apella-Technology/ds-ml
tests/api_server/services/events/ @Apella-Technology/ds-ml
tests_component/events/ @Apella-Technology/ds-ml

## Objects
api_server/services/objects/ @Apella-Technology/ds-ml

## Phases
api_server/services/phases/ @Apella-Technology/ds-ml
tests/api_server/services/phases/ @Apella-Technology/ds-ml
tests_component/phases/ @Apella-Technology/ds-ml
