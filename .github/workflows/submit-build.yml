name: Submit-Build

on:
  workflow_call:
    inputs:
      version:
        description: Release version to deploy
        required: true
        type: string
      CONTAINER_REGISTRY:
        required: true
        description: Path for package registry
        type: string
      GITHUB_RUNNER_ENV:
        required: true
        description: one of ('prod', 'nonprod')
        type: string

jobs:
  Submit-Build:
    runs-on:
      - self-hosted
      - ${{ inputs.GITHUB_RUNNER_ENV }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: |
            ${{inputs.CONTAINER_REGISTRY}}/api-server
          tags: |
            type=edge,branch=$repo.default_branch
            type=ref,event=branch
            type=ref,event=pr
            type=ref,event=tag
            type=sha
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}

      # Follow up action to swap to only using the prod docker registry, and clean up the versioning methods
      - name: Inject version
        run: printf "${{ inputs.version }}" > version

      - name: Submit build
        run: |
          # Unfortunately this --substitutions line cannot be broken into multiple lines or arguments
          # Use cloudbuild.yaml to pass necessary environment variables per environment.
          gcloud builds submit --config=cloudbuild.yaml \
              --substitutions=_VERSION="${{ inputs.version }}",_CONTAINER_REGISTRY="${{ inputs.CONTAINER_REGISTRY }}"

      - name: Add metadata tags
        env:
          TAGS: ${{ steps.meta.outputs.tags }}
          VERSION: ${{ steps.meta.outputs.version }}
        run: |
          buildVersion="${{ inputs.CONTAINER_REGISTRY }}/api-server:${{ inputs.version }}"
          while read -r tag; do
            gcloud artifacts docker tags add $buildVersion $tag
          done <<< "$TAGS"
