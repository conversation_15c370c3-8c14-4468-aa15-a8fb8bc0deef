SHELL := /bin/bash

run:
	poetry run python main.py

test: generate-client-types
	poetry run python -m pytest --timeout=300 tests

test-cov: generate-client-types
	set -o pipefail && poetry run python -m pytest --timeout=300 --junitxml=pytest.xml --cov=apella_cloud_api --cov=api_server --cov=auth tests | tee pytest-coverage.txt

green := $(shell tput setaf 2)
normal := $(shell tput sgr0)
magenta := $(shell tput setaf 5)
tempDir := $(shell mktemp -d)

print-auth-message:
	@printf "\n$(green)If you run into auth issues, ensure you are logged in to GCP: 'gcloud auth application-default login' $(normal)\n"
print-starting-service:
	@printf "\n$(magenta)Starting service...$(normal)\n"
print-updating-db:
	@printf "\n$(magenta)Updating database...$(normal)\n"
print-prometheus-metrics-dir:
	@printf "\nUsing $(PROMETHEUS_MULTIPROC_DIR) as prometheus metrics' working directory$(normal)\n"

# Since each command in a makefile is run in a child subprocess, environment variables need
# to be added as separate directives. In the future, we should consider upgrading Make to 3.82 and
# using `.ONESHELL:`
# https://stackoverflow.com/questions/********/how-can-i-pass-env-variables-between-make-targets
# API Server
run-dev: print-auth-message
run-dev: export PORT = 8080
run-dev: export INTROSPECTION_ENABLE = True
run-dev: export GCP_PROJECT = dev-web-api-72f12b
run-dev: export TENSORFLOW_STORAGE_BUCKET = dev-public-tensorflow-models
run-dev: export TARGET_SERVICE_ACCOUNT = <EMAIL>
run-dev: export BIGQUERY_OBJECTS_TABLE := dev-ml-794354.dev_realtime.image_processing_output
# These dev tables are empty, use the prod table to be able to test locally.
run-dev: export BIGQUERY_STAFF_SITE_FREQUENCY_TABLE := prod-data-platform-027529.gold.staff_site_frequency_latest
run-dev: export BIGQUERY_SCHEDULE_ASSISTANT_SURGEON_PROCEDURES_TABLE := prod-data-platform-027529.gold.schedule_assistant_surgeon_procedure_combos_latest
run-dev: export BIGQUERY_CAMERA_PATIENT_BLUR_STATIC_TABLE := prod-data-platform-027529.gold.patient_blur_static_bounding_box_latest
run-dev: export BIGQUERY_BLOCK_TIME_CASES := dev-data-platform-439b4c.gold.block_time_cases_latest
# Since we are using the prod table, we need to override the org_id for the Case Duration page.
# Ideally, the dev environment would contain data, and we would not need to use this.
run-dev: export CASE_DURATION_ORG_OVERRIDE := houston_methodist
run-dev: export CASE_DURATION_PREDICTION_BAYESIAN_ENDPOINT := http://localhost:9980/v3/standalone/sample
run-dev: export CASE_DURATION_TURNOVER_ENDPOINT := http://localhost:9998/predict
run-dev: export CASE_DURATION_SLOTTING_ENDPOINT := http://localhost:9997/suggest
# Auth0 Auth
run-dev: export AUTH0_CLIENT_ID_KEY := dev-api-server-auth0-client-id
run-dev: export AUTH0_CLIENT_SECRET_KEY := dev-api-server-auth0-client-secret
run-dev: export AUTH0_URL := https://apella-dev.us.auth0.com
run-dev: export AUTH0_DEFAULT_DOMAIN := https://apella-dev.us.auth0.com
run-dev: export API_GATEWAY_URL := https://api.dev.apella.io
run-dev: export MEDIA_ASSET_SERVICE_ENDPOINT := https://media-asset-service.dev.internal.apella.io
run-dev: export TWILIO_ACCOUNT_SID_NAME := dev-twilio-account-sid
run-dev: export TWILIO_AUTH_TOKEN_NAME := dev-twilio-auth-token
run-dev: export TWILIO_PHONE_NUMBER_NAME := dev-twilio-from-number
run-dev: export SENDGRID_API_KEY := dev-sendgrid-api-key
# prometheus-client multiprocess setup
run-dev: export PROMETHEUS_MULTIPROC_DIR := $(tempDir)
run-dev: print-prometheus-metrics-dir
run-dev: export LAUNCH_DARKLY_SDK_KEY_NAME := dev-launch-darkly-sdk-key
# GCP Postgres SQL
run-dev: export SQL_USER := api-server
run-dev: export SQL_DATABASE := postgres
run-dev: export SQL_PASSWORD_SECRET := projects/dev-web-api-72f12b/secrets/dev-api-user-password
run-dev: export SQL_PORT := 3306
run-dev: export SQL_HOST := localhost
run-dev: export ENABLE_IMAGE_UPLOAD_SUBSCRIBER=false
run-dev: export RUN_MODE=DEV_MODE_WEB_SERVER
run-dev:
	@printf "$(green)If you run into sql connection issues, ensure the GCP tunnel is running: 'dev tunnel'$(normal)\n"
	@make print-starting-service
	poetry run fastapi dev main.py --port 8080

# API Server
run-local: print-auth-message
run-local: export PORT=8080
run-local: export INTROSPECTION_ENABLE=True
run-local: export GCP_PROJECT=dev-web-api-72f12b
# Auth0 Auth
run-local: export AUTH0_CLIENT_ID_KEY=dev-api-server-auth0-client-id
run-local: export AUTH0_CLIENT_SECRET_KEY=dev-api-server-auth0-client-secret
run-local: export AUTH0_URL=https://apella-dev.us.auth0.com
run-local: export AUTH0_DEFAULT_DOMAIN := https://apella-dev.us.auth0.com
run-local: export API_GATEWAY_URL=https://api.dev.apella.io
run-local: export TWILIO_ACCOUNT_SID_NAME := dev-twilio-account-sid
run-local: export TWILIO_AUTH_TOKEN_NAME := dev-twilio-auth-token
run-local: export TWILIO_PHONE_NUMBER_NAME := dev-twilio-from-number
run-local: export SENDGRID_API_KEY := dev-sendgrid-api-key
# prometheus-client multiprocess setup
run-local: export PROMETHEUS_MULTIPROC_DIR := $(tempDir)
run-local: print-prometheus-metrics-dir
run-local: export LAUNCH_DARKLY_SDK_KEY_NAME := dev-launch-darkly-sdk-key
# Local docker SQL
run-local: start-postgres-local
run-local: export SQL_USER=postgres
run-local: export SQL_DATABASE=postgres
run-local: export SQL_PASSWORD=password
run-local: export SQL_HOST=localhost
run-local: export ENABLE_IMAGE_UPLOAD_SUBSCRIBER=false
run-local: export WEB_SERVER_WORKER_COUNT=4
run-local: export RUN_MODE=DEV_MODE_WEB_SERVER
run-local:
	@make print-updating-db
	poetry run alembic upgrade head
	@make print-starting-service
	poetry run fastapi dev main.py --port 8080


# API Server
run-notion-processor: print-auth-message
run-notion-processor: export PORT=8080
run-notion-processor: export INTROSPECTION_ENABLE=True
run-notion-processor: export GCP_PROJECT=dev-web-api-72f12b
run-notion-processor: export APELLA_ENVIRONMENT=dev
# Auth0 Auth
run-dev: export AUTH0_CLIENT_ID_KEY := dev-api-server-auth0-client-id
run-dev: export AUTH0_CLIENT_SECRET_KEY := dev-api-server-auth0-client-secret
run-dev: export AUTH0_URL := https://apella-dev.us.auth0.com
run-dev: export AUTH0_DEFAULT_DOMAIN := https://apella-dev.us.auth0.com
run-dev: export API_GATEWAY_URL := https://api.dev.apella.io
run-dev: export MEDIA_ASSET_SERVICE_ENDPOINT := https://media-asset-service.dev.internal.apella.io
run-dev: export TWILIO_ACCOUNT_SID_NAME := dev-twilio-account-sid
run-dev: export TWILIO_AUTH_TOKEN_NAME := dev-twilio-auth-token
run-dev: export TWILIO_PHONE_NUMBER_NAME := dev-twilio-from-number
run-dev: export SENDGRID_API_KEY := dev-sendgrid-api-key
run-notion-processor: export NOTION_READONLY_API_KEY_NAME=dev-notion-readonly-api-key
# prometheus-client multiprocess setup
run-notion-processor: export PROMETHEUS_MULTIPROC_DIR := $(tempDir)
run-notion-processor: print-prometheus-metrics-dir
run-notion-processor: export LAUNCH_DARKLY_SDK_KEY_NAME := prod-launch-darkly-sdk-key
# Local docker SQL
run-notion-processor: start-postgres-local
run-notion-processor: export SQL_USER=postgres
run-notion-processor: export SQL_DATABASE=postgres
run-notion-processor: export SQL_PASSWORD=password
run-notion-processor: export SQL_HOST=localhost
run-notion-processor: export ENABLE_IMAGE_UPLOAD_SUBSCRIBER=false
run-notion-processor: export WEB_SERVER_WORKER_COUNT=4
run-notion-processor: export RUN_MODE=NOTION_DAEMON
run-notion-processor: export NOTION_DB_ID=1ebaf54ad37a800d99b5ff7e03b9d89d
run-notion-processor:
	@make print-updating-db
	poetry run alembic upgrade head
	@make print-starting-service
	poetry run python -m main

# Alembic Revision
# how to use: make alembic-local-revision message="<enter_message_here_to_pass_to_alembic_revision>"
alembic-local-revision: print-auth-message
alembic-local-revision: export SQL_USER=postgres
alembic-local-revision: export SQL_DATABASE=postgres
alembic-local-revision: export SQL_PASSWORD=password
alembic-local-revision: export SQL_HOST=localhost
alembic-local-revision:
	make start-postgres-local
	@make print-updating-db
	poetry run alembic upgrade head
	@printf "\n$(magenta)Revising database schema...$(normal)\n"
	poetry run alembic revision --autogenerate -m "$(message)"

component-test:
	# Explicitly set the number of pytest workers as this might overwhelm the github runner.
	poetry run python -m pytest --timeout=300 tests_component --tb=short -n auto

start-gcs-emulator:
	@printf "\n$(magenta)Ensuring GCS Emulator docker container is up...$(normal)\n"
	docker start google-cloud-storage-emulator || docker run \
		-d \
		--rm \
		--name google-cloud-storage-emulator \
		-p 4443:4443 \
		-v ${PWD}/tests_component/mock_gcs:/data \
		fsouza/fake-gcs-server:latest \
		-scheme http

build-client-packages:
	make generate-client-types
	poetry run python create_packages.py bdist_wheel
	poetry run python setup_cli_tool.py bdist_wheel

upload-client-packages:
	poetry run python -m twine upload --repository-url ${PYTHON_ARTIFACT_REGISTRY} dist/*

start-postgres-local: export SQL_USER = postgres
start-postgres-local: export SQL_DB = postgres
start-postgres-local: export SQL_PASSWORD = password
start-postgres-local: export SQL_HOST = localhost
start-postgres-local:
	@printf "\n$(magenta)Ensuring SQL docker container is up...$(normal)\n"
	docker start postgres-local || make start-postgres-container

dump-postgres-local: export SQL_USER = postgres
dump-postgres-local: export SQL_DB = postgres
dump-postgres-local: export SQL_PASSWORD = password
dump-postgres-local: export SQL_HOST = localhost
dump-postgres-local:
	@printf "\n$(magenta)Starting database...$(normal)\n"
	make start-postgres-local
	@printf "\n$(magenta)Waiting for SQL to be ready...$(normal)\n"
	@until nc -z localhost 5432; do sleep 5; done
	poetry run alembic upgrade head
	@printf "\n$(magenta)Dumping database...$(normal)\n"
	PGPASSWORD=${SQL_PASSWORD} docker exec -i postgres-local pg_dump -U ${SQL_USER} ${SQL_DB} > databases/sql/schema/public_schema.sql
	docker rm -f postgres-local

start-postgres-container:
	@printf "\n$(magenta)Starting SQL docker container...$(normal)\n"
	docker run \
		-p 5432:5432 \
		--rm \
	    -e POSTGRES_DB=${SQL_DB} \
	    -e POSTGRES_PASSWORD=${SQL_PASSWORD} \
	    -e POSTGRES_USER=${SQL_USER} \
		-d \
	    --name postgres-local \
	    postgres@sha256:c83014a2b46834ef6d17f64c8e4a70089901a8c0dee158f1ca5ccae032ea32e5 \
		-c wal_level=logical

	@make print-updating-db
	@printf "\n$(magenta)Waiting for SQL to be ready...$(normal)\n"
	@until nc -z localhost 5432; do sleep 5; done

start-zipkin-container:
	@printf "\n$(magenta)Starting Zipkin docker container...$(normal)\n"
	docker run \
		-p 9411:9411 \
		--rm \
		-d \
		--name zipkin-local \
		openzipkin/zipkin

start-redis-local:
	@printf "\n$(magenta)Starting Redis docker container...$(normal)\n"
	docker start redis-local || docker run \
		-p 6379:6379 \
		--rm \
		-d \
		--name redis-local \
		redis

generate-graphql:
	poetry run python -m tools.graphql_schema_generator

generate-client-types: generate-graphql
	poetry run sgqlc-codegen schema schema.json apella_cloud_api/api_server_schema.py
	poetry run ruff check --fix apella_cloud_api/api_server_schema.py
	poetry run ruff format apella_cloud_api/api_server_schema.py
	poetry run python -m apella_cloud_api.new_schema_generator.schema_generator schema.graphql apella_cloud_api/new_api_server_schema.py apella_cloud_api/new_client_schema.py apella_cloud_api/new_input_schema.py

check-schema-head:
	@head_rev=$$(poetry run alembic heads | tail -n 1 | awk '{print $$1}'); \
	if grep -q "$$head_rev" databases/sql/schema/public_schema.sql; then \
		echo "✅ Revision $$head_rev found in databases/sql/schema/public_schema.sql."; \
	else \
		echo "❌ Revision $$head_rev not found in databases/sql	/schema/public_schema.sql! Run make dump-postgres-local"; \
		exit 1; \
	fi

format:
	poetry run ruff format .
	poetry run ruff check --fix .

lint:
	poetry run ruff check .
	poetry run ruff format --check .
	poetry run mypy .
