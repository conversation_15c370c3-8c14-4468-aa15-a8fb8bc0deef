# Apella Cloud API Server

This application is an API Server for the Apella Cloud.
It handles the processing of requests coming through the API Gateway.
The requests that it receives are already authenticated, but not
already authorized.

## Quick Start Setup

1. Install Python (using [pyenv](https://github.com/pyenv/pyenv) and [homebrew](https://brew.sh/) on Mac OS X)

   ```sh
   brew install pyenv pyenv-virtualenv
   echo 'eval "$(pyenv init -)"' >> ~/.zshrc
   echo 'eval "$(pyenv init --path)"' >> ~/.zprofile
   pyenv install 3.10
   pyenv global 3.10
   ```

1. Authenticate with Apella Python Artifact Registry
   - `poetry self add keyrings.google-artifactregistry-auth`
   - `gcloud auth application-default login`
   - Test for success: `gcloud artifacts repositories list --project prod-platform-29b5cb`
1. Install [Poetry](https://python-poetry.org/) for Python package management: `curl -sSL https://install.python-poetry.org | python3 -`
1. Install python dependencies via poetry: `poetry install`
1. Make sure unit tests run and succeed: `make test`
1. Start Docker (Docker for Mac or otherwise)
1. Make sure the component tests run and succeed: `make component-test`
1. Run `make run-local` which will first start a local postgres container and then run the api on localhost:8080

## Local Usage

To run the API Server locally, you need to set up some libraries and connections first.

### Python Set up

You first need:

- Python 3.10

Follow the instructions [here](https://python-poetry.org/docs/#installation) to install Poetry.

Install python requirements

```sh
poetry install
```

### Docker set up

If you are using local google storage or postgres, you need to log into Docker
Hub so that you can download appropriate containers.

### ruff Python formatter

We use [ruff](https://github.com/astral-sh/ruff) to ensure the codebase remains up to standard. If you'd like for your IDE to format your code as you develop, follow [these instructions](https://github.com/astral-sh/ruff?tab=readme-ov-file#getting-started).

### Updating GraphQL Python API client

After updating GraphQL, you need to run this to update the Python API client

```sh
make generate-client-types
```

### Running API Server for component tests

Note: Make sure the Docker client is running if running locally.
You can use `make component-test` to run the component tests, which automatically starts the API
server and its dependencies in the background via `gunicorn`:

```sh
make component-test
```

If you would like to selectively run component tests from the CLI, run tests directly with `pytest` using the `-k` (keyword expression) option. It will fuzzy match test names.

```sh
poetry run python -m pytest --timeout=300 tests_component --tb=short -n auto -k my_test_name
```

### Running API Server with (mostly) local dependencies

This will run the server locally with:

- Auth0: dev Auth0
- SQL: local postgres docker container

```sh
make run-local
```

### Running API Server with dev dependencies

This will run the server locally with:

- Auth0: dev Auth0
- SQL: GCP Postgres in dev-web-api project

```sh
make run-dev
```

### Confirm it's running

```sh
curl localhost:8080/v1/version
```

### GraphQL Query and Field Resolution metrics

Using a middleware, we observe how long each graphql field resolution takes that's longer than 10ms; observing the field_name and the operation_name, as well as they query resolution times by operation name.
To see this in action,

- follow the instructions to run the dashboard locally
- follow the instructions to run the api-server locally, or using dev settings
- Load the web-dashboard running locally to generate traffic, and some graphql requests
- Visit the metrics endpoint to see the latest metrics collected:

```sh
curl localhost:8080/metrics
```

### Troubleshooting

If installing dependencies errors out on your M1 system, you may need to separately install a few libraries.
Try running the following commands and seeing if that fixes the issue.

```sh
brew install openssl
brew install postgresql
```

For High Sierra M1s and greater there may be issues with `psycopg2-binary` and the component-tests
forking improperly. In order to resolve these issues, add the following to your `~/.zshrc` or `~/.bashrc` and source the file.

```sh
export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES
export LDFLAGS="-L/opt/homebrew/opt/openssl@3/lib"
export CPPFLAGS="-I/opt/homebrew/opt/openssl@3/include"
```

For issues when installing `grpcio` on M1, include these exports

```sh
export GRPC_PYTHON_BUILD_SYSTEM_OPENSSL=1
export GRPC_PYTHON_BUILD_SYSTEM_ZLIB=1
```

If you see errors indicating missing modules (e.g. "ModuleNotFoundError: No module named 'starlette_graphene3'"), run `poetry install`.

### Additional Options

#### GCP Credentials

If you are using any GCP resource, you need to set up credentials locally.

1. Generate credentials for your user account.

   ```sh
   gcloud auth application-default login
   ```

2. Set the GCP Project for auth

   ```sh
   gcloud config set project dev-web-api-72f12b
   ```

#### GCP SQL through tunnel

Our GCP SQL servers are not exposed to the public internet, so you need to
connect to them through a host bastion tunnel

1. Follow [this wiki](https://www.notion.so/apella/Cloud-API-Server-9b2f0ade751e40048f90276a9bc89ff4) to have the connection commands available
2. Connect to the dev bastion host

   ```sh
   dev-tunnel
   ```

#### Apollo Studio Sandbox

Run queries against your local server by visting the hosted [Apollo Studio Sandbox](https://studio.apollographql.com/sandbox/explorer).

1. Enable GraphQL introspection (This is the default for `make run-dev` and `make run-local`)
```sh
export INTROSPECTION_ENABLE=True
```
2. Visit [Apollo Studio Sandbox](https://studio.apollographql.com/sandbox/explorer)
3. [Authenticate your requests](https://www.notion.so/apella/Authenticating-Apollo-Studio-Explorer-Requests-via-Auth0-3291847560ec476cac6e862ee8c79ce2) by configuring the Preflight Script and Environment Variables.

#### k8s tunnel for Case Duration service

In order to access the Case Duration ML predictions when running locally, you will need to configure and open a tunnel.

When doing this for the first time, you will need to run

```sh
gcloud container clusters get-credentials prod-internal-gke --region us-central1 --project prod-internal-c5ac6b
```

To establish the tunnel and forward the ports, run

```sh
kubectl -n model-bayesian-case-duration port-forward service/model-bayesian-case-duration 9980:80
```

For other Case Duration prediction services, please check the Makefile to make sure you are
using the correct forwarding port for the tunnel (so that each service has its own port).

Please note that there are no data for the test orgs at the moment, so you will need to temporarily modify the code to use a real org when testing locally.

#### If you want to view traces locally
1. Run jaeger locally as a [docker container](https://www.jaegertracing.io/docs/1.6/getting-started/#all-in-one-docker-image)
2. Change the default sample rate in ApellaTraceSampler to 1.0
3. Add the exporter endpoint as an environment variable: `export OTLP_TRACE_EXPORTER_ENDPOINT := http://localhost:4317`
4. Hit any API Endpoint. View traces at http://localhost:16686/

## Tests

We have both unit tests which rely only the the API server code and component tests which rely on external services such as a working postgres database.

### Unit tests

Simply run

```sh
make test
```

## Deployment

### Dev Deployment

Merge your PR which will commit your code to main. Every commit pushed to main will automatically
trigger the [deploy_dev](https://github.com/Apella-Technology/cloud-api-server/actions/workflows/deploy_dev.yml)
workflow.

### Staging / Prod Deployment

To deploy to staging and production, create a Release.
Releases are handled through github releases.
The release uploads a docker build to GCP artifact repository,
publishes the api client library to GCP artifact registry, and deploys to staging and production.

To trigger a new release:

1. Go to the [releases page](https://github.com/Apella-Technology/cloud-api-server/releases)
2. Click 'Draft a new release'
3. Enter a tag version, using semantic versioning (major.minor.build_number)
4. Enter the same version as the release title
5. Add a description
6. Click 'Publish Release'
7. Go to the [release action](https://github.com/Apella-Technology/cloud-api-server/actions?query=workflow%3ARelease)
8. After looking at the [staging logs](https://console.cloud.google.com/run/detail/us-central1/api-server-staging/metrics?folder=&organizationId=&project=staging-web-api-3efef9) and ensuring there are no errors, approve the deployment to Production through the github UI
9. Wait for the release to complete successfully 🎉

### One-off, manual Staging / Prod Deployment

If there is a need to deploy a one-off version of staging or production:

1. Navigate to the deployment workflow you want:
   - [Staging](https://github.com/Apella-Technology/cloud-api-server/actions/workflows/manual_staging_deployment.yml)
   - [Production](https://github.com/Apella-Technology/cloud-api-server/actions/workflows/manual_prod_deployment.yml)
2. Click 'Run Workflow' to open the workflow trigger dropdown
3. Enter the release version, such as `v0.20.1`
4. Click 'Run Workflow' in the dropdown
5. Wait for the job to complete

### Check deployed version

Simply navigate to these urls:

- [Dev](https://api.dev.apella.io/v1/version)
- [Staging](https://api.staging.apella.io/v1/version)
- [Prod](https://api.apella.io/v1/version)

### Monitoring

[Datadog](https://app.datadoghq.com/dashboard/kbd-3et-yin/api-server-kubernetes?fromUser=false&refresh_mode=sliding&view=spans&from_ts=1724788794721&to_ts=1724789694721&live=true) is used for monitoring. Use the `env` dropdown to select the desired environemnt.