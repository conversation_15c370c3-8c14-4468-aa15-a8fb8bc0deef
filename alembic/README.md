# Alembic SQL Migration manager

Alembic helps us manage the migrations of the SQL table schema.

## Alembic revisions

An Alembic revision is a file used to upgrade/downgrade databases and contains at least these things
1. `revision` hash
1. `down_revision` hash (akin to the last commit in git logs, it's the previous Alembic revision)
1. `upgrade()` containing the Alembic operations that capture the sqlalchemy changes in the source code
1. `downgrade()` the opposite steps of upgrade()

## Creating an Alembic revision

First, make sure you are pointing at local postgres by setting these env variables:
```sh
export SQL_HOST=localhost
export SQL_USER=postgres
export SQL_PASSWORD=password
export SQL_DATABASE=postgres
```

1. Modify or create the sqlalchemy python class related to the database table(s).
1. Ensure that any new database classes that extend Base are imported into env.py
1. Start local Postgres and auto-generate the Alembic revision, which translates changes in the source code to Alembic upgrade and downgrade operations
   ```sh
   make alembic-local-revision message="<enter_message_here_to_pass_to_alembic_revision>"
   ```
1. Check the new revision file in `alembic/versions` to ensure the upgrade looks good
1. Commit this new file
1. Perform the upgrade locally (this applies the autogenerated alembic revision to the local DB)
   ```sh
   poetry run alembic upgrade head
   ```
1. log into the local postgres and check that the expected changes have been made (`brew install pgcli` if you haven't)
   ```sh
   . tools/connect.sh local
   ```
1. Verify that downgrading works
   ``` sh
   poetry run alembic downgrade MY_DOWN_REVISION
   ```

## Testing Alembic migrations in Dev and Staging environments

Dev and Staging environments are different from each other and local. For this reason, it's important to test that the Alembic migration will work in these environments before merging. Currently, Alembic migrations are
only attempted by GitHub after merging to main, which means if your merge to main fails Dev and Staging
migrations, it will block subsequent migrations because your migration will keep failing before the 
new migration can start.

Long-term, a GitHub job should be added which checks that migrations to dev, staging, and potentially
prod will succeed before permitting merges to main.


### Dry-run
1. Connect to Dev or Staging following the section on connecting to Dev or Staging below.
1. `poetry run alembic -x dry-run upgrade head`
1. Check that the Exception "Dry-run mode enabled - Rolling back transaction" is generated. This automatically rolls back the transaction, ensuring that Dev and Staging's state are the same as in the main branch.

### Non-dry-run
If you need to test an interactive change in Dev or Staging:
1. Make sure `poetry run alembic downgrade MY_DOWN_REVISION` works locally
1. Sign up for time using the "cloud-api-server dev testing" Google calendar so that you don't accidentally affect other people's migrations. Ideally, do this during times people are unlikely to merge PRs.
1. Execute the `tunnel.sh` and `env_setup.sh` scripts as described below.
1. `poetry run alembic upgrade head`  # this will update Dev or Staging
1. Test interactively
1. `poetry run alembic downgrade MY_DOWN_REVISION`
1. Check that your changes have been reversed 

## Connecting to Dev and Staging environments

Get credentials for gcloud:
```sh
gcloud auth login --update-adc
```
### Tunnel
Replace `MY_ENV` with `dev` or `staging`
```sh
. tools/tunnel.sh MY_ENV
```

### Create env variables
Open another terminal and replace `MY_ENV` with `dev` or `staging`.
Alembic uses these environment variables.
```sh
. tools/env_setup.sh MY_ENV
```

### Run Alembic commands as described in Dry-run and Non-dry-run or connect to the database
Replace `MY_ENV` with `local`, `dev` or `staging`
```sh
. tools/connect.sh MY_ENV
```

## Removing a column from the Database

We learned that removal of columns from the DB is a six step deploy process:

1. Add logging to service and GraphQL the cloud api server to track all GraphQL references to the
   column to be removed. The point of this step is to track down all of the times a GraphQL request
   that will use the column to be deleted is sent to the server.
2. Mark all references to that column in GraphQL queries and mutations as optional in the server(
   cloud api repo)
3. Remove all references to the column in client GraphQL calls(non cloud api repos)
4. Remove the column from all GraphQL queries, mutations, and models and remove the column from the
   SQL Alchemy model however don’t create an alembic migration yet(cloud api repo)
5. Update all client APIs to use the latest version of the Cloud API so there are no implicit
   references in GraphQL.(non cloud api repos)
6. Create an Alembic migration
