# When Alembic is executed, it doesn't have the rest of the project on its path
# This should be mitigated by running `pip install -e .` to install the current directory
# But that requires setup.py which we do not have.  And it would require all users
# to run that install, which doesn't seem like it should be necessary.
# So instead, just add the rest of the project to the path while running alembic,
# So that we can import the sql definitions.
import asyncio
from typing import Optional, MutableMapping, Literal

import sys
from logging.config import fileConfig

import alembic_postgresql_enum  # noqa: F401
from alembic import context
from sqlalchemy import Connection, StaticPool

sys.path = ["", ".."] + sys.path[1:]
# Here we import all the SQLAlchemy tables, even though they appear as "unused", they are
# required so that ale<PERSON><PERSON> reads the python classes when traversing the class tree of Base

import api_server.services.anesthesia.anesthesia_store  # nopep8 # noqa
import api_server.services.annotation_tasks.annotation_task_store  # nopep8 # noqa
import api_server.services.block.block_store  # nopep8 # noqa
import api_server.services.block_utilization.block_utilization_store  # nopep8 # noqa
import api_server.services.camera.camera_store  # nopep8 # noqa
import api_server.services.case.case_classification_type_store  # nopep8 # noqa
import api_server.services.plan.case_note_plan_store  # nopep8 # noqa
import api_server.services.case.case_procedure_store  # nopep8 # noqa
import api_server.services.case.case_staff_store  # nopep8 # noqa
import api_server.services.plan.case_staff_plan_store  # nopep8 # noqa
import api_server.services.case.case_matching_store  # nopep8 # noqa
import api_server.services.case.case_flag_store  # nopep8 # noqa
import api_server.services.case.case_store  # nopep8 # noqa
import api_server.services.case_derived_properties.case_derived_properties_store  # nopep8 # noqa
import api_server.services.case_to_block.case_to_block_store  # nopep8 # noqa
import api_server.services.contact_information.contact_information_store  # nopep8 # noqa
import api_server.services.contact_information.staff_event_notification_contact_information_store  # nopep8 # noqa
import api_server.services.custom_phase_config.custom_phase_config_store  # nopep8 # noqa
import api_server.services.ehr_interfaces.mapping_store  # nopep8 # noqa
import api_server.services.events.event_store  # nopep8 # noqa
import api_server.services.first_case_config.first_case_config_store  # nopep8 # noqa
import api_server.services.highlights.highlight_feedback_store  # nopep8 # noqa
import api_server.services.highlights.highlight_store  # nopep8 # noqa
import api_server.services.measurement_periods.measurement_period_store  # nopep8 # noqa
import api_server.services.observations.observation_store  # nopep8 # noqa
import api_server.services.organization.organization_store  # nopep8 # noqa
import api_server.services.phases.phase_store  # nopep8 # noqa
import api_server.services.prime_time.prime_time_store  # nopep8 # noqa
import api_server.services.procedures.procedure_store  # nopep8 # noqa
import api_server.services.room.room_store  # nopep8 # noqa
import api_server.services.closures.closure_store  # nopep8 # noqa
import api_server.services.service_lines.service_line_store  # nopep8 # noqa
import api_server.services.site.site_store  # nopep8 # noqa
import api_server.services.boards.board_store  # nopep8 # noqa
import api_server.services.staff.staff_store  # nopep8 # noqa
import api_server.services.staff_role.staff_role_store  # nopep8 # noqa
import api_server.services.staffing_needs.staffing_needs_store  # nopep8 # noqa
import api_server.services.user_filter_views.user_filter_view_store  # nopep8 # noqa
import api_server.services.cluster.cluster_store  # nopep8 # noqa
import api_server.services.terminal_cleans.terminal_cleans_store  # nopep8 # noqa
import api_server.services.turnover.turnover_store  # nopep8 # noqa
import api_server.services.turnover.turnover_label_store  # nopep8 # noqa
import api_server.services.turnover.turnover_label_assoc_store  # nopep8 # noqa
import api_server.services.turnover.turnover_note_store  # nopep8 # noqa
import api_server.services.case_forecasts.case_forecast_store  # nopep8 # noqa
import api_server.services.case_labels.case_label_store  # nopep8 # noqa
import config as apella_config  # nopep8 # noqa
from databases.secret_store import SecretStore  # nopep8 # noqa

# imports must go after the previous path
from databases.sql import Base, engine_provider, DECODABLE_SCHEMA  # nopep8 # noqa

# Set the Base class of the SQLAlchemy DB
target_metadata = Base.metadata

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
# context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
assert context.config.config_file_name is not None
fileConfig(context.config.config_file_name)

ignored_enums = ["casetype"]


def include_enum_name(name: str) -> bool:
    return name not in ignored_enums


alembic_postgresql_enum.set_configuration(
    alembic_postgresql_enum.Config(
        include_name=include_enum_name,
    )
)


def include_name(
    name: Optional[str],
    type_: Literal[
        "schema", "table", "column", "index", "unique_constraint", "foreign_key_constraint"
    ],
    parent_names: MutableMapping[
        Literal["schema_name", "table_name", "schema_qualified_table_name"], Optional[str]
    ],
):
    if type_ == "schema":
        return name != DECODABLE_SCHEMA
    else:
        return True


def run_migration(connection: Connection) -> None:
    context.configure(
        include_schemas=True,
        include_name=include_name,
        connection=connection,
        target_metadata=target_metadata,
        transaction_per_migration=True,
    )
    with context.begin_transaction():
        context.run_migrations()
        if "dry-run" in context.get_x_argument():
            raise Exception("Dry-run mode enabled - Rolling back transaction")


async def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    async_engine = engine_provider(pool_class=StaticPool)
    async with async_engine.begin() as connection:
        await connection.run_sync(run_migration)


asyncio.run(run_migrations_online())
