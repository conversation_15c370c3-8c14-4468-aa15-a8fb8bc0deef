"""Add annotation task type users

Revision ID: 01126046b5e9
Revises: 2576c4a5a780
Create Date: 2023-06-01 16:17:48.374069

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "01126046b5e9"
down_revision = "2576c4a5a780"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "annotation_task_type_users",
        sa.<PERSON>umn(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("annotation_task_type_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("user_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["annotation_task_type_id"],
            ["annotation_task_types.id"],
        ),
        sa.PrimaryKeyConstraint("annotation_task_type_id", "user_id"),
    )
    op.create_index(
        op.f("ix_annotation_task_type_users_annotation_task_type_id"),
        "annotation_task_type_users",
        ["annotation_task_type_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_task_type_users_created_time"),
        "annotation_task_type_users",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_task_type_users_updated_time"),
        "annotation_task_type_users",
        ["updated_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_task_type_users_user_id"),
        "annotation_task_type_users",
        ["user_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_annotation_task_type_users_user_id"), table_name="annotation_task_type_users"
    )
    op.drop_index(
        op.f("ix_annotation_task_type_users_updated_time"), table_name="annotation_task_type_users"
    )
    op.drop_index(
        op.f("ix_annotation_task_type_users_created_time"), table_name="annotation_task_type_users"
    )
    op.drop_index(
        op.f("ix_annotation_task_type_users_annotation_task_type_id"),
        table_name="annotation_task_type_users",
    )
    op.drop_table("annotation_task_type_users")
    # ### end Alembic commands ###
