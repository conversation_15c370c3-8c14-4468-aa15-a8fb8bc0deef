"""Add boards config tables

Revision ID: 03eae48bde5f
Revises: b6a49a7cca25
Create Date: 2023-10-26 15:16:20.111726

"""

import sqlalchemy as sa

from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "03eae48bde5f"
down_revision = "b6a49a7cca25"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "board_config",
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            server_default=sa.text("gen_random_uuid()"),
            nullable=False,
        ),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("page_size", sa.Integer(), nullable=False),
        sa.Column("page_duration", sa.Integer(), nullable=False),
        sa.Column("blur_video", sa.<PERSON>(), nullable=False),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.Column("site_id", sa.String(), nullable=False),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["org_id"],
            ["organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["site_id"],
            ["sites.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_board_config_created_time"), "board_config", ["created_time"], unique=False
    )
    op.create_index(op.f("ix_board_config_org_id"), "board_config", ["org_id"], unique=False)
    op.create_index(op.f("ix_board_config_site_id"), "board_config", ["site_id"], unique=False)
    op.create_index(
        op.f("ix_board_config_updated_time"), "board_config", ["updated_time"], unique=False
    )
    op.create_table(
        "board_room",
        sa.Column("board_config_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("room_id", sa.String(), nullable=False),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["board_config_id"],
            ["board_config.id"],
        ),
        sa.ForeignKeyConstraint(
            ["room_id"],
            ["rooms.id"],
        ),
        sa.PrimaryKeyConstraint("board_config_id", "room_id"),
    )
    op.create_index(
        op.f("ix_board_room_created_time"), "board_room", ["created_time"], unique=False
    )
    op.create_index(
        op.f("ix_board_room_updated_time"), "board_room", ["updated_time"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_board_room_updated_time"), table_name="board_room")
    op.drop_index(op.f("ix_board_room_created_time"), table_name="board_room")
    op.drop_table("board_room")
    op.drop_index(op.f("ix_board_config_updated_time"), table_name="board_config")
    op.drop_index(op.f("ix_board_config_site_id"), table_name="board_config")
    op.drop_index(op.f("ix_board_config_org_id"), table_name="board_config")
    op.drop_index(op.f("ix_board_config_created_time"), table_name="board_config")
    op.drop_table("board_config")
    # ### end Alembic commands ###
