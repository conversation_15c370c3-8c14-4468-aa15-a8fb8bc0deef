"""Adding block type field for blocks

Revision ID: 04acae0270b4
Revises: 65aecf5e1eb3
Create Date: 2025-05-07 15:50:37.192025

"""

import sqlalchemy as sa

from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "04acae0270b4"
down_revision = "65aecf5e1eb3"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "blocks",
        sa.Column(
            "block_type",
            postgresql.ENUM(
                "UNBLOCKED_BLOCK_TYPE",
                "UNAVAILABLE_BLOCK_TYPE",
                "GROUP_BLOCK_TYPE",
                "SURGEON_BLOCK_TYPE",
                "SERVICE_BLOCK_TYPE",
                "ON_HOLD_BLOCK_TYPE",
                "UNKNOWN",
                name="blocktypes",
                create_type=False,
            ),
            nullable=True,
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by <PERSON>embic - please adjust! ###
    op.drop_column("blocks", "block_type")
    # ### end Alembic commands ###
