"""add case derived properties

Revision ID: 051622cb2c33
Revises: 692b7557fc76
Create Date: 2023-04-14 10:19:29.657693

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "051622cb2c33"
down_revision = "692b7557fc76"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "case_derived_properties",
        sa.Column("case_id", sa.String(), nullable=False),
        sa.Column("is_in_flip_room", sa.<PERSON>(), server_default="false", nullable=False),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["case_id"],
            ["cases.case_id"],
        ),
        sa.PrimaryKeyConstraint("case_id"),
    )
    op.create_index(
        op.f("ix_case_derived_properties_case_id"),
        "case_derived_properties",
        ["case_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_derived_properties_created_time"),
        "case_derived_properties",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_derived_properties_updated_time"),
        "case_derived_properties",
        ["updated_time"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_case_derived_properties_updated_time"), table_name="case_derived_properties"
    )
    op.drop_index(
        op.f("ix_case_derived_properties_created_time"), table_name="case_derived_properties"
    )
    op.drop_index(op.f("ix_case_derived_properties_case_id"), table_name="case_derived_properties")
    op.drop_table("case_derived_properties")
    # ### end Alembic commands ###
