"""Add camera id to events optionally

Revision ID: 0bf06196a76e
Revises: ecabcb6f60e4
Create Date: 2021-10-19 16:17:54.296231

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "0bf06196a76e"
down_revision = "ecabcb6f60e4"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("events", sa.Column("camera_id", sa.String(), nullable=True))
    op.create_index(op.f("ix_events_camera_id"), "events", ["camera_id"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_events_camera_id"), table_name="events")
    op.drop_column("events", "camera_id")
    # ### end Alembic commands ###
