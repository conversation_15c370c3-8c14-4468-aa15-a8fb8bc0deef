"""Add task type history

Revision ID: 0c0c7191ccee
Revises: 2d57fdb47d29
Create Date: 2023-12-07 23:52:37.052455

"""

import sqlalchemy as sa

from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0c0c7191ccee"
down_revision = "2d57fdb47d29"
branch_labels = None
depends_on = None


def upgrade():
    # Create annotation_task_types_history table
    op.create_table(
        "annotation_task_types_history",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("archived_time", sa.DateTime(timezone=True), autoincrement=False, nullable=True),
        sa.Column("id", postgresql.UUID(as_uuid=True), autoincrement=False, nullable=False),
        sa.Column("name", sa.String(), autoincrement=False, nullable=False),
        sa.Column("description", sa.String(), autoincrement=False, nullable=False),
        sa.Column(
            "event_types", postgresql.ARRAY(sa.String()), autoincrement=False, nullable=False
        ),
        sa.Column(
            "context_event_types",
            postgresql.ARRAY(sa.String()),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("priority", sa.Integer(), autoincrement=False, nullable=False),
        sa.Column("detect_idle", sa.Boolean(), autoincrement=False, nullable=False),
        sa.Column("allow_skipping_review", sa.Boolean(), autoincrement=False, nullable=False),
        sa.Column(
            "version",
            sa.Integer(),
            server_default=sa.text("1"),
            autoincrement=False,
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id", "version"),
    )
    op.create_index(
        op.f("ix_annotation_task_types_history_archived_time"),
        "annotation_task_types_history",
        ["archived_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_task_types_history_created_time"),
        "annotation_task_types_history",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_task_types_history_updated_time"),
        "annotation_task_types_history",
        ["updated_time"],
        unique=False,
    )

    # Add version column to annotation_task_type
    op.add_column(
        "annotation_task_types",
        sa.Column("version", sa.Integer(), server_default=sa.text("1"), nullable=False),
    )

    # Add type_version column to annotation_tasks
    op.add_column(
        "annotation_tasks",
        sa.Column("type_version", sa.Integer(), nullable=False, server_default=sa.text("1")),
    )

    # Remove server default in previous step - we want to ensure that the type_version is handled by the caller
    op.alter_column("annotation_tasks", "type_version", server_default=None)

    # Backfill old annotation_task_types_history
    op.execute(
        """
        INSERT INTO annotation_task_types_history (
            created_time, updated_time, archived_time, id, name, description, event_types, context_event_types, priority, detect_idle, allow_skipping_review, version
        )
        SELECT
            created_time, updated_time, archived_time, id, name, description, event_types, context_event_types, priority, detect_idle, allow_skipping_review, version
        FROM annotation_task_types
        """
    )

    # Change annotation_tasks table to have composite foreign key constraint to annotation_tasks_history table
    op.create_index(
        op.f("ix_annotation_tasks_type_version"), "annotation_tasks", ["type_version"], unique=False
    )
    op.create_foreign_key(
        "annotation_tasks_type_id_type_version_fkey",
        "annotation_tasks",
        "annotation_task_types_history",
        ["type_id", "type_version"],
        ["id", "version"],
    )

    # Add type_version column to annotation_tasks_history, add new foreign key constraint
    op.add_column(
        "annotation_tasks_history",
        sa.Column(
            "type_version",
            sa.Integer(),
            autoincrement=False,
            nullable=False,
            server_default=sa.text("1"),
        ),
    )

    # Remove server default in previous step - we want to ensure that the type_version is handled by the caller
    op.alter_column("annotation_tasks_history", "type_version", server_default=None)

    op.create_index(
        op.f("ix_annotation_tasks_history_type_version"),
        "annotation_tasks_history",
        ["type_version"],
        unique=False,
    )
    op.create_foreign_key(
        "annotation_tasks_history_type_id_type_version_fkey",
        "annotation_tasks_history",
        "annotation_task_types_history",
        ["type_id", "type_version"],
        ["id", "version"],
    )


def downgrade():
    op.drop_constraint(
        "annotation_tasks_history_type_id_type_version_fkey",
        "annotation_tasks_history",
        type_="foreignkey",
    )
    op.drop_index(
        op.f("ix_annotation_tasks_history_type_version"), table_name="annotation_tasks_history"
    )
    op.drop_column("annotation_tasks_history", "type_version")
    op.drop_constraint(
        "annotation_tasks_type_id_type_version_fkey", "annotation_tasks", type_="foreignkey"
    )
    op.drop_index(op.f("ix_annotation_tasks_type_version"), table_name="annotation_tasks")
    op.drop_column("annotation_tasks", "type_version")
    op.drop_column("annotation_task_types", "version")
    op.drop_index(
        op.f("ix_annotation_task_types_history_updated_time"),
        table_name="annotation_task_types_history",
    )
    op.drop_index(
        op.f("ix_annotation_task_types_history_created_time"),
        table_name="annotation_task_types_history",
    )
    op.drop_index(
        op.f("ix_annotation_task_types_history_archived_time"),
        table_name="annotation_task_types_history",
    )
    op.drop_table("annotation_task_types_history")
