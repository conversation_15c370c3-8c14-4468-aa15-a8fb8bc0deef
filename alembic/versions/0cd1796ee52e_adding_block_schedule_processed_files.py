"""Adding block schedule processed files

Revision ID: 0cd1796ee52e
Revises: d7d9f3c7f994
Create Date: 2025-04-25 10:45:00.710147

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "0cd1796ee52e"
down_revision = "d7d9f3c7f994"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "block_schedule_processed_files",
        sa.Column("id", sa.UUID(), server_default=sa.text("gen_random_uuid()"), nullable=False),
        sa.Column("file_name", sa.String(), nullable=False),
        sa.Column("bucket_name", sa.String(), nullable=False),
        sa.Column("generation", sa.String(), nullable=False),
        sa.Column("size", sa.String(), nullable=False),
        sa.Column("md5_hash", sa.String(), nullable=False),
        sa.Column("media_link", sa.String(), nullable=False),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["org_id"],
            ["organizations.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_block_schedule_processed_files_bucket_name"),
        "block_schedule_processed_files",
        ["bucket_name"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_schedule_processed_files_created_time"),
        "block_schedule_processed_files",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_schedule_processed_files_file_name"),
        "block_schedule_processed_files",
        ["file_name"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_schedule_processed_files_md5_hash"),
        "block_schedule_processed_files",
        ["md5_hash"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_schedule_processed_files_media_link"),
        "block_schedule_processed_files",
        ["media_link"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_schedule_processed_files_org_id"),
        "block_schedule_processed_files",
        ["org_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_schedule_processed_files_updated_time"),
        "block_schedule_processed_files",
        ["updated_time"],
        unique=False,
    )
    op.create_index(
        "ix_unique_block_schedule_processed_files",
        "block_schedule_processed_files",
        ["file_name", "bucket_name", "md5_hash"],
        unique=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        "ix_unique_block_schedule_processed_files", table_name="block_schedule_processed_files"
    )
    op.drop_index(
        op.f("ix_block_schedule_processed_files_updated_time"),
        table_name="block_schedule_processed_files",
    )
    op.drop_index(
        op.f("ix_block_schedule_processed_files_org_id"),
        table_name="block_schedule_processed_files",
    )
    op.drop_index(
        op.f("ix_block_schedule_processed_files_media_link"),
        table_name="block_schedule_processed_files",
    )
    op.drop_index(
        op.f("ix_block_schedule_processed_files_md5_hash"),
        table_name="block_schedule_processed_files",
    )
    op.drop_index(
        op.f("ix_block_schedule_processed_files_file_name"),
        table_name="block_schedule_processed_files",
    )
    op.drop_index(
        op.f("ix_block_schedule_processed_files_created_time"),
        table_name="block_schedule_processed_files",
    )
    op.drop_index(
        op.f("ix_block_schedule_processed_files_bucket_name"),
        table_name="block_schedule_processed_files",
    )
    op.drop_table("block_schedule_processed_files")
    # ### end Alembic commands ###
