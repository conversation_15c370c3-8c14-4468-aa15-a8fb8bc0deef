"""Add the forecasted cases simple model

Revision ID: 0edab01aa17c
Revises: e40264c6313b
Create Date: 2024-09-17 15:33:03.994436

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "0edab01aa17c"
down_revision = "e40264c6313b"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "case_forecast_history",
        sa.Column("id", sa.UUID(), autoincrement=False, nullable=False),
        sa.Column("case_id", sa.String(), autoincrement=False, nullable=False),
        sa.Column(
            "forecast_start_time", sa.DateTime(timezone=True), autoincrement=False, nullable=False
        ),
        sa.Column(
            "forecast_end_time", sa.DateTime(timezone=True), autoincrement=False, nullable=False
        ),
        sa.Column("forecast_variant", sa.String(), autoincrement=False, nullable=False),
        sa.Column("room_id", sa.String(), autoincrement=False, nullable=False),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("org_id", sa.String(), autoincrement=False, nullable=False),
        sa.Column("site_id", sa.String(), autoincrement=False, nullable=False),
        sa.Column(
            "version",
            sa.Integer(),
            server_default=sa.text("1"),
            autoincrement=False,
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id", "version"),
    )
    op.create_index(
        op.f("ix_case_forecast_history_case_id"), "case_forecast_history", ["case_id"], unique=False
    )
    op.create_index(
        op.f("ix_case_forecast_history_created_time"),
        "case_forecast_history",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_history_forecast_end_time"),
        "case_forecast_history",
        ["forecast_end_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_history_forecast_start_time"),
        "case_forecast_history",
        ["forecast_start_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_history_forecast_variant"),
        "case_forecast_history",
        ["forecast_variant"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_history_id"), "case_forecast_history", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_case_forecast_history_org_id"), "case_forecast_history", ["org_id"], unique=False
    )
    op.create_index(
        op.f("ix_case_forecast_history_site_id"), "case_forecast_history", ["site_id"], unique=False
    )
    op.create_index(
        op.f("ix_case_forecast_history_updated_time"),
        "case_forecast_history",
        ["updated_time"],
        unique=False,
    )
    op.create_table(
        "case_forecast",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("case_id", sa.String(), nullable=False),
        sa.Column("forecast_start_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("forecast_end_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("forecast_variant", sa.String(), nullable=False),
        sa.Column("room_id", sa.String(), nullable=False),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.Column("site_id", sa.String(), nullable=False),
        sa.Column("version", sa.Integer(), server_default=sa.text("1"), nullable=False),
        sa.ForeignKeyConstraint(["case_id"], ["cases.case_id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(
            ["org_id"],
            ["organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["room_id"],
            ["rooms.id"],
        ),
        sa.ForeignKeyConstraint(
            ["site_id"],
            ["sites.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("case_id", "forecast_variant", name="uq_case_id_forecast_variant"),
    )
    op.create_index(op.f("ix_case_forecast_case_id"), "case_forecast", ["case_id"], unique=False)
    op.create_index(
        op.f("ix_case_forecast_created_time"), "case_forecast", ["created_time"], unique=False
    )
    op.create_index(
        op.f("ix_case_forecast_forecast_end_time"),
        "case_forecast",
        ["forecast_end_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_forecast_start_time"),
        "case_forecast",
        ["forecast_start_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_forecast_variant"),
        "case_forecast",
        ["forecast_variant"],
        unique=False,
    )
    op.create_index(op.f("ix_case_forecast_id"), "case_forecast", ["id"], unique=False)
    op.create_index(op.f("ix_case_forecast_org_id"), "case_forecast", ["org_id"], unique=False)
    op.create_index(op.f("ix_case_forecast_site_id"), "case_forecast", ["site_id"], unique=False)
    op.create_index(
        op.f("ix_case_forecast_updated_time"), "case_forecast", ["updated_time"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_case_forecast_updated_time"), table_name="case_forecast")
    op.drop_index(op.f("ix_case_forecast_site_id"), table_name="case_forecast")
    op.drop_index(op.f("ix_case_forecast_org_id"), table_name="case_forecast")
    op.drop_index(op.f("ix_case_forecast_id"), table_name="case_forecast")
    op.drop_index(op.f("ix_case_forecast_forecast_variant"), table_name="case_forecast")
    op.drop_index(op.f("ix_case_forecast_forecast_start_time"), table_name="case_forecast")
    op.drop_index(op.f("ix_case_forecast_forecast_end_time"), table_name="case_forecast")
    op.drop_index(op.f("ix_case_forecast_created_time"), table_name="case_forecast")
    op.drop_index(op.f("ix_case_forecast_case_id"), table_name="case_forecast")
    op.drop_table("case_forecast")
    op.drop_index(op.f("ix_case_forecast_history_updated_time"), table_name="case_forecast_history")
    op.drop_index(op.f("ix_case_forecast_history_site_id"), table_name="case_forecast_history")
    op.drop_index(op.f("ix_case_forecast_history_org_id"), table_name="case_forecast_history")
    op.drop_index(op.f("ix_case_forecast_history_id"), table_name="case_forecast_history")
    op.drop_index(
        op.f("ix_case_forecast_history_forecast_variant"), table_name="case_forecast_history"
    )
    op.drop_index(
        op.f("ix_case_forecast_history_forecast_start_time"), table_name="case_forecast_history"
    )
    op.drop_index(
        op.f("ix_case_forecast_history_forecast_end_time"), table_name="case_forecast_history"
    )
    op.drop_index(op.f("ix_case_forecast_history_created_time"), table_name="case_forecast_history")
    op.drop_index(op.f("ix_case_forecast_history_case_id"), table_name="case_forecast_history")
    op.drop_table("case_forecast_history")
    # ### end Alembic commands ###
