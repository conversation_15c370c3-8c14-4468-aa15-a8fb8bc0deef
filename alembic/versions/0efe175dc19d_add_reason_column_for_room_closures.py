"""Add reason column for room closures

Revision ID: 0efe175dc19d
Revises: 582216b46869
Create Date: 2024-10-28 12:43:25.501025

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "0efe175dc19d"
down_revision = "582216b46869"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("room_closures", sa.Column("reason", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("room_closures", "reason")
    # ### end Alembic commands ###
