"""linking blocks to sites

Revision ID: 0f6f3891f6fb
Revises: eaa2b81ead8e
Create Date: 2025-03-27 13:05:25.641351

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "0f6f3891f6fb"
down_revision = "eaa2b81ead8e"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("""
        INSERT INTO block_sites (block_id, site_id)
        SELECT bt.block_id, r.site_id
        FROM block_times bt
        JOIN rooms r ON bt.room_id = r.id
        ON CONFLICT (block_id, site_id) DO NOTHING;
    """)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
