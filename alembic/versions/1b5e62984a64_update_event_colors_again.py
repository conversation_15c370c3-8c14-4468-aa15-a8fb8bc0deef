"""update event colors - again

Revision ID: 1b5e62984a64
Revises: 7f1e413f14fc
Create Date: 2023-01-12 14:15:04.507508

"""

import sqlalchemy as sa
from sqlalchemy.sql import column, table

from alembic import op

# revision identifiers, used by Alembic.
revision = "1b5e62984a64"
down_revision = "7f1e413f14fc"
branch_labels = None
depends_on = None

eventType = table("event_types", column("id", sa.String(32)), column("color", sa.String(16)))


def upgrade():
    # Blue - 70
    op.execute(
        eventType.update().where(eventType.c.id == "back_table_cleared").values(color="#004399")
    )
    # Blue - 50
    op.execute(
        eventType.update().where(eventType.c.id == "back_table_open").values(color="#006FFF")
    )
    # Blue - 30
    op.execute(eventType.update().where(eventType.c.id == "endo_pack_open").values(color="#66a9ff"))

    # Teal - 30
    op.execute(eventType.update().where(eventType.c.id == "patient_draped").values(color="#72c8c8"))
    # Teat - 60
    op.execute(
        eventType.update().where(eventType.c.id == "patient_undraped").values(color="#108383")
    )

    # Red - 30
    op.execute(
        eventType.update().where(eventType.c.id == "patient_wheels_in").values(color="#f36f66")
    )
    # Red - 60
    op.execute(
        eventType.update().where(eventType.c.id == "patient_wheels_out").values(color="#bc0c00")
    )

    # Violet - 60
    op.execute(
        eventType.update().where(eventType.c.id == "patient_xfer_to_bed").values(color="#5e49cc")
    )
    # Violet - 30
    op.execute(
        eventType.update()
        .where(eventType.c.id == "patient_xfer_to_or_table")
        .values(color="#ad9dff")
    )

    # Orange - 30
    op.execute(
        eventType.update()
        .where(eventType.c.id == "sensitive_content_start")
        .values(color="#f2ab66")
    )
    # Orange - 60
    op.execute(
        eventType.update().where(eventType.c.id == "sensitive_content_end").values(color="#ba5c00")
    )

    # Green - 30
    op.execute(
        eventType.update().where(eventType.c.id == "terminal_clean_start").values(color="#6fcca6")
    )
    # Green - 60
    op.execute(
        eventType.update().where(eventType.c.id == "terminal_clean_end").values(color="#0c8856")
    )


def downgrade():
    # Blue - 60
    op.execute(
        eventType.update().where(eventType.c.id == "back_table_cleared").values(color="#2d5899")
    )
    # Blue - 40
    op.execute(
        eventType.update().where(eventType.c.id == "back_table_open").values(color="#4c94ff")
    )
    # Blue - 20
    op.execute(eventType.update().where(eventType.c.id == "endo_pack_open").values(color="#93beff"))

    # Teal - 20
    op.execute(eventType.update().where(eventType.c.id == "patient_draped").values(color="#72c8c8"))
    # Teat - 40
    op.execute(
        eventType.update().where(eventType.c.id == "patient_undraped").values(color="#14a4a4")
    )

    # Red - 20
    op.execute(
        eventType.update().where(eventType.c.id == "patient_wheels_in").values(color="#f4a6a6")
    )
    # Red - 40
    op.execute(
        eventType.update().where(eventType.c.id == "patient_wheels_out").values(color="#ed6c6b")
    )

    # Violet - 40
    op.execute(
        eventType.update().where(eventType.c.id == "patient_xfer_to_bed").values(color="#8c8ce3")
    )
    # Violet - 20
    op.execute(
        eventType.update()
        .where(eventType.c.id == "patient_xfer_to_or_table")
        .values(color="#babaee")
    )

    # Orange - 20
    op.execute(
        eventType.update()
        .where(eventType.c.id == "sensitive_content_start")
        .values(color="#f1ab66")
    )
    # Orange - 40
    op.execute(
        eventType.update().where(eventType.c.id == "sensitive_content_end").values(color="#e97300")
    )

    # Green - 20
    op.execute(
        eventType.update().where(eventType.c.id == "terminal_clean_start").values(color="#6fcca6")
    )
    # Green - 40
    op.execute(
        eventType.update().where(eventType.c.id == "terminal_clean_end").values(color="#0faa6b")
    )
