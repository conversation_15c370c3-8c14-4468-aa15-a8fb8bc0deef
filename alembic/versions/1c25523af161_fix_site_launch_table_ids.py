"""Fix site launch table ids

Revision ID: 1c25523af161
Revises: 1f753a179e56
Create Date: 2025-05-29 18:20:09.062807

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "1c25523af161"
down_revision = "1f753a179e56"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("TRUNCATE TABLE site_launch_information")
    op.add_column("site_launch_information", sa.Column("notion_id", sa.String(), nullable=False))
    op.drop_constraint("uq_site_id", "site_launch_information", type_="unique")
    op.create_index(
        op.f("ix_site_launch_information_notion_id"),
        "site_launch_information",
        ["notion_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("TRUNCATE TABLE site_launch_information")
    op.drop_index(
        op.f("ix_site_launch_information_notion_id"), table_name="site_launch_information"
    )
    op.create_unique_constraint("uq_site_id", "site_launch_information", ["site_id"])
    op.drop_column("site_launch_information", "notion_id")
    # ### end Alembic commands ###
