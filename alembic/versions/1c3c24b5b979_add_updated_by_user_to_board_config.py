"""Add updated by user to board config

Revision ID: 1c3c24b5b979
Revises: 03eae48bde5f
Create Date: 2023-10-26 15:58:44.824525

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "1c3c24b5b979"
down_revision = "03eae48bde5f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("board_config", sa.Column("updated_by_user_id", sa.String(), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("board_config", "updated_by_user_id")
    # ### end Alembic commands ###
