"""Add Annotation Task Type table

Revision ID: 1ce5d9dca1bf
Revises: a4699c900d25
Create Date: 2022-08-31 15:14:10.974696

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "1ce5d9dca1bf"
down_revision = "a4699c900d25"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    annotation_task_types_table = op.create_table(
        "annotation_task_types",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("description", sa.String(), nullable=True),
        sa.Column("event_types", postgresql.ARRAY(sa.String()), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_annotation_task_types_created_time"),
        "annotation_task_types",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_task_types_updated_time"),
        "annotation_task_types",
        ["updated_time"],
        unique=False,
    )
    op.add_column("annotation_tasks", sa.Column("type_id", sa.String(), nullable=True))  # for now
    op.create_index(
        op.f("ix_annotation_tasks_type_id"),
        "annotation_tasks",
        ["type_id"],
        unique=False,
    )
    op.create_foreign_key(
        "annotation_tasks_type_id_fkey",
        "annotation_tasks",
        "annotation_task_types",
        ["type_id"],
        ["id"],
    )
    op.add_column(
        "annotation_tasks_history",
        sa.Column("type_id", sa.String(), nullable=True),  # for now
    )
    op.create_index(
        op.f("ix_annotation_tasks_history_type_id"),
        "annotation_tasks_history",
        ["type_id"],
        unique=False,
    )
    op.create_foreign_key(
        "annotation_tasks_history_type_id_fkey",
        "annotation_tasks_history",
        "annotation_task_types",
        ["type_id"],
        ["id"],
    )

    op.bulk_insert(
        annotation_task_types_table,
        [
            {
                "id": "surgery",
                "name": "Surgery",
                "description": "The default task type containing all events",
                "event_types": [
                    "back_table_open",
                    "back_table_unprepared",
                    "case_cart_in",
                    "case_cart_out",
                    "case_cart_visible",
                    "cleaning_crew_in",
                    "cleaning_crew_out",
                    "closing_start",
                    "count_end",
                    "count_start",
                    "endo_pack_open",
                    "endoscopy_end",
                    "endoscopy_start",
                    "extubation",
                    "first_incision_start",
                    "intubation",
                    "lights_off",
                    "lights_on",
                    "mop",
                    "mop_in",
                    "mop_out",
                    "no_case_cart",
                    "no_mop",
                    "no_patient",
                    "or_table_ready",
                    "patient_briefing_end",
                    "patient_briefing_start",
                    "patient_draped",
                    "patient_imaging_end",
                    "patient_imaging_start",
                    "patient_on_hospital_bed",
                    "patient_on_or_table",
                    "patient_skin_prep_end",
                    "patient_skin_prep_start",
                    "patient_undraped",
                    "patient_wheels_in",
                    "patient_wheels_out",
                    "patient_xfer_to_bed",
                    "patient_xfer_to_or_table",
                    "post_operative",
                    "pre_operative",
                    "sensitive_content_end",
                    "sensitive_content_start",
                    "sterile_pack_on_back_table",
                    "surgery",
                    "terminal_clean_end",
                    "terminal_clean_start",
                    "timeout_end",
                    "timeout_start",
                    "turn_over_clean",
                    "turn_over_idle",
                    "turn_over_open",
                ],
            },
            {
                "id": "mop",
                "name": "Mop",
                "description": "The mop type concerns all mop-related events",
                "event_types": [
                    "mop",
                    "mop_in",
                    "mop_out",
                ],
            },
            {
                "id": "light",
                "name": "Light",
                "description": "Is it on or off? ¿Porque no los dos?",
                "event_types": [
                    "lights_off",
                    "lights_on",
                ],
            },
        ],
    )

    # Back-fill all annotation_tasks' and annotation_tasks_history's entries with `surgery` type
    op.execute("UPDATE annotation_tasks SET type_id = 'surgery'")
    op.execute("UPDATE annotation_tasks_history SET type_id = 'surgery'")

    # Now flip type_id back to nullable=False
    op.alter_column("annotation_tasks", "type_id", nullable=False)
    op.alter_column("annotation_tasks_history", "type_id", nullable=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "annotation_tasks_history_type_id_fkey",
        "annotation_tasks_history",
        type_="foreignkey",
    )
    op.drop_index(
        op.f("ix_annotation_tasks_history_type_id"),
        table_name="annotation_tasks_history",
    )
    op.drop_column("annotation_tasks_history", "type_id")
    op.drop_constraint("annotation_tasks_type_id_fkey", "annotation_tasks", type_="foreignkey")
    op.drop_index(op.f("ix_annotation_tasks_type_id"), table_name="annotation_tasks")
    op.drop_column("annotation_tasks", "type_id")
    op.drop_index(
        op.f("ix_annotation_task_types_updated_time"),
        table_name="annotation_task_types",
    )
    op.drop_index(
        op.f("ix_annotation_task_types_created_time"),
        table_name="annotation_task_types",
    )
    op.drop_table("annotation_task_types")
    # ### end Alembic commands ###
