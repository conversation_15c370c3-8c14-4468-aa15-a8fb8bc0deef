"""deleting block schedule rows for misc block types

Revision ID: 1dab82cc7ab0
Revises: bb2e6c157c76
Create Date: 2025-05-16 15:11:59.903977

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "1dab82cc7ab0"
down_revision = "bb2e6c157c76"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        """
            DELETE FROM public.block_schedule_file_rows
            WHERE block_type in ('ON_HOLD_BLOCK_TYPE', 'UNBLOCKED_BLOCK_TYPE', 'UNAVAILABLE_BLOCK_TYPE', 'SERVICE_BLOCK_TYPE');
        """
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
