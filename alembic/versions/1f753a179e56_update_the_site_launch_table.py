"""Update the site launch table

Revision ID: 1f753a179e56
Revises: 8ab0cea7fe7f
Create Date: 2025-05-29 16:47:47.793046

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "1f753a179e56"
down_revision = "8ab0cea7fe7f"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("TRUNCATE TABLE site_launch_information")
    op.drop_constraint("pk_site_launch", "site_launch_information", type_="primary")
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("site_launch_information", sa.Column("site_name", sa.String(), nullable=False))
    op.alter_column("site_launch_information", "site_id", existing_type=sa.VARCHAR(), nullable=True)
    op.create_index(
        op.f("ix_site_launch_information_site_name"),
        "site_launch_information",
        ["site_name"],
        unique=False,
    )
    # ### end Alembic commands ###
    op.create_primary_key("pk_site_launch", "site_launch_information", ["site_name"])
    op.create_unique_constraint("uq_site_id", "site_launch_information", ["site_id"])


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_site_launch_information_site_name"), table_name="site_launch_information"
    )
    op.alter_column(
        "site_launch_information", "site_id", existing_type=sa.VARCHAR(), nullable=False
    )
    op.drop_constraint("pk_site_launch", "site_launch_information", type_="primary")
    op.drop_column("site_launch_information", "site_name")
    # ### end Alembic commands ###
    op.execute("TRUNCATE TABLE site_launch_information")
    op.create_primary_key("pk_site_launch", "site_launch_information", ["site_id"])
    op.drop_constraint("uq_site_id", "site_launch_information", type_="unique")
