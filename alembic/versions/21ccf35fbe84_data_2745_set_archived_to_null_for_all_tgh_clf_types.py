"""DATA-2745: set archived to null for all TGH clf types

Revision ID: 21ccf35fbe84
Revises: c783a838437c
Create Date: 2024-08-27 10:24:18.611213

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "21ccf35fbe84"
down_revision = "c783a838437c"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
        UPDATE case_classification_types
        SET archived_time = NULL
        WHERE org_id = 'tampa_general'
        AND id IN ('CASE_CLASSIFICATION_ELECTIVE', 'CASE_CLASSIFICATION_EMERGENT', 'CASE_CLASSIFICATION_EXPEDITED', 'CASE_CLASSIFICATION_URGENT');
    """)


def downgrade():
    op.execute("""
        UPDATE case_classification_types
        SET archived_time = CURRENT_TIMESTAMP
        WHERE org_id = 'tampa_general'
        AND id IN ('CASE_CLASSIFICATION_ELECTIVE', 'CASE_CLASSIFICATION_EMERGENT', 'CASE_CLASSIFICATION_EXPEDITED', 'CASE_CLASSIFICATION_URGENT');
    """)
