"""Modify phase_types

Revision ID: 25a37eb1d5eb
Revises: f106f340195c
Create Date: 2022-08-23 13:42:46.777612

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "25a37eb1d5eb"
down_revision = "f106f340195c"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        "UPDATE phase_types SET description='An opening phase is measured as the period between when the back table is opened to the patient wheels in.' WHERE id='TURNOVER_OPEN'"
    )
    op.execute(
        "UPDATE phase_types SET description='A cleaning phase is measured as the period between when a patient wheels out and the back table is opened.' WHERE id='TURNOVER_CLEAN'"
    )
    op.execute(
        "UPDATE phase_types SET description='A turnover phase is measured as the period between when a patient wheels out and the next patient wheels in.' WHERE id='TURNOVER'"
    )
    op.execute("UPDATE phase_types SET title='Opening' WHERE id='TURNOVER_OPEN'")
    op.execute("UPDATE phase_types SET title='Cleaning' WHERE id='TURNOVER_CLEAN'")


def downgrade():
    op.execute(
        "UPDATE phase_types SET description='A Turnover Open phase is measured as the period between when the back table is opened to the patient wheels in.' WHERE id='TURNOVER_OPEN'"
    )
    op.execute(
        "UPDATE phase_types SET description='A Terminal Clean phase is measured as the period between the cleaning crew in to the cleaning crew out, including a mopping of the walls.' WHERE id='TURNOVER_CLEAN'"
    )
    op.execute(
        "UPDATE phase_types SET description='A Turnover phase is measured as the period between when a patient wheels out and the next patient wheels in. Turnovers longer than 90 minutes are excluded.' WHERE id='TURNOVER'"
    )
    op.execute("UPDATE phase_types SET title='Turnover Open' WHERE id='TURNOVER_OPEN'")
    op.execute("UPDATE phase_types SET title='Turnover Clean' WHERE id='TURNOVER_CLEAN'")
