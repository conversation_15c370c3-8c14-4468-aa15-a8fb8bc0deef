"""Add Block time model

Revision ID: 25aff70d1840
Revises: b768a1aa1479
Create Date: 2023-10-10 13:14:08.146381

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "25aff70d1840"
down_revision = "b768a1aa1479"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "blocks",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("archived_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            server_default=sa.text("gen_random_uuid()"),
            nullable=False,
        ),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("color", sa.String(length=16), nullable=False),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.CheckConstraint("(color ~ '^#[0-9a-fA-F]{6}$')", name="ck_block_color"),
        sa.ForeignKeyConstraint(
            ["org_id"],
            ["organizations.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_blocks_archived_time"), "blocks", ["archived_time"], unique=False)
    op.create_index(op.f("ix_blocks_created_time"), "blocks", ["created_time"], unique=False)
    op.create_index(op.f("ix_blocks_name"), "blocks", ["name"], unique=False)
    op.create_index(op.f("ix_blocks_org_id"), "blocks", ["org_id"], unique=False)
    op.create_index(op.f("ix_blocks_updated_time"), "blocks", ["updated_time"], unique=False)
    op.create_table(
        "block_times",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            server_default=sa.text("gen_random_uuid()"),
            nullable=False,
        ),
        sa.Column("block_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("block_time", postgresql.TSRANGE(), nullable=False),
        sa.Column("room_id", sa.String(), nullable=False),
        postgresql.ExcludeConstraint(
            (sa.column("room_id"), "="),
            (sa.column("block_time"), "&&"),
            using="gist",
            name="unique_room_tsrange_constraint",
        ),
        sa.ForeignKeyConstraint(
            ["block_id"],
            ["blocks.id"],
        ),
        sa.ForeignKeyConstraint(
            ["room_id"],
            ["rooms.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_block_times_block_id"), "block_times", ["block_id"], unique=False)
    op.create_index(op.f("ix_block_times_block_time"), "block_times", ["block_time"], unique=False)
    op.create_index(
        op.f("ix_block_times_created_time"), "block_times", ["created_time"], unique=False
    )
    op.create_index(op.f("ix_block_times_room_id"), "block_times", ["room_id"], unique=False)
    op.create_index(
        op.f("ix_block_times_updated_time"), "block_times", ["updated_time"], unique=False
    )
    op.drop_index("ix_block_history_archived_time", table_name="block_history")
    op.drop_index("ix_block_history_created_time", table_name="block_history")
    op.drop_index("ix_block_history_name", table_name="block_history")
    op.drop_index("ix_block_history_org_id", table_name="block_history")
    op.drop_index("ix_block_history_updated_time", table_name="block_history")
    op.drop_table("block_history")
    op.drop_index("ix_block_archived_time", table_name="block")
    op.drop_index("ix_block_created_time", table_name="block")
    op.drop_index("ix_block_name", table_name="block")
    op.drop_index("ix_block_org_id", table_name="block")
    op.drop_index("ix_block_updated_time", table_name="block")
    op.drop_table("block")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "block",
        sa.Column(
            "created_time",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "archived_time", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True
        ),
        sa.Column(
            "id",
            postgresql.UUID(),
            server_default=sa.text("gen_random_uuid()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("name", sa.VARCHAR(length=255), autoincrement=False, nullable=False),
        sa.Column("org_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column(
            "version",
            sa.INTEGER(),
            server_default=sa.text("1"),
            autoincrement=False,
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["org_id"], ["organizations.id"], name="block_org_id_fkey"),
        sa.PrimaryKeyConstraint("id", name="block_pkey"),
    )
    op.create_index("ix_block_updated_time", "block", ["updated_time"], unique=False)
    op.create_index("ix_block_org_id", "block", ["org_id"], unique=False)
    op.create_index("ix_block_name", "block", ["name"], unique=False)
    op.create_index("ix_block_created_time", "block", ["created_time"], unique=False)
    op.create_index("ix_block_archived_time", "block", ["archived_time"], unique=False)
    op.create_table(
        "block_history",
        sa.Column(
            "created_time",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "archived_time", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True
        ),
        sa.Column(
            "id",
            postgresql.UUID(),
            server_default=sa.text("gen_random_uuid()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("name", sa.VARCHAR(length=255), autoincrement=False, nullable=False),
        sa.Column("org_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column(
            "version",
            sa.INTEGER(),
            server_default=sa.text("1"),
            autoincrement=False,
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id", "version", name="block_history_pkey"),
    )
    op.create_index(
        "ix_block_history_updated_time", "block_history", ["updated_time"], unique=False
    )
    op.create_index("ix_block_history_org_id", "block_history", ["org_id"], unique=False)
    op.create_index("ix_block_history_name", "block_history", ["name"], unique=False)
    op.create_index(
        "ix_block_history_created_time", "block_history", ["created_time"], unique=False
    )
    op.create_index(
        "ix_block_history_archived_time", "block_history", ["archived_time"], unique=False
    )
    op.drop_index(op.f("ix_block_times_updated_time"), table_name="block_times")
    op.drop_index(op.f("ix_block_times_room_id"), table_name="block_times")
    op.drop_index(op.f("ix_block_times_created_time"), table_name="block_times")
    op.drop_index(op.f("ix_block_times_block_time"), table_name="block_times")
    op.drop_index(op.f("ix_block_times_block_id"), table_name="block_times")
    op.drop_table("block_times")
    op.drop_index(op.f("ix_blocks_updated_time"), table_name="blocks")
    op.drop_index(op.f("ix_blocks_org_id"), table_name="blocks")
    op.drop_index(op.f("ix_blocks_name"), table_name="blocks")
    op.drop_index(op.f("ix_blocks_created_time"), table_name="blocks")
    op.drop_index(op.f("ix_blocks_archived_time"), table_name="blocks")
    op.drop_table("blocks")
    # ### end Alembic commands ###
