"""Add org_id column to CaseStaff

Revision ID: 26900aa0e5d9
Revises: 792c9384fb8f
Create Date: 2022-02-14 16:25:38.739131

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "26900aa0e5d9"
down_revision = "792c9384fb8f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Add org_id column as nullable
    op.add_column("case_staffing", sa.Column("org_id", sa.String(), nullable=True))

    # Populate org_id values
    op.execute(
        "UPDATE case_staffing SET org_id = cases.org_id "
        "FROM cases "
        "WHERE cases.case_id = case_staffing.case_id"
    )

    # Make column non-nullable
    op.alter_column("case_staffing", "org_id", existing_type=sa.VARCHAR(), nullable=False)

    # Other changes
    op.alter_column("case_raw", "org_id", existing_type=sa.VARCHAR(), nullable=False)
    op.create_index(op.f("ix_case_raw_org_id"), "case_raw", ["org_id"], unique=False)
    op.create_index(op.f("ix_case_staffing_org_id"), "case_staffing", ["org_id"], unique=False)
    op.create_foreign_key(None, "case_staffing", "organizations", ["org_id"], ["id"])
    op.create_index(op.f("ix_cases_org_id"), "cases", ["org_id"], unique=False)
    op.alter_column("highlight_feedback", "org_id", existing_type=sa.VARCHAR(), nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("highlight_feedback", "org_id", existing_type=sa.VARCHAR(), nullable=True)
    op.drop_index(op.f("ix_cases_org_id"), table_name="cases")
    op.drop_constraint(None, "case_staffing", type_="foreignkey")
    op.drop_index(op.f("ix_case_staffing_org_id"), table_name="case_staffing")
    op.drop_column("case_staffing", "org_id")
    op.drop_index(op.f("ix_case_raw_org_id"), table_name="case_raw")
    op.alter_column("case_raw", "org_id", existing_type=sa.VARCHAR(), nullable=True)
    # ### end Alembic commands ###
