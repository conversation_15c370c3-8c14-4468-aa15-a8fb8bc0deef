"""Add nullable external_message_id column to case_raw table

Revision ID: 28b2ae391144
Revises: d4d82b19c0ba
Create Date: 2022-11-29 09:00:39.409029

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "28b2ae391144"
down_revision = "d4d82b19c0ba"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("case_raw", sa.Column("external_message_id", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("case_raw", "external_message_id")
    # ### end Alembic commands ###
