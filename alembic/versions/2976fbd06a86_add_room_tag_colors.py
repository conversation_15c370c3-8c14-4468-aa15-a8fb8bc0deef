"""Add room tag colors

Revision ID: 2976fbd06a86
Revises: 3110cb241c19
Create Date: 2024-08-22 10:49:00.128380

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "2976fbd06a86"
down_revision = "3110cb241c19"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "room_tags", sa.Column("color", sa.String(), server_default="#999999", nullable=False)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("room_tags", "color")
    # ### end Alembic commands ###
