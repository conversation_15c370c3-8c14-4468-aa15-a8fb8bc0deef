"""add_case_staff_plan_table

Revision ID: 2aa5eb0feed3
Revises: e921f4da6c21
Create Date: 2023-12-04 10:29:45.821622

"""

import sqlalchemy as sa

from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "2aa5eb0feed3"
down_revision = "e921f4da6c21"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "case_staff_plan",
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            server_default=sa.text("gen_random_uuid()"),
            nullable=False,
        ),
        sa.Column("case_id", sa.String(), nullable=True),
        sa.Column("staff_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("role", sa.String(), nullable=True),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("archived_time", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["case_id"],
            ["cases.case_id"],
        ),
        sa.ForeignKeyConstraint(
            ["staff_id"],
            ["staff.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "case_id", "staff_id", "role", "archived_time", name="uq_case_staff_role_archived"
        ),
    )
    op.create_index(
        op.f("ix_case_staff_plan_archived_time"), "case_staff_plan", ["archived_time"], unique=False
    )
    op.create_index(
        op.f("ix_case_staff_plan_case_id"), "case_staff_plan", ["case_id"], unique=False
    )
    op.create_index(
        op.f("ix_case_staff_plan_created_time"), "case_staff_plan", ["created_time"], unique=False
    )
    op.create_index(op.f("ix_case_staff_plan_role"), "case_staff_plan", ["role"], unique=False)
    op.create_index(
        op.f("ix_case_staff_plan_staff_id"), "case_staff_plan", ["staff_id"], unique=False
    )
    op.create_index(
        op.f("ix_case_staff_plan_updated_time"), "case_staff_plan", ["updated_time"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_case_staff_plan_updated_time"), table_name="case_staff_plan")
    op.drop_index(op.f("ix_case_staff_plan_staff_id"), table_name="case_staff_plan")
    op.drop_index(op.f("ix_case_staff_plan_role"), table_name="case_staff_plan")
    op.drop_index(op.f("ix_case_staff_plan_created_time"), table_name="case_staff_plan")
    op.drop_index(op.f("ix_case_staff_plan_case_id"), table_name="case_staff_plan")
    op.drop_index(op.f("ix_case_staff_plan_archived_time"), table_name="case_staff_plan")
    op.drop_table("case_staff_plan")
    # ### end Alembic commands ###
