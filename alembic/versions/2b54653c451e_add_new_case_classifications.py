"""Add new case classifications

Revision ID: 2b54653c451e
Revises: 3d99993fde5a
Create Date: 2024-02-29 21:38:41.560803

"""

from sqlalchemy import MetaData, Table

from alembic import op

# revision identifiers, used by Alembic.
revision = "2b54653c451e"
down_revision = "3d99993fde5a"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # get metadata from current connection
    # meta = MetaData(bind=op.get_bind())
    meta = MetaData()
    # pass in tuple with tables we want to reflect, otherwise whole database will get reflected
    meta.reflect(only=("case_classification_types",), bind=op.get_bind())

    # define table representation
    case_classification_types = Table("case_classification_types", meta)
    op.bulk_insert(
        case_classification_types,
        [
            {"id": "CASE_CLASSIFICATION_ACCIDENT", "name": "Accident"},
            {"id": "CASE_CLASSIFICATION_EMERGENCY", "name": "Emergency"},
            {"id": "CASE_CLASSIFICATION_LABOR_AND_DELIVERY", "name": "Labor and Delivery"},
            {"id": "CASE_CLASSIFICATION_NEWBORN", "name": "Newborn (Birth in healthcare facility)"},
            {"id": "CASE_CLASSIFICATION_ROUTINE", "name": "Routine"},
            {"id": "CASE_CLASSIFICATION_EXPEDITED", "name": "Expedited"},
        ],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        "DELETE FROM case_classification_types WHERE id IN ('CASE_CLASSIFICATION_ACCIDENT', 'CASE_CLASSIFICATION_EMERGENCY', 'CASE_CLASSIFICATION_LABOR_AND_DELIVERY', 'CASE_CLASSIFICATION_NEWBORN', 'CASE_CLASSIFICATION_ROUTINE')"
    )
    # ### end Alembic commands ###
