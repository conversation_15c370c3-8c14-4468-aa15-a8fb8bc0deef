"""Add unreleased_time column

Revision ID: 2d57fdb47d29
Revises: 2aa5eb0feed3
Create Date: 2023-12-06 10:35:40.198369

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "2d57fdb47d29"
down_revision = "2aa5eb0feed3"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "block_time_releases",
        sa.Column("unreleased_time", sa.DateTime(timezone=True), nullable=True),
    )
    op.create_index(
        op.f("ix_block_time_releases_unreleased_time"),
        "block_time_releases",
        ["unreleased_time"],
        unique=False,
    )
    op.add_column(
        "block_time_releases",
        sa.Column("unreleased_source", sa.String(), nullable=True),
    )
    op.execute(
        f"""
        DO $$
            BEGIN
                ALTER TABLE public.block_time_releases DROP CONSTRAINT IF EXISTS unique_block_time_release_tsrange_constraint;

                ALTER TABLE public.block_time_releases ADD CONSTRAINT unique_block_time_release_tsrange_constraint EXCLUDE USING gist (block_time_id WITH =, release_time WITH &&) WHERE (unreleased_time IS NULL);
            END
        $$
        """
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        f"""
        DO $$
            BEGIN
                DELETE FROM public.block_time_releases WHERE unreleased_time IS NOT NULL;

                ALTER TABLE public.block_time_releases DROP CONSTRAINT IF EXISTS unique_block_time_release_tsrange_constraint;

                ALTER TABLE public.block_time_releases ADD CONSTRAINT unique_block_time_release_tsrange_constraint EXCLUDE USING gist (block_time_id WITH =, release_time WITH &&);
            END
        $$
        """
    )
    op.drop_index(op.f("ix_block_time_releases_unreleased_time"), table_name="block_time_releases")
    op.drop_column("block_time_releases", "unreleased_time")
    op.drop_column("block_time_releases", "unreleased_source")
    # ### end Alembic commands ###
