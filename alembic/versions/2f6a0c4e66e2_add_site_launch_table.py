"""Add site launch table

Revision ID: 2f6a0c4e66e2
Revises: fef18e1545e0
Create Date: 2025-05-05 19:56:00.511485

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "2f6a0c4e66e2"
down_revision = "fef18e1545e0"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "site_launch_information",
        sa.Column("site_id", sa.String(), nullable=False),
        sa.Column("launch_date", sa.Date(), nullable=False),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["site_id"],
            ["sites.id"],
        ),
        sa.PrimaryKeyConstraint("site_id", name="pk_site_launch"),
    )
    op.create_index(
        op.f("ix_site_launch_information_created_time"),
        "site_launch_information",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_site_launch_information_launch_date"),
        "site_launch_information",
        ["launch_date"],
        unique=False,
    )
    op.create_index(
        op.f("ix_site_launch_information_updated_time"),
        "site_launch_information",
        ["updated_time"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_site_launch_information_updated_time"), table_name="site_launch_information"
    )
    op.drop_index(
        op.f("ix_site_launch_information_launch_date"), table_name="site_launch_information"
    )
    op.drop_index(
        op.f("ix_site_launch_information_created_time"), table_name="site_launch_information"
    )
    op.drop_table("site_launch_information")
    # ### end Alembic commands ###
