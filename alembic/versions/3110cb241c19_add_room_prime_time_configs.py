"""Add room prime time configs

Revision ID: 3110cb241c19
Revises: af31f13a92eb
Create Date: 2024-08-20 13:25:33.206270

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "3110cb241c19"
down_revision = "af31f13a92eb"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "room_prime_time_configs",
        sa.Column("id", sa.UUID(), server_default=sa.text("gen_random_uuid()"), nullable=False),
        sa.Column("room_id", sa.String(), nullable=False),
        sa.Column("sunday_start_time", sa.Time(), nullable=True),
        sa.Column("sunday_end_time", sa.Time(), nullable=True),
        sa.Column("monday_start_time", sa.Time(), nullable=True),
        sa.Column("monday_end_time", sa.Time(), nullable=True),
        sa.Column("tuesday_start_time", sa.Time(), nullable=True),
        sa.Column("tuesday_end_time", sa.Time(), nullable=True),
        sa.Column("wednesday_start_time", sa.Time(), nullable=True),
        sa.Column("wednesday_end_time", sa.Time(), nullable=True),
        sa.Column("thursday_start_time", sa.Time(), nullable=True),
        sa.Column("thursday_end_time", sa.Time(), nullable=True),
        sa.Column("friday_start_time", sa.Time(), nullable=True),
        sa.Column("friday_end_time", sa.Time(), nullable=True),
        sa.Column("saturday_start_time", sa.Time(), nullable=True),
        sa.Column("saturday_end_time", sa.Time(), nullable=True),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.CheckConstraint(
            "friday_start_time IS NULL AND friday_end_time IS NULL OR friday_start_time IS NOT NULL AND friday_end_time IS NOT NULL AND friday_start_time < friday_end_time",
            name="ck_room_prime_time_configs_friday_valid_times",
        ),
        sa.CheckConstraint(
            "monday_start_time IS NULL AND monday_end_time IS NULL OR monday_start_time IS NOT NULL AND monday_end_time IS NOT NULL AND monday_start_time < monday_end_time",
            name="ck_room_prime_time_configs_monday_valid_times",
        ),
        sa.CheckConstraint(
            "saturday_start_time IS NULL AND saturday_end_time IS NULL OR saturday_start_time IS NOT NULL AND saturday_end_time IS NOT NULL AND saturday_start_time < saturday_end_time",
            name="ck_room_prime_time_configs_saturday_valid_times",
        ),
        sa.CheckConstraint(
            "sunday_start_time IS NULL AND sunday_end_time IS NULL OR sunday_start_time IS NOT NULL AND sunday_end_time IS NOT NULL AND sunday_start_time < sunday_end_time",
            name="ck_room_prime_time_configs_sunday_valid_times",
        ),
        sa.CheckConstraint(
            "thursday_start_time IS NULL AND thursday_end_time IS NULL OR thursday_start_time IS NOT NULL AND thursday_end_time IS NOT NULL AND thursday_start_time < thursday_end_time",
            name="ck_room_prime_time_configs_thursday_valid_times",
        ),
        sa.CheckConstraint(
            "tuesday_start_time IS NULL AND tuesday_end_time IS NULL OR tuesday_start_time IS NOT NULL AND tuesday_end_time IS NOT NULL AND tuesday_start_time < tuesday_end_time",
            name="ck_room_prime_time_configs_tuesday_valid_times",
        ),
        sa.CheckConstraint(
            "wednesday_start_time IS NULL AND wednesday_end_time IS NULL OR wednesday_start_time IS NOT NULL AND wednesday_end_time IS NOT NULL AND wednesday_start_time < wednesday_end_time",
            name="ck_room_prime_time_configs_wednesday_valid_times",
        ),
        sa.ForeignKeyConstraint(["room_id"], ["rooms.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id", "room_id"),
        sa.UniqueConstraint("room_id"),
    )
    op.create_index(
        op.f("ix_room_prime_time_configs_created_time"),
        "room_prime_time_configs",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_room_prime_time_configs_friday_end_time"),
        "room_prime_time_configs",
        ["friday_end_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_room_prime_time_configs_friday_start_time"),
        "room_prime_time_configs",
        ["friday_start_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_room_prime_time_configs_monday_end_time"),
        "room_prime_time_configs",
        ["monday_end_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_room_prime_time_configs_monday_start_time"),
        "room_prime_time_configs",
        ["monday_start_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_room_prime_time_configs_room_id"),
        "room_prime_time_configs",
        ["room_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_room_prime_time_configs_saturday_end_time"),
        "room_prime_time_configs",
        ["saturday_end_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_room_prime_time_configs_saturday_start_time"),
        "room_prime_time_configs",
        ["saturday_start_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_room_prime_time_configs_sunday_end_time"),
        "room_prime_time_configs",
        ["sunday_end_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_room_prime_time_configs_sunday_start_time"),
        "room_prime_time_configs",
        ["sunday_start_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_room_prime_time_configs_thursday_end_time"),
        "room_prime_time_configs",
        ["thursday_end_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_room_prime_time_configs_thursday_start_time"),
        "room_prime_time_configs",
        ["thursday_start_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_room_prime_time_configs_tuesday_end_time"),
        "room_prime_time_configs",
        ["tuesday_end_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_room_prime_time_configs_tuesday_start_time"),
        "room_prime_time_configs",
        ["tuesday_start_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_room_prime_time_configs_updated_time"),
        "room_prime_time_configs",
        ["updated_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_room_prime_time_configs_wednesday_end_time"),
        "room_prime_time_configs",
        ["wednesday_end_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_room_prime_time_configs_wednesday_start_time"),
        "room_prime_time_configs",
        ["wednesday_start_time"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_room_prime_time_configs_wednesday_start_time"),
        table_name="room_prime_time_configs",
    )
    op.drop_index(
        op.f("ix_room_prime_time_configs_wednesday_end_time"), table_name="room_prime_time_configs"
    )
    op.drop_index(
        op.f("ix_room_prime_time_configs_updated_time"), table_name="room_prime_time_configs"
    )
    op.drop_index(
        op.f("ix_room_prime_time_configs_tuesday_start_time"), table_name="room_prime_time_configs"
    )
    op.drop_index(
        op.f("ix_room_prime_time_configs_tuesday_end_time"), table_name="room_prime_time_configs"
    )
    op.drop_index(
        op.f("ix_room_prime_time_configs_thursday_start_time"), table_name="room_prime_time_configs"
    )
    op.drop_index(
        op.f("ix_room_prime_time_configs_thursday_end_time"), table_name="room_prime_time_configs"
    )
    op.drop_index(
        op.f("ix_room_prime_time_configs_sunday_start_time"), table_name="room_prime_time_configs"
    )
    op.drop_index(
        op.f("ix_room_prime_time_configs_sunday_end_time"), table_name="room_prime_time_configs"
    )
    op.drop_index(
        op.f("ix_room_prime_time_configs_saturday_start_time"), table_name="room_prime_time_configs"
    )
    op.drop_index(
        op.f("ix_room_prime_time_configs_saturday_end_time"), table_name="room_prime_time_configs"
    )
    op.drop_index(op.f("ix_room_prime_time_configs_room_id"), table_name="room_prime_time_configs")
    op.drop_index(
        op.f("ix_room_prime_time_configs_monday_start_time"), table_name="room_prime_time_configs"
    )
    op.drop_index(
        op.f("ix_room_prime_time_configs_monday_end_time"), table_name="room_prime_time_configs"
    )
    op.drop_index(
        op.f("ix_room_prime_time_configs_friday_start_time"), table_name="room_prime_time_configs"
    )
    op.drop_index(
        op.f("ix_room_prime_time_configs_friday_end_time"), table_name="room_prime_time_configs"
    )
    op.drop_index(
        op.f("ix_room_prime_time_configs_created_time"), table_name="room_prime_time_configs"
    )
    op.drop_table("room_prime_time_configs")
    # ### end Alembic commands ###
