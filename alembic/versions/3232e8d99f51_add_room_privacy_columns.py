"""add_room_privacy_columns

Revision ID: 3232e8d99f51
Revises: b964e280a36a
Create Date: 2024-03-04 10:05:06.784771

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "3232e8d99f51"
down_revision = "b964e280a36a"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "rooms", sa.Column("privacy_enabled_at", sa.DateTime(timezone=True), nullable=True)
    )
    op.add_column("rooms", sa.Column("privacy_updated_by_user_id", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("rooms", "privacy_updated_by_user_id")
    op.drop_column("rooms", "privacy_enabled_at")
    # ### end Alembic commands ###
