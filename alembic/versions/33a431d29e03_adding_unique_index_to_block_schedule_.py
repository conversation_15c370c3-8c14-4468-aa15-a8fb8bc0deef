"""Adding unique index to block schedule file rows table

Revision ID: 33a431d29e03
Revises: 04acae0270b4
Create Date: 2025-05-12 12:21:18.848784

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "33a431d29e03"
down_revision = "04acae0270b4"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("block_schedule_file_rows", sa.Column("row_number", sa.Integer(), nullable=False))
    op.create_index(
        "ix_unique_block_schedule_file_row_number",
        "block_schedule_file_rows",
        ["block_schedule_file_id", "row_number"],
        unique=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_unique_block_schedule_file_row_number", table_name="block_schedule_file_rows")
    op.drop_column("block_schedule_file_rows", "row_number")
    # ### end Alembic commands ###
