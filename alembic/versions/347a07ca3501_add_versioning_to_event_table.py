"""Add versioning to event table

Revision ID: 347a07ca3501
Revises: d610d1e44179
Create Date: 2022-09-22 09:45:18.343934

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "347a07ca3501"
down_revision = "d610d1e44179"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "events_history",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("id", sa.String(), autoincrement=False, nullable=False),
        sa.Column("event_type", sa.String(), autoincrement=False, nullable=True),
        sa.Column("event_name", sa.String(), autoincrement=False, nullable=False),
        sa.Column("start_time", sa.DateTime(timezone=True), autoincrement=False, nullable=False),
        sa.Column("end_time", sa.DateTime(timezone=True), autoincrement=False, nullable=True),
        sa.Column(
            "process_timestamp", sa.DateTime(timezone=True), autoincrement=False, nullable=False
        ),
        sa.Column("deleted_at", sa.DateTime(timezone=True), autoincrement=False, nullable=True),
        sa.Column("event_type_id", sa.String(), autoincrement=False, nullable=True),
        sa.Column("site_id", sa.String(), autoincrement=False, nullable=False),
        sa.Column("room_id", sa.String(), autoincrement=False, nullable=False),
        sa.Column("camera_id", sa.String(), autoincrement=False, nullable=True),
        sa.Column("case_id", sa.String(), autoincrement=False, nullable=True),
        sa.Column("source", sa.String(), autoincrement=False, nullable=False),
        sa.Column("model_version", sa.String(), autoincrement=False, nullable=True),
        sa.Column("source_type", sa.String(), autoincrement=False, nullable=False),
        sa.Column("confidence", sa.Float(), autoincrement=False, nullable=True),
        sa.Column("labels", postgresql.ARRAY(sa.String()), autoincrement=False, nullable=True),
        sa.Column("notes", sa.String(), autoincrement=False, nullable=True),
        sa.Column("org_id", sa.String(), autoincrement=False, nullable=False),
        sa.Column(
            "version",
            sa.Integer(),
            server_default=sa.text("1"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("changed", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id", "version"),
    )
    op.create_index(
        op.f("ix_events_history_camera_id"), "events_history", ["camera_id"], unique=False
    )
    op.create_index(op.f("ix_events_history_case_id"), "events_history", ["case_id"], unique=False)
    op.create_index(
        op.f("ix_events_history_created_time"), "events_history", ["created_time"], unique=False
    )
    op.create_index(
        op.f("ix_events_history_end_time"), "events_history", ["end_time"], unique=False
    )
    op.create_index(
        op.f("ix_events_history_event_name"), "events_history", ["event_name"], unique=False
    )
    op.create_index(
        op.f("ix_events_history_event_type"), "events_history", ["event_type"], unique=False
    )
    op.create_index(
        op.f("ix_events_history_event_type_id"), "events_history", ["event_type_id"], unique=False
    )
    op.create_index(op.f("ix_events_history_labels"), "events_history", ["labels"], unique=False)
    op.create_index(
        op.f("ix_events_history_model_version"), "events_history", ["model_version"], unique=False
    )
    op.create_index(op.f("ix_events_history_org_id"), "events_history", ["org_id"], unique=False)
    op.create_index(op.f("ix_events_history_room_id"), "events_history", ["room_id"], unique=False)
    op.create_index(op.f("ix_events_history_site_id"), "events_history", ["site_id"], unique=False)
    op.create_index(
        op.f("ix_events_history_source_type"), "events_history", ["source_type"], unique=False
    )
    op.create_index(
        op.f("ix_events_history_start_time"), "events_history", ["start_time"], unique=False
    )
    op.create_index(
        op.f("ix_events_history_updated_time"), "events_history", ["updated_time"], unique=False
    )
    op.add_column("events", sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True))
    op.add_column(
        "events", sa.Column("version", sa.Integer(), nullable=False, server_default=sa.text("1"))
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("events", "version")
    op.drop_column("events", "deleted_at")
    op.drop_index(op.f("ix_events_history_updated_time"), table_name="events_history")
    op.drop_index(op.f("ix_events_history_start_time"), table_name="events_history")
    op.drop_index(op.f("ix_events_history_source_type"), table_name="events_history")
    op.drop_index(op.f("ix_events_history_site_id"), table_name="events_history")
    op.drop_index(op.f("ix_events_history_room_id"), table_name="events_history")
    op.drop_index(op.f("ix_events_history_org_id"), table_name="events_history")
    op.drop_index(op.f("ix_events_history_model_version"), table_name="events_history")
    op.drop_index(op.f("ix_events_history_labels"), table_name="events_history")
    op.drop_index(op.f("ix_events_history_event_type_id"), table_name="events_history")
    op.drop_index(op.f("ix_events_history_event_type"), table_name="events_history")
    op.drop_index(op.f("ix_events_history_event_name"), table_name="events_history")
    op.drop_index(op.f("ix_events_history_end_time"), table_name="events_history")
    op.drop_index(op.f("ix_events_history_created_time"), table_name="events_history")
    op.drop_index(op.f("ix_events_history_case_id"), table_name="events_history")
    op.drop_index(op.f("ix_events_history_camera_id"), table_name="events_history")
    op.drop_table("events_history")
    # ### end Alembic commands ###
