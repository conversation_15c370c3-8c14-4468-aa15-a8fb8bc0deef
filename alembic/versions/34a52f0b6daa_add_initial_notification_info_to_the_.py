"""Add initial notification info to the contact information

Revision ID: 34a52f0b6daa
Revises: fea475f03858
Create Date: 2023-02-06 13:02:15.164969

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "34a52f0b6daa"
down_revision = "f5ee334a3168"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "contact_information",
        sa.Column("initial_notification_sent", sa.DateTime(timezone=True), nullable=True),
    )
    op.create_index(
        op.f("ix_contact_information_initial_notification_sent"),
        "contact_information",
        ["initial_notification_sent"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_contact_information_initial_notification_sent"), table_name="contact_information"
    )
    op.drop_column("contact_information", "initial_notification_sent")
    # ### end Alembic commands ###
