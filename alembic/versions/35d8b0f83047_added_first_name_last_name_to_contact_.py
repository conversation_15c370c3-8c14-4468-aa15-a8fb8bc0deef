"""Added first name last name to contact information

Revision ID: 35d8b0f83047
Revises: f2dc5efcf8f8
Create Date: 2023-07-31 12:29:35.763986

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "35d8b0f83047"
down_revision = "f2dc5efcf8f8"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("contact_information", sa.Column("first_name", sa.String(), nullable=True))
    op.add_column("contact_information", sa.Column("last_name", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("contact_information", "last_name")
    op.drop_column("contact_information", "first_name")
    # ### end Alembic commands ###
