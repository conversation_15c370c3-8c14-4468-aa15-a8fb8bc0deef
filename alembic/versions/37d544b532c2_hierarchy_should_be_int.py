"""Hierarchy should be int

Revision ID: 37d544b532c2
Revises: d622bc2ee03c
Create Date: 2022-11-16 13:53:43.476785

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "37d544b532c2"
down_revision = "d622bc2ee03c"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "case_procedures", "hierarchy", type_=sa.Integer(), postgresql_using="hierarchy::integer"
    )


def downgrade():
    op.alter_column(
        "case_procedures", "hierarchy", type_=sa.String(), postgresql_using="hierarchy::varchar"
    )
