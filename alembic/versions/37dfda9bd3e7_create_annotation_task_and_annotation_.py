"""Create annotation task and annotation task history tables

Revision ID: 37dfda9bd3e7
Revises: 0bf06196a76e
Create Date: 2021-11-16 16:22:14.040860

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "37dfda9bd3e7"
down_revision = "0bf06196a76e"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "annotation_tasks",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.Column("site_id", sa.String(), nullable=False),
        sa.Column("room_id", sa.String(), nullable=False),
        sa.Column("start_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("end_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("status", sa.String(), nullable=False),
        sa.Column("annotator_user_id", sa.String(), nullable=True),
        sa.Column("reviewer_user_id", sa.String(), nullable=True),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_annotation_tasks_annotator_user_id"),
        "annotation_tasks",
        ["annotator_user_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_tasks_end_time"),
        "annotation_tasks",
        ["end_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_tasks_org_id"), "annotation_tasks", ["org_id"], unique=False
    )
    op.create_index(
        op.f("ix_annotation_tasks_reviewer_user_id"),
        "annotation_tasks",
        ["reviewer_user_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_tasks_room_id"),
        "annotation_tasks",
        ["room_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_tasks_site_id"),
        "annotation_tasks",
        ["site_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_tasks_start_time"),
        "annotation_tasks",
        ["start_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_tasks_status"), "annotation_tasks", ["status"], unique=False
    )
    op.create_index(
        op.f("ix_annotation_tasks_updated_time"),
        "annotation_tasks",
        ["updated_time"],
        unique=False,
    )
    op.create_table(
        "annotation_tasks_history",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("annotation_task_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.Column("site_id", sa.String(), nullable=False),
        sa.Column("room_id", sa.String(), nullable=False),
        sa.Column("start_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("end_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("status", sa.String(), nullable=False),
        sa.Column("annotator_user_id", sa.String(), nullable=True),
        sa.Column("reviewer_user_id", sa.String(), nullable=True),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["annotation_task_id"],
            ["annotation_tasks.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_annotation_tasks_history_annotator_user_id"),
        "annotation_tasks_history",
        ["annotator_user_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_tasks_history_end_time"),
        "annotation_tasks_history",
        ["end_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_tasks_history_org_id"),
        "annotation_tasks_history",
        ["org_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_tasks_history_reviewer_user_id"),
        "annotation_tasks_history",
        ["reviewer_user_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_tasks_history_room_id"),
        "annotation_tasks_history",
        ["room_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_tasks_history_site_id"),
        "annotation_tasks_history",
        ["site_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_tasks_history_start_time"),
        "annotation_tasks_history",
        ["start_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_tasks_history_status"),
        "annotation_tasks_history",
        ["status"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_tasks_history_updated_time"),
        "annotation_tasks_history",
        ["updated_time"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_annotation_tasks_history_updated_time"),
        table_name="annotation_tasks_history",
    )
    op.drop_index(
        op.f("ix_annotation_tasks_history_status"),
        table_name="annotation_tasks_history",
    )
    op.drop_index(
        op.f("ix_annotation_tasks_history_start_time"),
        table_name="annotation_tasks_history",
    )
    op.drop_index(
        op.f("ix_annotation_tasks_history_site_id"),
        table_name="annotation_tasks_history",
    )
    op.drop_index(
        op.f("ix_annotation_tasks_history_room_id"),
        table_name="annotation_tasks_history",
    )
    op.drop_index(
        op.f("ix_annotation_tasks_history_reviewer_user_id"),
        table_name="annotation_tasks_history",
    )
    op.drop_index(
        op.f("ix_annotation_tasks_history_org_id"),
        table_name="annotation_tasks_history",
    )
    op.drop_index(
        op.f("ix_annotation_tasks_history_end_time"),
        table_name="annotation_tasks_history",
    )
    op.drop_index(
        op.f("ix_annotation_tasks_history_annotator_user_id"),
        table_name="annotation_tasks_history",
    )
    op.drop_table("annotation_tasks_history")
    op.drop_index(op.f("ix_annotation_tasks_updated_time"), table_name="annotation_tasks")
    op.drop_index(op.f("ix_annotation_tasks_status"), table_name="annotation_tasks")
    op.drop_index(op.f("ix_annotation_tasks_start_time"), table_name="annotation_tasks")
    op.drop_index(op.f("ix_annotation_tasks_site_id"), table_name="annotation_tasks")
    op.drop_index(op.f("ix_annotation_tasks_room_id"), table_name="annotation_tasks")
    op.drop_index(op.f("ix_annotation_tasks_reviewer_user_id"), table_name="annotation_tasks")
    op.drop_index(op.f("ix_annotation_tasks_org_id"), table_name="annotation_tasks")
    op.drop_index(op.f("ix_annotation_tasks_end_time"), table_name="annotation_tasks")
    op.drop_index(op.f("ix_annotation_tasks_annotator_user_id"), table_name="annotation_tasks")
    op.drop_table("annotation_tasks")
    # ### end Alembic commands ###
