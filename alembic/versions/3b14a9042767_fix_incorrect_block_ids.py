"""Fix incorrect block ids

Revision ID: 3b14a9042767
Revises: d2921723106b
Create Date: 2024-06-13 16:35:46.788675

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "3b14a9042767"
down_revision = "d2921723106b"
branch_labels = None
depends_on = None


def upgrade():
    # These block times belong to Tampa General's `Surgical Oncology` block with id `63715d8a-822e-4eb4-a013-a358f68ed2e9`
    op.execute(
        """
        UPDATE block_times
        SET block_id = '63715d8a-822e-4eb4-a013-a358f68ed2e9'
        WHERE id IN (
            'dd06dedb-cee1-4245-8d13-fa890a1d4ebe', '2f10520b-d5a8-4881-93fb-71056a7217f0', '0d06dbbe-58e7-448a-813a-a70ac81bef05', 'd4074978-6f29-4e3f-b7c0-d2845eb86cac'
        )
        """
    )

    # These block times belong to North Bay's `Trauma` block with id `7e21e41a-69c2-432f-891c-dd4b3d295fbd`
    op.execute(
        """
        UPDATE block_times
        SET block_id = '7e21e41a-69c2-432f-891c-dd4b3d295fbd'
        WHERE id IN (
            '3d1a2124-9e59-403c-a263-fd061663bc14',
            '6146b372-80d1-4f1d-947a-0b9442d92b54',
            '550491aa-e724-4ebc-915e-c8d89dd98d63',
            'f3048314-f361-4a53-af4d-039d060dfe93',
            'c7395ee3-9251-4460-acc9-694810273bbc',
            '65117689-800c-4703-824b-5786cbf126b1',
            'f0e8f45e-69eb-40e7-8cdf-d52f59a12b6b',
            '20a99f1e-df59-48cd-b777-4a91441769e5',
            '2b3a02c5-2b08-4141-9fdd-a424a5a9948e',
            'c9b99b01-9cd7-4e3a-8f18-5c6f396d8665',
            'ce913c57-9e43-460e-a75d-ca6f9c943c24',
            'cd942778-5563-4bd0-95a3-1c2c3621cc7c',
            'a9c79a94-2aed-4da4-9e52-634c47505367',
            'f82a05c7-697a-4c86-80f9-4c3f31e3be24',
            '739d2f37-0085-4696-a5a7-704daeb44803',
            '606a16f5-9813-438e-9cc8-5822764f8ba7',
            'de34d036-4e5f-4776-b8e4-caeed993d873',
            '42af3693-9d0f-45cb-af12-40a0da2dfb8e',
            'a94ce808-aba1-4536-a6d8-c41f3c942e96',
            '1fbc905f-2049-432a-b809-204d6c7d7361',
            '47b28b93-a2aa-43d1-a475-72acf791a4b0',
            '2e6e86fb-abf4-442c-a1f0-555c47b47633',
            '291a3b95-e9a4-438b-bd89-a838606c2e1f',
            'fc8b47b7-bf46-4aba-8480-dcde562392bf',
            'd58860ef-57b9-40a2-8485-c0b42a846f42',
            '73af6105-a560-4c57-b37f-bfba43377d32',
            '38334597-4e75-4b05-8f4c-4919ee6d7cf5',
            'c9629860-01db-42b0-bb36-1b7cd4a1c6ee',
            'f7a30475-9468-4f33-a910-2ce74608251a',
            '0dc1b2e6-5417-45b3-835e-35502c3750f5',
            '19aab20c-62bc-4692-83e7-7f6b96162d48',
            '63c9183e-0a34-46e3-ba45-f736e98d7b16',
            '2a672c3f-6c05-49ba-a864-6c0652cbdd94',
            '7458193f-b3a1-44ae-8d3f-9839832b95bd',
            '64c79ae5-3628-4d09-a493-b1f114ffbb04',
            '24883dc5-03fe-4cd1-8db5-988a48c46c6e',
            '7766387f-dd9e-421f-a95a-44a5672a053d',
            '15c16e91-fb7a-4086-abb3-48d21bcb890f',
            'c77d62cf-bf47-45da-a39d-0cacf516c73b',
            'eb7e2f74-cb24-4656-b815-1a425053b7e0',
            'd4edb4e4-214c-442d-92b2-3bb782ff4e62',
            '3e2ff5b9-ac2f-4b6f-b53b-627dcbad318b',
            'cc79c567-51c3-47de-aa7b-9e934b0a6f61'
        )
        """
    )


def downgrade():
    pass
