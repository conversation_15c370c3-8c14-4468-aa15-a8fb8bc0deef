"""Allow toggling idle detection

Revision ID: 3c0d6e6385e6
Revises: 57880d46c43a
Create Date: 2023-06-21 11:12:06.439429

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "3c0d6e6385e6"
down_revision = "57880d46c43a"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "annotation_task_types",
        sa.Column("detect_idle", sa.<PERSON>(), nullable=False, server_default="FALSE"),
    )

    # Set all existing task types to true for backwards compatibility
    op.execute("UPDATE annotation_task_types SET detect_idle = TRUE")


def downgrade() -> None:
    op.drop_column("annotation_task_types", "detect_idle")
