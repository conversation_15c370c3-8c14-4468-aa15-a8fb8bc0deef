"""Remove empty room prime time

Revision ID: 3f26f2deffbe
Revises: 0efe175dc19d
Create Date: 2024-11-01 13:28:40.724327

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "3f26f2deffbe"
down_revision = "5dd7a84e7ec9"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        """
        DELETE FROM room_prime_time_configs
        WHERE
            sunday_start_time IS NULL AND sunday_end_time IS NULL AND
            monday_start_time IS NULL AND monday_end_time IS NULL AND
            tuesday_start_time IS NULL AND tuesday_end_time IS NULL AND
            wednesday_start_time IS NULL AND wednesday_end_time IS NULL AND
            thursday_start_time IS NULL AND thursday_end_time IS NULL AND
            friday_start_time IS NULL AND friday_end_time IS NULL AND
            saturday_start_time IS NULL AND saturday_end_time IS NULL
        """
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # no downgrade
    pass
