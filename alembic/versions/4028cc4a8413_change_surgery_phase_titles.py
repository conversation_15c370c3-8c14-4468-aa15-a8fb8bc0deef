"""Change surgery phase titles

Revision ID: 4028cc4a8413
Revises: f993ffa11abe
Create Date: 2023-12-14 15:48:42.951209

"""

import sqlalchemy as sa
from sqlalchemy.sql import column, table

from alembic import op


# revision identifiers, used by Alembic.
revision = "4028cc4a8413"
down_revision = "f993ffa11abe"
branch_labels = None
depends_on = None

phase_types_table = table(
    "phase_types",
    column("id", sa.String(32)),
    column("title", sa.String(32)),
    column("description", sa.String()),
)


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        phase_types_table.update()
        .where(phase_types_table.c.id == "PRE_OPERATIVE")
        .values(
            title="Prep",
            description="A Prep (or Pre Operative) phase is measured as the period between when a patient wheels into the OR and the patient is draped.",
        )
    )
    op.execute(
        phase_types_table.update()
        .where(phase_types_table.c.id == "INTRA_OPERATIVE")
        .values(
            title="Surgery",
            description="An Surgery (or Intra Operative) phase is measured as the period between when a patient is draped and undraped.",
        )
    )
    op.execute(
        phase_types_table.update()
        .where(phase_types_table.c.id == "POST_OPERATIVE")
        .values(
            title="Wrap-up",
            description="A Wrap-up (or Post Operative) phase is measured as the period between when a patient is undraped and the patient is wheeled out.",
        )
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        phase_types_table.update()
        .where(phase_types_table.c.id == "PRE_OPERATIVE")
        .values(
            title="Pre Operative",
            description="A Pre Operative phase is measured as the period between when a patient wheels into the OR and the patient is draped.",
        )
    )
    op.execute(
        phase_types_table.update()
        .where(phase_types_table.c.id == "INTRA_OPERATIVE")
        .values(
            title="Intra Operative",
            description="An Intra Operative phase is measured as the period between when a patient is draped and undraped.",
        )
    )
    op.execute(
        phase_types_table.update()
        .where(phase_types_table.c.id == "POST_OPERATIVE")
        .values(
            title="Post Operative",
            description="A Post Operative phase is measured as the period between when a patient is undraped and the patient is wheeled out.",
        )
    )
    # ### end Alembic commands ###
