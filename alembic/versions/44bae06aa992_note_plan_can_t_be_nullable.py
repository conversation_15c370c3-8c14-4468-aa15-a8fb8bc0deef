"""Note plan can't be nullable

Revision ID: 44bae06aa992
Revises: d3f43fae5928
Create Date: 2024-01-17 10:22:11.520241

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "44bae06aa992"
down_revision = "d3f43fae5928"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("case_note_plan", "note", existing_type=sa.VARCHAR(), nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("case_note_plan", "note", existing_type=sa.VARCHAR(), nullable=True)
    # ### end Alembic commands ###
