"""Add phase_relationships table

Revision ID: 451a54bcd7d9
Revises: e09d106b0370
Create Date: 2022-06-21 16:39:05.341216

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "451a54bcd7d9"
down_revision = "e09d106b0370"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "phase_relationships",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("parent_phase_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("child_phase_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["child_phase_id"],
            ["phases.id"],
        ),
        sa.ForeignKeyConstraint(
            ["org_id"],
            ["organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["parent_phase_id"],
            ["phases.id"],
        ),
        sa.PrimaryKeyConstraint("parent_phase_id", "child_phase_id"),
    )
    op.create_index(
        op.f("ix_phase_relationships_created_time"),
        "phase_relationships",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_phase_relationships_org_id"), "phase_relationships", ["org_id"], unique=False
    )
    op.create_index(
        op.f("ix_phase_relationships_updated_time"),
        "phase_relationships",
        ["updated_time"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_phase_relationships_updated_time"), table_name="phase_relationships")
    op.drop_index(op.f("ix_phase_relationships_org_id"), table_name="phase_relationships")
    op.drop_index(op.f("ix_phase_relationships_created_time"), table_name="phase_relationships")
    op.drop_table("phase_relationships")
    # ### end Alembic commands ###
