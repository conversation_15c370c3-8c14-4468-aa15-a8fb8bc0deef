"""Add fcots config

Revision ID: 50c8d071447f
Revises: 3cc2edb453fa
Create Date: 2025-03-21 10:37:09.625283

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "50c8d071447f"
down_revision = "3cc2edb453fa"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "first_case_configs",
        sa.Column("id", sa.UUID(), server_default=sa.text("gen_random_uuid()"), nullable=False),
        sa.Column("site_id", sa.String(), nullable=False),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("sunday_start_time", sa.Time(), nullable=True),
        sa.Column("sunday_end_time", sa.Time(), nullable=True),
        sa.Column("monday_start_time", sa.Time(), nullable=True),
        sa.Column("monday_end_time", sa.Time(), nullable=True),
        sa.Column("tuesday_start_time", sa.Time(), nullable=True),
        sa.Column("tuesday_end_time", sa.Time(), nullable=True),
        sa.Column("wednesday_start_time", sa.Time(), nullable=True),
        sa.Column("wednesday_end_time", sa.Time(), nullable=True),
        sa.Column("thursday_start_time", sa.Time(), nullable=True),
        sa.Column("thursday_end_time", sa.Time(), nullable=True),
        sa.Column("friday_start_time", sa.Time(), nullable=True),
        sa.Column("friday_end_time", sa.Time(), nullable=True),
        sa.Column("saturday_start_time", sa.Time(), nullable=True),
        sa.Column("saturday_end_time", sa.Time(), nullable=True),
        sa.CheckConstraint(
            "friday_start_time IS NULL AND friday_end_time IS NULL OR friday_start_time IS NOT NULL AND friday_end_time IS NOT NULL AND friday_start_time < friday_end_time",
            name="ck_first_case_configs_friday_valid_times",
        ),
        sa.CheckConstraint(
            "monday_start_time IS NULL AND monday_end_time IS NULL OR monday_start_time IS NOT NULL AND monday_end_time IS NOT NULL AND monday_start_time < monday_end_time",
            name="ck_first_case_configs_monday_valid_times",
        ),
        sa.CheckConstraint(
            "saturday_start_time IS NULL AND saturday_end_time IS NULL OR saturday_start_time IS NOT NULL AND saturday_end_time IS NOT NULL AND saturday_start_time < saturday_end_time",
            name="ck_first_case_configs_saturday_valid_times",
        ),
        sa.CheckConstraint(
            "sunday_start_time IS NULL AND sunday_end_time IS NULL OR sunday_start_time IS NOT NULL AND sunday_end_time IS NOT NULL AND sunday_start_time < sunday_end_time",
            name="ck_first_case_configs_sunday_valid_times",
        ),
        sa.CheckConstraint(
            "thursday_start_time IS NULL AND thursday_end_time IS NULL OR thursday_start_time IS NOT NULL AND thursday_end_time IS NOT NULL AND thursday_start_time < thursday_end_time",
            name="ck_first_case_configs_thursday_valid_times",
        ),
        sa.CheckConstraint(
            "tuesday_start_time IS NULL AND tuesday_end_time IS NULL OR tuesday_start_time IS NOT NULL AND tuesday_end_time IS NOT NULL AND tuesday_start_time < tuesday_end_time",
            name="ck_first_case_configs_tuesday_valid_times",
        ),
        sa.CheckConstraint(
            "wednesday_start_time IS NULL AND wednesday_end_time IS NULL OR wednesday_start_time IS NOT NULL AND wednesday_end_time IS NOT NULL AND wednesday_start_time < wednesday_end_time",
            name="ck_first_case_configs_wednesday_valid_times",
        ),
        sa.ForeignKeyConstraint(["site_id"], ["sites.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id", "site_id"),
    )
    op.create_index(
        op.f("ix_first_case_configs_created_time"),
        "first_case_configs",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_first_case_configs_site_id"), "first_case_configs", ["site_id"], unique=True
    )
    op.create_index(
        op.f("ix_first_case_configs_updated_time"),
        "first_case_configs",
        ["updated_time"],
        unique=False,
    )
    op.drop_index(
        "ix_room_prime_time_configs_friday_end_time", table_name="room_prime_time_configs"
    )
    op.drop_index(
        "ix_room_prime_time_configs_friday_start_time", table_name="room_prime_time_configs"
    )
    op.drop_index(
        "ix_room_prime_time_configs_monday_end_time", table_name="room_prime_time_configs"
    )
    op.drop_index(
        "ix_room_prime_time_configs_monday_start_time", table_name="room_prime_time_configs"
    )
    op.drop_index(
        "ix_room_prime_time_configs_saturday_end_time", table_name="room_prime_time_configs"
    )
    op.drop_index(
        "ix_room_prime_time_configs_saturday_start_time", table_name="room_prime_time_configs"
    )
    op.drop_index(
        "ix_room_prime_time_configs_sunday_end_time", table_name="room_prime_time_configs"
    )
    op.drop_index(
        "ix_room_prime_time_configs_sunday_start_time", table_name="room_prime_time_configs"
    )
    op.drop_index(
        "ix_room_prime_time_configs_thursday_end_time", table_name="room_prime_time_configs"
    )
    op.drop_index(
        "ix_room_prime_time_configs_thursday_start_time", table_name="room_prime_time_configs"
    )
    op.drop_index(
        "ix_room_prime_time_configs_tuesday_end_time", table_name="room_prime_time_configs"
    )
    op.drop_index(
        "ix_room_prime_time_configs_tuesday_start_time", table_name="room_prime_time_configs"
    )
    op.drop_index(
        "ix_room_prime_time_configs_wednesday_end_time", table_name="room_prime_time_configs"
    )
    op.drop_index(
        "ix_room_prime_time_configs_wednesday_start_time", table_name="room_prime_time_configs"
    )
    op.drop_constraint(
        "room_prime_time_configs_room_id_key", "room_prime_time_configs", type_="unique"
    )
    op.drop_index("ix_room_prime_time_configs_room_id", table_name="room_prime_time_configs")
    op.create_index(
        op.f("ix_room_prime_time_configs_room_id"),
        "room_prime_time_configs",
        ["room_id"],
        unique=True,
    )
    op.drop_index(
        "ix_site_prime_time_configs_friday_end_time", table_name="site_prime_time_configs"
    )
    op.drop_index(
        "ix_site_prime_time_configs_friday_start_time", table_name="site_prime_time_configs"
    )
    op.drop_index(
        "ix_site_prime_time_configs_monday_end_time", table_name="site_prime_time_configs"
    )
    op.drop_index(
        "ix_site_prime_time_configs_monday_start_time", table_name="site_prime_time_configs"
    )
    op.drop_index(
        "ix_site_prime_time_configs_saturday_end_time", table_name="site_prime_time_configs"
    )
    op.drop_index(
        "ix_site_prime_time_configs_saturday_start_time", table_name="site_prime_time_configs"
    )
    op.drop_index(
        "ix_site_prime_time_configs_sunday_end_time", table_name="site_prime_time_configs"
    )
    op.drop_index(
        "ix_site_prime_time_configs_sunday_start_time", table_name="site_prime_time_configs"
    )
    op.drop_index(
        "ix_site_prime_time_configs_thursday_end_time", table_name="site_prime_time_configs"
    )
    op.drop_index(
        "ix_site_prime_time_configs_thursday_start_time", table_name="site_prime_time_configs"
    )
    op.drop_index(
        "ix_site_prime_time_configs_tuesday_end_time", table_name="site_prime_time_configs"
    )
    op.drop_index(
        "ix_site_prime_time_configs_tuesday_start_time", table_name="site_prime_time_configs"
    )
    op.drop_index(
        "ix_site_prime_time_configs_wednesday_end_time", table_name="site_prime_time_configs"
    )
    op.drop_index(
        "ix_site_prime_time_configs_wednesday_start_time", table_name="site_prime_time_configs"
    )
    op.drop_constraint(
        "site_prime_time_configs_site_id_key", "site_prime_time_configs", type_="unique"
    )
    op.drop_index("ix_site_prime_time_configs_site_id", table_name="site_prime_time_configs")
    op.create_index(
        op.f("ix_site_prime_time_configs_site_id"),
        "site_prime_time_configs",
        ["site_id"],
        unique=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_site_prime_time_configs_site_id"), table_name="site_prime_time_configs")
    op.create_index(
        "ix_site_prime_time_configs_site_id", "site_prime_time_configs", ["site_id"], unique=False
    )
    op.create_unique_constraint(
        "site_prime_time_configs_site_id_key", "site_prime_time_configs", ["site_id"]
    )
    op.create_index(
        "ix_site_prime_time_configs_wednesday_start_time",
        "site_prime_time_configs",
        ["wednesday_start_time"],
        unique=False,
    )
    op.create_index(
        "ix_site_prime_time_configs_wednesday_end_time",
        "site_prime_time_configs",
        ["wednesday_end_time"],
        unique=False,
    )
    op.create_index(
        "ix_site_prime_time_configs_tuesday_start_time",
        "site_prime_time_configs",
        ["tuesday_start_time"],
        unique=False,
    )
    op.create_index(
        "ix_site_prime_time_configs_tuesday_end_time",
        "site_prime_time_configs",
        ["tuesday_end_time"],
        unique=False,
    )
    op.create_index(
        "ix_site_prime_time_configs_thursday_start_time",
        "site_prime_time_configs",
        ["thursday_start_time"],
        unique=False,
    )
    op.create_index(
        "ix_site_prime_time_configs_thursday_end_time",
        "site_prime_time_configs",
        ["thursday_end_time"],
        unique=False,
    )
    op.create_index(
        "ix_site_prime_time_configs_sunday_start_time",
        "site_prime_time_configs",
        ["sunday_start_time"],
        unique=False,
    )
    op.create_index(
        "ix_site_prime_time_configs_sunday_end_time",
        "site_prime_time_configs",
        ["sunday_end_time"],
        unique=False,
    )
    op.create_index(
        "ix_site_prime_time_configs_saturday_start_time",
        "site_prime_time_configs",
        ["saturday_start_time"],
        unique=False,
    )
    op.create_index(
        "ix_site_prime_time_configs_saturday_end_time",
        "site_prime_time_configs",
        ["saturday_end_time"],
        unique=False,
    )
    op.create_index(
        "ix_site_prime_time_configs_monday_start_time",
        "site_prime_time_configs",
        ["monday_start_time"],
        unique=False,
    )
    op.create_index(
        "ix_site_prime_time_configs_monday_end_time",
        "site_prime_time_configs",
        ["monday_end_time"],
        unique=False,
    )
    op.create_index(
        "ix_site_prime_time_configs_friday_start_time",
        "site_prime_time_configs",
        ["friday_start_time"],
        unique=False,
    )
    op.create_index(
        "ix_site_prime_time_configs_friday_end_time",
        "site_prime_time_configs",
        ["friday_end_time"],
        unique=False,
    )
    op.drop_index(op.f("ix_room_prime_time_configs_room_id"), table_name="room_prime_time_configs")
    op.create_index(
        "ix_room_prime_time_configs_room_id", "room_prime_time_configs", ["room_id"], unique=False
    )
    op.create_unique_constraint(
        "room_prime_time_configs_room_id_key", "room_prime_time_configs", ["room_id"]
    )
    op.create_index(
        "ix_room_prime_time_configs_wednesday_start_time",
        "room_prime_time_configs",
        ["wednesday_start_time"],
        unique=False,
    )
    op.create_index(
        "ix_room_prime_time_configs_wednesday_end_time",
        "room_prime_time_configs",
        ["wednesday_end_time"],
        unique=False,
    )
    op.create_index(
        "ix_room_prime_time_configs_tuesday_start_time",
        "room_prime_time_configs",
        ["tuesday_start_time"],
        unique=False,
    )
    op.create_index(
        "ix_room_prime_time_configs_tuesday_end_time",
        "room_prime_time_configs",
        ["tuesday_end_time"],
        unique=False,
    )
    op.create_index(
        "ix_room_prime_time_configs_thursday_start_time",
        "room_prime_time_configs",
        ["thursday_start_time"],
        unique=False,
    )
    op.create_index(
        "ix_room_prime_time_configs_thursday_end_time",
        "room_prime_time_configs",
        ["thursday_end_time"],
        unique=False,
    )
    op.create_index(
        "ix_room_prime_time_configs_sunday_start_time",
        "room_prime_time_configs",
        ["sunday_start_time"],
        unique=False,
    )
    op.create_index(
        "ix_room_prime_time_configs_sunday_end_time",
        "room_prime_time_configs",
        ["sunday_end_time"],
        unique=False,
    )
    op.create_index(
        "ix_room_prime_time_configs_saturday_start_time",
        "room_prime_time_configs",
        ["saturday_start_time"],
        unique=False,
    )
    op.create_index(
        "ix_room_prime_time_configs_saturday_end_time",
        "room_prime_time_configs",
        ["saturday_end_time"],
        unique=False,
    )
    op.create_index(
        "ix_room_prime_time_configs_monday_start_time",
        "room_prime_time_configs",
        ["monday_start_time"],
        unique=False,
    )
    op.create_index(
        "ix_room_prime_time_configs_monday_end_time",
        "room_prime_time_configs",
        ["monday_end_time"],
        unique=False,
    )
    op.create_index(
        "ix_room_prime_time_configs_friday_start_time",
        "room_prime_time_configs",
        ["friday_start_time"],
        unique=False,
    )
    op.create_index(
        "ix_room_prime_time_configs_friday_end_time",
        "room_prime_time_configs",
        ["friday_end_time"],
        unique=False,
    )
    op.drop_index(op.f("ix_first_case_configs_updated_time"), table_name="first_case_configs")
    op.drop_index(op.f("ix_first_case_configs_site_id"), table_name="first_case_configs")
    op.drop_index(op.f("ix_first_case_configs_created_time"), table_name="first_case_configs")
    op.drop_table("first_case_configs")
    # ### end Alembic commands ###
