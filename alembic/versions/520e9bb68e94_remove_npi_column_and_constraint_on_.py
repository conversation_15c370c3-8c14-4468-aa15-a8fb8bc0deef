"""Remove NPI column and constraint on staff table

Revision ID: 520e9bb68e94
Revises: e12b98419da7
Create Date: 2022-06-03 11:15:06.213799

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "520e9bb68e94"
down_revision = "e12b98419da7"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_staff_npi", table_name="staff")
    op.drop_constraint("uq_orgId_npi", "staff", type_="unique")
    op.create_unique_constraint(
        "uq_orgId_firstName_lastName", "staff", ["org_id", "first_name", "last_name"]
    )
    op.drop_column("staff", "npi")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("staff", sa.Column("npi", sa.VARCHAR(), autoincrement=False, nullable=False))
    op.drop_constraint("uq_orgId_firstName_lastName", "staff", type_="unique")
    op.create_unique_constraint("uq_orgId_npi", "staff", ["org_id", "npi"])
    op.create_index("ix_staff_npi", "staff", ["npi"], unique=False)
    # ### end Alembic commands ###
