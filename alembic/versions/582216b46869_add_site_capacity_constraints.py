"""Add site capacity constraints

Revision ID: 582216b46869
Revises: 5bccc3b39284
Create Date: 2024-10-22 13:57:46.953464

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "582216b46869"
down_revision = "5bccc3b39284"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "site_capacity_constraints",
        sa.<PERSON>umn("id", sa.UUID(), server_default=sa.text("gen_random_uuid()"), nullable=False),
        sa.Column("day_of_week", sa.Integer(), nullable=False),
        sa.Column("count", sa.Integer(), nullable=False),
        sa.Column("start_time", sa.Time(), nullable=False),
        sa.Column("site_id", sa.String(), nullable=False),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.CheckConstraint("count > 0", name="check_count_is_positive"),
        sa.CheckConstraint("day_of_week >= 0 AND day_of_week <= 6", name="check_day_of_week_valid"),
        sa.ForeignKeyConstraint(["site_id"], ["sites.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id", "site_id"),
    )
    op.create_index(
        op.f("ix_site_capacity_constraints_count"),
        "site_capacity_constraints",
        ["count"],
        unique=False,
    )
    op.create_index(
        op.f("ix_site_capacity_constraints_created_time"),
        "site_capacity_constraints",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_site_capacity_constraints_day_of_week"),
        "site_capacity_constraints",
        ["day_of_week"],
        unique=False,
    )
    op.create_index(
        op.f("ix_site_capacity_constraints_site_id"),
        "site_capacity_constraints",
        ["site_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_site_capacity_constraints_start_time"),
        "site_capacity_constraints",
        ["start_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_site_capacity_constraints_updated_time"),
        "site_capacity_constraints",
        ["updated_time"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_site_capacity_constraints_updated_time"), table_name="site_capacity_constraints"
    )
    op.drop_index(
        op.f("ix_site_capacity_constraints_start_time"), table_name="site_capacity_constraints"
    )
    op.drop_index(
        op.f("ix_site_capacity_constraints_site_id"), table_name="site_capacity_constraints"
    )
    op.drop_index(
        op.f("ix_site_capacity_constraints_day_of_week"), table_name="site_capacity_constraints"
    )
    op.drop_index(
        op.f("ix_site_capacity_constraints_created_time"), table_name="site_capacity_constraints"
    )
    op.drop_index(
        op.f("ix_site_capacity_constraints_count"), table_name="site_capacity_constraints"
    )
    op.drop_table("site_capacity_constraints")
    # ### end Alembic commands ###
