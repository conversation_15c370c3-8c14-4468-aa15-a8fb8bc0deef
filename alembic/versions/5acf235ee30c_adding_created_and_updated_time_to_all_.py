"""Adding created and updated time to all tables.


Revision ID: 5acf235ee30c
Revises: 8173d07930c2
Create Date: 2022-04-05 10:08:53.085008

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "5acf235ee30c"
down_revision = "8173d07930c2"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "annotation_tasks",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
    )
    op.alter_column(
        "annotation_tasks",
        "updated_time",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        nullable=True,
        existing_server_default=sa.text("now()"),
    )
    op.create_index(
        op.f("ix_annotation_tasks_created_time"),
        "annotation_tasks",
        ["created_time"],
        unique=False,
    )
    op.add_column(
        "cameras",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
    )
    op.add_column(
        "cameras",
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
    )
    op.create_index(op.f("ix_cameras_created_time"), "cameras", ["created_time"], unique=False)
    op.create_index(op.f("ix_cameras_updated_time"), "cameras", ["updated_time"], unique=False)
    op.add_column(
        "organizations",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
    )
    op.add_column(
        "organizations",
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
    )
    op.create_index(
        op.f("ix_organizations_created_time"),
        "organizations",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_organizations_updated_time"),
        "organizations",
        ["updated_time"],
        unique=False,
    )
    op.add_column(
        "rooms",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
    )
    op.add_column(
        "rooms",
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
    )
    op.create_index(op.f("ix_rooms_created_time"), "rooms", ["created_time"], unique=False)
    op.create_index(op.f("ix_rooms_updated_time"), "rooms", ["updated_time"], unique=False)
    op.add_column(
        "sites",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
    )
    op.add_column(
        "sites",
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
    )
    op.create_index(op.f("ix_sites_created_time"), "sites", ["created_time"], unique=False)
    op.create_index(op.f("ix_sites_updated_time"), "sites", ["updated_time"], unique=False)
    op.drop_column("sites", "charge_desk_phone_number")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "sites",
        sa.Column("charge_desk_phone_number", sa.VARCHAR(), autoincrement=False, nullable=True),
    )
    op.drop_index(op.f("ix_sites_updated_time"), table_name="sites")
    op.drop_index(op.f("ix_sites_created_time"), table_name="sites")
    op.drop_column("sites", "updated_time")
    op.drop_column("sites", "created_time")
    op.drop_index(op.f("ix_rooms_updated_time"), table_name="rooms")
    op.drop_index(op.f("ix_rooms_created_time"), table_name="rooms")
    op.drop_column("rooms", "updated_time")
    op.drop_column("rooms", "created_time")
    op.drop_index(op.f("ix_organizations_updated_time"), table_name="organizations")
    op.drop_index(op.f("ix_organizations_created_time"), table_name="organizations")
    op.drop_column("organizations", "updated_time")
    op.drop_column("organizations", "created_time")
    op.drop_index(op.f("ix_cameras_updated_time"), table_name="cameras")
    op.drop_index(op.f("ix_cameras_created_time"), table_name="cameras")
    op.drop_column("cameras", "updated_time")
    op.drop_column("cameras", "created_time")
    op.drop_index(op.f("ix_annotation_tasks_created_time"), table_name="annotation_tasks")
    op.alter_column(
        "annotation_tasks",
        "updated_time",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        nullable=False,
        existing_server_default=sa.text("now()"),
    )
    op.drop_column("annotation_tasks", "created_time")
    # ### end Alembic commands ###
