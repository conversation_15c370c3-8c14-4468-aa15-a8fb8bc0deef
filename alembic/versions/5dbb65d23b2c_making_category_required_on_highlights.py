"""Making category required on highlights

Revision ID: 5dbb65d23b2c
Revises: aaa3cb785f60
Create Date: 2022-01-27 17:12:23.314539

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "5dbb65d23b2c"
down_revision = "aaa3cb785f60"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("update highlights set category='turnover' where description ilike '%turn%over%'")
    op.execute("update highlights set category='timeout' where description ilike '%time%out%'")
    op.execute(
        "update highlights set category='terminal_clean' where description ilike '%terminal%'"
    )
    # Spot checking after the only Highlights that are uncategorized, are some of our earliest
    # These include the beeping OR table, pre-op, post-op, delays, and 1st case start
    op.execute("update highlights set category='uncategorized' where category is null")
    op.alter_column("highlights", "category", existing_type=sa.VARCHAR(), nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by <PERSON>embic - please adjust! ###
    op.alter_column("highlights", "category", existing_type=sa.VARCHAR(), nullable=True)
    # ### end Alembic commands ###
