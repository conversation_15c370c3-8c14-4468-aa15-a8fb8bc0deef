"""Change task type to uuid

Revision ID: 5eabc936d18f
Revises: ab97e4a5e1bc
Create Date: 2023-03-31 13:49:34.104203

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.sql import column, table

from alembic import op

# revision identifiers, used by Alembic.
revision = "5eabc936d18f"
down_revision = "ab97e4a5e1bc"
branch_labels = None
depends_on = None


task_type_ids_and_new_uuids = (
    ("surgery", "831027c5-942b-477a-b490-93ab731ebba6"),
    ("light", "f775f20b-0aff-48b2-9808-35622ad554ed"),
    ("mop", "4d426c27-fd3c-4b88-8cb4-fdf2338b97d8"),
)


annotation_task_types = table("annotation_task_types", column("id"), column("id_temp"))
annotation_task_schedules = table(
    "annotation_task_schedules",
    column("annotation_task_type_id"),
    column("annotation_task_type_id_temp"),
)
annotation_tasks = table("annotation_tasks", column("type_id"), column("type_id_temp"))
annotation_tasks_history = table(
    "annotation_tasks_history", column("type_id"), column("type_id_temp")
)


def upgrade() -> None:
    # Add temp uuid columns to each table
    op.add_column(
        "annotation_task_types", sa.Column("id_temp", postgresql.UUID(as_uuid=True), nullable=True)
    )
    op.add_column(
        "annotation_task_schedules",
        sa.Column("annotation_task_type_id_temp", postgresql.UUID(as_uuid=True), nullable=True),
    )
    op.add_column(
        "annotation_tasks", sa.Column("type_id_temp", postgresql.UUID(as_uuid=True), nullable=True)
    )
    op.add_column(
        "annotation_tasks_history",
        sa.Column("type_id_temp", postgresql.UUID(as_uuid=True), nullable=True),
    )

    # Change columns to uuid values
    for id, uuid in task_type_ids_and_new_uuids:
        op.execute(
            annotation_task_types.update()
            .where(annotation_task_types.c.id == id)
            .values(id_temp=uuid)
        )
        op.execute(
            annotation_task_schedules.update()
            .where(annotation_task_schedules.c.annotation_task_type_id == id)
            .values(annotation_task_type_id_temp=uuid)
        )
        op.execute(
            annotation_tasks.update()
            .where(annotation_tasks.c.type_id == id)
            .values(type_id_temp=uuid)
        )
        op.execute(
            annotation_tasks_history.update()
            .where(annotation_tasks_history.c.type_id == id)
            .values(type_id_temp=uuid)
        )

    # Drop indexes / foreign key constraints
    _drop_foreign_key_constraints_and_indexes()

    # Change annotation_task_types id_temp to be id column
    op.drop_constraint("annotation_task_types_pkey", "annotation_task_types", type_="primary")
    op.drop_column("annotation_task_types", "id")
    op.alter_column("annotation_task_types", "id_temp", new_column_name="id", nullable=False)
    op.create_primary_key("annotation_task_types_pkey", "annotation_task_types", ["id"])

    # Drop remaining temp columns, rename to original names
    op.drop_column("annotation_task_schedules", "annotation_task_type_id")
    op.alter_column(
        "annotation_task_schedules",
        "annotation_task_type_id_temp",
        new_column_name="annotation_task_type_id",
        nullable=False,
    )

    op.drop_column("annotation_tasks", "type_id")
    op.alter_column(
        "annotation_tasks",
        "type_id_temp",
        new_column_name="type_id",
        nullable=False,
    )

    op.drop_column("annotation_tasks_history", "type_id")
    op.alter_column(
        "annotation_tasks_history",
        "type_id_temp",
        new_column_name="type_id",
        nullable=False,
    )

    # Add back indexes, foreign key constraints
    _add_back_foreign_key_constraints_and_indexes()


def downgrade() -> None:
    # Drop indexes / foreign key constraints
    _drop_foreign_key_constraints_and_indexes()

    # Change column types to string
    op.alter_column("annotation_task_types", "id", type_=sa.String())
    op.alter_column("annotation_task_schedules", "annotation_task_type_id", type_=sa.String())
    op.alter_column("annotation_tasks", "type_id", type_=sa.String())
    op.alter_column("annotation_tasks_history", "type_id", type_=sa.String())

    # Change columns from uuid values to string values
    for id, uuid in task_type_ids_and_new_uuids:
        op.execute(
            annotation_task_types.update().where(annotation_task_types.c.id == uuid).values(id=id)
        )
        op.execute(
            annotation_task_schedules.update()
            .where(annotation_task_schedules.c.annotation_task_type_id == uuid)
            .values(annotation_task_type_id=id)
        )
        op.execute(
            annotation_tasks.update().where(annotation_tasks.c.type_id == uuid).values(type_id=id)
        )
        op.execute(
            annotation_tasks_history.update()
            .where(annotation_tasks_history.c.type_id == uuid)
            .values(type_id=id)
        )

    # Add back indexes, foreign key constraints
    _add_back_foreign_key_constraints_and_indexes()


def _drop_foreign_key_constraints_and_indexes() -> None:
    """
    Drops foreign key constaints and indexes for tables that depend
    on annotation_task_types
    """
    op.drop_constraint(
        "annotation_tasks_history_type_id_fkey",
        "annotation_tasks_history",
        type_="foreignkey",
    )
    op.drop_index(
        op.f("ix_annotation_tasks_history_type_id"),
        table_name="annotation_tasks_history",
    )

    op.drop_constraint("annotation_tasks_type_id_fkey", "annotation_tasks", type_="foreignkey")
    op.drop_index(op.f("ix_annotation_tasks_type_id"), table_name="annotation_tasks")

    op.drop_constraint(
        "annotation_task_schedules_annotation_task_type_id_fkey",
        "annotation_task_schedules",
        type_="foreignkey",
    )
    op.drop_index(
        op.f("ix_annotation_task_schedules_annotation_task_type_id"),
        table_name="annotation_task_schedules",
    )


def _add_back_foreign_key_constraints_and_indexes() -> None:
    """
    Adds back foreign key constaints and indexes for tables that depend
    on annotation_task_types
    """
    op.create_index(
        op.f("ix_annotation_tasks_type_id"),
        "annotation_tasks",
        ["type_id"],
        unique=False,
    )
    op.create_foreign_key(
        "annotation_tasks_type_id_fkey",
        "annotation_tasks",
        "annotation_task_types",
        ["type_id"],
        ["id"],
    )

    op.create_index(
        op.f("ix_annotation_tasks_history_type_id"),
        "annotation_tasks_history",
        ["type_id"],
        unique=False,
    )
    op.create_foreign_key(
        "annotation_tasks_history_type_id_fkey",
        "annotation_tasks_history",
        "annotation_task_types",
        ["type_id"],
        ["id"],
    )

    op.create_index(
        op.f("ix_annotation_task_schedules_annotation_task_type_id"),
        "annotation_task_schedules",
        ["annotation_task_type_id"],
        unique=False,
    )
    op.create_foreign_key(
        "annotation_task_schedules_annotation_task_type_id_fkey",
        "annotation_task_schedules",
        "annotation_task_types",
        ["annotation_task_type_id"],
        ["id"],
    )
