"""Add invalidation_reason

Revision ID: 5fe466d1e97b
Revises: c4bd4f0aa237
Create Date: 2022-11-07 14:19:59.410094

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "5fe466d1e97b"
down_revision = "c4bd4f0aa237"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("phases", sa.Column("invalidation_reason", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("phases", "invalidation_reason")
    # ### end Alembic commands ###
