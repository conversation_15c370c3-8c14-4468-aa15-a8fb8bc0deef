"""Create cameras, rooms, sites, organizations tables

Revision ID: 63be24bf3a40
Revises: ad2b9dafe792
Create Date: 2021-12-13 14:32:38.937043

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "63be24bf3a40"
down_revision = "ad2b9dafe792"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "organizations",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("auth0_org_id", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_organizations_auth0_org_id"),
        "organizations",
        ["auth0_org_id"],
        unique=False,
    )
    op.create_table(
        "sites",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("charge_desk_phone_number", sa.String(), nullable=True),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.Column("timezone", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["org_id"],
            ["organizations.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_sites_org_id"), "sites", ["org_id"], unique=False)
    op.create_table(
        "rooms",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("room_phone_number", sa.String(), nullable=True),
        sa.Column("site_id", sa.String(), nullable=False),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["org_id"],
            ["organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["site_id"],
            ["sites.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_rooms_org_id"), "rooms", ["org_id"], unique=False)
    op.create_index(op.f("ix_rooms_site_id"), "rooms", ["site_id"], unique=False)
    op.create_table(
        "cameras",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("room_id", sa.String(), nullable=False),
        sa.Column("site_id", sa.String(), nullable=False),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("rtsp_url", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["org_id"],
            ["organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["room_id"],
            ["rooms.id"],
        ),
        sa.ForeignKeyConstraint(
            ["site_id"],
            ["sites.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_cameras_org_id"), "cameras", ["org_id"], unique=False)
    op.create_index(op.f("ix_cameras_room_id"), "cameras", ["room_id"], unique=False)
    op.create_index(op.f("ix_cameras_site_id"), "cameras", ["site_id"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_cameras_site_id"), table_name="cameras")
    op.drop_index(op.f("ix_cameras_room_id"), table_name="cameras")
    op.drop_index(op.f("ix_cameras_org_id"), table_name="cameras")
    op.drop_table("cameras")
    op.drop_index(op.f("ix_rooms_site_id"), table_name="rooms")
    op.drop_index(op.f("ix_rooms_org_id"), table_name="rooms")
    op.drop_table("rooms")
    op.drop_index(op.f("ix_sites_org_id"), table_name="sites")
    op.drop_table("sites")
    op.drop_index(op.f("ix_organizations_auth0_org_id"), table_name="organizations")
    op.drop_table("organizations")
    # ### end Alembic commands ###
