"""Adding observations model

Revision ID: 64af2811b15f
Revises: 6ac53cf9565a
Create Date: 2022-07-05 13:41:25.682023

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "64af2811b15f"
down_revision = "80502b8dc05e"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    observation_types_table = op.create_table(
        "observation_types",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("description", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_observation_types_created_time"),
        "observation_types",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_observation_types_updated_time"),
        "observation_types",
        ["updated_time"],
        unique=False,
    )
    op.bulk_insert(
        observation_types_table,
        [
            {
                "id": "OBSERVED_ANESTHESIA_START",
                "name": "Observed Anesthesia Start",
            },
            {
                "id": "OBSERVED_ANESTHESIA_FINISH",
                "name": "Observed Anesthesia Finish",
            },
            {
                "id": "OBSERVED_CASE_START",
                "name": "Observed Case Start",
            },
            {
                "id": "OBSERVED_CASE_FINISH",
                "name": "Observed Case Finish",
            },
            {
                "id": "OBSERVED_CLEANUP_START",
                "name": "Observed Cleanup Start",
            },
            {
                "id": "OBSERVED_CLEANUP_COMPLETE",
                "name": "Observed Cleanup Complete",
            },
            {
                "id": "OBSERVED_PREP_START",
                "name": "Observed Prep Start",
            },
            {
                "id": "OBSERVED_PREP_COMPLETE",
                "name": "Observed Prep Complete",
            },
            {
                "id": "IN_ROOM",
                "name": "In Room",
            },
            {
                "id": "OUT_OF_ROOM",
                "name": "Out of Room",
            },
        ],
    )
    op.create_table(
        "observations",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("site_id", sa.String(), nullable=True),
        sa.Column("room_id", sa.String(), nullable=True),
        sa.Column("case_id", sa.String(), nullable=False),
        sa.Column("type_id", sa.String(), nullable=False),
        sa.Column("observation_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("recorded_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["case_id"],
            ["cases.case_id"],
        ),
        sa.ForeignKeyConstraint(
            ["org_id"],
            ["organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["type_id"],
            ["observation_types.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_observations_case_id"), "observations", ["case_id"], unique=False)
    op.create_index(
        op.f("ix_observations_created_time"), "observations", ["created_time"], unique=False
    )
    op.create_index(
        op.f("ix_observations_observation_time"), "observations", ["observation_time"], unique=False
    )
    op.create_index(op.f("ix_observations_org_id"), "observations", ["org_id"], unique=False)
    op.create_index(
        op.f("ix_observations_recorded_time"), "observations", ["recorded_time"], unique=False
    )
    op.create_index(op.f("ix_observations_type_id"), "observations", ["type_id"], unique=False)
    op.create_index(
        op.f("ix_observations_updated_time"), "observations", ["updated_time"], unique=False
    )
    op.create_unique_constraint(
        "uq_case_observation",
        "observations",
        [
            "org_id",
            "case_id",
            "type_id",
            "observation_time",
        ],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_observations_updated_time"), table_name="observations")
    op.drop_index(op.f("ix_observations_type_id"), table_name="observations")
    op.drop_index(op.f("ix_observations_recorded_time"), table_name="observations")
    op.drop_index(op.f("ix_observations_org_id"), table_name="observations")
    op.drop_index(op.f("ix_observations_observation_time"), table_name="observations")
    op.drop_index(op.f("ix_observations_created_time"), table_name="observations")
    op.drop_index(op.f("ix_observations_case_id"), table_name="observations")
    op.drop_constraint("uq_case_observation", "observations", type_="unique")
    op.drop_table("observations")
    op.drop_index(op.f("ix_observation_types_updated_time"), table_name="observation_types")
    op.drop_index(op.f("ix_observation_types_created_time"), table_name="observation_types")
    op.drop_table("observation_types")
    # ### end Alembic commands ###
