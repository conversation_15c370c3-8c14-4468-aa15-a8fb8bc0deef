"""add_block_time_overrides_history_table

Revision ID: 65aecf5e1eb3
Revises: 7e2ed2bd97a1
Create Date: 2025-05-08 15:41:01.875583

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "65aecf5e1eb3"
down_revision = "7e2ed2bd97a1"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "block_time_overrides_history",
        sa.Column("block_time_id", sa.UUID(), autoincrement=False, nullable=False),
        sa.Column("block_time_minutes", sa.Integer(), autoincrement=False, nullable=False),
        sa.Column("user_id", sa.String(), autoincrement=False, nullable=False),
        sa.Column("note", sa.String(), autoincrement=False, nullable=True),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "version",
            sa.Integer(),
            server_default=sa.text("1"),
            autoincrement=False,
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("block_time_id", "version"),
        sqlite_autoincrement=True,
    )
    op.create_index(
        op.f("ix_block_time_overrides_history_created_time"),
        "block_time_overrides_history",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_time_overrides_history_updated_time"),
        "block_time_overrides_history",
        ["updated_time"],
        unique=False,
    )
    op.add_column(
        "block_time_overrides",
        sa.Column("version", sa.Integer(), server_default=sa.text("1"), nullable=False),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("block_time_overrides", "version")
    op.drop_index(
        op.f("ix_block_time_overrides_history_updated_time"),
        table_name="block_time_overrides_history",
    )
    op.drop_index(
        op.f("ix_block_time_overrides_history_created_time"),
        table_name="block_time_overrides_history",
    )
    op.drop_table("block_time_overrides_history")
    # ### end Alembic commands ###
