"""create_anesthesia_table

Revision ID: 66628421b0c2
Revises: 2f7bc47b9995
Create Date: 2023-11-29 16:55:57.172092

"""

import sqlalchemy as sa

from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "66628421b0c2"
down_revision = "2f7bc47b9995"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "anesthesias",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            server_default=sa.text("gen_random_uuid()"),
            nullable=False,
        ),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["org_id"],
            ["organizations.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("org_id", "name", name="uq_anesthesia_orgId_name"),
    )
    op.create_index(
        op.f("ix_anesthesias_created_time"), "anesthesias", ["created_time"], unique=False
    )
    op.create_index(op.f("ix_anesthesias_name"), "anesthesias", ["name"], unique=False)
    op.create_index(op.f("ix_anesthesias_org_id"), "anesthesias", ["org_id"], unique=False)
    op.create_index(
        op.f("ix_anesthesias_updated_time"), "anesthesias", ["updated_time"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_anesthesias_updated_time"), table_name="anesthesias")
    op.drop_index(op.f("ix_anesthesias_org_id"), table_name="anesthesias")
    op.drop_index(op.f("ix_anesthesias_name"), table_name="anesthesias")
    op.drop_index(op.f("ix_anesthesias_created_time"), table_name="anesthesias")
    op.drop_table("anesthesias")
    # ### end Alembic commands ###
