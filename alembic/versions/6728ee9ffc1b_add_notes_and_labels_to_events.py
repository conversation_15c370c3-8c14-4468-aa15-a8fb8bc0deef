"""Add notes and labels to events

Revision ID: 6728ee9ffc1b
Revises: fa7b0385eae3
Create Date: 2021-03-29 14:33:09.750474

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "6728ee9ffc1b"
down_revision = "fa7b0385eae3"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("events", sa.Column("labels", sa.ARRAY(sa.String()), nullable=True))
    op.add_column("events", sa.Column("notes", sa.String(), nullable=True))


def downgrade():
    op.drop_column("events", "notes")
    op.drop_column("events", "labels")
