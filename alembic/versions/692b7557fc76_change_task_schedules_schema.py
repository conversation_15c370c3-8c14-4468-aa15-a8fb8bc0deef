"""Change task schedules schema

Revision ID: 692b7557fc76
Revises: 5eabc936d18f
Create Date: 2023-04-05 11:34:25.127881

"""

import uuid

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.orm import Session
from sqlalchemy.sql import column, select, table

from alembic import op

# revision identifiers, used by Alembic.
revision = "692b7557fc76"
down_revision = "5eabc936d18f"
branch_labels = None
depends_on = None


def upgrade() -> None:
    annotation_task_schedules_sites = op.create_table(
        "annotation_task_schedules_sites",
        sa.Column("schedule_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("site_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["schedule_id"],
            ["annotation_task_schedules.id"],
        ),
        sa.ForeignKeyConstraint(
            ["site_id"],
            ["sites.id"],
        ),
        sa.PrimaryKeyConstraint("schedule_id", "site_id"),
    )

    # For each task schedule, add a row to the new table
    rows = []
    with Session(bind=op.get_bind()) as session:
        rows = session.execute(
            select(table("annotation_task_schedules"), column("id"), column("site_id"))
        ).fetchall()

    if len(rows) > 0:
        # A single row will be the survivor. We assume all have a 06:00 `start_time` and 12hr `interval`
        schedule_id = rows[0][0]

        op.bulk_insert(
            annotation_task_schedules_sites,
            [{"schedule_id": schedule_id, "site_id": row[1]} for row in rows],
        )

        # Delete all rows except the survivor
        op.execute(
            table("annotation_task_schedules", column("id"))
            .delete()
            .where(column("id") != schedule_id)
        )

    # Drop old column / indexes / constraints
    op.drop_index("ix_annotation_task_schedules_site_id", table_name="annotation_task_schedules")
    op.drop_constraint(
        "annotation_task_schedules_site_id_fkey", "annotation_task_schedules", type_="foreignkey"
    )
    op.drop_column("annotation_task_schedules", "site_id")

    # Add new unique constraint.
    # For some reason, there wasn't a unique constraint added originally, so we don't need to drop any.
    op.create_unique_constraint(
        None, "annotation_task_schedules", ["annotation_task_type_id", "start_time", "interval"]
    )


def downgrade() -> None:
    op.add_column(
        "annotation_task_schedules",
        sa.Column("site_id", sa.VARCHAR(), autoincrement=False, nullable=True),
    )
    op.drop_constraint(
        "annotation_task_schedules_annotation_task_type_id_start_tim_key",
        "annotation_task_schedules",
        type_="unique",
    )

    # Query data from the new table and old table to prepare to insert into old table
    rows = []
    with Session(bind=op.get_bind()) as session:
        annotation_task_schedules_sites = table(
            "annotation_task_schedules_sites",
            column("schedule_id"),
            column("site_id"),
        )

        annotation_task_schedules = table(
            "annotation_task_schedules",
            column("id"),
            column("annotation_task_type_id"),
            column("start_time"),
            column("interval"),
        )

        rows = session.execute(
            select(
                [
                    annotation_task_schedules.c.id,
                    annotation_task_schedules.c.annotation_task_type_id,
                    annotation_task_schedules.c.start_time,
                    annotation_task_schedules.c.interval,
                    annotation_task_schedules_sites.c.site_id,
                ]
            ).select_from(
                annotation_task_schedules.join(
                    annotation_task_schedules_sites,
                    annotation_task_schedules.c.id == annotation_task_schedules_sites.c.schedule_id,
                )
            )
        ).fetchall()

    # Drop new table
    op.drop_table("annotation_task_schedules_sites")

    # Group rows by schedule id
    rows_by_schedule_id = {}  # type: ignore
    for row in rows:
        schedule_id = row[0]
        if schedule_id not in rows_by_schedule_id:
            rows_by_schedule_id[schedule_id] = []

        rows_by_schedule_id[schedule_id].append(row)

    # For each row in annotation_task_schedules_sites table, add a row to the old table
    for schedule_id, schedule_rows in rows_by_schedule_id.items():
        op.bulk_insert(
            table(
                "annotation_task_schedules",
                column("id"),
                column("annotation_task_type_id"),
                column("start_time"),
                column("interval"),
                column("site_id"),
            ),
            [
                {
                    "id": uuid.uuid4(),
                    "annotation_task_type_id": sr[1],
                    "start_time": sr[2],
                    "interval": sr[3],
                    "site_id": sr[4],
                }
                for sr in schedule_rows
            ],
        )

    # Delete all rows that were not just inserted
    op.execute(
        table("annotation_task_schedules", column("site_id"))
        .delete()
        .where(column("site_id").is_(None))
    )

    # Restore old indexes / constraints
    op.alter_column("annotation_task_schedules", "site_id", nullable=False)
    op.create_foreign_key(
        "annotation_task_schedules_site_id_fkey",
        "annotation_task_schedules",
        "sites",
        ["site_id"],
        ["id"],
    )
    op.create_index(
        "ix_annotation_task_schedules_site_id",
        "annotation_task_schedules",
        ["site_id"],
        unique=False,
    )
