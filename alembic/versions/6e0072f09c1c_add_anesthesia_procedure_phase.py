"""Add ANESTHESIA_PROCEDURE phase

Revision ID: 6e0072f09c1c
Revises: 34a52f0b6daa
Create Date: 2023-02-21 09:50:03.222105

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "6e0072f09c1c"
down_revision = "34a52f0b6daa"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """
        INSERT INTO phase_types (ID, Title, Slug, Description) VALUES (
         'ANESTHESIA_PROCEDURE',
         'Anesthesia Procedure',
         'anesthesia-procedure',
         'An Anesthesia Procedure Phase is measured as the period between when a patient is draped for anesthesia and when they are undraped for anesthesia')
    """
    )


def downgrade():
    op.execute(
        """
        DELETE FROM phase_types WHERE ID = 'ANESTHESIA_PROCEDURE'
    """
    )
