"""Add bayesian model fields

Revision ID: 6e1a1bb29377
Revises: e8d159246fb7
Create Date: 2025-02-14 13:58:19.874813

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "6e1a1bb29377"
down_revision = "e8d159246fb7"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "case_forecast", sa.Column("bayesian_duration_minutes", sa.Float(), nullable=True)
    )
    op.add_column(
        "case_forecast", sa.Column("bayesian_end_time", sa.DateTime(timezone=True), nullable=True)
    )
    op.create_index(
        op.f("ix_case_forecast_bayesian_duration_minutes"),
        "case_forecast",
        ["bayesian_duration_minutes"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_bayesian_end_time"),
        "case_forecast",
        ["bayesian_end_time"],
        unique=False,
    )
    op.add_column(
        "case_forecast_history",
        sa.Column("bayesian_duration_minutes", sa.Float(), autoincrement=False, nullable=True),
    )
    op.add_column(
        "case_forecast_history",
        sa.Column(
            "bayesian_end_time", sa.DateTime(timezone=True), autoincrement=False, nullable=True
        ),
    )
    op.create_index(
        op.f("ix_case_forecast_history_bayesian_duration_minutes"),
        "case_forecast_history",
        ["bayesian_duration_minutes"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_forecast_history_bayesian_end_time"),
        "case_forecast_history",
        ["bayesian_end_time"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_case_forecast_history_bayesian_end_time"), table_name="case_forecast_history"
    )
    op.drop_index(
        op.f("ix_case_forecast_history_bayesian_duration_minutes"),
        table_name="case_forecast_history",
    )
    op.drop_column("case_forecast_history", "bayesian_end_time")
    op.drop_column("case_forecast_history", "bayesian_duration_minutes")
    op.drop_index(op.f("ix_case_forecast_bayesian_end_time"), table_name="case_forecast")
    op.drop_index(op.f("ix_case_forecast_bayesian_duration_minutes"), table_name="case_forecast")
    op.drop_column("case_forecast", "bayesian_end_time")
    op.drop_column("case_forecast", "bayesian_duration_minutes")
    # ### end Alembic commands ###
