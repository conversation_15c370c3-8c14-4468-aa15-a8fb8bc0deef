"""add robot drape event types

Revision ID: 7295dc2a7329
Revises: 3b7a35ac964d
Create Date: 2023-04-21 13:26:46.178217

"""

import sqlalchemy as sa
from sqlalchemy.sql import column, table

from alembic import op

# revision identifiers, used by Alembic.
revision = "7295dc2a7329"
down_revision = "3b7a35ac964d"

eventType = table(
    "event_types",
    column("id", sa.String(32)),
    column("name", sa.String(64)),
    column("type", sa.String()),
    column("color", sa.String(16)),
    column("hidden", sa.Bo<PERSON>an()),
)


def upgrade() -> None:
    # Gray - 30
    op.execute(
        eventType.insert().values(
            id="robot_drape_start",
            name="Robot draping start",
            type="uncategorized",
            color="#e6e6e6",
            hidden=False,
        )
    )
    # Gray - 60
    op.execute(
        eventType.insert().values(
            id="robot_drape_end",
            name="Robot draping end",
            type="uncategorized",
            color="#737373",
            hidden=False,
        )
    )
    # Green - 20
    op.execute(
        eventType.insert().values(
            id="ceiling_cleaning_start",
            name="Ceiling cleaning start",
            type="terminal_cleaning",
            color="#9fddc4",
            hidden=False,
        )
    )
    # Green - 40
    op.execute(
        eventType.insert().values(
            id="wall_cleaning_start",
            name="Wall cleaning start",
            type="terminal_cleaning",
            color="#3fbb89",
            hidden=False,
        )
    )
    # Green - 50
    op.execute(
        eventType.insert().values(
            id="floor_cleaning_start",
            name="Floor cleaning start",
            type="terminal_cleaning",
            color="#0FAA6B",
            hidden=False,
        )
    )
    # Green - 70
    op.execute(
        eventType.insert().values(
            id="furniture_cleaning_start",
            name="Furniture cleaning start",
            type="terminal_cleaning",
            color="#096640",
            hidden=False,
        )
    )
    # Green - 80
    op.execute(
        eventType.insert().values(
            id="under_OR_equipment_cleaning_start",
            name="Under OR equipment cleaning start",
            type="terminal_cleaning",
            color="#06442b",
            hidden=False,
        )
    )
    # Teal - 20
    op.execute(
        eventType.insert().values(
            id="under_OR_table_pads_cleaning_start",
            name="Under OR table pads cleaning start",
            type="terminal_cleaning",
            color="#a1dbdb",
            hidden=False,
        )
    )
    # Teal - 40
    op.execute(
        eventType.insert().values(
            id="nursing_station_cleaning_start",
            name="Nursing station cleaning start",
            type="terminal_cleaning",
            color="#43b6b6",
            hidden=False,
        )
    )
    # Teal - 50
    op.execute(
        eventType.insert().values(
            id="door_to_the_core_cleaning_start",
            name="Door to the core cleaning start",
            type="terminal_cleaning",
            color="#14A4A4",
            hidden=False,
        )
    )
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    op.execute(eventType.delete().where(eventType.c.id == "robot_drape_start"))
    op.execute(eventType.delete().where(eventType.c.id == "robot_drape_end"))
    op.execute(eventType.delete().where(eventType.c.id == "ceiling_cleaning_start"))
    op.execute(eventType.delete().where(eventType.c.id == "wall_cleaning_start"))
    op.execute(eventType.delete().where(eventType.c.id == "floor_cleaning_start"))
    op.execute(eventType.delete().where(eventType.c.id == "furniture_cleaning_start"))
    op.execute(eventType.delete().where(eventType.c.id == "under_OR_equipment_cleaning_start"))
    op.execute(eventType.delete().where(eventType.c.id == "under_OR_table_pads_cleaning_start"))
    op.execute(eventType.delete().where(eventType.c.id == "nursing_station_cleaning_start"))
    op.execute(eventType.delete().where(eventType.c.id == "door_to_the_core_cleaning_start"))

    pass
    # ### end Alembic commands ###
