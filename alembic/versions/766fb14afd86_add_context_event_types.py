"""Add context event types

Revision ID: 766fb14afd86
Revises: 1b88cb94de2b
Create Date: 2023-07-13 16:51:45.319621

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "766fb14afd86"
down_revision = "1b88cb94de2b"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute("update annotation_task_types set event_types='{}' where event_types is NULL")

    op.add_column(
        "annotation_task_types",
        sa.Column(
            "context_event_types",
            postgresql.ARRAY(sa.String()),
            nullable=False,
            server_default="{}",
        ),
    )

    op.alter_column(
        "annotation_task_types",
        "event_types",
        existing_type=postgresql.ARRAY(sa.VARCHAR()),
        server_default="{}",
        nullable=False,
    )


def downgrade() -> None:
    op.alter_column(
        "annotation_task_types",
        "event_types",
        existing_type=postgresql.ARRAY(sa.VARCHAR()),
        nullable=True,
    )

    op.drop_column("annotation_task_types", "context_event_types")
