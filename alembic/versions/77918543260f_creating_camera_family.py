"""Creating camera family

Revision ID: 77918543260f
Revises: c82c7541352a
Create Date: 2024-07-10 10:30:07.823811

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "77918543260f"
down_revision = "c82c7541352a"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Create the new column nullable true
    op.add_column("cameras", sa.Column("family", sa.String(), nullable=True))
    # Update all current entries to have a value
    op.execute("UPDATE cameras set family='geovision'")
    # Modify the column to be nullable false
    op.alter_column("cameras", "family", nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("cameras", "family")
    # ### end Alembic commands ###
