"""Add missing OBX types

Revision ID: 7952810eeb10
Revises: 64af2811b15f
Create Date: 2022-07-12 17:32:54.059488

"""

import sqlalchemy as sa

# revision identifiers, used by Alembic.
from sqlalchemy import MetaData, Table

from alembic import op

revision = "7952810eeb10"
down_revision = "64af2811b15f"
branch_labels = None
depends_on = None

NEW_OBX_TYPES = [
    {"id": "OBSERVED_IN_FACILITY", "name": "Observed In Facility"},
    {"id": "OBSERVED_IN_PRE_PROCEDURE", "name": "Observed In Pre-Procedure"},
    {"id": "OBSERVED_PRE_PROCEDURE_COMPLETE", "name": "Observed Pre-Procedure Complete"},
    {"id": "OBSERVED_OUT_OF_PRE_PROCEDURE", "name": "Observed Out of Pre-Procedure"},
    {"id": "OBSERVED_ANESTHESIA_AVAILABLE", "name": "Observed Anesthesia Available"},
    {"id": "OBSERVED_ANESTHESIA_READY", "name": "Observed Anesthesia Ready"},
    {"id": "OBSERVED_CASE_CLOSING", "name": "Observed Case Closing"},
    {"id": "OBSERVED_PROCEDURAL_CARE_COMPLETE", "name": "Observed Procedural Care Complete"},
    {"id": "OBSERVED_IN_PACU", "name": "Observed In PACU"},
    {
        "id": "OBSERVED_PATIENT_MOVED_TO_ANOTHER_OR_ROOM",
        "name": "Observed Patient moved to another OR room",
    },
    {"id": "OBSERVED_PHYSICIAN_AVAILABLE", "name": "Observed Physician Available"},
    {"id": "OBSERVED_SEDATION_END", "name": "Observed Sedation End"},
]


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("observations", "room_id")
    op.drop_column("observations", "site_id")
    # meta = MetaData(bind=op.get_bind())
    meta = MetaData()
    meta.reflect(only=("observation_types",), bind=op.get_bind())
    observation_types_table = Table("observation_types", meta)
    op.bulk_insert(observation_types_table, NEW_OBX_TYPES)
    op.execute(
        """
        UPDATE observation_types SET id = 'OBSERVED_OUT_OF_ROOM', name = 'Observed Out of Room'
            WHERE id = 'OUT_OF_ROOM';
        """
    )
    op.execute(
        """
        UPDATE observation_types SET id = 'OBSERVED_IN_ROOM', name = 'Observed In Room'
            WHERE id = 'IN_ROOM';
        """
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "observations", sa.Column("site_id", sa.VARCHAR(), autoincrement=False, nullable=True)
    )
    op.add_column(
        "observations", sa.Column("room_id", sa.VARCHAR(), autoincrement=False, nullable=True)
    )
    op.execute(
        f"""
        DELETE FROM observation_types WHERE id IN ({",".join(["'{}'".format(observation_type["id"]) for observation_type in NEW_OBX_TYPES])})
    """
    )
    op.execute(
        """
        UPDATE observation_types SET id = 'OUT_OF_ROOM', name = 'Out of Room'
            WHERE id = 'OBSERVED_OUT_OF_ROOM';
        UPDATE observation_types SET id = 'IN_ROOM', name = 'In Room'
            WHERE id = 'OBSERVED_IN_ROOM';
    """
    )
    # ### end Alembic commands ###
