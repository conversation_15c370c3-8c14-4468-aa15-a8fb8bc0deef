"""Make block name constraint case insensitive

Revision ID: 79b4fa5611d7
Revises: 21ccf35fbe84
Create Date: 2024-09-20 12:54:28.970846

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "79b4fa5611d7"
down_revision = "21ccf35fbe84"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        "ix_unique_block_name_org_id_not_archived",
        table_name="blocks",
        postgresql_where="(archived_time IS NULL)",
    )
    op.create_index(
        "ix_unique_block_name_org_id_not_archived",
        "blocks",
        [sa.text("lower(name)"), "org_id"],
        unique=True,
        postgresql_where="(archived_time IS NULL)",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        "ix_unique_block_name_org_id_not_archived",
        table_name="blocks",
        postgresql_where="(archived_time IS NULL)",
    )
    op.create_index(
        "ix_unique_block_name_org_id_not_archived",
        "blocks",
        ["name", "org_id"],
        unique=True,
        postgresql_where="(archived_time IS NULL)",
    )
    # ### end Alembic commands ###
