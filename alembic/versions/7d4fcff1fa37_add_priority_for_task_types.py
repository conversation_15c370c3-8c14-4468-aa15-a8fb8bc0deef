"""Add priority for task types

Revision ID: 7d4fcff1fa37
Revises: 4d95be6664b0
Create Date: 2023-05-12 16:39:05.920413

"""

import sqlalchemy as sa
from sqlalchemy.sql import and_, column

from alembic import op

# revision identifiers, used by Alembic.
revision = "7d4fcff1fa37"
down_revision = "4d95be6664b0"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column("annotation_task_types", sa.Column("priority", sa.SmallInteger(), nullable=True))

    op.execute("UPDATE annotation_task_types SET priority=5")
    op.execute("UPDATE annotation_task_types SET priority=2 WHERE name='Surgery'")

    op.alter_column("annotation_task_types", "priority", nullable=False)

    op.create_check_constraint(
        "ck_annotation_task_types_priority_within_range_1_to_5_inclusive",
        "annotation_task_types",
        and_(column("priority") >= 1, column("priority") <= 5),
    )


def downgrade() -> None:
    op.drop_constraint(
        "ck_annotation_task_types_priority_within_range_1_to_5_inclusive",
        "annotation_task_types",
        type_="check",
    )

    op.drop_column("annotation_task_types", "priority")
