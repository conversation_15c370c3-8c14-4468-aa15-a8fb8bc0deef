"""add_block_time_overrides_table

Revision ID: 7e2ed2bd97a1
Revises: 81ed1c82caf6
Create Date: 2025-05-08 14:05:20.521187

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "7e2ed2bd97a1"
down_revision = "81ed1c82caf6"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "block_time_overrides",
        sa.<PERSON>umn("block_time_id", sa.UUID(), nullable=False),
        sa.Column("block_time_minutes", sa.Integer(), nullable=False),
        sa.Column("user_id", sa.String(), nullable=False),
        sa.Column("note", sa.String(), nullable=True),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["block_time_id"],
            ["block_times.id"],
        ),
        sa.PrimaryKeyConstraint("block_time_id"),
    )
    op.create_index(
        op.f("ix_block_time_overrides_created_time"),
        "block_time_overrides",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_time_overrides_updated_time"),
        "block_time_overrides",
        ["updated_time"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_block_time_overrides_updated_time"), table_name="block_time_overrides")
    op.drop_index(op.f("ix_block_time_overrides_created_time"), table_name="block_time_overrides")
    op.drop_table("block_time_overrides")
    # ### end Alembic commands ###
