"""Fix site launch table

Revision ID: 81ed1c82caf6
Revises: 2f6a0c4e66e2
Create Date: 2025-05-08 11:59:36.370080

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "81ed1c82caf6"
down_revision = "2f6a0c4e66e2"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "site_launch_information", sa.Column("actual_launch_date", sa.Date(), nullable=True)
    )
    op.add_column(
        "site_launch_information", sa.Column("anticipated_launch_date", sa.Date(), nullable=True)
    )
    op.drop_index("ix_site_launch_information_launch_date", table_name="site_launch_information")
    op.create_index(
        op.f("ix_site_launch_information_actual_launch_date"),
        "site_launch_information",
        ["actual_launch_date"],
        unique=False,
    )
    op.create_index(
        op.f("ix_site_launch_information_anticipated_launch_date"),
        "site_launch_information",
        ["anticipated_launch_date"],
        unique=False,
    )
    op.drop_column("site_launch_information", "launch_date")
    # ### end Alembic commands ###


def downgrade():
    op.execute("TRUNCATE TABLE site_launch_information")
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "site_launch_information",
        sa.Column("launch_date", sa.DATE(), autoincrement=False, nullable=False),
    )
    op.drop_index(
        op.f("ix_site_launch_information_anticipated_launch_date"),
        table_name="site_launch_information",
    )
    op.drop_index(
        op.f("ix_site_launch_information_actual_launch_date"), table_name="site_launch_information"
    )
    op.create_index(
        "ix_site_launch_information_launch_date",
        "site_launch_information",
        ["launch_date"],
        unique=False,
    )
    op.drop_column("site_launch_information", "anticipated_launch_date")
    op.drop_column("site_launch_information", "actual_launch_date")
    # ### end Alembic commands ###
