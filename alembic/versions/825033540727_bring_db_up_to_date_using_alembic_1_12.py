"""Bring <PERSON> up to date using alembic 1.12

Revision ID: 825033540727
Revises: 2b54653c451e
Create Date: 2024-03-01 16:59:21.628466

"""

import sqlalchemy as sa

from alembic import op
from alembic_postgresql_enum import TableReference

# revision identifiers, used by Alembic.
revision = "825033540727"
down_revision = "3232e8d99f51"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("SET statement_timeout TO 60000;")  # 1 minute for this session

    # ### commands auto generated by <PERSON><PERSON>bic - please adjust! ###

    # ### XXX: HANDWRITTEN BY PATRICK ###
    # This enum name was the result of a bad copy/paste but <PERSON><PERSON><PERSON> doesn't know that so we have to
    # manually rename the type to the expected one here.
    op.execute("ALTER TYPE cancelled_reason_enum RENAME TO boardviewtype")
    # ### XXX: END HANDWRITTEN BY PATRICK ###

    sa.Enum(
        "IDLE",
        "OUTAGE",
        "SENSITIVE_CONTENT",
        "STAFF_IN_TRAINING",
        "CAMERAS_OUT_OF_SYNC",
        name="cancelledreason",
    ).create(op.get_bind())
    sa.Enum(
        "NOT_STARTED",
        "IN_PROGRESS",
        "READY_FOR_REVIEW",
        "IN_REVIEW",
        "DONE",
        "CANCELLED",
        "BLOCKED",
        name="taskstatus",
    ).create(op.get_bind())
    sa.Enum("INVALID", "VALID", name="phasestatus").create(op.get_bind())
    op.alter_column(
        "annotation_task_types",
        "priority",
        existing_type=sa.SMALLINT(),
        type_=sa.Integer(),
        existing_nullable=False,
    )
    op.alter_column(
        "annotation_tasks",
        "status",
        existing_type=sa.VARCHAR(),
        type_=sa.Enum(
            "NOT_STARTED",
            "IN_PROGRESS",
            "READY_FOR_REVIEW",
            "IN_REVIEW",
            "DONE",
            "CANCELLED",
            "BLOCKED",
            name="taskstatus",
        ),
        existing_nullable=False,
        postgresql_using="status::taskstatus",
    )
    op.alter_column(
        "annotation_tasks",
        "cancelled_reason",
        existing_type=sa.VARCHAR(),
        type_=sa.Enum(
            "IDLE",
            "OUTAGE",
            "SENSITIVE_CONTENT",
            "STAFF_IN_TRAINING",
            "CAMERAS_OUT_OF_SYNC",
            name="cancelledreason",
        ),
        existing_nullable=True,
        postgresql_using="cancelled_reason::cancelledreason",
    )
    op.alter_column(
        "annotation_tasks_history",
        "status",
        existing_type=sa.VARCHAR(),
        type_=sa.Enum(
            "NOT_STARTED",
            "IN_PROGRESS",
            "READY_FOR_REVIEW",
            "IN_REVIEW",
            "DONE",
            "CANCELLED",
            "BLOCKED",
            name="taskstatus",
        ),
        existing_nullable=False,
        autoincrement=False,
        postgresql_using="status::taskstatus",
    )
    op.alter_column(
        "annotation_tasks_history",
        "cancelled_reason",
        existing_type=sa.VARCHAR(),
        type_=sa.Enum(
            "IDLE",
            "OUTAGE",
            "SENSITIVE_CONTENT",
            "STAFF_IN_TRAINING",
            "CAMERAS_OUT_OF_SYNC",
            name="cancelledreason",
        ),
        existing_nullable=True,
        autoincrement=False,
        postgresql_using="cancelled_reason::cancelledreason",
    )
    op.alter_column(
        "highlight_feedback",
        "user_id",
        existing_type=sa.TEXT(),
        type_=sa.String(),
        existing_nullable=False,
    )

    # ### XXX: HANDWRITTEN BY PATRICK ###
    # Alembic couldn't figure out how to cast the existing default value for phase status to the
    # enum. So, we just unset the default here and re-set it below with the right type.
    op.alter_column("phases", "status", server_default=None)
    # ### XXX: END HANDWRITTEN BY PATRICK ###

    op.alter_column(
        "phases",
        "status",
        existing_type=sa.VARCHAR(),
        type_=sa.Enum("INVALID", "VALID", name="phasestatus"),
        existing_nullable=False,
        # ### XXX: HANDWRITTEN BY PATRICK ###
        # See above...
        server_default=sa.text("'VALID'::phasestatus"),
        # ### XXX: END HANDWRITTEN BY PATRICK ###
        postgresql_using="status::phasestatus",
    )
    op.sync_enum_values(
        "public",
        "patientclass",
        [
            "EMERGENCY",
            "HOSPITAL_OUTPATIENT_SURGERY",
            "INPATIENT",
            "SURGERY_ADMIT",
            "PRE_ADMIT",
            "OBSERVATION",
            "OTHER",
        ],
        [
            TableReference(table_schema="public", table_name="cases", column_name="patient_class"),
            TableReference(
                table_schema="public", table_name="cases_history", column_name="patient_class"
            ),
        ],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        "public",
        "patientclass",
        [
            "EMERGENCY",
            "HOSPITAL_OUTPATIENT_SURGERY",
            "INPATIENT",
            "SURGERY_ADMIT",
            "PRE_ADMIT",
            "OBSERVATION",
        ],
        [
            TableReference(table_schema="public", table_name="cases", column_name="patient_class"),
            TableReference(
                table_schema="public", table_name="cases_history", column_name="patient_class"
            ),
        ],
        enum_values_to_rename=[],
    )
    op.alter_column("phases", "status", server_default=None)
    op.alter_column(
        "phases",
        "status",
        existing_type=sa.Enum("INVALID", "VALID", name="phasestatus"),
        type_=sa.VARCHAR(),
        existing_nullable=False,
        server_default=sa.text("'VALID'::character varying"),
    )
    op.alter_column(
        "highlight_feedback",
        "user_id",
        existing_type=sa.String(),
        type_=sa.TEXT(),
        existing_nullable=False,
    )
    op.alter_column(
        "annotation_tasks_history",
        "cancelled_reason",
        existing_type=sa.Enum(
            "IDLE",
            "OUTAGE",
            "SENSITIVE_CONTENT",
            "STAFF_IN_TRAINING",
            "CAMERAS_OUT_OF_SYNC",
            name="cancelledreason",
        ),
        type_=sa.VARCHAR(),
        existing_nullable=True,
        autoincrement=False,
    )
    op.alter_column(
        "annotation_tasks_history",
        "status",
        existing_type=sa.Enum(
            "NOT_STARTED",
            "IN_PROGRESS",
            "READY_FOR_REVIEW",
            "IN_REVIEW",
            "DONE",
            "CANCELLED",
            "BLOCKED",
            name="taskstatus",
        ),
        type_=sa.VARCHAR(),
        existing_nullable=False,
        autoincrement=False,
    )
    op.alter_column(
        "annotation_tasks",
        "cancelled_reason",
        existing_type=sa.Enum(
            "IDLE",
            "OUTAGE",
            "SENSITIVE_CONTENT",
            "STAFF_IN_TRAINING",
            "CAMERAS_OUT_OF_SYNC",
            name="cancelledreason",
        ),
        type_=sa.VARCHAR(),
        existing_nullable=True,
    )
    op.alter_column(
        "annotation_tasks",
        "status",
        existing_type=sa.Enum(
            "NOT_STARTED",
            "IN_PROGRESS",
            "READY_FOR_REVIEW",
            "IN_REVIEW",
            "DONE",
            "CANCELLED",
            "BLOCKED",
            name="taskstatus",
        ),
        type_=sa.VARCHAR(),
        existing_nullable=False,
    )
    op.alter_column(
        "annotation_task_types",
        "priority",
        existing_type=sa.Integer(),
        type_=sa.SMALLINT(),
        existing_nullable=False,
    )
    sa.Enum("INVALID", "VALID", name="phasestatus").drop(op.get_bind())
    sa.Enum(
        "NOT_STARTED",
        "IN_PROGRESS",
        "READY_FOR_REVIEW",
        "IN_REVIEW",
        "DONE",
        "CANCELLED",
        "BLOCKED",
        name="taskstatus",
    ).drop(op.get_bind())
    sa.Enum(
        "IDLE",
        "OUTAGE",
        "SENSITIVE_CONTENT",
        "STAFF_IN_TRAINING",
        "CAMERAS_OUT_OF_SYNC",
        name="cancelledreason",
    ).drop(op.get_bind())
    op.execute("ALTER TYPE boardviewtype RENAME TO cancelled_reason_enum")
    # ### end Alembic commands ###
