"""Deleting generated block times for reprocessing

Revision ID: 82aee357497a
Revises: d0c35153c737
Create Date: 2025-03-13 10:03:22.900110

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "82aee357497a"
down_revision = "d0c35153c737"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """
            DELETE FROM public.block_times
            WHERE released_from IS NOT NULL;
        """
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
