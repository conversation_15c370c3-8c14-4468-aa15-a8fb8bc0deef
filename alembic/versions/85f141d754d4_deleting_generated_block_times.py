"""deleting generated block times

Revision ID: 85f141d754d4
Revises: a9ae0d31a744
Create Date: 2025-03-07 12:25:28.668788

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "85f141d754d4"
down_revision = "a9ae0d31a744"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """
            DELETE FROM public.block_times
            WHERE released_from IS NOT NULL;
        """
    )


def downgrade():
    # downgrade is a no-op
    pass
