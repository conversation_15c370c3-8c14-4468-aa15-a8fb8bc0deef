"""Add case start source to case forecast

Revision ID: 8ab0cea7fe7f
Revises: 1dab82cc7ab0
Create Date: 2025-05-21 15:23:01.266526

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "8ab0cea7fe7f"
down_revision = "1dab82cc7ab0"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("case_forecast", sa.Column("case_start_source", sa.String(), nullable=True))
    op.create_index(
        op.f("ix_case_forecast_case_start_source"),
        "case_forecast",
        ["case_start_source"],
        unique=False,
    )
    op.add_column(
        "case_forecast_history",
        sa.Column("case_start_source", sa.String(), autoincrement=False, nullable=True),
    )
    op.create_index(
        op.f("ix_case_forecast_history_case_start_source"),
        "case_forecast_history",
        ["case_start_source"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_case_forecast_history_case_start_source"), table_name="case_forecast_history"
    )
    op.drop_column("case_forecast_history", "case_start_source")
    op.drop_index(op.f("ix_case_forecast_case_start_source"), table_name="case_forecast")
    op.drop_column("case_forecast", "case_start_source")
    # ### end Alembic commands ###
