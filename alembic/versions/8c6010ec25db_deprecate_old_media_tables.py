"""Deprecate old media tables

Revision ID: 8c6010ec25db
Revises: 825033540727
Create Date: 2024-03-04 17:33:56.818929

"""

import sqlalchemy as sa

from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "8c6010ec25db"
down_revision = "825033540727"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_media_assets_asset_type", table_name="media_assets")
    op.drop_index("ix_media_assets_asset_uri", table_name="media_assets")
    op.drop_index("ix_media_assets_camera_id", table_name="media_assets")
    op.drop_index("ix_media_assets_created_time", table_name="media_assets")
    op.drop_index("ix_media_assets_end_time", table_name="media_assets")
    op.drop_index("ix_media_assets_org_id", table_name="media_assets")
    op.drop_index("ix_media_assets_room_id", table_name="media_assets")
    op.drop_index(
        "ix_media_assets_room_id_asset_type_start_time_end_time", table_name="media_assets"
    )
    op.drop_index("ix_media_assets_site_id", table_name="media_assets")
    op.drop_index("ix_media_assets_start_time", table_name="media_assets")
    op.drop_index("ix_media_assets_updated_time", table_name="media_assets")
    op.drop_table("media_assets")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "media_assets",
        sa.Column("org_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("site_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("camera_id", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("room_id", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("camera_name", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("asset_uri", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("content_type", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("asset_resolution", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column(
            "extra", postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True
        ),
        sa.Column(
            "created_time",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_time", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True
        ),
        sa.Column(
            "end_time", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True
        ),
        sa.Column(
            "start_time", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True
        ),
        sa.Column("asset_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("asset_type", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(["camera_id"], ["cameras.id"], name="media_assets_camera_id_fkey"),
        sa.ForeignKeyConstraint(["org_id"], ["organizations.id"], name="media_assets_org_id_fkey"),
        sa.ForeignKeyConstraint(["room_id"], ["rooms.id"], name="media_assets_room_id_fkey"),
        sa.ForeignKeyConstraint(["site_id"], ["sites.id"], name="media_assets_site_id_fkey"),
        sa.PrimaryKeyConstraint("asset_id", name="media_assets_pkey"),
    )
    op.create_index("ix_media_assets_updated_time", "media_assets", ["updated_time"], unique=False)
    op.create_index("ix_media_assets_start_time", "media_assets", ["start_time"], unique=False)
    op.create_index("ix_media_assets_site_id", "media_assets", ["site_id"], unique=False)
    op.create_index(
        "ix_media_assets_room_id_asset_type_start_time_end_time",
        "media_assets",
        ["room_id", "asset_type", "start_time", "end_time"],
        unique=False,
    )
    op.create_index("ix_media_assets_room_id", "media_assets", ["room_id"], unique=False)
    op.create_index("ix_media_assets_org_id", "media_assets", ["org_id"], unique=False)
    op.create_index("ix_media_assets_end_time", "media_assets", ["end_time"], unique=False)
    op.create_index("ix_media_assets_created_time", "media_assets", ["created_time"], unique=False)
    op.create_index("ix_media_assets_camera_id", "media_assets", ["camera_id"], unique=False)
    op.create_index("ix_media_assets_asset_uri", "media_assets", ["asset_uri"], unique=False)
    op.create_index("ix_media_assets_asset_type", "media_assets", ["asset_type"], unique=False)
    # ### end Alembic commands ###
