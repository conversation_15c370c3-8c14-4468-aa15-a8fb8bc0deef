"""Adding column for asset_type

Revision ID: 8ca6851dda39
Revises: f3948d99b784
Create Date: 2021-08-12 14:10:04.914825

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "8ca6851dda39"
down_revision = "0fc59119ce6e"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("media_assets", sa.Column("asset_type", sa.String(), nullable=True))
    op.create_index(
        op.f("ix_media_assets_asset_type"), "media_assets", ["asset_type"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_media_assets_asset_type"), table_name="media_assets")
    op.drop_column("media_assets", "asset_type")
    # ### end Alembic commands ###
