"""Create media_assets table

Revision ID: 8cf5fd04a704
Revises:
Create Date: 2021-02-19 11:30:52.927133

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "8cf5fd04a704"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "media_assets",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("org_id", sa.String(), nullable=True),
        sa.Column("site_id", sa.String(), nullable=True),
        sa.Column("camera_id", sa.String(), nullable=True),
        sa.Column("room_id", sa.String(), nullable=True),
        sa.Column("camera_name", sa.String(), nullable=True),
        sa.Column("asset_uri", sa.String(), nullable=True),
        sa.Column("start_time", sa.Integer(), nullable=True),
        sa.Column("end_time", sa.Integer(), nullable=True),
        sa.Column("content_type", sa.String(), nullable=True),
        sa.Column("asset_resolution", sa.String(), nullable=True),
        sa.Column("extra", sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("media_assets")
    # ### end Alembic commands ###
