"""Add more event types

Revision ID: 8ee5ff22b83d
Revises: b54bd9d8ca47
Create Date: 2022-08-24 14:52:05.744670

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "8ee5ff22b83d"
down_revision = "b54bd9d8ca47"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    t_event_types = sa.Table(
        "event_types",
        sa.MetaData(),
        sa.<PERSON>umn("id", sa.String(32)),
        sa.Column("name", sa.String(64)),
        sa.<PERSON>umn("type", sa.String(64)),
        sa.<PERSON>umn("color", sa.String(16)),
    )
    op.bulk_insert(
        t_event_types,
        [
            {
                "id": "lights_off",
                "type": "uncategorized",
                "name": "Lights off",
                "color": "#999999",
            },
            {
                "id": "lights_on",
                "type": "uncategorized",
                "name": "Lights on",
                "color": "#999999",
            },
            {
                "id": "patient_on_or_table",
                "type": "uncategorized",
                "name": "Patient on OR table",
                "color": "#999999",
            },
            {
                "id": "patient_on_hospital_bed",
                "type": "uncategorized",
                "name": "Patient on hospital bed",
                "color": "#999999",
            },
            {
                "id": "no_patient",
                "type": "uncategorized",
                "name": "No patient",
                "color": "#999999",
            },
            {
                "id": "back_table_unprepared",
                "type": "uncategorized",
                "name": "Back table unprepared",
                "color": "#999999",
            },
            {
                "id": "case_cart_visible",
                "type": "uncategorized",
                "name": "Case cart visible",
                "color": "#999999",
            },
            {
                "id": "no_case_cart",
                "type": "uncategorized",
                "name": "No case cart",
                "color": "#999999",
            },
            {
                "id": "no_mop",
                "type": "uncategorized",
                "name": "No mop",
                "color": "#999999",
            },
            {
                "id": "mop",
                "type": "uncategorized",
                "name": "Mop",
                "color": "#999999",
            },
        ],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
