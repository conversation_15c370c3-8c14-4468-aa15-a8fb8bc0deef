"""Expose back_table_cleared

Revision ID: 906c8f53ab47
Revises: d4d82b19c0ba
Create Date: 2022-11-29 09:31:50.556010

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "906c8f53ab47"
down_revision = "28b2ae391144"
branch_labels = None
depends_on = None


def upgrade():
    # Update the back_table_cleared type with color and such for the annotators
    op.execute(
        "UPDATE event_types SET "
        "color = '#4c94ff', "
        "hidden = false,"
        "name = 'Back table cleared'"
        "WHERE id = 'back_table_cleared'"
    )
    # Add it to the list of events for the main task type
    op.execute(
        "UPDATE annotation_task_types SET "
        "event_types = array_append(event_types, 'back_table_cleared')"
        "WHERE id = 'surgery'"
    )
    # There is already no back_table_unprepared events anymore in prod, but dev/staging may
    # still have them. So clear them out
    op.execute(
        "UPDATE events SET event_type_id = 'back_table_cleared' WHERE event_type_id = 'back_table_unprepared'"
    )
    op.execute(
        "UPDATE events_history SET event_type_id = 'back_table_cleared' WHERE event_type_id = 'back_table_unprepared'"
    )
    # And remove from our annotation task type
    op.execute(
        "UPDATE annotation_task_types SET "
        "event_types = array_remove(event_types, 'back_table_unprepared')"
        "WHERE id = 'surgery'"
    )
    # And then remove the old event type
    op.execute("DELETE FROM event_types WHERE id = 'back_table_unprepared'")


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
