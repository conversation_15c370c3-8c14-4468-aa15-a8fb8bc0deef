"""Add sort key to room table

Revision ID: 91fdaf511aca
Revises: d5f51f224b2a
Create Date: 2024-05-15 17:23:12.298295

"""

import sqlalchemy as sa

from alembic import op
from sqlalchemy import column, table


# revision identifiers, used by Alembic.
revision = "91fdaf511aca"
down_revision = "33ca75b0f8dd"
branch_labels = None
depends_on = None

rooms_table = table(
    "rooms",
    column("id", sa.String),
    column("sort_key", sa.String),
)


def upgrade():
    op.add_column("rooms", sa.Column("sort_key", sa.String(), nullable=True))
    op.execute(
        rooms_table.update()
        .where(rooms_table.c.id == "HMH-DUNN06-LD03")
        .values(
            sort_key="HMH-DUNN06-LD03",
        )
    )


def downgrade():
    op.drop_column("rooms", "sort_key")
