"""Store task sites as rooms instead

Revision ID: 949875921217
Revises: 97348db6ef94
Create Date: 2024-02-22 13:53:07.045358

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "949875921217"
down_revision = "97348db6ef94"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """
        INSERT INTO annotation_task_schedules_rooms (schedule_id, room_id)
        SELECT ss.schedule_id, r.id
        FROM annotation_task_schedules_sites ss
        JOIN rooms r ON ss.site_id = r.site_id
        """
    )

    # Delete all records from schedules_sites
    op.execute("DELETE FROM annotation_task_schedules_sites")


def downgrade():
    op.execute(
        """
        INSERT INTO annotation_task_schedules_sites (schedule_id, site_id)
        SELECT sr.schedule_id, r.site_id
        FROM annotation_task_schedules_rooms sr
        JOIN rooms r ON sr.room_id = r.id
        GROUP BY sr.schedule_id, r.site_id
        """
    )

    # Delete all records from schedules_rooms
    op.execute("DELETE FROM annotation_task_schedules_rooms")
