"""Add block time releases

Revision ID: 9abb4ab91557
Revises: 1c3c24b5b979
Create Date: 2023-10-30 14:58:40.405376

"""

import sqlalchemy as sa

from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "9abb4ab91557"
down_revision = "1c3c24b5b979"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "block_time_releases",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            server_default=sa.text("gen_random_uuid()"),
            nullable=False,
        ),
        sa.Column("block_time_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("release_time", postgresql.TSTZRANGE(), nullable=False),
        sa.Column("reason", sa.String(), nullable=True),
        sa.Column(
            "released_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("source", sa.String(), nullable=False),
        sa.Column("source_type", sa.String(), nullable=False),
        postgresql.ExcludeConstraint(
            (sa.column("block_time_id"), "="),
            (sa.column("release_time"), "&&"),
            using="gist",
            name="unique_block_time_release_tsrange_constraint",
        ),
        sa.ForeignKeyConstraint(
            ["block_time_id"],
            ["block_times.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_block_time_releases_block_time_id"),
        "block_time_releases",
        ["block_time_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_time_releases_created_time"),
        "block_time_releases",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_time_releases_release_time"),
        "block_time_releases",
        ["release_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_time_releases_released_at"),
        "block_time_releases",
        ["released_at"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_time_releases_source_type"),
        "block_time_releases",
        ["source_type"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_time_releases_updated_time"),
        "block_time_releases",
        ["updated_time"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_block_time_releases_updated_time"), table_name="block_time_releases")
    op.drop_index(op.f("ix_block_time_releases_source_type"), table_name="block_time_releases")
    op.drop_index(op.f("ix_block_time_releases_released_at"), table_name="block_time_releases")
    op.drop_index(op.f("ix_block_time_releases_release_time"), table_name="block_time_releases")
    op.drop_index(op.f("ix_block_time_releases_created_time"), table_name="block_time_releases")
    op.drop_index(op.f("ix_block_time_releases_block_time_id"), table_name="block_time_releases")
    op.drop_table("block_time_releases")
    # ### end Alembic commands ###
