"""dropping constraint for overlapping block times

Revision ID: 9be6965495d2
Revises: 42ca16b3d500
Create Date: 2025-02-06 10:22:05.158506

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "9be6965495d2"
down_revision = "42ca16b3d500"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("unique_room_tsrange_constraint", "block_times")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        "ALTER TABLE block_times ADD CONSTRAINT unique_room_tsrange_constraint EXCLUDE USING GIST (room_id WITH =, "
        "block_time WITH &&)"
    )
    # ### end Alembic commands ###
