"""Add case history table

Revision ID: 9da8f4312af2
Revises: 7295dc2a7329
Create Date: 2023-04-26 18:15:05.276364

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "9da8f4312af2"
down_revision = "eb244766fc0d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "cases_history",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("case_id", sa.String(), autoincrement=False, nullable=False),
        sa.Column("external_case_id", sa.String(), autoincrement=False, nullable=False),
        sa.Column("room_id", sa.String(), autoincrement=False, nullable=False),
        sa.Column("case_classification_types_id", sa.String(), autoincrement=False, nullable=True),
        sa.Column(
            "scheduled_start_time", sa.DateTime(timezone=True), autoincrement=False, nullable=False
        ),
        sa.Column(
            "scheduled_end_time", sa.DateTime(timezone=True), autoincrement=False, nullable=False
        ),
        sa.Column("status", sa.String(), autoincrement=False, nullable=False),
        sa.Column(
            "service_line_id", postgresql.UUID(as_uuid=True), autoincrement=False, nullable=True
        ),
        sa.Column("is_add_on", sa.Boolean(), autoincrement=False, nullable=True),
        sa.Column(
            "patient_class",
            postgresql.ENUM(
                "EMERGENCY",
                "HOSPITAL_OUTPATIENT_SURGERY",
                "INPATIENT",
                "SURGERY_ADMIT",
                name="patientclass",
                create_type=False,
            ),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("org_id", sa.String(), autoincrement=False, nullable=False),
        sa.Column("site_id", sa.String(), autoincrement=False, nullable=False),
        sa.Column(
            "version",
            sa.Integer(),
            server_default=sa.text("1"),
            autoincrement=False,
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["case_classification_types_id"],
            ["case_classification_types.id"],
        ),
        sa.ForeignKeyConstraint(
            ["case_classification_types_id"],
            ["case_classification_types.id"],
        ),
        sa.ForeignKeyConstraint(
            ["service_line_id"],
            ["service_lines.id"],
        ),
        sa.PrimaryKeyConstraint("case_id", "version"),
    )
    op.create_index(
        op.f("ix_cases_history_created_time"), "cases_history", ["created_time"], unique=False
    )
    op.create_index(
        op.f("ix_cases_history_external_case_id"),
        "cases_history",
        ["external_case_id"],
        unique=False,
    )
    op.create_index(op.f("ix_cases_history_org_id"), "cases_history", ["org_id"], unique=False)
    op.create_index(
        op.f("ix_cases_history_patient_class"), "cases_history", ["patient_class"], unique=False
    )
    op.create_index(
        op.f("ix_cases_history_scheduled_end_time"),
        "cases_history",
        ["scheduled_end_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_cases_history_scheduled_start_time"),
        "cases_history",
        ["scheduled_start_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_cases_history_service_line_id"), "cases_history", ["service_line_id"], unique=False
    )
    op.create_index(op.f("ix_cases_history_site_id"), "cases_history", ["site_id"], unique=False)
    op.create_index(
        op.f("ix_cases_history_updated_time"), "cases_history", ["updated_time"], unique=False
    )
    op.add_column(
        "cases", sa.Column("version", sa.Integer(), server_default=sa.text("1"), nullable=False)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("cases", "version")
    op.drop_index(op.f("ix_cases_history_updated_time"), table_name="cases_history")
    op.drop_index(op.f("ix_cases_history_site_id"), table_name="cases_history")
    op.drop_index(op.f("ix_cases_history_service_line_id"), table_name="cases_history")
    op.drop_index(op.f("ix_cases_history_scheduled_start_time"), table_name="cases_history")
    op.drop_index(op.f("ix_cases_history_scheduled_end_time"), table_name="cases_history")
    op.drop_index(op.f("ix_cases_history_patient_class"), table_name="cases_history")
    op.drop_index(op.f("ix_cases_history_org_id"), table_name="cases_history")
    op.drop_index(op.f("ix_cases_history_external_case_id"), table_name="cases_history")
    op.drop_index(op.f("ix_cases_history_created_time"), table_name="cases_history")
    op.drop_table("cases_history")
    # ### end Alembic commands ###
