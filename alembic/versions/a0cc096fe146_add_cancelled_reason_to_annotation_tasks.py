"""Add cancelled reason to annotation tasks

Revision ID: a0cc096fe146
Revises: 7d4fcff1fa37
Create Date: 2023-05-17 17:55:42.452845

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "a0cc096fe146"
down_revision = "7d4fcff1fa37"
branch_labels = None
depends_on = None

CancelledReasonType = sa.Enum("IDLE", "OUTAGE", "SENSITIVE_CONTENT", name="cancelled_reason_enum")


def upgrade() -> None:
    CancelledReasonType.create(op.get_bind())

    op.add_column(
        "annotation_tasks",
        sa.Column(
            "cancelled_reason",
            CancelledReasonType,
            nullable=True,
        ),
    )
    op.create_index(
        op.f("ix_annotation_tasks_cancelled_reason"),
        "annotation_tasks",
        ["cancelled_reason"],
        unique=False,
    )


def downgrade() -> None:
    op.drop_index(op.f("ix_annotation_tasks_cancelled_reason"), table_name="annotation_tasks")
    op.drop_column("annotation_tasks", "cancelled_reason")

    CancelledReasonType.drop(op.get_bind())
