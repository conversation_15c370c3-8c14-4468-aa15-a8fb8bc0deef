"""RT-160_add_new_obx_sub_ids

Revision ID: a4699c900d25
Revises: 8ee5ff22b83d
Create Date: 2022-08-30 18:30:47.648077

"""

# revision identifiers, used by Alembic.
from sqlalchemy import MetaData, Table

from alembic import op

revision = "a4699c900d25"
down_revision = "8ee5ff22b83d"
branch_labels = None
depends_on = None

OBX_TYPES_V_SEP_2022 = [
    {"id": "OBSERVED_AT_CECUM", "name": "At Cecum"},
    {"id": "OBSERVED_CATHLAB_TO_OR", "name": "Cathlab to OR"},
    {"id": "OBSERVED_DECISION_TIME", "name": "Decision Time"},
    {"id": "OBSERVED_DISCHARGE_CRITERIA_MET", "name": "Discharge Criteria Met"},
    {"id": "OBSERVED_EPIDURAL_TO_C_SECTION", "name": "Epidural to C-section"},
    {"id": "OBSERVED_HYSTEROTOMY", "name": "Hysterotomy"},
    {"id": "OBSERVED_IN_EXTENDED_RECOVERY", "name": "In Extended Recovery "},
    {"id": "OBSERVED_IN_HOLDING_AREA", "name": "In Holding Area"},
    {"id": "OBSERVED_IN_PHASE_II", "name": "In Phase II"},
    {"id": "OBSERVED_IN_PROCEDURAL_RECOVERY", "name": "In Procedural Recovery "},
    {"id": "OBSERVED_INDUCTION", "name": "Induction"},
    {"id": "OBSERVED_OUT_OF_PACU", "name": "Out of PACU"},
    {"id": "OBSERVED_OPNOTE_VERIFIED", "name": "OpNote Verified"},
    {"id": "OBSERVED_OUT_OF_EXTENDED_RECOVERY", "name": "Out of Extended Recovery"},
    {"id": "OBSERVED_OUT_OF_HOLDING_AREA", "name": "Out of Holding Area"},
    {"id": "OBSERVED_OUT_OF_PACU_2ND_TIME", "name": "Out of PACU (2nd Time)"},
    {"id": "OBSERVED_OUT_OF_PACU_3RD_TIME", "name": "Out of PACU (3rd Time)"},
    {"id": "OBSERVED_OUT_OF_PHASE_II", "name": "Out of Phase II"},
    {"id": "OBSERVED_OUT_OF_PHASE_II_2ND_TIME", "name": "Out of Phase II (2nd Time)"},
    {"id": "OBSERVED_OUT_OF_PROCEDURAL_RECOVERY", "name": "Out of Procedural Recovery"},
    {"id": "OBSERVED_PATIENT_SENT_FOR", "name": "Patient Sent For"},
    {
        "id": "OBSERVED_PATIENT_TRANSFER_TO_HOSPITAL_ROOM",
        "name": "Patient transfer to a hospital room",
    },
    {"id": "OBSERVED_PHASE_II_CARE_COMPLETE", "name": "Phase II Care Complete"},
    {"id": "OBSERVED_PROCEDURE_FINISH", "name": "Procedure Finish"},
    {"id": "OBSERVED_RETURN_TO_OR", "name": "Return to OR"},
    {"id": "OBSERVED_RETURN_TO_PACU_3RD_TIME", "name": "Return to PACU (3rd Time)"},
    {"id": "OBSERVED_RETURN_TO_PACU", "name": "Return to PACU"},
    {"id": "OBSERVED_RETURN_TO_PHASE_II", "name": "Return to Phase II"},
    {"id": "OBSERVED_SEDATION_START", "name": "Sedation Start"},
    {"id": "OBSERVED_SETUP_COMPLETE", "name": "Setup Complete"},
    {"id": "OBSERVED_SETUP_START", "name": "Setup Start"},
    {"id": "OBSERVED_TO_PHASE_II", "name": "To Phase II"},
    {"id": "OBSERVED_TIMEOUT_ANESTHESIA", "name": "TIMEOUT_ANESTHESIA"},
    {"id": "OBSERVED_TIMEOUT_DEBRIEF", "name": "TIMEOUT_DEBRIEF"},
    {"id": "OBSERVED_TIMEOUT_FIRE_SAFETY", "name": "TIMEOUT_FIRE_SAFETY"},
    {"id": "OBSERVED_TIMEOUT_PREINCISION", "name": "TIMEOUT_PREINCISION"},
    {"id": "OBSERVED_TIMEOUT_PREPROCEDURE", "name": "TIMEOUT_PREPROCEDURE"},
]


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # meta = MetaData(bind=op.get_bind())
    meta = MetaData()
    meta.reflect(only=("observation_types",), bind=op.get_bind())
    observation_types_table = Table("observation_types", meta)
    op.bulk_insert(observation_types_table, OBX_TYPES_V_SEP_2022)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        f"""
            DELETE FROM observation_types WHERE id IN ({",".join(["'{}'".format(observation_type["id"]) for observation_type in OBX_TYPES_V_SEP_2022])})
        """
    )
    # ### end Alembic commands ###
