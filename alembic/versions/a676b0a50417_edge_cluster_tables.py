"""Edge cluster tables

Revision ID: a676b0a50417
Revises: 77918543260f
Create Date: 2024-07-17 16:35:21.448471

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "a676b0a50417"
down_revision = "77918543260f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "cluster",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("enable_audio", sa.<PERSON>(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_cluster_enable_audio"), "cluster", ["enable_audio"], unique=False)
    op.create_index(op.f("ix_cluster_name"), "cluster", ["name"], unique=False)
    op.create_table(
        "cluster_mapping",
        sa.Column("cluster_id", sa.UUID(), nullable=False),
        sa.Column("site_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["cluster_id"],
            ["cluster.id"],
        ),
        sa.ForeignKeyConstraint(
            ["site_id"],
            ["sites.id"],
        ),
        sa.PrimaryKeyConstraint("cluster_id", "site_id"),
    )
    op.create_index(
        op.f("ix_cluster_mapping_cluster_id"), "cluster_mapping", ["cluster_id"], unique=False
    )
    op.create_index(
        op.f("ix_cluster_mapping_site_id"), "cluster_mapping", ["site_id"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_cluster_mapping_site_id"), table_name="cluster_mapping")
    op.drop_index(op.f("ix_cluster_mapping_cluster_id"), table_name="cluster_mapping")
    op.drop_table("cluster_mapping")
    op.drop_index(op.f("ix_cluster_name"), table_name="cluster")
    op.drop_index(op.f("ix_cluster_enable_audio"), table_name="cluster")
    op.drop_table("cluster")
    # ### end Alembic commands ###
