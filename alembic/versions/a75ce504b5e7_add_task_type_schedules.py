"""Add task type schedules

Revision ID: a75ce504b5e7
Revises: 2f8387b2d7cd
Create Date: 2023-03-13 09:53:27.194013

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "a75ce504b5e7"
down_revision = "2f8387b2d7cd"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "annotation_task_schedules",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("site_id", sa.String(), nullable=False),
        sa.Column("annotation_task_type_id", sa.String(), nullable=False),
        sa.Column("start_time", sa.Time(), nullable=False),
        sa.Column("interval", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["annotation_task_type_id"],
            ["annotation_task_types.id"],
        ),
        sa.ForeignKeyConstraint(
            ["site_id"],
            ["sites.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("site_id", "annotation_task_type_id", "start_time", "interval"),
    )
    op.create_index(
        op.f("ix_annotation_task_schedules_annotation_task_type_id"),
        "annotation_task_schedules",
        ["annotation_task_type_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_task_schedules_created_time"),
        "annotation_task_schedules",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_task_schedules_site_id"),
        "annotation_task_schedules",
        ["site_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_task_schedules_updated_time"),
        "annotation_task_schedules",
        ["updated_time"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_annotation_task_schedules_updated_time"), table_name="annotation_task_schedules"
    )
    op.drop_index(
        op.f("ix_annotation_task_schedules_site_id"), table_name="annotation_task_schedules"
    )
    op.drop_index(
        op.f("ix_annotation_task_schedules_created_time"), table_name="annotation_task_schedules"
    )
    op.drop_index(
        op.f("ix_annotation_task_schedules_annotation_task_type_id"),
        table_name="annotation_task_schedules",
    )
    op.drop_table("annotation_task_schedules")
    # ### end Alembic commands ###
