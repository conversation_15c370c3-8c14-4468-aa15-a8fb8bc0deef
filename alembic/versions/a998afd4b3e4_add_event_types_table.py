"""Add event_types table

Revision ID: a998afd4b3e4
Revises: 7952810eeb10
Create Date: 2022-07-13 15:36:32.240977

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "a998afd4b3e4"
down_revision = "7952810eeb10"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    event_types_table = op.create_table(
        "event_types",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("description", sa.String(), nullable=True),
        sa.Column("type", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_event_types_created_time"), "event_types", ["created_time"], unique=False
    )
    op.create_index(op.f("ix_event_types_type"), "event_types", ["type"], unique=False)
    op.create_index(
        op.f("ix_event_types_updated_time"), "event_types", ["updated_time"], unique=False
    )
    op.add_column("events", sa.Column("event_type_id", sa.String(), nullable=True))
    op.create_index(op.f("ix_events_event_type_id"), "events", ["event_type_id"], unique=False)
    op.create_foreign_key(
        "events_event_type_id_fkey", "events", "event_types", ["event_type_id"], ["id"]
    )

    op.bulk_insert(
        event_types_table,
        [
            {"id": "back_table_open", "type": "backtable_status", "name": "Back table open"},
            {"id": "case_cart_in", "type": "casecart_status", "name": "Case cart in"},
            {"id": "case_cart_out", "type": "casecart_status", "name": "Case cart out"},
            {"id": "cleaning_crew_in", "type": "uncategorized", "name": "Cleaning crew in"},
            {"id": "cleaning_crew_out", "type": "uncategorized", "name": "Cleaning crew out"},
            {"id": "closing_start", "type": "uncategorized", "name": "Closing start"},
            {"id": "count_start", "type": "uncategorized", "name": "Count start"},
            {"id": "count_end", "type": "uncategorized", "name": "Count end"},
            {"id": "endo_pack_open", "type": "uncategorized", "name": "Endo pack open"},
            {"id": "endoscopy_start", "type": "uncategorized", "name": "Endoscopy start"},
            {"id": "endoscopy_end", "type": "uncategorized", "name": "Endoscopy end"},
            {"id": "first_incision_start", "type": "uncategorized", "name": "First Incision"},
            {"id": "intubation", "type": "patient_status", "name": "Intubation"},
            {"id": "extubation", "type": "patient_status", "name": "Extubation"},
            {"id": "mop_in", "type": "mop_status", "name": "Mop in"},
            {"id": "mop_out", "type": "mop_status", "name": "Mop out"},
            {"id": "or_table_ready", "type": "uncategorized", "name": "OR table ready"},
            {
                "id": "patient_briefing_start",
                "type": "patient_status",
                "name": "Patient briefin g start",
            },
            {
                "id": "patient_briefing_end",
                "type": "patient_status",
                "name": "Patient briefing end",
            },
            {"id": "patient_draped", "type": "patient_status", "name": "Patient draped"},
            {
                "id": "patient_imaging_start",
                "type": "patient_status",
                "name": "Patient imaging start",
            },
            {"id": "patient_imaging_end", "type": "patient_status", "name": "Patient imaging end"},
            {
                "id": "patient_skin_prep_start",
                "type": "patient_status",
                "name": "Patient skin pre p start",
            },
            {
                "id": "patient_skin_prep_end",
                "type": "patient_status",
                "name": "Patient skin prep end",
            },
            {"id": "patient_wheels_in", "type": "patient_status", "name": "Patient wheels in"},
            {"id": "patient_wheels_out", "type": "patient_status", "name": "Patient wheels out"},
            {
                "id": "patient_xfer_to_or_table",
                "type": "patient_status",
                "name": "OR table occupied",
            },
            {"id": "patient_xfer_to_bed", "type": "patient_status", "name": "Bed occupied"},
            {"id": "patient_undraped", "type": "patient_status", "name": "Patient undraped"},
            {
                "id": "sensitive_content_start",
                "type": "uncategorized",
                "name": "Sensitive conten t start",
            },
            {
                "id": "sensitive_content_end",
                "type": "uncategorized",
                "name": "Sensitive content end",
            },
            {
                "id": "sterile_pack_on_back_table",
                "type": "uncategorized",
                "name": "Sterile pack o n back table",
            },
            {
                "id": "terminal_clean_start",
                "type": "uncategorized",
                "name": "Terminal cleaning start",
            },
            {"id": "terminal_clean_end", "type": "uncategorized", "name": "Terminal cleaning end"},
            {"id": "timeout_start", "type": "uncategorized", "name": "Timeout start"},
            {"id": "timeout_end", "type": "uncategorized", "name": "Timeout end"},
        ],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("events_event_type_id_fkey", "events", type_="foreignkey")
    op.drop_index(op.f("ix_events_event_type_id"), table_name="events")
    op.drop_column("events", "event_type_id")
    op.drop_index(op.f("ix_event_types_updated_time"), table_name="event_types")
    op.drop_index(op.f("ix_event_types_type"), table_name="event_types")
    op.drop_index(op.f("ix_event_types_created_time"), table_name="event_types")
    op.drop_table("event_types")
    # ### end Alembic commands ###
