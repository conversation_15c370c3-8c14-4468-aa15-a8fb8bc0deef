"""Add site and org IDs to CaseNotePlanStore

Revision ID: a9a231f78d7e
Revises: 20b387ef4216
Create Date: 2023-12-22 10:11:10.249981

"""

import sqlalchemy as sa

from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "a9a231f78d7e"
down_revision = "20b387ef4216"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "case_note_plan",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            server_default=sa.text("gen_random_uuid()"),
            nullable=False,
        ),
        sa.Column("case_id", sa.String(), nullable=False),
        sa.Column("note", sa.String(), nullable=True),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.Column("site_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["case_id"],
            ["cases.case_id"],
        ),
        sa.ForeignKeyConstraint(
            ["org_id"],
            ["organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["site_id"],
            ["sites.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("case_id", name="uq_case_id"),
    )
    op.create_index(op.f("ix_case_note_plan_case_id"), "case_note_plan", ["case_id"], unique=False)
    op.create_index(
        op.f("ix_case_note_plan_created_time"), "case_note_plan", ["created_time"], unique=False
    )
    op.create_index(op.f("ix_case_note_plan_org_id"), "case_note_plan", ["org_id"], unique=False)
    op.create_index(op.f("ix_case_note_plan_site_id"), "case_note_plan", ["site_id"], unique=False)
    op.create_index(
        op.f("ix_case_note_plan_updated_time"), "case_note_plan", ["updated_time"], unique=False
    )
    op.drop_index("ix_case_notes_plan_case_id", table_name="case_notes_plan")
    op.drop_index("ix_case_notes_plan_created_time", table_name="case_notes_plan")
    op.drop_index("ix_case_notes_plan_updated_time", table_name="case_notes_plan")
    op.drop_table("case_notes_plan")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "case_notes_plan",
        sa.Column(
            "created_time",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("case_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("notes", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(
            ["case_id"], ["cases.case_id"], name="case_notes_plan_case_id_fkey"
        ),
        sa.PrimaryKeyConstraint("case_id", name="case_notes_plan_pkey"),
    )
    op.create_index(
        "ix_case_notes_plan_updated_time", "case_notes_plan", ["updated_time"], unique=False
    )
    op.create_index(
        "ix_case_notes_plan_created_time", "case_notes_plan", ["created_time"], unique=False
    )
    op.create_index("ix_case_notes_plan_case_id", "case_notes_plan", ["case_id"], unique=False)
    op.drop_index(op.f("ix_case_note_plan_updated_time"), table_name="case_note_plan")
    op.drop_index(op.f("ix_case_note_plan_site_id"), table_name="case_note_plan")
    op.drop_index(op.f("ix_case_note_plan_org_id"), table_name="case_note_plan")
    op.drop_index(op.f("ix_case_note_plan_created_time"), table_name="case_note_plan")
    op.drop_index(op.f("ix_case_note_plan_case_id"), table_name="case_note_plan")
    op.drop_table("case_note_plan")
    # ### end Alembic commands ###
