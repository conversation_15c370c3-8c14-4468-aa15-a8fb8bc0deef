"""Adding category to a highlight

Revision ID: aaa3cb785f60
Revises: e2fbec2b56fa
Create Date: 2022-01-26 15:21:07.269659

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "aaa3cb785f60"
down_revision = "e2fbec2b56fa"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("highlights", sa.Column("category", sa.String(), nullable=True))
    op.create_index(op.f("ix_highlights_category"), "highlights", ["category"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_highlights_category"), table_name="highlights")
    op.drop_column("highlights", "category")
    # ### end Alembic commands ###
