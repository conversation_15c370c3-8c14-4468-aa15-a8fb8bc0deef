"""Add OBSERVATION to PatientClass

Revision ID: aad40d1c14b1
Revises: 9abb4ab91557
Create Date: 2023-11-17 16:38:03.229169

"""

from alembic import op


# revision identifiers, used by Alembic.
from sqlalchemy import text

revision = "aad40d1c14b1"
down_revision = "9abb4ab91557"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(text("ALTER TYPE patientclass ADD VALUE 'OBSERVATION' "))


def downgrade():
    op.execute(text("ALTER TYPE patientclass RENAME TO patientclassold"))
    op.execute(
        text(
            "CREATE TYPE patientclass"
            " AS ENUM ('EMERGENCY','HOSPITAL_OUTPATIENT_SURGERY','INPATIENT','SURGERY_ADMIT','PRE_ADMIT')"
        )
    )
    op.execute(
        text(
            "ALTER TABLE cases ALTER COLUMN patient_class TYPE patientclass USING patient_class::text::patientclass"
        )
    )
    op.execute(
        text(
            "ALTER TABLE cases_history ALTER COLUMN patient_class TYPE patientclass USING patient_class::text::patientclass"
        )
    )
    op.execute("DROP TYPE patientclassold")
