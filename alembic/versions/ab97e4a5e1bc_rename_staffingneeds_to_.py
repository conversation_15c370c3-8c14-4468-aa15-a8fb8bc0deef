"""Rename StaffingNeeds to StaffingNeedsRatio

Revision ID: ab97e4a5e1bc
Revises: d289922383cb
Create Date: 2023-03-27 17:35:36.105328

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "ab97e4a5e1bc"
down_revision = "d289922383cb"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "staffing_needs_ratio",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("archived_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("set_by_user_id", sa.String(), nullable=False),
        sa.Column("archived_by_user_id", sa.String(), nullable=True),
        sa.Column("ratio", sa.Float(), nullable=True),
        sa.Column("staff_role_id", sa.String(), nullable=False),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.Column("site_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["org_id"],
            ["organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["site_id"],
            ["sites.id"],
        ),
        sa.ForeignKeyConstraint(
            ["staff_role_id"],
            ["staff_role.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_staffing_needs_ratio_archived_time"),
        "staffing_needs_ratio",
        ["archived_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staffing_needs_ratio_created_time"),
        "staffing_needs_ratio",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staffing_needs_ratio_org_id"), "staffing_needs_ratio", ["org_id"], unique=False
    )
    op.create_index(
        op.f("ix_staffing_needs_ratio_site_id"), "staffing_needs_ratio", ["site_id"], unique=False
    )
    op.create_index(
        op.f("ix_staffing_needs_ratio_updated_time"),
        "staffing_needs_ratio",
        ["updated_time"],
        unique=False,
    )
    op.drop_index("ix_staffing_needs_archived_time", table_name="staffing_needs")
    op.drop_index("ix_staffing_needs_created_time", table_name="staffing_needs")
    op.drop_index("ix_staffing_needs_org_id", table_name="staffing_needs")
    op.drop_index("ix_staffing_needs_site_id", table_name="staffing_needs")
    op.drop_index("ix_staffing_needs_updated_time", table_name="staffing_needs")
    op.drop_table("staffing_needs")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "staffing_needs",
        sa.Column(
            "created_time",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("set_by_user_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column(
            "ratio", postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True
        ),
        sa.Column("staff_role_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("org_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("site_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column(
            "archived_time", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True
        ),
        sa.Column("archived_by_user_id", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(
            ["org_id"], ["organizations.id"], name="staffing_needs_org_id_fkey"
        ),
        sa.ForeignKeyConstraint(["site_id"], ["sites.id"], name="staffing_needs_site_id_fkey"),
        sa.ForeignKeyConstraint(
            ["staff_role_id"], ["staff_role.id"], name="staffing_needs_staff_role_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="staffing_needs_pkey"),
    )
    op.create_index(
        "ix_staffing_needs_updated_time", "staffing_needs", ["updated_time"], unique=False
    )
    op.create_index("ix_staffing_needs_site_id", "staffing_needs", ["site_id"], unique=False)
    op.create_index("ix_staffing_needs_org_id", "staffing_needs", ["org_id"], unique=False)
    op.create_index(
        "ix_staffing_needs_created_time", "staffing_needs", ["created_time"], unique=False
    )
    op.create_index(
        "ix_staffing_needs_archived_time", "staffing_needs", ["archived_time"], unique=False
    )
    op.drop_index(op.f("ix_staffing_needs_ratio_updated_time"), table_name="staffing_needs_ratio")
    op.drop_index(op.f("ix_staffing_needs_ratio_site_id"), table_name="staffing_needs_ratio")
    op.drop_index(op.f("ix_staffing_needs_ratio_org_id"), table_name="staffing_needs_ratio")
    op.drop_index(op.f("ix_staffing_needs_ratio_created_time"), table_name="staffing_needs_ratio")
    op.drop_index(op.f("ix_staffing_needs_ratio_archived_time"), table_name="staffing_needs_ratio")
    op.drop_table("staffing_needs_ratio")
    # ### end Alembic commands ###
