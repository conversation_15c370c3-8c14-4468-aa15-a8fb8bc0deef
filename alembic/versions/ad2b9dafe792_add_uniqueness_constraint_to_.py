"""Add uniqueness constraint to AnnotationTask table

Revision ID: ad2b9dafe792
Revises: 37dfda9bd3e7
Create Date: 2021-11-18 18:13:26.103223

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "ad2b9dafe792"
down_revision = "37dfda9bd3e7"
branch_labels = None
depends_on = None


def upgrade():
    op.create_unique_constraint(
        "uq_orgId_siteId_roomId_startTime_endTime",
        "annotation_tasks",
        ["org_id", "site_id", "room_id", "start_time", "end_time"],
    )


def downgrade():
    op.drop_constraint("uq_orgId_siteId_roomId_startTime_endTime", "annotation_tasks")
