"""Adjust priority check constraint

Revision ID: ae1b0c9ff4d9
Revises: f3f3f3296a38
Create Date: 2025-01-16 13:06:48.358754

"""

from alembic import op
from sqlalchemy.sql import and_, column

# revision identifiers, used by Alembic.
revision = "ae1b0c9ff4d9"
down_revision = "f3f3f3296a38"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.drop_constraint(
        "ck_annotation_task_types_priority_within_range_1_to_5_inclusive",
        "annotation_task_types",
        type_="check",
    )

    op.create_check_constraint(
        "ck_annotation_task_types_priority_is_positive",
        "annotation_task_types",
        column("priority") > 0,
    )


def downgrade() -> None:
    op.drop_constraint(
        "ck_annotation_task_types_priority_is_positive",
        "annotation_task_types",
        type_="check",
    )

    op.create_check_constraint(
        "ck_annotation_task_types_priority_within_range_1_to_5_inclusive",
        "annotation_task_types",
        and_(column("priority") >= 1, column("priority") <= 5),
    )
