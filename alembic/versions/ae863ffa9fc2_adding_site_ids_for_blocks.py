"""adding site ids for blocks

Revision ID: ae863ffa9fc2
Revises: 50c8d071447f
Create Date: 2025-03-24 17:33:27.418718

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "ae863ffa9fc2"
down_revision = "************"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "block_sites",
        sa.Column("block_id", sa.UUID(), nullable=False),
        sa.Column("site_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["block_id"],
            ["blocks.id"],
        ),
        sa.ForeignKeyConstraint(
            ["site_id"],
            ["sites.id"],
        ),
        sa.PrimaryKeyConstraint("block_id", "site_id"),
    )
    op.create_index(op.f("ix_block_sites_block_id"), "block_sites", ["block_id"], unique=False)
    op.create_index(op.f("ix_block_sites_site_id"), "block_sites", ["site_id"], unique=False)
    op.create_index("ix_unique_block_site", "block_sites", ["block_id", "site_id"], unique=True)
    op.drop_index(
        "ix_unique_block_name_org_id_not_archived",
        table_name="blocks",
        postgresql_where="(archived_time IS NULL)",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(
        "ix_unique_block_name_org_id_not_archived",
        "blocks",
        [sa.text("lower(name::text)"), "org_id"],
        unique=True,
        postgresql_where="(archived_time IS NULL)",
    )
    op.drop_index("ix_unique_block_site", table_name="block_sites")
    op.drop_index(op.f("ix_block_sites_site_id"), table_name="block_sites")
    op.drop_index(op.f("ix_block_sites_block_id"), table_name="block_sites")
    op.drop_table("block_sites")
    # ### end Alembic commands ###
