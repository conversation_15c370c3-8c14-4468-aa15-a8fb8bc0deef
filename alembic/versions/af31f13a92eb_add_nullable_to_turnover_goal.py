"""Add nullable to turnover goal

Revision ID: af31f13a92eb
Revises: c5dab8feeb9c
Create Date: 2024-08-14 16:52:30.727324

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "af31f13a92eb"
down_revision = "c5dab8feeb9c"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("turnover_goals", "goal_minutes", existing_type=sa.INTEGER(), nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("turnover_goals", "goal_minutes", existing_type=sa.INTEGER(), nullable=False)
    # ### end Alembic commands ###
