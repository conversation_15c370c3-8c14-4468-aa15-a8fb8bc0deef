"""add_cancellation_reason_column_to_cases

Revision ID: b6a49a7cca25
Revises: b1a0efc19a3f
Create Date: 2023-10-24 10:43:47.153708

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "b6a49a7cca25"
down_revision = "dd34c3a45574"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "cases", sa.Column("cancellation_reason", postgresql.ARRAY(sa.String()), nullable=True)
    )
    op.add_column(
        "cases_history",
        sa.Column(
            "cancellation_reason", postgresql.ARRAY(sa.String()), autoincrement=False, nullable=True
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("cases_history", "cancellation_reason")
    op.drop_column("cases", "cancellation_reason")
    # ### end Alembic commands ###
