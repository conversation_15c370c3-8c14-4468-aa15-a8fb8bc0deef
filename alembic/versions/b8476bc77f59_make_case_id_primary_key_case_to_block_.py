"""make_case_id_primary_key_case_to_block_overrides_table

Revision ID: b8476bc77f59
Revises: 3334d62911d5
Create Date: 2025-03-28 10:19:27.816971

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "b8476bc77f59"
down_revision = "3334d62911d5"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "uq_case_id_for_case_to_block_mapping", "case_to_block_overrides", type_="unique"
    )
    # make case_id primary key and remove id primary key
    op.drop_constraint("case_to_block_overrides_pkey", "case_to_block_overrides", type_="primary")
    op.create_primary_key("case_to_block_overrides_pkey", "case_to_block_overrides", ["case_id"])

    op.drop_column("case_to_block_overrides", "id")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "case_to_block_overrides",
        sa.Column(
            "id",
            sa.UUID(),
            server_default=sa.text("gen_random_uuid()"),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.drop_constraint("case_to_block_overrides_pkey", "case_to_block_overrides", type_="primary")
    op.create_primary_key("case_to_block_overrides_pkey", "case_to_block_overrides", ["id"])
    op.create_unique_constraint(
        "uq_case_id_for_case_to_block_mapping", "case_to_block_overrides", ["case_id"]
    )
    # ### end Alembic commands ###
