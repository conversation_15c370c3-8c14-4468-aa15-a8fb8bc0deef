"""Add Case Labels tables

Revision ID: b9b6fe5d81a0
Revises: d7d9f3c7f994
Create Date: 2025-04-23 14:07:40.557864

"""

import sqlalchemy as sa

from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "b9b6fe5d81a0"
down_revision = "0cd1796ee52e"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum("BOOLEAN", "SINGLE_SELECT", name="caselabelfieldtype").create(op.get_bind())
    op.create_table(
        "case_label_categories",
        sa.Column("id", sa.UUID(), server_default=sa.text("gen_random_uuid()"), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("site_id", sa.String(), nullable=False),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["org_id"],
            ["organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["site_id"],
            ["sites.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_case_label_categories_org_id"), "case_label_categories", ["org_id"], unique=False
    )
    op.create_index(
        op.f("ix_case_label_categories_site_id"), "case_label_categories", ["site_id"], unique=False
    )
    op.create_table(
        "case_label_fields",
        sa.Column("id", sa.UUID(), server_default=sa.text("gen_random_uuid()"), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column(
            "type",
            postgresql.ENUM(
                "BOOLEAN", "SINGLE_SELECT", name="caselabelfieldtype", create_type=False
            ),
            nullable=False,
        ),
        sa.Column("category_id", sa.UUID(), nullable=False),
        sa.Column("ordinal", sa.Integer(), nullable=False, server_default="0"),
        sa.Column("site_id", sa.String(), nullable=False),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["category_id"],
            ["case_label_categories.id"],
        ),
        sa.ForeignKeyConstraint(
            ["org_id"],
            ["organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["site_id"],
            ["sites.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_case_label_fields_category_id"), "case_label_fields", ["category_id"], unique=False
    )
    op.create_index(
        op.f("ix_case_label_fields_org_id"), "case_label_fields", ["org_id"], unique=False
    )
    op.create_index(
        op.f("ix_case_label_fields_site_id"), "case_label_fields", ["site_id"], unique=False
    )
    op.create_table(
        "case_label_field_options",
        sa.Column("id", sa.UUID(), server_default=sa.text("gen_random_uuid()"), nullable=False),
        sa.Column("field_id", sa.UUID(), nullable=False),
        sa.Column("color", sa.String(), nullable=False),
        sa.Column("abbreviation", sa.String(), nullable=False),
        sa.Column("value", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["case_label_fields.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_case_label_field_options_field_id"),
        "case_label_field_options",
        ["field_id"],
        unique=False,
    )
    op.create_table(
        "case_label_assoc",
        sa.Column("id", sa.UUID(), server_default=sa.text("gen_random_uuid()"), nullable=False),
        sa.Column("option_id", sa.UUID(), nullable=False),
        sa.Column("case_id", sa.String(), nullable=False),
        sa.Column("updated_by_user_id", sa.String(), nullable=False),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("archived_time", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["case_id"],
            ["cases.case_id"],
        ),
        sa.ForeignKeyConstraint(
            ["option_id"],
            ["case_label_field_options.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("option_id", "case_id", name="uq_optionId_caseId"),
    )
    op.create_index(
        op.f("ix_case_label_assoc_archived_time"),
        "case_label_assoc",
        ["archived_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_label_assoc_case_id"), "case_label_assoc", ["case_id"], unique=False
    )
    op.create_index(
        op.f("ix_case_label_assoc_created_time"), "case_label_assoc", ["created_time"], unique=False
    )
    op.create_index(
        op.f("ix_case_label_assoc_option_id"), "case_label_assoc", ["option_id"], unique=False
    )
    op.create_index(
        op.f("ix_case_label_assoc_updated_time"), "case_label_assoc", ["updated_time"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_case_label_assoc_updated_time"), table_name="case_label_assoc")
    op.drop_index(op.f("ix_case_label_assoc_option_id"), table_name="case_label_assoc")
    op.drop_index(op.f("ix_case_label_assoc_created_time"), table_name="case_label_assoc")
    op.drop_index(op.f("ix_case_label_assoc_case_id"), table_name="case_label_assoc")
    op.drop_index(op.f("ix_case_label_assoc_archived_time"), table_name="case_label_assoc")
    op.drop_table("case_label_assoc")
    op.drop_index(
        op.f("ix_case_label_field_options_field_id"), table_name="case_label_field_options"
    )
    op.drop_table("case_label_field_options")
    op.drop_index(op.f("ix_case_label_fields_site_id"), table_name="case_label_fields")
    op.drop_index(op.f("ix_case_label_fields_org_id"), table_name="case_label_fields")
    op.drop_index(op.f("ix_case_label_fields_category_id"), table_name="case_label_fields")
    op.drop_table("case_label_fields")
    op.drop_index(op.f("ix_case_label_categories_site_id"), table_name="case_label_categories")
    op.drop_index(op.f("ix_case_label_categories_org_id"), table_name="case_label_categories")
    op.drop_table("case_label_categories")
    sa.Enum("BOOLEAN", "SINGLE_SELECT", name="caselabelfieldtype").drop(op.get_bind())
    # ### end Alembic commands ###
