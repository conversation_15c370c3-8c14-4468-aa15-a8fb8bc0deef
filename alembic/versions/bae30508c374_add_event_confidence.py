"""Add Event confidence

Revision ID: bae30508c374
Revises: 5acf235ee30c
Create Date: 2022-04-18 11:42:11.235077

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "bae30508c374"
down_revision = "5acf235ee30c"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("events", sa.Column("confidence", sa.Float(), nullable=True))


def downgrade():
    op.drop_column("events", "confidence")
