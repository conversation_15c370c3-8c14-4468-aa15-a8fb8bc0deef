"""Update Case Label Assoc unique index

Revision ID: bb2e6c157c76
Revises: fbd21e182e9b
Create Date: 2025-05-16 14:58:01.628903

"""

from alembic import op


# revision identifiers, used by Alembic.
revision = "bb2e6c157c76"
down_revision = "fbd21e182e9b"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("uq_optionId_caseId", "case_label_assoc", type_="unique")
    op.create_index(
        "ix_unique_option_case_archived",
        "case_label_assoc",
        ["option_id", "case_id"],
        unique=True,
        postgresql_where="archived_time IS NULL",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        "ix_unique_option_case_archived",
        table_name="case_label_assoc",
        postgresql_where="archived_time IS NULL",
    )
    op.create_unique_constraint("uq_optionId_caseId", "case_label_assoc", ["option_id", "case_id"])
    # ### end Alembic commands ###
