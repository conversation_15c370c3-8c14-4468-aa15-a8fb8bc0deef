"""Add patient class to Case

Revision ID: bc1eed63aa0f
Revises: 2efd34f790c6
Create Date: 2023-03-28 14:19:46.018997

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "bc1eed63aa0f"
down_revision = "2efd34f790c6"
branch_labels = None
depends_on = None


def generate_patient_class_enum():
    return postgresql.ENUM(
        "EMERGENCY",
        "HOSPITAL_OUTPATIENT_SURGERY",
        "INPATIENT",
        "SURGERY_ADMIT",
        name="patientclass",
    )


def upgrade():
    patient_class = generate_patient_class_enum()
    patient_class.create(op.get_bind())
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "cases",
        sa.Column(
            "patient_class",
            patient_class,
            nullable=True,
        ),
    )
    op.create_index(op.f("ix_cases_patient_class"), "cases", ["patient_class"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_cases_patient_class"), table_name="cases")
    op.drop_column("cases", "patient_class")
    # ### end Alembic commands ###
    patient_class = generate_patient_class_enum()
    patient_class.drop(op.get_bind())
