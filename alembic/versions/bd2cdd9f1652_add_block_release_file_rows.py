"""add block_release_file_rows

Revision ID: bd2cdd9f1652
Revises: 50c8d071447f
Create Date: 2025-03-25 09:48:44.020897

"""

import sqlalchemy as sa

from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "bd2cdd9f1652"
down_revision = "50c8d071447f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum("PENDING", "PROCESSED", "REJECTED", name="block_processing_statuses").create(
        op.get_bind()
    )
    op.create_table(
        "block_release_file_rows",
        sa.Column("block_release_file_id", sa.UUID(), nullable=False),
        sa.Column("row_number", sa.Integer(), nullable=False),
        sa.Column("external_id", sa.String(), nullable=True),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.Column("site_id", sa.String(), nullable=True),
        sa.Column("room_id", sa.String(), nullable=True),
        sa.Column("room_name", sa.String(), nullable=True),
        sa.Column("timezone", sa.String(), nullable=True),
        sa.Column("start_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column("end_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column("release_length", sa.Integer(), nullable=True),
        sa.Column("released_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column("release_reason", sa.String(), nullable=True),
        sa.Column("released_by", sa.String(), nullable=True),
        sa.Column("block_name", sa.String(), nullable=True),
        sa.Column("block_date", sa.DateTime(timezone=True), nullable=True),
        sa.Column("to_block_name", sa.String(), nullable=True),
        sa.Column(
            "from_block_type",
            postgresql.ENUM(
                "UNBLOCKED_BLOCK_TYPE",
                "UNAVAILABLE_BLOCK_TYPE",
                "GROUP_BLOCK_TYPE",
                "SURGEON_BLOCK_TYPE",
                "SERVICE_BLOCK_TYPE",
                "ON_HOLD_BLOCK_TYPE",
                "UNKNOWN",
                name="blocktypes",
                create_type=False,
            ),
            nullable=True,
        ),
        sa.Column(
            "to_block_type",
            postgresql.ENUM(
                "UNBLOCKED_BLOCK_TYPE",
                "UNAVAILABLE_BLOCK_TYPE",
                "GROUP_BLOCK_TYPE",
                "SURGEON_BLOCK_TYPE",
                "SERVICE_BLOCK_TYPE",
                "ON_HOLD_BLOCK_TYPE",
                "UNKNOWN",
                name="blocktypes",
                create_type=False,
            ),
            nullable=True,
        ),
        sa.Column(
            "processing_status",
            postgresql.ENUM(
                "PENDING",
                "PROCESSED",
                "REJECTED",
                name="block_processing_statuses",
                create_type=False,
            ),
            nullable=False,
        ),
        sa.Column("rejected_reason", sa.String(), nullable=True),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["block_release_file_id"], ["block_release_processed_files.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("block_release_file_id", "row_number"),
    )
    op.create_index(
        op.f("ix_block_release_file_rows_block_release_file_id"),
        "block_release_file_rows",
        ["block_release_file_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_release_file_rows_created_time"),
        "block_release_file_rows",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_release_file_rows_external_id"),
        "block_release_file_rows",
        ["external_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_release_file_rows_org_id"),
        "block_release_file_rows",
        ["org_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_release_file_rows_room_id"),
        "block_release_file_rows",
        ["room_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_release_file_rows_room_name"),
        "block_release_file_rows",
        ["room_name"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_release_file_rows_site_id"),
        "block_release_file_rows",
        ["site_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_release_file_rows_updated_time"),
        "block_release_file_rows",
        ["updated_time"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_block_release_file_rows_updated_time"), table_name="block_release_file_rows"
    )
    op.drop_index(op.f("ix_block_release_file_rows_site_id"), table_name="block_release_file_rows")
    op.drop_index(
        op.f("ix_block_release_file_rows_room_name"), table_name="block_release_file_rows"
    )
    op.drop_index(op.f("ix_block_release_file_rows_room_id"), table_name="block_release_file_rows")
    op.drop_index(op.f("ix_block_release_file_rows_org_id"), table_name="block_release_file_rows")
    op.drop_index(
        op.f("ix_block_release_file_rows_external_id"), table_name="block_release_file_rows"
    )
    op.drop_index(
        op.f("ix_block_release_file_rows_created_time"), table_name="block_release_file_rows"
    )
    op.drop_index(
        op.f("ix_block_release_file_rows_block_release_file_id"),
        table_name="block_release_file_rows",
    )
    op.drop_table("block_release_file_rows")
    sa.Enum("PENDING", "PROCESSED", "REJECTED", name="block_processing_statuses").drop(
        op.get_bind()
    )
    # ### end Alembic commands ###
