"""modifying user id to be a string

Revision ID: bd7c4f7d5e7f
Revises: c83db3bb9078
Create Date: 2021-08-19 09:42:39.916452

"""

from sqlalchemy import text
from sqlalchemy.orm import Session

from alembic import op

# revision identifiers, used by Alembic.

revision = "bd7c4f7d5e7f"
down_revision = "c83db3bb9078"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Automatically created that I didn't modify
    op.create_index(op.f("ix_case_raw_created_time"), "case_raw", ["created_time"], unique=False)
    op.create_index(op.f("ix_case_raw_updated_time"), "case_raw", ["updated_time"], unique=False)
    op.create_index(
        op.f("ix_case_staffing_created_time"),
        "case_staffing",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_staffing_updated_time"),
        "case_staffing",
        ["updated_time"],
        unique=False,
    )
    op.create_index(op.f("ix_cases_created_time"), "cases", ["created_time"], unique=False)
    op.create_index(op.f("ix_cases_updated_time"), "cases", ["updated_time"], unique=False)
    op.create_index(
        op.f("ix_identifier_mapping_created_time"),
        "identifier_mapping",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_identifier_mapping_updated_time"),
        "identifier_mapping",
        ["updated_time"],
        unique=False,
    )

    # My modification to change user_id in highlight_feedback from uuid to string
    # ALTER TABLE highlight_feedback ALTER COLUMN user_id SET DATA TYPE text;
    bind = op.get_bind()
    session = Session(bind=bind)
    session.execute(text("ALTER TABLE highlight_feedback ALTER COLUMN user_id SET DATA TYPE text;"))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_identifier_mapping_updated_time"), table_name="identifier_mapping")
    op.drop_index(op.f("ix_identifier_mapping_created_time"), table_name="identifier_mapping")
    op.drop_index(op.f("ix_cases_updated_time"), table_name="cases")
    op.drop_index(op.f("ix_cases_created_time"), table_name="cases")
    op.drop_index(op.f("ix_case_staffing_updated_time"), table_name="case_staffing")
    op.drop_index(op.f("ix_case_staffing_created_time"), table_name="case_staffing")
    op.drop_index(op.f("ix_case_raw_updated_time"), table_name="case_raw")
    op.drop_index(op.f("ix_case_raw_created_time"), table_name="case_raw")

    # ALTER TABLE highlight_feedback ALTER COLUMN user_id SET DATA TYPE uuid;
    bind = op.get_bind()
    session = Session(bind=bind)
    session.execute(
        text(
            "ALTER TABLE highlight_feedback ALTER COLUMN user_id SET DATA TYPE uuid USING user_id::uuid;"
        )
    )  # noqa E501
    # ### end Alembic commands ###
