"""Add table for tracking sent notifications

Revision ID: be89b76595b7
Revises: 051622cb2c33
Create Date: 2023-04-19 18:02:48.292781

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "be89b76595b7"
down_revision = "16e4ffd9a974"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "staff_event_notification_contact_information",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=True),
    )
    op.create_index(
        op.f("ix_staff_event_notification_contact_information_id"),
        "staff_event_notification_contact_information",
        ["id"],
        unique=False,
    )
    op.create_unique_constraint(
        "uq_staffEventNotification_id", "staff_event_notification_contact_information", ["id"]
    )
    op.create_table(
        "staff_event_notification",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("message_id", sa.String(), nullable=False),
        sa.Column(
            "staff_event_contact_information_id", postgresql.UUID(as_uuid=True), nullable=False
        ),
        sa.Column("event_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(["event_id"], ["events.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(
            ["staff_event_contact_information_id"],
            ["staff_event_notification_contact_information.id"],
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint(
            "message_id",
            "staff_event_contact_information_id",
            name="pk_staffEventNotification_msgId_staffEventContactInformationId",
        ),
    )
    op.create_index(
        op.f("ix_staff_event_notification_created_time"),
        "staff_event_notification",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_event_id"),
        "staff_event_notification",
        ["event_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_id"), "staff_event_notification", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_staff_event_notification_message_id"),
        "staff_event_notification",
        ["message_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_staff_event_contact_information_id"),
        "staff_event_notification",
        ["staff_event_contact_information_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_staff_event_notification_updated_time"),
        "staff_event_notification",
        ["updated_time"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_staff_event_notification_updated_time"), table_name="staff_event_notification"
    )
    op.drop_index(
        op.f("ix_staff_event_notification_staff_event_contact_information_id"),
        table_name="staff_event_notification",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_message_id"), table_name="staff_event_notification"
    )
    op.drop_index(op.f("ix_staff_event_notification_id"), table_name="staff_event_notification")
    op.drop_index(
        op.f("ix_staff_event_notification_event_id"), table_name="staff_event_notification"
    )
    op.drop_index(
        op.f("ix_staff_event_notification_created_time"), table_name="staff_event_notification"
    )
    op.drop_table("staff_event_notification")
    op.drop_constraint(
        "uq_staffEventNotification_id",
        "staff_event_notification_contact_information",
        type_="unique",
    )
    op.drop_index(
        op.f("ix_staff_event_notification_contact_information_id"),
        table_name="staff_event_notification_contact_information",
    )
    op.drop_column("staff_event_notification_contact_information", "id")
    # ### end Alembic commands ###
