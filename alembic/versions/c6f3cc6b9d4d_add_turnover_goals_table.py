"""Add turnover_goals table

Revision ID: c6f3cc6b9d4d
Revises: 035122c960d8
Create Date: 2024-08-01 18:02:13.230473

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "c6f3cc6b9d4d"
down_revision = "035122c960d8"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "turnover_goals",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.<PERSON>umn("goal_minutes", sa.Integer(), nullable=False),
        sa.<PERSON>umn("max_minutes", sa.Integer(), nullable=False),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.Column("site_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["org_id"],
            ["organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["site_id"],
            ["sites.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_turnover_goals_created_time"),
        "turnover_goals",
        ["created_time"],
        unique=False,
    )
    op.create_index(op.f("ix_turnover_goals_org_id"), "turnover_goals", ["org_id"], unique=False)
    op.create_index(op.f("ix_turnover_goals_site_id"), "turnover_goals", ["site_id"], unique=False)
    op.create_index(
        op.f("ix_turnover_goals_updated_time"),
        "turnover_goals",
        ["updated_time"],
        unique=False,
    )

    op.create_check_constraint("goal_minutes_positive", "turnover_goals", "goal_minutes > 0")
    op.create_check_constraint("max_minutes_positive", "turnover_goals", "max_minutes > 0")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_turnover_goals_updated_time"), table_name="turnover_goals")
    op.drop_index(op.f("ix_turnover_goals_site_id"), table_name="turnover_goals")
    op.drop_index(op.f("ix_turnover_goals_org_id"), table_name="turnover_goals")
    op.drop_index(op.f("ix_turnover_goals_created_time"), table_name="turnover_goals")
    op.drop_table("turnover_goals")
    # ### end Alembic commands ###
