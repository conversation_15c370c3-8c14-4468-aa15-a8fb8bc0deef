"""EHR-584: remove default org_id value

Revision ID: c783a838437c
Revises: e4a40adc80e0
Create Date: 2024-08-23 16:57:37.076794

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "c783a838437c"
down_revision = "e4a40adc80e0"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "case_classification_types",
        "org_id",
        existing_type=sa.String(),
        server_default=None,
    )


def downgrade():
    op.alter_column(
        "case_classification_types",
        "org_id",
        existing_type=sa.String(),
        server_default="temporary",
    )
