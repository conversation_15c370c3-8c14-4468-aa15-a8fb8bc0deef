"""Add task type rooms

Revision ID: c9a94e945cf0
Revises: e072a3281e64
Create Date: 2024-01-29 18:04:17.339778

"""

import sqlalchemy as sa

from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "c9a94e945cf0"
down_revision = "e072a3281e64"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "annotation_task_schedules_rooms",
        sa.Column("schedule_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("room_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["room_id"],
            ["rooms.id"],
        ),
        sa.ForeignKeyConstraint(
            ["schedule_id"],
            ["annotation_task_schedules.id"],
        ),
        sa.PrimaryKeyConstraint("schedule_id", "room_id"),
    )


def downgrade():
    op.drop_table("annotation_task_schedules_rooms")
