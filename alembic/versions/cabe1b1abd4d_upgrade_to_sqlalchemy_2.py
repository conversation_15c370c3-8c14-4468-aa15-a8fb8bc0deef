"""Upgrade to sqlalchemy 2

Revision ID: cabe1b1abd4d
Revises: ccca3c3cb269
Create Date: 2024-06-20 13:43:12.071677

"""

import sqlalchemy as sa

from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "cabe1b1abd4d"
down_revision = "ccca3c3cb269"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "annotation_tasks_history_type_id_type_version_fkey",
        "annotation_tasks_history",
        type_="foreignkey",
    )
    op.drop_constraint(
        "annotation_tasks_history_type_id_fkey", "annotation_tasks_history", type_="foreignkey"
    )
    op.drop_constraint(
        "cases_history_case_classification_types_id_fkey", "cases_history", type_="foreignkey"
    )
    op.drop_constraint("cases_history_service_line_id_fkey", "cases_history", type_="foreignkey")
    op.alter_column("contact_information", "id", existing_type=sa.UUID(), nullable=False)
    op.drop_constraint("events_history_event_type_id_fkey", "events_history", type_="foreignkey")
    op.drop_constraint("phases_history_end_event_id_fkey", "phases_history", type_="foreignkey")
    op.drop_constraint("phases_history_start_event_id_fkey", "phases_history", type_="foreignkey")
    op.drop_constraint("phases_history_end_event_id_fkey1", "phases_history", type_="foreignkey")
    op.drop_constraint("phases_history_start_event_id_fkey1", "phases_history", type_="foreignkey")
    op.alter_column(
        "staff_event_notification",
        "sent_time",
        existing_type=postgresql.TIMESTAMP(),
        type_=sa.DateTime(timezone=True),
        existing_nullable=True,
    )
    op.alter_column(
        "staff_event_notification_history",
        "sent_time",
        existing_type=postgresql.TIMESTAMP(),
        type_=sa.DateTime(timezone=True),
        existing_nullable=True,
        autoincrement=False,
    )
    op.alter_column(
        "staff_event_notification_old_01", "id", existing_type=sa.UUID(), nullable=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("staff_event_notification_old_01", "id", existing_type=sa.UUID(), nullable=True)
    op.alter_column(
        "staff_event_notification_history",
        "sent_time",
        existing_type=sa.DateTime(timezone=True),
        type_=postgresql.TIMESTAMP(),
        existing_nullable=True,
        autoincrement=False,
    )
    op.alter_column(
        "staff_event_notification",
        "sent_time",
        existing_type=sa.DateTime(timezone=True),
        type_=postgresql.TIMESTAMP(),
        existing_nullable=True,
    )
    op.create_foreign_key(
        "phases_history_start_event_id_fkey", "phases_history", "events", ["start_event_id"], ["id"]
    )
    op.create_foreign_key(
        "phases_history_end_event_id_fkey", "phases_history", "events", ["end_event_id"], ["id"]
    )
    op.create_foreign_key(
        "phases_history_start_event_id_fkey1",
        "phases_history",
        "events",
        ["start_event_id"],
        ["id"],
    )
    op.create_foreign_key(
        "phases_history_end_event_id_fkey1", "phases_history", "events", ["end_event_id"], ["id"]
    )
    op.create_foreign_key(
        "events_history_event_type_id_fkey",
        "events_history",
        "event_types",
        ["event_type_id"],
        ["id"],
    )
    op.alter_column("contact_information", "id", existing_type=sa.UUID(), nullable=True)
    op.create_foreign_key(
        "cases_history_service_line_id_fkey",
        "cases_history",
        "service_lines",
        ["service_line_id"],
        ["id"],
    )
    op.create_foreign_key(
        "cases_history_case_classification_types_id_fkey",
        "cases_history",
        "case_classification_types",
        ["case_classification_types_id"],
        ["id"],
    )
    op.create_foreign_key(
        "annotation_tasks_history_type_id_fkey",
        "annotation_tasks_history",
        "annotation_task_types",
        ["type_id"],
        ["id"],
    )
    op.create_foreign_key(
        "annotation_tasks_history_type_id_type_version_fkey",
        "annotation_tasks_history",
        "annotation_task_types_history",
        ["type_id", "type_version"],
        ["id", "version"],
    )
    # ### end Alembic commands ###
