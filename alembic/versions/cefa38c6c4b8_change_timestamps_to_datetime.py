"""Change timestamps to datetime, and add created and updated time.

This change is destructive, and drops the columns.  So we want to run this
before we actually care about keeping the data.

Revision ID: cefa38c6c4b8
Revises: 8cf5fd04a704
Create Date: 2021-03-01 11:58:53.441669

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "cefa38c6c4b8"
down_revision = "8cf5fd04a704"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column("media_assets", "end_time")
    op.drop_column("media_assets", "start_time")
    op.add_column(
        "media_assets",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
    )
    op.add_column(
        "media_assets",
        sa.Column("updated_time", sa.DateTime(timezone=True), nullable=True),
    )
    op.add_column("media_assets", sa.Column("end_time", sa.DateTime(timezone=True), nullable=True))
    op.add_column(
        "media_assets",
        sa.Column("start_time", sa.DateTime(timezone=True), nullable=True),
    )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("media_assets", "updated_time")
    op.drop_column("media_assets", "start_time")
    op.drop_column("media_assets", "end_time")
    op.drop_column("media_assets", "created_time")
    op.add_column(
        "media_assets",
        sa.Column("start_time", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.add_column(
        "media_assets",
        sa.Column("end_time", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    # ### end Alembic commands ###
