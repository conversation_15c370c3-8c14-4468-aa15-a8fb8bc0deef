"""Add deleted at index

Revision ID: cf9303c43ce3
Revises: 5fe466d1e97b
Create Date: 2022-11-10 14:49:53.923243

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "cf9303c43ce3"
down_revision = "5fe466d1e97b"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f("ix_events_deleted_at"), "events", ["deleted_at"], unique=False)
    op.create_index(
        op.f("ix_events_history_deleted_at"), "events_history", ["deleted_at"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_events_history_deleted_at"), table_name="events_history")
    op.drop_index(op.f("ix_events_deleted_at"), table_name="events")
    # ### end Alembic commands ###
