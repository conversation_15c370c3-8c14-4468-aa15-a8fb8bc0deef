"""Add archived_by_user_id to StaffingNeedsModel

Revision ID: d289922383cb
Revises: 7ed83e6c0738
Create Date: 2023-03-27 16:52:03.740965

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "d289922383cb"
down_revision = "7ed83e6c0738"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("staffing_needs", sa.Column("archived_by_user_id", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("staffing_needs", "archived_by_user_id")
    # ### end Alembic commands ###
