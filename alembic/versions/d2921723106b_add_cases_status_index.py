"""Add cases.status index

Revision ID: d2921723106b
Revises: 91fdaf511aca
Create Date: 2024-06-06 16:58:54.412888

"""

from alembic import op


# revision identifiers, used by Alembic.
revision = "d2921723106b"
down_revision = "91fdaf511aca"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f("ix_cases_status"), "cases", ["status"], unique=False)
    op.create_index(op.f("ix_cases_history_status"), "cases_history", ["status"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_cases_history_status"), table_name="cases_history")
    op.drop_index(op.f("ix_cases_status"), table_name="cases")
    # ### end Alembic commands ###
