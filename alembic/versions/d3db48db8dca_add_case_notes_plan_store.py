"""Add Case Notes Plan Store

Revision ID: d3db48db8dca
Revises: 2d57fdb47d29
Create Date: 2023-12-12 14:17:18.052278

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "d3db48db8dca"
down_revision = "93396b1e7175"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "case_notes_plan",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("case_id", sa.String(), nullable=False),
        sa.Column("notes", sa.String(), nullable=True),
        sa.ForeignKeyConstraint(
            ["case_id"],
            ["cases.case_id"],
        ),
        sa.PrimaryKeyConstraint("case_id"),
    )
    op.create_index(
        op.f("ix_case_notes_plan_case_id"), "case_notes_plan", ["case_id"], unique=False
    )
    op.create_index(
        op.f("ix_case_notes_plan_created_time"), "case_notes_plan", ["created_time"], unique=False
    )
    op.create_index(
        op.f("ix_case_notes_plan_updated_time"), "case_notes_plan", ["updated_time"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_case_notes_plan_updated_time"), table_name="case_notes_plan")
    op.drop_index(op.f("ix_case_notes_plan_created_time"), table_name="case_notes_plan")
    op.drop_index(op.f("ix_case_notes_plan_case_id"), table_name="case_notes_plan")
    op.drop_table("case_notes_plan")
    # ### end Alembic commands ###
