"""Add start and end times to block_time_releases, block_times, and measurement_periods

Revision ID: d3f43fae5928
Revises: e8f9ff56673d
Create Date: 2024-01-08 16:25:57.693715

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "d3f43fae5928"
down_revision = "e8f9ff56673d"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("SET statement_timeout TO 60000;")  # 1 minute for this session

    op.add_column(
        "block_time_releases", sa.Column("start_time", sa.DateTime(timezone=True), nullable=True)
    )
    op.add_column(
        "block_time_releases", sa.Column("end_time", sa.DateTime(timezone=True), nullable=True)
    )
    op.create_index(
        op.f("ix_block_time_releases_end_time"), "block_time_releases", ["end_time"], unique=False
    )
    op.create_index(
        op.f("ix_block_time_releases_start_time"),
        "block_time_releases",
        ["start_time"],
        unique=False,
    )
    op.execute(
        """
        UPDATE public.block_time_releases
        SET start_time = lower(release_time),
            end_time = upper(release_time)
    """
    )
    op.execute(
        """
        DO $$
            BEGIN
                ALTER TABLE public.block_time_releases DROP CONSTRAINT IF EXISTS ck_block_time_release_start_time_lt_end_time;

                ALTER TABLE public.block_time_releases ADD CONSTRAINT ck_block_time_release_start_time_lt_end_time CHECK (start_time < end_time);
            END
        $$
    """
    )
    op.alter_column("block_time_releases", "start_time", nullable=False)
    op.alter_column("block_time_releases", "end_time", nullable=False)

    op.add_column("block_times", sa.Column("start_time", sa.DateTime(timezone=True), nullable=True))
    op.add_column("block_times", sa.Column("end_time", sa.DateTime(timezone=True), nullable=True))
    op.create_index(op.f("ix_block_times_end_time"), "block_times", ["end_time"], unique=False)
    op.create_index(op.f("ix_block_times_start_time"), "block_times", ["start_time"], unique=False)
    op.execute(
        """
        UPDATE public.block_times
        SET start_time = lower(block_time),
            end_time = upper(block_time)
    """
    )
    op.execute(
        """
        DO $$
            BEGIN
                ALTER TABLE public.block_time_releases DROP CONSTRAINT IF EXISTS ck_block_time_start_time_lt_end_time;

                ALTER TABLE public.block_times ADD CONSTRAINT ck_block_time_start_time_lt_end_time CHECK (start_time < end_time);
            END
        $$
    """
    )
    op.alter_column("block_times", "start_time", nullable=False)
    op.alter_column("block_times", "end_time", nullable=False)

    op.add_column("measurement_periods", sa.Column("start_date", sa.Date(), nullable=True))
    op.add_column("measurement_periods", sa.Column("end_date", sa.Date(), nullable=True))
    op.create_index(
        op.f("ix_measurement_periods_end_date"), "measurement_periods", ["end_date"], unique=False
    )
    op.create_index(
        op.f("ix_measurement_periods_start_date"),
        "measurement_periods",
        ["start_date"],
        unique=False,
    )
    op.execute(
        """
        UPDATE public.measurement_periods
        SET start_date = lower(measurement_period),
            end_date = upper(measurement_period)
    """
    )
    op.alter_column("measurement_periods", "start_date", nullable=False)
    op.alter_column("measurement_periods", "end_date", nullable=False)


def downgrade():
    op.drop_index(op.f("ix_measurement_periods_start_date"), table_name="measurement_periods")
    op.drop_index(op.f("ix_measurement_periods_end_date"), table_name="measurement_periods")
    op.drop_column("measurement_periods", "end_date")
    op.drop_column("measurement_periods", "start_date")

    op.drop_index(op.f("ix_block_times_start_time"), table_name="block_times")
    op.drop_index(op.f("ix_block_times_end_time"), table_name="block_times")
    op.drop_column("block_times", "end_time")
    op.drop_column("block_times", "start_time")

    op.drop_index(op.f("ix_block_time_releases_start_time"), table_name="block_time_releases")
    op.drop_index(op.f("ix_block_time_releases_end_time"), table_name="block_time_releases")
    op.drop_column("block_time_releases", "end_time")
    op.drop_column("block_time_releases", "start_time")
