"""Add back_table_cleared

Revision ID: d4d82b19c0ba
Revises: 37d544b532c2
Create Date: 2022-11-21 10:04:05.537733

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "d4d82b19c0ba"
down_revision = "37d544b532c2"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        "INSERT INTO event_types "
        "(id, type, name, color, hidden) "
        "VALUES('back_table_cleared','backtable_status','','#999999',true) ON CONFLICT DO NOTHING;"
    )


def downgrade():
    op.execute("DELETE FROM event_types WHERE id = 'back_table_cleared'")
