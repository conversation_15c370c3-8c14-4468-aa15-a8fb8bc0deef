"""Add event_label_options table

Revision ID: d5f51f224b2a
Revises: b4b1fe14e22f
Create Date: 2024-05-09 16:22:01.051609

"""

import sqlalchemy as sa

from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "d5f51f224b2a"
down_revision = "b4b1fe14e22f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "event_label_options",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            server_default=sa.text("gen_random_uuid()"),
            nullable=False,
        ),
        sa.Column("name", sa.String(length=128), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_event_label_options_created_time"),
        "event_label_options",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_event_label_options_name"), "event_label_options", ["name"], unique=True
    )
    op.create_index(
        op.f("ix_event_label_options_updated_time"),
        "event_label_options",
        ["updated_time"],
        unique=False,
    )

    # Seed the table with existing options
    op.execute(
        """
        INSERT INTO event_label_options (name)
        VALUES ('Needs Review'),
               ('Edge Case'),
               ('Endo Case'),
               ('Movie Matinee'),
               ('Highlights'),
               ('Delay'),
               ('Terminal Clean'),
               ('Turnover'),
               ('Timeout'),
               ('Patient Carried'),
                ('Patient In Wheelchair'),
                ('Patient Walked In'),
                ('Patient Transported - Case In Progress'),
                ('Peri-operative Drape'),
                ('Post-operative Drape'),
                ('Potential Dataset'),
                ('Infant Procedure');
    """
    )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_event_label_options_updated_time"), table_name="event_label_options")
    op.drop_index(op.f("ix_event_label_options_name"), table_name="event_label_options")
    op.drop_index(op.f("ix_event_label_options_created_time"), table_name="event_label_options")
    op.drop_table("event_label_options")
    # ### end Alembic commands ###
