"""add case classification

Revision ID: d610d1e44179
Revises: 158d9cb860d7
Create Date: 2022-09-22 14:38:26.450630

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "d610d1e44179"
down_revision = "158d9cb860d7"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    case_classification_table = op.create_table(
        "case_classification_types",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("description", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_case_classification_types_created_time"),
        "case_classification_types",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_case_classification_types_updated_time"),
        "case_classification_types",
        ["updated_time"],
        unique=False,
    )
    op.bulk_insert(
        case_classification_table,
        [
            {"id": "CASE_CLASSIFICATION_ELECTIVE", "name": "Elective"},
            {"id": "CASE_CLASSIFICATION_URGENT", "name": "Urgent"},
            {"id": "CASE_CLASSIFICATION_EMERGENT", "name": "Emergent"},
        ],
    )
    op.alter_column(
        "phases",
        "status",
        existing_type=sa.VARCHAR(),
        nullable=True,
        existing_server_default=sa.text("'VALID'::character varying"),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "phases",
        "status",
        existing_type=sa.VARCHAR(),
        nullable=False,
        existing_server_default=sa.text("'VALID'::character varying"),
    )
    op.drop_index(
        op.f("ix_case_classification_types_updated_time"), table_name="case_classification_types"
    )
    op.drop_index(
        op.f("ix_case_classification_types_created_time"), table_name="case_classification_types"
    )
    op.drop_table("case_classification_types")
    # ### end Alembic commands ###
