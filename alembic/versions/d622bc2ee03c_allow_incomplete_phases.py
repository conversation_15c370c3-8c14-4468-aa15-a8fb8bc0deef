"""Allow incomplete phases

Revision ID: d622bc2ee03c
Revises: 5fe466d1e97b
Create Date: 2022-11-08 09:41:58.158324

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "d622bc2ee03c"
down_revision = "d896e4033903"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("phases", "end_event_id", existing_type=sa.VARCHAR(), nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("phases", "end_event_id", existing_type=sa.VARCHAR(), nullable=False)
    # ### end Alembic commands ###
