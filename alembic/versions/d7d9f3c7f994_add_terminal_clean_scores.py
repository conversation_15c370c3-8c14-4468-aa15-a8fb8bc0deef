"""Add terminal clean scores

Revision ID: d7d9f3c7f994
Revises: a3524a2d3c19
Create Date: 2025-04-16 18:05:10.244256

"""

import sqlalchemy as sa

from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "d7d9f3c7f994"
down_revision = "a3524a2d3c19"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum("COMPLETE", "PARTIAL", "MISSED", name="cleanscoreenum").create(op.get_bind())
    op.create_table(
        "terminal_clean_scores",
        sa.Column("id", sa.UUID(), server_default=sa.text("gen_random_uuid()"), nullable=False),
        sa.Column("room_id", sa.String(), nullable=False),
        sa.Column("date", sa.Date(), nullable=False),
        sa.Column("comments", sa.String(), server_default=sa.text("''"), nullable=False),
        sa.Column(
            "score",
            postgresql.ENUM(
                "COMPLETE", "PARTIAL", "MISSED", name="cleanscoreenum", create_type=False
            ),
            nullable=True,
        ),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(["room_id"], ["rooms.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("room_id", "date", name="terminal_clean_scores_unique_room_date"),
    )
    op.create_index(
        op.f("ix_terminal_clean_scores_created_time"),
        "terminal_clean_scores",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_terminal_clean_scores_date"), "terminal_clean_scores", ["date"], unique=False
    )
    op.create_index(
        op.f("ix_terminal_clean_scores_room_id"), "terminal_clean_scores", ["room_id"], unique=False
    )
    op.create_index(
        op.f("ix_terminal_clean_scores_updated_time"),
        "terminal_clean_scores",
        ["updated_time"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_terminal_clean_scores_updated_time"), table_name="terminal_clean_scores")
    op.drop_index(op.f("ix_terminal_clean_scores_room_id"), table_name="terminal_clean_scores")
    op.drop_index(op.f("ix_terminal_clean_scores_date"), table_name="terminal_clean_scores")
    op.drop_index(op.f("ix_terminal_clean_scores_created_time"), table_name="terminal_clean_scores")
    op.drop_table("terminal_clean_scores")
    sa.Enum("COMPLETE", "PARTIAL", "MISSED", name="cleanscoreenum").drop(op.get_bind())
    # ### end Alembic commands ###
