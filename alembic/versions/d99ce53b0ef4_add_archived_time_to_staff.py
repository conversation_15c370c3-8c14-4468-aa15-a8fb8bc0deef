"""Add archived_time to staff

Revision ID: d99ce53b0ef4
Revises: 309955556681
Create Date: 2022-10-21 14:36:06.813996

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "d99ce53b0ef4"
down_revision = "309955556681"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("staff", sa.Column("archived_time", sa.DateTime(timezone=True), nullable=True))
    op.create_index(op.f("ix_staff_archived_time"), "staff", ["archived_time"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_staff_archived_time"), table_name="staff")
    op.drop_column("staff", "archived_time")
    # ### end Alembic commands ###
