"""Change asset_id to a string

Revision ID: d9becd6cf0f8
Revises: cefa38c6c4b8
Create Date: 2021-03-12 10:33:56.804505

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "d9becd6cf0f8"
down_revision = "cefa38c6c4b8"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("media_assets", sa.Column("asset_id", sa.String(), nullable=True))
    # Add a unique uuid for existing rows, and make it non-nullable
    op.execute("UPDATE media_assets SET asset_id=(SELECT gen_random_uuid())")
    op.alter_column("media_assets", "asset_id", nullable=False)

    op.drop_column("media_assets", "id")


def downgrade():
    op.add_column("media_assets", sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=True))
    op.execute("CREATE TEMPORARY SEQUENCE id_seq")
    op.execute("UPDATE media_assets SET id=nextval('id_seq')")
    op.alter_column("media_assets", "id", nullable=False)

    op.drop_column("media_assets", "asset_id")
