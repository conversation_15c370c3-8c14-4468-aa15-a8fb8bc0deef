"""Fix custom phase column type

Revision ID: dc908967efe1
Revises: 4633f8e13b2e
Create Date: 2025-04-14 11:01:31.968406

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "dc908967efe1"
down_revision = "4633f8e13b2e"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "observation_type_names",
        sa.Column(
            "is_custom_phase_end_point", sa.<PERSON>(), server_default="false", nullable=False
        ),
    )
    op.drop_column("observation_type_names", "is_custom_phase_endpoint")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "observation_type_names",
        sa.Column(
            "is_custom_phase_endpoint",
            sa.VARCHAR(),
            server_default=sa.text("'false'::character varying"),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.drop_column("observation_type_names", "is_custom_phase_end_point")
    # ### end Alembic commands ###
