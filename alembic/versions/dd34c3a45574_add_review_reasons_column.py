"""Add review reasons column

Revision ID: dd34c3a45574
Revises: b1a0efc19a3f
Create Date: 2023-10-18 14:36:38.869326

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "dd34c3a45574"
down_revision = "b1a0efc19a3f"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "annotation_tasks",
        sa.Column(
            "review_reasons", postgresql.ARRAY(sa.String()), nullable=False, server_default="{}"
        ),
    )
    op.add_column(
        "annotation_tasks_history",
        sa.Column(
            "review_reasons",
            postgresql.ARRAY(sa.String()),
            autoincrement=False,
            nullable=False,
            server_default="{}",
        ),
    )


def downgrade() -> None:
    op.drop_column("annotation_tasks_history", "review_reasons")
    op.drop_column("annotation_tasks", "review_reasons")
