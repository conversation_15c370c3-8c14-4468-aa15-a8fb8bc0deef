"""Remove FK constraints from event_type_id and event_id

Revision ID: dda66d950d23
Revises: fc24d86a80eb
Create Date: 2025-04-10 21:26:48.353673

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "dda66d950d23"
down_revision = "fc24d86a80eb"
branch_labels = None
depends_on = None


def upgrade():
    # Drop FK from staff_event_notification_contact_information.event_type_id
    op.drop_constraint(
        "staff_event_notification_contact_information_event_type_id_fkey",
        "staff_event_notification_contact_information",
        type_="foreignkey",
    )

    # Drop FK from staff_event_notification.event_id
    op.drop_constraint(
        "staff_event_notification_event_id_fkey", "staff_event_notification", type_="foreignkey"
    )

    # Drop FK from staff_event_notification_old_01.event_id
    op.drop_constraint(
        "staff_event_notification_old_01_event_id_fkey",
        "staff_event_notification_old_01",
        type_="foreignkey",
    )


def downgrade():
    # Re-add FK to event_types.id (if reverting back to single-table mapping)
    op.create_foreign_key(
        "staff_event_notification_contact_information_event_type_id_fkey",
        "staff_event_notification_contact_information",
        "event_types",
        ["event_type_id"],
        ["id"],
        ondelete="CASCADE",
    )

    # Re-add FK to events.id
    op.create_foreign_key(
        "staff_event_notification_event_id_fkey",
        "staff_event_notification",
        "events",
        ["event_id"],
        ["id"],
        ondelete="CASCADE",
    )

    # Re-add FK to events.id
    op.create_foreign_key(
        "staff_event_notification_old_01_event_id_fkey",
        "staff_event_notification_old_01",
        "events",
        ["event_id"],
        ["id"],
        ondelete="CASCADE",
    )
