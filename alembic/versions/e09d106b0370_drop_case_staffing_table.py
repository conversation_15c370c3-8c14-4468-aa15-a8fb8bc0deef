"""Drop case_staffing table

Revision ID: e09d106b0370
Revises: 40a65e4f73a3
Create Date: 2022-06-10 12:57:59.234579

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "e09d106b0370"
down_revision = "40a65e4f73a3"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_case_staffing_case_id", table_name="case_staffing")
    op.drop_index("ix_case_staffing_created_time", table_name="case_staffing")
    op.drop_index("ix_case_staffing_org_id", table_name="case_staffing")
    op.drop_index("ix_case_staffing_updated_time", table_name="case_staffing")
    op.drop_table("case_staffing")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "case_staffing",
        sa.Column(
            "created_time",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_time", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True
        ),
        sa.Column("staffing_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("external_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("external_id_type", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("case_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("org_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(["case_id"], ["cases.case_id"], name="case_staffing_case_id_fkey"),
        sa.ForeignKeyConstraint(["org_id"], ["organizations.id"], name="case_staffing_org_id_fkey"),
        sa.PrimaryKeyConstraint("staffing_id", name="case_staffing_pkey"),
    )
    op.create_index(
        "ix_case_staffing_updated_time", "case_staffing", ["updated_time"], unique=False
    )
    op.create_index("ix_case_staffing_org_id", "case_staffing", ["org_id"], unique=False)
    op.create_index(
        "ix_case_staffing_created_time", "case_staffing", ["created_time"], unique=False
    )
    op.create_index("ix_case_staffing_case_id", "case_staffing", ["case_id"], unique=False)
    # ### end Alembic commands ###
