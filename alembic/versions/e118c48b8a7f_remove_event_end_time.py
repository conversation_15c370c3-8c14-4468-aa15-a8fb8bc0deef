"""Remove Event End Time

Revision ID: e118c48b8a7f
Revises: 347a07ca3501
Create Date: 2022-09-23 13:14:41.885124

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "e118c48b8a7f"
down_revision = "347a07ca3501"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("DROP INDEX IF EXISTS ix_events_end_time;")
    op.execute("ALTER TABLE events DROP COLUMN IF EXISTS end_time;")

    op.execute("DROP INDEX IF EXISTS ix_events_history_end_time;")
    op.execute("ALTER TABLE events_history DROP COLUMN IF EXISTS end_time;")

    # This statement is run through a DB Client to avoid timing out issue
    # op.execute("UPDATE events SET deleted_at = NOW() WHERE event_type = 'phase'")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "events_history",
        sa.Column(
            "end_time", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True
        ),
    )
    op.create_index("ix_events_history_end_time", "events_history", ["end_time"], unique=False)
    op.add_column(
        "events",
        sa.Column(
            "end_time", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True
        ),
    )
    op.create_index("ix_events_end_time", "events", ["end_time"], unique=False)
    # ### end Alembic commands ###
