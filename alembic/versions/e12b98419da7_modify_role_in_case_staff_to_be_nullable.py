"""Modify role in case_staff to be nullable

Revision ID: e12b98419da7
Revises: 25075f7c51bc
Create Date: 2022-06-03 16:33:33.004952

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "e12b98419da7"
down_revision = "25075f7c51bc"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("case_staff", "role", existing_type=sa.VARCHAR(), nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("case_staff", "role", existing_type=sa.VARCHAR(), nullable=False)
    # ### end Alembic commands ###
