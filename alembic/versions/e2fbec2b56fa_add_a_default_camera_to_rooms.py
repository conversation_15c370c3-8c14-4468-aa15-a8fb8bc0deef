"""add a default camera to rooms

Revision ID: e2fbec2b56fa
Revises: 707f26515bda
Create Date: 2022-01-10 15:25:04.589891

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "e2fbec2b56fa"
down_revision = "707f26515bda"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("rooms", sa.Column("default_camera_id", sa.String(), nullable=True))
    op.create_foreign_key(
        None, "rooms", "cameras", ["default_camera_id"], ["id"], ondelete="SET NULL"
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "rooms", type_="foreignkey")
    op.drop_column("rooms", "default_camera_id")
    # ### end Alembic commands ###
