"""Add measurement period model

Revision ID: e3b0d138e81c
Revises: eb8241cc25a9
Create Date: 2023-06-27 14:20:06.972559

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "e3b0d138e81c"
down_revision = "eb8241cc25a9"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("CREATE EXTENSION IF NOT EXISTS btree_gist;")

    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "measurement_periods",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            server_default=sa.text("gen_random_uuid()"),
            nullable=False,
        ),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("measurement_period", postgresql.DATERANGE(), nullable=False),
        sa.Column("site_id", sa.String(), nullable=False),
        postgresql.ExcludeConstraint(
            (sa.column("site_id"), "="),
            (sa.column("measurement_period"), "&&"),
            using="gist",
            name="unique_daterange_constraint",
        ),
        sa.ForeignKeyConstraint(
            ["site_id"],
            ["sites.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_measurement_periods_created_time"),
        "measurement_periods",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_measurement_periods_name"),
        "measurement_periods",
        ["name"],
        unique=False,
    )
    op.create_index(
        op.f("ix_measurement_periods_site_id"),
        "measurement_periods",
        ["site_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_measurement_periods_updated_time"),
        "measurement_periods",
        ["updated_time"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_measurement_periods_updated_time"), table_name="measurement_periods")
    op.drop_index(op.f("ix_measurement_periods_site_id"), table_name="measurement_periods")
    op.drop_index(op.f("ix_measurement_periods_name"), table_name="measurement_periods")
    op.drop_index(op.f("ix_measurement_periods_created_time"), table_name="measurement_periods")
    op.drop_table("measurement_periods")

    op.execute("DROP EXTENSION IF EXISTS btree_gist;")
    # ### end Alembic commands ###
