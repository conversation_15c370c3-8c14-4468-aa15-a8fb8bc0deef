"""Case Raw adding archived at column

Revision ID: e3b70043c6e0
Revises: 25aaa0fd8421
Create Date: 2024-04-05 11:54:12.892331

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "e3b70043c6e0"
down_revision = "25aaa0fd8421"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("case_raw", sa.Column("archived_at", sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("case_raw", "archived_at")
    # ### end Alembic commands ###
