"""Add isApellaEmployee

Revision ID: e40264c6313b
Revises: 635a242575f1
Create Date: 2024-09-18 15:12:33.058661

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "e40264c6313b"
down_revision = "635a242575f1"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "contact_information", sa.<PERSON>umn("is_apella_employee", sa.<PERSON>(), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("contact_information", "is_apella_employee")
    # ### end Alembic commands ###
