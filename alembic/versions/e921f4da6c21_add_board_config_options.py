"""add board config options

Revision ID: e921f4da6c21
Revises: 92d088d3af3a
Create Date: 2023-11-27 12:16:14.410690

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "e921f4da6c21"
down_revision = "0d595b603b1c"
branch_labels = None
depends_on = None


BoardViewType = sa.Enum("TIMELINE", "TILE", name="cancelled_reason_enum")


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    BoardViewType.create(op.get_bind())
    op.add_column(
        "board_config",
        sa.Column("board_view_type", BoardViewType, nullable=False, server_default="TILE"),
    )
    op.add_column(
        "board_config", sa.Column("enable_video", sa.Boolean(), nullable=False, server_default="t")
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("board_config", "enable_video")
    op.drop_column("board_config", "board_view_type")
    BoardViewType.drop(op.get_bind())
    # ### end Alembic commands ###
