"""Create Observation Type Names table with per org_id names for observation types

Revision ID: eaa2b81ead8e
Revises: 2deb38c6d1bb
Create Date: 2025-03-27 11:09:37.882157

"""

import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = "eaa2b81ead8e"
down_revision = "2deb38c6d1bb"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "observation_type_names",
        sa.Column("type_id", sa.String(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("color", sa.String(), server_default="#C0997B", nullable=False),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.CheckConstraint("(color ~ '^#[0-9a-fA-F]{6}$')", name="ck_observationtype_color"),
        sa.ForeignKeyConstraint(
            ["org_id"],
            ["organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["type_id"],
            ["observation_types.id"],
        ),
        sa.PrimaryKeyConstraint("org_id", "type_id"),
        sa.UniqueConstraint("org_id", "type_id", "name", name="uq_observation_type"),
    )
    op.create_index(
        op.f("ix_observation_type_names_created_time"),
        "observation_type_names",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_observation_type_names_org_id"), "observation_type_names", ["org_id"], unique=False
    )
    op.create_index(
        op.f("ix_observation_type_names_updated_time"),
        "observation_type_names",
        ["updated_time"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_observation_type_names_updated_time"), table_name="observation_type_names"
    )
    op.drop_index(op.f("ix_observation_type_names_org_id"), table_name="observation_type_names")
    op.drop_index(
        op.f("ix_observation_type_names_created_time"), table_name="observation_type_names"
    )
    op.drop_table("observation_type_names")
    # ### end Alembic commands ###
