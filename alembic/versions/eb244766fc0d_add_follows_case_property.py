"""add follows case property

Revision ID: eb244766fc0d
Revises: 7295dc2a7329
Create Date: 2023-04-24 11:21:18.804638

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "eb244766fc0d"
down_revision = "7295dc2a7329"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "case_derived_properties", sa.Column("preceding_case_id", sa.String(), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("case_derived_properties", "preceding_case_id")
    # ### end Alembic commands ###
