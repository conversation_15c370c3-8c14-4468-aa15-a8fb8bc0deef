"""Add task type reviewers

Revision ID: eb474158ea8c
Revises: 35d8b0f83047
Create Date: 2023-08-08 09:21:34.012992

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "eb474158ea8c"
down_revision = "35d8b0f83047"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Drop indexes
    op.drop_index(
        "ix_annotation_task_type_users_annotation_task_type_id",
        table_name="annotation_task_type_users",
    )
    op.drop_index(
        "ix_annotation_task_type_users_created_time", table_name="annotation_task_type_users"
    )
    op.drop_index(
        "ix_annotation_task_type_users_updated_time", table_name="annotation_task_type_users"
    )
    op.drop_index("ix_annotation_task_type_users_user_id", table_name="annotation_task_type_users")

    # Rename annotation_task_type_users to annotation_task_type_annotators
    op.rename_table("annotation_task_type_users", "annotation_task_type_annotators")

    # Recreate indexes
    op.create_index(
        op.f("ix_annotation_task_type_annotators_annotation_task_type_id"),
        "annotation_task_type_annotators",
        ["annotation_task_type_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_task_type_annotators_created_time"),
        "annotation_task_type_annotators",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_task_type_annotators_updated_time"),
        "annotation_task_type_annotators",
        ["updated_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_task_type_annotators_user_id"),
        "annotation_task_type_annotators",
        ["user_id"],
        unique=False,
    )

    # Add reviewers table
    op.create_table(
        "annotation_task_type_reviewers",
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("annotation_task_type_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("user_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["annotation_task_type_id"],
            ["annotation_task_types.id"],
        ),
        sa.PrimaryKeyConstraint("annotation_task_type_id", "user_id"),
    )
    op.create_index(
        op.f("ix_annotation_task_type_reviewers_annotation_task_type_id"),
        "annotation_task_type_reviewers",
        ["annotation_task_type_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_task_type_reviewers_created_time"),
        "annotation_task_type_reviewers",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_task_type_reviewers_updated_time"),
        "annotation_task_type_reviewers",
        ["updated_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_task_type_reviewers_user_id"),
        "annotation_task_type_reviewers",
        ["user_id"],
        unique=False,
    )

    # Add all annotators to reviewers
    op.execute(
        "INSERT INTO annotation_task_type_reviewers (annotation_task_type_id, user_id) SELECT annotation_task_type_id, user_id FROM annotation_task_type_annotators"
    )


def downgrade() -> None:
    # Drop annotation_task_type_annotators indexes
    op.drop_index(
        op.f("ix_annotation_task_type_annotators_user_id"),
        table_name="annotation_task_type_annotators",
    )
    op.drop_index(
        op.f("ix_annotation_task_type_annotators_updated_time"),
        table_name="annotation_task_type_annotators",
    )
    op.drop_index(
        op.f("ix_annotation_task_type_annotators_created_time"),
        table_name="annotation_task_type_annotators",
    )
    op.drop_index(
        op.f("ix_annotation_task_type_annotators_annotation_task_type_id"),
        table_name="annotation_task_type_annotators",
    )

    # Rename table from annotation_task_type_annotators back to annotation_task_type_users
    op.rename_table("annotation_task_type_annotators", "annotation_task_type_users")

    # Recreate indexes
    op.create_index(
        "ix_annotation_task_type_users_user_id",
        "annotation_task_type_users",
        ["user_id"],
        unique=False,
    )
    op.create_index(
        "ix_annotation_task_type_users_updated_time",
        "annotation_task_type_users",
        ["updated_time"],
        unique=False,
    )
    op.create_index(
        "ix_annotation_task_type_users_created_time",
        "annotation_task_type_users",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        "ix_annotation_task_type_users_annotation_task_type_id",
        "annotation_task_type_users",
        ["annotation_task_type_id"],
        unique=False,
    )

    # Drop reviewers table
    op.drop_index(
        op.f("ix_annotation_task_type_reviewers_user_id"),
        table_name="annotation_task_type_reviewers",
    )
    op.drop_index(
        op.f("ix_annotation_task_type_reviewers_updated_time"),
        table_name="annotation_task_type_reviewers",
    )
    op.drop_index(
        op.f("ix_annotation_task_type_reviewers_created_time"),
        table_name="annotation_task_type_reviewers",
    )
    op.drop_index(
        op.f("ix_annotation_task_type_reviewers_annotation_task_type_id"),
        table_name="annotation_task_type_reviewers",
    )
    op.drop_table("annotation_task_type_reviewers")
