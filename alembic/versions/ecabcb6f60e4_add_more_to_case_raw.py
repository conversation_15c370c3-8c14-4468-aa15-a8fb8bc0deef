"""Add more to case raw

Revision ID: ecabcb6f60e4
Revises: c2674207b0f5
Create Date: 2021-10-18 12:41:39.991199

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "ecabcb6f60e4"
down_revision = "c2674207b0f5"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("case_raw", sa.Column("event_time", sa.DateTime(timezone=True), nullable=True))
    op.add_column("case_raw", sa.Column("event_type", sa.String(), nullable=True))
    op.add_column("case_raw", sa.Column("org_id", sa.String(), nullable=True))
    op.alter_column("case_raw", "case_id", existing_type=sa.VARCHAR(), nullable=True)
    op.create_index(op.f("ix_case_raw_event_time"), "case_raw", ["event_time"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_case_raw_event_time"), table_name="case_raw")
    op.alter_column("case_raw", "case_id", existing_type=sa.VARCHAR(), nullable=False)
    op.drop_column("case_raw", "org_id")
    op.drop_column("case_raw", "event_type")
    op.drop_column("case_raw", "event_time")
