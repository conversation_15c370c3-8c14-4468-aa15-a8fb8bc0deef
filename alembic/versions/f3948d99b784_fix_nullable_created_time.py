"""Make created time non-null

Revision ID: f3948d99b784
Revises: 6728ee9ffc1b
Create Date: 2021-04-02 13:26:57.498129

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "f3948d99b784"
down_revision = "6728ee9ffc1b"
branch_labels = None
depends_on = None


def upgrade():
    # This should have been non-nullable before, but due to weird ordering of how
    # things were interpreted, it wasn't.
    op.alter_column(
        "media_assets",
        "created_time",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        nullable=False,
        existing_server_default=sa.text("now()"),
    )


def downgrade():
    op.alter_column(
        "media_assets",
        "created_time",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        nullable=True,
        existing_server_default=sa.text("now()"),
    )
