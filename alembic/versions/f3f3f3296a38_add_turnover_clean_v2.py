"""Add TURNOVER_CLEAN_V2 phase to phase types

Revision ID: f3f3f3296a38
Revises: 6a4ecc9e9fc9
Create Date: 2024-12-16 13:38:45.915188

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "f3f3f3296a38"
down_revision = "6a4ecc9e9fc9"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
        INSERT INTO phase_types (id, title, slug, description)
        VALUES (
            'TURNOVER_CLEAN_V2',
            'Turnover Clean V2',
            'turnover-clean-v2',
            'A cleaning phase is measured as the period between when a patient wheels out and the mop leaves the room.'
        )
    """)


def downgrade():
    op.execute("""DELETE FROM phase_types WHERE id = 'TURNOVER_CLEAN_V2'""")
