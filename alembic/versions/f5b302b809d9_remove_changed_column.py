"""Remove changed column


Revision ID: f5b302b809d9
Revises: b55ee9aa6f95
Create Date: 2022-10-04 09:40:31.294141

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "f5b302b809d9"
down_revision = "b55ee9aa6f95"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("events_history", "changed")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "events_history",
        sa.Column("changed", postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    )
    # ### end Alembic commands ###
