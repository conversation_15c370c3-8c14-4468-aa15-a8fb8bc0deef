"""Add new draping event type

Revision ID: f5ee334a3168
Revises: fea475f03858
Create Date: 2023-02-06 12:35:08.229609

"""

import sqlalchemy as sa
from sqlalchemy.sql import column, table

from alembic import op

# revision identifiers, used by Alembic.
revision = "f5ee334a3168"
down_revision = "fea475f03858"
branch_labels = None
depends_on = None

eventType = table(
    "event_types",
    column("id", sa.String(32)),
    column("name", sa.String(64)),
    column("type", sa.String()),
    column("color", sa.String(16)),
    column("hidden", sa.<PERSON>()),
)


def upgrade():
    op.execute(
        eventType.update()
        .where(eventType.c.id == "patient_draped")
        .values(name="Patient draped (Procedural)")
    )
    op.execute(
        eventType.update()
        .where(eventType.c.id == "patient_undraped")
        .values(name="Patient undraped (Procedural)")
    )
    # Magenta - 30
    op.execute(
        eventType.insert().values(
            id="anesthesia_draping",
            name="Anesthesia draping",
            type="patient_status",
            color="#f89dca",
            hidden=False,
        )
    )
    # Magenta - 60
    op.execute(
        eventType.insert().values(
            id="anesthesia_undraping",
            type="patient_status",
            name="Anesthesia undraping",
            color="#c34a86",
            hidden=False,
        )
    )
    pass


def downgrade():
    op.execute(
        eventType.update().where(eventType.c.id == "patient_draped").values(name="Patient draped")
    )
    op.execute(
        eventType.update()
        .where(eventType.c.id == "patient_undraped")
        .values(name="Patient undraped")
    )
    op.execute(eventType.delete().where(eventType.c.id == "anesthesia_draping"))
    op.execute(eventType.delete().where(eventType.c.id == "anesthesia_undraping"))
    pass
