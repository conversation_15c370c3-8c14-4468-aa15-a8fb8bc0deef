"""Recategorize event types

Revision ID: f8eb504dbe27
Revises: 1ce5d9dca1bf
Create Date: 2022-09-13 12:47:33.929788

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "f8eb504dbe27"
down_revision = "1ce5d9dca1bf"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    # Recategorize event types
    op.execute(
        "UPDATE event_types SET type = 'backtable_status' WHERE id = 'back_table_unprepared'"
    )
    op.execute(
        "UPDATE event_types SET type = 'backtable_status' WHERE id = 'sterile_pack_on_back_table'"
    )

    op.execute("UPDATE event_types SET type = 'brightness' WHERE id = 'lights_on'")
    op.execute("UPDATE event_types SET type = 'brightness' WHERE id = 'lights_off'")

    op.execute("UPDATE event_types SET type = 'casecart_status' WHERE id = 'no_case_cart'")

    op.execute(
        "UPDATE event_types SET type = 'patient_status' WHERE id = 'patient_on_hospital_bed'"
    )
    op.execute("UPDATE event_types SET type = 'patient_status' WHERE id = 'patient_on_or_table'")

    # Backfill old events with stale event_name
    op.execute(
        "UPDATE events SET event_type_id = 'back_table_unprepared' WHERE event_name = 'BackTableStatus.BACK_TABLE_UNPREPARED'"
    )
    op.execute(
        "UPDATE events SET event_type_id = 'sterile_pack_on_back_table' WHERE event_name = 'BackTableStatus.STERILE_PACKAGE_ON_BACK_TABLE'"
    )
    op.execute(
        "UPDATE events SET event_type_id = 'no_case_cart' WHERE event_name = 'CaseCartStatus.NO_CASE_CART'"
    )
    op.execute(
        "UPDATE events SET event_type_id = 'no_patient' WHERE event_name = 'PatientStatus.NO_PATIENT'"
    )
    op.execute(
        "UPDATE events SET event_type_id = 'patient_draped' WHERE event_name = 'PatientStatus.PATIENT_DRAPED'"
    )
    op.execute(
        "UPDATE events SET event_type_id = 'patient_on_hospital_bed' WHERE event_name = 'PatientStatus.PATIENT_ON_HOSPITAL_BED'"
    )
    op.execute(
        "UPDATE events SET event_type_id = 'patient_on_or_table' WHERE event_name = 'PatientStatus.PATIENT_ON_OR_TABLE'"
    )

    # Backfill old events prior to event_type_id intro
    op.execute(
        "UPDATE events SET event_type_id = event_name WHERE event_type_id IS NULL AND event_name IN (SELECT id FROM event_types)"
    )

    op.alter_column("events", "event_type", existing_type=sa.VARCHAR(), nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("events", "event_type", existing_type=sa.VARCHAR(), nullable=False)
    # ### end Alembic commands ###
