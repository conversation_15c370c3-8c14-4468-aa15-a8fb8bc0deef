"""Make end_time nullable

Revision ID: fa7b0385eae3
Revises: c8858cb44511
Create Date: 2021-03-22 17:14:03.599030

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "fa7b0385eae3"
down_revision = "c8858cb44511"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column("events", "end_time", nullable=True)
    pass


def downgrade():
    op.alter_column("events", "end_time", nullable=False)
    pass
