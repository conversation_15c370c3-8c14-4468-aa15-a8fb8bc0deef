"""Re-adding unique index for block schedule rows

Revision ID: fbd21e182e9b
Revises: 33a431d29e03
Create Date: 2025-05-12 18:11:28.972571

"""

from alembic import op


# revision identifiers, used by Alembic.
revision = "fbd21e182e9b"
down_revision = "33a431d29e03"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_unique_block_schedule_file_row_number", table_name="block_schedule_file_rows")
    op.create_unique_constraint(
        "uq_block_schedule_file_row_number",
        "block_schedule_file_rows",
        ["block_schedule_file_id", "row_number"],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "uq_block_schedule_file_row_number", "block_schedule_file_rows", type_="unique"
    )
    op.create_index(
        "ix_unique_block_schedule_file_row_number",
        "block_schedule_file_rows",
        ["block_schedule_file_id", "row_number"],
        unique=True,
    )
    # ### end Alembic commands ###
