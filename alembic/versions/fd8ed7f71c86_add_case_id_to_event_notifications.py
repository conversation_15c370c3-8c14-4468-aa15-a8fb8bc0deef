"""Add case id to event notifications

Revision ID: fd8ed7f71c86
Revises: 917c76eb7a06
Create Date: 2023-08-21 13:04:12.411805

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "fd8ed7f71c86"
down_revision = "512092d3a91f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("staff_event_notification", sa.Column("case_id", sa.String(), nullable=True))
    op.create_index(
        op.f("ix_staff_event_notification_case_id"),
        "staff_event_notification",
        ["case_id"],
        unique=False,
    )
    op.create_foreign_key(
        "notification_case_id_fkey",
        "staff_event_notification",
        "cases",
        ["case_id"],
        ["case_id"],
        ondelete="SET NULL",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("notification_case_id_fkey", "staff_event_notification", type_="foreignkey")
    op.drop_index(
        op.f("ix_staff_event_notification_case_id"), table_name="staff_event_notification"
    )
    op.drop_column("staff_event_notification", "case_id")
    # ### end Alembic commands ###
