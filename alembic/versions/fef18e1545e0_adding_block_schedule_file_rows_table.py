"""Adding block schedule file rows table

Revision ID: fef18e1545e0
Revises: b9b6fe5d81a0
Create Date: 2025-04-30 14:04:04.254034

"""

import sqlalchemy as sa

from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "fef18e1545e0"
down_revision = "b9b6fe5d81a0"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "block_schedule_file_rows",
        sa.Column("id", sa.UUID(), server_default=sa.text("gen_random_uuid()"), nullable=False),
        sa.Column("block_schedule_file_id", sa.UUID(), nullable=False),
        sa.Column("org_id", sa.String(), nullable=False),
        sa.Column("site_id", sa.String(), nullable=True),
        sa.Column("room_id", sa.String(), nullable=True),
        sa.Column("room_name", sa.String(), nullable=False),
        sa.Column("timezone", sa.String(), nullable=True),
        sa.Column("block_date", sa.DateTime(timezone=True), nullable=True),
        sa.Column("block_name", sa.String(), nullable=True),
        sa.Column(
            "block_type",
            postgresql.ENUM(
                "UNBLOCKED_BLOCK_TYPE",
                "UNAVAILABLE_BLOCK_TYPE",
                "GROUP_BLOCK_TYPE",
                "SURGEON_BLOCK_TYPE",
                "SERVICE_BLOCK_TYPE",
                "ON_HOLD_BLOCK_TYPE",
                "UNKNOWN",
                name="blocktypes",
                create_type=False,
            ),
            nullable=True,
        ),
        sa.Column("block_start_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column("block_end_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column(
            "processing_status",
            postgresql.ENUM(
                "PENDING",
                "PROCESSED",
                "REJECTED",
                name="block_processing_statuses",
                create_type=False,
            ),
            nullable=False,
        ),
        sa.Column("rejected_reason", sa.String(), nullable=True),
        sa.Column(
            "created_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_time",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["block_schedule_file_id"], ["block_schedule_processed_files.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_block_schedule_file_rows_block_schedule_file_id"),
        "block_schedule_file_rows",
        ["block_schedule_file_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_schedule_file_rows_created_time"),
        "block_schedule_file_rows",
        ["created_time"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_schedule_file_rows_org_id"),
        "block_schedule_file_rows",
        ["org_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_schedule_file_rows_room_id"),
        "block_schedule_file_rows",
        ["room_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_schedule_file_rows_room_name"),
        "block_schedule_file_rows",
        ["room_name"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_schedule_file_rows_site_id"),
        "block_schedule_file_rows",
        ["site_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_block_schedule_file_rows_updated_time"),
        "block_schedule_file_rows",
        ["updated_time"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_block_schedule_file_rows_updated_time"), table_name="block_schedule_file_rows"
    )
    op.drop_index(
        op.f("ix_block_schedule_file_rows_site_id"), table_name="block_schedule_file_rows"
    )
    op.drop_index(
        op.f("ix_block_schedule_file_rows_room_name"), table_name="block_schedule_file_rows"
    )
    op.drop_index(
        op.f("ix_block_schedule_file_rows_room_id"), table_name="block_schedule_file_rows"
    )
    op.drop_index(op.f("ix_block_schedule_file_rows_org_id"), table_name="block_schedule_file_rows")
    op.drop_index(
        op.f("ix_block_schedule_file_rows_created_time"), table_name="block_schedule_file_rows"
    )
    op.drop_index(
        op.f("ix_block_schedule_file_rows_block_schedule_file_id"),
        table_name="block_schedule_file_rows",
    )
    op.drop_table("block_schedule_file_rows")
    # ### end Alembic commands ###
