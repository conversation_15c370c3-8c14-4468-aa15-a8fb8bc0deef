import json
import os
import time
from typing import Any, Callable, Dict, List, Mapping, Optional, TypeVar, Union
from urllib.error import HTTPError

import sgqlc.types
from graphql import GraphQLError
from requests import ConnectionError, Response
from sgqlc.endpoint.http import HTTPEndpoint
from sgqlc.operation import Operation
from opentelemetry.sdk.trace.id_generator import RandomIdGenerator
import random

from apella_cloud_api import configuration
from apella_cloud_api.api_server_schema import (
    Anesthesia,
    AnesthesiaConnection,
    AnesthesiaQueryInput,
    AnesthesiaUpsertInput,
    AnnotationTask,
    AnnotationTaskConnection,
    AnnotationTaskQueryInput,
    CameraCaptureLatestImage,
    CameraCaptureLatestImageInput,
    CaseEhrMessage,
    CaseEhrMessageConnection,
    CaseEhrMessageQueryInput,
    CaseHistoryQueryInput,
    CaseProceduresUpsert,
    CaseProceduresUpsertAndArchive,
    CaseProceduresUpsertAndArchiveInput,
    CaseProceduresUpsertInput,
    CaseStaffUpsert,
    CaseStaffUpsertAndArchive,
    CaseStaffUpsertAndArchiveInput,
    CaseStaffUpsertInput,
    ContactInformation,
    ContactInformationConnection,
    ContactInformationSearchInput,
    Event,
    EventConnection,
    EventDeleteInput,
    EventHistorySearchInput,
    EventSearchInput,
    EventUpsertInput,
    Mutation,
    Observation,
    ObservationConnection,
    ObservationSearchInput,
    ObservationsUpsert,
    ObservationUpsertInput,
    Phase,
    PhaseConnection,
    PhaseCreate,
    PhaseCreateInput,
    PhaseDelete,
    PhaseDeleteInput,
    PhaseQueryInput,
    PhaseRelationshipCreate,
    PhaseRelationshipCreateInput,
    PhaseRelationshipDelete,
    PhaseRelationshipDeleteInput,
    PhaseTypeConnection,
    PhaseTypeRecord,
    PhaseUpdate,
    PhaseUpdateInput,
    PhaseUpsert,
    PhaseUpsertInput,
    Procedure,
    ProcedureConnection,
    ProcedureQueryInput,
    ProcedureUpsertInput,
    ProcessCaseDerivedProperties,
    ProcessCaseDerivedPropertiesInput,
    Query,
    ScheduledCase,
    ScheduledCaseConnection,
    ScheduledCaseQueryInput,
    Staff,
    StaffCode,
    StaffCodeUpsertInput,
    StaffConnection,
    StaffEventNotificationContactInformation,
    StaffEventNotificationContactInformationConnection,
    StaffEventNotificationContactInformationSearchInput,
    StaffingNeedsRatioCreate,
    StaffingNeedsRatioCreateInput,
    StaffQueryInput,
    StaffRole,
    StaffUpsertInput,
)
from apella_cloud_api.auth.handlers import AuthHandler
from apella_cloud_api.auth.handlers.handlers import get_auth_handler
from apella_cloud_api.constants import UNSET_TOKEN
from apella_cloud_api.dtos import (
    BatchEventCreateRequestDto,
    BatchEventCreateResultDto,
    CameraInfoDto,
    CaseInfoDto,
    CaseRawDto,
    CaseRawResult,
    CaseSearchDto,
    CaseSearchResultDto,
    CaseUpdateDto,
    EventDto,
    EventQueryResultDto,
    HighlightAssetDto,
    RoomInfoDto,
    ScheduleDto,
    SiteInfoDto,
    UserDto,
)
from apella_cloud_api.exceptions import ServerError
from apella_cloud_api.new_api_server_schema import (
    ApellaGQLClient,
    gql__Mutation,
    gql__Query,
)
from apella_cloud_api.new_client_schema import GQLMutation, GQLQuery
from apella_cloud_api.new_schema_generator.schema_generator_base_classes import (
    ObjectField,
    results_to_dataclass,
)
from apella_cloud_api.apella_requests import RequestClient, Session, assert_no_error_status_codes


GET = "GET"
POST = "POST"
DELETE = "DELETE"
PUT = "PUT"
PATCH = "PATCH"


class Client(RequestClient):
    apella_gql_client: ApellaGQLClient
    server_url: str
    auth_handler: AuthHandler
    timeout_seconds: Optional[float]
    http_session: Session

    def __init__(
        self,
        server_url: Optional[str] = None,
        jwt: Optional[str] = None,
        auth_url: Optional[str] = None,
        auth_client_id: Optional[str] = None,
        auth_client_secret: Optional[str] = None,
        sa_keyfile: Optional[str] = None,
        sa_keyinfo: Optional[dict[str, str]] = None,
        permissions: Optional[List[str]] = None,
        use_service_account_identity: bool = False,
        environment: Optional[str] = None,
        no_auth: bool = False,
        timeout_seconds: Optional[float] = None,
        http_session: Optional[Session] = None,
        tracing_sampling_rate: Optional[float] = None,
    ):
        super().__init__(timeout_seconds=timeout_seconds, http_session=http_session)
        environment = os.getenv("APELLA_ENVIRONMENT", environment)
        if environment is not None:
            server_url = configuration.API_SERVER_URLS[environment]
            auth_url = configuration.AUTH0_URLS.get(environment)

        if server_url is None:
            raise Exception("Missing Apella Environment or server url")

        self.server_url = server_url
        self.graphql_url = f"{server_url}/v1/graphql"
        self.auth_handler = get_auth_handler(
            environment=environment,
            server_url=server_url,
            auth_url=auth_url,
            auth_client_id=auth_client_id,
            auth_client_secret=auth_client_secret,
            sa_keyfile=sa_keyfile,
            sa_keyinfo=sa_keyinfo,
            permissions=permissions,
            use_service_account_identity=use_service_account_identity,
            jwt=jwt,
            no_auth=no_auth,
        )

        self.apella_gql_client = ApellaGQLClient()
        self.tracing_sampling_rate = tracing_sampling_rate

    def headers(self, is_json: bool = True) -> Dict[str, str]:
        result = {}
        if is_json:
            result = {"content-type": "application/json"}
        # Add any auth headers
        self.auth_handler.add_auth_headers(headers=result)

        if self.tracing_sampling_rate and random.random() < self.tracing_sampling_rate:
            id_generator = RandomIdGenerator()
            result["x-b3-traceid"] = f"{id_generator.generate_trace_id()}"
            result["x-b3-spanid"] = f"{id_generator.generate_span_id()}"
            result["x-b3-sampled"] = "1"

        return result

    def create_http_endpoint(self, url: str, base_headers: Dict[str, str]) -> HTTPEndpoint:
        return HTTPEndpoint(url, base_headers=base_headers, timeout=self.timeout_seconds)

    def case_forecast_heartbeat(self, site_id: str, temporality: str) -> dict[str, bool]:
        req = self.make_request(
            POST,
            f"{self.server_url}/v1/case_forecast/heartbeat/{site_id}/{temporality}",
            headers=self.headers(),
        )
        assert_no_error_status_codes(req)
        return req.json()

    def get_case(self, case_id: str) -> CaseInfoDto:
        req = self.make_request_with_retry(
            GET, f"{self.server_url}/v1/case/{case_id}", headers=self.headers()
        )
        assert_no_error_status_codes(req)
        return CaseInfoDto.from_dict(req.json())

    def get_case_by_external_id(self, external_case_id: str, org_id: str) -> CaseInfoDto:
        req = self.make_request_with_retry(
            GET,
            f"{self.server_url}/v1/{org_id}/case/{external_case_id}",
            headers=self.headers(),
        )
        assert_no_error_status_codes(req)
        return CaseInfoDto.from_dict(req.json())

    def upsert_case(self, case: CaseUpdateDto) -> CaseInfoDto:
        req = self.make_request_with_retry(
            PUT,
            f"{self.server_url}/v1/case",
            headers=self.headers(),
            json=case.to_dict(),
        )
        assert_no_error_status_codes(req)
        return CaseInfoDto.from_dict(req.json())

    def search_cases(self, case_search: CaseSearchDto) -> CaseSearchResultDto:
        req = self.make_request(
            POST,
            f"{self.server_url}/v1/case/search",
            headers=self.headers(),
            json=case_search.to_dict(),
        )
        assert_no_error_status_codes(req)
        query_result = CaseSearchResultDto.from_dict(req.json())
        return query_result

    def create_raw_case_event(self, case_raw: CaseRawDto) -> CaseRawDto:
        req = self.make_request_with_retry(
            POST,
            f"{self.server_url}/v1/case_raw/{case_raw.external_message_id}",
            headers=self.headers(),
            json=case_raw.to_dict(),
        )
        assert_no_error_status_codes(req)
        return CaseRawDto.from_dict(req.json())

    def get_raw_case_events(self, external_case_id: str, org_id: str) -> CaseRawResult:
        req = self.make_request_with_retry(
            GET,
            f"{self.server_url}/v1/{org_id}/case/{external_case_id}/raw",
            headers=self.headers(),
        )
        assert_no_error_status_codes(req)
        return CaseRawResult.from_dict(req.json())

    def get_mapping(self, external_id_type: str, external_id: str) -> str:
        req = self.make_request_with_retry(
            GET,
            f"{self.server_url}/v1/mapping/{external_id_type}/{external_id}",
            headers=self.headers(),
        )
        assert_no_error_status_codes(req)
        return req.text

    def set_mapping(self, external_id_type: str, external_id: str, internal_id: str) -> None:
        req = self.make_request(
            PUT,
            f"{self.server_url}/v1/mapping/{external_id_type}/{external_id}",
            headers=self.headers(),
            data=internal_id,
        )
        assert_no_error_status_codes(req)

    def get_mapping_v2(self, external_id_type: str, external_id: str) -> str:
        req = self.make_request_with_retry(
            GET,
            f"{self.server_url}/v2/mapping/{external_id_type}/{external_id}",
            headers=self.headers(),
        )
        assert_no_error_status_codes(req)
        return req.text

    def get_all_site_ids(self) -> Any:
        req = self.make_request_with_retry(
            GET, f"{self.server_url}/v1/sites", headers=self.headers()
        )
        assert_no_error_status_codes(req)
        return req.json()

    def get_user(self, user_id: str) -> UserDto:
        req = self.make_request_with_retry(
            GET, f"{self.server_url}/v1/user/{user_id}", headers=self.headers()
        )
        assert_no_error_status_codes(req)
        return UserDto.from_dict(req.json())

    def get_user_schedule_today(self, user_id: str) -> ScheduleDto:
        req = self.make_request_with_retry(
            GET,
            f"{self.server_url}/v1/user/{user_id}/schedule/today",
            headers=self.headers(),
        )
        assert_no_error_status_codes(req)
        return ScheduleDto.from_dict(req.json())

    def sync_user(self, email: str) -> None:
        req = self.make_request(
            POST,
            f"{self.server_url}/v1/user/sync",
            headers=self.headers(),
            json={"email": email},
        )
        assert_no_error_status_codes(req)

    def add_roles_to_user(self, user_id: str, roles: Any) -> None:
        req = self.make_request(
            POST,
            f"{self.server_url}/v1/user/{user_id}/roles",
            headers=self.headers(),
            json={"roles": roles},
        )
        assert_no_error_status_codes(req)

    def get_room_info(self, room_id: str) -> RoomInfoDto:
        req = self.make_request_with_retry(
            GET, f"{self.server_url}/v1/room/{room_id}", headers=self.headers()
        )
        assert_no_error_status_codes(req)
        return RoomInfoDto.from_dict(req.json())

    def get_site_info(self, site_id: str) -> SiteInfoDto:
        req = self.make_request_with_retry(
            GET, f"{self.server_url}/v1/site/{site_id}", headers=self.headers()
        )
        assert_no_error_status_codes(req)
        return SiteInfoDto.from_dict(req.json())

    def get_camera_info(self, camera_id: str) -> CameraInfoDto:
        req = self.make_request_with_retry(
            GET, f"{self.server_url}/v1/camera/{camera_id}", headers=self.headers()
        )
        assert_no_error_status_codes(req)
        return CameraInfoDto.from_dict(req.json())

    def get_version(self) -> str:
        req = self.make_request_with_retry(GET, f"{self.server_url}/v1/version")
        assert_no_error_status_codes(req)
        return req.text

    def get_virtual_hls_playlist(
        self, camera_id: str, start_time: Union[int, str], end_time: Union[int, str]
    ) -> str:
        req = self.make_request_with_retry(
            GET,
            f"{self.server_url}/v1/media_playlist/{camera_id}/{start_time}/{end_time}.m3u8",
            headers=self.headers(),
        )
        assert_no_error_status_codes(req)
        return req.text

    def get_virtual_hls_playlist_segment(
        self,
        camera_id: str,
        start_time: Union[int, str],
        playlist_name: str,
        segment_file: str,
    ) -> Response:
        req = self.make_request_with_retry(
            GET,
            (
                f"{self.server_url}/v1/media_playlist/{camera_id}/{start_time}"
                f"/{playlist_name}/{segment_file}.ts"
            ),
            headers=self.headers(),
        )
        assert_no_error_status_codes(req)
        return req

    def create_event(self, event: EventDto, live: bool = False) -> EventDto:
        params = dict()
        if live:
            params["live"] = "true"

        req = self.make_request(
            POST,
            f"{self.server_url}/v1/events",
            headers=self.headers(),
            params=params,
            json=event.to_dict(),
        )
        assert_no_error_status_codes(req)
        return EventDto.from_dict(req.json())

    def create_events(
        self, events: BatchEventCreateRequestDto, publish_changelog: bool = True
    ) -> BatchEventCreateResultDto:
        params = dict()
        if publish_changelog:
            params["publish_changelog"] = "true"
        else:
            params["publish_changelog"] = "false"

        req = self.make_request(
            POST,
            f"{self.server_url}/v1/batch/events",
            headers=self.headers(),
            json=events.to_dict(),
            params=params,
        )
        assert_no_error_status_codes(req)
        return BatchEventCreateResultDto.from_dict(req.json())

    def get_event(self, event_id: str) -> EventDto:
        req = self.make_request_with_retry(
            GET, f"{self.server_url}/v1/events/{event_id}", headers=self.headers()
        )
        assert_no_error_status_codes(req)
        return EventDto.from_dict(req.json())

    def replace_event(self, event: EventDto) -> EventDto:
        req = self.make_request(
            PUT,
            f"{self.server_url}/v1/events/{event.event_id}",
            headers=self.headers(),
            json=event.to_dict(),
        )
        assert_no_error_status_codes(req)
        return EventDto.from_dict(req.json())

    def delete_event(self, event_delete_input: EventDeleteInput) -> None:
        # Setup
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        # Generate the graphql query
        operation.event_delete(input=event_delete_input)
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

    def query_events(self, **kwargs: Any) -> EventQueryResultDto:
        req = self.make_request_with_retry(
            GET,
            f"{self.server_url}/v1/events/query",
            headers=self.headers(),
            params=kwargs,
        )
        assert_no_error_status_codes(req)
        query_result = EventQueryResultDto.from_dict(req.json())
        return query_result

    def search_events(self, query: EventSearchInput) -> List[Event]:
        # Setup
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Query)

        # Generate the graphql query
        events: EventConnection = operation.event_search(query=query)
        events.edges.node.id()
        events.edges.node.type()
        events.edges.node.name()
        events.edges.node.start_time()
        events.edges.node.labels()
        events.edges.node.notes()
        events.edges.node.source()
        events.edges.node.source_type()
        events.edges.node.model_version()
        events.edges.node.confidence()
        events.edges.node.camera_id()
        events.edges.node.site.id()
        events.edges.node.room.id()
        events.edges.node.organization.id()
        events.edges.node.version()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        # Parse the response to a python type
        return [edge.node for edge in (operation + data).event_search.edges]

    def search_event_history(self, query: EventHistorySearchInput) -> List[Event]:
        # Setup
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Query)

        # Generate the graphql query
        events: EventConnection = operation.event_history_search(query=query)
        events.edges.node.id()
        events.edges.node.type()
        events.edges.node.name()
        events.edges.node.start_time()
        events.edges.node.labels()
        events.edges.node.notes()
        events.edges.node.source()
        events.edges.node.source_type()
        events.edges.node.model_version()
        events.edges.node.camera_id()
        events.edges.node.site.id()
        events.edges.node.room.id()
        events.edges.node.organization.id()
        events.edges.node.version()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        # Parse the response to a python type
        return [edge.node for edge in (operation + data).event_history_search.edges]

    def upsert_event(self, events: List[EventUpsertInput]) -> List[Event]:
        # Setup
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        # Generate the graphql query
        operation.event_upsert(input=events)
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)
        result = (operation + data).event_upsert
        # Parse the response to a python type
        return result.created_events

    def get_annotation_task(self, annotation_task_id: str) -> AnnotationTask:
        # Setup
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Query)

        # Generate the graphql query
        tasks: AnnotationTaskConnection = operation.annotation_task(id=annotation_task_id)
        tasks.id()
        tasks.start_time()
        tasks.end_time()
        tasks.updated_time()
        tasks.status()
        tasks.annotator_user_id()
        tasks.reviewer_user_id()
        tasks.type_id()
        tasks.org_id()
        tasks.site_id()
        tasks.room_id()
        tasks.events()
        tasks.context_events()

        data = endpoint(operation.__to_graphql__(auto_select_depth=1))
        self.assert_successful_graphql_request(data)

        # Parse the response to a python type
        return (operation + data).annotation_task

    def query_annotation_tasks(self, query: AnnotationTaskQueryInput) -> List[AnnotationTask]:
        # Setup
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Query)

        # Generate the graphql query
        tasks: AnnotationTaskConnection = operation.annotation_tasks(query=query)
        tasks.edges.node.id()
        tasks.edges.node.org_id()
        tasks.edges.node.site_id()
        tasks.edges.node.room_id()
        tasks.edges.node.start_time()
        tasks.edges.node.end_time()
        tasks.edges.node.updated_time()
        tasks.edges.node.status()
        tasks.edges.node.annotator_user_id()
        tasks.edges.node.reviewer_user_id()
        tasks.edges.node.type_id()
        data = endpoint(operation.__to_graphql__(auto_select_depth=1))
        self.assert_successful_graphql_request(data)

        # Parse the response to a python type
        return [task.node for task in (operation + data).annotation_tasks.edges]

    def query_phases(self, query: PhaseQueryInput) -> List[Phase]:
        # Setup
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Query)

        # Generate the graphql query
        phases: PhaseConnection = operation.phases(query=query)
        phases.edges.node.id()
        phases.edges.node.site.id()
        phases.edges.node.room.id()
        phases.edges.node.organization.id()
        phases.edges.node.child_phases.id()
        phases.edges.node.parent_phases.id()
        phases.edges.node.type_id()
        phases.edges.node.source_type()
        phases.edges.node.case.id()
        phases.edges.node.start_event()
        phases.edges.node.start_time()
        phases.edges.node.end_event()
        phases.edges.node.end_time()
        phases.edges.node.duration()
        phases.edges.node.status()
        phases.edges.node.invalidation_reason()
        phases.edges.node.updated_time()
        phases.edges.node.created_time()
        data = endpoint(operation.__to_graphql__(auto_select_depth=1))
        self.assert_successful_graphql_request(data)

        # Parse the response to a python type
        result = [phase.node for phase in (operation + data).phases.edges]

        for phase in result:
            self.fix_nullabe_fields_in_phase(phase)

        return result

    def query_phase_types(self, ids: List[str]) -> List[PhaseTypeRecord]:
        # Setup
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Query)

        # Generate the graphql query
        phaseTypes: PhaseTypeConnection = operation.phase_types(ids=ids)
        phaseTypes.edges.node()

        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        # Parse the response to a python type
        result = [phaseType.node for phaseType in (operation + data).phase_types.edges]

        return result

    def delete_phase(self, input: PhaseDeleteInput) -> Any:
        # Setup
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        # Generate the graphql mutation
        response: PhaseDelete = operation.phase_delete(input=input)
        response.success()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        return (operation + data).phase_delete

    def fix_nullable_fields_in_phase_inputs(self, input: sgqlc.types.Input) -> None:
        # Graphene v2 has a bug where `null` cannot be passed in directly in a mutation input.
        # It can only be passed in as a variable. Since sgqlc doesn't allow passing in input as
        # variables block.  We add this hack here.
        if "case_id" in input and input.case_id is None:
            input.case_id = UNSET_TOKEN
        if "invalidation_reason" in input and input.invalidation_reason is None:
            input.invalidation_reason = UNSET_TOKEN
        if "end_event_id" in input and input.end_event_id is None:
            input.end_event_id = UNSET_TOKEN

    def fix_nullabe_fields_in_phase(self, phase: Phase) -> None:
        # Unfortunately sgqlc is just a bad library.
        # It doesn't cast null to None for Field objects, only Scalar objects.
        # I'm afraid we're going to need to write custom logic to convert any
        # empty Event() to None ourselves :(
        if len(phase.end_event.__dict__["__json_data__"]) == 0:
            phase.end_event = None

    def update_phase(self, input: PhaseUpdateInput) -> Any:
        # Setup
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        self.fix_nullable_fields_in_phase_inputs(input)

        # Generate the graphql mutation
        response: PhaseUpdate = operation.phase_update(input=input)
        response.success()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        return (operation + data).phase_update

    def upsert_phases(self, input: List[PhaseUpsertInput]) -> List[Phase]:
        # Setup
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        for upsert in input:
            self.fix_nullable_fields_in_phase_inputs(upsert)

        # Generate the graphql mutation
        response: PhaseUpsert = operation.phase_upsert(input=input)
        response.success()
        response.created_phases.id()
        response.created_phases.type_id()
        response.created_phases.phase_detail()
        response.created_phases.source_type()
        response.created_phases.has_video_available()
        response.created_phases.start_time()
        response.created_phases.end_time()
        response.created_phases.created_time()
        response.created_phases.updated_time()
        response.created_phases.status()
        response.created_phases.invalidation_reason()
        response.created_phases.time_range_verified()
        response.created_phases.start_event.id()
        response.created_phases.end_event.id()
        response.created_phases.duration()
        response.created_phases.case.id()
        response.created_phases.case.scheduled_start_time()
        response.created_phases.case.scheduled_end_time()
        response.created_phases.case.updated_time()
        response.created_phases.case.created_time()
        response.created_phases.case.case_classification_type()
        response.created_phases.case.status()
        response.created_phases.case.is_in_flip_room()
        response.created_phases.case.is_add_on()
        response.created_phases.case.patient_class()
        data = endpoint(operation.__to_graphql__(auto_select_depth=1))
        self.assert_successful_graphql_request(data)

        result = (operation + data).phase_upsert
        return result.created_phases

    def create_phase(self, input: PhaseCreateInput) -> Any:
        # Setup
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        self.fix_nullable_fields_in_phase_inputs(input)

        # Generate the graphql mutation
        response: PhaseCreate = operation.phase_create(input=input)
        response.success()
        response.created_phase.id()
        response.created_phase.type_id()
        response.created_phase.phase_detail()
        response.created_phase.source_type()
        response.created_phase.has_video_available()
        response.created_phase.start_time()
        response.created_phase.end_time()
        response.created_phase.created_time()
        response.created_phase.updated_time()
        response.created_phase.status()
        response.created_phase.invalidation_reason()
        response.created_phase.time_range_verified()
        response.created_phase.start_event.id()
        response.created_phase.end_event.id()
        response.created_phase.duration()
        response.created_phase.case.id()
        response.created_phase.case.scheduled_start_time()
        response.created_phase.case.scheduled_end_time()
        response.created_phase.case.updated_time()
        response.created_phase.case.created_time()
        response.created_phase.case.case_classification_type()
        response.created_phase.case.status()
        response.created_phase.case.is_in_flip_room()
        response.created_phase.case.is_add_on()
        response.created_phase.case.patient_class()
        data = endpoint(operation.__to_graphql__(auto_select_depth=1))
        self.assert_successful_graphql_request(data)

        return (operation + data).phase_create

    def bulk_update_phases(
        self,
        phases_to_create: Optional[List[PhaseCreateInput]] = None,
        phases_to_update: Optional[List[PhaseUpdateInput]] = None,
        phases_to_delete: Optional[List[PhaseDeleteInput]] = None,
        phase_relationships_to_delete: Optional[List[PhaseRelationshipDeleteInput]] = None,
        phase_relationships_to_create: Optional[List[PhaseRelationshipCreateInput]] = None,
    ) -> None:
        # Setup
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        # Instead of None checking below, it is easier to fill in empty lists here
        phases_to_create = phases_to_create or list()
        phases_to_update = phases_to_update or list()
        phases_to_delete = phases_to_delete or list()
        phase_relationships_to_delete = phase_relationships_to_delete or list()
        phase_relationships_to_create = phase_relationships_to_create or list()

        for phase_to_create in phases_to_create:
            self.fix_nullable_fields_in_phase_inputs(phase_to_create)
        for phase_to_update in phases_to_update:
            self.fix_nullable_fields_in_phase_inputs(phase_to_update)

        # Generate the graphql mutation
        for idx, phase_to_create in enumerate(phases_to_create):
            create_response: PhaseCreate = operation.phase_create(
                input=phase_to_create, __alias__=f"create_{idx}"
            )
            create_response.success()
        for idx, phase_to_update in enumerate(phases_to_update):
            update_response: PhaseUpdate = operation.phase_update(
                input=phase_to_update, __alias__=f"update_{idx}"
            )
            update_response.success()
        for idx, phase_to_delete in enumerate(phases_to_delete):
            delete_response: PhaseDelete = operation.phase_delete(
                input=phase_to_delete, __alias__=f"delete_{idx}"
            )
            delete_response.success()
        for idx, phase_relationship_to_create in enumerate(phase_relationships_to_create):
            create_relationship_response: PhaseRelationshipCreate = (
                operation.phase_relationship_create(
                    input=phase_relationship_to_create,
                    __alias__=f"create_relationship_{idx}",
                )
            )
            create_relationship_response.success()
        for idx, phase_relationship_to_delete in enumerate(phase_relationships_to_delete):
            delete_relationship_response: PhaseRelationshipDelete = (
                operation.phase_relationship_delete(
                    input=phase_relationship_to_delete,
                    __alias__=f"delete_relationship_{idx}",
                )
            )
            delete_relationship_response.success()

        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

    def create_phase_relationship(self, input: PhaseRelationshipCreateInput) -> Any:
        # Setup
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        # Generate the graphql mutation
        response: PhaseRelationshipCreate = operation.phase_relationship_create(input=input)
        response.success()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        return (operation + data).phase_relationship_create

    def delete_phase_relationship(self, input: PhaseRelationshipDeleteInput) -> Any:
        # Setup
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        # Generate the graphql mutation
        response: PhaseRelationshipDelete = operation.phase_relationship_delete(input=input)
        response.success()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        return (operation + data).phase_relationship_delete

    def query_cases(
        self,
        query: ScheduledCaseQueryInput,
        only_primary_surgeons: bool = False,
        procedure_hierarchy: Optional[int] = None,
    ) -> List[ScheduledCase]:
        # Setup
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Query)

        # Generate the graphql query
        cases: ScheduledCaseConnection = operation.cases(query=query)
        cases.edges.node.id()
        cases.edges.node.scheduled_start_time()
        cases.edges.node.scheduled_end_time()
        cases.edges.node.site.id()
        cases.edges.node.room.id()
        cases.edges.node.status()
        cases.edges.node.patient_class()
        cases.edges.node.is_in_flip_room()
        cases.edges.node.preceding_case.id()
        cases.edges.node.preceding_case.scheduled_start_time()
        cases.edges.node.is_first_case()
        cases.edges.node.updated_time()
        cases.edges.node.created_time()
        cases.edges.node.case_staff(only_primary_surgeons=only_primary_surgeons)
        cases.edges.node.staff(only_primary_surgeons=only_primary_surgeons).edges()
        cases.edges.node.patient()
        cases.edges.node.case_classification_type.id()
        if procedure_hierarchy:
            cases.edges.node.procedures(hierarchy=procedure_hierarchy).edges()
        else:
            cases.edges.node.procedures().edges()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        # Parse the response to a python type
        return [edge.node for edge in (operation + data).cases.edges]

    # TODO RT-260: deprecate in favor of `cases` endpoint
    def query_scheduled_cases(
        self,
        query: ScheduledCaseQueryInput,
        only_primary_surgeons: bool = False,
        hierarchy: Optional[int] = None,
    ) -> List[ScheduledCase]:
        # Setup
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Query)

        # Generate the graphql query
        scheduled_cases: ScheduledCaseConnection = operation.scheduled_cases(query=query)
        scheduled_cases.edges.node.id()
        scheduled_cases.edges.node.scheduled_start_time()
        scheduled_cases.edges.node.scheduled_end_time()
        scheduled_cases.edges.node.site.id()
        scheduled_cases.edges.node.room.id()
        scheduled_cases.edges.node.status()
        scheduled_cases.edges.node.patient_class()
        scheduled_cases.edges.node.is_add_on()
        scheduled_cases.edges.node.is_in_flip_room()
        scheduled_cases.edges.node.preceding_case.id()
        scheduled_cases.edges.node.preceding_case.scheduled_start_time()
        scheduled_cases.edges.node.is_first_case()
        scheduled_cases.edges.node.staff(only_primary_surgeons=only_primary_surgeons).edges()
        scheduled_cases.edges.node.case_staff(only_primary_surgeons=only_primary_surgeons)
        scheduled_cases.edges.node.patient()
        scheduled_cases.edges.node.case_classification_type.id()
        if hierarchy:
            scheduled_cases.edges.node.procedures(hierarchy=hierarchy).edges()
        else:
            scheduled_cases.edges.node.procedures().edges()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        # Parse the response to a python type
        return [edge.node for edge in (operation + data).scheduled_cases.edges]

    def query_cases_history(
        self,
        query: CaseHistoryQueryInput,
        only_primary_surgeons: bool = False,
        hierarchy: Optional[int] = None,
        **kwargs: dict[Any, Any],
    ) -> List[ScheduledCase]:
        # Setup
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Query)

        # Generate the graphql query
        scheduled_cases_history: ScheduledCaseConnection = operation.cases_history(
            query=query, **kwargs
        )
        scheduled_cases_history.edges.node.id()
        scheduled_cases_history.edges.node.scheduled_start_time()
        scheduled_cases_history.edges.node.scheduled_end_time()
        scheduled_cases_history.edges.node.site.id()
        scheduled_cases_history.edges.node.room.id()
        scheduled_cases_history.edges.node.status()
        scheduled_cases_history.edges.node.patient_class()
        scheduled_cases_history.edges.node.is_add_on()
        scheduled_cases_history.edges.node.is_in_flip_room()
        scheduled_cases_history.edges.node.staff(
            only_primary_surgeons=only_primary_surgeons
        ).edges()
        scheduled_cases_history.edges.node.case_staff(only_primary_surgeons=only_primary_surgeons)
        scheduled_cases_history.edges.node.case_classification_type.id()
        scheduled_cases_history.edges.node.version()
        if hierarchy:
            scheduled_cases_history.edges.node.procedures(hierarchy=hierarchy).edges()
        else:
            scheduled_cases_history.edges.node.procedures().edges()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        # Parse the response to a python type
        return [edge.node for edge in (operation + data).cases_history.edges]

    def create_highlight_asset(self, highlight_asset_dto: HighlightAssetDto) -> HighlightAssetDto:
        req = self.make_request(
            POST,
            f"{self.server_url}/v1/highlight/asset",
            headers=self.headers(),
            json=highlight_asset_dto.to_dict(),
        )
        assert_no_error_status_codes(req)
        return HighlightAssetDto.from_dict(req.json())

    def set_latest_image_for_camera(self, input: CameraCaptureLatestImageInput) -> Any:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        response: CameraCaptureLatestImage = operation.camera_capture_latest_image(input=input)
        response.success()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        return (operation + data).camera_capture_latest_image

    def query_case_ehr_messages(
        self, case_ehr_messages_input: CaseEhrMessageQueryInput
    ) -> List[CaseEhrMessage]:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Query)

        ehr_message_connection: CaseEhrMessageConnection = operation.case_ehr_messages(
            query=case_ehr_messages_input
        )
        ehr_message_connection.edges.node()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        return [edge.node for edge in (operation + data).case_ehr_messages.edges]

    def upsert_procedures(self, procedures: List[ProcedureUpsertInput]) -> List[Procedure]:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        op_procedures = operation.procedures_upsert(procedures=procedures)
        op_procedures.success()
        op_procedures.created_procedures.edges()

        data = endpoint(operation)
        self.assert_successful_graphql_request(data)
        result = (operation + data).procedures_upsert

        return [edge.node for edge in result.created_procedures.edges]

    def upsert_anesthesias(self, anesthesias: List[AnesthesiaUpsertInput]) -> List[Anesthesia]:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        op_anesthesias = operation.anesthesias_upsert(input=anesthesias)
        op_anesthesias.success()
        op_anesthesias.anesthesias.edges()

        data = endpoint(operation)
        self.assert_successful_graphql_request(data)
        result = (operation + data).anesthesias_upsert

        return [edge.node for edge in result.anesthesias.edges]

    def upsert_case_procedures(
        self, case_procedures: List[CaseProceduresUpsertInput]
    ) -> CaseProceduresUpsert:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        response: CaseProceduresUpsert = operation.case_procedures_upsert(input=case_procedures)
        response.success()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        return operation + data

    def process_case_derived_properties(
        self, input: List[ProcessCaseDerivedPropertiesInput]
    ) -> ProcessCaseDerivedProperties:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        response: ProcessCaseDerivedProperties = operation.process_case_derived_properties(
            input=input
        )
        response.success()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        return (operation + data).process_case_derived_properties

    def upsert_and_archive_case_procedures(
        self, case_procedures: List[CaseProceduresUpsertAndArchiveInput]
    ) -> CaseProceduresUpsertAndArchive:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        response: CaseProceduresUpsertAndArchive = operation.case_procedures_upsert_and_archive(
            input=case_procedures
        )
        response.success()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        return operation + data

    def query_procedures(self, procedure_query_input: ProcedureQueryInput) -> List[Procedure]:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Query)

        op_procedure: ProcedureConnection = operation.procedures(query=procedure_query_input)
        op_procedure.edges()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        return [edge.node for edge in (operation + data).procedures.edges]

    def query_anesthesias(self, anesthesia_query_input: AnesthesiaQueryInput) -> List[Anesthesia]:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Query)

        op_procedure: AnesthesiaConnection = operation.anesthesias(query=anesthesia_query_input)
        op_procedure.edges()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        return [edge.node for edge in (operation + data).anesthesias.edges]

    def upsert_staff(self, staff: List[StaffUpsertInput]) -> List[Staff]:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        op_staff = operation.staff_upsert(staff=staff)
        op_staff.success()
        op_staff.created_staff.edges()

        data = endpoint(operation)
        self.assert_successful_graphql_request(data)
        result = (operation + data).staff_upsert

        return [edge.node for edge in result.created_staff.edges]

    def query_staff(
        self,
        staff_query_input: StaffQueryInput,
        contact_information_event_type_ids: Optional[List[str]] = None,
        contact_information_initialized: Optional[bool] = None,
    ) -> List[Staff]:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Query)

        staff_event_notification_contact_information_input_dict: Dict[str, Any] = {}
        if contact_information_event_type_ids:
            staff_event_notification_contact_information_input_dict[
                "contact_information_event_type_ids"
            ] = contact_information_event_type_ids
        if contact_information_initialized is not None:
            staff_event_notification_contact_information_input_dict["initialized"] = (
                contact_information_initialized
            )

        op_staff: StaffConnection = operation.staff(query=staff_query_input)
        op_staff.edges.node.staff_event_notification_contact_information(
            **staff_event_notification_contact_information_input_dict
        ).edges()
        op_staff.edges.node.id()
        op_staff.edges.node.first_name()
        op_staff.edges.node.last_name()
        op_staff.edges.node.name()
        op_staff.edges.node.external_staff_id()
        op_staff.edges.node.codes()
        op_staff.edges.node.organization.name()

        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        return [edge.node for edge in (operation + data).staff.edges]

    def upsert_staff_codes(self, staff_codes: List[StaffCodeUpsertInput]) -> List[StaffCode]:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        op_staff_codes = operation.staff_codes_upsert(staff_codes=staff_codes)
        op_staff_codes.success()
        op_staff_codes.created_staff_codes()

        data = endpoint(operation)
        self.assert_successful_graphql_request(data)
        result = (operation + data).staff_codes_upsert

        return result.created_staff_codes

    def upsert_case_staff(self, case_staff: List[CaseStaffUpsertInput]) -> CaseStaffUpsert:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        response: CaseStaffUpsert = operation.case_staff_upsert(input=case_staff)
        response.success()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        return operation + data

    def upsert_and_archive_case_staff(
        self, case_staff: List[CaseStaffUpsertAndArchiveInput]
    ) -> CaseStaffUpsertAndArchive:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        response: CaseStaffUpsertAndArchive = operation.case_staff_upsert_and_archive(
            input=case_staff
        )
        response.success()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        return operation + data

    def upsert_observations(self, observations: List[ObservationUpsertInput]) -> List[Observation]:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        op_observations: ObservationsUpsert = operation.observations_upsert(
            observations=observations
        )
        op_observations.success()
        op_observations.created_observations.edges()

        data = endpoint(operation)
        self.assert_successful_graphql_request(data)
        result = (operation + data).observations_upsert

        return [edge.node for edge in result.created_observations.edges]

    def query_observations(
        self, observations_query_input: ObservationSearchInput
    ) -> List[Observation]:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Query)

        op_observations: ObservationConnection = operation.observation(
            query=observations_query_input
        )
        op_observations.edges.node()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        return [edge.node for edge in (operation + data).observations.edges]

    def search_contact_information(
        self, contact_information_search_input: ContactInformationSearchInput
    ) -> List[ContactInformation]:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Query)

        op_contact_information: ContactInformationConnection = operation.contact_information(
            query=contact_information_search_input
        )
        op_contact_information.edges.node()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        return [edge.node for edge in (operation + data).contact_information.edges]

    def search_staff_event_notification_contact_information(
        self,
        staff_event_notification_contact_information_query_input: StaffEventNotificationContactInformationSearchInput,
    ) -> List[StaffEventNotificationContactInformation]:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Query)

        op_staff_event_notification_contact_information: StaffEventNotificationContactInformationConnection = operation.staff_event_notification_contact_information(
            query=staff_event_notification_contact_information_query_input
        )
        op_staff_event_notification_contact_information.edges.node()
        data = endpoint(operation)
        self.assert_successful_graphql_request(data)

        return [
            edge.node
            for edge in (operation + data).staff_event_notification_contact_information.edges
        ]

    def get_staffing_needs_roles(self) -> List[StaffRole]:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Query)

        operation.staffing_needs_roles()

        data = endpoint(operation)
        self.assert_successful_graphql_request(data)
        return (operation + data).staffing_needs_roles

    def get_staffing_needs_ratios(self, site_id: str) -> List[StaffRole]:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Query)

        operation.site(id=site_id).staffing_needs_ratios()

        data = endpoint(operation)
        self.assert_successful_graphql_request(data)
        return (operation + data).site.staffing_needs_ratios

    def create_staffing_needs_ratio(
        self, staffing_needs_ratio: StaffingNeedsRatioCreateInput
    ) -> StaffingNeedsRatioCreate:
        endpoint = self.create_http_endpoint(self.graphql_url, self.headers())
        operation = Operation(Mutation)

        op: StaffingNeedsRatioCreate = operation.staffing_needs_ratio_create(
            input=staffing_needs_ratio
        )
        op.success()
        op.created_staffing_needs_ratio()

        data = endpoint(operation)
        self.assert_successful_graphql_request(data)
        return (operation + data).staffing_needs_ratio_create

    @staticmethod
    def assert_successful_graphql_request(data: Mapping[str, Any]) -> None:
        if "errors" in data:
            # sgqlc library will put the exception object in the `errors` response.
            # For a more native python experience, re-raise the exception. This also helps when the
            # bespoke error JSON sgqlc creates is unprintable.
            if len(data["errors"]) > 0 and "exception" in data["errors"][0]:
                exception = data["errors"][0]["exception"]
                if isinstance(exception, Exception):
                    raise exception

            # Otherwise, wrap the errors in a GraphQLError.
            # Logging libraries don't do well with lists as messages. Convert the list to string.
            # ValueError has no "message" key
            messages = [
                e["message"] if isinstance(e, dict) and "message" in e else str(e)
                for e in data["errors"]
            ]
            raise GraphQLError(message=". ".join(messages))

    def execute_graphql(
        self,
        statement: str,
        variables: Optional[Mapping[str, Any]] = None,
        operation_name: str | None = None,
    ) -> Any:
        data = {"query": statement, "variables": variables}
        if operation_name:
            data["operationName"] = operation_name
        req = self.make_request(
            POST, self.graphql_url, headers=self.headers(), data=json.dumps(data)
        )
        assert_no_error_status_codes(req)
        return req.json()

    def query_graphql_from_schema(
        self, query: ObjectField[gql__Query], label: Optional[str] = None
    ) -> GQLQuery:
        query_obj = self.apella_gql_client.query.select(query)._set_label(label)
        query_obj_gql = query_obj.to_gql()
        results = self.execute_graphql(query_obj_gql, operation_name=label)
        if "errors" in results:
            raise GraphQLError(message=str(results["errors"]))
        return results_to_dataclass(results["data"], query_obj)  # type: ignore

    def query_graphql_from_schema_many(
        self, query: list[ObjectField[gql__Query]], label: Optional[str] = None
    ) -> GQLQuery:
        query_obj = self.apella_gql_client.query.select(*query)._set_label(label)
        query_obj_gql = query_obj.to_gql()
        results = self.execute_graphql(query_obj_gql, operation_name=label)
        if "errors" in results:
            raise GraphQLError(message=str(results["errors"]))
        return results_to_dataclass(results["data"], query_obj)  # type: ignore

    def mutate_graphql_from_schema(
        self, mutation: ObjectField[gql__Mutation], label: Optional[str] = None
    ) -> GQLMutation:
        mutation_obj = self.apella_gql_client.mutation.select(mutation)._set_label(label)
        mutation_obj_gql = mutation_obj.to_gql()
        results = self.execute_graphql(mutation_obj_gql, operation_name=label)
        if "errors" in results:
            raise GraphQLError(message=str(results["errors"]))
        return results_to_dataclass(results["data"], mutation_obj)  # type: ignore

    def mutate_graphql_from_schema_many(
        self, mutation: list[ObjectField[gql__Mutation]], label: Optional[str] = None
    ) -> GQLMutation:
        mutation_obj = self.apella_gql_client.mutation.select(*mutation)._set_label(label)
        mutation_obj_gql = mutation_obj.to_gql()
        results = self.execute_graphql(mutation_obj_gql, operation_name=label)
        if "errors" in results:
            raise GraphQLError(message=str(results["errors"]))
        return results_to_dataclass(results["data"], mutation_obj)  # type: ignore


T = TypeVar("T")


# TODO: Consider refactoring to use `tenacity.retry()` instead.
def retry_until_success(
    fn: Callable[[], T],
    retry_interval: int = 1,
    max_retry_interval: int = 600,
    max_retries: int = 6,
) -> T:
    # The default retry settings here result in a max delay of:
    # 1 + 2 + 4 + 8 + 16 + 32 = 63 seconds ~= 1 minute
    cur_retry = 0
    while True:
        try:
            return fn()
        # GraphQL returns an HTTPErrors for errors, so we need to catch that
        except (ServerError, ConnectionError, HTTPError) as e:
            cur_retry = cur_retry + 1
            # URLlib only has a single HTTPError class, so we need to check the message to see if it's a 5xx
            if cur_retry >= max_retries or (
                isinstance(e, HTTPError) and e.code >= 400 and e.code < 500
            ):
                # Not attempting to retry, raise this error
                raise e
            time.sleep(retry_interval)
            # This increases the time until retry by just 1 second
            # Usually these back_offs are exponential
            retry_interval = retry_interval * 2
            if retry_interval > max_retry_interval:
                retry_interval = max_retry_interval
