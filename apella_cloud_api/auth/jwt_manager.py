import json

from os.path import expanduser
from pathlib import Path
from typing import Any, Dict

# This class just writes and reads json from this file,
jwt_db = expanduser("~/.apella/cli/jwt.json")


# Where the json is structured like:
# {
#    'production': {...},
#    'staging': {...}
# }
#
# Where `{...}` is whatever is passed into `add_jwt(environment, jwt)`


def read_jwt_db() -> Any:
    try:
        with open(jwt_db, "r") as db:
            return json.load(db)
    except FileNotFoundError:
        return {}


def write_jwt_db(data: Any) -> Any:
    Path(jwt_db).parent.mkdir(parents=True, exist_ok=True)
    with open(jwt_db, "w") as db:
        return json.dump(data, db)


def add_jwt(environment: str, jwt: Any) -> None:
    data = read_jwt_db()
    data[environment] = jwt
    write_jwt_db(data)


def get_jwt(environment: str) -> Dict[str, str]:
    data = read_jwt_db()
    return data.get(environment)


def remove_jwt(environment: str) -> None:
    data = read_jwt_db()
    data.pop(environment)
    write_jwt_db(data)
