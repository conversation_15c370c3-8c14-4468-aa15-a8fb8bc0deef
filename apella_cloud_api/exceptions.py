from http import HTTPStatus

from fastapi import HTTPException


class HttpError(HTTPException):
    def __init__(self, status_code: int, message: str) -> None:
        super().__init__(status_code=status_code, detail=message)
        self.message = message

    def __str__(self) -> str:
        return f"({self.status_code}): {self.message}"


class ClientError(HttpError):
    pass


class NotAuthorized(ClientError):
    pass


class NotFound(ClientError):
    def __init__(self, message: str) -> None:
        ClientError.__init__(self, HTTPStatus.NOT_FOUND, message)


class ServerError(HttpError):
    pass


class RetryableError(HttpError):
    pass
