import re
import subprocess
import sys
from functools import singledispatch
from pathlib import Path
from typing import Any

from graphql import parse
from graphql.language.ast import (
    EnumTypeDefinitionNode,
    EnumValueDefinitionNode,
    FieldDefinitionNode,
    InputObjectTypeDefinitionNode,
    InputValueDefinitionNode,
    ListTypeNode,
    NamedTypeNode,
    NonNullTypeNode,
    ObjectTypeDefinitionNode,
    TypeNode,
)
from graphql.language.visitor import Visitor, visit

from apella_cloud_api.new_schema_generator.schema_generator_intermediate_classes import (
    IntermediateEnum,
    IntermediateEnumValue,
    IntermediateInputField,
    IntermediateInputType,
    IntermediateListType,
    IntermediateObjectField,
    IntermediateObjectType,
    IntermediateTypeDefinitionNode,
)

"""
The purpose of this module is to generate a schema that allows developers to confidently
write valid GraphQL queries against the provided schema. This is done by generating a set of classes
that represent the schema. These classes are type annotated and can be used to generate a GQL
query that has been type checked.
This query can then be executed against the schema to get a response which can be converted into
the corresponding client side classes.

This file is best thought of as the last 2 phases of a compiler
See https://en.wikipedia.org/wiki/Compiler#Three-stage_compiler_structure for a high level overview.
We skip the code optimization phase because it is not relevant to our use case.
The first 3 phases are handled by the graphql library and result in an AST that this file parses:
    1. Lexical Analysis
    2. Syntax Analysis
    3. Semantic Analysis
        Results in the AST that this file parses
The last 2 phases are handled by this file:
    4. Intermediate Code Generation
        This file generates a collection of intermediate types and
    5. Code Generation

Queries are of the form:
Schema.Query.ObjectConnection.args(kwarg=value).select(
    Schema.Object.Field1,
    Schema.Object.Field2,
    Schema.Object.Field3,
)
and result in an object of the form:
    Query:
       ...
        ObjectConnection:
            Results[]:
                Field1: value
                Field2: value
                Field3: value
        ...
"""


@singledispatch
def generate_intermediate_type(
    node: TypeNode, nullable: bool = True
) -> IntermediateTypeDefinitionNode:
    raise NotImplementedError(f"Type {type(node)} not implemented")


@generate_intermediate_type.register(NamedTypeNode)
def generate_named_type(
    node: NamedTypeNode, nullable: bool = True
) -> IntermediateTypeDefinitionNode:
    return IntermediateTypeDefinitionNode(
        name=node.name.value, final_type_value=node.name.value, nullable=nullable
    )


@generate_intermediate_type.register(NonNullTypeNode)
def generate_non_null_type(
    node: NonNullTypeNode, nullable: bool = False
) -> IntermediateTypeDefinitionNode:
    return generate_intermediate_type(node.type, nullable=False)


@generate_intermediate_type.register(ListTypeNode)
def generate_list_type(node: ListTypeNode, nullable: bool = True) -> IntermediateTypeDefinitionNode:
    wrapped_type = generate_intermediate_type(node.type)
    final_type_value = wrapped_type.final_type_value
    return IntermediateListType(
        name="ListType",
        final_type_value=final_type_value,
        wrapped_type=wrapped_type,
        nullable=nullable,
    )


class SchemaClassGenerator(Visitor):
    schema_name: str

    object_types: dict[str, IntermediateObjectType]
    input_object_types: dict[str, IntermediateInputType]
    enum_types: dict[str, IntermediateEnum]

    def __init__(self) -> None:
        super(SchemaClassGenerator, self).__init__()
        self.schema_name = "class ApellaSchema:\n"

        self.object_types = {}
        self.input_object_types = {}
        self.enum_types = {}

    def generate_input_value(self, node: InputValueDefinitionNode) -> IntermediateInputField:
        raw_name = node.name.value
        name = re.sub(r"(?<!^)(?=[A-Z])", "_", raw_name).lower()
        gql_type = generate_intermediate_type(node.type)
        return IntermediateInputField(
            name=name,
            raw_name=raw_name,
            final_type_value=gql_type.final_type_value,
            wrapped_type=gql_type,
            nullable=gql_type.nullable,
        )

    def generate_field_definition(self, node: FieldDefinitionNode) -> IntermediateObjectField:
        raw_name = node.name.value
        name = re.sub(r"(?<!^)(?=[A-Z])", "_", raw_name).lower()
        gql_type = generate_intermediate_type(node.type)
        field_args = [self.generate_input_value(node=arg) for arg in node.arguments]
        return IntermediateObjectField(
            name=name,
            raw_name=raw_name,
            final_type_value=gql_type.final_type_value,
            wrapped_type=gql_type,
            field_args=field_args,
            nullable=gql_type.nullable,
        )

    def generate_enum_value(self, node: EnumValueDefinitionNode) -> IntermediateEnumValue:
        name = node.name.value
        return IntermediateEnumValue(name=name)

    def enter_object_type_definition(
        self, node: ObjectTypeDefinitionNode, *args: list[Any]
    ) -> None:
        self.object_types[node.name.value] = IntermediateObjectType(
            name=node.name.value,
            fields=[self.generate_field_definition(node=field) for field in node.fields],
        )

    def enter_input_object_type_definition(
        self, node: InputObjectTypeDefinitionNode, *args: list[Any]
    ) -> None:
        self.input_object_types[node.name.value] = IntermediateInputType(
            name=node.name.value,
            fields=[self.generate_input_value(node=field) for field in node.fields],
        )

    def enter_enum_type_definition(self, node: EnumTypeDefinitionNode, *args: list[Any]) -> None:
        self.enum_types[node.name.value] = IntermediateEnum(
            name=node.name.value,
            values=[self.generate_enum_value(node=value) for value in node.values],
        )

    def get_client_object_schema_names(self, client_schema_module_name: str) -> str:
        client_schema_names = ",\n".join(
            sorted(
                [value.gql_client_type_name for value in self.object_types.values()]
                + [value.gql_client_type_name for value in self.enum_types.values()]
            )
        )
        return f"from .{client_schema_module_name} import ( # noqa\n{client_schema_names}\n)\n"

    def get_api_schema(self, client_schema_module_name: str, input_schema_module_name: str) -> str:
        input_object_names = ",\n".join(
            sorted(
                [
                    f"    {input_object_type.gql_client_type_name}"
                    for input_object_type in self.input_object_types.values()
                ]
            )
        )
        final_output = "".join(
            [
                "import inspect\n",
                "import isodate\n",
                "from typing import Generic, Union\n",
                "from uuid import UUID # noqa\n",
                "from datetime import datetime, date\n",
                "from .new_schema_generator.schema_generator_base_classes import ObjectField, DSLFieldOutputType, FieldSelector, Empty, GQLObject\n",
            ]
        )
        final_output += self.get_client_object_schema_names(client_schema_module_name)
        final_output += (
            f"from .{input_schema_module_name} import ( # noqa\n{input_object_names}\n)\n"
        )
        # Field selectors allow us to select fields from a given non-scalar non-enum type
        final_output += self.generate_field_selectors()
        final_output += "\n"
        final_output += self.generate_object_types()
        final_output += "\n"
        final_output += self.schema_name
        final_output += "\n".join(
            [
                f"    @property\n"
                f"    def {key}(self) -> {value.gql_server_type_name}:\n"
                f"        return {value.gql_server_type_name}()\n"
                for key, value in self.object_types.items()
            ]
        )
        final_output += "\n"
        final_output += "class ApellaGQLClient:\n"
        final_output += "\n".join(
            [
                f"    @property",
                f'    def query(self) -> "gql__Query__FieldSelector[gql__Query]":',
                f'        return gql__Query__FieldSelector("query", "")',
                f"    @property",
                f'    def mutation(self) -> "gql__Mutation__FieldSelector[gql__Mutation]":',
                f'        return gql__Mutation__FieldSelector("mutation", "")',
            ]
        )
        final_output += "\n"
        return final_output

    def get_input_type_schema(self, client_schema_module_name: str) -> str:
        final_output = "".join(
            [
                "from dataclasses import dataclass\n\n",
                "from typing import Union\n",
                "from uuid import UUID # noqa\n",
                "from datetime import datetime, date, timedelta\n",
                "from .new_schema_generator.schema_generator_base_classes import GQLInputObject, Empty\n",
            ]
        )
        final_output += "\n"
        final_output += self.get_client_object_schema_names(client_schema_module_name)
        final_output += "\n"
        final_output += self.generate_input_objects()
        final_output += "\n"
        return final_output

    def generate_object_types(self) -> str:
        object_types_code = []
        kwargs = {
            "enums": self.enum_types,
        }
        for object_type in self.object_types.values():
            object_types_code.append(object_type.generate_gql_code(**kwargs))
        return "\n".join(object_types_code) + "\n"

    def generate_field_selectors(self) -> str:
        field_selectors_code = []
        for object_type in self.object_types.values():
            field_selectors_code.append(object_type.generate_field_selector_code())
        return "\n".join(field_selectors_code) + "\n"

    def generate_client_objects(self) -> str:
        client_objects_code = []
        for object_type in self.object_types.values():
            client_objects_code.append(object_type.generate_client_code())
        for enum_type in self.enum_types.values():
            client_objects_code.append(enum_type.generate_client_code())
        return "\n".join(client_objects_code)

    def generate_input_objects(self) -> str:
        input_objects_code = []
        for input_object in self.input_object_types.values():
            input_objects_code.append(input_object.generate_input_code())
        return "\n".join(input_objects_code)

    def get_client_schema(self) -> str:
        final_output = "".join(
            [
                "import enum\n",
                "from uuid import UUID # noqa\n"
                "from datetime import datetime, date, timedelta\n"
                "from typing import Union\n",
                "from .new_schema_generator.schema_generator_base_classes import GQLClientObject, Empty, NotFound\n",
            ]
        )
        final_output += "\n"
        final_output += self.generate_client_objects()
        final_output += "\n"
        return final_output


def main() -> None:
    if len(sys.argv) != 5:
        print(
            f"Usage: {sys.argv[0]} <input_schema_file> <api_schema_file> <client_schema_file> <input_type_schema_file>"
        )
        sys.exit(1)

    input_schema_file = sys.argv[1]
    api_schema_output_file = sys.argv[2]
    client_schema_output_file = sys.argv[3]
    input_type_schema_output_file = sys.argv[4]

    with open(input_schema_file, "r") as f:
        schema_str = f.read()

    ast = parse(schema_str)

    generator = SchemaClassGenerator()
    visit(ast, generator)

    client_schema_output_module = client_schema_output_file.replace(".py", "").split("/")[-1]
    input_type_schema_module = input_type_schema_output_file.replace(".py", "").split("/")[-1]

    api_schema_output_file_path = Path(api_schema_output_file)
    with open(api_schema_output_file_path, "w") as f:
        f.write(generator.get_api_schema(client_schema_output_module, input_type_schema_module))

    subprocess.run(["ruff", "check", "--fix", api_schema_output_file], check=True)
    subprocess.run(["ruff", "format", api_schema_output_file], check=True)

    input_type_schema_output_file_path = Path(input_type_schema_output_file)
    with open(input_type_schema_output_file_path, "w") as f:
        f.write(generator.get_input_type_schema(client_schema_output_module))

    subprocess.run(["ruff", "check", "--fix", input_type_schema_output_file], check=True)
    subprocess.run(["ruff", "format", input_type_schema_output_file], check=True)

    client_schema_output_file_path = Path(client_schema_output_file)
    with open(client_schema_output_file_path, "w") as f:
        f.write(generator.get_client_schema())

    subprocess.run(["ruff", "check", "--fix", client_schema_output_file], check=True)
    subprocess.run(["ruff", "format", client_schema_output_file], check=True)

    print(f"GraphQL static schema generated in {api_schema_output_file}")


if __name__ == "__main__":
    main()
