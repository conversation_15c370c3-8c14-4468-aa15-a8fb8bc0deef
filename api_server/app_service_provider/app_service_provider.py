from functools import cache, cached_property
from typing import (
    TypeVar,
    Optional,
    Type,
    Any,
    Callable,
    Mapping,
    ContextManager,
    Sequence,
    Awaitable,
)

from di import Container, SolvedDependent
from di._container import BindHook, bind_by_type, ScopeState
from di.api.dependencies import DependentBase
from di.api.providers import DependencyProvider
from di.api.scopes import Scope
from di.dependent import Dependent
from di.executors import SyncExecutor, AsyncExecutor

import api_server.graphql
from api_server import logging
from api_server.graphql.context import Context, AppServices
from api_server.app_service_provider.app_service_base_classes import REQUEST_SCOPE, APP_SCOPE


T = TypeVar("T")


class AppServicesBuilder:
    _container: Container

    def __init__(self, providers: list[BindHook]) -> None:
        container = Container()
        for provider in providers:
            container.bind(provider)
        self._executor = SyncExecutor()
        self._async_executor = AsyncExecutor()
        self._container = container

    def add_simple_app_provider(self, service: T, type_: Type[T], scope: str = APP_SCOPE) -> None:
        self.add_app_provider(bind_by_type(Dependent(lambda: service, scope=scope), type_))

    def add_app_provider(self, provider: BindHook) -> None:
        self._container.bind(provider)

    def build_app_services_providers(self) -> None:
        for service in self.provide_app_scope_type(AppServices).__dict__.values():
            self.add_simple_app_provider(service, type(service))

    def app_context(self) -> Context:
        return self.run(
            api_server.graphql.context.Context,
            scope=REQUEST_SCOPE,
        )

    @cached_property
    def _app_context_manager(self) -> ContextManager[ScopeState]:
        return self._container.enter_scope(APP_SCOPE)

    @cached_property
    def _app_state(self) -> ScopeState:
        return self._app_context_manager.__enter__()

    @cache
    def _solve_for_type(self, type_: Callable[..., T], scope: str) -> SolvedDependent[T]:
        return self._container.solve(
            Dependent(type_, scope=scope),
            scopes=[APP_SCOPE, REQUEST_SCOPE],
            scope_resolver=_scope_resolver,
        )

    @cache
    def provide_app_scope_type(self, type_: Callable[..., T]) -> T:
        solved = self._solve_for_type(type_, APP_SCOPE)
        return solved.execute_sync(executor=self._executor, state=self._app_state)

    @cache
    async def async_provide_app_scope_type(self, type_: Callable[..., T]) -> T:
        solved = self._solve_for_type(type_, APP_SCOPE)
        return await solved.execute_async(executor=self._async_executor, state=self._app_state)

    def run(
        self,
        action: Callable[..., T],
        values: Optional[Mapping[DependencyProvider, Any]] = None,
        scope: Scope = REQUEST_SCOPE,
    ) -> T:
        if scope == APP_SCOPE:
            logging.warning("Run called at App Scope, use provide_app_level_type instead")
            return self.provide_app_scope_type(action)
        if values is None:
            values_to_use: Mapping[DependencyProvider, Any] = {}
        else:
            values_to_use = values

        solved = self._solve_for_type(action, scope)
        with self._container.enter_scope(scope, self._app_state) as scope_state:
            (result,) = (
                solved.execute_sync(
                    values=values_to_use,
                    executor=self._executor,
                    state=scope_state,
                ),
            )
            return result

    async def async_run(
        self,
        action: Callable[..., Awaitable[T]],
        values: Optional[Mapping[DependencyProvider, Any]] = None,
        scope: Scope = REQUEST_SCOPE,
    ) -> T:
        if scope == APP_SCOPE:
            logging.warning("Run called at App Scope, use provide_app_level_type instead")
            return await self.async_provide_app_scope_type(action)
        if values is None:
            values_to_use: Mapping[DependencyProvider, Any] = {}
        else:
            values_to_use = values

        solved = self._solve_for_type(action, scope)
        with self._container.enter_scope(scope, self._app_state) as scope_state:
            (result,) = (
                await solved.execute_async(
                    values=values_to_use,
                    executor=self._async_executor,
                    state=scope_state,
                ),
            )
            return result


def _scope_resolver(
    dep: DependentBase[Any],
    subdep_scopes: Sequence[Scope],
    scopes: Sequence[Scope],
) -> Scope:
    if dep.scope is not None:
        return dep.scope

    max_scope_index = max((scopes.index(scope) for scope in subdep_scopes if scope), default=None)

    scope = scopes[max_scope_index] if max_scope_index else APP_SCOPE
    return scope
