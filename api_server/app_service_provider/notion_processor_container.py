"""
This is the minimal set of dependencies in order to construct all the other dependencies.
"""

from di import bind_by_type
from di.dependent import Dependent

from api_server.app_service_provider.app_service_base_classes import APP_SCOPE
from api_server.app_service_provider.app_service_provider import AppServicesBuilder
from api_server.app_service_provider.app_services_utils import provide_secret_store
from databases.secret_store import SecretStore

notion_processor_container: AppServicesBuilder = AppServicesBuilder(
    [
        bind_by_type(Dependent(provide_secret_store, scope=APP_SCOPE), SecretStore),
    ]
)
