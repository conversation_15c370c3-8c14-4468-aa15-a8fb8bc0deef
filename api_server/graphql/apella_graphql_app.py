from typing import Any, Callable

from graphene import Schema
from graphql import graphql
from graphql.pyutils import did_you_mean
from starlette.requests import Request
from starlette.responses import JSONResponse
from starlette_graphene3 import Graph<PERSON>App, _get_operation_from_request  # type: ignore

from api_server.graphql.context import Context
from api_server.graphql.middleware.alias_limit_middleware import AliasLimitMiddleware
from api_server.graphql.middleware.disable_introspection_middleware import (
    DisableIntrospectionMiddleware,
)
from api_server.graphql.middleware.error_logging_filter_middleware import (
    ErrorLoggingFilterMiddleware,
)
from api_server.graphql.middleware.timing_middleware import TimingMiddleware
from api_server.graphql.middleware.tracing_middleware import TracingMiddleware


class ApellaGraphQLApp(GraphQLApp):
    def __init__(
        self,
        schema: Schema,
        context_value: Callable[[Request], Context],
    ):
        super(ApellaGraphQLApp, self).__init__(
            schema=schema,
            context_value=context_value,
            # Ordering matters here. The first middleware will run last. If changing the order make sure
            # the awaiting logic in TimingMiddleware matches the new middleware.
            middleware=[
                TimingMiddleware(),
                TracingMiddleware(),
                DisableIntrospectionMiddleware(),
                AliasLimitMiddleware(),
                ErrorLoggingFilterMiddleware(),
            ],
        )

        # Disable field suggestions
        # Exposing field suggestions may pose a security risk, as attackers could use the suggested
        # field names to gather information about the API's structure and potentially craft targeted
        # attacks, exploit other vulnerabilities, or gain unauthorized access to protected resources.
        did_you_mean.MAX_LENGTH = 0

    async def _handle_http_request(self, request: Request) -> JSONResponse:
        """
        This is copied from starlette_graphene3 0.6.0. We needed to remove error logging per error to avoid extra
        server side noise.
        """
        try:
            operations = await _get_operation_from_request(request)
        except ValueError as e:
            return JSONResponse({"errors": [e.args[0]]}, status_code=400)

        if isinstance(operations, list):
            return JSONResponse(
                {"errors": ["This server does not support batching"]}, status_code=400
            )
        else:
            operation = operations

        query = operation["query"]
        variable_values = operation.get("variables")
        operation_name = operation.get("operationName")
        context_value = await self._get_context_value(request)

        result = await graphql(
            self.schema.graphql_schema,
            source=query,
            context_value=context_value,
            root_value=self.root_value,
            middleware=self.middleware,
            variable_values=variable_values,
            operation_name=operation_name,
            execution_context_class=self.execution_context_class,
        )

        response: dict[str, Any] = {"data": result.data}
        if result.errors:
            response["errors"] = [self.error_formatter(error) for error in result.errors]

        return JSONResponse(
            response,
            status_code=200,
            background=context_value.get("background"),
        )
