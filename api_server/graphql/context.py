from dataclasses import dataclass

from api_server.services.block.block_release_processing_service import BlockReleaseProcessingService
from api_server.services.block_utilization.block_utilization_service import BlockUtilizationService
from api_server.services.case_activity.case_activity_service import CaseActivityService
from api_server.services.case_forecasts.case_forecast_service import CaseForecastService
from api_server.services.case_labels.case_label_service import CaseLabelService
from api_server.services.case_to_block.case_to_block_service import CaseToBlockService
from api_server.services.closures.closure_service import ClosureService
from api_server.services.closures.graphql.site_closure_loader import SiteClosureLoader
from api_server.services.available_time_slot.available_time_slot_service import (
    AvailableTimeSlotService,
)
from api_server.services.first_case_config.graphql.room_first_case_config_loader import (
    RoomFirstCaseConfigLoader,
)
from api_server.services.prime_time.graphql.site_capacity_constraint_loader import (
    SiteCapacityConstraintLoader,
)
from api_server.services.prime_time.graphql.site_prime_time_loader import (
    SitePrimeTimeConfigLoader,
)
from api_server.services.closures.graphql.room_closure_loader import RoomClosureLoader
from api_server.services.prime_time.graphql.room_prime_time_loader import (
    RoomPrimeTimeConfigLoader,
)
from api_server.services.prime_time.prime_time_service import PrimeTimeService
from api_server.services.first_case_config.first_case_config_service import (
    FirstCaseConfigService,
)
from api_server.services.first_case_config.graphql.site_first_case_config_loader import (
    SiteFirstCaseConfigLoader,
)
from api_server.services.terminal_cleans.terminal_cleans_service import TerminalCleansService
from starlette.requests import Request
from typing import Any

from fastapi import BackgroundTasks
from graphql import GraphQLSchema

from api_server.services.anesthesia.anesthesia_service import AnesthesiaService
from api_server.services.anesthesia.graphql.anesthesia_loader import AnesthesiaLoader
from api_server.services.annotation_tasks.annotation_task_service import (
    AnnotationTaskService,
)
from api_server.services.annotation_tasks.graphql.annotation_task_type_loader import (
    AnnotationTaskTypeLoader,
)
from api_server.services.annotation_tasks.graphql.remaining_annotation_tasks_loader import (
    RemainingAnnotationTasksLoader,
)
from api_server.services.apella_case.apella_case_service import ApellaCaseService
from api_server.services.apella_case.graphql.apella_case_loader import (
    RoomApellaCaseLoader,
)
from api_server.services.block.block_service import BlockService
from api_server.services.block.graphql.block_loader import (
    BlockLoader,
    BlockTimeLoader,
    BlockTimeForBlockLoader,
    BlockTimeReleaseForBlockTimeLoader,
    RoomBlockTimeLoader,
    StaffBlockLoader,
)
from api_server.services.boards.board_service import BoardService
from api_server.services.boards.graphql.board_rooms_loader import BoardRoomsLoader
from api_server.services.camera.camera_service import CameraService
from api_server.services.camera.graphql.camera_loader import CameraLoader
from api_server.services.camera.graphql.default_camera_loader import DefaultCameraLoader
from api_server.services.case.graphql.matching_status_reason_loader import (
    MatchingStatusReasonLoader,
)
from api_server.services.cluster.cluster_service import ClusterService
from api_server.services.cluster.graphql.cluster_loader import (
    ClusterLoader,
    ClusterMappingLoader,
)
from api_server.services.patients.graphql.patient_loader import PatientLoader
from api_server.services.plan.case_note_plan_service import CaseNotePlanService
from api_server.services.case.case_service import CaseService
from api_server.services.plan.case_staff_plan_service import CaseStaffPlanService
from api_server.services.case.case_staff_service import CaseStaffService
from api_server.services.case.graphql.case_flag_loader import CaseFlagLoader
from api_server.services.case.graphql.case_loader import CaseLoader
from api_server.services.case.graphql.case_procedure_loader import (
    CaseProcedureLoader,
    PrimaryCaseProcedureLoader,
)
from api_server.services.case.graphql.case_staff_loader import CaseStaffLoader
from api_server.services.case_derived_properties.case_derived_properties_service import (
    CaseDerivedPropertiesService,
)
from api_server.services.case_derived_properties.graphql.case_derived_properties_loader import (
    CaseDerivedPropertiesLoader,
)
from api_server.services.case_duration.case_duration_service import (
    CaseDurationService,
)
from api_server.services.contact_information.contact_information_service import (
    ContactInformationService,
)
from api_server.services.contact_information.graphql.contact_information_loader import (
    ContactInformationLoader,
)
from api_server.services.contact_information.graphql.staff_event_notification_contact_information_loader import (
    EventNotificationLoader,
    EventNotificationSubscriptionLoader,
    StaffEventNotificationContactInformationLoader,
    StaffEventNotificationLoader,
    ContactInformationSubscriptionLoader,
    StaffEventNotificationSubscriptionLoader,
)
from api_server.services.custom_phase_config.custom_phase_config_service import (
    CustomPhaseConfigService,
)
from api_server.services.events.event_service import EventService
from api_server.services.events.graphql.event_loaders import (
    EventAttrsLoader,
    EventLoader,
    RoomEventsLoader,
)
from api_server.services.highlights.graphql.highlight_feedback_loader import (
    HighlightFeedbackLoader,
)
from api_server.services.highlights.graphql.highlight_loader import HighlightLoader
from api_server.services.highlights.highlight_service import HighlightService
from api_server.services.launch_darkly.launch_darkly_service import LaunchDarklyService
from api_server.services.measurement_periods.graphql.measurement_periods_loader import (
    MeasurementPeriodLoader,
)
from api_server.services.measurement_periods.measurement_period_service import (
    MeasurementPeriodService,
)
from api_server.services.media.graphql.latest_image_by_camera_id_loader import (
    LatestImageByCameraIdLoader,
)
from api_server.services.media.graphql.playlist_loader import PlaylistLoader
from api_server.services.media.media_asset_service_client import MediaAssetService
from api_server.services.media.media_service import MediaService
from api_server.services.objects.objects_service import ObjectsService
from api_server.services.observations.graphql.observation_type_loader import (
    ObservationTypeLoader,
)
from api_server.services.observations.graphql.observations_loader import (
    ObservationLoader,
)
from api_server.services.observations.observation_service import ObservationService
from api_server.services.organization.graphql.organization_loader import (
    OrganizationLoader,
    OrganizationSitesLoader,
)
from api_server.services.organization.organization_service import OrganizationService
from api_server.services.phases.graphql.phase_loaders import (
    ChildPhaseLoader,
    ParentPhaseLoader,
    PhaseTypeLoader,
    PhaseLoader,
)
from api_server.services.phases.phase_service import PhaseService
from api_server.services.plan.graphql.case_note_plan_loader import CaseNotePlanLoader
from api_server.services.plan.graphql.case_staff_plan_loader import CaseStaffPlanLoader
from api_server.services.procedures.graphql.procedure_loader import ProcedureLoader
from api_server.services.procedures.procedure_service import ProcedureService
from api_server.services.room.graphql.room_loader import RoomLoader, RoomCamerasLoader
from api_server.services.room.graphql.room_tag_loader import RoomTagLoader
from api_server.services.room.room_service import RoomService
from api_server.services.schedule_assistant_email.schedule_assistant_email_service import (
    ScheduleAssistantEmailService,
)
from api_server.services.service_lines.graphql.service_line_loader import (
    ServiceLineLoader,
)
from api_server.services.case.case_classification_type_loader import (
    CaseClassificationTypeLoader,
)
from api_server.services.service_lines.service_line_service import ServiceLineService
from api_server.services.site.graphql.site_loader import SiteLoader, SiteRoomsLoader
from api_server.services.site.site_service import SiteService
from api_server.services.staff.graphql.staff_loader import StaffLoader
from api_server.services.staff.staff_service import StaffService
from api_server.services.staffing_needs.graphql.staffing_needs_roles_loader import (
    StaffingNeedsRolesLoader,
)
from api_server.services.staffing_needs.staffing_needs_service import (
    StaffingNeedsService,
)
from api_server.services.turnover.graphql.turnover_goals_loader import (
    TurnoverGoalsLoader,
)
from api_server.services.turnover.graphql.turnover_note_loader import TurnoverNoteLoader
from api_server.services.turnover.graphql.turnover_label_loader import TurnoverLabelLoader
from api_server.services.turnover.turnover_label_assoc_service import TurnoverLabelAssocService
from api_server.services.turnover.turnover_label_service import TurnoverLabelService
from api_server.services.turnover.turnover_note_service import TurnoverNoteService
from api_server.services.turnover.turnover_service import TurnoverService
from api_server.services.case_labels.graphql.case_label_loader import CaseLabelLoader
from api_server.services.users.graphql.user_loader import UserLoader
from api_server.services.users.user_service import UserService
from api_server.services.user_filter_views.user_filter_view_service import (
    UserFilterViewService,
)
from auth.auth import Auth


@dataclass
class AppServices:
    procedure_service: ProcedureService
    staff_service: StaffService
    staffing_needs_service: StaffingNeedsService
    case_note_plan_service: CaseNotePlanService
    case_staff_service: CaseStaffService
    case_staff_plan_service: CaseStaffPlanService
    observation_service: ObservationService

    auth: Auth

    prime_time_service: PrimeTimeService
    first_case_config_service: FirstCaseConfigService
    site_service: SiteService
    user_service: UserService
    event_service: EventService
    organization_service: OrganizationService
    room_service: RoomService
    closure_service: ClosureService
    highlight_service: HighlightService
    camera_service: CameraService
    media_service: MediaService
    media_asset_service: MediaAssetService
    annotation_task_service: AnnotationTaskService
    phase_service: PhaseService
    case_activity_service: CaseActivityService
    apella_case_service: ApellaCaseService
    case_forecast_service: CaseForecastService
    objects_service: ObjectsService
    case_service: CaseService
    contact_information_service: ContactInformationService
    case_derived_properties_service: CaseDerivedPropertiesService
    measurement_period_service: MeasurementPeriodService
    block_service: BlockService
    block_release_processing_service: BlockReleaseProcessingService
    board_service: BoardService
    launch_darkly_service: LaunchDarklyService
    anesthesia_service: AnesthesiaService
    service_line_service: ServiceLineService
    case_duration_service: CaseDurationService
    available_time_slot_service: AvailableTimeSlotService
    user_filter_view_service: UserFilterViewService
    cluster_service: ClusterService
    terminal_cleans_service: TerminalCleansService
    turnover_service: TurnoverService
    turnover_label_service: TurnoverLabelService
    turnover_label_assoc_service: TurnoverLabelAssocService
    turnover_note_service: TurnoverNoteService
    schedule_assistant_email_service: ScheduleAssistantEmailService
    case_to_block_service: CaseToBlockService
    block_utilization_service: BlockUtilizationService
    custom_phase_config_service: CustomPhaseConfigService
    case_label_service: CaseLabelService


# Instead of passing all the loaders and stores to all the different parts of the schema,
# we can pass this context object.
@dataclass
class Context(AppServices):
    user_loader: UserLoader
    org_loader: OrganizationLoader
    org_sites_loader: OrganizationSitesLoader
    staffing_needs_roles_loader: StaffingNeedsRolesLoader
    site_loader: SiteLoader
    room_loader: RoomLoader
    case_derived_properties_loader: CaseDerivedPropertiesLoader
    camera_loader: CameraLoader
    default_camera_loader: DefaultCameraLoader
    case_loader: CaseLoader
    parent_phase_loader: ParentPhaseLoader
    child_phase_loader: ChildPhaseLoader
    phase_loader: PhaseLoader
    phase_type_loader: PhaseTypeLoader
    latest_image_loader: LatestImageByCameraIdLoader
    highlight_loader: HighlightLoader
    highlight_feedback_loader: HighlightFeedbackLoader
    patient_loader: PatientLoader
    playlist_loader: PlaylistLoader
    event_attrs_loader: EventAttrsLoader
    event_loader: EventLoader
    room_closure_loader: RoomClosureLoader
    site_closure_loader: SiteClosureLoader
    room_events_loader: RoomEventsLoader
    room_apella_case_loader: RoomApellaCaseLoader
    room_cameras_loader: RoomCamerasLoader
    room_tag_loader: RoomTagLoader
    annotation_task_type_loader: AnnotationTaskTypeLoader
    remaining_annotation_tasks_loader: RemainingAnnotationTasksLoader
    staff_event_notification_contact_information_loader: (
        StaffEventNotificationContactInformationLoader
    )
    staff_event_notification_subscription_loader: StaffEventNotificationSubscriptionLoader
    contact_information_loader: ContactInformationLoader
    staff_loader: StaffLoader
    case_note_plan_loader: CaseNotePlanLoader
    case_staff_loader: CaseStaffLoader
    case_staff_plan_loader: CaseStaffPlanLoader
    procedure_loader: ProcedureLoader
    measurement_period_loader: MeasurementPeriodLoader
    case_procedure_loader: CaseProcedureLoader
    primary_case_procedure_loader: PrimaryCaseProcedureLoader
    event_notification_loader: EventNotificationLoader
    staff_event_notification_loader: StaffEventNotificationLoader
    observation_loader: ObservationLoader
    observation_type_loader: ObservationTypeLoader
    block_loader: BlockLoader
    staff_block_loader: StaffBlockLoader
    block_time_loader: BlockTimeLoader
    block_time_for_block_loader: BlockTimeForBlockLoader
    block_time_release_for_block_time_loader: BlockTimeReleaseForBlockTimeLoader
    room_block_time_loader: RoomBlockTimeLoader
    board_rooms_loader: BoardRoomsLoader
    site_rooms_loader: SiteRoomsLoader
    anesthesia_loader: AnesthesiaLoader
    contact_information_subscription_loader: ContactInformationSubscriptionLoader
    case_flag_loader: CaseFlagLoader
    service_line_loader: ServiceLineLoader
    case_classification_type_loader: CaseClassificationTypeLoader
    matching_status_reason_loader: MatchingStatusReasonLoader
    cluster_loader: ClusterLoader
    cluster_mapping_loader: ClusterMappingLoader
    turnover_goals_loader: TurnoverGoalsLoader
    event_notification_subscription_loader: EventNotificationSubscriptionLoader
    room_prime_time_config_loader: RoomPrimeTimeConfigLoader
    room_first_case_config_loader: RoomFirstCaseConfigLoader
    site_prime_time_config_loader: SitePrimeTimeConfigLoader
    site_first_case_config_loader: SiteFirstCaseConfigLoader
    site_capacity_constraint_loader: SiteCapacityConstraintLoader
    turnover_label_loader: TurnoverLabelLoader
    turnover_note_loader: TurnoverNoteLoader
    case_label_loader: CaseLabelLoader


# A request specific context to forward for graphql.
@dataclass
class RequestContext(Context):
    request: Request
    background: BackgroundTasks

    def get(self, key: str) -> Any:
        """
        Graphene v3 treats the context object as a dict instead of a dataclass. We, however, access the context as a
        dataclass. In order to get this working we need to provide a wrapper function for get. Ideally we shouldn't
        need the context and generate the needed dependencies at request level.
        """
        return getattr(self, key)


class GrapheneInfo(GraphQLSchema):
    """
    Derived Graphene GrapheneInfo class for type hinting.
    """

    context: Context
