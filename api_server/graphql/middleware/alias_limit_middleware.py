import inspect
from graphql import GraphQLError
from graphql.language.ast import (
    FieldNode,
    OperationDefinitionNode,
    SelectionSetNode,
)


MAX_NUM_ALIASES = 15


class AliasLimitMiddleware:
    """
    Middleware to prevent GraphQL Alias Overloading DoS attacks by limiting
    the number of aliases allowed in a single query.

    GraphQL aliases allow clients to request the same field multiple times
    with different names, which can be abused to create resource-intensive
    queries that overwhelm the server.
    """

    async def resolve(self, next, root, info, **kwargs):
        """
        Check if the query exceeds the maximum allowed aliases before resolving.
        """
        # Only check on the root level to avoid checking the same query multiple times
        if root is None and hasattr(info, "operation"):
            alias_count = self._count_aliases(info.operation)
            if alias_count > MAX_NUM_ALIASES:
                raise GraphQLError(
                    f"Query contains {alias_count} aliases, which exceeds the maximum allowed limit of {MAX_NUM_ALIASES}. "
                    f"This restriction helps prevent resource exhaustion attacks."
                )

        # Continue with the next middleware/resolver
        return_value = next(root, info, **kwargs)
        if inspect.isawaitable(return_value):
            return await return_value
        return return_value

    def _count_aliases(self, operation: OperationDefinitionNode) -> int:
        """
        Count the total number of aliases in a GraphQL operation.

        Args:
            operation: The GraphQL operation definition node

        Returns:
            Total count of aliases in the operation
        """
        if not operation.selection_set:
            return 0

        return self._count_aliases_in_selection_set(operation.selection_set)

    def _count_aliases_in_selection_set(self, selection_set: SelectionSetNode) -> int:
        """
        Recursively count aliases in a selection set.

        Args:
            selection_set: The selection set to analyze

        Returns:
            Total count of aliases in the selection set
        """
        alias_count = 0

        for selection in selection_set.selections:
            if isinstance(selection, FieldNode):
                # If the field has an alias, count it
                if selection.alias:
                    alias_count += 1

                # Recursively check nested selection sets
                if selection.selection_set:
                    alias_count += self._count_aliases_in_selection_set(selection.selection_set)

            # Handle inline fragments and fragment spreads
            elif hasattr(selection, "selection_set") and selection.selection_set:
                alias_count += self._count_aliases_in_selection_set(selection.selection_set)

        return alias_count
