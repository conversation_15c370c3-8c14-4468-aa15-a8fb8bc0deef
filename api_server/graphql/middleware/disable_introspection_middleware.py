# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs
import inspect

import graphql

from config import introspection_enable


class DisableIntrospectionMiddleware(object):
    async def handle_next(self, next, root, info, **kwargs):
        """
        Check if the resolver is awaitable and await otherwise execute sync.
        We may have some resolvers synchronous in cases of attribute name differences or other cases.
        """
        return_value = next(root, info, **kwargs)
        if inspect.isawaitable(return_value):
            return await return_value
        return return_value

    async def resolve(self, next, root, info, **kwargs):
        # If introspection_enable is not enabled and in the query they ask for introspection
        #  fields block the query, else continue to resolve fields
        if introspection_enable() is False and info.field_name.lower() in [
            "__schema",
            "__introspection",
        ]:
            raise graphql.GraphQLError("Not authorized to query introspection")
        return await self.handle_next(next, root, info, **kwargs)
