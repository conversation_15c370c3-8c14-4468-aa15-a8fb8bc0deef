"""
The code in this directory is forked from here:
https://github.com/saltycrane/graphene-relay-pagination-example/tree/artsy-example/graphene-api/artsy_relay_pagination

It provides a PaginationConnection and PaginationConnectionField, which can be added to any
ObjectType to enable pagination.

When querying a paginated object, a PageCursors field will be put on the Connection, see
fields.PageCursors for documentation
"""

from .exceptions import PaginationException  # noqa F401

# Exported for public API
from .fields import PaginationConnection  # noqa F401
from .fields import PaginationConnectionField  # noqa F401
