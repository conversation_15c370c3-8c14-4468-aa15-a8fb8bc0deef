from typing import Any, Mapping, Optional, Sequence, Type, TypeVar

import graphene

from .connection_helpers import convert_connection_args_to_page_options
from .paging_helpers import create_page_cursors


class PageCursor(graphene.ObjectType):
    """
    Information about a single page

    Attributes:
        cursor      An opaque string that can be used to fetch this page using either the
                    `first 5after <cursor>` or `last 5 before <cursor>` conventions described in the
                    Relay spec here: https://relay.dev/graphql/connections.htm
        is_current  True if this page is the currently active page
        page        The page number
    """

    cursor = graphene.String(required=True)
    is_current = graphene.Boolean(required=True)
    page = graphene.Int(required=True)


class PageCursors(graphene.ObjectType):
    """
    All the information about the pages for a connection. The `around` attribute will always be
    included but the others may be omitted. This is to make client implementation of pagination
    simpler, as it can ignore the logic of what page options need to be shown and just check for
    existence of the various keys.

    Attributes:
        around      A list of cursors around and including the current page.
        first       The first page cursor. If this is included in `around`, it is omitted.
        last        The last page cursor. If this is included in `around`, it is omitted.
        next        The cursor for the next page. If there is only one page, it is omitted.
        previous    The cursor for the previous page. If there is only one page, it is omitted.
    """

    around = graphene.List(graphene.NonNull(PageCursor))
    first = graphene.Field(PageCursor)
    last = graphene.Field(PageCursor)
    next = graphene.Field(PageCursor)
    previous = graphene.Field(PageCursor)


class PaginationConnection(graphene.Connection):
    class Meta:
        abstract = True

    page_cursors = graphene.Field(PageCursors)
    total_records = graphene.Int()


T = TypeVar("T")


class PaginationConnectionField(graphene.ConnectionField):
    @classmethod
    def resolve_connection(
        cls, connection_type: Type[T], args: Mapping[Any, Any], iterable: Sequence[Any]
    ) -> T:
        connection = super(PaginationConnectionField, cls).resolve_connection(
            connection_type, args, iterable
        )

        page_options = convert_connection_args_to_page_options(args)
        page_cursors = create_page_cursors(page_options, len(iterable))
        connection.page_cursors = page_cursors
        connection.total_records = len(iterable)

        return connection


GrapheneModelType = TypeVar("GrapheneModelType", bound=graphene.ObjectType)


def pagination_connection_factory(
    model_type: Type[GrapheneModelType], model_name: Optional[str] = None
) -> Type[PaginationConnection]:
    model_name = model_name or model_type.__name__

    class ModelConnection(PaginationConnection):
        class Meta:
            node = model_type
            name = f"{model_name}Connection"

        class ModelEdge(graphene.ObjectType):
            node = graphene.Field(model_type, required=True)
            cursor = graphene.String(required=True)

            class Meta:
                name = f"{model_name}Edge"

        edges = graphene.List(graphene.NonNull(ModelEdge), required=True)

    return ModelConnection
