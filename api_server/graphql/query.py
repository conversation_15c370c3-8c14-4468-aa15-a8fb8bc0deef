# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs
from datetime import date
from typing import Any, List, Optional, Sequence, Union
from api_server.services.case_duration.case_duration_store import ProcedureOption, SurgeonOption
from graphql import GraphQLError
import aiohttp

from api_server.services.block_utilization.graphql.block_utilization import (
    BlockUtilization,
    BlockUtilizationInput,
)
from api_server.services.case_forecasts.case_forecast_store import (
    ForecastQuery,
    get_case_forecast_status_enum_from_value,
    CaseForecastModel,
)
from api_server.services.case_forecasts.graphql.case_forecast import (
    CaseForecastConnection,
    CaseForecastQueryInput,
)
from api_server.services.case_to_block.case_to_block_service import CaseToBlockOverridesQuery
from api_server.services.case_to_block.graphql.case_to_block import (
    CaseToBlockConnection,
    CaseToBlockInput,
)
from api_server.services.case_to_block.graphql.case_to_block_override import (
    CaseToBlockOverride,
    CaseToBlockOverrideInput,
)
from api_server.services.closures.graphql.site_closure import (
    SiteClosureQueryInput,
    SiteClosureConnection,
)
from api_server.services.available_time_slot.available_time_slot_service import (
    AvailableTimeSlotQueryDto,
)
from api_server.services.available_time_slot.graphql.available_time_slots import (
    AvailableTimeSlot,
    AvailableTimeSlotQueryInput,
)
from api_server.services.contact_information.staff_event_notification_contact_information_store import (
    StaffEventNotificationContactInformationModel,
)
from api_server.services.events.graphql.event_dashboard_visibility import EventDashboardVisibility
from api_server.services.room.graphql.room_tag import RoomTagConnection
from api_server.services.terminal_cleans.graphql.terminal_clean_score import TerminalCleanScore
import graphene

from apella_cloud_api.dtos import (
    AvailableSlot,
    BlockUtilizationDto,
    BoardConfigQueryDto,
    CaseSearchDto,
    ContactInformationQueryDto,
)
from api_server.graphql.context import GrapheneInfo
from api_server.graphql.sorting import SortedPaginationConnectionField
from api_server.services.anesthesia.graphql.anesthesia import (
    AnesthesiaConnection,
    AnesthesiaQueryInput,
)
from api_server.services.annotation_tasks.annotation_task_models import (
    TaskQuery,
    get_task_status_enum,
)
from api_server.services.annotation_tasks.graphql.annotation_task_query_input import (
    AnnotationTaskQueryInput,
)
from api_server.services.annotation_tasks.graphql.annotation_tasks import (
    AnnotationTask,
    AnnotationTaskConnection,
    AnnotationTaskType,
)
from api_server.services.apella_case.apella_case_store import ApellaCaseQuery
from api_server.services.terminal_cleans.terminal_cleans_store import (
    TerminalCleanScore as TerminalCleanScoreModel,
)
from api_server.services.apella_case.graphql.apella_case import (
    ApellaCaseConnection,
    ApellaCaseQueryInput,
)
from api_server.services.block.graphql.blocks import (
    Block,
    BlockConnection,
    BlockModel,
    BlockQueryInput,
    BlockTime,
    BlockTimeConnection,
    BlockTimeModel,
    BlockTimeQueryInput,
    BlockTimeAvailableIntervalInput,
    BlockTimesBulkQueryInput,
    BlockTimeAvailableInterval,
    BlockTimeAvailableIntervalConnection,
)
from api_server.services.boards.graphql.board_config import (
    BoardConfigConnection,
    BoardConfigModel,
    BoardConfigQueryInput,
)
from api_server.services.camera.graphql.camera import Camera, CameraConnection
from api_server.services.case.case_service import CaseService
from api_server.services.case.case_status import SCHEDULED
from api_server.services.case.graphql.scheduled_case import (
    CaseClassificationType,
    CaseHistoryQueryInput,
    ScheduledCaseConnection,
    ScheduledCaseQueryInput,
)
from api_server.services.case_duration.case_duration_service import (
    DurationPredictions,
    TurnoverPrediction,
)
from api_server.services.case_duration.graphql.case_duration import (
    CaseDurationPredictionQueryInput,
    CaseDurationPredictions,
    CaseDurationProcedureOptionConnection,
    CaseDurationProceduresQueryInput,
    CaseDurationSurgeonOptionConnection,
    CaseDurationSurgeonProcedureMappingConnection,
    CaseDurationSurgeonsQueryInput,
    CaseDurationTurnoverPrediction,
)
from api_server.services.cluster.graphql.cluster_gql_models import Cluster, ClusterConnection
from api_server.services.contact_information.contact_information_store import (
    StaffEventNotificationQueryDto,
    ContactInformationModel,
)
from api_server.services.contact_information.graphql.contact_information import (
    ContactInformationConnection,
    ContactInformationSearchInput,
    StaffEventNotificationContactInformationConnection,
    StaffEventNotificationContactInformationSearchInput,
)
from api_server.services.custom_phase_config.graphql.custom_phase_config import CustomPhaseConfig
from api_server.services.ehr_interfaces.ehr_interfaces_models import (
    CaseEhrMessagesQuery,
)
from api_server.services.ehr_interfaces.graphql.case_ehr_message import (
    CaseEhrMessageConnection,
    CaseEhrMessageQueryInput,
)
from api_server.services.events.event_models import EventLabelOptionModel
from api_server.services.events.graphql.event import (
    Event,
    EventConnection,
    EventType,
    EventTypeConnection,
)
from api_server.services.events.graphql.event_label_option import EventLabelOption
from api_server.services.events.graphql.event_search_input import (
    EventHistorySearchInput,
    EventSearchInput,
)
from api_server.services.highlights import highlight_store
from api_server.services.highlights.graphql.highlight import (
    Highlight,
    HighlightConnection,
)
from api_server.services.highlights.graphql.highlight_feedback import (
    HighlightFeedbackConnection,
)
from api_server.services.highlights.graphql.highlight_feedback_search_input import (
    HighlightFeedbackSearchInput,
)
from api_server.services.highlights.graphql.highlight_input import HighlightInput
from api_server.services.highlights.graphql.highlight_search_input import (
    HighlightSearchInput,
)
from api_server.services.measurement_periods.graphql.measurement_periods import (
    MeasurementPeriod,
    MeasurementPeriodConnection,
    MeasurementPeriodQueryInput,
)
from api_server.services.media.graphql.latest_image import (
    CameraLatestImageConnection,
    LiveCameraImagesInput,
)
from api_server.services.objects.graphql.objects import ObjectMetrics
from api_server.services.observations.graphql.observation import (
    ObservationConnection,
    ObservationTypeName,
)
from api_server.services.observations.graphql.observation_search_input import (
    ObservationSearchInput,
    ObservationTypeNamesInput,
    ObservationTypeNamesInputForCustomPhases,
)
from api_server.services.observations.observation_store import (
    ObservationQuery,
)
from api_server.services.organization.graphql.organization import (
    Organization,
    OrganizationConnection,
)
from api_server.services.organization.organization_db import (
    Organization as OrganizationModel,
)
from api_server.services.phases.graphql.phase import PhaseConnection, PhaseQueryInput
from api_server.services.phases.graphql.phase_type import (
    PhaseTypeConnection,
    PhaseTypeRecord,
)
from api_server.services.phases.phase_store import (
    PhaseQuery,
    get_phase_status_enum_from_value,
)
from api_server.services.phases.phases_models import get_phase_type_enum_from_value
from api_server.services.procedures.graphql.procedure import (
    ProcedureConnection,
    ProcedureQueryInput,
)
from api_server.services.room.graphql.room import Room, RoomConnection
from api_server.services.closures.graphql.room_closure import (
    RoomClosureConnection,
    RoomClosureQueryInput,
)
from api_server.services.service_lines.graphql.service_line import (
    ServiceLine,
    ServiceLineQueryInput,
)
from api_server.services.site.graphql.site import Site, SiteConnection
from api_server.services.site.site_models import SiteQuery
from api_server.services.staff.graphql.staff import StaffConnection, StaffQueryInput
from api_server.services.staff.staff_models import StaffCodeQuery
from api_server.services.staff_role.graphql.staff_role import StaffRole
from api_server.services.staff_role.staff_role_store import StaffRoleModel
from api_server.services.turnover.graphql.turnover_label import (
    GQLTurnoverLabelQueryInput,
    TurnoverLabel,
)
from api_server.services.turnover.turnover_label_store import TurnoverLabelModel
from api_server.services.user_filter_views.graphql.user_filter_views import (
    UserFilterView,
    UserFilterViewQueryInput,
)
from api_server.services.user_filter_views.user_filter_view_model import (
    UserFilterViewModel,
)
from api_server.services.users.graphql.user_type import User, UserConnection
from api_server.services.users.graphql.users_search_input import UsersSearchInput


class Query(graphene.ObjectType):
    """
    This is the root of the graphql query schema.  Each of these queries
    represents an 'entrypoint' to the data.  You can think of each of these
    'entrypoints' as equivalent to a REST GET endpoint.  Each of these returns
    an object that can then be further traversed in the query statement.
    """

    me = graphene.Field(User)
    user = graphene.Field(User, user_id=graphene.String(required=True, name="id"))
    users = SortedPaginationConnectionField(
        lambda: UserConnection,
        query=graphene.Argument(UsersSearchInput, required=True),
        required=True,
    )
    organization = graphene.Field(Organization, org_id=graphene.String(required=True, name="id"))
    organizations = SortedPaginationConnectionField(
        lambda: OrganizationConnection,
        organization_ids=graphene.List(graphene.NonNull(graphene.String), default_value=None),
        required=True,
    )
    site = graphene.Field(Site, site_id=graphene.String(required=True, name="id"))
    sites = SortedPaginationConnectionField(
        lambda: SiteConnection,
        site_ids=graphene.List(graphene.NonNull(graphene.String), default_value=None),
        organization_ids=graphene.List(graphene.NonNull(graphene.String), default_value=None),
        required=True,
    )
    room = graphene.Field(Room, room_id=graphene.String(required=True, name="id"))
    rooms = SortedPaginationConnectionField(
        lambda: RoomConnection,
        ids=graphene.List(graphene.NonNull(graphene.String)),
        site_id=graphene.String(required=False),
        org_id=graphene.String(required=False),
        required=True,
    )

    room_closures = SortedPaginationConnectionField(
        lambda: RoomClosureConnection,
        query=graphene.Argument(RoomClosureQueryInput, required=True),
        required=True,
    )

    site_closures = SortedPaginationConnectionField(
        lambda: SiteClosureConnection,
        query=graphene.Argument(SiteClosureQueryInput, required=True),
        required=True,
    )

    room_tags = SortedPaginationConnectionField(
        lambda: RoomTagConnection,
        ids=graphene.List(graphene.NonNull(graphene.String)),
        org_id=graphene.String(required=False),
        required=True,
    )

    camera = graphene.Field(Camera, camera_id=graphene.String(required=True, name="id"))
    cameras = SortedPaginationConnectionField(
        lambda: CameraConnection,
        ids=graphene.List(graphene.NonNull(graphene.String)),
        families=graphene.List(graphene.NonNull(graphene.String)),
        room_ids=graphene.List(graphene.NonNull(graphene.String)),
        site_ids=graphene.List(graphene.NonNull(graphene.String)),
        organization_id=graphene.String(required=False),
        required=True,
    )
    cluster = graphene.Field(Cluster, cluster_id=graphene.ID(required=True, name="id"))
    clusters = SortedPaginationConnectionField(
        lambda: ClusterConnection,
        ids=graphene.List(graphene.NonNull(graphene.String)),
        required=False,
    )
    event = graphene.Field(Event, event_id=graphene.String(required=True, name="id"))
    event_search = SortedPaginationConnectionField(
        lambda: EventConnection,
        query=graphene.Argument(EventSearchInput, required=True),
        required=True,
    )
    event_history_search = SortedPaginationConnectionField(
        lambda: EventConnection,
        query=graphene.Argument(EventHistorySearchInput, required=True),
        required=True,
    )
    event_label_options = graphene.List(graphene.NonNull(EventLabelOption), required=True)
    event_type = graphene.Field(EventType, event_type_id=graphene.String(required=True, name="id"))
    event_types = SortedPaginationConnectionField(
        lambda: EventTypeConnection,
        types=graphene.List(graphene.NonNull(graphene.String), default_value=[]),
        ids=graphene.List(graphene.NonNull(graphene.String), default_value=[]),
    )
    highlight = graphene.Field(Highlight, highlight_id=graphene.String(required=True, name="id"))
    highlights = SortedPaginationConnectionField(
        lambda: HighlightConnection,
        query=graphene.Argument(HighlightInput),
        required=True,
    )
    highlight_search = SortedPaginationConnectionField(
        lambda: HighlightConnection,
        query=graphene.Argument(HighlightSearchInput, required=True),
        required=True,
    )
    highlight_feedback_search = SortedPaginationConnectionField(
        lambda: HighlightFeedbackConnection,
        query=graphene.Argument(HighlightFeedbackSearchInput, required=True),
        required=True,
    )
    annotation_task = graphene.Field(
        AnnotationTask, annotation_task_id=graphene.ID(required=True, name="id")
    )
    annotation_tasks = SortedPaginationConnectionField(
        lambda: AnnotationTaskConnection,
        query=graphene.Argument(AnnotationTaskQueryInput, required=True),
        required=True,
    )
    annotation_task_type = graphene.Field(AnnotationTaskType, id=graphene.ID(required=True))
    annotation_task_types = graphene.List(
        graphene.NonNull(AnnotationTaskType),
        ids=graphene.List(
            graphene.NonNull(graphene.String),
            default_value=[],
        ),
    )
    phases = SortedPaginationConnectionField(
        lambda: PhaseConnection,
        query=graphene.Argument(PhaseQueryInput, required=True),
        required=True,
    )
    apella_cases = SortedPaginationConnectionField(
        lambda: ApellaCaseConnection,
        query=graphene.Argument(ApellaCaseQueryInput, required=True),
        required=True,
    )
    phase_type = graphene.Field(PhaseTypeRecord, id=graphene.String(required=True))
    phase_types = SortedPaginationConnectionField(
        lambda: PhaseTypeConnection,
        ids=graphene.List(graphene.NonNull(graphene.String), default_value=[]),
        required=True,
    )
    object_metrics = graphene.Field(lambda: ObjectMetrics)
    # TODO RT-260: deprecate in favor of `cases` endpoint
    scheduled_cases = SortedPaginationConnectionField(
        lambda: ScheduledCaseConnection,
        query=graphene.Argument(ScheduledCaseQueryInput, required=True),
        required=True,
    )
    cases = SortedPaginationConnectionField(
        lambda: ScheduledCaseConnection,
        query=graphene.Argument(ScheduledCaseQueryInput, required=True),
        required=True,
    )
    cases_history = SortedPaginationConnectionField(
        lambda: ScheduledCaseConnection,
        query=graphene.Argument(CaseHistoryQueryInput, required=True),
        required=True,
    )
    case_ehr_messages = SortedPaginationConnectionField(
        lambda: CaseEhrMessageConnection,
        query=graphene.Argument(CaseEhrMessageQueryInput, required=True),
        required=True,
    )
    case_classification_types = graphene.List(
        graphene.NonNull(CaseClassificationType),
        ids=graphene.List(
            graphene.NonNull(graphene.String),
            default_value=None,
        ),
    )
    live_camera_images = SortedPaginationConnectionField(
        lambda: CameraLatestImageConnection,
        query=graphene.Argument(LiveCameraImagesInput),
        required=True,
    )
    service_lines = graphene.List(
        graphene.NonNull(ServiceLine),
        query=graphene.Argument(ServiceLineQueryInput, required=True),
    )
    procedures = SortedPaginationConnectionField(
        lambda: ProcedureConnection,
        query=graphene.Argument(ProcedureQueryInput, required=True),
        required=True,
    )
    anesthesias = SortedPaginationConnectionField(
        lambda: AnesthesiaConnection,
        query=graphene.Argument(AnesthesiaQueryInput, required=True),
        required=True,
    )
    measurement_period = graphene.Field(
        MeasurementPeriod, measurement_period_id=graphene.String(required=True, name="id")
    )
    measurement_periods = SortedPaginationConnectionField(
        lambda: MeasurementPeriodConnection,
        query=graphene.Argument(MeasurementPeriodQueryInput, required=True),
        required=True,
    )
    staff = SortedPaginationConnectionField(
        lambda: StaffConnection,
        query=graphene.Argument(StaffQueryInput, required=True),
        required=True,
    )
    observation = SortedPaginationConnectionField(
        lambda: ObservationConnection,
        query=graphene.Argument(ObservationSearchInput, required=True),
        required=True,
    )
    observation_type_names = graphene.List(
        ObservationTypeName,
        query=graphene.Argument(ObservationTypeNamesInput, required=True),
        required=True,
    )

    observation_type_names_for_custom_phases = graphene.List(
        ObservationTypeName,
        query=graphene.Argument(ObservationTypeNamesInputForCustomPhases, required=True),
        required=True,
    )

    contact_information = SortedPaginationConnectionField(
        lambda: ContactInformationConnection,
        required=True,
        query=graphene.Argument(ContactInformationSearchInput, required=True),
    )
    staff_event_notification_contact_information = SortedPaginationConnectionField(
        lambda: StaffEventNotificationContactInformationConnection,
        required=True,
        query=graphene.Argument(StaffEventNotificationContactInformationSearchInput, required=True),
    )
    staffing_needs_roles = graphene.NonNull(graphene.List(graphene.NonNull(StaffRole)))
    block = graphene.Field(Block, id=graphene.ID(required=True, name="id"))
    blocks = SortedPaginationConnectionField(
        lambda: BlockConnection,
        required=True,
        query=graphene.Argument(BlockQueryInput, required=True),
    )
    block_time = graphene.Field(BlockTime, id=graphene.ID(required=True, name="id"))
    block_times_bulk = graphene.NonNull(
        graphene.List(graphene.NonNull(BlockTime)),
        query=graphene.Argument(BlockTimesBulkQueryInput, required=True),
    )
    block_times = SortedPaginationConnectionField(
        lambda: BlockTimeConnection,
        required=True,
        query=graphene.Argument(BlockTimeQueryInput, required=True),
    )
    block_times_available_intervals = SortedPaginationConnectionField(
        lambda: BlockTimeAvailableIntervalConnection,
        required=True,
        query=graphene.Argument(BlockTimeAvailableIntervalInput, required=True),
    )
    board_configs = SortedPaginationConnectionField(
        lambda: BoardConfigConnection,
        required=True,
        query=graphene.Argument(BoardConfigQueryInput, required=True),
    )
    case_duration_surgeons_and_procedures = SortedPaginationConnectionField(
        lambda: CaseDurationSurgeonProcedureMappingConnection,
        required=True,
        deprecation_reason="This query is deprecated and will be removed in the future. This has been replaced by two separate queries: case_duration_surgeons and case_duration_procedures.",
    )
    case_duration_turnover_prediction = graphene.Field(
        CaseDurationTurnoverPrediction,
        query=graphene.Argument(CaseDurationPredictionQueryInput, required=True),
    )
    case_duration_predictions = graphene.Field(
        CaseDurationPredictions,
        query=graphene.Argument(CaseDurationPredictionQueryInput, required=True),
    )
    case_duration_surgeons = SortedPaginationConnectionField(
        lambda: CaseDurationSurgeonOptionConnection,
        required=True,
        query=graphene.Argument(CaseDurationSurgeonsQueryInput, required=True),
    )
    case_duration_procedures = SortedPaginationConnectionField(
        lambda: CaseDurationProcedureOptionConnection,
        required=True,
        query=graphene.Argument(CaseDurationProceduresQueryInput, required=True),
    )
    available_time_slots = graphene.List(
        graphene.NonNull(AvailableTimeSlot),
        query=graphene.Argument(AvailableTimeSlotQueryInput, required=True),
    )
    user_filter_views = graphene.List(
        graphene.NonNull(UserFilterView),
        query=graphene.Argument(UserFilterViewQueryInput, required=True),
    )
    case_forecasts = SortedPaginationConnectionField(
        lambda: CaseForecastConnection,
        required=True,
        query=graphene.Argument(CaseForecastQueryInput, required=True),
    )
    dashboard_events = graphene.List(graphene.NonNull(EventDashboardVisibility), required=True)
    turnover_labels = graphene.NonNull(
        graphene.List(graphene.NonNull(TurnoverLabel)),
        query=graphene.Argument(GQLTurnoverLabelQueryInput, required=False),
    )
    cases_to_blocks = SortedPaginationConnectionField(
        lambda: CaseToBlockConnection,
        query=graphene.Argument(CaseToBlockInput, required=True),
        required=True,
    )
    case_to_block_overrides = graphene.List(
        graphene.NonNull(CaseToBlockOverride),
        query=graphene.Argument(CaseToBlockOverrideInput, required=True),
    )
    block_utilizations = graphene.List(
        graphene.NonNull(BlockUtilization),
        query=graphene.Argument(BlockUtilizationInput, required=True),
    )
    custom_phase_configs = graphene.NonNull(graphene.List(graphene.NonNull(CustomPhaseConfig)))

    terminal_clean_score = graphene.Field(
        TerminalCleanScore, room_id=graphene.ID(required=True), date=graphene.Date(required=True)
    )

    @staticmethod
    async def resolve_dashboard_events(parent, info: GrapheneInfo):
        return await info.context.event_service.query_event_dashboard_visiblity()

    @staticmethod
    async def resolve_me(parent, info: GrapheneInfo):
        return await info.context.user_service.get_me()

    @staticmethod
    async def resolve_user(parent, info: GrapheneInfo, user_id):
        return await info.context.user_loader.load(user_id)

    @staticmethod
    async def resolve_users(
        parent,
        info: GrapheneInfo,
        query,
        before=None,
        after=None,
        first=None,
        last=None,
        *args: Any,
        **kwargs: Any,
    ):
        return await info.context.user_service.query_users(query.role)

    @staticmethod
    async def resolve_organization(parent, info: GrapheneInfo, org_id):
        return await info.context.org_loader.load(org_id)

    @staticmethod
    async def resolve_site(parent, info: GrapheneInfo, site_id):
        return await info.context.site_loader.load(site_id)

    @staticmethod
    async def resolve_room(parent, info: GrapheneInfo, room_id):
        return await info.context.room_loader.load(room_id)

    @staticmethod
    async def resolve_camera(parent, info: GrapheneInfo, camera_id):
        return await info.context.camera_loader.load(camera_id)

    @staticmethod
    async def resolve_cluster(parent, info: GrapheneInfo, cluster_id):
        return await info.context.cluster_loader.load(cluster_id)

    @staticmethod
    async def resolve_event(parent, info: GrapheneInfo, event_id):
        return await info.context.event_service.get_event(event_id=event_id)

    @staticmethod
    async def resolve_event_search(
        parent, info: GrapheneInfo, query: EventSearchInput, *args: Any, **kwargs: Any
    ):
        if query.include_dashboard_events_only:
            org_id = info.context.auth.get_calling_org_id()
            org_dashboard_events = await info.context.event_service.query_event_dashboard_visiblity(
                org_id
            )
            org_dashboard_event_names = [e.event_type_id for e in org_dashboard_events]
            query.event_names = org_dashboard_event_names

        return await info.context.event_service.query_events(query.to_query_dto())

    @staticmethod
    async def resolve_event_history_search(
        parent, info: GrapheneInfo, query: EventHistorySearchInput, *args: Any, **kwargs: Any
    ):
        return await info.context.event_service.query_event_history(query.to_query_dto())

    @staticmethod
    async def resolve_event_type(parent, info: GrapheneInfo, event_type_id):
        return await info.context.event_service.get_event_type(event_type_id=event_type_id)

    @staticmethod
    async def resolve_event_types(
        parent, info: GrapheneInfo, types: List[str], ids: List[str], **kwargs
    ):
        return await info.context.event_service.get_event_types(types, ids)

    @staticmethod
    async def resolve_highlight(
        parent, info: GrapheneInfo, highlight_id, *args: Any, **kwargs: Any
    ) -> Optional[highlight_store.Highlight]:
        return await info.context.highlight_loader.load(highlight_id)

    @staticmethod
    def resolve_highlights(parent, info: GrapheneInfo, query=None, *args: Any, **kwargs: Any):
        if query:
            return info.context.highlight_service.query_users_highlights(
                user_id=info.context.auth.get_calling_user_id(),
                status=query.status,
                min_time=query.min_time,
                max_time=query.max_time,
            )
        return info.context.highlight_service.query_users_highlights(
            user_id=info.context.auth.get_calling_user_id(),
        )

    @staticmethod
    def resolve_highlight_search(parent, info: GrapheneInfo, query, **kwargs):
        return info.context.highlight_service.query_highlights(
            min_time=query.min_time,
            max_time=query.max_time,
            org_ids=query.organization_ids,
            room_ids=query.room_ids,
            site_ids=query.site_ids,
            categories=query.categories,
            assigned_user_ids=query.assigned_user_ids,
            feedback_status=query.feedback_status,
        )

    @staticmethod
    def resolve_highlight_feedback_search(parent, info: GrapheneInfo, query, **kwargs):
        return info.context.highlight_service.query_feedback(
            min_time=query.min_time, max_time=query.max_time
        )

    @staticmethod
    async def resolve_annotation_task(
        parent, info: GrapheneInfo, annotation_task_id: str, *args: Any, **kwargs: Any
    ):
        return await info.context.annotation_task_service.get_task(
            annotation_task_id=annotation_task_id
        )

    @staticmethod
    async def resolve_annotation_tasks(
        parent, info: GrapheneInfo, query: AnnotationTaskQueryInput, *args: Any, **kwargs: Any
    ):
        if query.statuses is not None:
            query.statuses = [get_task_status_enum(status) for status in query.statuses]

        task_query = TaskQuery(
            start_time=query.start_time,
            end_time=query.end_time,
            min_updated_time=query.min_updated_time,
            max_updated_time=query.max_updated_time,
            statuses=query.statuses,
            annotator_user_ids=query.annotator_user_ids,
            reviewer_user_ids=query.reviewer_user_ids,
            organization_id=query.organization_id,
            site_id=query.site_id,
            room_id=query.room_id,
            type_ids=query.type_ids,
        )
        return await info.context.annotation_task_service.query_tasks(task_query=task_query)

    @staticmethod
    async def resolve_organizations(
        parent,
        info: GrapheneInfo,
        organization_ids: Optional[List[str]] = None,
        *args: Any,
        **kwargs: Any,
    ) -> Union[list[OrganizationModel], list[Optional[OrganizationModel]]]:
        """
        Queries all the Organizations, regardless of organization membership in Auth0.
        """
        if organization_ids is None:
            return await info.context.organization_service.get_all_organizations()
        else:
            return await info.context.org_loader.load_many(organization_ids)

    @staticmethod
    async def resolve_sites(
        parent, info: GrapheneInfo, site_ids=None, organization_ids=None, *args: Any, **kwargs: Any
    ):
        site_query = SiteQuery(site_ids=site_ids, organization_ids=organization_ids)
        return await info.context.site_service.query_sites(site_query)

    @staticmethod
    async def resolve_rooms(
        parent,
        info: GrapheneInfo,
        ids=None,
        site_id=None,
        org_id=None,
        *args: Any,
        **kwargs: Any,
    ):
        return await info.context.room_service.query_rooms(
            room_ids=ids, site_id=site_id, org_id=org_id
        )

    @staticmethod
    async def resolve_room_closures(
        parent,
        info: GrapheneInfo,
        query: RoomClosureQueryInput,
        *args: Any,
        **kwargs: Any,
    ):
        return await info.context.closure_service.query_room_closures(
            query.to_query_dto(),
        )

    @staticmethod
    async def resolve_site_closures(
        parent,
        info: GrapheneInfo,
        query: SiteClosureQueryInput,
        *args: Any,
        **kwargs: Any,
    ):
        return await info.context.closure_service.query_site_closures(
            query.to_query_dto(),
        )

    @staticmethod
    async def resolve_room_tags(
        parent,
        info: GrapheneInfo,
        ids=None,
        site_ids=None,
        org_id=None,
        *args: Any,
        **kwargs: Any,
    ):
        return await info.context.room_service.query_room_tags(tag_ids=ids, org_id=org_id)

    @staticmethod
    async def resolve_cameras(
        parent,
        info: GrapheneInfo,
        ids: Optional[List[str]] = None,
        families: Optional[List[str]] = None,
        room_ids: Optional[List[str]] = None,
        site_ids: Optional[List[str]] = None,
        organization_id: Optional[str] = None,
        *args: Any,
        **kwargs: Any,
    ):
        return await info.context.camera_service.query_cameras(
            camera_ids=ids,
            families=families,
            room_ids=room_ids,
            site_ids=site_ids,
            org_id=organization_id,
        )

    @staticmethod
    async def resolve_clusters(
        parent,
        info: GrapheneInfo,
        ids: Optional[List[str]] = None,
        *args: Any,
        **kwargs: Any,
    ):
        return await info.context.cluster_service.get_clusters(ids=ids)

    @staticmethod
    def resolve_object_metrics(parent, info: GrapheneInfo, *args: Any, **kwargs: Any):
        # Graphene needs a non-None object to then fetch metrics from the subfields
        return []

    # TODO RT-260: deprecate in favor of `cases` endpoint
    @staticmethod
    async def resolve_scheduled_cases(
        parent, info: GrapheneInfo, query: ScheduledCaseQueryInput, *args: Any, **kwargs: Any
    ):
        query.status = SCHEDULED
        return await Query.resolve_cases(
            parent,
            info,
            query,
            *args,
            **kwargs,
        )

    @staticmethod
    async def resolve_cases(
        parent, info: GrapheneInfo, query: ScheduledCaseQueryInput, *args: Any, **kwargs: Any
    ):
        case_search_query = CaseSearchDto(
            min_start_time=query.min_start_time,
            max_start_time=query.max_start_time or query.max_time,
            min_end_time=query.min_end_time or query.min_time,
            max_end_time=query.max_end_time,
            room_ids=query.room_ids,
            site_ids=query.site_ids,
            status=query.status,
            case_classification_types_ids=query.case_classification_types_ids,
            min_updated_time=query.min_updated_time,
            max_updated_time=query.max_updated_time,
            min_created_time=query.min_created_time,
            max_created_time=query.max_created_time,
            staff_ids=query.staff_ids,
            procedure_ids=query.procedure_ids,
            is_add_ons=query.is_add_ons,
            case_flags=query.case_flags,
            case_ids=query.case_ids,
        )
        return await info.context.case_service.query_cases(search_query=case_search_query)

    @staticmethod
    async def resolve_cases_history(
        parent, info: GrapheneInfo, query: CaseHistoryQueryInput, *args: Any, **kwargs: Any
    ):
        case_search_query = CaseSearchDto(
            min_start_time=query.min_start_time,
            max_start_time=query.max_start_time or query.max_time,
            min_end_time=query.min_end_time or query.min_time,
            max_end_time=query.max_end_time,
            room_ids=query.room_ids,
            site_ids=query.site_ids,
            status=query.status,
            case_classification_types_ids=query.case_classification_types_ids,
            min_updated_time=query.min_updated_time,
            max_updated_time=query.max_updated_time,
            min_created_time=query.min_created_time,
            max_created_time=query.max_created_time,
            staff_ids=query.staff_ids,
            procedure_ids=query.procedure_ids,
            is_add_ons=query.is_add_ons,
            case_ids=query.case_ids,
        )
        return await info.context.case_service.query_cases_history(search_query=case_search_query)

    @staticmethod
    async def resolve_case_ehr_messages(
        parent, info: GrapheneInfo, query: CaseEhrMessageQueryInput, *args: Any, **kwargs: Any
    ):
        ehr_messages_query = CaseEhrMessagesQuery(
            org_id=query.org_id,
            min_event_time=query.min_event_time,
            max_event_time=query.max_event_time,
            case_id=query.case_id,
            event_types=query.event_types,
            raw_message_text_search=query.raw_message_text_search,
            no_case_id=query.no_case_id,
            latest_per_case_id=query.latest_per_case_id,
        )
        case_service: CaseService = info.context.case_service
        return await case_service.query_ehr_messages(ehr_messages_query=ehr_messages_query)

    @staticmethod
    async def resolve_case_classification_types(
        parent, info: GrapheneInfo, ids: Optional[List[str]] = None, *args: Any, **kwargs: Any
    ):
        return await info.context.case_service.get_case_classification_types(ids=ids)

    @staticmethod
    async def resolve_live_camera_images(
        parent, info: GrapheneInfo, query: LiveCameraImagesInput, *args: Any, **kwargs: Any
    ):
        return await info.context.latest_image_loader.load_many(query.camera_ids)

    @staticmethod
    async def resolve_service_lines(
        parent, info: GrapheneInfo, query: ServiceLineQueryInput, *args: Any, **kwargs: Any
    ):
        if query.ids:
            return await info.context.service_line_loader.load_many(query.ids)
        else:
            return await info.context.service_line_service.query_service_lines(org_id=query.org_id)

    @staticmethod
    async def resolve_procedures(
        parent, info: GrapheneInfo, query: ProcedureQueryInput, *args: Any, **kwargs: Any
    ):
        return await info.context.procedure_service.query_procedures(
            org_id=query.org_id, names=query.names, case_id=query.case_id, hierarchy=query.hierarchy
        )

    @staticmethod
    async def resolve_anesthesias(
        parent, info: GrapheneInfo, query: AnesthesiaQueryInput, *args, **kwargs
    ):
        return await info.context.anesthesia_service.query_anesthesias(
            org_id=query.org_id, names=query.names
        )

    @staticmethod
    async def resolve_measurement_period(parent, info: GrapheneInfo, measurement_period_id: str):
        return await info.context.measurement_period_loader.load(measurement_period_id)

    @staticmethod
    async def resolve_measurement_periods(
        parent, info: GrapheneInfo, query: MeasurementPeriodQueryInput, *args: Any, **kwargs: Any
    ):
        return await info.context.measurement_period_service.query_measurement_periods(
            site_id=query.site_id,
            names=query.names,
            measurement_period_start=query.measurement_period_start,
            measurement_period_end=query.measurement_period_end,
            annotation_task_type_id=query.annotation_task_type_id,
        )

    @staticmethod
    async def resolve_staff(
        parent, info: GrapheneInfo, query: StaffQueryInput, *args: Any, **kwargs: Any
    ):
        # Transform model
        staff_code_query: Optional[StaffCodeQuery] = None
        if query.staff_code is not None:
            staff_code_query = StaffCodeQuery(
                coding_system=query.staff_code.coding_system, code=query.staff_code.code
            )

        # Make request
        return await info.context.staff_service.query_staff(
            ids=query.ids,
            name=query.name,
            org_id=query.org_id,
            case_ids=[query.case_id] if query.case_id else None,
            staff_code=staff_code_query,
            only_primary_surgeons=query.only_primary_surgeons,
            staff_roles=query.staff_roles,
        )

    @staticmethod
    async def resolve_phases(
        parent, info: GrapheneInfo, query: PhaseQueryInput, *args: Any, **kwargs: Any
    ):
        query_type: Optional[str] = None
        if query.type is not None:
            query_type = get_phase_type_enum_from_value(query.type).name
        if query.statuses is not None:
            query.statuses = [get_phase_status_enum_from_value(status) for status in query.statuses]
        phase_query = PhaseQuery(
            org_id=query.organization_id,
            site_ids=query.site_ids,
            room_ids=query.room_ids,
            case_ids=query.case_ids,
            phase_type=query_type,
            min_time=query.min_time,
            max_time=query.max_time,
            min_start_time=query.min_start_time,
            max_start_time=query.max_start_time,
            min_end_time=query.min_end_time,
            max_end_time=query.max_end_time,
            min_updated_time=query.min_updated_time,
            max_updated_time=query.max_updated_time,
            min_created_time=query.min_created_time,
            max_created_time=query.max_created_time,
            show_human_ground_truth_data=query.show_human_ground_truth_data,
            max_duration=query.max_duration,
            statuses=query.statuses,
            source_type=query.source_type,
            ensure_phase_start_time_has_not_elapsed=query.ensure_phase_start_time_has_not_elapsed,
        )
        return await info.context.phase_service.query_phases(phase_query)

    @staticmethod
    async def resolve_apella_cases(
        parent, info: GrapheneInfo, query: ApellaCaseQueryInput, *args: Any, **kwargs: Any
    ):
        case_query = ApellaCaseQuery(
            org_id=query.organization_id,
            site_ids=query.site_ids,
            room_ids=query.room_ids,
            case_ids=query.case_ids,
            phase_ids=query.phase_ids,
            min_end_time=query.min_end_time,
            max_start_time=query.max_start_time,
            case_types=query.case_types,
            case_matching_statuses=query.case_matching_statuses,
            scheduled_case_status=query.scheduled_case_status,
            staff_ids=query.staff_ids,
        )
        return await info.context.apella_case_service.query_cases(query=case_query)

    @staticmethod
    async def resolve_observation(
        parent, info: GrapheneInfo, query: ObservationSearchInput, *args: Any, **kwargs: Any
    ):
        phase_query = ObservationQuery(
            org_id=query.organization_id,
            site_ids=query.site_ids,
            room_ids=query.room_ids,
            case_ids=query.case_ids,
            type_ids=query.type_ids,
            min_observation_time=query.min_observation_time,
            max_observation_time=query.max_observation_time,
            min_recorded_time=query.min_recorded_time,
            max_recorded_time=query.max_recorded_time,
        )
        return await info.context.observation_service.query_observations(phase_query)

    @staticmethod
    async def resolve_observation_type_names(
        parent, info: GrapheneInfo, query: ObservationTypeNamesInput, *args: Any, **kwargs: Any
    ):
        return await info.context.observation_service.get_observation_type_names(query)

    @staticmethod
    async def resolve_observation_type_names_for_custom_phases(
        parent,
        info: GrapheneInfo,
        query: ObservationTypeNamesInputForCustomPhases,
        *args: Any,
        **kwargs: Any,
    ):
        return await info.context.observation_service.get_observation_type_names_for_custom_phase(
            query
        )

    @staticmethod
    async def resolve_phase_type(parent, info: GrapheneInfo, id: str):
        return await info.context.phase_service.get_phase_type(type_id=id)

    @staticmethod
    async def resolve_phase_types(
        parent, info: GrapheneInfo, ids: List[str], *args: Any, **kwargs: Any
    ):
        return await info.context.phase_service.get_phase_types(type_ids=ids)

    @staticmethod
    async def resolve_annotation_task_type(parent, info: GrapheneInfo, id: graphene.String):
        return await info.context.annotation_task_service.get_annotation_task_type(id)

    @staticmethod
    async def resolve_annotation_task_types(parent, info: GrapheneInfo, ids: List[str]):
        return await info.context.annotation_task_service.get_annotation_task_types(ids)

    @staticmethod
    async def resolve_contact_information(
        parent, info: GrapheneInfo, query: ContactInformationSearchInput, *args: Any, **kwargs: Any
    ) -> Sequence[ContactInformationModel]:
        query_dto = ContactInformationQueryDto(
            ids=query.ids,
            staff_ids=query.staff_ids,
            case_ids=query.case_ids,
            contact_information_values=query.contact_information_values,
            types=query.types,
            event_type_ids=query.event_type_ids,
        )
        return await info.context.contact_information_service.query_contact_information(
            contact_information_query=query_dto
        )

    @staticmethod
    async def resolve_staff_event_notification_contact_information(
        parent,
        info: GrapheneInfo,
        query: StaffEventNotificationContactInformationSearchInput,
        *args: Any,
        **kwargs: Any,
    ) -> list[StaffEventNotificationContactInformationModel]:
        query_dto = StaffEventNotificationQueryDto(
            ids=query.staff_ids,
            event_type_ids=query.event_type_ids,
            org_ids=query.org_ids,
        )
        return [
            staff_contact_info.model
            for staff_contact_info in (
                await info.context.contact_information_service.query_staff_event_notification_contact_information_relationships(
                    query=query_dto
                )
            )
        ]

    @staticmethod
    async def resolve_staffing_needs_roles(parent, info: GrapheneInfo) -> List[StaffRoleModel]:
        return await info.context.staffing_needs_service.get_staffing_needs_roles()

    @staticmethod
    async def resolve_block(parent, info: GrapheneInfo, id: str) -> BlockModel:
        return await info.context.block_service.get_block(id=id)

    @staticmethod
    async def resolve_blocks(
        parent, info: GrapheneInfo, query: BlockQueryInput, *args: Any, **kwargs: Any
    ) -> List[BlockModel]:
        return await info.context.block_service.query_blocks(
            query.to_dto(),
        )

    @staticmethod
    async def resolve_block_time(parent, info: GrapheneInfo, id: str) -> BlockTimeModel:
        return await info.context.block_service.get_block_time(id=id)

    @staticmethod
    async def resolve_block_times_bulk(
        parent, info: GrapheneInfo, query: BlockTimesBulkQueryInput, *args: Any, **kwargs: Any
    ) -> Union[list[BlockTimeModel], list[Optional[BlockTimeModel]]]:
        return await info.context.block_time_loader.load_many(query.ids)

    @staticmethod
    async def resolve_block_times(
        parent, info: GrapheneInfo, query: BlockTimeQueryInput, *args: Any, **kwargs: Any
    ) -> List[BlockTimeModel]:
        return await info.context.block_service.query_block_times(
            site_id=query.site_id,
            room_ids=query.room_ids,
            min_end_time=query.min_end_time,
            max_start_time=query.max_start_time,
            block_id=query.block_id,
        )

    @staticmethod
    async def resolve_block_times_available_intervals(
        parent,
        info: GrapheneInfo,
        query: BlockTimeAvailableIntervalInput,
        *args: Any,
        **kwargs: Any,
    ) -> List[BlockTimeAvailableInterval]:
        available_intervals = (
            await info.context.block_service.query_block_times_available_intervals(
                site_id=query.site_id,
                min_end_time=query.min_end_time,
                max_start_time=query.max_start_time,
            )
        )
        return [
            BlockTimeAvailableInterval(
                id=available_interval.id,
                block_time_id=available_interval.block_time_id,
                start_time=available_interval.start_time,
                end_time=available_interval.end_time,
                room_id=available_interval.room_id,
                surgeon_ids=available_interval.surgeon_ids,
            )
            for available_interval in available_intervals
        ]

    @staticmethod
    async def resolve_board_configs(
        parent, info: GrapheneInfo, query: BoardConfigQueryInput, *args: Any, **kwargs: Any
    ) -> Sequence[BoardConfigModel]:
        return await info.context.board_service.query_board_configs(
            BoardConfigQueryDto(
                org_ids=query.organization_ids,
                site_ids=query.site_ids,
                room_ids=query.room_ids,
            )
        )

    @staticmethod
    async def resolve_case_duration_surgeons(
        parent,
        info: GrapheneInfo,
        query: CaseDurationSurgeonsQueryInput,
        *args: Any,
        **kwargs: Any,
    ) -> List[SurgeonOption]:
        return await info.context.case_duration_service.get_case_duration_surgeons(
            surgeon_term=query.surgeon_term, surgeon_id=query.surgeon_id
        )

    @staticmethod
    async def resolve_case_duration_procedures(
        parent,
        info: GrapheneInfo,
        query: CaseDurationProceduresQueryInput,
        *args: Any,
        **kwargs: Any,
    ) -> List[ProcedureOption]:
        return await info.context.case_duration_service.get_case_duration_procedures(
            procedure_term=query.procedure_term, surgeon_id=query.surgeon_id
        )

    async def resolve_case_duration_turnover_prediction(
        parent,
        info: GrapheneInfo,
        query: CaseDurationPredictionQueryInput,
        *args: Any,
        **kwargs: Any,
    ) -> TurnoverPrediction:
        try:
            return await info.context.case_duration_service.get_turnover_prediction(
                surgeon_id=query.surgeon_id, procedure=query.procedure
            )
        except ValueError as e:
            raise GraphQLError(str(e))

    @staticmethod
    async def resolve_case_duration_predictions(
        parent,
        info: GrapheneInfo,
        query: CaseDurationPredictionQueryInput,
        *args: Any,
        **kwargs: Any,
    ) -> DurationPredictions:
        try:
            return await info.context.case_duration_service.get_duration_predictions(
                surgeon_id=query.surgeon_id,
                procedure=query.procedure,
                additional_procedures=query.additional_procedures,
            )
        except ValueError as e:
            raise GraphQLError(str(e))
        except aiohttp.ClientResponseError:
            raise GraphQLError("Unable to fetch case duration predictions")

    @staticmethod
    async def resolve_available_time_slots(
        parent,
        info: GrapheneInfo,
        query: AvailableTimeSlotQueryInput,
        *args: Any,
        **kwargs: Any,
    ) -> List[AvailableSlot]:
        query_dto = AvailableTimeSlotQueryDto(
            min_available_duration=query.min_available_duration,
            site_ids=query.site_ids,
            start_date=query.start_date,
            end_date=query.end_date,
            surgeon_id=query.surgeon_id,
        )
        return await info.context.available_time_slot_service.get_available_time_slots(
            query=query_dto
        )

    @staticmethod
    async def resolve_user_filter_views(
        parent, info: GrapheneInfo, query: UserFilterViewQueryInput, *args: Any, **kwargs: Any
    ) -> List[UserFilterViewModel]:
        return await info.context.user_filter_view_service.query_user_filter_views(
            ids=query.ids,
            name=query.name,
            user_id=info.context.auth.get_calling_user_id(),
        )

    @staticmethod
    async def resolve_event_label_options(
        parent, info: GrapheneInfo, *args: Any, **kwargs: Any
    ) -> List[EventLabelOptionModel]:
        return await info.context.event_service.get_event_label_options()

    @staticmethod
    async def resolve_case_forecasts(
        parent, info: GrapheneInfo, query: CaseForecastQueryInput, *args: Any, **kwargs: Any
    ) -> list[CaseForecastModel]:
        if query.forecast_statuses is not None:
            query.forecast_statuses = [
                get_case_forecast_status_enum_from_value(status)
                for status in query.forecast_statuses
            ]
        return await info.context.case_forecast_service.query_case_forecasts(
            ForecastQuery(
                min_start_time=query.min_start_time,
                max_start_time=query.max_start_time,
                case_ids=query.case_ids,
                room_ids=query.room_ids,
                site_ids=query.site_ids,
                forecast_variants=query.forecast_variants,
                forecast_statuses=query.forecast_statuses,
            )
        )

    @staticmethod
    async def resolve_turnover_labels(
        parent, info: GrapheneInfo, query: Optional[GQLTurnoverLabelQueryInput] = None
    ) -> list[TurnoverLabelModel]:
        # Resolver for turnover labels. If no type is passed, return all labels
        label_type = query.type if query else None
        return await info.context.turnover_label_service.query_turnover_labels(label_type)

    @staticmethod
    async def resolve_cases_to_blocks(
        parent,
        info: GrapheneInfo,
        query: CaseToBlockInput,
        *args: Any,
        **kwargs: Any,
    ):
        return await info.context.case_to_block_service.get_cases_to_blocks_for_date_range(
            min_date=query.min_date,
            max_date=query.max_date,
            site_id=query.site_id,
            block_id=query.block_id,
        )

    @staticmethod
    async def resolve_case_to_block_overrides(
        parent,
        info: GrapheneInfo,
        query: CaseToBlockOverrideInput,
        *args: Any,
        **kwargs: Any,
    ):
        overrides_query = CaseToBlockOverridesQuery(
            min_date=query.min_date,
            max_date=query.max_date,
            block_ids=query.block_ids,
            case_ids=query.case_ids,
        )
        return await info.context.case_to_block_service.query_case_to_block_overrides(
            query=overrides_query
        )

    @staticmethod
    async def resolve_block_utilizations(
        parent,
        info: GrapheneInfo,
        query: BlockUtilizationInput,
        *args: Any,
        **kwargs: Any,
    ) -> list[BlockUtilizationDto]:
        return await info.context.block_utilization_service.get_block_utilization_for_site(
            min_date=query.min_date,
            max_date=query.max_date,
            site_id=query.site_id,
            block_id=query.block_id,
        )

    @staticmethod
    async def resolve_custom_phase_configs(parent, info: GrapheneInfo):
        return await info.context.custom_phase_config_service.query_custom_phase_configs()

    @staticmethod
    async def resolve_terminal_clean_score(
        parent, info: GrapheneInfo, room_id: str, date: date
    ) -> Optional[TerminalCleanScoreModel]:
        return await info.context.terminal_cleans_service.get_terminal_clean_score(
            room_id=room_id,
            date=date,
        )
