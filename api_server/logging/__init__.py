# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

import datetime
import logging
import uuid
from multiprocessing.synchronize import Lock as LockType
from sys import stdout
from typing import Any, List, Optional

import google.cloud.logging
import sentry_sdk
from opentelemetry import trace
from opentelemetry.trace.span import INVALID_SPAN
from sentry_sdk.integrations.logging import _IGNORED_LOGGERS as SENTRY_IGNORED_LOGGERS
from sentry_sdk.integrations.logging import B<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EventHandler
from sentry_sdk.integrations.redis import RedisIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration

import config
from auth.auth import Auth
from api_server.server.middleware.request_claims_provider import (
    get_request_auth_header_claims,
    get_jwt_cookie_claims,
)

auth: Optional[Auth] = None
root_logger = logging.getLogger(name=None)
# This is really ugly to get around multiprocessing to the same server log file. In certain cases from
# alembic migration script, this value may not get initialized.
# https://linear.app/apella/issue/PS-970/refactor-logging-to-use-python-logging
_LOCK: Optional[LockType] = None


class ExclusionFilter(logging.Filter):
    def __init__(self, logger_names: List[str]):
        super().__init__()
        self.logger_names = logger_names

    def filter(self, record: logging.LogRecord) -> bool:
        """
        Returns True if the record should be logged, or False otherwise.
        """
        return record.name not in self.logger_names


class ErrorLogFileFormatter(logging.Formatter):
    def format(self, record):
        # add the extra kwargs to the error log file
        if "json_fields" in record.__dict__:
            record.msg = str(record.msg) + ", " + str(record.__dict__["json_fields"])
        return super().format(record)


def configure_logger(lock: LockType) -> logging.Logger:
    root_logger.setLevel(logging.INFO)
    _LOCK = lock
    if config.use_sentry_logging():
        # Add the Sentry Logging Handlers before the GCP handlers.
        # Sentry handlers must be added before GCP handlers because GCP handler removes exc_info
        # from log record. We must also manually instantiate the log handlers instead of using the
        # default Sentry LogIntegration (hence the default_integrations=False param below)
        # because Sentry's LogIntegration doesn't actually use handlers. It's strange in that
        # Sentry overwrites python's logging library (callHandlers, specifically).
        #
        # GCP logging code which removes exc_info:
        # https://github.com/googleapis/python-logging/blob/main/google/cloud/logging_v2/handlers/structured_log.py#L80  # noqa
        breadcrumb_handler = BreadcrumbHandler(level=logging.INFO)
        event_handler = EventHandler(level=logging.ERROR)

        root_logger.addHandler(breadcrumb_handler)
        root_logger.addHandler(event_handler)

        root_logger.addFilter(ExclusionFilter(SENTRY_IGNORED_LOGGERS))  # type: ignore

        sentry_sdk.init(
            dsn="https://<EMAIL>/6247473",
            integrations=[
                SqlalchemyIntegration(),
                RedisIntegration(),
            ],
            default_integrations=False,
            traces_sample_rate=0.00005,  # send sample of traces to Sentry for Performance Monitoring
            release=config.version(),
            environment=config.environment_name(),
            attach_stacktrace=True,
        )

    if config.use_gcloud_logging():
        # Retrieves a Cloud Logging handler based on the environment
        # you're running in and integrates the handler with the
        # Python logging module. By default, this captures all logs
        # at INFO level and higher.
        client = google.cloud.logging.Client()
        client.setup_logging()
    else:
        # Configure the standard, default StreamHandler if not using GCP logging.
        # We must print to stdout so that component tests work which read stdout
        stream_handler = logging.StreamHandler(stream=stdout)
        stream_handler.setLevel(logging.INFO)
        root_logger.addHandler(stream_handler)

    if config.web_server_error_log_file():
        error_log_file = logging.FileHandler(config.web_server_error_log_file())
        error_log_file.setFormatter(ErrorLogFileFormatter())
        root_logger.addHandler(error_log_file)

    return root_logger


def report_internal_error(e: Exception, extras: Optional[dict[str, Any]] = None):
    # For internal errors, we don't want to return any information to the caller,
    # but we do want to be able to debug the issue.  So we generate a UUID
    # and send that instead
    error_id = uuid.uuid4().hex

    # Put info about the error into a structure
    error_info = {
        "error_id": error_id,
    }
    if extras is not None:
        error_info.update(extras)

    # And log it all
    error(exception=e, **error_info)

    # and return the id
    return error_id


def debug(message, **kwargs):
    log(logging.DEBUG, message, **kwargs)


def info(message, **kwargs):
    log(logging.INFO, message, **kwargs)


def warning(message: Optional[str] = None, exception: Optional[Exception] = None, **kwargs):
    log(logging.WARNING, message=message, exception=exception, **kwargs)


def error(message: Optional[str] = None, exception: Optional[Exception] = None, **kwargs):
    log(logging.ERROR, message=message, exception=exception, **kwargs)


def _acquire_lock() -> None:
    if _LOCK:
        _LOCK.acquire()


def _release_lock() -> None:
    if _LOCK:
        _LOCK.release()


def has_request_context() -> bool:
    return get_request_auth_header_claims() is not None or get_jwt_cookie_claims() is not None


def log(
    level: int,
    message: Optional[str] = None,
    exception: Optional[Exception] = None,
    **kwargs: object,
) -> None:
    if has_request_context() and auth is not None:
        # Add user_id if possible
        kwargs["user_id"] = auth.safe_get_calling_user_id()

    # Since Dogdata pipeline is configured to use data.timestamp field as timestamp, we will
    # add a 'data' field with the current timestamp.
    # This field will be under 'json_fields' and therefore appear as 'data.timestamp' in the final log.
    kwargs["data"] = {"timestamp": datetime.datetime.now().isoformat()}
    if trace and trace.get_current_span() is not INVALID_SPAN:
        span_context = trace.get_current_span().get_span_context()
        # Used for linking in Grafana
        kwargs["traceID"] = trace.format_trace_id(span_context.trace_id)
        kwargs["spanID"] = trace.format_span_id(span_context.span_id)
        kwargs["traceSampled"] = span_context.trace_flags == span_context.trace_flags.SAMPLED

        # Useful for linking in Datadog
        kwargs["dd.trace_id"] = trace.format_trace_id(span_context.trace_id)
        kwargs["dd.span_id"] = trace.format_span_id(span_context.span_id)

    # For GCP handler, `json_fields` appear in the `jsonPayload` field in the CloudRun logs
    extra = {"json_fields": kwargs}
    _acquire_lock()
    if exception is not None:
        logging.log(level=level, msg=exception, exc_info=exception, extra=extra)
    else:
        logging.log(level=level, msg=message, extra=extra)
    _release_lock()
