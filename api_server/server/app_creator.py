from asyncio import CancelledError
from contextlib import asynccontextmanager
from typing import Optional, cast, AsyncIterator

import redis
from fastapi import FastAPI
from fastapi.middleware.gzip import GZipMiddleware
from hypercorn.app_wrappers import <PERSON>GI<PERSON>rapper
from hypercorn.typing import <PERSON>GI<PERSON>ramework
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.aiohttp_client import AioHttpClientInstrumentor
from opentelemetry.instrumentation.asyncpg import AsyncPGInstrumentor
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.propagate import set_global_textmap
from opentelemetry.propagators.b3 import B3MultiFormat
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from starlette_graphene3 import Graph<PERSON>App  # type: ignore

import api_server
import config
from api_server.app_service_provider.apella_app_container import app_container
from api_server.graphql.graphene import setup_graphql_app
from api_server.logging import error
from api_server.server.exception_handlers import add_exception_handlers
from api_server.server.middleware import (
    ApellaCORSMiddleware,
    AuthRequestContextMiddleware,
)
from api_server.server.middleware.fast_api_graphql_query_name_middleware import (
    FastAPIGraphQLQueryNameMiddleware,
)
from api_server.server.middleware.fast_api_graphql_timing_middleware import (
    FastAPIGraphQLTimingMiddleware,
)
from api_server.server.middleware.fast_api_graphql_tracing_middleware import (
    FastAPIGraphQLTracingMiddleware,
)
from api_server.server.middleware.fast_api_request_logger import FastAPIRequestLoggerMiddleware
from api_server.server.middleware.sql_alchemy_connection_count_middleware import (
    SqlAlchemyConnectionCountMiddleware,
)
from api_server.services.block.rest.block_endpoints import blocks_api_router
from api_server.services.camera.rest.camera_endpoints import camera_api_router
from api_server.services.case.rest.case_endpoints import case_api_router
from api_server.services.case_forecasts.rest.case_forecast_endpoints import forecasting_router
from api_server.services.contact_information.contact_information_service import (
    ContactInformationService,
)
from api_server.services.contact_information.rest import contact_information_endpoints
from api_server.services.contact_information.rest.contact_information_endpoints import (
    contact_information_api_router,
)
from api_server.services.events.rest.event_endpoints import events_router
from api_server.services.healthz.rest.healthz_endpoints import healthz_api_router
from api_server.services.highlights.rest.highlight_endpoints import highlight_api_router
from api_server.services.media.rest.media_assets_endpoints import media_api_router
from api_server.services.meta.rest.meta_endpoints import meta_api_router
from api_server.services.room.rest.room_endpoints import room_api_router
from api_server.services.site.rest.site_endpoints import site_api_router
from api_server.services.ehr_interfaces.rest.mapping_endpoints import (
    mapping_api_router,
    mapping_api_router_v2,
)
from api_server.services.organization.rest.organization_endpoints import organization_api_router
from api_server.services.users.rest.user_endpoints import user_api_router
from api_server.tracing import ApellaTracer
from api_server.tracing.apella_trace_sampler import ApellaTraceSampler
from auth.auth import Auth
from databases.cache.persistent_cache import PersistentCache
from databases.sql import org_id_filter, site_id_filter


def _init_db_resources() -> None:
    api_server.logging.auth = app_container.provide_app_scope_type(Auth)
    org_id_filter.auth = app_container.provide_app_scope_type(Auth)
    site_id_filter.auth = app_container.provide_app_scope_type(Auth)


def _init_dependencies() -> None:
    """Inject dependencies"""
    contact_information_endpoints.contact_information_service = (
        app_container.provide_app_scope_type(ContactInformationService)
    )


@asynccontextmanager
async def _app_lifespan(fast_api_app: FastAPI) -> AsyncIterator[None]:
    try:
        yield
    except CancelledError:
        # Ignore the cancel error on app_queue get from fastApi. We are trying to shut down
        # the evergreen get thread from the connection queue that needs to be cancelled.
        pass
    except Exception as exception:
        error(message="Encountered an error during fastAPI shutdown", exception=exception)


def _add_middleware(fast_api_app: FastAPI) -> None:
    """
    Configure all request scope level middleware here.
    """
    fast_api_app.add_middleware(GZipMiddleware)
    fast_api_app.add_middleware(AuthRequestContextMiddleware)
    fast_api_app.add_middleware(ApellaCORSMiddleware)
    fast_api_app.add_middleware(SqlAlchemyConnectionCountMiddleware)
    fast_api_app.add_middleware(FastAPIRequestLoggerMiddleware)
    fast_api_app.add_middleware(FastAPIGraphQLTracingMiddleware)
    fast_api_app.add_middleware(FastAPIGraphQLTimingMiddleware)
    # Order matters, this middleware must be executed before other GraphQL middleware, so must be added last
    fast_api_app.add_middleware(FastAPIGraphQLQueryNameMiddleware)


def _add_routes(fast_api_app: FastAPI, import_name: str) -> None:
    """
    As we migrate routes to fastAPI, we need to mount routes here.
    Ordering matters; we have overlap in prefixes as we migrate to ASGI.
    """
    _init_dependencies()

    # mount migrated rest endpoints
    fast_api_app.include_router(blocks_api_router)
    fast_api_app.include_router(camera_api_router)
    fast_api_app.include_router(contact_information_api_router)
    fast_api_app.include_router(healthz_api_router)
    fast_api_app.include_router(highlight_api_router)
    fast_api_app.include_router(media_api_router)
    fast_api_app.include_router(room_api_router)
    fast_api_app.include_router(site_api_router)
    fast_api_app.include_router(mapping_api_router)
    fast_api_app.include_router(mapping_api_router_v2)
    fast_api_app.include_router(meta_api_router)
    fast_api_app.include_router(organization_api_router)
    fast_api_app.include_router(user_api_router)
    fast_api_app.include_router(case_api_router)
    fast_api_app.include_router(events_router)
    fast_api_app.include_router(forecasting_router)

    # mount graphql app
    _init_graphql_app(fast_api_app)


def _init_redis() -> Optional[PersistentCache]:
    redis_host = config.redis_host()
    _persistent_cache: Optional[PersistentCache] = None
    if redis_host is not None:
        redis_client = redis.Redis(host=redis_host, port=config.redis_port())
        _persistent_cache = PersistentCache(redis_client)
    app_container.add_simple_app_provider(_persistent_cache, cast(type, Optional[PersistentCache]))
    return _persistent_cache


def _init_graphql_app(app: FastAPI) -> None:
    graphql_app: GraphQLApp = setup_graphql_app(get_context=app_container.app_context)
    app.add_route("/v1/graphql", graphql_app)


def _init_tracing(fast_api_app: FastAPI) -> None:
    # OpenTelemetry Setup
    tracer_provider = TracerProvider(
        sampler=ApellaTraceSampler(config.sample_rates_by_operation_names()),
        resource=Resource.create(
            {
                "service.name": "api-server",
            }
        ),
    )

    if config.otlp_trace_exporter_endpoint() is not None:
        otlp_exporter = OTLPSpanExporter(
            endpoint=config.otlp_trace_exporter_endpoint(), insecure=True
        )
        tracer_provider.add_span_processor(BatchSpanProcessor(otlp_exporter))

    set_global_textmap(B3MultiFormat())
    trace.set_tracer_provider(tracer_provider)
    # Instrument FastAPI
    FastAPIInstrumentor().instrument_app(fast_api_app)
    # Instrument all requests objects
    RequestsInstrumentor().instrument(tracer_provider=tracer_provider)
    # Instrument all aiohttp objects
    AioHttpClientInstrumentor().instrument(tracer_provider=tracer_provider)
    # Instrument asyncpg
    AsyncPGInstrumentor().instrument(tracer_provider=tracer_provider)
    # Instrument our trace annotation
    ApellaTracer.tracer = trace.get_tracer(__name__)


def get_fast_api_app(import_name: str) -> FastAPI:
    """
    Returns a FastAPI app instance.
    """

    _init_redis()
    _init_db_resources()
    app_container.build_app_services_providers()

    fast_api_app = FastAPI(
        # Disables openapi schema endpoint and the documentation UI that uses it
        openapi_url=None,
        lifespan=_app_lifespan,
    )
    _add_middleware(fast_api_app)
    _init_tracing(fast_api_app=fast_api_app)
    add_exception_handlers(fast_api_app)
    _add_routes(fast_api_app, import_name)

    return fast_api_app


def get_app_wrapper(
    import_name: str,
    app: Optional[FastAPI] = None,
) -> ASGIWrapper:
    """
    We pull the ASGI Wrapper class from Hypercorn here in order to manually serve the process traffic.
    """
    if app is None:
        app = get_fast_api_app(import_name)
    return ASGIWrapper(cast(ASGIFramework, app))
