from copy import copy
from prometheus_client import Counter
import config
from datetime import datetime, time, timedelta, timezone, tzinfo
from typing import Iterator, Optional, Set, Tuple, Union
from zoneinfo import ZoneInfo

from api_server import logging
from api_server.services.annotation_tasks.optimizer_service import OptimizerService
from annotation_task_optimizer import AnnotationNeedsResult, ResultStatus
from sqlalchemy import String

import random
from apella_cloud_api.constants import UNSET_TOKEN
from apella_cloud_api.dtos import EventQueryDto
from api_server.logging.audit import log_calls_to_audit
from api_server.services.annotation_tasks.annotation_task_models import (
    CancelledReason,
    ReviewReason,
    TaskBulkUpdate,
    TaskQ<PERSON>y,
    TaskScheduleCreate,
    TaskScheduleUpdate,
    TaskStatus,
    TaskTypeUpdate,
    TaskUpdate,
)
from api_server.services.annotation_tasks.annotation_task_store import (
    AnnotationTask,
    AnnotationTaskSchedule,
    AnnotationTaskStore,
    AnnotationTaskTypeModel,
)
from api_server.services.events.event_service import EventService
from api_server.services.events.event_store import EventModel
from api_server.services.events.source_type import HUMAN_GROUND_TRUTH, PREDICTION
from api_server.services.media.media_asset_service_client import MediaAssetService
from api_server.services.media.media_service import MediaService
from api_server.services.objects.objects_service import ObjectsService
from api_server.services.room.room_service import RoomService
from api_server.services.room.room_store import RoomModel
from api_server.services.site.site_service import SiteService
from api_server.tracing.trace_decorator import class_traced
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import (
    READ_ANY_ANNOTATION_TASK,
    READ_ANY_EVENT,
    WRITE_ANY_ANNOTATION_TASK,
    WRITE_ANY_ANNOTATION_TASK_TYPE,
)

MEDIA_AVAILABILITY_THRESHOLD = 0.5
DEFAULT_BULK_GENERATION_WINDOW = timedelta(days=2)
TASK_GENERATION_DELAY = timedelta(minutes=10)
ANNOTATION_TASK_REVIEW_SAMPLE_RATE = 0.15

ANNOTATION_TASK_GENERATION_COUNTER = Counter(
    "annotation_task_generation_count",
    "Counts the number of annotation tasks generated",
    ["environment"],
)


def get_possible_start_times(start_time: time, interval_hrs: int) -> list[time]:
    """
    Given a start time and an interval in hours, returns a list of possible start times
    Example:
    >>> get_possible_start_times(datetime.time(18, 0), 6)
    [datetime.time(18, 0), datetime.time(0, 0), datetime.time(6, 0), datetime.time(12, 0)]
    """
    return [
        time(hour=(start_time.hour + interval_hrs * i) % 24, minute=start_time.minute)
        for i in range(24 // interval_hrs)
    ]


def seek_to(start_datetime: datetime, seek_time: time, tz: tzinfo = timezone.utc) -> datetime:
    """
    Starting from start_datetime, go to the next instance of seek_time in a particular timezone.
    """
    seek_time_on_current_date = datetime.combine(
        date=start_datetime.date(), time=seek_time, tzinfo=tz
    )
    if seek_time_on_current_date >= start_datetime.astimezone(tz):
        return seek_time_on_current_date

    return seek_time_on_current_date + timedelta(days=1)


def time_range(
    start_time: datetime, end_time: datetime, interval: timedelta
) -> Iterator[tuple[datetime, datetime]]:
    """
    Allows iterating through a time range
    """
    interval_start_time = start_time
    interval_end_time = start_time + interval
    while interval_end_time <= end_time:
        yield (interval_start_time, interval_end_time)
        interval_start_time += interval
        interval_end_time += interval


def _get_start_time_for_timezone_and_schedule(
    start_time: datetime, timezone: tzinfo, schedule: AnnotationTaskSchedule
) -> datetime:
    """
    Get the earliest possible start time defined by schedule after start_time
    """
    return min(
        [
            seek_to(start_time, schedule_start_time, tz=timezone)
            for schedule_start_time in get_possible_start_times(
                schedule.start_time, schedule.interval
            )
        ]
    )


def _increment_annotation_task_generation_counter(count: int) -> None:
    try:
        ANNOTATION_TASK_GENERATION_COUNTER.labels(environment=config.environment_name()).inc(count)
    except Exception as e:
        logging.error(
            message=f"Incrementing annotation task generation counter failed: {e}",
            exception=e,
        )


@class_traced()
class AnnotationTaskService:
    auth: Auth
    annotation_task_store: AnnotationTaskStore
    room_service: RoomService
    site_service: SiteService
    media_asset_service: MediaAssetService
    media_service: MediaService
    event_service: EventService
    object_service: ObjectsService

    def __init__(
        self,
        auth: Auth,
        annotation_task_store: AnnotationTaskStore,
        site_service: SiteService,
        room_service: RoomService,
        media_service: MediaAssetService,
        event_service: EventService,
        object_service: ObjectsService,
    ):
        self.auth = auth
        self.annotation_task_store = annotation_task_store
        self.site_service = site_service
        self.room_service = room_service
        self.media_asset_service = media_service
        self.event_service = event_service
        self.object_service = object_service

    @requires_permissions(WRITE_ANY_ANNOTATION_TASK, enforce_universal_user=True)
    async def update_task(self, task_update: TaskUpdate) -> AnnotationTask:
        task_update_obj = copy(task_update)
        task_update_obj.updated_by_user_id = self.auth.get_calling_user_id()

        return await self.annotation_task_store.update_task(task_update=task_update_obj)

    @requires_permissions(READ_ANY_ANNOTATION_TASK, enforce_universal_user=True)
    async def bulk_update_tasks(
        self,
        task_bulk_update: TaskBulkUpdate,
        task_query: Optional[TaskQuery] = None,
        task_ids: Optional[list[str]] = None,
    ) -> int:
        task_bulk_update_obj = copy(task_bulk_update)
        task_bulk_update_obj.updated_by_user_id = self.auth.get_calling_user_id()

        return await self.annotation_task_store.bulk_update_tasks(
            task_query=task_query,
            task_bulk_update=task_bulk_update_obj,
            task_ids=task_ids,
        )

    @requires_permissions(READ_ANY_ANNOTATION_TASK)
    async def get_task(self, annotation_task_id: str) -> AnnotationTask:
        return await self.annotation_task_store.get_task(annotation_task_id=annotation_task_id)

    @requires_permissions(READ_ANY_ANNOTATION_TASK)
    async def query_tasks(self, task_query: TaskQuery) -> list[AnnotationTask]:
        return await self.annotation_task_store.query_tasks(task_query=task_query)

    @requires_permissions(WRITE_ANY_ANNOTATION_TASK, enforce_universal_user=True)
    async def exit_task(self, task_id: str) -> AnnotationTask:
        """
        Exit task via the annotator workflow. Only meant to be triggered by the user
        assigned to the task.
        """
        user_id = self.auth.get_calling_user_id()

        task = await self.get_task(task_id)

        if task.status == TaskStatus.IN_PROGRESS and user_id == task.annotator_user_id:
            return await self.update_task(
                TaskUpdate(
                    id=task.id,
                    status=TaskStatus.NOT_STARTED,
                    annotator_user_id=UNSET_TOKEN,
                )
            )
        if task.status == TaskStatus.IN_REVIEW and user_id == task.reviewer_user_id:
            return await self.update_task(
                TaskUpdate(
                    id=task.id,
                    status=TaskStatus.READY_FOR_REVIEW,
                    reviewer_user_id=UNSET_TOKEN,
                )
            )

        return task

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_ANNOTATION_TASK, enforce_universal_user=True)
    async def bulk_generate_tasks(
        self,
        start_time: Union[datetime, None] = None,
        end_time: Union[datetime, None] = None,
        task_type_ids: Optional[list[str]] = None,
    ) -> bool:
        """
        Generate and create annotation tasks for the given request parameters.
        start_time: will default to 2 days and 10 minutes ago
        end_time: will default to 2 days after start_time
        """
        start_time = start_time or (
            datetime.now(tz=timezone.utc) - DEFAULT_BULK_GENERATION_WINDOW - TASK_GENERATION_DELAY
        )

        await self.__generate_tasks(
            start_time=start_time,
            end_time=end_time or (start_time + DEFAULT_BULK_GENERATION_WINDOW),
            task_type_ids=task_type_ids,
        )

        return True

    @requires_permissions(WRITE_ANY_ANNOTATION_TASK, enforce_universal_user=True)
    async def next_annotate_task(
        self, current_task_id: Optional[str], status: Optional[TaskStatus] = None
    ) -> Tuple[Optional[AnnotationTask], Optional[AnnotationTask]]:
        """
        - Changes the current task to the status provided
          - If we can skip review, change the status to "Done"
        - Finds the next annotation task
        - If an annotation task is available, assigns the curent user to the task
          and sets the status to "In Progress"

        Priority:
        - Assigned as annotator
        - Task Priority
        - Task start time (oldest first)
        """
        user_id = self.auth.get_calling_user_id()

        current_task = None if current_task_id is None else await self.get_task(current_task_id)

        if current_task is not None and status is not None:
            review_reasons = await self.get_review_reasons(current_task)
            if len(review_reasons) == 0:
                current_task = await self.update_task(
                    TaskUpdate(id=current_task.id, status=TaskStatus.DONE, review_reasons=set())
                )
            else:
                current_task = await self.update_task(
                    TaskUpdate(
                        id=current_task.id,
                        status=status,
                        review_reasons=set(review_reasons),
                    )
                )

        next_task = await self.annotation_task_store.get_next_annotate_task(
            user_id=user_id, current_task_id=current_task_id
        )

        if next_task is not None and next_task.status == TaskStatus.NOT_STARTED:
            next_task = await self.update_task(
                TaskUpdate(
                    id=next_task.id,
                    status=TaskStatus.IN_PROGRESS,
                    annotator_user_id=user_id,
                )
            )

        return (current_task, next_task)

    @requires_permissions(WRITE_ANY_ANNOTATION_TASK, enforce_universal_user=True)
    async def next_review_task(
        self, current_task_id: Optional[str], status: Optional[TaskStatus] = None
    ) -> Tuple[Optional[AnnotationTask], Optional[AnnotationTask]]:
        """
        - Changes the current task to the status provided
        - Finds the next review task
        - If a review task is available, assigns the curent user to the task
          and sets the status to "In Review"

        Priority:
        - Assigned as reviewer
        - Task Priority
        - Task start time (oldest first)
        """
        user_id = self.auth.get_calling_user_id()

        current_task = None if current_task_id is None else await self.get_task(current_task_id)

        if current_task is not None and status is not None:
            current_task = await self.update_task(TaskUpdate(id=current_task.id, status=status))

        next_task = await self.annotation_task_store.get_next_review_task(
            user_id=user_id, current_task_id=current_task_id
        )

        if next_task is not None and next_task.status == TaskStatus.READY_FOR_REVIEW:
            next_task = await self.update_task(
                TaskUpdate(
                    id=next_task.id,
                    status=TaskStatus.IN_REVIEW,
                    reviewer_user_id=user_id,
                )
            )

        return (current_task, next_task)

    async def __generate_tasks(
        self,
        start_time: datetime,
        end_time: datetime,
        task_type_ids: Optional[list[str]] = None,
    ) -> None:
        """
        Returns all possible tasks within a particular date range. Tasks are derived as
        NOT_STARTED, and without an annotator or reviewer.
        """
        if task_type_ids is None:
            task_types = await self.annotation_task_store.get_annotation_task_types_for_generation(
                archived=False
            )
        else:
            task_types = await self.annotation_task_store.get_annotation_task_types_for_generation(
                keys=task_type_ids
            )

        for task_type in task_types:
            for schedule in task_type.schedules:
                for site in schedule.sites:
                    task_generation_start_time = _get_start_time_for_timezone_and_schedule(
                        start_time=start_time,
                        timezone=ZoneInfo(site.timezone),
                        schedule=schedule,
                    )

                    task_generation_end_time = end_time.astimezone(ZoneInfo(site.timezone))

                    for room in site.rooms:
                        annotation_tasks = await self._generate_tasks_for_room(
                            start_time=task_generation_start_time,
                            end_time=task_generation_end_time,
                            interval=schedule.interval,
                            room=room,
                            task_type=task_type,
                        )
                        await self.annotation_task_store.create_tasks(
                            annotation_tasks=annotation_tasks
                        )
                        _increment_annotation_task_generation_counter(len(annotation_tasks))
                for room in schedule.rooms:
                    site = room.site

                    task_generation_start_time = _get_start_time_for_timezone_and_schedule(
                        start_time=start_time,
                        timezone=ZoneInfo(site.timezone),
                        schedule=schedule,
                    )

                    task_generation_end_time = end_time.astimezone(ZoneInfo(site.timezone))

                    annotation_tasks = await self._generate_tasks_for_room(
                        start_time=task_generation_start_time,
                        end_time=task_generation_end_time,
                        interval=schedule.interval,
                        room=room,
                        task_type=task_type,
                    )
                    await self.annotation_task_store.create_tasks(annotation_tasks=annotation_tasks)
                    _increment_annotation_task_generation_counter(len(annotation_tasks))

    async def _generate_tasks_for_room(
        self,
        start_time: datetime,
        end_time: datetime,
        interval: int,
        room: RoomModel,
        task_type: AnnotationTaskTypeModel,
    ) -> list[AnnotationTask]:
        annotation_tasks = []

        for interval_start_datetime, interval_end_datetime in time_range(
            start_time=start_time,
            end_time=end_time,
            interval=timedelta(hours=interval),
        ):
            if (
                len(
                    await self.annotation_task_store.query_tasks(
                        task_query=TaskQuery(
                            type_ids=[str(task_type.id)],
                            start_time=interval_start_datetime,
                            end_time=interval_end_datetime,
                            room_id=str(room.id),
                        )
                    )
                )
                > 0
            ):
                continue

            if not await self.media_asset_service.has_media_asset_hls_playlist_video_available(
                room_id=str(room.id),
                start_time=interval_start_datetime,
                end_time=interval_end_datetime,
            ):
                logging.info(
                    f"Did not get playlist for {room.id} from {interval_start_datetime} to {interval_end_datetime}"
                )
                continue

            task = AnnotationTask(
                org_id=room.org_id,
                site_id=room.site_id,
                room_id=room.id,
                start_time=interval_start_datetime,
                end_time=interval_end_datetime,
                type_id=task_type.id,
                # AnnotationTaskTypeModel uses the history mapper for its version attribute
                type_version=task_type.version,  # type: ignore [attr-defined]
            )

            if task_type.optimize_tasks:
                result = self._get_annotation_needs(
                    room.id, task.start_time, task.end_time, task_type.event_types
                )

                if result is None:
                    continue

                if result.status == ResultStatus.IDLE:
                    task.status = TaskStatus.CANCELLED
                    task.cancelled_reason = CancelledReason.IDLE
                elif result.status == ResultStatus.SKIP:
                    task.status = TaskStatus.CANCELLED
                    task.cancelled_reason = CancelledReason.SKIP
                elif result.status == ResultStatus.ANNOTATE:
                    task.status = TaskStatus.NOT_STARTED
                else:
                    logging.error(
                        f"Unknown ResultStatus {result.status}, skipping {room.id} from {task.start_time} to {task.end_time} for task type {task_type.id}"
                    )
                    continue
            else:
                if task_type.detect_idle and await self.__is_idle(
                    start_time=interval_start_datetime,
                    end_time=interval_end_datetime,
                    room_id=str(room.id),
                    event_types=(task_type.event_types + task_type.context_event_types),
                ):
                    task.status = TaskStatus.CANCELLED
                    task.cancelled_reason = CancelledReason.IDLE
                else:
                    task.status = TaskStatus.NOT_STARTED

            annotation_tasks.append(task)

        return annotation_tasks

    def _get_annotation_needs(
        self, room_id: str, start_time: datetime, end_time: datetime, event_types: list[str]
    ) -> Optional[AnnotationNeedsResult]:
        try:
            return OptimizerService.get_instance().get_annotation_needs(
                room_id, start_time, end_time, event_types
            )
        except Exception as e:
            logging.error(
                message=f"Failed to get annotation needs for {room_id} from {start_time} to {end_time} for event types {event_types}: {e}",
                exception=e,
            )

            return None

    async def __is_idle(
        self,
        start_time: datetime,
        end_time: datetime,
        room_id: str,
        event_types: list[str],
    ) -> bool:
        """
        Determines if task is idle. A task is idle if there are no events in
        the given time range. If we are missing frames, we want to still
        generate a task.
        """
        if (
            len(
                await self.event_service.query_events(
                    EventQueryDto(
                        room_id=room_id,
                        min_start_time=start_time,
                        max_start_time=end_time,
                        event_names=event_types,
                    )
                )
            )
            > 0
        ):
            return False

        for multi_metric_bucket_float in self.object_service.get_occupancy_and_outage_buckets(
            room_id=room_id,
            min_time=start_time,
            max_time=end_time,
            bucket_size=timedelta(minutes=10),
        ):
            for metric_float in multi_metric_bucket_float.metrics:
                if (
                    metric_float.metric == "media_availability"
                    and metric_float.value < MEDIA_AVAILABILITY_THRESHOLD
                ):
                    return False

        return True

    @requires_permissions(READ_ANY_ANNOTATION_TASK, enforce_universal_user=True)
    async def get_annotation_task_type(self, id: String) -> AnnotationTaskTypeModel:
        return await self.annotation_task_store.get_annotation_task_type(id=id)

    @requires_permissions(READ_ANY_ANNOTATION_TASK, enforce_universal_user=True)
    async def get_annotation_task_events(
        self, annotation_task: AnnotationTask, include_deleted: Optional[bool] = None
    ) -> list[EventModel]:
        if len(annotation_task.historical_task_type.event_types) > 0:
            return await self.event_service.query_events(
                EventQueryDto(
                    source_types=[HUMAN_GROUND_TRUTH, PREDICTION],
                    event_names=annotation_task.task_type.event_types,
                    room_id=annotation_task.room_id,
                    min_time=annotation_task.start_time,
                    max_time=annotation_task.end_time,
                    include_deleted=include_deleted,
                )
            )
        else:
            return []

    @requires_permissions(READ_ANY_ANNOTATION_TASK, enforce_universal_user=True)
    async def get_annotation_task_context_events(
        self, annotation_task: AnnotationTask, include_deleted: Optional[bool] = None
    ) -> list[EventModel]:
        if len(annotation_task.historical_task_type.context_event_types) > 0:
            return await self.event_service.query_events(
                EventQueryDto(
                    source_types=[HUMAN_GROUND_TRUTH, PREDICTION],
                    event_names=annotation_task.task_type.context_event_types,
                    room_id=annotation_task.room_id,
                    min_time=annotation_task.start_time,
                    max_time=annotation_task.end_time,
                    include_deleted=include_deleted,
                )
            )
        else:
            return []

    @requires_permissions(READ_ANY_ANNOTATION_TASK, enforce_universal_user=True)
    async def get_review_reasons(self, annotation_task: AnnotationTask) -> set[ReviewReason]:
        """
        Determines reasons task should be reviewed
        """
        user_id = self.auth.get_calling_user_id()

        review_reasons: set[ReviewReason] = set()

        if not annotation_task.task_type.allow_skipping_review:
            review_reasons.add(ReviewReason.STANDARD)
        elif user_id in annotation_task.task_type.provisional_annotator_ids:
            review_reasons.add(ReviewReason.PROVISIONAL)
        elif await self._event_needs_review(annotation_task):
            review_reasons.add(ReviewReason.REVIEW_REQUESTED)
        elif random.random() < ANNOTATION_TASK_REVIEW_SAMPLE_RATE:
            review_reasons.add(ReviewReason.RANDOM_SAMPLE)

        return review_reasons

    async def _event_needs_review(self, annotation_task: AnnotationTask) -> bool:
        for event in await self.get_annotation_task_events(annotation_task, include_deleted=False):
            if event.labels is None:
                continue
            # If any event includes the label "Needs Review", add reason
            if any("Needs Review" == label for label in event.labels):
                return True

        return False

    @requires_permissions(READ_ANY_ANNOTATION_TASK)
    async def get_annotation_task_types(
        self, keys: Union[list[str], None]
    ) -> list[AnnotationTaskTypeModel]:
        return await self.annotation_task_store.get_annotation_task_types(keys=keys)

    @requires_permissions(WRITE_ANY_ANNOTATION_TASK_TYPE, enforce_universal_user=True)
    async def create_annnotation_task_type(
        self, annotation_task_type: AnnotationTaskTypeModel
    ) -> AnnotationTaskTypeModel:
        return await self.annotation_task_store.create_annotation_task_type(
            annotation_task_type=annotation_task_type
        )

    @requires_permissions(WRITE_ANY_ANNOTATION_TASK_TYPE, enforce_universal_user=True)
    async def update_annnotation_task_type(
        self, task_type_update: TaskTypeUpdate
    ) -> AnnotationTaskTypeModel:
        return await self.annotation_task_store.update_annotation_task_type(
            task_type_update=task_type_update
        )

    @requires_permissions(WRITE_ANY_ANNOTATION_TASK_TYPE, enforce_universal_user=True)
    async def create_annnotation_task_schedule(
        self, task_schedule_create: TaskScheduleCreate
    ) -> AnnotationTaskSchedule:
        return await self.annotation_task_store.create_annotation_task_schedule(
            task_schedule_create=task_schedule_create
        )

    @requires_permissions(WRITE_ANY_ANNOTATION_TASK_TYPE, enforce_universal_user=True)
    async def update_annnotation_task_schedule(
        self, task_schedule_update: TaskScheduleUpdate
    ) -> AnnotationTaskSchedule:
        return await self.annotation_task_store.update_annotation_task_schedule(
            task_schedule_update=task_schedule_update
        )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_EVENT)
    async def get_unverified_phases_for_phase_ids(
        self,
        phase_ids: list[str],
    ) -> Set[str]:
        return await self.annotation_task_store.get_unverified_phases_for_phase_ids(phase_ids)
