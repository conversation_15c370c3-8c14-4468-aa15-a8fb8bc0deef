import uuid
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime, timezone
from http import HTTPStatus
from typing import Any, DefaultDict, List, Optional, Sequence, Set, TypeVar, Union, overload
from asyncpg.exceptions import UniqueViolationError, CheckViolationError
import sqlalchemy.exc
from sqlalchemy.ext.asyncio import AsyncSession
import sqlalchemy.orm
from sqlalchemy.sql.expression import Select, Update
from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    Enum,
    ForeignKey,
    ForeignKeyConstraint,
    Integer,
    String,
    Table,
    Time,
    UniqueConstraint,
    and_,
    asc,
    union,
    literal,
    select,
    update,
)
from sqlalchemy.dialects.postgresql import ARRAY, UUID
from sqlalchemy.ext.associationproxy import association_proxy
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import aliased, relationship, Mapped, mapped_column, selectinload

from apella_cloud_api.exceptions import ClientError, NotFound
from api_server.graphql.partial_updates import interpret_field_value
from api_server.services.annotation_tasks.annotation_task_models import (
    CancelledReason,
    TaskBulkUpdate,
    TaskQuery,
    TaskScheduleCreate,
    TaskScheduleUpdate,
    TaskStatus,
    TaskTypeUpdate,
    TaskUpdate,
)
from api_server.services.events.event_store import EventModel, EventStore
from api_server.services.phases.phase_store import PhaseModel
from api_server.services.room.room_store import RoomModel, RoomStore
from api_server.services.site.site_models import SiteQuery
from api_server.services.site.site_store import Site, SiteStore
from api_server.services.utils.sql_utils import get_in_filter_with_null
from api_server.tracing.trace_decorator import class_traced
from databases.sql import Base, new_async_session
from databases.sql.helpers import check_exception, get_exception_message
from databases.sql.mixins import (
    ArchivedTimeMixin,
    OrgIdMixin,
    SiteIdMixin,
    TimestampMixin,
)
from databases.sql.sync_as_async_session import async_db_session_provider
from utils.history_meta import Versioned

T = TypeVar("T", bound=Base)

PRIORITY_LABEL = "next_task_query_priority"

annotation_task_schedules_sites = Table(
    "annotation_task_schedules_sites",
    Base.metadata,
    Column(
        "schedule_id",
        UUID(as_uuid=True),
        ForeignKey("annotation_task_schedules.id"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "site_id",
        String,
        ForeignKey("sites.id"),
        primary_key=True,
        nullable=False,
    ),
)

annotation_task_schedules_rooms = Table(
    "annotation_task_schedules_rooms",
    Base.metadata,
    Column(
        "schedule_id",
        UUID(as_uuid=True),
        ForeignKey("annotation_task_schedules.id"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "room_id",
        String,
        ForeignKey("rooms.id"),
        primary_key=True,
        nullable=False,
    ),
)


class AnnotationTaskTypeAnnotator(Base, TimestampMixin):
    __tablename__ = "annotation_task_type_annotators"
    annotation_task_type_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("annotation_task_types.id"),
        nullable=False,
        index=True,
        primary_key=True,
    )

    user_id: Mapped[str] = mapped_column(String, nullable=False, index=True, primary_key=True)


class AnnotationTaskTypeProvisionalAnnotator(Base, TimestampMixin):
    __tablename__ = "annotation_task_type_provisional_annotators"
    annotation_task_type_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("annotation_task_types.id"),
        nullable=False,
        index=True,
        primary_key=True,
    )

    user_id: Mapped[str] = mapped_column(String, nullable=False, index=True, primary_key=True)


class AnnotationTaskTypeReviewer(Base, TimestampMixin):
    __tablename__ = "annotation_task_type_reviewers"
    annotation_task_type_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("annotation_task_types.id"),
        nullable=False,
        index=True,
        primary_key=True,
    )

    user_id: Mapped[str] = mapped_column(String, nullable=False, index=True, primary_key=True)


class AnnotationTaskSchedule(Base, TimestampMixin):
    __tablename__ = "annotation_task_schedules"
    __table_args__ = (
        UniqueConstraint(
            "annotation_task_type_id",
            "start_time",
            "interval",
        ),
    )

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    annotation_task_type_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("annotation_task_types.id"), nullable=False, index=True
    )
    start_time = mapped_column(Time, nullable=False)
    interval: Mapped[int] = mapped_column(Integer, nullable=False)

    sites = relationship(Site, secondary=annotation_task_schedules_sites, lazy="selectin")
    rooms = relationship(RoomModel, secondary=annotation_task_schedules_rooms, lazy="selectin")

    def validate_and_coerce_values(self) -> None:
        site_ids = [s.id for s in self.sites]
        for room in self.rooms:
            if room.site_id in site_ids:
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    message=f"Schedule cannot be assigned rooms ('{room.id}') from the same site ('{room.site_id}')",
                )


class AnnotationTaskTypeModel(Base, TimestampMixin, ArchivedTimeMixin, Versioned):
    __tablename__ = "annotation_task_types"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name: Mapped[str] = mapped_column(String, nullable=False)
    description: Mapped[str] = mapped_column(String, nullable=False)
    event_types: Mapped[list[str]] = mapped_column(ARRAY(String), nullable=False, default=[])
    context_event_types = mapped_column(ARRAY(String), nullable=False, default=[])
    priority: Mapped[int] = mapped_column(Integer, nullable=False, default=5)
    detect_idle: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    optimize_tasks: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=False, server_default="false"
    )
    allow_skipping_review: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)

    provisional_annotator_ids = association_proxy("_provisional_annotators", "user_id")

    annotator_ids = association_proxy("_annotators", "user_id")

    reviewer_ids = association_proxy("_reviewers", "user_id")

    schedules = relationship("AnnotationTaskSchedule", lazy="selectin")

    def __repr__(self) -> str:
        return f"<AnnotationTaskType(id='{self.id}')>"

    async def validate_and_coerce_values(self, session: AsyncSession) -> None:
        for s in self.schedules:
            s.validate_and_coerce_values()

        self.name = self.name.strip()
        self.description = self.description.strip()

        valid_event_types = [
            event.id for event in await EventStore().get_event_types(session=session)
        ]

        if self.id is None:
            # we don't use the column default here b/c otherwise the Versioned mixin will use
            # the default uuid function when creating the history record, causing the history
            # record to have a different id value from the original record
            self.id = uuid.uuid4()
        if self.event_types is None:
            self.event_types = []
        if self.context_event_types is None:
            self.context_event_types = []

        for event_type in self.event_types:
            if event_type not in valid_event_types:
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    message=f"Invalid event type '{event_type}'",
                )

        for event_type in self.context_event_types:
            if event_type not in valid_event_types:
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    message=f"Invalid event type '{event_type}'",
                )


AnnotationTaskSchedule.annotation_task_type = relationship(
    "AnnotationTaskTypeModel", lazy="joined", back_populates="schedules"
)
AnnotationTaskTypeModel._provisional_annotators = relationship(
    "AnnotationTaskTypeProvisionalAnnotator",
    lazy="selectin",
    cascade="all, delete-orphan",
)

AnnotationTaskTypeModel._annotators = relationship(
    "AnnotationTaskTypeAnnotator",
    lazy="selectin",
    cascade="all, delete-orphan",
)

AnnotationTaskTypeModel._reviewers = relationship(
    "AnnotationTaskTypeReviewer",
    lazy="selectin",
    cascade="all, delete-orphan",
)


class AnnotationTask(Base, TimestampMixin, OrgIdMixin, SiteIdMixin, Versioned):
    __tablename__ = "annotation_tasks"

    @declared_attr  # type: ignore # this should be resolved when updating sqlalchemy
    def __table_args__(cls) -> tuple[UniqueConstraint, ForeignKeyConstraint, dict[str, bool]]:
        return (
            UniqueConstraint(
                "org_id",
                "site_id",
                "room_id",
                "start_time",
                "end_time",
                "type_id",
                name="uq_orgId_siteId_roomId_startTime_endTime_typeId",
            ),
            ForeignKeyConstraint(
                ["type_id", "type_version"],
                ["annotation_task_types_history.id", "annotation_task_types_history.version"],
            ),
            Versioned.__table_args__,
        )

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    room_id: Mapped[str] = mapped_column(String, ForeignKey("rooms.id"), nullable=False, index=True)

    start_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, index=True
    )
    end_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, index=True)

    status: Mapped[TaskStatus] = mapped_column(Enum(TaskStatus), nullable=False, index=True)
    cancelled_reason = mapped_column(Enum(CancelledReason), index=True, nullable=True)

    annotator_user_id: Mapped[Union[str, None]] = mapped_column(String, nullable=True, index=True)
    reviewer_user_id: Mapped[Union[str, None]] = mapped_column(String, nullable=True, index=True)

    updated_by_user_id: Mapped[Union[str, None]] = mapped_column(String, nullable=True, index=True)

    type_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("annotation_task_types.id"),
        nullable=False,
        index=True,
    )
    type_version: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        index=True,
    )

    review_reasons: Mapped[list[str]] = mapped_column(ARRAY(String), nullable=False, default=[])

    task_type = relationship(
        AnnotationTaskTypeModel,
        foreign_keys=[type_id],
        lazy="joined",
        overlaps="historical_task_type",
    )
    historical_task_type = relationship(
        AnnotationTaskTypeModel.__history_mapper__.class_,  # type: ignore [attr-defined]
        lazy="joined",
        overlaps="task_type",
    )

    def __repr__(self) -> str:
        return f"<AnnotationTask(id='{self.id}')>"

    def validate(self) -> None:
        if self.id is None:
            self.id = str(uuid.uuid4())


@dataclass
class PhaseAnnotationTaskCheck:
    phase_id: str
    phase_start: datetime
    phase_end: datetime
    annotation_start: datetime
    annotation_end: datetime


@class_traced()
class AnnotationTaskStore:
    @async_db_session_provider
    async def get_task(self, session: AsyncSession, annotation_task_id: str) -> AnnotationTask:
        try:
            return (
                await session.scalars(
                    select(AnnotationTask).where(AnnotationTask.id == annotation_task_id)
                )
            ).one()
        except sqlalchemy.orm.exc.NoResultFound:
            raise NotFound(f"No annotation task found with id: {annotation_task_id}")

    @async_db_session_provider
    async def get_next_annotate_task(
        self, session: AsyncSession, user_id: str, current_task_id: Optional[str]
    ) -> Optional[AnnotationTask]:
        """
        Priority:
        - Assigned as annotator
        - Unassigned "Not Started" tasks
        - Task start time (oldest first)
        """
        PRIORITY_LABEL = "next_task_query_priority"

        assigned_annotate_tasks = (
            select(AnnotationTask)
            .join(AnnotationTask.task_type)
            .add_columns(AnnotationTaskTypeModel.priority)
            .add_columns(literal(1).label(PRIORITY_LABEL))
            .filter(AnnotationTask.id != current_task_id)
            .filter(AnnotationTask.annotator_user_id == user_id)
            .filter(AnnotationTask.status.in_([TaskStatus.IN_PROGRESS, TaskStatus.NOT_STARTED]))
        )
        unassigned_annotate_tasks = (
            select(AnnotationTask)
            .join(AnnotationTask.task_type)
            .join(getattr(AnnotationTaskTypeModel, "_annotators"))
            .add_columns(AnnotationTaskTypeModel.priority)
            .add_columns(literal(2).label(PRIORITY_LABEL))
            .filter(AnnotationTaskTypeAnnotator.user_id == user_id)
            .filter(AnnotationTask.annotator_user_id.is_(None))
            .filter(AnnotationTask.status.in_([TaskStatus.NOT_STARTED]))
        )

        task_id = (
            await session.scalars(
                union(assigned_annotate_tasks, unassigned_annotate_tasks).order_by(
                    asc(PRIORITY_LABEL),
                    asc(AnnotationTaskTypeModel.priority),
                    asc(AnnotationTask.start_time),
                )
            )
        ).first()

        if task_id is None:
            return task_id

        return await self.get_task(session=session, annotation_task_id=task_id)

    @async_db_session_provider
    async def get_next_review_task(
        self, session: AsyncSession, user_id: str, current_task_id: Optional[str]
    ) -> Optional[AnnotationTask]:
        """
        Priority:
        - Assigned as reviewer
        - Unassigned "Ready for Review" tasks
        - Task start time (oldest first)
        """
        assigned_review_tasks = (
            select(AnnotationTask)
            .join(AnnotationTask.task_type)
            .add_columns(AnnotationTaskTypeModel.priority)
            .add_columns(literal(1).label(PRIORITY_LABEL))
            .where(AnnotationTask.reviewer_user_id == user_id)
            .where(AnnotationTask.id != current_task_id)
            .where(AnnotationTask.status.in_([TaskStatus.IN_REVIEW, TaskStatus.READY_FOR_REVIEW]))
        )

        unassigned_review_tasks = (
            select(AnnotationTask)
            .join(AnnotationTask.task_type)
            .join(getattr(AnnotationTaskTypeModel, "_reviewers"))
            .add_columns(AnnotationTaskTypeModel.priority)
            .add_columns(literal(2).label(PRIORITY_LABEL))
            .where(AnnotationTaskTypeReviewer.user_id == user_id)
            .where(AnnotationTask.reviewer_user_id.is_(None))
            .where(AnnotationTask.annotator_user_id != user_id)
            .where(AnnotationTask.status.in_([TaskStatus.READY_FOR_REVIEW]))
        )

        task_id = (
            await session.scalars(
                union(assigned_review_tasks, unassigned_review_tasks).order_by(
                    asc(PRIORITY_LABEL),
                    asc(AnnotationTaskTypeModel.priority),
                    asc(AnnotationTask.start_time),
                )
            )
        ).first()

        if task_id is None:
            return task_id

        return await self.get_task(session=session, annotation_task_id=task_id)

    @overload
    def _get_task_query(self, expr: Update, task_query: TaskQuery) -> Update: ...
    @overload
    def _get_task_query(
        self, expr: Select[tuple[AnnotationTask]], task_query: TaskQuery
    ) -> Select[tuple[AnnotationTask]]: ...
    def _get_task_query(
        self, expr: Union[Select[tuple[AnnotationTask]], Update], task_query: TaskQuery
    ) -> Union[Select[tuple[AnnotationTask]], Update]:
        # Filter the Annotation Task's `start_time` and `end_time` for any
        # datetime overlap with the query's start/end time.
        if task_query.start_time is not None:
            expr = expr.filter(task_query.start_time < AnnotationTask.end_time)
        if task_query.end_time is not None:
            expr = expr.where(task_query.end_time > AnnotationTask.start_time)

        if task_query.min_updated_time is not None:
            expr = expr.where(AnnotationTask.updated_time >= task_query.min_updated_time)
        if task_query.max_updated_time is not None:
            expr = expr.where(AnnotationTask.updated_time <= task_query.max_updated_time)

        if task_query.statuses is not None:
            expr = expr.where(AnnotationTask.status.in_(task_query.statuses))
        if task_query.annotator_user_ids is not None:
            annotator_filter_obj = get_in_filter_with_null(
                AnnotationTask.annotator_user_id, task_query.annotator_user_ids
            )

            expr = expr.where(annotator_filter_obj)
        if task_query.reviewer_user_ids is not None:
            reviewer_filter_obj = get_in_filter_with_null(
                AnnotationTask.reviewer_user_id, task_query.reviewer_user_ids
            )

            expr = expr.where(reviewer_filter_obj)
        if task_query.organization_id is not None:
            expr = expr.where(AnnotationTask.org_id == task_query.organization_id)
        if task_query.site_id is not None:
            expr = expr.where(AnnotationTask.site_id == task_query.site_id)
        if task_query.room_id is not None:
            expr = expr.where(AnnotationTask.room_id == task_query.room_id)

        if task_query.type_ids is not None:
            type_id_filter_obj = get_in_filter_with_null(
                AnnotationTask.type_id, task_query.type_ids
            )
            expr = expr.where(type_id_filter_obj)

        return expr

    @async_db_session_provider
    async def query_tasks(
        self, session: AsyncSession, task_query: TaskQuery
    ) -> Sequence[AnnotationTask]:
        return (
            await session.scalars(
                self._get_task_query(expr=select(AnnotationTask), task_query=task_query)
            )
        ).all()

    async def __create_model(self, model: T, session: AsyncSession) -> T:
        try:
            session.add(model)
            await session.commit()
            await session.refresh(model)
            return model
        except sqlalchemy.exc.IntegrityError as e:
            if check_exception(UniqueViolationError, e):
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY, f"{model.__tablename__} record already exists"
                )

            if check_exception(CheckViolationError, e):
                raise ClientError(HTTPStatus.UNPROCESSABLE_ENTITY, str(e.orig))

            raise e

    @async_db_session_provider
    async def create_task(
        self, session: AsyncSession, annotation_task: AnnotationTask
    ) -> AnnotationTask:
        annotation_task.validate()
        return await self.__create_model(model=annotation_task, session=session)

    @async_db_session_provider
    async def create_annotation_task_schedule(
        self, session: AsyncSession, task_schedule_create: TaskScheduleCreate
    ) -> AnnotationTaskSchedule:
        schedule = AnnotationTaskSchedule()

        schedule.annotation_task_type_id = task_schedule_create.annotation_task_type_id
        schedule.start_time = task_schedule_create.start_time
        schedule.interval = task_schedule_create.interval
        schedule.sites = (
            await session.scalars(select(Site).filter(Site.id.in_(task_schedule_create.site_ids)))
        ).all()
        schedule.rooms = (
            await session.scalars(
                select(RoomModel).filter(RoomModel.id.in_(task_schedule_create.room_ids))
            )
        ).all()

        schedule.validate_and_coerce_values()

        return await self.__create_model(model=schedule, session=session)

    @async_db_session_provider
    async def create_annotation_task_type(
        self, session: AsyncSession, annotation_task_type: AnnotationTaskTypeModel
    ) -> AnnotationTaskTypeModel:
        await annotation_task_type.validate_and_coerce_values(session=session)

        return await self.__create_model(model=annotation_task_type, session=session)

    @async_db_session_provider
    async def create_tasks(
        self, session: AsyncSession, annotation_tasks: List[AnnotationTask]
    ) -> bool:
        try:
            await session.commit()
            for task in annotation_tasks:
                task.validate()
                session.add(task)
                await session.commit()
            return True
        except sqlalchemy.exc.IntegrityError as e:
            if check_exception(UniqueViolationError, e):
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY, f"annotation_task already exists"
                )
            raise e

    @async_db_session_provider
    async def bulk_update_tasks(
        self,
        session: AsyncSession,
        task_bulk_update: TaskBulkUpdate,
        task_query: Optional[TaskQuery] = None,
        task_ids: Optional[list[str]] = None,
    ) -> int:
        update_values: dict[Any, Any] = {
            "updated_by_user_id": task_bulk_update.updated_by_user_id,
        }

        if task_bulk_update.status is not None:
            update_values["status"] = task_bulk_update.status
            if task_bulk_update.status != TaskStatus.CANCELLED:
                update_values["cancelled_reason"] = None

        if task_bulk_update.cancelled_reason is not None:
            if task_bulk_update.status != TaskStatus.CANCELLED:
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    f"Cannot set a cancelled reason if status is not cancelled",
                )

            update_values["cancelled_reason"] = interpret_field_value(
                task_bulk_update.cancelled_reason
            )

        if task_bulk_update.review_reasons is not None:
            update_values["review_reasons"] = [
                str(reason) for reason in task_bulk_update.review_reasons
            ]

        if task_bulk_update.annotator_user_id is not None:
            update_values["annotator_user_id"] = interpret_field_value(
                task_bulk_update.annotator_user_id
            )
        if task_bulk_update.reviewer_user_id is not None:
            update_values["reviewer_user_id"] = interpret_field_value(
                task_bulk_update.reviewer_user_id
            )

        if task_ids is not None:
            result = await session.execute(
                update(AnnotationTask).where(AnnotationTask.id.in_(task_ids)).values(update_values)
            )
            await session.commit()
            return result.rowcount
        elif task_query is not None:
            result = await session.execute(
                self._get_task_query(expr=update(AnnotationTask), task_query=task_query).values(
                    update_values
                )
            )
            await session.commit()
            return result.rowcount
        else:
            raise ClientError(
                HTTPStatus.UNPROCESSABLE_ENTITY,
                "task_query and task_ids cannot both be None",
            )

    @async_db_session_provider
    async def update_task(self, session: AsyncSession, task_update: TaskUpdate) -> AnnotationTask:
        # Get the existing task
        task = await self.get_task(session=session, annotation_task_id=str(task_update.id))

        # this field is always updated
        task.updated_by_user_id = task_update.updated_by_user_id

        # Update existing task in the table
        if task_update.status is not None:
            task.status = task_update.status

            if task.status != TaskStatus.CANCELLED:
                task.cancelled_reason = None

        if task_update.cancelled_reason is not None:
            if task.status == TaskStatus.CANCELLED:
                task.cancelled_reason = interpret_field_value(task_update.cancelled_reason)
            else:
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    f"Cannot set a cancelled reason if status is not cancelled",
                )

        if task_update.review_reasons is not None:
            task.review_reasons = [str(reason) for reason in task_update.review_reasons]

        # Fields which are None, are not meant to be updated.
        if task_update.annotator_user_id is not None:
            task.annotator_user_id = interpret_field_value(task_update.annotator_user_id)
        if task_update.reviewer_user_id is not None:
            task.reviewer_user_id = interpret_field_value(task_update.reviewer_user_id)

        try:
            await session.commit()
            await session.refresh(task)
        except sqlalchemy.exc.IntegrityError as e:
            if check_exception(UniqueViolationError, e):
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    f"Encountered an error updating annotation_task: {get_exception_message(e)}",
                )
            raise e

        return task

    @async_db_session_provider
    async def update_annotation_task_type(
        self, session: AsyncSession, task_type_update: TaskTypeUpdate
    ) -> AnnotationTaskTypeModel:
        task_type = await self.get_annotation_task_type(
            session=session, id=str(task_type_update.id)
        )

        session.add(task_type)

        now = datetime.now(tz=timezone.utc)

        if task_type_update.name is not None:
            task_type.name = task_type_update.name

        if task_type_update.description is not None:
            task_type.description = task_type_update.description

        if task_type_update.event_types is not None:
            task_type.event_types = task_type_update.event_types

        if task_type_update.context_event_types is not None:
            task_type.context_event_types = task_type_update.context_event_types

        if task_type_update.annotator_ids is not None:
            for user_id in task_type_update.annotator_ids:
                if user_id not in task_type.annotator_ids:
                    task_type._annotators.append(AnnotationTaskTypeAnnotator(user_id=user_id))

            annotators = task_type._annotators[:]

            for annotator in annotators:
                if annotator.user_id not in task_type_update.annotator_ids:
                    task_type._annotators.remove(annotator)

        if task_type_update.provisional_annotator_ids is not None:
            for user_id in task_type_update.provisional_annotator_ids:
                if user_id not in task_type.provisional_annotator_ids:
                    task_type._provisional_annotators.append(
                        AnnotationTaskTypeProvisionalAnnotator(user_id=user_id)
                    )

            provisional_annnotators = task_type._provisional_annotators[:]

            for provisional_annnotator in provisional_annnotators:
                if provisional_annnotator.user_id not in task_type_update.provisional_annotator_ids:
                    task_type._provisional_annotators.remove(provisional_annnotator)

        if task_type_update.reviewer_ids is not None:
            for user_id in task_type_update.reviewer_ids:
                if user_id not in task_type.reviewer_ids:
                    task_type._reviewers.append(AnnotationTaskTypeReviewer(user_id=user_id))

            reviewers = task_type._reviewers[:]

            for reviewer in reviewers:
                if reviewer.user_id not in task_type_update.reviewer_ids:
                    task_type._reviewers.remove(reviewer)

        if task_type_update.archived is not None:
            if task_type_update.archived:
                if task_type.archived_time is None:
                    task_type.archived_time = now
            else:
                task_type.archived_time = None

        if task_type_update.priority is not None:
            task_type.priority = task_type_update.priority

        if task_type_update.detect_idle is not None:
            task_type.detect_idle = task_type_update.detect_idle

        if task_type_update.allow_skipping_review is not None:
            task_type.allow_skipping_review = task_type_update.allow_skipping_review

        if task_type_update.optimize_tasks is not None:
            task_type.optimize_tasks = task_type_update.optimize_tasks

        task_type.updated_time = now

        await task_type.validate_and_coerce_values(session=session)

        await session.commit()
        await session.refresh(task_type)

        return task_type

    async def update_annotation_task_schedule(
        self, task_schedule_update: TaskScheduleUpdate
    ) -> AnnotationTaskSchedule:
        async with new_async_session() as session:
            result = await session.execute(
                select(AnnotationTaskSchedule).filter(
                    AnnotationTaskSchedule.id == task_schedule_update.id
                )
            )

            schedule = result.unique().scalar_one_or_none()

            if schedule is None:
                raise ValueError("Schedule not found")

            if task_schedule_update.start_time is not None:
                schedule.start_time = task_schedule_update.start_time

            if task_schedule_update.interval is not None:
                schedule.interval = task_schedule_update.interval

            if task_schedule_update.site_ids is not None:
                new_site_ids = set(task_schedule_update.site_ids)
                current_site_ids = {site.id for site in schedule.sites}

                site_ids_to_add = new_site_ids - current_site_ids
                site_ids_to_remove = current_site_ids - new_site_ids

                if len(site_ids_to_remove) > 0:
                    sites_to_keep = []
                    for site in schedule.sites:
                        if site.id not in site_ids_to_remove:
                            sites_to_keep.append(site)

                    schedule.sites = sites_to_keep

                if len(site_ids_to_add) > 0:
                    site_query = SiteQuery(site_ids=list(site_ids_to_add))
                    sites = await SiteStore().query_sites(site_query=site_query)
                    schedule.sites.extend(sites)

            if task_schedule_update.room_ids is not None:
                new_room_ids = set(task_schedule_update.room_ids)
                current_room_ids = {room.id for room in schedule.rooms}

                room_ids_to_add = new_room_ids - current_room_ids
                room_ids_to_remove = current_room_ids - new_room_ids

                if len(room_ids_to_remove) > 0:
                    rooms_to_keep = []
                    for room in schedule.rooms:
                        if room.id not in room_ids_to_remove:
                            rooms_to_keep.append(room)

                    schedule.rooms = rooms_to_keep

                if len(room_ids_to_add) > 0:
                    rooms = await RoomStore().get_rooms(list(room_ids_to_add))
                    schedule.rooms.extend(rooms)

            schedule.validate_and_coerce_values()
            schedule = await session.merge(schedule)
            await session.commit()
            await session.refresh(schedule)

            return schedule

    @async_db_session_provider
    async def get_annotation_task_type(
        self, session: AsyncSession, id: Union[uuid.UUID, str]
    ) -> AnnotationTaskTypeModel:
        try:
            return (
                await session.scalars(
                    select(AnnotationTaskTypeModel).filter(AnnotationTaskTypeModel.id == id)
                )
            ).one()
        except sqlalchemy.orm.exc.NoResultFound:
            raise NotFound(f"No annotation task type found with id: {id}")

    def _get_base_annotation_task_types_query(
        self,
        keys: Optional[List[String]] = None,
        archived: Optional[bool] = None,
    ) -> Select[tuple[AnnotationTaskTypeModel]]:
        query = select(AnnotationTaskTypeModel).order_by(AnnotationTaskTypeModel.name)

        if keys is not None and len(keys) > 0:
            query = query.filter(AnnotationTaskTypeModel.id.in_(keys))

        if archived is not None:
            if archived:
                query = query.filter(AnnotationTaskTypeModel.archived_time.isnot(None))
            else:
                query = query.filter(AnnotationTaskTypeModel.archived_time.is_(None))

        return query

    @async_db_session_provider
    async def get_annotation_task_types(
        self,
        session: AsyncSession,
        keys: Optional[List[String]] = None,
        archived: Optional[bool] = None,
    ) -> List[AnnotationTaskTypeModel]:
        query = self._get_base_annotation_task_types_query(keys=keys, archived=archived)
        query = query.options(selectinload(AnnotationTaskTypeModel.schedules))
        return list((await session.scalars(query)).all())

    @async_db_session_provider
    async def get_annotation_task_types_for_generation(
        self,
        session: AsyncSession,
        keys: Optional[List[String]] = None,
        archived: Optional[bool] = None,
    ) -> List[AnnotationTaskTypeModel]:
        query = self._get_base_annotation_task_types_query(keys=keys, archived=archived)
        query = query.options(
            selectinload(AnnotationTaskTypeModel.schedules)
            .selectinload(AnnotationTaskSchedule.rooms)
            .selectinload(RoomModel.site),
            selectinload(AnnotationTaskTypeModel.schedules)
            .selectinload(AnnotationTaskSchedule.sites)
            .selectinload(Site.rooms),
        )
        return list((await session.scalars(query)).all())

    @async_db_session_provider
    async def get_unverified_phases_for_phase_ids(
        self,
        phase_ids: List[str],
        session: AsyncSession,
    ) -> Set[str]:
        """
        Finds which phases are in time periods that are not yet verified
        """
        start = aliased(EventModel)
        end = aliased(EventModel)
        room_remaining_tasks_query = (
            select(
                PhaseModel.id.label("phase_id"),
                start.start_time.label("phase_start"),
                end.start_time.label("phase_end"),
                AnnotationTask.start_time.label("annotation_start"),
                AnnotationTask.end_time.label("annotation_end"),
            )
            .join(start, PhaseModel.start_event)
            .join(end, PhaseModel.end_event)
            .join(
                AnnotationTask,
                and_(
                    AnnotationTask.start_time <= end.start_time,
                    AnnotationTask.end_time >= start.start_time,
                    AnnotationTask.room_id == PhaseModel.room_id,
                    AnnotationTask.status == TaskStatus.DONE,
                ),
            )
            .filter(
                PhaseModel.id.in_(phase_ids),
            )
            .order_by(PhaseModel.id, AnnotationTask.start_time)
        )
        scalar_results = await session.execute(room_remaining_tasks_query)
        room_remaining_tasks_results = list(scalar_results.all())
        # In order for a phase to be verified there have to be
        # `done` annotation tasks across it's whole time span
        phase_ranges: DefaultDict[str, List[PhaseAnnotationTaskCheck]] = defaultdict(list)
        unverifed_phase_ids = set()
        for row in room_remaining_tasks_results:
            item = row._asdict()
            phase_ranges[str(item["phase_id"])].append(
                PhaseAnnotationTaskCheck(
                    phase_id=item["phase_id"],
                    phase_start=item["phase_start"],
                    phase_end=item["phase_end"],
                    annotation_start=item["annotation_start"],
                    annotation_end=item["annotation_end"],
                )
            )
        for phase_id in phase_ids:
            if (
                not phase_ranges[phase_id]
                or phase_ranges[phase_id][0].phase_start
                < phase_ranges[phase_id][0].annotation_start
            ):
                unverifed_phase_ids.add(phase_id)
            else:
                check_time = phase_ranges[phase_id][0].annotation_end
                phase_end_time = phase_ranges[phase_id][0].phase_end
                for phase_row in phase_ranges[phase_id][1:]:
                    # While Annotation tasks should have exclusive timeranges
                    # that isn't enforced at a data level, so the logic is a little convoluted
                    # Basically we make sure there are no gaps in the time Verified
                    if check_time < phase_row.annotation_start:
                        unverifed_phase_ids.add(phase_id)
                        break
                    check_time = max(phase_row.annotation_end, check_time)
                if check_time < phase_end_time:
                    unverifed_phase_ids.add(phase_id)

        return unverifed_phase_ids
