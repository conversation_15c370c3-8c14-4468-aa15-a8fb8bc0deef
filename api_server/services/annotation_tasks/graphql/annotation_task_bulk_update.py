import graphene

from api_server.graphql.context import GrapheneInfo
from api_server.graphql.partial_updates import get_field_value_from_request_input
from api_server.services.annotation_tasks.annotation_task_models import (
    CancelledReasonGraphene,
    TaskBulkUpdate,
    TaskStatusGraphene,
    get_task_status_enum,
)
from api_server.services.annotation_tasks.graphql.annotation_task_query_input import (
    AnnotationTaskQueryInput,
)
from api_server.services.annotation_tasks.graphql.annotation_tasks import get_cancelled_reason


class AnnotationTaskBulkUpdateUpdateInput(graphene.InputObjectType):
    annotator_user_id = graphene.ID()
    reviewer_user_id = graphene.ID()
    status = TaskStatusGraphene()
    cancelled_reason = CancelledReasonGraphene()


class AnnotationTaskBulkUpdateInput(graphene.InputObjectType):
    update_input = graphene.NonNull(AnnotationTaskBulkUpdateUpdateInput)
    query_input = AnnotationTaskQueryInput()
    task_ids = graphene.List(graphene.NonNull(graphene.ID), required=False)


class AnnotationTaskBulkUpdate(graphene.Mutation):
    success = graphene.Boolean()
    count = graphene.Int()

    class Arguments:
        input = graphene.Argument(AnnotationTaskBulkUpdateInput, required=False)

    @staticmethod
    async def mutate(
        parent: None, info: GrapheneInfo, input: AnnotationTaskBulkUpdateInput
    ) -> "AnnotationTaskBulkUpdate":
        update_input = input.update_input

        count = await info.context.annotation_task_service.bulk_update_tasks(
            task_query=input.query_input,
            task_bulk_update=TaskBulkUpdate(
                reviewer_user_id=get_field_value_from_request_input(
                    update_input, "reviewer_user_id"
                ),
                annotator_user_id=get_field_value_from_request_input(
                    update_input, "annotator_user_id"
                ),
                status=get_task_status_enum(update_input.status),
                cancelled_reason=get_cancelled_reason(update_input),
            ),
            task_ids=input.task_ids,
        )

        return AnnotationTaskBulkUpdate(success=True, count=count)
