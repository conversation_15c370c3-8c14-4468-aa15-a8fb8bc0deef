import graphene

from api_server.graphql.context import GrapheneInfo
from api_server.services.annotation_tasks.graphql.annotation_tasks import AnnotationTask


class AnnotationTaskExitInput(graphene.InputObjectType):
    task_id = graphene.ID(required=True)


class AnnotationTaskExit(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(AnnotationTaskExitInput, required=True)

    success = graphene.Boolean(required=True)
    task = graphene.Field(AnnotationTask, required=False)

    @staticmethod
    async def mutate(
        parent: None, info: GrapheneInfo, input: AnnotationTaskExitInput
    ) -> "AnnotationTaskExit":
        return AnnotationTaskExit(
            success=True, task=info.context.annotation_task_service.exit_task(input.task_id)
        )
