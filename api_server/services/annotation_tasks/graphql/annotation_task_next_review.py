import graphene

from api_server.graphql.context import GrapheneInfo
from api_server.services.annotation_tasks.annotation_task_models import (
    TaskStatusGraphene,
    get_task_status_enum,
)
from api_server.services.annotation_tasks.graphql.annotation_tasks import AnnotationTask


class AnnotationTaskNextReviewInput(graphene.InputObjectType):
    current_task_id = graphene.ID(required=False)
    status = TaskStatusGraphene()


class AnnotationTaskNextReview(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(AnnotationTaskNextReviewInput, required=True)

    success = graphene.Boolean(required=True)
    current_task = graphene.Field(AnnotationTask, required=False)
    next_task = graphene.Field(AnnotationTask, required=False)

    @staticmethod
    async def mutate(
        parent: None, info: GrapheneInfo, input: AnnotationTaskNextReviewInput
    ) -> "AnnotationTaskNextReview":
        (current_task, next_task) = await info.context.annotation_task_service.next_review_task(
            current_task_id=input.current_task_id, status=get_task_status_enum(input.status)
        )

        return AnnotationTaskNextReview(
            success=True, current_task=current_task, next_task=next_task
        )
