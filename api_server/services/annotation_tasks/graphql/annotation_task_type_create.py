import graphene

from api_server.graphql.context import GrapheneInfo
from api_server.services.annotation_tasks.annotation_task_store import (
    AnnotationTaskSchedule as AnnotationTaskScheduleModel,
)
from api_server.services.annotation_tasks.annotation_task_store import (
    AnnotationTaskT<PERSON><PERSON>nn<PERSON><PERSON>,
    AnnotationTaskTypeModel,
    AnnotationTaskTypeProvisionalAnnotator,
    AnnotationTaskTypeReviewer,
)
from api_server.services.annotation_tasks.graphql.annotation_tasks import (
    AnnotationTaskType,
)
from api_server.services.site.site_models import SiteQuery


class AnnotationTaskScheduleInput(graphene.InputObjectType):
    start_time = graphene.Time(required=True)
    interval = graphene.Int(required=True)
    site_ids = graphene.List(graphene.NonNull(graphene.ID), required=False, default_value=[])
    room_ids = graphene.List(graphene.NonNull(graphene.ID), required=False, default_value=[])


class AnnnotationTaskTypeCreateInput(graphene.InputObjectType):
    name = graphene.String(required=True)
    description = graphene.String(required=True)
    event_types = graphene.List(graphene.NonNull(graphene.ID), required=False, default_value=[])
    context_event_types = graphene.List(
        graphene.NonNull(graphene.ID), required=False, default_value=[]
    )

    annotator_ids = graphene.List(graphene.NonNull(graphene.ID), required=False, default_value=[])
    provisional_annotator_ids = graphene.List(
        graphene.NonNull(graphene.ID), required=False, default_value=[]
    )
    reviewer_ids = graphene.List(graphene.NonNull(graphene.ID), required=False, default_value=[])
    priority = graphene.Int(required=False, default_value=5, description="1 = highest")
    detect_idle = graphene.Boolean(required=False, default_value=False)
    allow_skipping_review = graphene.Boolean(required=False, default_value=False)
    optimize_tasks = graphene.Boolean(required=False, default_value=False)

    schedules = graphene.List(graphene.NonNull(AnnotationTaskScheduleInput), default_value=[])


class AnnnotationTaskTypeCreate(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(AnnnotationTaskTypeCreateInput, required=True)

    success = graphene.Boolean(required=True)
    created_annotation_task_type = graphene.Field(AnnotationTaskType, required=True)

    @staticmethod
    async def mutate(
        parent: None, info: GrapheneInfo, input: AnnnotationTaskTypeCreateInput
    ) -> "AnnnotationTaskTypeCreate":
        annotation_task_type = AnnotationTaskTypeModel(
            name=input.name,
            description=input.description,
            event_types=input.event_types,
            context_event_types=input.context_event_types,
            priority=input.priority,
            detect_idle=input.detect_idle,
            allow_skipping_review=input.allow_skipping_review,
            optimize_tasks=input.optimize_tasks,
        )

        for s in input.schedules:
            schedule = AnnotationTaskScheduleModel(start_time=s.start_time, interval=s.interval)
            site_query = SiteQuery(site_ids=s.site_ids)
            schedule.sites.extend(await info.context.site_service.query_sites(site_query))
            schedule.rooms.extend(await info.context.room_service.get_rooms(s.room_ids))
            # schedules are a lazy loaded relationship that is created using classical forms
            annotation_task_type.schedules.append(schedule)

        annotator_ids = input.annotator_ids
        for user_id in annotator_ids:
            # _annotations are a lazy loaded relationship that is created using classical forms
            annotation_task_type._annotators.append(  # type: ignore [attr-defined]
                AnnotationTaskTypeAnnotator(user_id=user_id)
            )

        for user_id in input.provisional_annotator_ids:
            # _provisional_annotations are a lazy loaded relationship that is created using classical forms
            annotation_task_type._provisional_annotators.append(  # type: ignore [attr-defined]
                AnnotationTaskTypeProvisionalAnnotator(user_id=user_id)
            )

        for user_id in input.reviewer_ids:
            # _reviers are a lazy loaded relationship that is created using classical forms
            annotation_task_type._reviewers.append(  # type: ignore [attr-defined]
                AnnotationTaskTypeReviewer(user_id=user_id)
            )

        new_annotation_task_type = (
            await info.context.annotation_task_service.create_annnotation_task_type(
                annotation_task_type
            )
        )

        return AnnnotationTaskTypeCreate(
            success=True, created_annotation_task_type=new_annotation_task_type
        )
