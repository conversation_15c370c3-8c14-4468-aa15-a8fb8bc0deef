import graphene

from api_server.graphql.context import GrapheneInfo
from api_server.graphql.partial_updates import get_field_value_from_request_input
from api_server.services.annotation_tasks.annotation_task_models import (
    CancelledReasonGraphene,
    TaskStatusGraphene,
    TaskUpdate,
    get_task_status_enum,
)
from api_server.services.annotation_tasks.graphql.annotation_tasks import (
    AnnotationTask,
    get_cancelled_reason,
)


class AnnotationTaskUpdateInput(graphene.InputObjectType):
    id = graphene.ID(required=True)
    annotator_user_id = graphene.ID()
    reviewer_user_id = graphene.ID()
    status = TaskStatusGraphene()
    cancelled_reason = CancelledReasonGraphene()


class AnnotationTaskUpdate(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(AnnotationTaskUpdateInput)

    success = graphene.Boolean()
    updated_annotation_task = graphene.Field(AnnotationTask)

    @staticmethod
    async def mutate(
        parent: None, info: GrapheneInfo, input: AnnotationTaskUpdateInput
    ) -> "AnnotationTaskUpdate":
        task_update = TaskUpdate(
            id=input.id,
            reviewer_user_id=get_field_value_from_request_input(input, "reviewer_user_id"),
            annotator_user_id=get_field_value_from_request_input(input, "annotator_user_id"),
            status=get_task_status_enum(input.status),
            cancelled_reason=get_cancelled_reason(input),
        )

        updated_annotation_task = await info.context.annotation_task_service.update_task(
            task_update=task_update
        )

        return AnnotationTaskUpdate(success=True, updated_annotation_task=updated_annotation_task)
