# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

from collections import Counter
from typing import Literal, Optional, Union

import graphene
from graphql import GraphQLError

import api_server.services.organization.graphql.organization as organization_schema
import api_server.services.room.graphql.room as room_schema
import api_server.services.site.graphql.site as site_schema
import api_server.services.users.graphql.user_type as user_schema
from apella_cloud_api.constants import UNSET_TOKEN
from apella_cloud_api.exceptions import NotFound
from api_server.graphql.context import GrapheneInfo
from api_server.graphql.pagination import PaginationConnection
from api_server.graphql.partial_updates import get_field_value_from_request_input
from api_server.services.annotation_tasks import annotation_task_store
from api_server.services.annotation_tasks.annotation_task_models import (
    CancelledReason,
    CancelledReasonGraphene,
    TaskStatusGraphene,
)
from api_server.services.annotation_tasks.annotation_task_store import (
    AnnotationTaskTypeModel,
)
from api_server.services.annotation_tasks.graphql.annotation_tasks_count import (
    ANNOTATION_TASKS_COUNT_FIELDS,
    AnnotationTasksCount,
    TasksCountObj,
)
from api_server.services.events.event_store import EventModel
from api_server.services.events.graphql.event import Event
from api_server.services.organization.organization_db import Organization
from api_server.services.room.room_store import RoomModel
from api_server.services.site.site_store import Site as SiteModel
from api_server.services.users.user_store import User


class AnnotationTask(graphene.ObjectType):
    id = graphene.ID(required=True)

    start_time = graphene.DateTime(required=True)
    end_time = graphene.DateTime(required=True)
    status = TaskStatusGraphene(required=True)
    cancelled_reason = CancelledReasonGraphene(required=False)

    updated_time = graphene.DateTime(required=True)

    org_id = graphene.String(required=True)
    organization = graphene.Field(lambda: organization_schema.Organization, required=True)

    site_id = graphene.String(required=True)
    site = graphene.Field(lambda: site_schema.Site, required=True)

    room_id = graphene.String(required=True)
    room = graphene.Field(lambda: room_schema.Room, required=True)

    annotator_user_id = graphene.String(required=False)
    annotator = graphene.Field(lambda: user_schema.User, required=False)

    reviewer_user_id = graphene.String(required=False)
    reviewer = graphene.Field(lambda: user_schema.User, required=False)

    updated_by_user_id = graphene.String(required=False)
    updated_by_user = graphene.Field(lambda: user_schema.User, required=False)

    type_id = graphene.String(required=True)
    type = graphene.Field(lambda: AnnotationTaskType, required=True)

    events = graphene.List(graphene.NonNull(Event), required=True)
    context_events = graphene.List(graphene.NonNull(Event), required=True)

    @staticmethod
    async def resolve_events(
        annotation_task: annotation_task_store.AnnotationTask,
        info: GrapheneInfo,
        **kwargs,
    ) -> list[EventModel]:
        return await info.context.annotation_task_service.get_annotation_task_events(
            annotation_task
        )

    @staticmethod
    async def resolve_context_events(
        annotation_task: annotation_task_store.AnnotationTask,
        info: GrapheneInfo,
        **kwargs,
    ) -> list[EventModel]:
        return await info.context.annotation_task_service.get_annotation_task_context_events(
            annotation_task
        )

    @staticmethod
    def resolve_status(
        annotation_task: annotation_task_store.AnnotationTask,
        info: GrapheneInfo,
        **kwargs,
    ):
        return annotation_task.status

    @staticmethod
    async def resolve_annotator(
        annotation_task: annotation_task_store.AnnotationTask,
        info: GrapheneInfo,
        **kwargs,
    ) -> Optional[User]:
        if annotation_task.annotator_user_id is None:
            return None

        try:
            user = await info.context.user_loader.load(annotation_task.annotator_user_id)
            assert isinstance(user, User)
            return user
        except NotFound:
            return None

    @staticmethod
    async def resolve_reviewer(
        annotation_task: annotation_task_store.AnnotationTask,
        info: GrapheneInfo,
        **kwargs,
    ) -> Optional[User]:
        if annotation_task.reviewer_user_id is None:
            return None

        try:
            user = await info.context.user_loader.load(annotation_task.reviewer_user_id)
            assert isinstance(user, User)
            return user
        except NotFound:
            return None

    @staticmethod
    async def resolve_organization(
        annotation_task: annotation_task_store.AnnotationTask,
        info: GrapheneInfo,
        **kwargs,
    ) -> Organization:
        organization: Optional[Organization] = await info.context.org_loader.load(
            annotation_task.org_id
        )
        if organization is None:
            raise GraphQLError(
                message=f"Unable to find organization for annotation task with org id: {annotation_task.org_id}"
            )
        return organization

    @staticmethod
    async def resolve_site(
        annotation_task: annotation_task_store.AnnotationTask,
        info: GrapheneInfo,
        **kwargs,
    ) -> SiteModel:
        site: Optional[SiteModel] = await info.context.site_loader.load(annotation_task.site_id)
        if site is None:
            raise GraphQLError(
                message=f"Unable to find site for annotation task with site id: {annotation_task.site_id}"
            )
        return site

    @staticmethod
    async def resolve_room(
        annotation_task: annotation_task_store.AnnotationTask,
        info: GrapheneInfo,
        **kwargs,
    ) -> RoomModel:
        room: Optional[RoomModel] = await info.context.room_loader.load(annotation_task.room_id)
        if room is None:
            raise GraphQLError(
                message=f"Unable to find room for annotation task with site id: {annotation_task.room_id}"
            )
        return room

    @staticmethod
    async def resolve_type(
        annotation_task: annotation_task_store.AnnotationTask,
        info: GrapheneInfo,
        **kwargs,
    ) -> AnnotationTaskTypeModel:
        annotation_task_type: Optional[
            AnnotationTaskTypeModel
        ] = await info.context.annotation_task_type_loader.load(str(annotation_task.type_id))
        if annotation_task_type is None:
            raise GraphQLError(
                message=f"Unable to find task type for annotation task with type id: {annotation_task.type_id}"
            )
        return annotation_task_type


class AnnotationTaskConnection(PaginationConnection):
    class Meta:
        node = AnnotationTask

    class AnnotationTaskEdge(graphene.ObjectType):
        node = graphene.Field(AnnotationTask, required=True)
        cursor = graphene.String(required=True)

    edges = graphene.List(graphene.NonNull(AnnotationTaskEdge), required=True)
    counts = graphene.Field(AnnotationTasksCount)

    @staticmethod
    def resolve_counts(task_connection, info: GrapheneInfo, *args, **kwargs):
        count_object = TasksCountObj

        for count_field in ANNOTATION_TASKS_COUNT_FIELDS:
            counter = Counter(
                map(lambda task: getattr(task, count_field), task_connection.iterable)
            )
            count_object.set_field_value(
                field=count_field,
                value=[{"parent_id": key, "count": str(value)} for (key, value) in counter.items()],
            )

        return count_object


class AnnotationTaskSchedule(graphene.ObjectType):
    id = graphene.ID(required=True)
    start_time = graphene.Time(required=True)
    interval = graphene.Int(required=True)

    sites = graphene.List(graphene.NonNull(site_schema.Site))
    rooms = graphene.List(graphene.NonNull(room_schema.Room))

    annotation_task_type_id = graphene.ID(required=True)
    annotation_task_type = graphene.Field(lambda: AnnotationTaskType, required=True)


class AnnotationTaskType(graphene.ObjectType):
    id = graphene.ID(
        resolver=lambda annotation_task_type, info: annotation_task_type.id,
        required=True,
    )
    name = graphene.String(
        resolver=lambda annotation_task_type, info: annotation_task_type.name,
        required=True,
    )
    description = graphene.String(
        resolver=lambda annotation_task_type, info: annotation_task_type.description
    )
    event_types = graphene.List(graphene.NonNull(graphene.String))

    context_event_types = graphene.List(graphene.NonNull(graphene.String))

    archived_time = graphene.DateTime()

    schedules = graphene.List(graphene.NonNull(AnnotationTaskSchedule))

    annotator_ids = graphene.List(graphene.NonNull(graphene.ID))

    provisional_annotator_ids = graphene.List(graphene.NonNull(graphene.ID))

    reviewer_ids = graphene.List(graphene.NonNull(graphene.ID))

    priority = graphene.Int(required=True, description="1 = highest, 5 = lowest")

    detect_idle = graphene.Boolean(required=True)

    allow_skipping_review = graphene.Boolean(required=True)

    optimize_tasks = graphene.Boolean(required=True)


def get_cancelled_reason(
    input: graphene.InputObjectType,
) -> Optional[Union[CancelledReason, Literal["UNSET"]]]:
    cancelled_reason = get_field_value_from_request_input(input, "cancelled_reason")

    if cancelled_reason is None or cancelled_reason == UNSET_TOKEN:
        return cancelled_reason

    return CancelledReason(cancelled_reason)
