from aiodataloader import DataLoader

from api_server.services.annotation_tasks.annotation_task_service import (
    AnnotationTaskService,
)


class RemainingAnnotationTasksLoader(DataLoader[str, bool]):
    annotation_task_service: AnnotationTaskService

    def __init__(self, annotation_task_service: AnnotationTaskService):
        super().__init__()
        self.annotation_task_service = annotation_task_service

    async def batch_load_fn(self, keys: list[str]) -> list[bool]:
        # Fetch the phases with outstanding annotation tasks by the set of keys
        room_remaining_tasks_results = (
            await self.annotation_task_service.get_unverified_phases_for_phase_ids(keys)
        )
        # Based on the ordering of the keys passed into the function re-sort the returned room
        # time range
        return [key not in room_remaining_tasks_results for key in keys]
