from typing import Optional
from annotation_task_optimizer.annotation_task_optimizer import AnnotationTaskOptimizer

from config import data_platform_gcp_project
from databases.big_query import BigQueryClient


# wrapper around https://github.com/Apella-Technology/annotation-task-optimizer
class OptimizerService:
    _optimizer_instance: Optional[AnnotationTaskOptimizer] = None

    @classmethod
    def get_instance(cls) -> AnnotationTaskOptimizer:
        if cls._optimizer_instance is None:
            bq_client = BigQueryClient.get_client()
            cls._optimizer_instance = AnnotationTaskOptimizer(
                bq_client=bq_client, data_platform_project_id=data_platform_gcp_project()
            )

        return cls._optimizer_instance
