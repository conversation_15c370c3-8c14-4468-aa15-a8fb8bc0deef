import asyncio
import uuid
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum, auto
from typing import Dict, List, Optional, Tuple

import graphene
import sqlalchemy
from sqlalchemy import (
    and_,
    literal,
    or_,
    select,
    DateTime,
    Boolean,
    ColumnElement,
    BinaryExpression,
)
from sqlalchemy.orm import aliased, joinedload, contains_eager, mapped_column, Mapped

from apella_cloud_api.api_server_schema import PhaseStatus
from api_server.services.case_forecasts.case_forecast_store import (
    CaseForecastModel,
    CaseForecastStatus,
)
from api_server.services.case.case_matching_store import CaseMatchingStatusReasonModel
from api_server.services.case.case_staff_store import CaseStaffModel
from api_server.services.case.case_status import SCHEDULED
from api_server.services.case.case_store import Case, PatientClass
from api_server.services.events.event_store import EventModel
from api_server.services.observations.observation_store import ObservationModel
from api_server.services.phases.phase_store import PhaseModel
from api_server.services.phases.phase_type import CASE, MAX_PHASE_LENGTH
from api_server.services.phases.source_type import CASE_SOURCE_TYPES
from api_server.services.phases.source_type import UNIFIED
from api_server.services.room.room_store import RoomModel
from api_server.tracing.trace_decorator import class_traced
from databases.sql.enums import CaseMatchingStatus
from databases.sql import new_async_session, Base
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import String

from databases.sql.mixins import OrgIdMixin, SiteIdMixin, EventMatchingStatus, Decodable
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from typing import Union


class CaseSource(Enum):
    INTERNAL = auto()
    EXTERNAL = auto()


class CaseType(Enum):
    LIVE = auto()
    FORECAST = auto()
    COMPLETE = auto()

    @staticmethod
    def from_str(case_type_str: str) -> "CaseType":
        try:
            enum = {el.name: el for el in CaseType}.get(case_type_str)
            if enum is not None:
                return enum
            else:
                raise NotImplementedError(f"Invalid case type: {case_type_str}")
        except Exception:
            raise NotImplementedError(f"Invalid case type: {case_type_str}")


class CaseStatusName(Enum):
    SCHEDULED = "SCHEDULED"
    IN_FACILITY = "IN_FACILITY"
    PRE_PROCEDURE = "PRE_PROCEDURE"
    PRE_PROCEDURE_COMPLETE = "PRE_PROCEDURE_COMPLETE"
    IN_HOLD = "IN_HOLD"
    PREP = "PREP"
    SURGERY = "SURGERY"
    WRAP_UP = "WRAP_UP"
    ACTUAL = "ACTUAL"
    RECOVERY = "RECOVERY"
    PHASE_II = "PHASE_II"
    COMPLETE = "COMPLETE"


APELLA_CASE_STATUS_PRIORITY = {
    CaseStatusName.SCHEDULED: 0,
    CaseStatusName.IN_FACILITY: 100,
    CaseStatusName.PRE_PROCEDURE: 200,
    CaseStatusName.PRE_PROCEDURE_COMPLETE: 250,
    CaseStatusName.IN_HOLD: 300,
    CaseStatusName.PREP: 400,
    # Cases can have multiple drapings, in which case they can go from surgery
    # to wrap-up then back to surgery. To account for this, we set the priority
    # of surgery and wrap-up to be equal.
    CaseStatusName.SURGERY: 500,
    CaseStatusName.WRAP_UP: 500,
    CaseStatusName.ACTUAL: 600,
    CaseStatusName.RECOVERY: 700,
    CaseStatusName.PHASE_II: 800,
    CaseStatusName.COMPLETE: 900,
}

APELLA_DEFAULT_CASE_STATUS_MAP = {
    CaseType.LIVE: CaseStatusName.SURGERY,
    CaseType.COMPLETE: CaseStatusName.ACTUAL,
    CaseType.FORECAST: CaseStatusName.SCHEDULED,
}


class LiveEvent(Enum):
    PATIENT_WHEELS_IN = "patient_wheels_in"
    PATIENT_WHEELS_OUT = "patient_wheels_out"
    BACK_TABLE_OPEN = "back_table_open"
    ENDO_PACK_OPEN = "endo_pack_open"
    PATIENT_DRAPED = "patient_draped"
    PATIENT_UNDRAPED = "patient_undraped"
    ANESTHESIA_DRAPING = "anesthesia_draping"
    ANESTHESIA_UNDRAPING = "anesthesia_undraping"
    PATIENT_XFER_TO_BED = "patient_xfer_to_bed"
    PATIENT_XFER_TO_OR_TABLE = "patient_xfer_to_or_table"
    TERMINAL_CLEAN_START = "terminal_clean_start"
    TERMINAL_CLEAN_END = "terminal_clean_end"


class ObservationType(Enum):
    OBSERVED_IN_FACILITY = "OBSERVED_IN_FACILITY"
    OBSERVED_IN_PRE_PROCEDURE = "OBSERVED_IN_PRE_PROCEDURE"
    OBSERVED_PRE_PROCEDURE_COMPLETE = "OBSERVED_PRE_PROCEDURE_COMPLETE"
    OBSERVED_IN_HOLDING_AREA = "OBSERVED_IN_HOLDING_AREA"
    OBSERVED_IN_ROOM = "OBSERVED_IN_ROOM"
    OBSERVED_CASE_CLOSING = "OBSERVED_CASE_CLOSING"
    OBSERVED_IN_PACU = "OBSERVED_IN_PACU"
    OBSERVED_RETURN_TO_PACU = "OBSERVED_RETURN_TO_PACU"
    OBSERVED_RETURN_TO_PACU_2ND_TIME = "OBSERVED_RETURN_TO_PACU_2ND_TIME"
    OBSERVED_RETURN_TO_PACU_3RD_TIME = "OBSERVED_RETURN_TO_PACU_3RD_TIME"
    OBSERVED_IN_PROCEDURAL_RECOVERY = "OBSERVED_IN_PROCEDURAL_RECOVERY"
    OBSERVED_IN_PHASE_II = "OBSERVED_IN_PHASE_II"
    OBSERVED_RETURN_TO_PHASE_II = "OBSERVED_RETURN_TO_PHASE_II"
    OBSERVED_PROCEDURAL_CARE_COMPLETE = "OBSERVED_PROCEDURAL_CARE_COMPLETE"
    OBSERVED_OUT_OF_PACU = "OBSERVED_OUT_OF_PACU"
    OBSERVED_OUT_OF_PHASE_II = "OBSERVED_OUT_OF_PHASE_II"
    OBSERVED_PHASE_II_CARE_COMPLETE = "OBSERVED_PHASE_II_CARE_COMPLETE"
    OBSERVED_OUT_OF_PACU_2ND_TIME = "OBSERVED_OUT_OF_PACU_2ND_TIME"
    OBSERVED_OUT_OF_PACU_3RD_TIME = "OBSERVED_OUT_OF_PACU_3RD_TIME"
    OBSERVED_ANESTHESIA_READY = "OBSERVED_ANESTHESIA_READY"
    OBSERVED_CASE_START = "OBSERVED_CASE_START"


EVENT_TO_CASE_STATUS_MAP = {
    LiveEvent.PATIENT_WHEELS_IN: CaseStatusName.PREP,
    LiveEvent.PATIENT_XFER_TO_OR_TABLE: CaseStatusName.PREP,
    LiveEvent.ANESTHESIA_DRAPING: CaseStatusName.PREP,
    LiveEvent.ANESTHESIA_UNDRAPING: CaseStatusName.PREP,
    LiveEvent.PATIENT_DRAPED: CaseStatusName.SURGERY,
    LiveEvent.PATIENT_UNDRAPED: CaseStatusName.WRAP_UP,
    LiveEvent.PATIENT_XFER_TO_BED: CaseStatusName.WRAP_UP,
    LiveEvent.PATIENT_WHEELS_OUT: CaseStatusName.ACTUAL,
}

OBSERVATION_TO_CASE_STATUS_MAP = {
    ObservationType.OBSERVED_IN_FACILITY: CaseStatusName.IN_FACILITY,
    ObservationType.OBSERVED_IN_PRE_PROCEDURE: CaseStatusName.PRE_PROCEDURE,
    ObservationType.OBSERVED_PRE_PROCEDURE_COMPLETE: CaseStatusName.PRE_PROCEDURE_COMPLETE,
    ObservationType.OBSERVED_IN_HOLDING_AREA: CaseStatusName.IN_HOLD,
    ObservationType.OBSERVED_IN_PACU: CaseStatusName.RECOVERY,
    ObservationType.OBSERVED_RETURN_TO_PACU: CaseStatusName.RECOVERY,
    ObservationType.OBSERVED_RETURN_TO_PACU_2ND_TIME: CaseStatusName.RECOVERY,
    ObservationType.OBSERVED_RETURN_TO_PACU_3RD_TIME: CaseStatusName.RECOVERY,
    ObservationType.OBSERVED_IN_PROCEDURAL_RECOVERY: CaseStatusName.RECOVERY,
    ObservationType.OBSERVED_IN_PHASE_II: CaseStatusName.PHASE_II,
    ObservationType.OBSERVED_RETURN_TO_PHASE_II: CaseStatusName.PHASE_II,
    ObservationType.OBSERVED_PROCEDURAL_CARE_COMPLETE: CaseStatusName.COMPLETE,
    ObservationType.OBSERVED_OUT_OF_PACU: CaseStatusName.COMPLETE,
    ObservationType.OBSERVED_OUT_OF_PHASE_II: CaseStatusName.COMPLETE,
    ObservationType.OBSERVED_PHASE_II_CARE_COMPLETE: CaseStatusName.COMPLETE,
    ObservationType.OBSERVED_OUT_OF_PACU_2ND_TIME: CaseStatusName.COMPLETE,
    ObservationType.OBSERVED_OUT_OF_PACU_3RD_TIME: CaseStatusName.COMPLETE,
}

PRE_OP_OBSERVATIONS = [
    ObservationType.OBSERVED_IN_FACILITY,
    ObservationType.OBSERVED_IN_PRE_PROCEDURE,
    ObservationType.OBSERVED_IN_HOLDING_AREA,
    ObservationType.OBSERVED_PRE_PROCEDURE_COMPLETE,
]

POST_OP_OBSERVATIONS = [
    ObservationType.OBSERVED_IN_PACU,
    ObservationType.OBSERVED_RETURN_TO_PACU,
    ObservationType.OBSERVED_RETURN_TO_PACU_2ND_TIME,
    ObservationType.OBSERVED_RETURN_TO_PACU_3RD_TIME,
    ObservationType.OBSERVED_IN_PROCEDURAL_RECOVERY,
    ObservationType.OBSERVED_IN_PHASE_II,
    ObservationType.OBSERVED_RETURN_TO_PHASE_II,
    ObservationType.OBSERVED_PROCEDURAL_CARE_COMPLETE,
    ObservationType.OBSERVED_OUT_OF_PACU,
    ObservationType.OBSERVED_OUT_OF_PHASE_II,
    ObservationType.OBSERVED_PHASE_II_CARE_COMPLETE,
    ObservationType.OBSERVED_OUT_OF_PACU_2ND_TIME,
    ObservationType.OBSERVED_OUT_OF_PACU_3RD_TIME,
]


@dataclass
class ApellaCaseStatus:
    name: CaseStatusName
    source: CaseSource
    since: Optional[datetime] = None


class ScheduledAndActualCase(Base, OrgIdMixin, SiteIdMixin, Decodable):
    __tablename__ = "scheduled_and_actual_cases_v2"

    # Key Fields
    id: Mapped[str] = mapped_column(String, nullable=False)
    phase_id: Mapped[str] = mapped_column(String, nullable=False, primary_key=True)
    case_id: Mapped[str] = mapped_column(String, nullable=False, primary_key=True)

    # Computed fields
    org_id: Mapped[str] = mapped_column(String, nullable=False)
    site_id: Mapped[str] = mapped_column(String, nullable=False)
    room_id: Mapped[str] = mapped_column(String, nullable=False)
    start_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
    )
    end_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
    )
    min_start_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    max_end_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
    case_type: Mapped[str] = mapped_column(
        String,
        nullable=False,
    )

    # Predicted/actuals
    phase_site_id: Mapped[str] = mapped_column(String, nullable=True)
    phase_room_id: Mapped[str] = mapped_column(String, nullable=True)
    start_event_id: Mapped[str] = mapped_column(
        String,
        nullable=True,
    )
    end_event_id: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    event_matching_status: Mapped[EventMatchingStatus] = mapped_column(
        sqlalchemy.Enum(EventMatchingStatus),
        nullable=True,
    )
    actual_start_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
    )
    actual_end_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
    )

    # Scheduled Fields
    case_site_id: Mapped[str] = mapped_column(String, nullable=True)
    case_room_id: Mapped[str] = mapped_column(String, nullable=True)
    scheduled_start_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
    scheduled_end_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
    scheduled_case_status: Mapped[str] = mapped_column(String, nullable=True)
    case_classification_types_id: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    external_case_id: Mapped[str] = mapped_column(String, nullable=True, index=True)
    service_line_id: Mapped[Union[uuid.UUID, None]] = mapped_column(
        UUID(as_uuid=True), nullable=True
    )
    is_add_on: Mapped[Union[bool, None]] = mapped_column(Boolean, nullable=True)
    patient_class: Mapped[PatientClass] = mapped_column(
        sqlalchemy.Enum(PatientClass), nullable=True
    )
    cancellation_reason: Mapped[Union[list[str], None]] = mapped_column(
        ARRAY(String), nullable=True
    )
    scheduled_created_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
    scheduled_updated_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)


@dataclass
class ApellaCase:
    _actual: Optional[PhaseModel]
    _case: Optional[Case]
    _case_forecast: Optional[CaseForecastModel]
    _apella_case_model: Optional[ScheduledAndActualCase]

    @property
    def case_or_case_id(self) -> Optional[Case | str]:
        if self._case is not None:
            return self._case

        case_id = (
            self._apella_case_model.case_id.strip() if self._apella_case_model is not None else None
        )

        return case_id if case_id else None

    @property
    def case_forecast(self) -> Optional[CaseForecastModel]:
        if self._case_forecast is not None:
            return self._case_forecast
        return None

    @property
    def actual_phase_or_phase_id(self) -> Optional[PhaseModel | str]:
        if self._actual is not None:
            return self._actual

        phase_id = (
            self._apella_case_model.phase_id.strip()
            if self._apella_case_model is not None
            else None
        )

        return phase_id if phase_id else None

    @property
    def id(self) -> str:
        if self._apella_case_model is not None:
            return self._apella_case_model.id
        if self._case is not None:
            return f"case:{self._case.case_id}"

        if self._actual is not None:
            return f"phase:{self._actual.id}"

        if self._case_forecast is not None:
            return f"forecast_case:{self._case_forecast.id}"

        raise Exception("Could not determine apella case id")

    @property
    def room_id(self) -> str:
        if self._apella_case_model is not None:
            return self._apella_case_model.room_id
        if self._actual is not None:
            return self._actual.room_id
        if self._case is not None:
            return self._case.room_id
        if self._case_forecast is not None:
            return self._case_forecast.room_id

        raise Exception("Could not determine room")

    @property
    def site_id(self) -> str:
        if self._apella_case_model is not None:
            return self._apella_case_model.site_id
        if self._actual is not None:
            return self._actual.site_id
        if self._case is not None:
            return self._case.site_id
        if self._case_forecast is not None:
            return self._case_forecast.site_id

        raise Exception("Could not determine site")

    @property
    def case_id(self) -> Optional[str]:
        if self._apella_case_model is not None:
            case_id = self._apella_case_model.case_id.strip()
            return case_id if case_id else None

        if self._actual is not None and self._actual.case_id is not None:
            return self._actual.case_id

        if self._case is not None:
            return self._case.case_id

        return None

    @property
    def scheduled_start_time(self) -> Optional[datetime]:
        if self._apella_case_model is not None:
            return self._apella_case_model.scheduled_start_time

        if self._case is not None:
            return self._case.scheduled_start_time

        return None

    @property
    def scheduled_end_time(self) -> Optional[datetime]:
        if self._apella_case_model is not None:
            return self._apella_case_model.scheduled_end_time

        if self._case is not None:
            return self._case.scheduled_end_time

        return None

    @property
    def scheduled_case_status(self) -> Optional[str]:
        if self._apella_case_model is not None:
            return self._apella_case_model.scheduled_case_status

        if self._case is not None:
            return self._case.status

        return None

    @property
    def forecasted_start_time(self) -> Optional[datetime]:
        if self._case_forecast is not None:
            now_utc = datetime.now(timezone.utc)
            return max(self._case_forecast.forecast_start_time, now_utc)

        return None

    @property
    def forecasted_end_time(self) -> Optional[datetime]:
        if self._case_forecast is not None:
            now_utc = datetime.now(timezone.utc)
            return max(self._case_forecast.forecast_end_time, now_utc)

        return None

    @property
    def start_time(self) -> datetime:
        if self._apella_case_model is not None:
            if self.type == CaseType.FORECAST:
                return (
                    self.forecasted_start_time
                    if self.forecasted_start_time is not None
                    else self._apella_case_model.start_time
                )
            return self._apella_case_model.start_time

        start_time = None

        if self._actual is not None:
            start_time = self._actual.start_time()
        elif self.forecasted_start_time is not None:
            start_time = self.forecasted_start_time
        else:
            start_time = self.scheduled_start_time

        if start_time is not None:
            return start_time

        raise Exception("Could not determine start_time")

    @property
    def end_time(self) -> Optional[datetime]:
        now_utc = datetime.now(timezone.utc)

        if self._apella_case_model is not None:
            if self.type == CaseType.COMPLETE:
                return self._apella_case_model.end_time
            else:
                return (
                    max(self._case_forecast.forecast_end_time, now_utc)
                    if self._case_forecast is not None
                    else self._apella_case_model.end_time
                )

        if self._actual is None and self._case_forecast is None and self._case is not None:
            return self._case.scheduled_end_time

        if self._actual is not None and self._actual.end_time() is not None:
            return self._actual.end_time()

        if self._case_forecast is not None:
            return self.forecasted_end_time

        if self._case is not None:
            return self._case.scheduled_end_time

        return None

    @property
    def source(self) -> CaseSource:
        if self._apella_case_model is not None:
            return (
                CaseSource.INTERNAL
                if (self._case_forecast is not None and self._case_forecast.id is not None)
                or self._apella_case_model.actual_start_time is not None
                else CaseSource.EXTERNAL
            )

        if self._actual is None and self._case is None:
            raise Exception("Could not determine source")

        return (
            CaseSource.INTERNAL
            if self._actual is not None or self._case_forecast is not None
            else CaseSource.EXTERNAL
        )

    @property
    def type(self) -> CaseType:
        if self._apella_case_model is not None:
            return CaseType.from_str(self._apella_case_model.case_type)

        now_utc = datetime.now(timezone.utc)
        if self.start_time > now_utc or (self._actual is None and self._case is not None):
            return CaseType.FORECAST

        end_time = self.end_time or now_utc
        return (
            CaseType.COMPLETE
            if end_time < now_utc
            and (
                (self._actual is not None and self._actual.end_time() is not None)
                or (
                    self._apella_case_model is not None
                    and self._apella_case_model.actual_end_time is not None
                )
            )
            else CaseType.LIVE
        )

    def default_status_since(self) -> dict[CaseStatusName, Optional[datetime]]:
        case_status_since = (
            self._apella_case_model.scheduled_updated_time
            or self._apella_case_model.scheduled_created_time
            if self._apella_case_model is not None and self._apella_case_model.case_id is not None
            else None
        )

        case_status_since = (
            (self._case.updated_time or self._case.created_time)
            if self._case is not None and self._apella_case_model is None
            else case_status_since
        )

        return {
            APELLA_DEFAULT_CASE_STATUS_MAP[CaseType.COMPLETE]: self.end_time,
            APELLA_DEFAULT_CASE_STATUS_MAP[CaseType.FORECAST]: case_status_since,
            APELLA_DEFAULT_CASE_STATUS_MAP[CaseType.LIVE]: self.start_time,
        }

    def case_status(
        self, events: List[EventModel], observations: List[ObservationModel]
    ) -> ApellaCaseStatus:
        case_type = self.type

        default_since_time = self.default_status_since().get(
            APELLA_DEFAULT_CASE_STATUS_MAP[case_type]
        )

        event_for_status: Optional[EventModel] = None
        default_status_name = APELLA_DEFAULT_CASE_STATUS_MAP[case_type]

        events.sort(key=lambda event: event.start_time)

        for event in events:
            current_case_status = (
                EVENT_TO_CASE_STATUS_MAP.get(LiveEvent(event_for_status.event_type_id), None)
                if event_for_status is not None
                else None
            )
            case_status_to_check = EVENT_TO_CASE_STATUS_MAP.get(
                LiveEvent(event.event_type_id), None
            )

            current_case_status_priority = (
                APELLA_CASE_STATUS_PRIORITY[current_case_status] or -1
                if current_case_status is not None
                else -1
            )
            case_status_to_check_priority = (
                APELLA_CASE_STATUS_PRIORITY[case_status_to_check] or -1
                if case_status_to_check is not None
                else -1
            )

            if (
                case_status_to_check_priority >= current_case_status_priority
                or event_for_status is None
            ):
                event_for_status = event

        most_recent_status = (
            EVENT_TO_CASE_STATUS_MAP.get(
                LiveEvent(event_for_status.event_type_id), default_status_name
            )
            if event_for_status is not None
            else default_status_name
        )

        event_priority = APELLA_CASE_STATUS_PRIORITY[most_recent_status] or -1

        prep_priority = APELLA_CASE_STATUS_PRIORITY[CaseStatusName.PREP]
        wrap_priority = APELLA_CASE_STATUS_PRIORITY[CaseStatusName.WRAP_UP]

        is_in_room = prep_priority <= event_priority and event_priority <= wrap_priority

        case_status = ApellaCaseStatus(
            name=most_recent_status,
            source=CaseSource.INTERNAL,
            since=(
                event_for_status.start_time if event_for_status is not None else default_since_time
            ),
        )

        if len(observations) == 0 or self.case_id is None or is_in_room:
            return case_status

        observation_for_status: Optional[ObservationModel] = None

        observations.sort(key=lambda observation: observation.observation_time)

        for observation in observations:
            current_observation_status = (
                OBSERVATION_TO_CASE_STATUS_MAP.get(
                    ObservationType(observation_for_status.type_id), None
                )
                if observation_for_status is not None
                else None
            )

            observation_status_to_check = OBSERVATION_TO_CASE_STATUS_MAP.get(
                ObservationType(observation.type_id), None
            )

            current_observation_priority = (
                APELLA_CASE_STATUS_PRIORITY[current_observation_status] or -1
                if current_observation_status is not None
                else -1
            )

            observation_to_check_priority = (
                APELLA_CASE_STATUS_PRIORITY[observation_status_to_check] or -1
                if observation_status_to_check is not None
                else -1
            )

            is_pre_or_post_op_priority = (
                observation_to_check_priority >= wrap_priority
                and event_priority >= wrap_priority
                or observation_to_check_priority <= prep_priority
                and event_priority <= prep_priority
            )

            if (
                is_pre_or_post_op_priority
                and observation_to_check_priority > event_priority
                and observation_status_to_check is not None
                and (
                    observation_to_check_priority > current_observation_priority
                    or observation_for_status is None
                )
            ):
                observation_for_status = observation
                case_status = ApellaCaseStatus(
                    name=observation_status_to_check,
                    source=CaseSource.EXTERNAL,
                    since=observation.observation_time,
                )

        return case_status


CaseSourceGraphene = graphene.Enum.from_enum(CaseSource)


@dataclass
class ApellaCaseQuery:
    min_end_time: datetime
    max_start_time: datetime
    query_id: uuid.UUID = field(default_factory=uuid.uuid4)
    org_id: Optional[str] = None
    site_ids: Optional[List[str]] = None
    room_ids: Optional[List[str]] = None
    case_ids: Optional[List[str]] = None
    phase_ids: Optional[List[str]] = None
    staff_ids: Optional[List[str]] = None
    case_types: Optional[List[CaseType]] = None
    case_matching_statuses: Optional[List[CaseMatchingStatus]] = None
    scheduled_case_status: Optional[List[str]] = None
    # If the user isn't part of a specific experiment, we default to stable forecasts
    forecast_variant: str = "stable"
    use_streaming_table: bool = False


@class_traced()
class ApellaCaseStore:
    @property
    def case_forecast_room_join_filters(self) -> list[ColumnElement[bool] | BinaryExpression[bool]]:
        return [
            RoomModel.id == CaseForecastModel.room_id,
            RoomModel.is_forecasting_enabled.is_(True),
        ]

    async def _fetch_cases(
        self,
        session: AsyncSession,
        query: ApellaCaseQuery,
    ) -> List[Tuple[Case, PhaseModel, CaseForecastModel]]:
        async with session.begin():
            actual = aliased(PhaseModel)

            sub_query = (
                select(CaseForecastModel)
                .join(
                    RoomModel,
                    and_(*self.case_forecast_room_join_filters),
                    isouter=False,
                )
                .filter(
                    CaseForecastModel.forecast_status == CaseForecastStatus.VALID,
                )
                .subquery()
            )

            case_forecasts = aliased(CaseForecastModel, sub_query)

            case_query = (
                select(Case, actual, case_forecasts)
                # Start events are eagerly inner joined, which causes cases to not be returned if a corresponding phase doesn't exist
                .options(joinedload(actual.start_event, innerjoin=False))
                .join(
                    CaseMatchingStatusReasonModel,
                    and_(CaseMatchingStatusReasonModel.case_id == Case.case_id),
                    isouter=True,
                )
                .join(
                    actual,
                    and_(
                        actual.case_id == Case.case_id,
                        actual.type_id == CASE,
                        actual.source_type == UNIFIED,
                        actual.status == PhaseStatus.VALID,
                    ),
                    isouter=True,
                )
                .join(
                    case_forecasts,
                    and_(
                        case_forecasts.case_id == Case.case_id,
                        case_forecasts.forecast_variant == query.forecast_variant,
                    ),
                    isouter=True,
                )
                .filter(
                    Case.scheduled_start_time < query.max_start_time,
                    Case.scheduled_end_time >= query.min_end_time,
                )
            )

            if query.org_id is not None:
                case_query = case_query.filter(
                    Case.org_id == query.org_id,
                )
            if query.site_ids is not None:
                case_query = case_query.filter(
                    Case.site_id.in_(query.site_ids),
                )
            if query.room_ids is not None:
                case_query = case_query.filter(
                    Case.room_id.in_(query.room_ids),
                )
            if query.case_ids is not None:
                case_query = case_query.filter(Case.case_id.in_(query.case_ids))
            if query.scheduled_case_status is not None:
                case_query = case_query.filter(Case.status.in_(query.scheduled_case_status))
            else:
                case_query = case_query.filter(Case.status == SCHEDULED)
            if query.staff_ids is not None:
                case_query = case_query.join(
                    CaseStaffModel, CaseStaffModel.case_id == Case.case_id
                ).filter(CaseStaffModel.staff_id.in_(query.staff_ids))

            if query.case_matching_statuses is not None:
                case_query = case_query.filter(
                    or_(
                        CaseMatchingStatusReasonModel.case_matching_status.in_(
                            query.case_matching_statuses
                        ),
                        and_(
                            CaseMatchingStatusReasonModel.case_matching_status.is_(None),
                            literal(CaseMatchingStatus.AUTOMATIC in query.case_matching_statuses),
                        ),
                    )
                )
            else:
                case_query = case_query.filter(
                    or_(
                        CaseMatchingStatusReasonModel.case_matching_status.is_(None),
                        CaseMatchingStatusReasonModel.case_matching_status
                        != CaseMatchingStatus.CANCELED,
                    )
                )
            result = await session.execute(case_query)
            cases_result: List[Tuple[Case, PhaseModel, CaseForecastModel]] = [
                (row[0], row[1], row[2]) for row in result.all()
            ]
            return cases_result

    async def _fetch_case_forecasts(
        self,
        session: AsyncSession,
        query: ApellaCaseQuery,
    ) -> List[tuple[CaseForecastModel, Case, PhaseModel]]:
        async with session.begin():
            actual = aliased(PhaseModel)
            case_forecast_query = (
                select(CaseForecastModel, Case, actual)
                .join(
                    RoomModel,
                    and_(*self.case_forecast_room_join_filters),
                    isouter=False,
                )
                .join(
                    Case,
                    CaseForecastModel.case_id == Case.case_id,
                )
                .join(
                    CaseMatchingStatusReasonModel,
                    and_(CaseMatchingStatusReasonModel.case_id == Case.case_id),
                    isouter=True,
                )
                .options(joinedload(actual.start_event, innerjoin=False))
                .join(
                    actual,
                    and_(
                        actual.case_id == Case.case_id,
                        actual.type_id == CASE,
                        actual.source_type == UNIFIED,
                        actual.status == PhaseStatus.VALID,
                    ),
                    isouter=True,
                )
                .filter(
                    CaseForecastModel.forecast_start_time < query.max_start_time,
                    CaseForecastModel.forecast_end_time >= query.min_end_time,
                    CaseForecastModel.forecast_variant == query.forecast_variant,
                    CaseForecastModel.forecast_status == CaseForecastStatus.VALID,
                    Case.scheduled_start_time < (query.max_start_time + timedelta(days=1)),
                    Case.scheduled_end_time >= (query.min_end_time - timedelta(days=1)),
                )
            )

            if query.org_id is not None:
                case_forecast_query = case_forecast_query.filter(
                    CaseForecastModel.org_id == query.org_id, Case.org_id == query.org_id
                )
            if query.site_ids is not None:
                case_forecast_query = case_forecast_query.filter(
                    CaseForecastModel.site_id.in_(query.site_ids),
                    Case.site_id.in_(query.site_ids),
                )
            if query.room_ids is not None:
                case_forecast_query = case_forecast_query.filter(
                    CaseForecastModel.room_id.in_(query.room_ids),
                    Case.room_id.in_(query.room_ids),
                )
            if query.case_ids is not None:
                case_forecast_query = case_forecast_query.filter(
                    CaseForecastModel.case_id.in_(query.case_ids),
                    Case.case_id.in_(query.case_ids),
                )
            if query.scheduled_case_status is not None:
                case_forecast_query = case_forecast_query.filter(
                    Case.status.in_(query.scheduled_case_status)
                )
            else:
                case_forecast_query = case_forecast_query.filter(Case.status == SCHEDULED)

            if query.staff_ids is not None:
                case_forecast_query = case_forecast_query.join(
                    CaseStaffModel, CaseStaffModel.case_id == Case.case_id
                ).filter(CaseStaffModel.staff_id.in_(query.staff_ids))

            if query.case_matching_statuses is not None:
                case_forecast_query = case_forecast_query.filter(
                    or_(
                        CaseMatchingStatusReasonModel.case_matching_status.in_(
                            query.case_matching_statuses
                        ),
                        and_(
                            CaseMatchingStatusReasonModel.case_matching_status.is_(None),
                            literal(CaseMatchingStatus.AUTOMATIC in query.case_matching_statuses),
                        ),
                    )
                )
            else:
                case_forecast_query = case_forecast_query.filter(
                    or_(
                        CaseMatchingStatusReasonModel.case_matching_status.is_(None),
                        CaseMatchingStatusReasonModel.case_matching_status
                        != CaseMatchingStatus.CANCELED,
                    )
                )

            case_forecasts_result = await session.execute(case_forecast_query)
            case_forecasts: List[tuple[CaseForecastModel, Case, PhaseModel]] = [
                (row[0], row[1], row[2]) for row in case_forecasts_result.all()
            ]
            return case_forecasts

    # A phase can start/end or it's forecast can fall into the time range we care about, while
    # at the same time it's scheduled start/end can fall outside of the time range, so we join
    # to cases here again even though we've already fetched cases based on their scheduled start time.
    #
    # We can't do this all in one query because right outer joins don't include rows where the joined field is null
    #
    # Note, we also limit the max length for a live phase to filter out any dangling really old phases
    async def _fetch_phases(
        self,
        session: AsyncSession,
        query: ApellaCaseQuery,
        current_time_utc: datetime,
    ) -> List[Tuple[PhaseModel, Case, CaseForecastModel]]:
        async with session.begin():
            start_event_alias = aliased(EventModel)
            end_event_alias = aliased(EventModel)

            sub_query = (
                select(CaseForecastModel)
                .join(
                    RoomModel,
                    and_(*self.case_forecast_room_join_filters),
                    isouter=False,
                )
                .filter(
                    CaseForecastModel.forecast_status == CaseForecastStatus.VALID,
                )
                .subquery()
            )

            case_forecasts = aliased(CaseForecastModel, sub_query)

            phase_query = (
                select(PhaseModel, Case, case_forecasts)
                .join(
                    Case,
                    and_(PhaseModel.case_id == Case.case_id),
                    isouter=True,
                )
                .join(
                    case_forecasts,
                    and_(
                        case_forecasts.case_id == Case.case_id,
                    ),
                    isouter=True,
                )
                .join(
                    CaseMatchingStatusReasonModel,
                    and_(CaseMatchingStatusReasonModel.case_id == Case.case_id),
                    isouter=True,
                )
                # The only way to filter relationship entities is through .has() in sqlalchemy, which
                # results in a non-performant EXISTS subquery.
                # To create more performant query plans, explicitly join the start and end events
                .join(start_event_alias, PhaseModel.start_event)
                .join(end_event_alias, PhaseModel.end_event, isouter=True)
                .options(contains_eager(PhaseModel.start_event.of_type(start_event_alias)))
                .options(contains_eager(PhaseModel.end_event.of_type(end_event_alias)))
                .filter(
                    PhaseModel.status == PhaseStatus.VALID,
                    start_event_alias.start_time < query.max_start_time,
                    # This filter isn't specifically needed logic-wise but helps the SQL performance
                    # by forcing the query plan to filter by start_event timerange early on.
                    # All phases should have a start time within MAX_PHASE_LENGTH before the query's min_end_time
                    start_event_alias.start_time >= query.min_end_time - MAX_PHASE_LENGTH,
                    start_event_alias.event_type_id == LiveEvent.PATIENT_WHEELS_IN.value,
                    PhaseModel.type_id == CASE,
                    PhaseModel.source_type.in_(CASE_SOURCE_TYPES),
                    or_(
                        and_(
                            # See https://stackoverflow.com/questions/30838518/sqlalchemy-filter-by-relationship for why
                            # flake8 is disabled here. The equivalent compliant code is something like ~PhaseModel.end_event.has(EventModel.id.isnot(None))
                            # which is both a hard SQL query to debug and hard code to understand...
                            PhaseModel.end_event_id == None,  # noqa: E711
                            (current_time_utc - start_event_alias.start_time) <= MAX_PHASE_LENGTH,
                            literal(current_time_utc > query.min_end_time),
                        ),
                        end_event_alias.start_time >= query.min_end_time,
                    ),
                )
            )

            if query.org_id is not None:
                phase_query = phase_query.filter(
                    PhaseModel.org_id == query.org_id, start_event_alias.org_id == query.org_id
                )
            if query.site_ids is not None:
                phase_query = phase_query.filter(
                    PhaseModel.site_id.in_(query.site_ids),
                    start_event_alias.site_id.in_(query.site_ids),
                )
            if query.room_ids is not None:
                phase_query = phase_query.filter(PhaseModel.room_id.in_(query.room_ids))
            if query.case_ids is not None:
                phase_query = phase_query.filter(PhaseModel.case_id.in_(query.case_ids))
            if query.scheduled_case_status is not None:
                phase_query = phase_query.filter(Case.status.in_(query.scheduled_case_status))
            else:
                phase_query = phase_query.filter(
                    or_(Case.status.is_(None), Case.status == SCHEDULED)
                )
            if query.staff_ids is not None:
                phase_query = phase_query.join(
                    CaseStaffModel, CaseStaffModel.case_id == Case.case_id
                ).filter(CaseStaffModel.staff_id.in_(query.staff_ids))

            if query.case_matching_statuses is not None:
                phase_query = phase_query.filter(
                    or_(
                        CaseMatchingStatusReasonModel.case_matching_status.in_(
                            query.case_matching_statuses
                        ),
                        and_(
                            CaseMatchingStatusReasonModel.case_matching_status.is_(None),
                            literal(CaseMatchingStatus.AUTOMATIC in query.case_matching_statuses),
                        ),
                    )
                )
            else:
                phase_query = phase_query.filter(
                    or_(
                        CaseMatchingStatusReasonModel.case_matching_status.is_(None),
                        CaseMatchingStatusReasonModel.case_matching_status
                        != CaseMatchingStatus.CANCELED,
                    )
                )

            phases_result = await session.execute(phase_query)
            case_phases_result: List[Tuple[PhaseModel, Case, CaseForecastModel]] = [
                (row[0], row[1], row[2]) for row in phases_result.all()
            ]
            return case_phases_result

    async def query_apella_cases(
        self,
        query: ApellaCaseQuery,
    ) -> List[ApellaCase]:
        def _filter_apella_cases(case: ApellaCase) -> bool:
            # If we filter by phase id in the query then we'll exclude one of the other and
            # the resulting entity will be missing data/wrong. Therefore we can't query by phase ids
            # in SQL and must do it here because we want to get the forecast and unified phases.
            if query.phase_ids is not None:
                is_phase = case._actual is not None

                is_in_phase_ids = (
                    case._actual is not None and str(case._actual.id) in query.phase_ids
                )

                if not is_phase or (is_phase and not is_in_phase_ids):
                    return False

            if query.case_types is not None and case.type not in query.case_types:
                return False

            return True

        current_time_utc = datetime.now(timezone.utc)
        if query.use_streaming_table:
            return await self._fetch_apella_cases(query)
        else:
            async with (
                new_async_session() as session1,
                new_async_session() as session2,
                new_async_session() as session3,
            ):
                cases, case_phases, case_forecasts = await asyncio.gather(
                    self._fetch_cases(
                        session1,
                        query,
                    ),
                    self._fetch_phases(session2, query, current_time_utc),
                    self._fetch_case_forecasts(session3, query),
                )

            cases_dict: Dict[str, ApellaCase] = {
                case[0].case_id: ApellaCase(
                    _case=case[0],
                    _actual=case[1],
                    _case_forecast=case[2],
                    _apella_case_model=None,
                )
                for case in cases
            }

            for case_forecast_tuple in case_forecasts:
                case_forecast_id = case_forecast_tuple[0].case_id
                if case_forecast_id not in cases_dict:
                    cases_dict[case_forecast_id] = ApellaCase(
                        _case=case_forecast_tuple[1],
                        _actual=case_forecast_tuple[2],
                        _case_forecast=case_forecast_tuple[0],
                        _apella_case_model=None,
                    )

            # Because we can get duplicate results between our case and phase queries
            # we resolve those duplications here
            for case_phase in case_phases:
                phase_model: PhaseModel = case_phase[0]
                case: Optional[Case] = case_phase[1]
                actual_phase = phase_model if phase_model.source_type == UNIFIED else None
                case_forecast = case_phase[2]

                # We don't actually use this key for anything, this just helps us use the same dict
                # and not have to combine phases without a case and phases with a case
                key = phase_model.case_id or f"phase:{phase_model.id}"

                apella_case = cases_dict.get(
                    key,
                    ApellaCase(
                        _actual=actual_phase,
                        _case=case,
                        _case_forecast=case_forecast,
                        _apella_case_model=None,
                    ),
                )

                if actual_phase is not None and apella_case._actual is None:
                    apella_case._actual = actual_phase

                if case is not None and apella_case._case is None:
                    apella_case._case = case

                cases_dict[key] = apella_case

            cases_result = list(cases_dict.values())

            all_cases = filter(_filter_apella_cases, cases_result)

            apella_cases = sorted(
                all_cases,
                key=lambda case: case.start_time,
            )

            return apella_cases

    async def _fetch_apella_cases(self, query: ApellaCaseQuery) -> list[ApellaCase]:
        current_time_utc = datetime.now(timezone.utc)

        sub_query = (
            select(CaseForecastModel)
            .join(
                RoomModel,
                and_(*self.case_forecast_room_join_filters),
                isouter=False,
            )
            .filter(
                CaseForecastModel.forecast_status == CaseForecastStatus.VALID,
            )
            .subquery()
        )

        case_forecasts = aliased(CaseForecastModel, sub_query)

        async with new_async_session() as session:
            forecast_variant = (
                query.forecast_variant if query.forecast_variant is not None else "stable"
            )

            statement = (
                select(ScheduledAndActualCase, case_forecasts)
                .join(
                    CaseMatchingStatusReasonModel,
                    and_(CaseMatchingStatusReasonModel.case_id == ScheduledAndActualCase.case_id),
                    isouter=True,
                )
                .join(
                    case_forecasts,
                    and_(
                        case_forecasts.case_id == ScheduledAndActualCase.case_id,
                        case_forecasts.forecast_variant == forecast_variant,
                    ),
                    isouter=True,
                )
                .filter(
                    and_(
                        or_(
                            and_(
                                ScheduledAndActualCase.min_start_time < query.max_start_time,
                                or_(
                                    ScheduledAndActualCase.max_end_time >= query.min_end_time,
                                    and_(
                                        ScheduledAndActualCase.max_end_time.is_(None),
                                        (current_time_utc - ScheduledAndActualCase.start_time)
                                        <= MAX_PHASE_LENGTH,
                                    ),
                                ),
                            ),
                            and_(
                                case_forecasts.forecast_start_time < query.max_start_time,
                                case_forecasts.forecast_end_time >= query.min_end_time,
                            ),
                        ),
                    )
                )
            )

            if query.org_id is not None:
                statement = statement.filter(ScheduledAndActualCase.org_id == query.org_id)

            if query.site_ids is not None:
                statement = statement.filter(ScheduledAndActualCase.site_id.in_(query.site_ids))

            if query.room_ids is not None:
                statement = statement.filter(ScheduledAndActualCase.room_id.in_(query.room_ids))

            if query.case_ids is not None:
                statement = statement.filter(ScheduledAndActualCase.case_id.in_(query.case_ids))

            if query.phase_ids is not None:
                statement = statement.filter(ScheduledAndActualCase.phase_id.in_(query.phase_ids))

            if query.case_types is not None:
                case_types = [el.name for el in query.case_types]
                statement = statement.filter(ScheduledAndActualCase.case_type.in_(case_types))

            if query.scheduled_case_status is not None:
                statement = statement.filter(
                    ScheduledAndActualCase.scheduled_case_status.in_(query.scheduled_case_status)
                )
            else:
                statement = statement.filter(
                    or_(
                        ScheduledAndActualCase.scheduled_case_status.is_(None),
                        ScheduledAndActualCase.scheduled_case_status == SCHEDULED,
                    )
                )

            if query.staff_ids is not None:
                statement = statement.join(
                    CaseStaffModel, CaseStaffModel.case_id == ScheduledAndActualCase.case_id
                ).filter(CaseStaffModel.staff_id.in_(query.staff_ids))

            if query.case_matching_statuses is not None:
                statement = statement.filter(
                    or_(
                        CaseMatchingStatusReasonModel.case_matching_status.in_(
                            query.case_matching_statuses
                        ),
                        and_(
                            CaseMatchingStatusReasonModel.case_matching_status.is_(None),
                            literal(CaseMatchingStatus.AUTOMATIC in query.case_matching_statuses),
                        ),
                    )
                )
            else:
                statement = statement.filter(
                    or_(
                        CaseMatchingStatusReasonModel.case_matching_status.is_(None),
                        CaseMatchingStatusReasonModel.case_matching_status
                        != CaseMatchingStatus.CANCELED,
                    )
                )

            statement = statement.order_by(ScheduledAndActualCase.start_time)

            result = (await session.execute(statement)).all()

            apella_cases = [
                ApellaCase(
                    _apella_case_model=row[0],
                    _case_forecast=row[1],
                    _actual=None,
                    _case=None,
                )
                for row in result
            ]

            return apella_cases
