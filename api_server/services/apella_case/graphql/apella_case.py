# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs
from datetime import timezone, datetime
from typing import Type, Optional

import graphene
from graphql import GraphQLError

from api_server.services.case.case_matching_store import CaseMatchingStatusGraphene
import api_server.services.case.graphql.scheduled_case as case_schema
from api_server.services.case_forecasts.case_forecast_store import CaseForecastModel
import api_server.services.room.graphql.room as room_schema
from api_server.graphql.context import GrapheneInfo
from api_server.graphql.pagination.fields import (
    PaginationConnection,
    pagination_connection_factory,
)
from api_server.services.apella_case.apella_case_service import (
    ApellaCaseStatus as ApellaStatusModel,
)
from api_server.services.apella_case.apella_case_service import (
    CaseStatusNameGraphene,
    CaseTypeGraphene,
)
from api_server.services.apella_case.apella_case_store import (
    POST_OP_OBSERVATIONS,
    PRE_OP_OBSERVATIONS,
    EVENT_TO_CASE_STATUS_MAP,
)
from api_server.services.apella_case.apella_case_store import (
    ApellaCase as ApellaCaseModel,
)
from api_server.services.apella_case.apella_case_store import (
    CaseSourceGraphene,
    CaseType,
)
from api_server.services.events.graphql.event_loaders import FrozenRoomEventsQueryDto
from api_server.services.events.source_type import HUMAN_GROUND_TRUTH, PREDICTION
from api_server.services.observations.observation_store import ObservationModel
from api_server.services.room.room_store import RoomModel
from api_server.services.phases.graphql.phase import Phase


def import_case_forecast():
    from api_server.services.case_forecasts.graphql.case_forecast import CaseForecast

    return CaseForecast


class ApellaCaseStatus(graphene.ObjectType):
    name = CaseStatusNameGraphene(required=True)
    source = CaseSourceGraphene(required=True)
    since = graphene.DateTime(required=False)


class ApellaCase(graphene.ObjectType):
    id = graphene.ID(required=True)

    case = graphene.Field(lambda: case_schema.ScheduledCase, required=False)
    actual = graphene.Field(lambda: Phase, required=False)
    forecast = graphene.Field(
        lambda: Phase,
        required=False,
        deprecation_reason="This field is deprecated and will be removed in the future. This has been replace by case_forecast",
    )
    case_forecast = graphene.Field(import_case_forecast, required=False)
    type = CaseTypeGraphene(required=True)

    status = graphene.Field(
        lambda: ApellaCaseStatus, use_observations=graphene.Boolean(required=False), required=True
    )

    source = CaseSourceGraphene(required=True)
    start_time = graphene.DateTime(required=True)
    end_time = graphene.DateTime(required=False)

    room = graphene.Field(lambda: room_schema.Room, required=True)
    room_id = graphene.ID(required=True)
    site_id = graphene.ID(required=True)

    @staticmethod
    async def resolve_case(case: ApellaCaseModel, info: GrapheneInfo):
        case_or_case_id = case.case_or_case_id
        if case_or_case_id is None:
            return None

        if isinstance(case_or_case_id, str):
            return await info.context.case_loader.load(case_or_case_id)

        return case_or_case_id

    @staticmethod
    async def resolve_actual(case: ApellaCaseModel, info: GrapheneInfo):
        phase_or_phase_id = case.actual_phase_or_phase_id

        if phase_or_phase_id is None:
            return None

        if isinstance(phase_or_phase_id, str):
            return await info.context.phase_loader.load(phase_or_phase_id)

        return phase_or_phase_id

    # This field is deprecated, but we need to remove it from the API schema in a future release
    @staticmethod
    async def resolve_forecast():
        return None

    @staticmethod
    async def resolve_case_forecast(
        case: ApellaCaseModel, info: GrapheneInfo
    ) -> Optional[CaseForecastModel]:
        return case.case_forecast

    @staticmethod
    async def resolve_room(case: ApellaCaseModel, info: GrapheneInfo) -> RoomModel:
        room: Optional[RoomModel] = await info.context.room_loader.load(case.room_id)
        if room is None:
            raise GraphQLError(
                message=f"Unable to find room for case id: {case.id} with room id: {case.room_id}"
            )
        return room

    @staticmethod
    async def resolve_status(
        case: ApellaCaseModel, info: GrapheneInfo, use_observations: bool = False
    ) -> ApellaStatusModel:
        observation_types = frozenset(
            [
                observation_type.value
                for observation_type in (
                    POST_OP_OBSERVATIONS if case.type == CaseType.COMPLETE else PRE_OP_OBSERVATIONS
                )
            ]
        )
        observations: list[ObservationModel] = []

        if case.case_id is not None and use_observations and case.type != CaseType.LIVE:
            observations = await info.context.observation_loader.load(
                (case.case_id, observation_types, None)
            )

        # We currently only need to query for events in the scenario that the case is live
        # as the only events we map to a state are in-room events that should take place between
        # patient wheels in and patient wheels out (i.e. a case)
        events_during_case = []
        if case.type == CaseType.LIVE:
            event_names = tuple([key.value for key in EVENT_TO_CASE_STATUS_MAP.keys()])

            # To be able to optimize the number of queries we make, we can query using a loader.
            # The loader joins queries by room id if every other query param is the same.
            start_of_day = case.start_time.replace(hour=0, minute=0, second=0, microsecond=0)
            end_of_current_day = datetime.now(tz=timezone.utc).replace(
                hour=23, minute=59, second=59, microsecond=999
            )
            events = await info.context.room_events_loader.load(
                FrozenRoomEventsQueryDto(
                    room_id=case.room_id,
                    min_time=start_of_day,
                    max_time=end_of_current_day,
                    event_names=event_names,
                    include_deleted=False,
                    source_types=(PREDICTION, HUMAN_GROUND_TRUTH),
                )
            )

            # filter for events that happened during the case
            events_during_case = [event for event in events if case.start_time <= event.start_time]

        return info.context.apella_case_service.get_apella_case_status(
            case=case, observations=observations, events=events_during_case
        )


ApellaCaseConnection: Type[PaginationConnection] = pagination_connection_factory(ApellaCase)


class ApellaCaseBaseQueryInput(graphene.InputObjectType):
    min_end_time = graphene.DateTime(required=True)
    max_start_time = graphene.DateTime(required=True)
    case_ids = graphene.List(graphene.NonNull(graphene.ID))
    phase_ids = graphene.List(graphene.NonNull(graphene.ID))
    staff_ids = graphene.List(graphene.NonNull(graphene.ID))
    case_types = graphene.List(graphene.NonNull(CaseTypeGraphene))
    case_matching_statuses = graphene.List(graphene.NonNull(CaseMatchingStatusGraphene))
    scheduled_case_status = graphene.List(graphene.NonNull(graphene.String))


class ApellaCaseQueryInput(ApellaCaseBaseQueryInput):
    organization_id = graphene.ID(required=False)
    site_ids = graphene.List(graphene.NonNull(graphene.ID))
    room_ids = graphene.List(graphene.NonNull(graphene.ID))
