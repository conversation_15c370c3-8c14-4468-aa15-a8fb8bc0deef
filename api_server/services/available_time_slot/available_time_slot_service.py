import asyncio
from dataclasses import dataclass
from datetime import date, datetime, time, timedelta
from typing import List, Optional
from zoneinfo import ZoneInfo
from apella_cloud_api.dtos import AvailableSlot, RoomClosureQueryDto, SiteClosureQueryDto
from api_server.logging.audit import log_calls_to_audit
from api_server.services.apella_case.apella_case_service import ApellaCaseService
from api_server.services.apella_case.apella_case_store import ApellaCaseQuery
from api_server.services.block.block_service import BlockService
from api_server.services.case.case_staff_service import CaseStaffService
from api_server.services.case.case_staff_store import CaseStaffQuery
from api_server.services.available_time_slot.case_slot_model_helpers import get_cases
from api_server.services.available_time_slot.find_slots import suggest_available_time_slots
from api_server.services.closures.closure_service import ClosureService
from api_server.services.prime_time.prime_time_service import PrimeTimeService
from api_server.services.room.room_service import RoomService
from api_server.services.site.site_service import SiteService
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import READ_ANY_CASE


@dataclass
class AvailableTimeSlotQueryDto:
    min_available_duration: timedelta
    site_ids: List[str]
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    surgeon_id: Optional[str] = None
    now: Optional[datetime] = None


@dataclass
class AvailableTimeSlotService:
    auth: Auth
    apella_case_service: ApellaCaseService
    block_service: BlockService
    case_staff_service: CaseStaffService
    closure_service: ClosureService
    room_service: RoomService
    prime_time_service: PrimeTimeService
    site_service: SiteService

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE)
    async def get_available_time_slots(
        self, query: AvailableTimeSlotQueryDto
    ) -> list[AvailableSlot]:
        start_date = query.start_date or datetime.now().date()
        end_date = query.end_date or (start_date + timedelta(days=7))

        time_slots_by_site = await asyncio.gather(
            *[
                self._get_available_time_slots_for_site(
                    site_id=site_id,
                    min_available_duration=query.min_available_duration,
                    start_date=start_date,
                    end_date=end_date,
                    surgeon_id=query.surgeon_id,
                    now=query.now,
                )
                for site_id in query.site_ids
            ]
        )

        return [slot for slots in time_slots_by_site for slot in slots]

    async def _get_available_time_slots_for_site(
        self,
        site_id: str,
        min_available_duration: timedelta,
        start_date: date,
        end_date: date,
        surgeon_id: Optional[str] = None,
        now: Optional[datetime] = None,
    ) -> list[AvailableSlot]:
        site = await self.prime_time_service.get_site_with_constraints(site_id=site_id)

        site_timezone = ZoneInfo(site.timezone)
        min_end_time = datetime.combine(start_date, time(0, 0, tzinfo=site_timezone))
        max_start_time = datetime.combine(end_date, time(0, 0, tzinfo=site_timezone)) + timedelta(
            days=1
        )

        (
            block_times,
            room_closures,
            apella_cases,
            site_closures,
        ) = await asyncio.gather(
            self.block_service.query_block_times_available_intervals(
                site_id=site_id,
                min_end_time=min_end_time,
                max_start_time=max_start_time,
            ),
            self.closure_service.query_room_closures(
                RoomClosureQueryDto(
                    site_id=site_id,
                    min_end_time=min_end_time,
                    max_start_time=max_start_time,
                )
            ),
            self.apella_case_service.query_cases(
                query=ApellaCaseQuery(
                    site_ids=[site_id],
                    min_end_time=min_end_time,
                    max_start_time=max_start_time,
                    scheduled_case_status=["scheduled"],
                )
            ),
            self.closure_service.query_site_closures(
                SiteClosureQueryDto(
                    site_id=site_id,
                    start_date=start_date,
                    end_date=end_date,
                )
            ),
        )

        case_staff_query = CaseStaffQuery(
            case_ids=[case.case_id for case in apella_cases if case.case_id],
            only_primary_surgeons=True,
        )
        staff = await self.case_staff_service.query_case_staff_relationships(case_staff_query)
        cases = get_cases(list(apella_cases), staff, [site_id], min_end_time, max_start_time)

        open_times = suggest_available_time_slots(
            min_available_duration=min_available_duration,
            cases=cases,
            block_times=block_times,
            site=site,
            start_date=start_date or datetime.now().date(),
            end_date=end_date or datetime.now().date() + timedelta(days=7),
            primary_surgeon_id=surgeon_id,
            room_closures=room_closures,
            site_closures=site_closures,
            now=now,
        )

        return [
            AvailableSlot(
                room_id=open_time.room_id,
                start_time=open_time.start_time,
                end_time=open_time.end_time,
                max_available_duration=open_time.max_available_duration,
                block_time_ids=open_time.block_time_ids,
            )
            for open_time in open_times
        ]
