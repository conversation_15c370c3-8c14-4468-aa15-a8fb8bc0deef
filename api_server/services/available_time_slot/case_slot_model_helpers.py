import logging
from collections import defaultdict

from datetime import datetime

from api_server.services.apella_case.apella_case_store import ApellaCase
from api_server.services.case.case_staff_store import CaseStaffModel

from pydantic import BaseModel

logger = logging.getLogger(__name__)


class Case(BaseModel):
    """This is not a complete represetnation of a Case and should not be used
    outside case_slotting. The attributes in `Case` is ONLY what is needed to evaluate
    where the next case could/should be added and are the parameters we are retrieving
    from the cloud_api_client"""

    site_id: str
    room_id: str
    case_id: str
    scheduled_start_time: datetime
    scheduled_end_time: datetime
    forecasted_start_time: datetime
    forecasted_end_time: datetime
    primary_staff_ids: list[str]


def get_cases(
    case_results: list[ApellaCase],
    case_staff_results: list[CaseStaffModel],
    site_ids: list[str],
    min_end_time: datetime,
    max_start_time: datetime,
) -> list[Case]:
    case_dict = defaultdict(list)
    for case_staff in case_staff_results:
        case_dict[case_staff.case_id].append(str(case_staff.staff_id))
    cases = [
        Case(
            site_id=case.site_id,
            room_id=case.room_id,
            case_id=case.case_id,
            scheduled_start_time=case.scheduled_start_time,
            scheduled_end_time=case.scheduled_end_time,
            forecasted_start_time=case.forecasted_start_time,
            forecasted_end_time=case.forecasted_end_time,
            primary_staff_ids=case_dict[case.case_id],
        )
        for case in case_results
        if case.case_id
        and case.scheduled_start_time
        and case.scheduled_end_time
        and case.forecasted_start_time
        and case.forecasted_end_time
    ]

    cases_to_drop = [case for case in cases if case.site_id not in site_ids]
    sites_to_drop = set([case.site_id for case in cases_to_drop])

    if len(cases_to_drop) > 0:
        logger.warning(
            f"We query for cases in {site_ids} but got {len(cases_to_drop)} cases in sites: {sites_to_drop}"
        )
        logger.warning(f"First 3 cases to drop are: {cases_to_drop[:3]}")

    # currently, the GQLApellaCaseQueryInput performs an OR between forecasted and scheduled
    # cases. Say a case was scheduled at time t1, forecasted at time close to t1 and then
    # rescheduled at time t2 far far away. If we query around t1 we will still get the case
    # because forecasting matches (even though the scheduled one doesn't).
    # We need cases that are scheduled in the desired range, therefor filter out those
    # ones that match the query because for other reasons
    cases = [
        c
        for c in cases
        if min_end_time <= c.scheduled_start_time
        and c.scheduled_end_time < max_start_time
        and c.site_id in site_ids
    ]
    return cases
