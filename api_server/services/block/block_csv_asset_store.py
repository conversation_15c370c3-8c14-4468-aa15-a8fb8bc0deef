# mypy: ignore-errors
import csv
import io

import config
from datetime import datetime, timedelta
from typing import Optional, Text
from google.cloud.storage import Blob

from api_server.tracing.trace_decorator import traced
from api_server.services.block.block_models import (
    BlockReleaseDataInput,
    get_output_csv_content,
)
from api_server.services.media.media_asset_store import MediaAssetStore


class BlockCsvAssetStore(MediaAssetStore):
    @traced()
    def download_csv_file_as_text(self, csv_gs_url: str) -> str:
        blob = Blob.from_string(csv_gs_url, self.storage_client)
        return blob.download_as_text()

    @traced()
    def get_blob(self, bucket_name: str, file_name: str, env: Optional[str] = "localhost") -> Blob:
        bucket_name = (
            f"{env}-{bucket_name}" if env is not None and env != "localhost" else bucket_name
        )
        bucket = self.storage_client.bucket(bucket_name)

        return bucket.blob(file_name)

    @traced()
    async def upload_processed_releases(
        self, releases: list[BlockReleaseDataInput], source: str = "sftp"
    ) -> Optional[str]:
        env = config.environment_name()

        if env is None or env == "localhost":
            return None

        now = datetime.now()
        file_name = f"{now.replace(microsecond=0).isoformat()}-{source}.csv"
        csv_content = get_output_csv_content(releases)

        blob = self.get_blob("processed-block-release-data", file_name, env)

        blob.upload_from_string(csv_content, content_type="text/csv")

        return self.get_signed_url_for_blob(
            method="GET",
            blob=blob,
            expiration=now + timedelta(days=90),
        )

    @staticmethod
    def convert_to_file_like_obj(file_content: Text, skip_n_rows: Optional[int] = 0) -> io.StringIO:
        lines = io.StringIO(file_content).readlines()
        lines = lines[skip_n_rows:]

        return io.StringIO("".join(lines))

    @staticmethod
    def get_csv_content(
        file_content: Text, skip_n_rows: Optional[int] = 0, delimiter: Optional[str] = ","
    ) -> list[dict[str, str]]:
        csv_content = csv.DictReader(
            BlockCsvAssetStore.convert_to_file_like_obj(
                file_content=file_content, skip_n_rows=skip_n_rows
            ),
            delimiter=delimiter,
        )

        return list(
            map(
                lambda r: {k: v.strip() if isinstance(v, str) else "" for k, v in r.items()},
                csv_content,
            )
        )
