import csv
import io
import uuid
from dataclasses import asdict, dataclass, fields
from datetime import datetime, date
from enum import Enum
from typing import Any, Final, Literal, Optional, Union
from typing_extensions import ParamSpec

import sqlalchemy.orm.exc

from asyncpg import Range
from sqlalchemy import (
    CheckConstraint,
    Column,
    DateTime,
    ForeignKey,
    Index,
    Integer,
    PrimaryKeyConstraint,
    String,
    Table,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import TSTZRANGE, UUID, ExcludeConstraint
from sqlalchemy.ext.associationproxy import association_proxy
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func

from api_server.services.site.site_store import Site
from databases.sql import Base
from databases.sql.mixins import ArchivedTimeMixin, OrgIdMixin, TimestampMixin

from api_server.services.staff.staff_store import StaffModel

P = ParamSpec("P")


class BlockTypes(Enum):
    UNBLOCKED_BLOCK_TYPE = "Unblocked"
    UNAVAILABLE_BLOCK_TYPE = "Unavailable"
    GROUP_BLOCK_TYPE = "Group"
    SURGEON_BLOCK_TYPE = "Surgeon"
    SERVICE_BLOCK_TYPE = "Service"
    ON_HOLD_BLOCK_TYPE = "On Hold"
    UNKNOWN = "Unknown"


SPECIAL_BLOCK_TYPES: Final[set[BlockTypes]] = {
    BlockTypes.UNBLOCKED_BLOCK_TYPE,
    BlockTypes.UNAVAILABLE_BLOCK_TYPE,
}

SPECIAL_BLOCK_TYPE_VALUES: Final[set[str]] = {type.value for type in SPECIAL_BLOCK_TYPES}


class ReleaseTypes(Enum):
    AUTO = "Automatic Block Release"
    MANUAL = "Manual Block Release"
    UNKNOWN = "Unknown"


class BlockReleaseProcessedFileModel(Base, TimestampMixin, OrgIdMixin):
    __tablename__ = "block_release_processed_files"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=sqlalchemy.text("gen_random_uuid()"),
    )
    file_name: Mapped[str] = mapped_column(String, nullable=False, index=True)
    bucket_name: Mapped[str] = mapped_column(String, nullable=False, index=True)
    generation: Mapped[str] = mapped_column(String, nullable=False)
    size: Mapped[str] = mapped_column(String, nullable=False)
    md5_hash: Mapped[str] = mapped_column(String, nullable=False, index=True)
    media_link: Mapped[str] = mapped_column(String, nullable=False, index=True)

    __table_args__ = (
        Index(
            "ix_unique_block_release_processed_files",
            file_name,
            bucket_name,
            md5_hash,
            unique=True,
        ),
    )

    def __repr__(self) -> str:
        return f"<BlockReleaseProcessedFile(id='{self.id}', file_name='{self.file_name}', bucket_name='{self.bucket_name}', generation='{self.generation}', md5_hash='{self.md5_hash}')>"


class BlockScheduleProcessedFileModel(Base, TimestampMixin, OrgIdMixin):
    __tablename__ = "block_schedule_processed_files"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=sqlalchemy.text("gen_random_uuid()"),
    )
    file_name: Mapped[str] = mapped_column(String, nullable=False, index=True)
    bucket_name: Mapped[str] = mapped_column(String, nullable=False, index=True)
    generation: Mapped[str] = mapped_column(String, nullable=False)
    size: Mapped[str] = mapped_column(String, nullable=False)
    md5_hash: Mapped[str] = mapped_column(String, nullable=False, index=True)
    media_link: Mapped[str] = mapped_column(String, nullable=False, index=True)

    __table_args__ = (
        Index(
            "ix_unique_block_schedule_processed_files",
            file_name,
            bucket_name,
            md5_hash,
            unique=True,
        ),
    )

    def __repr__(self) -> str:
        return f"<BlockScheduleProcessedFile(id='{self.id}', file_name='{self.file_name}', bucket_name='{self.bucket_name}', generation='{self.generation}', md5_hash='{self.md5_hash}')>"


class BlockProcessingStatus(Enum):
    PENDING = "Pending"
    PROCESSED = "Processed"
    REJECTED = "Rejected"


# a model for each row in the csv file representing a single block release and status information
# about the release
class BlockReleaseFileRowModel(Base, TimestampMixin, OrgIdMixin):
    __tablename__ = "block_release_file_rows"

    block_release_file_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("block_release_processed_files.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    row_number: Mapped[int] = mapped_column(Integer, nullable=False)
    external_id: Mapped[str] = mapped_column(String, nullable=True, index=True)
    org_id: Mapped[str] = mapped_column(String, nullable=False, index=True)
    site_id: Mapped[str] = mapped_column(String, nullable=True, index=True)
    room_id: Mapped[str] = mapped_column(String, nullable=True, index=True)
    room_name: Mapped[str] = mapped_column(String, nullable=True, index=True)
    timezone: Mapped[str] = mapped_column(String, nullable=True)
    start_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
    end_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
    release_length: Mapped[int] = mapped_column(Integer, nullable=True)
    released_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
    release_reason: Mapped[str] = mapped_column(String, nullable=True)
    released_by: Mapped[str] = mapped_column(String, nullable=True)
    block_name: Mapped[str] = mapped_column(String, nullable=True)
    block_date: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
    to_block_name: Mapped[str] = mapped_column(String, nullable=True)
    from_block_type: Mapped[BlockTypes] = mapped_column(
        sqlalchemy.Enum(BlockTypes, name="blocktypes"), nullable=True
    )
    to_block_type: Mapped[BlockTypes] = mapped_column(
        sqlalchemy.Enum(BlockTypes, name="blocktypes"), nullable=True
    )
    release_type: Mapped[ReleaseTypes] = mapped_column(
        sqlalchemy.Enum(ReleaseTypes, name="releasetypes"), nullable=True
    )
    days_prior: Mapped[Union[int, None]] = mapped_column(Integer, nullable=True)
    processing_status: Mapped[BlockProcessingStatus] = mapped_column(
        sqlalchemy.Enum(BlockProcessingStatus, name="block_processing_statuses"), nullable=False
    )
    rejected_reason: Mapped[str] = mapped_column(String, nullable=True)

    __table_args__ = (PrimaryKeyConstraint("block_release_file_id", "row_number"),)

    def __repr__(self) -> str:
        return f"<BlockReleaseFileRow(block_release_file_id='{self.block_release_file_id}', row_number='{self.row_number}')>"


class BlockScheduleFileRowModel(Base, TimestampMixin, OrgIdMixin):
    __tablename__ = "block_schedule_file_rows"
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=sqlalchemy.text("gen_random_uuid()"),
    )
    block_schedule_file_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("block_schedule_processed_files.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    row_number: Mapped[int] = mapped_column(Integer, nullable=False)
    org_id: Mapped[str] = mapped_column(String, nullable=False, index=True)
    site_id: Mapped[str] = mapped_column(String, nullable=True, index=True)
    room_id: Mapped[str] = mapped_column(String, nullable=True, index=True)
    room_name: Mapped[str] = mapped_column(String, nullable=False, index=True)
    timezone: Mapped[str] = mapped_column(String, nullable=True)
    block_date: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
    block_name: Mapped[str] = mapped_column(String, nullable=True)
    block_type: Mapped[BlockTypes] = mapped_column(
        sqlalchemy.Enum(BlockTypes, name="blocktypes"), nullable=True
    )
    block_start_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
    block_end_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
    processing_status: Mapped[BlockProcessingStatus] = mapped_column(
        sqlalchemy.Enum(BlockProcessingStatus, name="block_processing_statuses"), nullable=False
    )
    rejected_reason: Mapped[str] = mapped_column(String, nullable=True)

    __table_args__ = (
        UniqueConstraint(
            "block_schedule_file_id", "row_number", name="uq_block_schedule_file_row_number"
        ),
    )

    def __repr__(self) -> str:
        return f"<BlockScheduleFileRow(block_schedule_file_id='{self.block_schedule_file_id}', row_number='{self.row_number}')>"


@dataclass
class BlockTimeDataInput:
    org_id: str
    room_name: str
    timezone: str
    block_date: date
    block_name: str
    block_type: BlockTypes
    row_number: int

    block_start_time: Optional[datetime] = None
    block_end_time: Optional[datetime] = None
    site_id: Optional[str] = None
    room_id: Optional[str] = None
    rejected_reason: Optional[str] = None

    @staticmethod
    def get_field_names() -> list[str]:
        return [field.name for field in fields(BlockTimeDataInput)]


class BlockTimeReleaseModel(Base, TimestampMixin):
    __tablename__ = "block_time_releases"

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        if (
            kwargs.get("start_time") is not None
            and kwargs.get("end_time") is not None
            and "release_time" not in kwargs
        ):
            start_time = kwargs.get("start_time")
            end_time = kwargs.get("end_time")
            kwargs["release_time"] = Range(start_time, end_time)

        super().__init__(*args, **kwargs)

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=sqlalchemy.text("gen_random_uuid()"),
    )
    block_time_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("block_times.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
    )
    from_block_type: Mapped[BlockTypes] = mapped_column(
        sqlalchemy.Enum(BlockTypes, name="blocktypes"), nullable=True
    )
    to_block_type: Mapped[BlockTypes] = mapped_column(
        sqlalchemy.Enum(BlockTypes, name="blocktypes"), nullable=True
    )
    to_block_id: Mapped[Union[uuid.UUID, None]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("blocks.id", ondelete="SET NULL"),
        nullable=True,
    )
    release_type: Mapped[ReleaseTypes] = mapped_column(
        sqlalchemy.Enum(ReleaseTypes, name="releasetypes"), nullable=True
    )
    days_prior: Mapped[Union[int, None]] = mapped_column(Integer, nullable=True)
    room_id: Mapped[str] = mapped_column(
        String,
        ForeignKey("rooms.id"),
        nullable=True,
    )
    block_time = relationship("BlockTimeModel", back_populates="releases")

    release_time = Column(TSTZRANGE(), nullable=False, index=True)
    # Redundant columns to enable Datastream replication
    # https://linear.app/apella/issue/OBS-1393/create-start-timeend-time-columns-for-tables-that-use-range-type
    start_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, index=True
    )
    end_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, index=True)

    reason: Mapped[Union[str, None]] = mapped_column(String, nullable=True)
    # TODO: to be deprecated (convention is type_time for time columns)
    released_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, index=True, server_default=func.now()
    )
    released_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, index=True, server_default=func.now()
    )
    source: Mapped[str] = mapped_column(String, nullable=False)
    source_type: Mapped[str] = mapped_column(String, nullable=False, index=True)

    unreleased_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=True, index=True
    )
    unreleased_source: Mapped[Union[str, None]] = mapped_column(String, nullable=True)

    __table_args__ = (
        ExcludeConstraint(
            ("block_time_id", "="),
            (Column("release_time"), "&&"),
            where="unreleased_time IS NULL",
            name="unique_block_time_release_tsrange_constraint",
            using="gist",
        ),
        CheckConstraint(
            "start_time < end_time", name="ck_block_time_release_start_time_lt_end_time"
        ),
        CheckConstraint("LENGTH(source) >= 3", name="source_min_length_check"),
    )

    def __repr__(self) -> str:
        return f"<BlockTimeRelease(id='{self.id}', block_time_id='{self.block_time_id}', start_time='{self.start_time}', 'end_time='{self.end_time}')>"


class BlockTimeModel(Base, TimestampMixin):
    __tablename__ = "block_times"

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        if (
            kwargs.get("start_time") is not None
            and kwargs.get("end_time") is not None
            and "block_time" not in kwargs
        ):
            start_time = kwargs.get("start_time")
            end_time = kwargs.get("end_time")
            kwargs["block_time"] = Range(start_time, end_time)

        super().__init__(*args, **kwargs)

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=sqlalchemy.text("gen_random_uuid()"),
    )
    block_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("blocks.id"), nullable=False, index=True
    )
    block = relationship("BlockModel", back_populates="block_times")

    block_time = Column(TSTZRANGE(), nullable=False, index=True)
    # Redundant columns to enable Datastream replication
    # https://linear.app/apella/issue/OBS-1393/create-start-timeend-time-columns-for-tables-that-use-range-type
    start_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, index=True
    )
    end_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, index=True)

    room_id: Mapped[str] = mapped_column(String, ForeignKey("rooms.id"), nullable=False, index=True)

    released_from: Mapped[uuid.UUID | None] = mapped_column(
        ForeignKey("block_times.id", ondelete="SET NULL"), nullable=True
    )

    created_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), index=True
    )

    releases = relationship(
        "BlockTimeReleaseModel",
        cascade="all, delete-orphan",
        back_populates="block_time",
    )

    __table_args__ = (
        CheckConstraint("start_time < end_time", name="ck_block_time_start_time_lt_end_time"),
    )

    def __repr__(self) -> str:
        return f"<BlockTime(id='{self.id}', block_id='{self.block_id}', room_id='{self.room_id}', start_time='{self.start_time}', 'end_time='{self.end_time}')>"


class BlockModel(Base, OrgIdMixin, TimestampMixin, ArchivedTimeMixin):
    __tablename__ = "blocks"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=sqlalchemy.text("gen_random_uuid()"),
    )
    name: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    color: Mapped[str] = mapped_column(String(16), nullable=False, default="#808080")
    block_times = relationship(
        "BlockTimeModel", cascade="all, delete-orphan", back_populates="block"
    )
    block_type: Mapped[BlockTypes] = mapped_column(
        sqlalchemy.Enum(BlockTypes, name="blocktypes"), nullable=True
    )
    surgeons = relationship(
        StaffModel,
        secondary=lambda: block_surgeons,
        lazy="joined",
    )

    surgeon_ids = association_proxy("surgeons", "id")

    sites_list = relationship(
        Site,
        secondary=lambda: block_sites,
        lazy="joined",
    )

    site_ids = association_proxy("sites_list", "id")

    __table_args__ = (
        CheckConstraint(
            "(color ~ '^#[0-9a-fA-F]{6}$')",
            name="ck_block_color",
        ),
    )

    def __repr__(self) -> str:
        return f"<Block(id='{self.id}', name='{self.name}')>"


block_surgeons: Final[Table] = Table(
    "block_surgeons",
    Base.metadata,
    Column(
        "block_id",
        UUID(as_uuid=True),
        ForeignKey("blocks.id"),
        nullable=False,
        index=True,
        primary_key=True,
    ),
    Column(
        "staff_id",
        UUID(as_uuid=True),
        ForeignKey("staff.id"),
        nullable=False,
        index=True,
        primary_key=True,
    ),
)

block_sites: Final[Table] = Table(
    "block_sites",
    Base.metadata,
    Column(
        "block_id",
        UUID(as_uuid=True),
        ForeignKey("blocks.id"),
        nullable=False,
        index=True,
        primary_key=True,
    ),
    Column(
        "site_id",
        String,
        ForeignKey("sites.id"),
        nullable=False,
        index=True,
        primary_key=True,
    ),
    Index("ix_unique_block_site", "block_id", "site_id", unique=True),
)


@dataclass
class BlockDataInput:
    id: uuid.UUID
    name: str
    color: str
    block_times: list[BlockTimeModel]
    surgeon_ids: Optional[list[Union[str, Literal["UNSET"]]]]
    org_id: Optional[str] = None
    site_ids: Optional[list[Union[str, Literal["UNSET"]]]] = None


@dataclass
class BlockReleaseDataInput:
    id: Union[UUID[str], str, uuid.UUID]
    release_reason: str
    released_by: str

    block_name: str
    room_name: str
    timezone: str

    block_date: Optional[datetime] = None
    block_time_id: Optional[uuid.UUID] = None
    to_block: Optional[str] = None
    to_block_id: Optional[uuid.UUID] = None
    from_block_type: Optional[BlockTypes] = None
    to_block_type: Optional[BlockTypes] = None
    release_type: Optional[ReleaseTypes] = None
    days_prior: Optional[int] = None

    site_id: Optional[str] = None
    room_id: Optional[str] = None

    release_start_time: Optional[datetime] = None
    release_end_time: Optional[datetime] = None
    release_length: Optional[int] = None
    released_time: Optional[datetime] = None

    block_release_file_id: Optional[uuid.UUID] = None
    row_number: Optional[int] = None
    external_id: Optional[str] = None
    rejected_reason: Optional[str] = None

    @staticmethod
    def get_field_names() -> list[str]:
        return [field.name for field in fields(BlockReleaseDataInput)]


def get_output_csv_content(releases: list[BlockReleaseDataInput]) -> str:
    field_names = BlockReleaseDataInput.get_field_names()
    output = io.StringIO()
    writer = csv.DictWriter(output, fieldnames=field_names)

    writer.writeheader()
    for release in releases:
        release_dict = asdict(release)
        for field_name in field_names:
            field_value = getattr(release, field_name)
            if isinstance(field_value, datetime):
                release_dict[field_name] = field_value.strftime("%Y-%m-%d %H:%M:%S")
        writer.writerow(release_dict)

    csv_content = output.getvalue()
    output.close()
    return csv_content


@dataclass
class BlockReleaseBulkCreateDataInput:
    releases: list[BlockReleaseDataInput]
    timezone: str  # org level timezone, as a fallback
    org_id: str


@dataclass
class BlockFile:
    file_name: str
    bucket_name: str
    generation: str
    size: str
    md5_hash: str
    media_link: str
    org_id: str


@dataclass
class TimeInterval:
    start_time: datetime
    end_time: datetime


def generate_interval_id(block_time_id: str, start_time: datetime) -> str:
    return f"{block_time_id}-{start_time.isoformat()}"


@dataclass
class BlockTimeInterval:
    id: str
    block_time_id: str
    start_time: datetime
    end_time: datetime
    room_id: str
    block_id: uuid.UUID
    surgeon_ids: Optional[list[str]] = None

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        self.block_time_id = kwargs.get("block_time_id")  # type: ignore
        self.start_time = kwargs.get("start_time")  # type: ignore
        self.end_time = kwargs.get("end_time")  # type: ignore
        self.room_id = kwargs.get("room_id")  # type: ignore
        self.block_id = kwargs.get("block_id")  # type: ignore
        self.surgeon_ids = kwargs.get("surgeon_ids")

        assert self.block_time_id is not None
        assert self.start_time is not None
        assert self.end_time is not None
        assert self.room_id is not None
        assert self.block_id is not None

        self.id = generate_interval_id(self.block_time_id, self.start_time)
