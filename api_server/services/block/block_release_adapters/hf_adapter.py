from typing import Text, Optional
from uuid import uuid4

from api_server.services.block.block_models import (
    BlockReleaseBulkCreateDataInput,
    BlockReleaseDataInput,
)
from api_server.services.block.block_release_adapters.block_release_base_adapter import (
    BlockReleaseBaseAdapter,
    check_released_by,
    convert_timestamp,
)
from api_server.services.block.block_csv_asset_store import BlockCsvAssetStore
from api_server.services.block.block_store import ROOM_NOT_FOUND
from api_server.services.ehr_interfaces.mapping_store import MappingStore
from api_server.services.site.site_models import SiteQuery
from api_server.services.site.site_store import SiteStore, Site

HF_ORG_ID = "health_first"
HEALTH_FIRST_CSV_HEADER = [
    "Block Name",
    "OR Room #",
    "Release Reason",
    "",
    "Minutes Released",
    "Block Date",
    "Released By",
    "Released On",
    "DOW",
    "Week of Month",
]


class BlockReleaseHFAdapter(BlockReleaseBaseAdapter):
    @staticmethod
    async def transform(
        file_content: Text, file_name: Optional[str] = None
    ) -> BlockReleaseBulkCreateDataInput:
        sites: list[Site] = await SiteStore().query_sites(SiteQuery(organization_id=HF_ORG_ID))
        timezone = sites[0].timezone

        csv_content = BlockCsvAssetStore.get_csv_content(file_content=file_content, skip_n_rows=13)
        room_names = set(
            row[HEALTH_FIRST_CSV_HEADER[1]]
            for row in csv_content
            if row[HEALTH_FIRST_CSV_HEADER[1]]
        )
        room_info = await MappingStore().populate_room_info_mapping(HF_ORG_ID, room_names, sites)

        releases = []

        for i, row in enumerate(csv_content):
            if row["Block Name"] == "":
                # Skip empty or invalid block record names
                continue
            # Initialize with some safe defaults (csv invalid format caught before this)
            release_input = BlockReleaseDataInput(
                id=uuid4(),
                block_name=row["Block Name"],
                room_name=row[HEALTH_FIRST_CSV_HEADER[1]],
                release_reason=row["Release Reason"],
                released_by=row["Released By"],
                release_length=int(row["Minutes Released"]),
                row_number=i,
                timezone=timezone,
            )

            try:
                release_input.released_by = check_released_by(
                    release_input.released_by, release_input.release_reason
                )
                room = room_info.get(release_input.room_name) if room_info else None

                if room:
                    release_input.site_id = room.site_id
                    release_input.room_id = room.room_id
                    release_input.timezone = room.timezone
                else:
                    release_input.rejected_reason = ROOM_NOT_FOUND

                release_input.block_date = convert_timestamp(
                    row["Block Date"], "%b-%d-%Y", timezone
                )
                release_input.released_time = convert_timestamp(
                    row["Released On"], "%b-%d-%Y", timezone
                )
            except ValueError as value_error:
                release_input.rejected_reason = str(value_error)
            except Exception as e:  # Catch-all for other unexpected errors
                release_input.rejected_reason = f"Unexpected error: {str(e)}"
            finally:
                releases.append(release_input)

        block_release_bulk_create_data_input = BlockReleaseBulkCreateDataInput(
            releases=releases,
            timezone=timezone,
            org_id=HF_ORG_ID,
        )

        return block_release_bulk_create_data_input
