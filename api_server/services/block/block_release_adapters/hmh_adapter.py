from typing import Text, Optional
from uuid import uuid4

from api_server.services.block.block_models import (
    BlockReleaseBulkCreateDataInput,
    BlockReleaseDataInput,
)
from api_server.services.block.block_release_adapters.block_release_base_adapter import (
    BlockReleaseBaseAdapter,
    check_released_by,
    get_block_type,
    convert_timestamp,
    get_release_type,
)
from api_server.services.block.block_csv_asset_store import BlockCsvAssetStore
from api_server.services.block.block_store import ROOM_NOT_FOUND
from api_server.services.ehr_interfaces.mapping_store import MappingStore
from api_server.services.site.site_models import SiteQuery
from api_server.services.site.site_store import SiteStore, Site

HMH_CSV_DATE_TIME_FORMAT = "%m/%d/%Y %H%M"
HMH_ORG_ID = "houston_methodist"
HOUSTON_METHODIST_CSV_HEADER = [
    "Release ID",
    "Release Type",
    "Location",
    "Room",
    "From Block Type",
    "From Block",
    "To Block",
    "Release Date",
    "Release Time",
    "Release Length",
    "Release Reason",
    "Block Date",
    "Release User",
    "To Block Type",
    "Release Comments",
    "Days Prior",
    "Release Start Time",
    "Release End Time",
]


class BlockReleaseHMHAdapter(BlockReleaseBaseAdapter):
    @staticmethod
    async def transform(
        file_content: Text, file_name: Optional[str]
    ) -> BlockReleaseBulkCreateDataInput:
        sites: list[Site] = await SiteStore().query_sites(SiteQuery(organization_id=HMH_ORG_ID))
        timezone = sites[0].timezone

        csv_content = BlockCsvAssetStore.get_csv_content(file_content=file_content)
        room_names = set(
            row[HOUSTON_METHODIST_CSV_HEADER[3]]
            for row in csv_content
            if row[HOUSTON_METHODIST_CSV_HEADER[3]]
        )
        room_info = await MappingStore().populate_room_info_mapping(HMH_ORG_ID, room_names, sites)
        releases = []

        for i, row in enumerate(csv_content):
            if row["From Block"] == "":
                # Skip empty or invalid block record names
                continue
            # Initialize with some safe defaults (csv invalid format caught before this)
            release_input = BlockReleaseDataInput(
                id=uuid4(),
                block_name=row["From Block"],
                room_name=row[HOUSTON_METHODIST_CSV_HEADER[3]],
                release_reason=f"{row['Release Reason']} {row['Release Comments']}",
                released_by=row["Release User"],
                release_length=int(row["Release Length"]),
                to_block=row["To Block"],
                from_block_type=get_block_type(row["From Block Type"].lower().title()),
                to_block_type=get_block_type(row["To Block Type"].lower().title()),
                row_number=i,
                external_id=row[HOUSTON_METHODIST_CSV_HEADER[0]],
                release_type=get_release_type(row["Release Type"].lower().title()),
                days_prior=int(row["Days Prior"]),
                timezone=timezone,
            )

            try:
                release_input.released_by = check_released_by(
                    release_input.released_by, release_input.release_reason
                )
                room = room_info.get(row["Room"]) if room_info else None

                if room:
                    release_input.site_id = room.site_id
                    release_input.room_id = room.room_id
                    release_input.timezone = room.timezone
                else:
                    release_input.rejected_reason = ROOM_NOT_FOUND

                release_input.block_date = convert_timestamp(
                    row["Block Date"], "%m/%d/%Y", timezone
                )
                release_input.released_time = convert_timestamp(
                    f"{row['Release Date']} {row['Release Time'].zfill(4)}",
                    HMH_CSV_DATE_TIME_FORMAT,
                    timezone,
                )
                release_input.release_start_time = convert_timestamp(
                    row["Release Start Time"], HMH_CSV_DATE_TIME_FORMAT, timezone
                )
                release_input.release_end_time = convert_timestamp(
                    row["Release End Time"], HMH_CSV_DATE_TIME_FORMAT, timezone
                )
            except ValueError as value_error:
                release_input.rejected_reason = str(value_error)
            except Exception as e:  # Catch-all for other unexpected errors
                release_input.rejected_reason = f"Unexpected error: {str(e)}"
            finally:
                releases.append(release_input)

        block_release_bulk_create_data_input = BlockReleaseBulkCreateDataInput(
            releases=releases,
            timezone=timezone,
            org_id=HMH_ORG_ID,
        )

        return block_release_bulk_create_data_input
