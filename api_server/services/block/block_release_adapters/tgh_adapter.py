from datetime import timed<PERSON><PERSON>
from typing import Text, Optional
from uuid import uuid4

from api_server.services.block.block_models import (
    BlockReleaseBulkCreateDataInput,
    BlockReleaseDataInput,
)
from api_server.services.block.block_release_adapters.block_release_base_adapter import (
    Block<PERSON>eleaseBaseAdapter,
    check_released_by,
    convert_timestamp,
    get_release_type,
    get_block_type,
    get_release_reason_str,
)
from api_server.services.block.block_csv_asset_store import BlockCsvAssetStore
from api_server.services.block.block_store import ROOM_NOT_FOUND
from api_server.services.ehr_interfaces.mapping_store import MappingStore
from api_server.services.site.site_models import SiteQuery
from api_server.services.site.site_store import SiteStore, Site

TGH_CSV_DATE_TIME_FORMAT = "%Y-%m-%d %H:%M:%S"
TGH_ORG_ID = "tampa_general"
TAMPA_GENERAL_CSV_HEADER = [
    "record_id",
    "template_modification_type",
    "release_days_in_advance",
    "room_id",
    "or_location",
    "modification_instant",
    "modification_user_id",
    "modification_user",
    "from_block_type",
    "from_block_id",
    "from_block",
    "from_block_record_name",
    "to_block_type",
    "to_block_id",
    "to_block",
    "to_block_record_name",
    "block_start_instant",
    "block_end_instant",
    "template_date",
    "release_reason",
    "unavailable_reason",
    "release_comments",
    "request_range",
    "request_declined",
    "request_declined_comments",
    "request_declined_by_user",
]


class BlockReleaseTGHAdapter(BlockReleaseBaseAdapter):
    @staticmethod
    async def transform(
        file_content: Text, file_name: Optional[str] = None
    ) -> BlockReleaseBulkCreateDataInput:
        sites: list[Site] = await SiteStore().query_sites(SiteQuery(organization_id=TGH_ORG_ID))
        timezone = sites[0].timezone

        csv_content = BlockCsvAssetStore.get_csv_content(file_content=file_content)
        room_names = set(
            row[TAMPA_GENERAL_CSV_HEADER[4]]
            for row in csv_content
            if row[TAMPA_GENERAL_CSV_HEADER[4]]
        )
        room_info = await MappingStore().populate_room_info_mapping(TGH_ORG_ID, room_names, sites)
        releases = []

        for i, row in enumerate(csv_content):
            if (row["from_block_record_name"] == "") or (row["from_block_record_name"] == "0"):
                # Skip empty or invalid block record names
                continue
            # Initialize with some safe defaults (csv invalid format caught before this)
            release_input = BlockReleaseDataInput(
                id=uuid4(),
                block_name=row["from_block_record_name"],
                room_name=row[TAMPA_GENERAL_CSV_HEADER[4]],
                release_reason=get_release_reason_str(
                    row["release_reason"], row["unavailable_reason"], row["release_comments"]
                ),
                released_by=row["modification_user"],
                release_length=0,
                to_block=row["to_block"],
                from_block_type=get_block_type(row["from_block_type"].lower().title()),
                to_block_type=get_block_type(row["to_block_type"].lower().title()),
                row_number=i,
                external_id=row[TAMPA_GENERAL_CSV_HEADER[0]],
                release_type=get_release_type(row["template_modification_type"].lower().title()),
                days_prior=int(row["release_days_in_advance"]),
                timezone=timezone,
            )

            try:
                release_input.released_by = check_released_by(
                    release_input.released_by, release_input.release_reason
                )
                room = room_info.get(release_input.room_name) if room_info else None

                if room:
                    release_input.site_id = room.site_id
                    release_input.room_id = room.room_id
                    release_input.timezone = room.timezone
                else:
                    release_input.rejected_reason = ROOM_NOT_FOUND

                block_start_instant = convert_timestamp(
                    row["block_start_instant"], TGH_CSV_DATE_TIME_FORMAT, timezone
                )
                release_input.release_start_time = block_start_instant
                release_input.block_date = block_start_instant
                block_end_instant = convert_timestamp(
                    row["block_end_instant"], TGH_CSV_DATE_TIME_FORMAT, timezone
                )
                release_input.release_end_time = block_end_instant

                release_input.release_length = int(
                    (block_end_instant - block_start_instant) / timedelta(minutes=1)
                )  # Convert to minutes
                release_input.released_time = convert_timestamp(
                    row["modification_instant"], TGH_CSV_DATE_TIME_FORMAT, timezone
                )
            except ValueError as value_error:
                release_input.rejected_reason = str(value_error)
            except Exception as e:  # Catch-all for other unexpected errors
                release_input.rejected_reason = f"Unexpected error: {str(e)}"
            finally:
                releases.append(release_input)

        block_release_bulk_create_data_input = BlockReleaseBulkCreateDataInput(
            releases=releases,
            timezone=timezone,
            org_id=TGH_ORG_ID,
        )

        return block_release_bulk_create_data_input
