from dataclasses import dataclass
import uuid
from sqlalchemy import (
    select,
)

from sqlalchemy import func

from apella_cloud_api.exceptions import NotFound
from datetime import date, datetime, time
from typing import Optional, Sequence

from api_server.services.block.block_models import (
    BlockProcessingStatus,
    BlockReleaseBulkCreateDataInput,
    BlockReleaseFileRowModel,
    BlockReleaseProcessedFileModel,
)
from databases.sql import new_async_session
from databases.sql.helpers import async_postgres_insert_on_conflict_do_update_helper


class BlockReleaseFileStore:
    async def get_processed_files(
        self,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        org_id: Optional[str] = None,
        md5_hash: Optional[str] = None,
    ) -> Sequence[BlockReleaseProcessedFileModel]:
        if not (start_date and end_date and org_id) and not md5_hash:
            return []
        async with new_async_session() as session:
            statement = select(BlockReleaseProcessedFileModel)
            if start_date and end_date and org_id:
                statement = statement.filter(
                    BlockReleaseProcessedFileModel.org_id == org_id,
                    func.date(BlockReleaseProcessedFileModel.created_time) >= start_date,
                    func.date(BlockReleaseProcessedFileModel.created_time) <= end_date,
                )
            elif md5_hash:
                statement = statement.filter(BlockReleaseProcessedFileModel.md5_hash == md5_hash)
            else:
                return []
            return (await session.scalars(statement)).all()

    async def save_processed_file(
        self, file: BlockReleaseProcessedFileModel
    ) -> BlockReleaseProcessedFileModel:
        async with new_async_session() as session:
            session.add(file)
            await session.commit()
            await session.refresh(file)
            return file

    async def save_transformed_block_release_rows(
        self,
        block_release_file_id: uuid.UUID,
        block_release_file_input: BlockReleaseBulkCreateDataInput,
    ) -> list[BlockReleaseFileRowModel]:
        async with new_async_session() as session:
            block_release_file_rows = [
                BlockReleaseFileRowModel(
                    org_id=block_release_file_input.org_id,
                    block_release_file_id=block_release_file_id,
                    row_number=release.row_number,
                    external_id=release.external_id,
                    site_id=release.site_id,
                    room_id=release.room_id,
                    room_name=release.room_name,
                    timezone=release.timezone,
                    start_time=release.release_start_time,
                    end_time=release.release_end_time,
                    release_length=release.release_length,
                    release_reason=release.release_reason,
                    released_time=release.released_time,
                    released_by=release.released_by,
                    release_type=release.release_type,
                    days_prior=release.days_prior,
                    block_name=release.block_name,
                    block_date=release.block_date,
                    to_block_name=release.to_block,
                    from_block_type=release.from_block_type,
                    to_block_type=release.to_block_type,
                    rejected_reason=release.rejected_reason,
                    processing_status=BlockProcessingStatus.PENDING
                    if release.rejected_reason is None or release.rejected_reason == ""
                    else BlockProcessingStatus.REJECTED,
                )
                for release in block_release_file_input.releases
                if release.row_number is not None
            ]
            await async_postgres_insert_on_conflict_do_update_helper(
                session=session,
                subject=BlockReleaseFileRowModel,
                values=block_release_file_rows,
                returning=BlockReleaseFileRowModel.row_number,
                index_elements=[
                    BlockReleaseFileRowModel.block_release_file_id,
                    BlockReleaseFileRowModel.row_number,
                ],
            )
            await session.commit()
            return block_release_file_rows

    @dataclass
    class BlockReleaseFileRowStatus:
        row_number: int
        rejected_reason: str | None

    # Bulk update block release file row statuses
    async def update_block_release_file_row_statuses(
        self,
        block_release_file_id: str | uuid.UUID,
        statuses: list[BlockReleaseFileRowStatus],
    ) -> None:
        async with new_async_session() as session:
            q = (
                select(BlockReleaseFileRowModel)
                .filter(
                    (BlockReleaseFileRowModel.block_release_file_id == block_release_file_id)
                    & (
                        BlockReleaseFileRowModel.row_number.in_(
                            [
                                status.row_number
                                for status in statuses
                                if status.row_number is not None
                            ]
                        )
                    )
                )
                .with_for_update()
            )
            rows = (await session.scalars(q)).all()
            row_map = {r.row_number: r for r in rows}
            for status in statuses:
                # Skip if row_number is None, these don't exist in the database
                if status.row_number is None:
                    continue

                row = row_map.get(status.row_number)
                if row is None:
                    raise NotFound(
                        f"Block release file row not found with block_release_file_id: {block_release_file_id} and row_number: {status.row_number}"
                    )

                row.rejected_reason = (
                    status.rejected_reason if status.rejected_reason is not None else ""
                )
                row.processing_status = (
                    BlockProcessingStatus.REJECTED
                    if status.rejected_reason is not None and status.rejected_reason != ""
                    else BlockProcessingStatus.PROCESSED
                )
            await session.commit()

    async def get_block_release_file_rows(
        self, block_release_file_id: str | uuid.UUID
    ) -> list[BlockReleaseFileRowModel]:
        async with new_async_session() as session:
            q = select(BlockReleaseFileRowModel).filter(
                BlockReleaseFileRowModel.block_release_file_id == block_release_file_id
            )
            return list((await session.scalars(q)).unique().all())

    async def get_pending_block_release_file_rows_by_room_ids_and_dates(
        self,
        room_ids: list[str],
        start_date: date,
        end_date: date,
    ) -> dict[str, dict[date, list[BlockReleaseFileRowModel]]]:
        async with new_async_session() as session:
            q = (
                select(BlockReleaseFileRowModel)
                .filter(
                    BlockReleaseFileRowModel.room_id.in_(room_ids),
                    # filter for models that have a block_date that is on the given date
                    (BlockReleaseFileRowModel.block_date >= datetime.combine(start_date, time.min))
                    & (BlockReleaseFileRowModel.block_date <= datetime.combine(end_date, time.max)),
                )
                .filter(BlockReleaseFileRowModel.processing_status == BlockProcessingStatus.PENDING)
                .order_by(
                    BlockReleaseFileRowModel.released_time.asc(),
                    BlockReleaseFileRowModel.row_number.asc(),
                )
            )
            rows = (await session.scalars(q)).all()
            result: dict[str, dict[date, list[BlockReleaseFileRowModel]]] = {}
            for row in rows:
                if row.room_id not in result:
                    result[row.room_id] = {}
                block_date = row.block_date.date()
                if block_date not in result[row.room_id]:
                    result[row.room_id][block_date] = []
                result[row.room_id][block_date].append(row)
            return result
