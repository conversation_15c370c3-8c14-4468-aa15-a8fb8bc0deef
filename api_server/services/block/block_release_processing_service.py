from dataclasses import dataclass
from datetime import date, datetime, time, timedelta
from http import HTTPStatus
import logging
from typing import Text, Type
import uuid

import pytz
from apella_cloud_api.dtos import BlockQueryDto, RoomClosureQueryDto
from api_server.logging.audit import log_calls_to_audit
from api_server.services.block.block_models import (
    SPECIAL_BLOCK_TYPES,
    BlockModel,
    BlockReleaseBulkCreateDataInput,
    BlockReleaseDataInput,
    BlockReleaseFileRowModel,
    BlockFile,
    BlockReleaseProcessedFileModel,
    BlockTimeModel,
    BlockTimeReleaseModel,
    BlockTimeInterval,
    BlockTypes,
)
from api_server.services.block.block_release_adapters.apella_adapter import (
    APELLA_CSV_HEADER,
    BlockReleaseApellaAdapter,
)
from api_server.services.block.block_release_adapters.block_release_base_adapter import (
    BlockReleaseBaseAdapter,
)
from api_server.services.block.block_release_adapters.hf_adapter import (
    HEALTH_FIRST_CSV_HEADER,
    BlockReleaseHFAdapter,
)
from api_server.services.block.block_release_adapters.hmh_adapter import (
    HOUSTON_METHODIST_CSV_HEADER,
    BlockReleaseHMHAdapter,
)
from api_server.services.block.block_release_adapters.tgh_adapter import (
    TAMPA_GENERAL_CSV_HEADER,
    BlockReleaseTGHAdapter,
)
from api_server.services.block.block_csv_asset_store import BlockCsvAssetStore
from api_server.services.block.block_release_file_store import BlockReleaseFileStore
from api_server.services.block.block_service import BlockService
from api_server.services.block.block_slack_notifier import BlockSlackNotifier
from api_server.services.block.block_store import (
    BLOCK_RELEASE_SOURCE_TYPE,
    BLOCK_NOT_FOUND,
    RELEASE_BULK_IMPORT_BLOCK_TIME_NOT_FOUND,
    RELEASE_BULK_IMPORT_RELEASE_CONFLICTS,
    RELEASE_BULK_IMPORT_RELEASE_INVALID_TIMES,
    ROOM_NOT_FOUND,
)
from api_server.services.closures.closure_service import ClosureService
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import READ_ANY_BLOCK, WRITE_ANY_BLOCK
from apella_cloud_api.exceptions import ClientError


logger = logging.getLogger(__name__)


@dataclass
class BlockReleaseProcessingService:
    auth: Auth
    block_service: BlockService
    block_release_file_store: BlockReleaseFileStore
    block_csv_asset_store: BlockCsvAssetStore
    closure_service: ClosureService

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def get_block_given_block_name(
        self,
        name: str | None,
        org_id: str | None = None,
        site_id: str | None = None,
        room_id: str | None = None,
        blocks: list[BlockModel] | None = None,
    ) -> BlockModel | None:
        if not name:
            return None
        if blocks and site_id:
            return next(
                (
                    block
                    for block in blocks
                    if block.name.lower() == name.lower()
                    and site_id in block.site_ids
                    and block.archived_time is None
                ),
                None,
            )

        dto = BlockQueryDto(
            names=[name],
            room_ids=[room_id] if room_id else None,
            org_ids=[org_id] if org_id else None,
            site_ids=[site_id] if site_id else None,
        )
        blocks = await self.block_service.query_blocks(
            query=dto,
            eager_load_block_times=True,
            include_empty_blocks=False,
        )

        if not blocks:
            return None

        return next(
            (
                block
                for block in blocks
                if block.name.lower() == name.lower() and block.archived_time is None
            ),
            None,
        )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def generate_block_time_given_release_model(
        self,
        release: BlockTimeReleaseModel,
    ) -> BlockTimeModel | None:
        if not release.to_block_id:
            return None

        to_block = await self.block_service.get_block(str(release.to_block_id))

        if not to_block:
            logger.warning(f"Block not found for ID: {release.to_block_id}")
            return None

        block_times = await self.block_service.query_block_times(
            room_ids=[release.room_id],
            min_end_time=release.start_time,
            max_start_time=release.end_time,
        )

        intervals = [
            interval
            for block_time in block_times
            for interval in await self.block_service.get_block_time_available_intervals(block_time)
            if block_time.room_id == release.room_id
            and interval.start_time <= release.start_time
            and interval.end_time >= release.end_time
        ]
        if len(intervals) > 0:
            logger.debug("Found existing block time intervals: %s", intervals)
            return None

        block_time = BlockTimeModel(
            id=uuid.uuid4(),
            block_id=to_block.id,
            room_id=release.room_id,
            start_time=release.start_time,
            end_time=release.end_time,
            releases=[],
            released_from=release.block_time_id,
        )
        logger.debug("Block time generated: %s", block_time)
        return block_time

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def process_releases(
        self,
        releases: list[BlockReleaseDataInput],
    ) -> None:
        logger.debug("Processing releases: %s", releases)
        for release_input in releases:
            try:
                await self._process_release(release_input)
            except Exception as e:
                logger.error(f"Error processing release: {e}, release_input: {release_input}")
                release_input.rejected_reason = str(e)
                # don't re-raise the exception
            finally:
                if (
                    release_input.block_release_file_id is not None
                    and release_input.row_number is not None
                ):
                    try:
                        await self.update_block_release_file_row_status(
                            block_release_file_id=release_input.block_release_file_id,
                            row_number=release_input.row_number,
                            rejected_reason=release_input.rejected_reason,
                        )
                    except Exception as e:
                        logger.error(f"Error updating block release file row status: {e}")
                        release_input.rejected_reason = str(e)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def _process_release(self, release_input: BlockReleaseDataInput) -> None:
        logger.debug("Processing release_input: %s", release_input)

        if release_input.rejected_reason is not None:
            return

        if release_input.room_id is None or release_input.site_id is None:
            release_input.rejected_reason = ROOM_NOT_FOUND
            return

        matching_block_times = await self.find_matching_block_times(release_input)
        matching_release = (
            await self.find_matching_release_from_block_times(release_input, matching_block_times)
            if matching_block_times
            else await self.find_matching_releases_for_special_types(release_input)
            if release_input.from_block_type in SPECIAL_BLOCK_TYPES
            else None
        )
        matching_interval = (
            await self.find_matching_available_interval(
                release_input=release_input,
                block_times=matching_block_times,
            )
            if matching_block_times and not matching_release
            else None
        )

        to_block = (
            await self.block_service.find_block_by_name_and_site(
                name=release_input.to_block, site_id=release_input.site_id
            )
            if release_input.to_block
            else None
        )
        if release_input.to_block and to_block is None:
            logger.warning("Missing block release to_block: %s", release_input.to_block)

        release_input.block_time_id = (
            uuid.UUID(matching_interval.block_time_id) if matching_interval else None
        )
        release_input.to_block_id = to_block.id if to_block else None

        release = _generate_block_time_release(
            release_input=release_input,
        )

        logger.debug("Release metadata: %s", release)

        if matching_release:
            await self.block_service.update_block_time_release(release, matching_release.id)
            logger.debug("Release updated: %s", release)
        elif (
            release.from_block_type not in SPECIAL_BLOCK_TYPES and release.block_time_id is not None
        ):
            await self.block_service.create_block_time_release(release)
            logger.debug("Release created: %s", release)

        if release_input.to_block:
            # if the to_block is not empty, we may need to create a new block time or update closures
            logger.debug("Processing to_block: %s", release_input.to_block)
            await self.process_block_time_release_to_block(release)

        logger.debug("Finished processing releases")

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def find_matching_block_times(
        self, release_input: BlockReleaseDataInput
    ) -> list[BlockTimeModel] | None:
        if release_input.from_block_type in SPECIAL_BLOCK_TYPES:
            # We don't currently store block times for these types
            return None
        if release_input.site_id is None:
            release_input.rejected_reason = ROOM_NOT_FOUND
            return None

        logger.debug("Attempting to find a matching block")
        # if the from_block_type is not one of the above, we need to find the existing block/block time and maybe release it
        block = await self.block_service.find_block_by_name_and_site(
            name=release_input.block_name, site_id=release_input.site_id
        )
        if block is None:
            release_input.rejected_reason = BLOCK_NOT_FOUND
            return None

        # this is a bit complex, we should usually just get a start and end time, but sometimes we only get a block date and length

        # first check to see if we have enough info (start time or date + end time or length)
        if (release_input.release_start_time is None and release_input.block_date is None) or (
            release_input.release_end_time is None
            and (release_input.release_length is None or release_input.release_length == 0)
        ):
            release_input.rejected_reason = RELEASE_BULK_IMPORT_RELEASE_INVALID_TIMES
            return None

        # if we have a block date, set the start time to the start of the day
        if release_input.release_start_time is None and release_input.block_date is not None:
            release_input.release_start_time = _get_start_of_day_with_tz(
                release_input.block_date, release_input.timezone
            )

        # sanity check for mypy
        if release_input.release_start_time is None:
            release_input.rejected_reason = RELEASE_BULK_IMPORT_RELEASE_INVALID_TIMES
            return None

        # now ensure the end time is set
        release_input.release_end_time = (
            release_input.release_end_time
            or release_input.release_start_time
            # we know that either we have an end time or a length > 0, but mypy doesn't understand so we add `or 0`
            + timedelta(minutes=release_input.release_length or 0)
        )
        # ensure the start time is before the end time
        if release_input.release_start_time >= release_input.release_end_time:
            release_input.rejected_reason = RELEASE_BULK_IMPORT_RELEASE_INVALID_TIMES
            return None

        logger.debug("Attempting to find a matching block time")
        block_times = await self.block_service.query_block_times(
            room_ids=[release_input.room_id] if release_input.room_id else None,
            min_end_time=release_input.release_start_time,
            max_start_time=release_input.release_end_time,
            block_id=str(block.id),
        )
        logger.debug("Found block times: %s", block_times)
        # find block times that match the room id and time range
        matching_block_times: list[BlockTimeModel] = [
            block_time
            for block_time in block_times
            if block_time.start_time <= release_input.release_start_time
            and block_time.end_time >= release_input.release_end_time
        ]
        if len(matching_block_times) == 0:
            release_input.rejected_reason = RELEASE_BULK_IMPORT_BLOCK_TIME_NOT_FOUND
            return None

        logger.debug("Found matching block times: %s", matching_block_times)
        return matching_block_times

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def find_matching_release_from_block_times(
        self,
        release_input: BlockReleaseDataInput,
        matching_block_times: list[BlockTimeModel],
    ) -> BlockTimeReleaseModel | None:
        # find releases for the matching block times
        existing_releases = await self.block_service.get_block_time_releases_for_block_times(
            block_time_ids=[str(bt.id) for bt in matching_block_times],
            active_only=True,
        )

        # check for matching releases
        matching_releases = [
            release
            for block_time in matching_block_times
            for release in existing_releases[str(block_time.id)]
            if release.start_time == release_input.release_start_time
            and (
                release.end_time == release_input.release_end_time
                or release_input.release_end_time is None
            )
            and (
                release.released_time == release_input.released_time
                or release_input.released_time is None
            )
        ]

        if len(matching_releases) == 1:
            # found a matching release, do nothing
            logger.debug("Found a matching release: %s", matching_releases[0])
            return matching_releases[0]
        elif len(matching_releases) > 1:
            logger.error("Found multiple matching releases: %s", matching_releases)
            release_input.rejected_reason = RELEASE_BULK_IMPORT_RELEASE_CONFLICTS
        return None

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def find_matching_releases_for_special_types(
        self,
        release_input: BlockReleaseDataInput,
    ) -> BlockTimeReleaseModel | None:
        if release_input.from_block_type not in SPECIAL_BLOCK_TYPES:
            # We don't currently store block times for these types
            return None

        if release_input.site_id is None or release_input.room_id is None:
            release_input.rejected_reason = ROOM_NOT_FOUND
            return None

        return (
            await self.block_service.get_block_time_release_for_special_block(
                from_block_type=release_input.from_block_type,
                room_id=release_input.room_id,
                start_time=release_input.release_start_time,
                end_time=release_input.release_end_time,
                to_block_type=release_input.to_block_type,
            )
            if release_input.release_start_time and release_input.release_end_time
            else None
        )

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def find_matching_available_interval(
        self,
        release_input: BlockReleaseDataInput,
        block_times: list[BlockTimeModel],
    ) -> BlockTimeInterval | None:
        logger.debug("Finding matching available interval for release: %s", release_input)
        if release_input.room_id is None:
            return None

        intervals = [
            interval
            for block_time in block_times
            for interval in await self.block_service.get_block_time_available_intervals(block_time)
        ]
        logger.debug("Found available intervals: %s", intervals)
        matching_intervals = [
            interval
            for interval in intervals
            if (
                release_input.release_start_time
                and interval.start_time <= release_input.release_start_time
            )
            and (
                release_input.release_end_time is None
                or interval.end_time >= release_input.release_end_time
            )
        ]

        if len(matching_intervals) == 1:
            logger.debug("Found matching available interval: %s", matching_intervals[0])
            return matching_intervals[0]
        elif len(matching_intervals) > 1:
            logger.error("Found multiple matching available intervals: %s", matching_intervals)
        return None

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def process_block_time_release_to_block(
        self,
        release: BlockTimeReleaseModel,
    ) -> None:
        # if releasing from unavailable to anything else, maybe delete a closure
        if (
            release.from_block_type == BlockTypes.UNAVAILABLE_BLOCK_TYPE
            and release.to_block_type != BlockTypes.UNAVAILABLE_BLOCK_TYPE
        ):
            # we currently represent unavailable through prime time and closure, so we need to delete the closure if it exists since this release should be opening the room
            await self._delete_room_closure(release)

        match release.to_block_type:
            case BlockTypes.UNBLOCKED_BLOCK_TYPE:
                logger.debug("Processing to_block of type UNBLOCKED")
                # currently, we don't do anything for unblocked type
            case BlockTypes.UNAVAILABLE_BLOCK_TYPE:
                # release to unavailable means closing the room
                logger.debug("Processing to_block of type UNAVAILABLE")
                await self._create_room_closure(release)
            case _:
                # for all other types, we need to create a block time if we have a time to release
                if (
                    release.block_time_id is None
                    and release.from_block_type not in SPECIAL_BLOCK_TYPES
                ):
                    logger.warning("Block time ID is None for release ID: %s", release.id)
                    return
                logger.debug("Creating block time for release ID: %s", release.id)
                block_time_model = await self.generate_block_time_given_release_model(
                    release=release,
                )

                if block_time_model:
                    await self.block_service.create_block_time(block_time_model)
                    logger.debug("Block time created: %s", block_time_model)

    async def _create_room_closure(self, release: BlockTimeReleaseModel) -> None:
        logger.debug("Entering _create_room_closure with release: %s", release)
        if release.room_id and release.start_time and release.end_time:
            await self.closure_service.create_room_closure(
                room_id=release.room_id,
                start_time=release.start_time,
                end_time=release.end_time,
            )

    async def _delete_room_closure(self, release: BlockTimeReleaseModel) -> None:
        logger.debug("Entering _delete_room_closure with release: %s", release)
        closures = await self.closure_service.query_room_closures(
            RoomClosureQueryDto(
                room_id=release.room_id,
                site_id=None,
                min_end_time=release.start_time,
                max_start_time=release.end_time,
            )
        )
        if closures:
            for closure in closures:
                logger.debug("Deleting closure %s for room %s", closure.id, closure.room_id)
                await self.closure_service.delete_room_closure(room_closure_id=str(closure.id))

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def transform_and_save_release_file(
        self, file: BlockReleaseProcessedFileModel
    ) -> BlockReleaseBulkCreateDataInput | None:
        """
        This function is used to transform and save the block release file.
        It will download the file from the bucket, transform it using the adapter,
        and then save the transformed data to the database.
        """
        file_content = self.block_csv_asset_store.download_csv_file_as_text(file.media_link)
        adapter = self.get_block_release_adapter(file.org_id)
        block_release_bulk_create_data_input = await adapter.transform(file_content, file.file_name)

        # Save the transformed releases to the database
        pending_release_rows = await self.save_transformed_block_release_rows(
            block_release_file_id=file.id,
            block_release_file_input=block_release_bulk_create_data_input,
        )

        # update file id and row numbers for the saved pending releases
        block_release_bulk_create_data_input.releases = [
            self.get_block_release_input_from_row(pending_release)
            for pending_release in pending_release_rows
        ]

        return block_release_bulk_create_data_input

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def process_block_release_file(
        self,
        file: BlockReleaseProcessedFileModel,
        slack_notifier: BlockSlackNotifier,
    ) -> None:
        try:
            block_release_bulk_create_data_input = await self.transform_and_save_release_file(file)
            if not block_release_bulk_create_data_input:
                return

            releases_by_room_date: dict[str, dict[date, list[BlockReleaseDataInput]]] = {}

            for release in block_release_bulk_create_data_input.releases:
                if not release.room_id or not release.block_date:
                    continue
                block_date = release.block_date.date()
                releases_by_room_date.setdefault(release.room_id, {}).setdefault(
                    block_date, []
                ).append(release)

            # for each room and its associated dates, process the list of release entries
            for room_release_rows in releases_by_room_date.values():
                for block_release_data_inputs in room_release_rows.values():
                    if block_release_data_inputs:
                        await self.process_releases(releases=block_release_data_inputs)

            # Upload releases to bucket
            signed_processed_media_link = (
                await self.block_csv_asset_store.upload_processed_releases(
                    block_release_bulk_create_data_input.releases
                )
            )
            signed_media_link = self.block_csv_asset_store.get_signed_url_for_blob(
                method="GET",
                blob=self.block_csv_asset_store.get_blob(file.bucket_name, file.file_name),
                expiration=datetime.now() + timedelta(days=3),
            )

            # Send a slack notification
            await slack_notifier.report(
                releases=block_release_bulk_create_data_input.releases,
                time_uploaded=datetime.now(),
                media_link=signed_media_link,
                output_media_link=signed_processed_media_link,
            )
        except Exception as e:
            error_msg = f"Error processing file {file.file_name}: {str(e)}"
            logger.warning(error_msg)
            await slack_notifier.log_error(error_msg)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def transform_and_save_release_files(
        self, start_date: date, end_date: date, org_id: str
    ) -> None:
        files = await self.block_release_file_store.get_processed_files(
            start_date=start_date, end_date=end_date, org_id=org_id
        )
        for file in files:
            await self.transform_and_save_release_file(file)

            logger.info(f"Processed file: {file.file_name}")

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def bulk_reprocess_block_releases(
        self, start_date: date, end_date: date, org_id: str
    ) -> None:
        slack_notifier = BlockSlackNotifier()
        files_to_reprocess = await self.block_release_file_store.get_processed_files(
            start_date=start_date, end_date=end_date, org_id=org_id
        )
        for file in files_to_reprocess:
            await slack_notifier.notify_file_upload(
                username="user to reprocess", file_name=file.file_name, file_size=int(file.size)
            )
            await self.process_block_release_file(file, slack_notifier)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def process_block_releases_for_date_range(
        self, room_ids: list[str], start_date: date, end_date: date
    ) -> None:
        dates_to_process = [
            start_date + timedelta(days=i) for i in range(int((end_date - start_date).days) + 1)
        ]
        logger.info(
            f"Processing block releases for room_ids: {room_ids} and dates: {dates_to_process}"
        )
        release_rows = await self.get_pending_block_release_on_dates_for_rooms(
            room_ids=room_ids, start_date=start_date, end_date=end_date
        )
        if not release_rows:
            logger.info("No block releases to process")
            return
        for room_id in room_ids:
            room_release_rows = release_rows.get(room_id, {})
            for or_date in dates_to_process:
                block_release_data_inputs = room_release_rows.get(or_date, [])
                if block_release_data_inputs:
                    await self.process_releases(releases=block_release_data_inputs)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def get_pending_block_release_on_dates_for_rooms(
        self, room_ids: list[str], start_date: date, end_date: date
    ) -> dict[str, dict[date, list[BlockReleaseDataInput]]]:
        logger.info(
            f"Getting block releases for room_ids: {room_ids} and dates: {start_date} to {end_date}"
        )
        release_rows = await self.block_release_file_store.get_pending_block_release_file_rows_by_room_ids_and_dates(
            room_ids=room_ids, start_date=start_date, end_date=end_date
        )
        return {
            room_id: {
                date: [
                    self.get_block_release_input_from_row(row)
                    for row in release_rows[room_id][date]
                ]
                for date in release_rows[room_id].keys()
            }
            for room_id in release_rows.keys()
        }

    def get_block_release_input_from_row(
        self, row_model: BlockReleaseFileRowModel
    ) -> BlockReleaseDataInput:
        return BlockReleaseDataInput(
            id=str(uuid.uuid4()),
            external_id=row_model.external_id,
            block_release_file_id=row_model.block_release_file_id,
            row_number=row_model.row_number,
            block_name=row_model.block_name,
            from_block_type=row_model.from_block_type,
            block_date=row_model.block_date,
            to_block=row_model.to_block_name,
            to_block_type=row_model.to_block_type,
            room_name=row_model.room_name,
            room_id=row_model.room_id,
            site_id=row_model.site_id,
            release_reason=row_model.release_reason,
            released_by=row_model.released_by,
            release_start_time=row_model.start_time,
            release_end_time=row_model.end_time,
            release_length=row_model.release_length,
            released_time=row_model.released_time,
            release_type=row_model.release_type,
            days_prior=row_model.days_prior,
            timezone=row_model.timezone,
            rejected_reason=None if row_model.rejected_reason == "" else row_model.rejected_reason,
        )

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def save_and_process_file(self, file: BlockFile) -> BlockReleaseProcessedFileModel:
        file_model = BlockReleaseProcessedFileModel(
            file_name=file.file_name,
            bucket_name=file.bucket_name,
            generation=file.generation,
            size=file.size,
            md5_hash=file.md5_hash,
            media_link=file.media_link,
            org_id=file.org_id,
        )
        processed_files = await self.block_release_file_store.get_processed_files(
            md5_hash=file_model.md5_hash
        )
        if len(processed_files) > 1:
            logger.warning(
                f"Multiple files with same md5Hash found. md5Hash: {file_model.md5_hash}",
                exc_info=True,
            )
            raise ClientError(
                HTTPStatus.INTERNAL_SERVER_ERROR, "Multiple files with same md5Hash found"
            )

        if len(processed_files) == 1:
            logger.info(
                f"File already processed. File name: {file_model.file_name}, md5Hash: {file_model.md5_hash}",
                exc_info=True,
            )
            file_model = processed_files[0]
        else:
            file_model = await self.block_release_file_store.save_processed_file(file_model)
        await self.process_block_release_file(file_model, BlockSlackNotifier())
        return file_model

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def save_transformed_block_release_rows(
        self,
        block_release_file_id: uuid.UUID,
        block_release_file_input: BlockReleaseBulkCreateDataInput,
    ) -> list[BlockReleaseFileRowModel]:
        return await self.block_release_file_store.save_transformed_block_release_rows(
            block_release_file_id=block_release_file_id,
            block_release_file_input=block_release_file_input,
        )

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def update_block_release_file_row_status(
        self,
        block_release_file_id: str | uuid.UUID,
        row_number: int,
        rejected_reason: str | None = None,
    ) -> None:
        return await self.block_release_file_store.update_block_release_file_row_statuses(
            block_release_file_id=block_release_file_id,
            statuses=[
                BlockReleaseFileStore.BlockReleaseFileRowStatus(
                    row_number=row_number,
                    rejected_reason=rejected_reason,
                )
            ],
        )

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def update_block_release_file_row_statuses(
        self,
        block_release_file_id: str | uuid.UUID,
        statuses: list[BlockReleaseFileStore.BlockReleaseFileRowStatus],
    ) -> None:
        return await self.block_release_file_store.update_block_release_file_row_statuses(
            block_release_file_id, statuses
        )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def get_block_release_file_rows(
        self, block_release_file_id: str | uuid.UUID
    ) -> list[BlockReleaseFileRowModel]:
        return await self.block_release_file_store.get_block_release_file_rows(
            block_release_file_id
        )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    def get_block_release_adapter(self, username: str) -> Type[BlockReleaseBaseAdapter]:
        name_to_adapter = {
            "apella": BlockReleaseApellaAdapter,
            "apella_internal_0": BlockReleaseApellaAdapter,
            "hmh": BlockReleaseHMHAdapter,
            "houston_methodist": BlockReleaseHMHAdapter,
            "hf": BlockReleaseHFAdapter,
            "health_first": BlockReleaseHFAdapter,
            "tgh": BlockReleaseTGHAdapter,
            "tampa_general": BlockReleaseTGHAdapter,
        }
        adapter = name_to_adapter.get(username, None)
        if not adapter:
            raise ClientError(
                HTTPStatus.UNPROCESSABLE_ENTITY,
                message=f"Unable to find csv adapter for: {username}",
            )
        return adapter

    @staticmethod
    def auto_select_file_adapter(file_content: Text) -> Type[BlockReleaseBaseAdapter]:
        rows = file_content.split("\r\n")
        if not rows:
            raise ClientError(
                HTTPStatus.UNPROCESSABLE_ENTITY,
                message="Invalid CSV file format",
            )
        first_row_values = rows[0].split(",")

        if (
            len(first_row_values) == len(APELLA_CSV_HEADER)
            and first_row_values == APELLA_CSV_HEADER
        ):
            return BlockReleaseApellaAdapter
        elif (
            len(first_row_values) == len(HOUSTON_METHODIST_CSV_HEADER)
            and first_row_values == HOUSTON_METHODIST_CSV_HEADER
        ):
            return BlockReleaseHMHAdapter
        elif (
            len(first_row_values) == len(HEALTH_FIRST_CSV_HEADER)
            and len(rows) > 13
            and rows[13].split(",") == HEALTH_FIRST_CSV_HEADER
        ):
            return BlockReleaseHFAdapter
        elif (
            len(first_row_values) == 49
            and len(rows) > 4
            and rows[4].split(",") == TAMPA_GENERAL_CSV_HEADER
        ):
            return BlockReleaseTGHAdapter
        else:
            raise ClientError(
                HTTPStatus.UNPROCESSABLE_ENTITY,
                message="Invalid CSV file format",
            )


def _generate_block_time_release(
    release_input: BlockReleaseDataInput,
) -> BlockTimeReleaseModel:
    return BlockTimeReleaseModel(
        id=release_input.id,
        block_time_id=release_input.block_time_id,
        reason=release_input.release_reason,
        start_time=release_input.release_start_time,
        end_time=release_input.release_end_time,
        source=release_input.released_by,
        source_type=BLOCK_RELEASE_SOURCE_TYPE,
        from_block_type=release_input.from_block_type,
        to_block_type=release_input.to_block_type,
        to_block_id=release_input.to_block_id,
        released_time=release_input.released_time,
        room_id=release_input.room_id,
    )


def _get_start_of_day_with_tz(date: datetime, timezone: str) -> datetime:
    date_obj = datetime.fromisoformat(date.isoformat())
    date_obj_tz = date_obj.astimezone(pytz.timezone(timezone))

    return datetime.combine(date_obj_tz, time.min, tzinfo=date_obj_tz.tzinfo)
