import logging
import re
from typing import Dict, Union

from api_server.services.block.block_release_processing_service import BlockReleaseProcessingService
from api_server.services.block.block_schedule_processing_service import (
    BlockScheduleProcessingService,
)

logger = logging.getLogger(__name__)


class BlockRouter:
    def __init__(
        self,
        block_release_processing_service: BlockReleaseProcessingService,
        block_schedule_processing_service: BlockScheduleProcessingService,
    ):
        self.block_release_processing_service = block_release_processing_service
        self.block_schedule_processing_service = block_schedule_processing_service

        self.routes: Dict[
            str, Union[BlockReleaseProcessingService, BlockScheduleProcessingService]
        ] = {
            r".*block_releases/TGH_OR_Block.*": block_release_processing_service,
            r".*block_releases/HM_ORBLOCKRELEASE.*": block_release_processing_service,
            r".*block_releases/TGH_OR_Room_Schedule.*": block_schedule_processing_service,
            r".*block_schedules/.*": block_schedule_processing_service,
        }

    def route(
        self,
        file_path: str,
    ) -> BlockReleaseProcessingService | BlockScheduleProcessingService | None:
        for pattern, processor in self.routes.items():
            if re.search(pattern, file_path):
                return processor
        logger.info(f"No processor found for file, {file_path}", exc_info=True)
        return None
