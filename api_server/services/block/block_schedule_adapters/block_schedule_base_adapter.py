from api_server.services.block.block_models import BlockTimeDataInput
from datetime import datetime
from typing import Optional, Text
from zoneinfo import ZoneInfo

from api_server.services.block.block_models import BlockTypes


class BlockScheduleBaseAdapter:
    @staticmethod
    async def transform(file_content: Text, file_name: Optional[str]) -> list[BlockTimeDataInput]:
        raise NotImplementedError


def get_block_type(block_type: str) -> BlockTypes:
    value_to_enum = {bl_type.value: bl_type for bl_type in BlockTypes}
    return value_to_enum.get(block_type, BlockTypes.UNKNOWN)


def convert_timestamp(time_str: str, ft: str, timezone: str) -> datetime:
    dt = datetime.strptime(time_str, ft)
    return dt.replace(tzinfo=ZoneInfo(timezone))
