import logging
from typing import Text, Optional, Union, Tuple, TypeAlias, Final
from datetime import date, datetime
from zoneinfo import ZoneInfo

from api_server.services.block.block_csv_asset_store import BlockCsvAssetStore
from api_server.services.block.block_models import BlockTimeDataInput, BlockTypes
from api_server.services.block.block_schedule_adapters.block_schedule_base_adapter import (
    BlockScheduleBaseAdapter,
    get_block_type,
    convert_timestamp,
)
from api_server.services.block.block_store import ROOM_NOT_FOUND
from api_server.services.ehr_interfaces.mapping_store import MappingStore
from api_server.services.site.site_models import SiteQuery
from api_server.services.site.site_store import SiteStore, Site

logger = logging.getLogger(__name__)

TGH_CSV_BLOCK_DATE_TIME_FORMAT = "%H:%M:%S.%f0"
TGH_CSV_SCHEDULED_DATE_TIME_FORMAT = "%Y-%m-%d %H:%M:%S"

TGH_ORG_ID = "tampa_general"
TAMPA_GENERAL_CSV_HEADER = [
    "or_room",
    "room_id",
    "slot_type",
    "start_time",
    "end_time",
    "surgeon",
    "group_name",
    "block_start_time",
    "block_end_time",
    "block_length_minutes",
]

BlockKeyWithTime: TypeAlias = Tuple[str, BlockTypes, datetime]
BlockKeyWithName: TypeAlias = Tuple[str, date, str]

TGH_SPECIAL_BLOCK_TYPES: Final[set[BlockTypes]] = {
    BlockTypes.UNBLOCKED_BLOCK_TYPE,
    BlockTypes.UNAVAILABLE_BLOCK_TYPE,
    BlockTypes.ON_HOLD_BLOCK_TYPE,
}

BLOCK_TYPES_TO_IGNORE: Final[set[BlockTypes]] = {BlockTypes.SERVICE_BLOCK_TYPE}


class BlockScheduleTGHAdapter(BlockScheduleBaseAdapter):
    @staticmethod
    async def transform(
        file_content: Text, file_name: Optional[str] = None
    ) -> list[BlockTimeDataInput]:
        logger.info(f"Transforming TGH block schedule for {file_name}")
        sites: list[Site] = await SiteStore().query_sites(SiteQuery(organization_id=TGH_ORG_ID))
        timezone = sites[0].timezone

        csv_content = BlockCsvAssetStore.get_csv_content(file_content=file_content, delimiter="|")
        room_names = set(
            row[TAMPA_GENERAL_CSV_HEADER[0]]
            for row in csv_content
            if row[TAMPA_GENERAL_CSV_HEADER[0]]
        )
        room_info = await MappingStore().populate_room_info_mapping(TGH_ORG_ID, room_names, sites)

        # Use a dictionary to track unique blocks
        unique_block_times: dict[Union[BlockKeyWithTime, BlockKeyWithName], BlockTimeDataInput] = {}

        for i, row in enumerate(csv_content):
            # create a unique key for the block based on room, date, and block name
            block_date = convert_timestamp(
                row[TAMPA_GENERAL_CSV_HEADER[3]], TGH_CSV_SCHEDULED_DATE_TIME_FORMAT, timezone
            ).date()
            block_type = get_block_type(row[TAMPA_GENERAL_CSV_HEADER[2]].lower().title())
            block_name = row[TAMPA_GENERAL_CSV_HEADER[5]] or row[TAMPA_GENERAL_CSV_HEADER[6]]

            # for Unavailable, Unblocked and On Hold Block types, block_name would be None
            # so for these types, we would use start_time to differentiate between multiple blocks of the same type
            if block_type in TGH_SPECIAL_BLOCK_TYPES:
                start_time = convert_timestamp(
                    row[TAMPA_GENERAL_CSV_HEADER[3]], TGH_CSV_SCHEDULED_DATE_TIME_FORMAT, timezone
                )
                block_key: Union[BlockKeyWithTime, BlockKeyWithName] = (
                    row[TAMPA_GENERAL_CSV_HEADER[0]],
                    block_type,
                    start_time,
                )
            elif block_type not in BLOCK_TYPES_TO_IGNORE:
                block_key = (
                    row[TAMPA_GENERAL_CSV_HEADER[0]],
                    block_date,
                    block_name,
                )
            else:
                continue

            # If we've already processed this block time, skip it
            if block_key in unique_block_times:
                continue

            block_time_input = BlockTimeDataInput(
                room_name=row[TAMPA_GENERAL_CSV_HEADER[0]],
                timezone=timezone,
                org_id=TGH_ORG_ID,
                block_name=block_name,
                block_type=block_type,
                block_date=block_date,
                row_number=i,
            )
            room = room_info.get(block_time_input.room_name) if room_info else None

            try:
                if room:
                    block_time_input.site_id = room.site_id
                    block_time_input.room_id = room.room_id
                else:
                    block_time_input.rejected_reason = ROOM_NOT_FOUND

                # Handle block times based on two scenarios:
                # Scenario 1: When it's a valid block and block_start_time and block_end_time
                # are present (scheduled cases)
                if (
                    block_type not in TGH_SPECIAL_BLOCK_TYPES
                    and row[TAMPA_GENERAL_CSV_HEADER[7]]
                    and row[TAMPA_GENERAL_CSV_HEADER[8]]
                ):
                    block_start_time = datetime.strptime(
                        row[TAMPA_GENERAL_CSV_HEADER[7]], TGH_CSV_BLOCK_DATE_TIME_FORMAT
                    ).time()
                    block_end_time = datetime.strptime(
                        row[TAMPA_GENERAL_CSV_HEADER[8]], TGH_CSV_BLOCK_DATE_TIME_FORMAT
                    ).time()

                    block_time_input.block_start_time = datetime.combine(
                        block_date, block_start_time
                    ).replace(tzinfo=ZoneInfo(timezone))
                    block_time_input.block_end_time = datetime.combine(
                        block_date, block_end_time
                    ).replace(tzinfo=ZoneInfo(timezone))

                # Scenario 2: When it's a special block type or only start_time and end_time are present (future blocks)
                else:
                    block_time_input.block_start_time = convert_timestamp(
                        row[TAMPA_GENERAL_CSV_HEADER[3]],
                        TGH_CSV_SCHEDULED_DATE_TIME_FORMAT,
                        timezone,
                    )
                    block_time_input.block_end_time = convert_timestamp(
                        row[TAMPA_GENERAL_CSV_HEADER[4]],
                        TGH_CSV_SCHEDULED_DATE_TIME_FORMAT,
                        timezone,
                    )

                unique_block_times[block_key] = block_time_input

            except Exception as e:
                block_time_input.rejected_reason = f"Unexpected error: {str(e)}"
                unique_block_times[block_key] = block_time_input

        return list(unique_block_times.values())
