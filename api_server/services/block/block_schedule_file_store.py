import logging
import uuid

from sqlalchemy import (
    select,
)

from typing import Optional, Sequence, Iterator

from api_server.services.block.block_models import (
    BlockScheduleProcessedFileModel,
    BlockTimeDataInput,
    BlockScheduleFileRowModel,
    BlockProcessingStatus,
)
from databases.sql import new_async_session
from databases.sql.helpers import async_postgres_insert_on_conflict_do_update_helper

logger = logging.getLogger(__name__)

BATCH_SIZE = 2000


def get_batches(
    seq: Sequence[BlockTimeDataInput], batch_size: int
) -> Iterator[Sequence[BlockTimeDataInput]]:
    total = len(seq)
    for start in range(0, total, batch_size):
        end = min(start + batch_size, total)
        yield seq[start:end]


class BlockScheduleFileStore:
    async def get_processed_files(
        self,
        md5_hash: Optional[str] = None,
    ) -> Sequence[BlockScheduleProcessedFileModel]:
        if not md5_hash:
            return []
        async with new_async_session() as session:
            statement = select(BlockScheduleProcessedFileModel).filter(
                BlockScheduleProcessedFileModel.md5_hash == md5_hash
            )
            return (await session.scalars(statement)).all()

    async def save_processed_file(
        self, file: BlockScheduleProcessedFileModel
    ) -> BlockScheduleProcessedFileModel:
        async with new_async_session() as session:
            session.add(file)
            await session.commit()
            await session.refresh(file)
            return file

    async def save_transformed_block_schedule_rows(
        self,
        block_schedule_file_id: uuid.UUID,
        block_times: Sequence[BlockTimeDataInput],
    ) -> None:
        async with new_async_session() as session:
            for batch in get_batches(block_times, BATCH_SIZE):
                block_schedule_file_rows = [
                    BlockScheduleFileRowModel(
                        org_id=bt.org_id,
                        block_schedule_file_id=block_schedule_file_id,
                        site_id=bt.site_id,
                        room_id=bt.room_id,
                        room_name=bt.room_name,
                        timezone=bt.timezone,
                        row_number=bt.row_number,
                        block_start_time=bt.block_start_time,
                        block_end_time=bt.block_end_time,
                        block_name=bt.block_name,
                        block_date=bt.block_date,
                        block_type=bt.block_type,
                        rejected_reason=bt.rejected_reason,
                        processing_status=BlockProcessingStatus.PENDING
                        if bt.rejected_reason is None
                        else BlockProcessingStatus.REJECTED,
                    )
                    for bt in batch
                ]
                logger.info(f"Saving batch of {len(block_schedule_file_rows)} block times")
                await async_postgres_insert_on_conflict_do_update_helper(
                    session=session,
                    subject=BlockScheduleFileRowModel,
                    values=block_schedule_file_rows,
                    returning=BlockScheduleFileRowModel.row_number,
                    index_elements=[
                        BlockScheduleFileRowModel.block_schedule_file_id,
                        BlockScheduleFileRowModel.row_number,
                    ],
                )
            await session.commit()
