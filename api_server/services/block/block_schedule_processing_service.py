from typing import Type, Sequence
import uuid
from dataclasses import dataclass
import logging

from apella_cloud_api.exceptions import ClientError
from api_server.logging.audit import log_calls_to_audit
from api_server.services.block.block_models import (
    BlockFile,
    BlockScheduleProcessedFileModel,
    BlockTimeDataInput,
)
from api_server.services.block.block_csv_asset_store import BlockCsvAssetStore
from api_server.services.block.block_schedule_adapters.block_schedule_base_adapter import (
    BlockScheduleBaseAdapter,
)
from api_server.services.block.block_schedule_adapters.tgh_adapter import BlockScheduleTGHAdapter
from api_server.services.block.block_schedule_file_store import BlockScheduleFileStore
from http import HTTPStatus

from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import READ_ANY_BLOCK, WRITE_ANY_BLOCK

logger = logging.getLogger(__name__)


@dataclass
class BlockScheduleProcessingService:
    auth: Auth
    block_schedule_file_store: BlockScheduleFileStore
    block_csv_asset_store: BlockCsvAssetStore

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    def get_block_schedule_adapter(self, username: str) -> Type[BlockScheduleBaseAdapter]:
        logger.info(f"Getting block schedule adapter for: {username}")
        name_to_adapter = {
            "tampa_general": BlockScheduleTGHAdapter,
        }
        adapter = name_to_adapter.get(username, None)
        if not adapter:
            raise ClientError(
                HTTPStatus.UNPROCESSABLE_ENTITY,
                message=f"Unable to find csv adapter for: {username}",
            )
        return adapter

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def transform_and_save_release_file(
        self, file: BlockScheduleProcessedFileModel
    ) -> list[BlockTimeDataInput]:
        file_content = self.block_csv_asset_store.download_csv_file_as_text(file.media_link)
        adapter = self.get_block_schedule_adapter(file.org_id)

        logger.info(f"Transforming block schedule using adapter")

        block_times = await adapter.transform(file_content, file.file_name)

        logger.info(f"Saving transformed block times for file: {file}")

        await self.save_transformed_block_schedule_rows(
            block_schedule_file_id=file.id,
            block_times=block_times,
        )

        return block_times

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def save_transformed_block_schedule_rows(
        self,
        block_schedule_file_id: uuid.UUID,
        block_times: Sequence[BlockTimeDataInput],
    ) -> None:
        return await self.block_schedule_file_store.save_transformed_block_schedule_rows(
            block_schedule_file_id=block_schedule_file_id,
            block_times=block_times,
        )

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def save_and_process_file(
        self, file: BlockFile
    ) -> BlockScheduleProcessedFileModel | None:
        logger.info(f"Saving and processing file: {file}")
        file_model = BlockScheduleProcessedFileModel(
            file_name=file.file_name,
            bucket_name=file.bucket_name,
            generation=file.generation,
            size=file.size,
            md5_hash=file.md5_hash,
            media_link=file.media_link,
            org_id=file.org_id,
        )
        try:
            processed_files = await self.block_schedule_file_store.get_processed_files(
                md5_hash=file_model.md5_hash
            )
            if len(processed_files) > 1:
                logger.warning(
                    f"Multiple files with same md5Hash found. md5Hash: {file_model.md5_hash}",
                    exc_info=True,
                )
                raise ClientError(
                    HTTPStatus.INTERNAL_SERVER_ERROR, "Multiple files with same md5Hash found"
                )

            if len(processed_files) == 1:
                logger.info(
                    f"File already processed. File name: {file_model.file_name}, md5Hash: {file_model.md5_hash}",
                    exc_info=True,
                )
                file_model = processed_files[0]
            else:
                file_model = await self.block_schedule_file_store.save_processed_file(file_model)
                logger.info(
                    f"Saved block schedule report file successfully, file name: {file_model.file_name}"
                )
            await self.transform_and_save_release_file(file_model)
            return file_model

        except Exception as e:
            logger.error(
                f"Failed to save block schedule file, {file_model.file_name}: {e}", exc_info=True
            )
            return None
