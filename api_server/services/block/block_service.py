import logging
from dataclasses import dataclass
from datetime import datetime, timed<PERSON><PERSON>
from http import H<PERSON><PERSON>tatus
from typing import DefaultDict, Iterable, List, Optional
import uuid

from graphql import GraphQLError
import sqlalchemy


from apella_cloud_api.dtos import BlockQueryDto, BlockTimeQueryDto
from api_server.logging.audit import log_calls_to_audit
from api_server.services.block.block_models import (
    BlockDataInput,
    BlockReleaseDataInput,
    BlockTypes,
)
from api_server.services.block.block_store import (
    BlockModel,
    BlockStore,
    BlockTimeModel,
    BlockTimeInterval,
    BlockTimeReleaseModel,
)
from api_server.tracing.trace_decorator import class_traced
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import READ_ANY_BLOCK, WRITE_ANY_BLOCK
from apella_cloud_api.exceptions import ClientError

logger = logging.getLogger(__name__)


@dataclass
@class_traced()
class BlockService:
    auth: Auth
    block_store: BlockStore

    @staticmethod
    def validate_block_query_dto(query: BlockQueryDto) -> None:
        """
        Validates the base BlockQuery dto
        """
        if query.min_end_time and query.max_start_time:
            if query.min_end_time >= query.max_start_time:
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    message="'max_start_time' must be after 'min_end_time'",
                )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def get_block(self, id: str) -> BlockModel:
        return await self.block_store.get_block(id=id)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def get_blocks(self, ids: Iterable[str]) -> list[BlockModel]:
        return await self.block_store.get_blocks(ids=ids)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def get_block_times_for_block(self, block_id: uuid.UUID) -> list[BlockTimeModel]:
        return await self.block_store.get_block_times_for_block(block_id=block_id)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def get_block_times_for_blocks(
        self, block_ids: Iterable[str]
    ) -> DefaultDict[str, List[BlockTimeModel]]:
        return await self.block_store.get_block_times_for_blocks(block_ids=block_ids)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def get_block_times(self, ids: Iterable[str]) -> list[BlockTimeModel]:
        return await self.block_store.get_block_times(ids=ids)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def get_block_time(self, id: str) -> BlockTimeModel:
        return await self.block_store.get_block_time(id=id)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def get_block_time_releases_for_block_time(
        self, block_time_id: str, active_only: bool
    ) -> list[BlockTimeReleaseModel]:
        return await self.block_store.get_block_time_releases_for_block_time(
            block_time_id=block_time_id, active_only=active_only
        )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def get_block_time_releases_for_block_times(
        self, block_time_ids: Iterable[str], active_only: bool
    ) -> DefaultDict[str, List[BlockTimeReleaseModel]]:
        return await self.block_store.get_block_time_releases_for_block_times(
            block_time_ids=block_time_ids,
            active_only=active_only,
        )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def get_block_time_release_for_special_block(
        self,
        from_block_type: BlockTypes,
        room_id: str,
        start_time: datetime,
        end_time: datetime,
        to_block_type: BlockTypes | None = None,
    ) -> BlockTimeReleaseModel | None:
        return await self.block_store.get_block_time_release_for_special_block(
            from_block_type=from_block_type,
            room_id=room_id,
            start_time=start_time,
            end_time=end_time,
            to_block_type=to_block_type,
        )

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def create_block(self, block: BlockDataInput) -> BlockModel:
        return await self.block_store.create_block(block=block)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def update_block(self, block: BlockDataInput) -> BlockModel:
        return await self.block_store.update_block(block=block)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def archive_block(self, id: str) -> BlockModel:
        return await self.block_store.archive_block(id=id)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def unarchive_block(self, id: str) -> BlockModel:
        return await self.block_store.unarchive_block(id=id)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def query_blocks(
        self,
        query: BlockQueryDto,
        eager_load_block_times: bool | None = None,
        include_empty_blocks: bool | None = None,
    ) -> list[BlockModel]:
        self.validate_block_query_dto(query)
        return await self.block_store.query_blocks(
            dto=query,
            eager_load_block_times=eager_load_block_times,
            include_empty_blocks=include_empty_blocks,
        )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def find_block_by_name_and_site(self, name: str, site_id: str) -> BlockModel | None:
        if not name:
            raise ClientError(
                HTTPStatus.BAD_REQUEST,
                message="Block name is required",
            )

        try:
            return await self.block_store.find_block_by_name_and_site(name=name, site_id=site_id)
        except sqlalchemy.exc.MultipleResultsFound:
            raise ClientError(
                HTTPStatus.BAD_REQUEST,
                message=f"Multiple blocks found with name {name} for site {site_id}",
            )
        except Exception as e:
            logger.error(
                f"Error finding block by name and site: {name} - {site_id} - %s",
                e,
                exc_info=True,
            )
            raise ClientError(
                HTTPStatus.INTERNAL_SERVER_ERROR,
                message="An error occurred while finding the block",
            ) from e

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def query_block_times(
        self,
        room_ids: Optional[list[str]],
        min_end_time: datetime,
        max_start_time: datetime,
        site_id: Optional[str] = None,
        block_id: Optional[str] = None,
    ) -> list[BlockTimeModel]:
        return await self.block_store.query_block_times(
            site_id=site_id,
            room_ids=room_ids,
            min_end_time=min_end_time,
            max_start_time=max_start_time,
            block_id=block_id,
        )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def query_room_block_times(
        self,
        query: BlockTimeQueryDto,
    ) -> list[BlockTimeModel]:
        if query.room_ids is not None:
            room_ids = query.room_ids
        elif query.room_id is not None:
            room_ids = [query.room_id]
        else:
            room_ids = None

        return await self.block_store.query_block_times(
            site_id=query.site_id,
            room_ids=room_ids,
            min_end_time=query.min_end_time,
            max_start_time=query.max_start_time,
        )

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def create_block_time(self, block_time: BlockTimeModel) -> BlockTimeModel:
        return await self.block_store.create_block_time(block_time=block_time)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def bulk_add_block_times(self, block_times: list[BlockTimeModel]) -> list[str]:
        return await self.block_store.bulk_add_block_times(block_times=block_times)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def get_block_time_available_intervals(
        self, block_time: BlockTimeModel
    ) -> list[BlockTimeInterval]:
        return await self.block_store.get_block_time_available_intervals(block_time)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def query_block_times_available_intervals(
        self,
        site_id: str,
        min_end_time: datetime,
        max_start_time: datetime,
        release_cutoff: Optional[timedelta] = None,
        block_id: Optional[str] = None,
    ) -> list[BlockTimeInterval]:
        if min_end_time >= max_start_time:
            raise ValueError(
                f"Invalid range: min_end_time {min_end_time} needs to be before max_start_time {max_start_time}"
            )
        return await self.block_store.query_block_times_available_intervals(
            site_id=site_id,
            min_end_time=min_end_time,
            max_start_time=max_start_time,
            release_cutoff=release_cutoff,
            block_id=block_id,
        )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def get_staff_block_ids(self, staff_id: str) -> List[str]:
        return await self.block_store.get_staff_block_ids(staff_id=staff_id)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def get_many_staff_block_ids_by_staff_id(
        self, staff_ids: list[str]
    ) -> dict[str, set[str]]:
        return await self.block_store.get_many_staff_block_ids_by_staff_id(staff_ids)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def create_block_time_release(
        self, release: BlockTimeReleaseModel
    ) -> BlockTimeReleaseModel:
        return await self.block_store.create_block_time_release(release=release)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def update_block_time_release(
        self,
        release: BlockReleaseDataInput | BlockTimeReleaseModel,
        existing_release_id: uuid.UUID,
    ) -> BlockTimeReleaseModel | None:
        return await self.block_store.update_block_time_release(
            release=release, existing_release_id=existing_release_id
        )

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def bulk_add_block_releases(
        self, block_releases: list[BlockReleaseDataInput]
    ) -> dict[str, BlockReleaseDataInput]:
        return await self.block_store.bulk_add_block_releases(block_releases=block_releases)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def find_duplicate_block_times(
        self, block_times: list[BlockTimeModel]
    ) -> list[BlockTimeModel]:
        duplicates = []
        if len(block_times) <= 1:
            return []

        for i in range(len(block_times) - 1):
            current = block_times[i]
            next_bt = block_times[i + 1]

            if (
                str(current.block_id) == str(next_bt.block_id)
                and current.room_id == next_bt.room_id
                and current.start_time == next_bt.start_time
                and current.end_time == next_bt.end_time
                and current.released_from is None
                and next_bt.released_from is None
            ):
                duplicates.append(next_bt)

        return duplicates

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def delete_duplicate_block_times(self, org_id: str) -> int:
        blocks = await self.query_blocks(
            query=BlockQueryDto(org_ids=[org_id], include_archived=False)
        )

        if not blocks:
            return 0

        block_ids = [str(block.id) for block in blocks]
        block_times_dict = await self.get_block_times_for_blocks(block_ids=block_ids)

        all_block_times = []
        for block_id, block_times in block_times_dict.items():
            all_block_times.extend(block_times)

        all_block_times.sort(
            key=lambda bt: (str(bt.block_id), bt.room_id, bt.start_time, bt.end_time)
        )
        duplicates = await self.find_duplicate_block_times(all_block_times)

        if not duplicates:
            return 0

        duplicate_ids = [str(duplicate.id) for duplicate in duplicates]
        await self.block_store.delete_block_times(duplicate_ids)

        return len(duplicate_ids)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_BLOCK)
    async def filter_overlapping_block_times(
        self, block_times: list[BlockTimeModel]
    ) -> list[BlockTimeModel] | None:
        if not block_times:
            return None

        if not _validate_block_time_range(block_times):
            logger.warning(f"Block times have invalid time range. Block Times: {block_times}")
            raise ClientError(
                HTTPStatus.BAD_REQUEST,
                message="The start time must be strictly less than the end time.",
            )

        # check the new block time intervals doesn't overlap with themselves
        for block_time in block_times:
            if _check_overlapping_block_times_given_interval(block_times, block_time):
                logger.warning(
                    f"Block times are overlapping. Block Times: {block_times}, block time: {block_time}"
                )
                raise GraphQLError(
                    "Block time release start and end times are overlapping with existing block times."
                )

        # check the new block time intervals doesn't overlap with existing block time intervals

        # based on the list of new block times, get the existing block times based on room ids and dates
        existing_block_times = await self.query_room_block_times(
            BlockTimeQueryDto(
                min_end_time=min(bt.start_time for bt in block_times),
                max_start_time=max(bt.end_time for bt in block_times),
                room_ids=list(set(([block.room_id for block in block_times]))),
            )
        )

        # for the existing block times, calc the active block time intervals and check if they overlap
        reserved_block_intervals: list[BlockTimeInterval] = [
            interval
            for bt in existing_block_times
            for interval in await self.get_block_time_available_intervals(bt)
        ]

        conflicting_block_times = []

        for interval in reserved_block_intervals:
            block_times_to_ignore = _check_overlapping_block_times_given_interval(
                block_times, interval
            )
            if len(block_times_to_ignore) > 0:
                logger.warning(
                    f"Block times are overlapping. Block Times: {block_times}, interval: {interval}"
                )
                conflicting_block_times.extend(block_times_to_ignore)

        return [bt for bt in block_times if bt not in conflicting_block_times]


def _check_overlapping_block_times_given_interval(
    block_times: list[BlockTimeModel],
    given_interval: BlockTimeModel | BlockTimeInterval,
) -> list[BlockTimeModel]:
    overlapping_time_intervals = [
        bt
        for bt in block_times
        if bt.room_id == given_interval.room_id
        and not (
            given_interval.end_time <= bt.start_time or bt.end_time <= given_interval.start_time
        )
        and (not isinstance(given_interval, BlockTimeModel) or bt != given_interval)
    ]

    return overlapping_time_intervals


def _validate_block_time_range(block_times: list[BlockTimeModel]) -> bool:
    return all(t.start_time < t.end_time for t in block_times)
