from collections import OrderedDict, defaultdict
from http import HTTPStatus
import aiohttp
from datetime import datetime
from typing import Dict, List, Optional

from api_server.services.block.block_store import (
    ORDERED_RELEASE_PROCESSING_ERRORS,
    BLOCK_NOT_FOUND,
    RELEASE_BULK_IMPORT_BLOCK_TIME_NOT_FOUND,
    RELEASE_BULK_IMPORT_RELEASE_ALREADY_EXISTS,
    RELEASE_BULK_IMPORT_RELEASE_CONFLICTS,
    ROOM_NOT_FOUND,
)
from config import environment_name, oracle_block_release_slack_notification_url
from api_server.services.block.block_models import BlockReleaseDataInput
from api_server.logging import warning


# Convert bytes to human friendly format (MB, KB, etc.)
def convert_file_size(size: int) -> str:
    if size > 1024 * 1024:
        return f"{size / (1024 * 1024):.2f} MB"
    elif size > 1024:
        return f"{size / 1024:.2f} KB"
    return f"{size} bytes"


def build_markdown_section(text: str) -> object:
    return {
        "type": "section",
        "text": {
            "type": "mrkdwn",
            "text": text,
        },
    }


def build_rich_text_section(text: str) -> object:
    return {
        "type": "rich_text_section",
        "elements": [
            {
                "type": "text",
                "text": text,
            },
        ],
    }


def build_rich_text_list(indent: int, elements: list[object]) -> object:
    return {
        "type": "rich_text_list",
        "style": "bullet",
        "indent": indent,
        "border": 0,
        "elements": elements,
    }


def build_site_to_releases_list(
    releases: list[BlockReleaseDataInput],
) -> Dict[str, List[BlockReleaseDataInput]]:
    site_to_releases: Dict[str, List[BlockReleaseDataInput]] = defaultdict(list)

    for release in releases:
        if release.site_id is not None:
            site_to_releases[release.site_id].append(release)

    site_to_releases = OrderedDict(sorted(site_to_releases.items()))

    return site_to_releases


def build_success_breakdown_list(
    releases: list[BlockReleaseDataInput],
) -> list[object]:
    total_processed = 0
    elements = []

    site_to_releases = build_site_to_releases_list(releases)

    for site_id, releases in site_to_releases.items():
        site_label_processed = len(releases)

        if site_label_processed > 0:
            elements.append(
                build_rich_text_section(f"{site_id}: {site_label_processed}"),
            )
            total_processed += site_label_processed

    processed_list = []

    if total_processed > 0:
        processed_list.append(
            build_rich_text_list(
                indent=0,
                elements=[build_rich_text_section(f"Processed successfully: {total_processed}")],
            )
        )

    if len(elements) > 0:
        processed_list.append(build_rich_text_list(indent=1, elements=elements))

    return processed_list


def build_error_breakdown_list(
    releases: list[BlockReleaseDataInput],
) -> list[object]:
    error_list = []
    site_to_releases = build_site_to_releases_list(releases)

    if len(releases) > 0:
        error_list.append(
            build_rich_text_list(
                indent=0, elements=[build_rich_text_section(f"Failed: {len(releases)}")]
            ),
        )

    for site_id, site_releases in site_to_releases.items():
        site_label_rejected = len(site_releases)
        errors_by_type_for_site = {}
        error_rows_for_site = []

        if site_label_rejected > 0:
            error_list.append(
                build_rich_text_list(
                    indent=1,
                    elements=[
                        build_rich_text_section(f"{site_id}: {site_label_rejected}"),
                    ],
                )
            )
            for error_type in [
                err_type
                for err_type in [
                    BLOCK_NOT_FOUND,
                    RELEASE_BULK_IMPORT_BLOCK_TIME_NOT_FOUND,
                    RELEASE_BULK_IMPORT_RELEASE_ALREADY_EXISTS,
                    RELEASE_BULK_IMPORT_RELEASE_CONFLICTS,
                ]
            ]:
                num_errors = len(
                    [release for release in site_releases if release.rejected_reason == error_type]
                )
                if num_errors > 0:
                    errors_by_type_for_site[error_type] = num_errors

            for err_id, count in errors_by_type_for_site.items():
                error_rows_for_site.append(
                    build_rich_text_section(f"{err_id}: {count}"),
                )
            other_errors = len(
                [
                    release
                    for release in site_releases
                    if release.rejected_reason not in ORDERED_RELEASE_PROCESSING_ERRORS
                ]
            )
            if other_errors > 0:
                error_rows_for_site.append(
                    build_rich_text_section(f"Other Errors: {other_errors}"),
                )
            if len(error_rows_for_site) > 0:
                error_list.append(build_rich_text_list(indent=2, elements=error_rows_for_site))

    room_not_found_releases = [
        release for release in releases if release.rejected_reason == ROOM_NOT_FOUND
    ]
    num_room_not_found_errors = len(room_not_found_releases)

    if num_room_not_found_errors > 0:
        unknown_room_elements = []
        unknown_rooms = []

        error_list.append(
            build_rich_text_list(
                indent=1,
                elements=[
                    build_rich_text_section(f"Room not found: {num_room_not_found_errors}"),
                ],
            )
        )

        for release in room_not_found_releases:
            if release.room_name and release.room_name not in unknown_rooms:
                unknown_rooms.append(release.room_name)
                unknown_room_elements.append(
                    build_rich_text_section(f"{release.room_name}"),
                )

        if len(unknown_room_elements) > 0:
            error_list.append(build_rich_text_list(indent=2, elements=unknown_room_elements))

    return error_list


class BlockKitGenerator:
    """
    E.g.:
    Total Records in the File: 104

    Processed successfully: 10
        HMH-HMBT-OPC18: 10

    Failed: 94
        HMH-OPC18: 11
            Releasing to another block is not supported: 1
            Block time not found: 10

        HMH-HMBT-ASC: 1
            Block not found: 1

        HMH-HMBT-OR: 10
            Block not found: 10

        HMH-HMCL-ASC: 1
            Room not found: 1

        HMH-HMCL-OR: 4
            Block not found: 4
    """

    @staticmethod
    def generate_block_release_breakdown_list(
        releases: list[BlockReleaseDataInput],
    ) -> list[object]:
        num_of_rows = len(releases)

        if num_of_rows > 0:
            processed_list = build_success_breakdown_list(
                [release for release in releases if release.rejected_reason is None]
            )
            error_list = build_error_breakdown_list(
                [release for release in releases if release.rejected_reason is not None]
            )

            breakdown_list: list[object] = [
                build_rich_text_section(f"Total Records in the File: {num_of_rows}"),
            ]

            if len(processed_list) > 0 or len(error_list) > 0:
                breakdown_list.extend(processed_list + error_list)

            return breakdown_list
        return []

    @staticmethod
    def generate_block_release_blocks(
        releases: list[BlockReleaseDataInput],
        time_uploaded: Optional[datetime] = None,
        uploader: Optional[str] = None,
        media_link: Optional[str] = None,
        output_media_link: Optional[str] = None,
    ) -> list[object]:
        # Generate slack block kit payload
        blocks = [
            build_markdown_section(
                f"[{environment_name()}] Block releases uploaded by {uploader or 'SFTP'} at {(time_uploaded or datetime.now()).strftime('%Y-%m-%d %H:%M:%S')}"
            ),
            {"type": "divider"},
        ]

        breakdown_list: list[object] = BlockKitGenerator().generate_block_release_breakdown_list(
            releases
        )

        if len(breakdown_list) > 0:
            blocks.append(
                {
                    "type": "rich_text",
                    "elements": breakdown_list,
                },
            )

        if media_link is not None or output_media_link is not None:
            blocks.append({"type": "divider"})

        if media_link is not None:
            blocks.append(build_markdown_section(f"*<{media_link}|Original File>*"))
        if output_media_link is not None:
            blocks.append(build_markdown_section(f"*<{output_media_link}|Processed Log File>*"))

        return blocks


class BlockSlackNotifier:
    def __init__(self) -> None:
        # Fetch the Slack webhook URL from environment variable
        self.webhook_url = oracle_block_release_slack_notification_url()

    async def log_error(self, error: str) -> None:
        if not self.webhook_url:
            return

        blocks = [
            build_markdown_section(
                f"[{environment_name()}] Error occurred at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            ),
            {"type": "divider"},
            build_markdown_section(f"*Error:* {error}"),
        ]

        async with aiohttp.ClientSession() as session:
            async with session.post(
                url=self.webhook_url,
                json={"blocks": blocks},
            ) as response:
                response.raise_for_status()

                if response.status != HTTPStatus.OK:
                    warning(f"Error sending message to Slack: {response.text() or response.status}")

    async def log_warning(self, msg: str) -> None:
        if not self.webhook_url:
            return

        blocks = [
            build_markdown_section(f"*Warning:* {msg}"),
        ]

        async with aiohttp.ClientSession() as session:
            async with session.post(
                url=self.webhook_url,
                json={"blocks": blocks},
            ) as response:
                response.raise_for_status()

                if response.status != HTTPStatus.OK:
                    warning(f"Error sending message to Slack: {response.text() or response.status}")

    async def notify_file_upload(
        self,
        username: str,
        file_size: int,
        file_name: Optional[str] = None,
    ) -> None:
        if not self.webhook_url:
            return

        blocks = [
            build_markdown_section(
                f"[{environment_name()}] Block releases uploaded by {username} at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            ),
            {"type": "divider"},
            build_markdown_section(
                f"*File Name:* {file_name or 'Dashboard Upload'} | {convert_file_size(file_size)}"
            ),
        ]

        async with aiohttp.ClientSession() as session:
            async with session.post(
                url=self.webhook_url,
                json={"blocks": blocks},
            ) as response:
                response.raise_for_status()

                if response.status != HTTPStatus.OK:
                    warning(f"Error sending message to Slack: {response.text() or response.status}")

    async def report(
        self,
        releases: list[BlockReleaseDataInput],
        time_uploaded: Optional[datetime] = None,
        uploader: Optional[str] = None,
        media_link: Optional[str] = None,
        output_media_link: Optional[str] = None,
    ) -> None:
        if not self.webhook_url:
            return

        blocks = BlockKitGenerator.generate_block_release_blocks(
            releases=releases,
            time_uploaded=time_uploaded,
            uploader=uploader,
            media_link=media_link,
            output_media_link=output_media_link,
        )

        async with aiohttp.ClientSession() as session:
            async with session.post(
                url=self.webhook_url,
                json={"blocks": blocks},
            ) as response:
                response.raise_for_status()

                if response.status != HTTPStatus.OK:
                    warning(f"Error sending message to Slack: {response.text() or response.status}")
