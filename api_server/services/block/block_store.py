from collections import defaultdict
from datetime import datetime, time, timedelta
from http import HTT<PERSON>tatus
from typing import DefaultDict, Final, Iterable, List, Optional

import re
import uuid
from zoneinfo import ZoneInfo

from api_server.services.utils.sql_utils import idempotent_join, update_related_entities
import sqlalchemy
import sqlalchemy.exc
from asyncpg import CheckViolationError, ExclusionViolationError, Range, DataError
from sqlalchemy.orm import joinedload
from sqlalchemy.sql import func
from api_server.services.room.room_store import RoomStore
from dateutil.parser import parse
from apella_cloud_api.dtos import BlockQueryDto
from apella_cloud_api.exceptions import ClientError, NotFound
from api_server.services.staff.staff_store import StaffModel, StaffStore
from sqlalchemy.ext.asyncio import AsyncSession
from .block_models import (
    BlockTypes,
    block_surgeons,
    BlockModel,
    BlockDataInput,
    BlockTimeModel,
    BlockTimeInterval,
    BlockTimeReleaseModel,
    BlockReleaseDataInput,
    TimeInterval,
)
from databases.sql import new_async_session
from databases.sql.helpers import (
    async_postgres_insert_on_conflict_do_update_helper,
    check_exception,
    get_exception_message,
)
from sqlalchemy import (
    select,
)

from api_server.logging import error, warning
from api_server.services.site.site_models import SiteQuery
from api_server.services.site.site_store import SiteStore, Site
from api_server.tracing.trace_decorator import class_traced

BLOCK_RELEASE_SOURCE_TYPE: Final[str] = "csv_upload"

ROOM_NOT_FOUND: Final[str] = "Room not found"
BLOCK_NOT_FOUND: Final[str] = "Block not found"

RELEASE_BULK_IMPORT_BLOCK_TIME_NOT_FOUND: Final[str] = "Block time not found"
RELEASE_BULK_IMPORT_RELEASE_ALREADY_EXISTS: Final[str] = "Release already exists"
RELEASE_BULK_IMPORT_RELEASE_CONFLICTS: Final[str] = "Release time conflicts with another release"
RELEASE_BULK_IMPORT_RELEASE_INVALID_TIMES: Final[str] = "Invalid release times"

ORDERED_RELEASE_PROCESSING_ERRORS: Final[list[str]] = [
    ROOM_NOT_FOUND,
    BLOCK_NOT_FOUND,
    RELEASE_BULK_IMPORT_BLOCK_TIME_NOT_FOUND,
    RELEASE_BULK_IMPORT_RELEASE_ALREADY_EXISTS,
    RELEASE_BULK_IMPORT_RELEASE_CONFLICTS,
    RELEASE_BULK_IMPORT_RELEASE_INVALID_TIMES,
]

ORDERED_BLOCK_SCHEDULE_PROCESSING_ERRORS: Final[list[str]] = [
    ROOM_NOT_FOUND,
    BLOCK_NOT_FOUND,
]

UNBLOCKED_PREFIX: Final[str] = "unblocked"
UNBLOCKED_DELIMITER: Final[str] = "|"
THE_UNBLOCKED_BLOCK = BlockModel(
    id=UNBLOCKED_PREFIX,
    name="Unblocked",
    color="#F2F2F2",
    org_id="",
)


@class_traced()
class BlockStore:
    async def __get_block(
        self, id: str, session: AsyncSession, eager_load_block_times: Optional[bool] = False
    ) -> BlockModel:
        if UNBLOCKED_PREFIX in id:
            return THE_UNBLOCKED_BLOCK
        query = select(BlockModel)
        if eager_load_block_times:
            query = query.options(joinedload(BlockModel.block_times, innerjoin=True))
        query = query.filter(BlockModel.id == id)
        return (await session.scalars(query)).unique().one()

    async def __get_blocks(self, ids: Iterable[str], session: AsyncSession) -> list[BlockModel]:
        # decide if we need to query for the unblocked block and filter it out
        include_unblocked = any(UNBLOCKED_PREFIX in id for id in ids)
        if include_unblocked:
            ids = [id for id in ids if UNBLOCKED_PREFIX not in id]

        query = select(BlockModel).filter(BlockModel.id.in_(ids))
        result = list((await session.scalars(query)).unique().all())

        if include_unblocked:
            result.append(THE_UNBLOCKED_BLOCK)

        return result

    async def __get_block_time(self, id: str, session: AsyncSession) -> BlockTimeModel:
        # Unblocked block time is not stored in the database
        if UNBLOCKED_PREFIX in id:
            return generate_unblocked_block_time_for_id(id)
        query = select(BlockTimeModel).filter(BlockTimeModel.id == id)
        return (await session.scalars(query)).unique().one()

    async def __get_block_times(
        self, ids: Iterable[str], session: AsyncSession
    ) -> list[BlockTimeModel]:
        # decide if we need to query for unblocked block times and filter them out
        include_unblocked = any(UNBLOCKED_PREFIX in id for id in ids)
        ids_minus_unblocked = ids
        if include_unblocked:
            ids_minus_unblocked = [id for id in ids if UNBLOCKED_PREFIX not in id]

        query = select(BlockTimeModel).filter(BlockTimeModel.id.in_(ids_minus_unblocked))
        result = list((await session.scalars(query)).unique().all())

        # Add unblocked block times if requested
        if include_unblocked:
            result.extend(
                [generate_unblocked_block_time_for_id(id) for id in ids if UNBLOCKED_PREFIX in id]
            )

        return result

    async def __get_block_times_for_block(
        self, block_id: str, session: AsyncSession, eager_load_releases: Optional[bool] = False
    ) -> list[BlockTimeModel]:
        query = select(BlockTimeModel)
        if eager_load_releases:
            query = query.options(joinedload(BlockTimeModel.releases, innerjoin=True))
        query = query.filter(BlockTimeModel.block_id == block_id)
        return list((await session.scalars(query)).unique().all())

    async def __get_block_times_for_blocks(
        self, block_ids: Iterable[str], session: AsyncSession
    ) -> DefaultDict[str, List[BlockTimeModel]]:
        query = select(BlockTimeModel).filter(BlockTimeModel.block_id.in_(block_ids))
        results = list((await session.scalars(query)).unique().all())
        result_dict = defaultdict(list)

        for row in results:
            result_dict[str(row.block_id)].append(row)
        return result_dict

    async def __get_block_time_releases_for_block_time(
        self, block_time_id: str, active_only: bool, session: AsyncSession
    ) -> list[BlockTimeReleaseModel]:
        query = (
            select(BlockTimeReleaseModel)
            .order_by(BlockTimeReleaseModel.updated_time.desc())
            .filter(BlockTimeReleaseModel.block_time_id == block_time_id)
        )
        if active_only:
            query = query.filter(BlockTimeReleaseModel.unreleased_time.is_(None))
        return list((await session.scalars(query)).unique().all())

    async def __get_block_time_releases_for_block_times(
        self,
        block_time_ids: Iterable[str],
        active_only: bool,
        session: AsyncSession,
        release_cutoff: Optional[timedelta] = None,
    ) -> DefaultDict[str, List[BlockTimeReleaseModel]]:
        query = (
            select(BlockTimeReleaseModel)
            .order_by(BlockTimeReleaseModel.updated_time.desc())
            .filter(BlockTimeReleaseModel.block_time_id.in_(block_time_ids))
        )
        if active_only:
            query = query.filter(BlockTimeReleaseModel.unreleased_time.is_(None))

        # if the release_time is greater than the release_cutoff time compared to the start_time of the release then filter
        if release_cutoff:
            query = query.filter(
                BlockTimeReleaseModel.start_time > BlockTimeReleaseModel.released_time
            ).where(
                func.extract(
                    "epoch", BlockTimeReleaseModel.start_time - BlockTimeReleaseModel.released_time
                )
                >= release_cutoff.total_seconds()
            )

        results = list((await session.scalars(query)).unique().all())
        result_dict = defaultdict(list)

        for row in results:
            result_dict[str(row.block_time_id)].append(row)

        return result_dict

    async def __delete_block_time_releases(self, ids: Iterable[str], session: AsyncSession) -> None:
        query = select(BlockTimeReleaseModel).filter(BlockTimeReleaseModel.id.in_(ids))
        block_time_releases = (await session.scalars(query)).unique().all()
        for block_time_release in block_time_releases:
            await session.delete(block_time_release)

        await session.commit()

    async def __delete_block_times(self, ids: Iterable[str], session: AsyncSession) -> None:
        query = select(BlockTimeModel).filter(BlockTimeModel.id.in_(ids))
        block_times = (await session.scalars(query)).unique().all()
        for block_time in block_times:
            await session.delete(block_time)

        await session.commit()

    async def get_block(
        self, id: str, eager_load_block_times: Optional[bool] = False
    ) -> BlockModel:
        async with new_async_session() as session:
            try:
                return await self.__get_block(id, session, eager_load_block_times)
            except sqlalchemy.exc.NoResultFound:
                raise NotFound(f"No block found with id: {id}")

    async def get_blocks(self, ids: Iterable[str]) -> list[BlockModel]:
        async with new_async_session() as session:
            return await self.__get_blocks(ids, session)

    async def get_block_time(self, id: str) -> BlockTimeModel:
        async with new_async_session() as session:
            try:
                return await self.__get_block_time(id, session)
            except sqlalchemy.exc.NoResultFound:
                raise NotFound(f"No block time found with id: {id}")

    async def get_block_times(self, ids: Iterable[str]) -> list[BlockTimeModel]:
        async with new_async_session() as session:
            results = await self.__get_block_times(ids, session)
            # I don't know why we mypy throws an error when using len on iterable, but this is an alternative
            if len(list(ids)) != len(results):
                raise ClientError(
                    HTTPStatus.BAD_REQUEST,
                    message="Invalid block time id(s) provided.",
                )
            return results

    async def create_block(self, block: BlockDataInput) -> BlockModel:
        async with new_async_session() as session:
            block_without_block_times = BlockModel(
                id=block.id,
                name=block.name,
                color=block.color,
                org_id=block.org_id,
            )

            if block.surgeon_ids is not None:
                surgeons = await StaffStore().query_staff(ids=block.surgeon_ids)
                block_without_block_times.surgeons = surgeons

            if block.site_ids is not None:
                sites = await SiteStore().query_sites(site_query=SiteQuery(site_ids=block.site_ids))
                block_without_block_times.sites_list = sites

            session.add(block_without_block_times)
            await session.flush()
            await self.upsert_block_times(session, block_times=block.block_times)
            await session.commit()
            await session.refresh(block_without_block_times)
            return block_without_block_times

    async def delete_block(self, id: uuid.UUID) -> None:
        async with new_async_session() as session:
            block = await self.__get_block(str(id), session)
            await self.upsert_or_delete_block_times_for_block(session, block_id=id, block_times=[])
            await session.delete(block)
            await session.commit()

    async def update_block(self, block: BlockDataInput) -> BlockModel:
        async with new_async_session() as session:
            await self.upsert_or_delete_block_times_for_block(
                session, block_id=block.id, block_times=block.block_times
            )
            old_block = await self.__get_block(str(block.id), session)
            old_block.name = block.name
            old_block.color = block.color

            if block.surgeon_ids is not None:
                old_block.surgeons = await update_related_entities(
                    block.surgeon_ids,
                    old_block.surgeons,
                    lambda ids: StaffStore().query_staff(ids=ids),
                )

            if block.site_ids is not None:
                old_block.sites_list = await update_related_entities(
                    block.site_ids,
                    old_block.sites_list,
                    lambda ids: SiteStore().query_sites(site_query=SiteQuery(site_ids=ids)),
                )

            await session.merge(old_block)
            await session.commit()
            await session.refresh(old_block)
            return old_block

    async def archive_block(self, id: str) -> BlockModel:
        async with new_async_session() as session:
            block = await self.__get_block(id, session)
            block.archived_time = block.archived_time or func.now()
            await session.commit()
            await session.refresh(block)
            return block

    async def unarchive_block(self, id: str) -> BlockModel:
        async with new_async_session() as session:
            block = await self.__get_block(id, session)
            block.archived_time = None  # type: ignore
            await session.commit()
            await session.refresh(block)
            return block

    async def query_blocks(
        self,
        dto: BlockQueryDto,
        eager_load_block_times: bool | None = False,
        include_empty_blocks: bool | None = True,
    ) -> list[BlockModel]:
        async with new_async_session() as session:
            query = select(BlockModel).order_by(BlockModel.name)

            if dto.ids:
                query = query.filter(BlockModel.id.in_(dto.ids))

            if dto.names:
                # query by names is case insensitive
                query = query.filter(
                    func.lower(BlockModel.name).in_([name.lower() for name in dto.names])
                )
            if not dto.include_archived:
                query = query.filter(BlockModel.archived_time.is_(None))

            if dto.org_ids:
                query = query.filter(BlockModel.org_id.in_(dto.org_ids))

            if dto.surgeon_ids:
                query = query.filter(BlockModel.surgeon_ids.any(StaffModel.id.in_(dto.surgeon_ids)))

            if eager_load_block_times:
                query = query.options(joinedload(BlockModel.block_times, innerjoin=True))

            if dto.site_ids:
                query = query.filter(BlockModel.site_ids.any(Site.id.in_(dto.site_ids)))

            if dto.days_of_week:
                query = idempotent_join(query, BlockTimeModel)
                query = query.filter(
                    func.extract("isodow", BlockTimeModel.start_time).in_(dto.days_of_week)
                )

            if dto.room_ids:
                query = idempotent_join(query, BlockTimeModel)
                query = query.filter(BlockTimeModel.room_id.in_(dto.room_ids))

            if dto.min_end_time or dto.max_start_time:
                if (
                    dto.min_end_time is not None
                    and dto.max_start_time is not None
                    and dto.min_end_time >= dto.max_start_time
                ):
                    raise ClientError(
                        HTTPStatus.BAD_REQUEST,
                        message="min_end_time must be less than max_start_time.",
                    )
                query = idempotent_join(query, BlockTimeModel)

                if dto.min_end_time:
                    query = query.filter(BlockTimeModel.end_time >= dto.min_end_time)
                if dto.max_start_time:
                    query = query.filter(BlockTimeModel.start_time <= dto.max_start_time)

            result = list((await session.scalars(query)).unique().all())
            fetched_block_names = {block.name for block in result}

            if include_empty_blocks or (
                dto.names and (not (set(dto.names).issubset(fetched_block_names)))
            ):
                blocks_with_no_block_times_query = (
                    select(BlockModel)
                    .join(BlockModel.block_times, isouter=True)
                    .order_by(BlockModel.name)
                    .options(joinedload(BlockModel.block_times))
                    .filter(BlockTimeModel.id.is_(None))
                )

                if not dto.include_archived:
                    blocks_with_no_block_times_query = blocks_with_no_block_times_query.filter(
                        BlockModel.archived_time.is_(None)
                    )

                blocks_with_no_block_times = list(
                    (await session.scalars(blocks_with_no_block_times_query)).unique().all()
                )

                result.extend(blocks_with_no_block_times)

            unique_result: dict[str, BlockModel] = {}
            for block in result:
                unique_result[str(block.id)] = block

            return list(unique_result.values())

    async def find_block_by_name_and_site(self, name: str, site_id: str) -> Optional[BlockModel]:
        async with new_async_session() as session:
            query = (
                select(BlockModel)
                .filter(func.lower(BlockModel.name) == name.lower())
                .filter(BlockModel.sites_list.any(Site.id == site_id))
                .filter(BlockModel.archived_time.is_(None))
            )
            return (await session.scalars(query)).unique().one_or_none()

    async def query_block_times(
        self,
        min_end_time: datetime,
        max_start_time: datetime,
        room_ids: list[str] | None = None,
        site_id: str | None = None,
        block_id: str | None = None,
    ) -> list[BlockTimeModel]:
        async with new_async_session() as session:
            query = select(BlockTimeModel).order_by(BlockTimeModel.start_time.asc())

            if site_id is not None and room_ids is not None:
                raise ClientError(
                    HTTPStatus.BAD_REQUEST,
                    message="Cannot filter by both site_id and room_ids.",
                )

            if site_id is not None:
                room_ids_at_site = [
                    room.id for room in (await RoomStore().get_rooms_in_site(site_id))
                ]
                query = query.filter(BlockTimeModel.room_id.in_(room_ids_at_site))

            if room_ids is not None:
                query = query.filter(BlockTimeModel.room_id.in_(room_ids))

            if block_id is not None:
                query = query.filter(BlockTimeModel.block_id == block_id)

            if min_end_time > max_start_time:
                raise ClientError(
                    HTTPStatus.BAD_REQUEST,
                    message="time range lower bound must be less than or equal to upper bound.",
                )

            query = query.filter(
                BlockTimeModel.block_time.overlaps(Range(min_end_time, max_start_time))
            )

            return list((await session.scalars(query)).all())

    async def get_block_times_for_block(
        self, block_id: uuid.UUID, eager_load_releases: Optional[bool] = False
    ) -> list[BlockTimeModel]:
        async with new_async_session() as session:
            return await self.__get_block_times_for_block(
                str(block_id), session, eager_load_releases
            )

    async def get_block_times_for_dates(
        self, min_date: datetime, max_date: datetime
    ) -> list[BlockTimeModel]:
        # Minimum time for the given date (00:00:00)
        min_time = datetime.combine(min_date, time.min)
        # Maximum time for the given date (23:59:59)
        max_time = datetime.combine(max_date, time.max)

        async with new_async_session() as session:
            query = (
                select(BlockTimeModel)
                .filter(BlockTimeModel.block_time.overlaps(Range(min_time, max_time)))
                .order_by(BlockTimeModel.start_time.asc())
            )
            return list((await session.scalars(query)).unique().all())

    async def get_block_times_for_blocks(
        self, block_ids: Iterable[str]
    ) -> DefaultDict[str, List[BlockTimeModel]]:
        async with new_async_session() as session:
            return await self.__get_block_times_for_blocks(block_ids, session)

    async def get_block_time_releases_for_block_time(
        self, block_time_id: str, active_only: bool = False
    ) -> list[BlockTimeReleaseModel]:
        async with new_async_session() as session:
            return await self.__get_block_time_releases_for_block_time(
                block_time_id, active_only, session
            )

    async def get_block_time_releases_for_block_times(
        self, block_time_ids: Iterable[str], active_only: bool = False
    ) -> DefaultDict[str, List[BlockTimeReleaseModel]]:
        async with new_async_session() as session:
            return await self.__get_block_time_releases_for_block_times(
                block_time_ids, active_only, session
            )

    async def get_block_time_release_for_special_block(
        self,
        from_block_type: BlockTypes,
        room_id: str,
        start_time: datetime,
        end_time: datetime,
        to_block_type: BlockTypes | None = None,
    ) -> BlockTimeReleaseModel | None:
        async with new_async_session() as session:
            query = select(BlockTimeReleaseModel).filter(
                BlockTimeReleaseModel.from_block_type == from_block_type,
                BlockTimeReleaseModel.to_block_type == to_block_type,
                BlockTimeReleaseModel.block_time.has(BlockTimeModel.room_id == room_id),
                BlockTimeReleaseModel.start_time == start_time,
                BlockTimeReleaseModel.end_time == end_time,
            )
            return (await session.scalars(query)).unique().one_or_none()

    async def upsert_or_delete_block_times_for_block(
        self, session: AsyncSession, block_id: uuid.UUID, block_times: list[BlockTimeModel]
    ) -> None:
        # Get block time/release ids from input
        block_time_ids = [block_time.id for block_time in block_times]

        # Get existing block times and releases from db
        existing_block_times = await self.__get_block_times_for_block(str(block_id), session)
        existing_block_time_releases = await self.__get_block_time_releases_for_block_times(
            block_time_ids=[str(block_time.id) for block_time in existing_block_times],
            active_only=False,  # We do want to delete inactive releases as well
            session=session,
        )

        flat_existing_block_time_releases = [
            release
            for block_time_id in existing_block_time_releases.keys()
            for release in existing_block_time_releases[block_time_id]
        ]

        # Figure out which block times are to be deleted
        deleted_block_time_ids = [
            str(block_time.id)
            for block_time in existing_block_times
            if str(block_time.id) not in block_time_ids
        ]

        # NB: Unrelease !== delete
        # Here we delete the block time releases whose block times are to be deleted
        deleted_block_time_release_ids = [
            str(release.id)
            for release in flat_existing_block_time_releases
            if str(release.block_time_id) in deleted_block_time_ids
        ]

        if (len(deleted_block_time_release_ids)) > 0:
            await self.__delete_block_time_releases(deleted_block_time_release_ids, session)

        if (len(deleted_block_time_ids)) > 0:
            await self.__delete_block_times(deleted_block_time_ids, session)

        if len(block_times) > 0:
            await self.upsert_block_times(
                session,
                block_times=[
                    block_time
                    for block_time in block_times
                    if block_time.id not in deleted_block_time_ids
                ],
            )

    async def create_block_time(self, block_time: BlockTimeModel) -> BlockTimeModel:
        async with new_async_session() as session:
            try:
                session.add(block_time)
                await session.commit()
                await session.refresh(block_time)
            except sqlalchemy.exc.IntegrityError as e:
                await self._handle_integrity_error(e, session)
            except sqlalchemy.exc.DataError as e:
                self._handle_data_error(e)

        return block_time

    async def create_block_time_release(
        self, release: BlockTimeReleaseModel
    ) -> BlockTimeReleaseModel:
        async with new_async_session() as session:
            try:
                session.add(release)
                await session.commit()
                await session.refresh(release)
            except sqlalchemy.exc.IntegrityError as e:
                await self._handle_integrity_error(e, session)
            except sqlalchemy.exc.DataError as e:
                self._handle_data_error(e)

        return release

    async def upsert_block_times(
        self, session: AsyncSession, block_times: list[BlockTimeModel]
    ) -> list[BlockTimeModel]:
        if len(block_times) == 0:
            return []

        try:
            ids = await async_postgres_insert_on_conflict_do_update_helper(
                session=session,
                subject=BlockTimeModel,
                values=block_times,
                returning=BlockTimeModel.id,
            )

            all_block_time_releases = []

            for block_time in block_times:
                for release in block_time.releases:
                    all_block_time_releases.append(release)

            if len(all_block_time_releases) > 0:
                await async_postgres_insert_on_conflict_do_update_helper(
                    session=session,
                    subject=BlockTimeReleaseModel,
                    values=all_block_time_releases,
                    returning=BlockTimeReleaseModel.id,
                )

            await session.flush()
        except sqlalchemy.exc.IntegrityError as e:
            await self._handle_integrity_error(e, session)
        except sqlalchemy.exc.DataError as e:
            self._handle_data_error(e)

        return await self.__get_block_times(ids=ids, session=session)

    async def delete_block_times(self, ids: Iterable[str]) -> None:
        async with new_async_session() as session:
            return await self.__delete_block_times(ids, session)

    async def delete_block_time_releases(self, ids: Iterable[str]) -> None:
        async with new_async_session() as session:
            return await self.__delete_block_time_releases(ids, session)

    async def bulk_add_block_times(self, block_times: list[BlockTimeModel]) -> list[str]:
        async with new_async_session() as session:
            try:
                for block_time in block_times:
                    session.add(block_time)

                await session.commit()

            except sqlalchemy.exc.IntegrityError as e:
                await self._handle_integrity_error(e, session)
            except sqlalchemy.exc.DataError as e:
                self._handle_data_error(e)

            return [str(block_time.id) for block_time in block_times]

    async def bulk_add_block_releases(
        self, block_releases: list[BlockReleaseDataInput]
    ) -> dict[str, BlockReleaseDataInput]:
        async with new_async_session() as session:
            # Convert matched releases to BlockTimeReleaseModel now that we have all the necessary information
            block_release_models = [
                BlockTimeReleaseModel(
                    id=release.id,
                    block_time_id=release.block_time_id,
                    reason=release.release_reason,
                    start_time=release.release_start_time,
                    end_time=release.release_end_time,
                    released_time=release.released_time,
                    source=release.released_by,
                    source_type=BLOCK_RELEASE_SOURCE_TYPE,
                    unreleased_time=None,
                    unreleased_source=None,
                    from_block_type=release.from_block_type,
                    to_block_type=release.to_block_type,
                    release_type=release.release_type,
                    days_prior=release.days_prior,
                    room_id=release.room_id,
                    to_block_id=release.to_block_id,
                )
                for release in block_releases
            ]

            response: list[BlockTimeReleaseModel] = []

            try:
                session.add_all(block_release_models)
                await session.commit()
                response = block_release_models
            except sqlalchemy.exc.IntegrityError:
                await session.rollback()
                warning("Bulk inserting rows failed, fallback to one by one")

                for release in block_release_models:
                    try:
                        session.add(release)
                        await session.commit()
                        response.append(release)

                    except sqlalchemy.exc.IntegrityError:
                        await session.rollback()
                        error(f"Error inserting item: ${release}")

            response_ids = {row.id for row in response}

            saved_block_releases_dict = {
                str(release.id): release for release in block_releases if release.id in response_ids
            }

            return saved_block_releases_dict

    async def update_block_time_release(
        self,
        release: BlockReleaseDataInput | BlockTimeReleaseModel,
        existing_release_id: uuid.UUID,
    ) -> BlockTimeReleaseModel | None:
        async with new_async_session() as session:
            try:
                result = await session.execute(
                    select(BlockTimeReleaseModel).filter_by(id=existing_release_id)
                )
                existing_release = result.scalar_one_or_none()

                if existing_release is None:
                    raise ValueError("Release not found")

                if (
                    existing_release.block_time_id
                    and release.block_time_id
                    and existing_release.block_time_id == release.block_time_id
                    and release.from_block_type
                    and release.to_block_type
                    and release.release_type
                    and release.days_prior
                    and release.room_id
                    and not existing_release.to_block_id
                ):
                    existing_release.from_block_type = release.from_block_type
                    existing_release.to_block_type = release.to_block_type
                    existing_release.release_type = release.release_type
                    existing_release.days_prior = release.days_prior
                    existing_release.room_id = release.room_id
                    existing_release.to_block_id = release.to_block_id

                await session.commit()
                await session.refresh(existing_release)
                return existing_release

            except sqlalchemy.exc.IntegrityError as e:
                await self._handle_integrity_error(e, session)
            except sqlalchemy.exc.DataError as e:
                self._handle_data_error(e)
            return None

    def _handle_data_error(self, e: sqlalchemy.exc.DataError) -> None:
        if check_exception(DataError, e):
            # start_time > end_time error
            raise ClientError(
                HTTPStatus.BAD_REQUEST,
                message="The start time must be before the end time.",
            )
        raise e

    async def _handle_integrity_error(
        self, e: sqlalchemy.exc.IntegrityError, session: AsyncSession
    ) -> None:
        await session.rollback()

        if check_exception(ExclusionViolationError, e):
            # Parse e.orig.diag.message_detail:
            # Key (room_id, block_time)=(garage_0, ["2023-11-07 20:00:00+00","2023-11-07 23:00:00+00")) conflicts with existing key (room_id, block_time)=(garage_0, ["2023-11-07 17:00:00+00","2023-11-07 23:00:00+00"))
            pattern = r"\(room_id, block_time\)=\(([\w_-]+), \[\"([\d\s\:\+\-]+)\"\s*,\s*\"([\d\s\:\+\-]+)\"\)\)"

            _, existing = re.findall(pattern, get_exception_message(e))

            room_id, start, end = existing
            start_time = parse(start)
            end_time = parse(end)
            query = (
                select(BlockTimeModel)
                .order_by(BlockTimeModel.start_time.asc())
                .filter(BlockTimeModel.room_id == room_id)
                .filter(BlockTimeModel.block_time.overlaps(Range(start_time, end_time)))
            )
            existing_block_time_record = (await session.scalars(query)).one_or_none()

            if existing_block_time_record is not None:
                block = await self.__get_block(str(existing_block_time_record.block_id), session)

                raise ClientError(
                    HTTPStatus.CONFLICT,
                    message=f"TIME_RANGE_ERROR - BLOCK_NAME '{block.name}' BLOCK_TIME_RANGE {existing_block_time_record.block_time}",
                )

            # Throw a general error if we can't find the block time, most likely conflicts exist within the input
            raise ClientError(
                HTTPStatus.CONFLICT,
                message="A block time covering all or part of this time range already exists for this room.",
            )

        if check_exception(CheckViolationError, e):
            # start_time == end_time error
            raise ClientError(
                HTTPStatus.BAD_REQUEST,
                message="The start time must be strictly less than the end time.",
            )

        raise e

    async def query_block_times_available_intervals(
        self,
        site_id: str,
        min_end_time: datetime,
        max_start_time: datetime,
        release_cutoff: Optional[timedelta] = None,
        block_id: Optional[str] = None,
    ) -> list[BlockTimeInterval]:
        async with new_async_session() as session:
            room_ids_at_site = [room.id for room in (await RoomStore().get_rooms_in_site(site_id))]

            block_time_query = select(BlockTimeModel)
            if block_id and block_id.lower() != "unblocked":
                block_time_query = block_time_query.filter(
                    BlockTimeModel.block_id == uuid.UUID(block_id)
                )

            block_time_query = (
                block_time_query.filter(BlockTimeModel.room_id.in_(room_ids_at_site))
                .filter(BlockTimeModel.block_time.overlaps(Range(min_end_time, max_start_time)))
                .order_by(BlockTimeModel.start_time.asc())
            )

            block_times = (await session.execute(block_time_query)).scalars().all()
            block_time_ids = [str(bt.id) for bt in block_times]

            block_time_id_to_releases = await self.__get_block_time_releases_for_block_times(
                block_time_ids=block_time_ids,
                active_only=True,
                release_cutoff=release_cutoff,
                session=session,
            )
            blocks = await self.__get_blocks(
                ids=[str(bt.block_id) for bt in block_times], session=session
            )
            block_id_to_surgeon_ids = {str(block.id): block.surgeon_ids for block in blocks}

            available_intervals = []

            for row in block_times:
                new_intervals = calc_block_time_available_intervals(
                    block_time=row,
                    block_releases=block_time_id_to_releases.get(str(row.id), []),
                    room_id=row.room_id,
                    surgeon_ids=block_id_to_surgeon_ids.get(str(row.block_id), None),
                )
                available_intervals.extend(new_intervals)

            return available_intervals

    async def get_block_time_available_intervals(
        self, block_time: BlockTimeModel
    ) -> list[BlockTimeInterval]:
        async with new_async_session() as session:
            query = (
                select(BlockTimeReleaseModel)
                .order_by(BlockTimeReleaseModel.start_time.asc())
                .filter(BlockTimeReleaseModel.block_time_id == block_time.id)
                .filter(BlockTimeReleaseModel.unreleased_time.is_(None))
            )
            sorted_releases = list((await session.scalars(query)).unique().all())

            block = await self.__get_block(str(block_time.block_id), session)

            return calc_block_time_available_intervals(
                block_time,
                sorted_releases,
                room_id=block_time.room_id,
                surgeon_ids=block.surgeon_ids,
            )

    async def get_staff_block_ids(self, staff_id: str) -> List[str]:
        async with new_async_session() as session:
            query = select(block_surgeons).where(block_surgeons.c.staff_id == staff_id)

            rows = (await session.scalars(query)).unique().all()

            return [str(row) for row in rows]

    async def get_many_staff_block_ids_by_staff_id(
        self, staff_ids: list[str]
    ) -> dict[str, set[str]]:
        async with new_async_session() as session:
            query = select(block_surgeons).where(block_surgeons.c.staff_id.in_(staff_ids))

            rows = (await session.execute(query)).unique().all()
            results = defaultdict(set)
            for row in rows:
                results[str(row.staff_id)].add(str(row.block_id))
            return results


def calc_block_time_available_intervals(
    block_time: BlockTimeModel,
    block_releases: list[BlockTimeReleaseModel],
    room_id: str,
    surgeon_ids: Optional[list[str]] = None,
) -> list[BlockTimeInterval]:
    original_block = [
        BlockTimeInterval(
            block_time_id=str(block_time.id),
            start_time=block_time.start_time,
            end_time=block_time.end_time,
            room_id=room_id,
            surgeon_ids=surgeon_ids,
            block_id=block_time.block_id,
        )
    ]
    if len(block_releases) == 0:
        return original_block

    if (
        len(block_releases) == 1
        and block_releases[0].start_time == block_time.start_time
        and block_releases[0].end_time == block_time.end_time
    ):
        return [] if block_releases[0].unreleased_time is None else original_block

    # Append fake release at the beginning to simplify the logic
    closed_intervals = (
        [
            TimeInterval(
                start_time=block_time.start_time - timedelta(seconds=1),
                end_time=block_time.start_time,
            )
        ]
        + [
            TimeInterval(
                start_time=release.start_time,
                end_time=release.end_time,
            )
            for release in block_releases
            if release.unreleased_time is None
        ]
        + [
            TimeInterval(
                start_time=block_time.end_time,
                end_time=block_time.end_time + timedelta(seconds=1),
            )
        ]
    )

    available_intervals = []
    for i in range(len(closed_intervals)):
        if i > 0 and closed_intervals[i - 1].end_time != closed_intervals[i].start_time:
            available_intervals.append(
                BlockTimeInterval(
                    block_time_id=str(block_time.id),
                    start_time=closed_intervals[i - 1].end_time,
                    end_time=closed_intervals[i].start_time,
                    room_id=room_id,
                    block_id=block_time.block_id,
                    surgeon_ids=surgeon_ids,
                )
            )

    return available_intervals


def calculate_unblocked_time_id(room_id: str, start_time: datetime, end_time: datetime) -> str:
    return UNBLOCKED_DELIMITER.join(
        [
            UNBLOCKED_PREFIX,
            room_id,
            start_time.astimezone(ZoneInfo("UTC")).isoformat(),
            end_time.astimezone(ZoneInfo("UTC")).isoformat(),
        ]
    )


def generate_unblocked_block_time_for_id(id: str) -> BlockTimeModel:
    [_, room_id, start_time_str, end_time_str] = id.split(UNBLOCKED_DELIMITER)
    return BlockTimeModel(
        id=id,
        block_id=UNBLOCKED_PREFIX,
        room_id=room_id,
        start_time=parse(start_time_str),
        end_time=parse(end_time_str),
    )
