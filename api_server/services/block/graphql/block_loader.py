from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Sequence

from aiodataloader import DataLoader

from apella_cloud_api.dtos import BlockTimeQueryDto
from api_server.services.block.block_service import BlockService
from api_server.services.block.block_store import BlockModel, BlockTimeModel, BlockTimeReleaseModel
from api_server.services.utils.loader.base_classes import (
    AbstractDataLoader,
)
from api_server.services.utils.loader.util_functions import (
    async_create_loader_queries,
    sort_loader_results,
)


class BlockLoader(DataLoader[str, Optional[BlockModel]]):
    def __init__(self, block_service: BlockService):
        super().__init__()
        self.block_service = block_service

    async def batch_load_fn(self, block_ids: list[str]) -> list[Optional[BlockModel]]:
        deduped_block_ids = set(block_ids)
        blocks = await self.block_service.get_blocks(ids=deduped_block_ids)
        return sort_loader_results(block_ids, blocks)


class StaffBlockLoader(DataLoader[str, list[BlockModel]]):
    def __init__(self, block_service: BlockService):
        super().__init__()
        self.block_service = block_service

    async def batch_load_fn(self, staff_ids: list[str]) -> list[list[BlockModel]]:
        staff_block_ids_by_staff_id = await self.block_service.get_many_staff_block_ids_by_staff_id(
            staff_ids
        )
        deduped_block_ids = {
            block_id for block_ids in staff_block_ids_by_staff_id.values() for block_id in block_ids
        }
        blocks = await self.block_service.get_blocks(ids=deduped_block_ids)
        blocks_by_block_id: dict[str, BlockModel] = {str(block.id): block for block in blocks}
        staff_blocks_by_staff_id: dict[str, list[BlockModel]] = defaultdict(list)
        for staff_id, block_ids in staff_block_ids_by_staff_id.items():
            staff_blocks_by_staff_id[staff_id] = [
                blocks_by_block_id[block_id] for block_id in block_ids
            ]
        return [staff_blocks_by_staff_id.get(staff_id, []) for staff_id in staff_ids]


class BlockTimeLoader(DataLoader[str, Optional[BlockTimeModel]]):
    def __init__(self, block_service: BlockService):
        super().__init__()
        self.block_service = block_service

    async def batch_load_fn(self, block_time_ids: list[str]) -> list[Optional[BlockTimeModel]]:
        deduped_block_time_ids = set(block_time_ids)
        block_times = await self.block_service.get_block_times(ids=deduped_block_time_ids)
        return sort_loader_results(block_time_ids, block_times)


class BlockTimeForBlockLoader(DataLoader[str, list[BlockTimeModel]]):
    def __init__(self, block_service: BlockService):
        super().__init__()
        self.block_service = block_service

    async def batch_load_fn(self, block_ids: list[str]) -> list[list[BlockTimeModel]]:
        deduped_block_ids = set(block_ids)
        results_dict = await self.block_service.get_block_times_for_blocks(
            block_ids=deduped_block_ids
        )
        return [results_dict.get(str(block_id), []) for block_id in block_ids]


class BlockTimeReleaseForBlockTimeLoader(DataLoader[str, list[BlockTimeReleaseModel]]):
    def __init__(self, block_service: BlockService):
        super().__init__()
        self.block_service = block_service

    async def batch_load_fn(self, block_time_ids: list[str]) -> list[list[BlockTimeReleaseModel]]:
        deduped_block_time_ids = set(block_time_ids)

        results_dict = await self.block_service.get_block_time_releases_for_block_times(
            block_time_ids=deduped_block_time_ids,
            active_only=False,
        )
        return [results_dict.get(str(block_time_id), []) for block_time_id in block_time_ids]


@dataclass(frozen=True, eq=True)
class FrozenRoomBlockTimesQueryDto:
    room_id: Optional[str] = None
    min_end_time: Optional[datetime] = None
    max_start_time: Optional[datetime] = None

    @property
    def id(self) -> str:
        return f"{self.room_id}"


class RoomBlockTimeLoader(
    AbstractDataLoader[FrozenRoomBlockTimesQueryDto, Sequence[BlockTimeModel]]
):
    block_service: BlockService

    def compare_key_to_model(
        self,
        key: FrozenRoomBlockTimesQueryDto,
        value: BlockTimeModel,
    ) -> bool:
        return key.id == f"{value.room_id}"

    def __init__(self, block_service: BlockService):
        super().__init__()
        self.block_service = block_service

    async def batch_load_fn(
        self, keys: list[FrozenRoomBlockTimesQueryDto]
    ) -> list[Sequence[BlockTimeModel]]:
        result_dict: dict[
            FrozenRoomBlockTimesQueryDto, Sequence[BlockTimeModel]
        ] = await async_create_loader_queries(
            keys=keys,
            key_type=FrozenRoomBlockTimesQueryDto,
            query_type=BlockTimeQueryDto,
            service_query_fn=self.block_service.query_room_block_times,
            filter_fn=self.compare_key_to_model,
            pks={
                "room_id": "room_ids",
            },
        )
        return [result_dict.get(key, []) for key in keys]
