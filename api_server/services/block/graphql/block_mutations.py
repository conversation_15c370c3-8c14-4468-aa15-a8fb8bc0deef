import logging
from typing import Union, <PERSON>ple

from graphql import GraphQLError

from api_server.services.block.block_models import BlockDataInput, BlockModel, ReleaseTypes
import graphene
from uuid import uuid4, UUID

from api_server.graphql.context import GrapheneInfo
from api_server.graphql.partial_updates import get_field_value_from_request_input
from api_server.services.block.block_store import (
    BlockTimeModel,
    BlockTimeReleaseModel,
)
from api_server.services.block.graphql.blocks import Block
from api_server.services.events.source_type import HUMAN_GROUND_TRUTH

logger = logging.getLogger(__name__)


class BlockTimeReleaseInput(graphene.InputObjectType):
    id = graphene.ID(required=False)
    block_time_id = graphene.ID(required=False)
    start_time = graphene.DateTime(required=True)
    end_time = graphene.DateTime(required=True)
    reason = graphene.String(required=True)
    source = graphene.String(required=True)
    to_block = graphene.String(required=False, default=None)
    released_time = graphene.DateTime(required=False)
    unreleased_time = graphene.DateTime(required=False)
    unreleased_source = graphene.String(required=False)
    to_block_type = graphene.String(required=False, default=None)


class BlockTimeInput(graphene.InputObjectType):
    id = graphene.ID(required=False)
    block_id = graphene.ID(required=False)
    start_time = graphene.DateTime(required=True)
    end_time = graphene.DateTime(required=True)
    room_id = graphene.ID(required=True)
    releases = graphene.List(
        graphene.NonNull(BlockTimeReleaseInput), required=False, default_value=[]
    )


class BlockCreateInput(graphene.InputObjectType):
    id = graphene.ID(required=False)
    name = graphene.String(required=True)
    color = graphene.String(required=False, default_value="#808080")
    org_id = graphene.ID(required=True)
    site_ids = graphene.List(graphene.NonNull(graphene.ID), required=False, default_value=[])
    block_times = graphene.List(graphene.NonNull(BlockTimeInput), required=False, default_value=[])
    surgeon_ids = graphene.List(graphene.NonNull(graphene.ID), required=False, default_value=[])


class BlockUpdateInput(graphene.InputObjectType):
    id = graphene.ID(required=True)
    name = graphene.String(required=True)
    color = graphene.String(required=True)
    site_ids = graphene.List(graphene.NonNull(graphene.ID), required=False, default_value=[])
    block_times = graphene.List(graphene.NonNull(BlockTimeInput), required=True)
    surgeon_ids = graphene.List(graphene.NonNull(graphene.ID), required=False, default_value=[])


class BlockTimeBulkDeleteDuplicateInput(graphene.InputObjectType):
    org_id = graphene.String(required=True)


async def generate_block_time_model_from_input(
    input: Union[BlockCreateInput, BlockUpdateInput], block_id: UUID, info: GrapheneInfo
) -> Tuple[list[BlockTimeModel], list[BlockTimeModel]]:
    block_time_models: list[BlockTimeModel] = []
    generated_block_time_models: list[BlockTimeModel] = []

    for block_time in input.block_times:
        block_time_id = block_time.id or str(uuid4())
        releases = []

        for release in block_time.releases:
            if (release.start_time < block_time.start_time) or (
                release.end_time > block_time.end_time
            ):
                logger.warning(
                    f"Block time release start and end times must be within the block time. Release ID: {release.id}, "
                    f"Block time ID: {block_time_id}"
                )
                raise GraphQLError(
                    "Block time release start and end times must be within the block time."
                )
            to_block = (
                await info.context.block_release_processing_service.get_block_given_block_name(
                    name=release.to_block, room_id=block_time.room_id
                )
            )
            block_time_release_model = BlockTimeReleaseModel(
                id=release.id or str(uuid4()),
                block_time_id=block_time_id,
                reason=release.reason,
                start_time=release.start_time,
                end_time=release.end_time,
                source=release.source,
                source_type=HUMAN_GROUND_TRUTH,
                unreleased_time=release.unreleased_time,
                unreleased_source=release.unreleased_source,
                to_block_type=release.to_block_type,
                room_id=block_time.room_id,
                to_block_id=to_block.id if to_block else None,
                release_type=ReleaseTypes.MANUAL,
            )
            if release.released_time:
                # TODO: to be deprecated
                block_time_release_model.released_at = release.released_time
                block_time_release_model.released_time = release.released_time
                block_time_release_model.days_prior = (
                    release.start_time - release.released_time
                ).days

            releases.append(block_time_release_model)

            new_block_time_model = await info.context.block_release_processing_service.generate_block_time_given_release_model(
                release=block_time_release_model
            )
            if not new_block_time_model:
                continue
            generated_block_time_models.append(new_block_time_model)

        block_time_model = BlockTimeModel(
            id=block_time_id,
            block_id=block_id,
            room_id=block_time.room_id,
            start_time=block_time.start_time,
            end_time=block_time.end_time,
            releases=releases,
        )
        block_time_models.append(block_time_model)

    return block_time_models, generated_block_time_models


class BlockCreate(graphene.Mutation):
    class Arguments:
        input = BlockCreateInput(required=True)

    block = graphene.Field(Block)

    @staticmethod
    async def mutate(parent: None, info: GrapheneInfo, input: BlockCreateInput) -> "BlockCreate":
        block_id = input.id or uuid4()
        block_time_models, _ = await generate_block_time_model_from_input(
            input=input, block_id=block_id, info=info
        )

        filtered_block_times = await info.context.block_service.filter_overlapping_block_times(
            block_time_models
        )
        if filtered_block_times is not None and len(filtered_block_times) < len(block_time_models):
            raise GraphQLError(
                "Block release start and end times are overlapping with existing block times."
            )

        block = info.context.block_service.create_block(
            block=BlockDataInput(
                id=block_id,
                name=str(input.name),
                color=str(input.color),
                org_id=str(input.org_id),
                site_ids=get_field_value_from_request_input(input, "site_ids"),
                block_times=block_time_models,
                surgeon_ids=get_field_value_from_request_input(input, "surgeon_ids"),
            )
        )

        return BlockCreate(block=block)


class BlockUpdate(graphene.Mutation):
    class Arguments:
        input = BlockUpdateInput(required=True)

    block = graphene.Field(Block)

    @staticmethod
    async def mutate(parent: None, info: GrapheneInfo, input: BlockUpdateInput) -> "BlockUpdate":
        block_id = UUID(str(input.id))
        (
            block_time_models,
            generated_block_time_models,
        ) = await generate_block_time_model_from_input(input=input, block_id=block_id, info=info)
        block = await info.context.block_service.update_block(
            block=BlockDataInput(
                id=block_id,
                name=str(input.name),
                color=str(input.color),
                site_ids=get_field_value_from_request_input(input, "site_ids"),
                block_times=block_time_models,
                surgeon_ids=get_field_value_from_request_input(input, "surgeon_ids"),
            )
        )

        filtered_block_times = await info.context.block_service.filter_overlapping_block_times(
            generated_block_time_models
        )
        if filtered_block_times is not None and len(filtered_block_times) < len(
            generated_block_time_models
        ):
            raise GraphQLError(
                "Block release start and end times are overlapping with existing block times."
            )
        await info.context.block_service.bulk_add_block_times(generated_block_time_models)
        return BlockUpdate(block=block)


class BlockArchive(graphene.Mutation):
    class Arguments:
        id = graphene.ID(required=True)

    block = graphene.Field(Block)

    @staticmethod
    async def mutate(parent: None, info: GrapheneInfo, id: graphene.ID) -> "BlockArchive":
        block: BlockModel = await info.context.block_service.archive_block(id=id)
        return BlockArchive(block=block)


class BlockUnarchive(graphene.Mutation):
    class Arguments:
        id = graphene.ID(required=True)

    block = graphene.Field(Block)

    @staticmethod
    async def mutate(parent: None, info: GrapheneInfo, id: graphene.ID) -> "BlockUnarchive":
        return BlockUnarchive(block=info.context.block_service.unarchive_block(id=id))


class BlockTimeBulkCreateInput(graphene.InputObjectType):
    block_times = graphene.List(graphene.NonNull(BlockTimeInput), required=True)


class BlockTimeBulkCreate(graphene.Mutation):
    class Arguments:
        input = BlockTimeBulkCreateInput(required=True)

    success = graphene.Boolean(required=True)
    ids = graphene.List(graphene.NonNull(graphene.ID), required=True)

    @staticmethod
    async def mutate(
        parent: None, info: GrapheneInfo, input: BlockTimeBulkCreateInput
    ) -> "BlockTimeBulkCreate":
        block_time_models = [
            BlockTimeModel(
                id=block_time.id or str(uuid4()),
                block_id=block_time.block_id,
                room_id=block_time.room_id,
                start_time=block_time.start_time,
                end_time=block_time.end_time,
                releases=[],
            )
            for block_time in input.block_times
        ]
        filtered_block_times = await info.context.block_service.filter_overlapping_block_times(
            block_time_models
        )
        if not filtered_block_times:
            return BlockTimeBulkCreate(success=False, ids=[])

        if filtered_block_times is not None and len(filtered_block_times) < len(block_time_models):
            logger.warning(
                f"Filtered overlapping block times with existing block intervals. Block Times: {block_time_models}, filtered: {filtered_block_times}"
            )
        added_ids = await info.context.block_service.bulk_add_block_times(filtered_block_times)

        return BlockTimeBulkCreate(
            success=len(added_ids) == len(filtered_block_times), ids=added_ids
        )


class BlockTimeBulkDeleteDuplicate(graphene.Mutation):
    class Arguments:
        input = BlockTimeBulkDeleteDuplicateInput(required=True)

    success = graphene.Boolean(required=True)
    deleted_count = graphene.Int(required=True)

    @staticmethod
    async def mutate(
        parent: None, info: GrapheneInfo, input: BlockTimeBulkDeleteDuplicateInput
    ) -> "BlockTimeBulkDeleteDuplicate":
        org_id = input.org_id
        deleted_count = 0
        try:
            deleted_count = await info.context.block_service.delete_duplicate_block_times(
                org_id=org_id
            )
        except Exception as e:
            logger.error(f"Error deleting duplicate block times: {e}", exc_info=True)
            raise GraphQLError(f"Error deleting duplicate block times: {e}")
        return BlockTimeBulkDeleteDuplicate(success=True, deleted_count=deleted_count)
