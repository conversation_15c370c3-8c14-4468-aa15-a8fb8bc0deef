from datetime import datetime
from typing import Optional

import graphene
from graphql import GraphQLError

from apella_cloud_api.dtos import <PERSON>QueryDto, BlockTimeQueryDto
from api_server.graphql.context import GrapheneInfo
from api_server.graphql.pagination.fields import pagination_connection_factory
from api_server.services.block.block_store import BlockModel, BlockTimeModel, BlockTimeReleaseModel
from api_server.services.block.graphql.block_loader import FrozenRoomBlockTimesQueryDto
from api_server.services.organization.graphql import organization as organization_schema
from api_server.services.organization.organization_db import Organization as OrganizationModel
from api_server.services.room.graphql import room as room_schema
from api_server.services.room.room_store import RoomModel
from api_server.services.staff.graphql import staff as staff_schema
from api_server.services.staff.staff_store import StaffModel


class BlockTimeRelease(graphene.ObjectType):
    id = graphene.ID(required=True)
    block_time_id = graphene.ID(required=True)

    start_time = graphene.DateTime(required=True)
    end_time = graphene.DateTime(required=True)

    block_time = graphene.Field(lambda: BlockTime, required=True)

    reason = graphene.String(required=True)
    released_at = graphene.DateTime(required=True)
    released_time = graphene.DateTime(required=True)
    source = graphene.String(required=True)
    source_type = graphene.String(required=True)

    unreleased_time = graphene.DateTime()
    unreleased_source = graphene.String()

    @staticmethod
    async def resolve_start_time(
        block_time_release: BlockTimeReleaseModel, info: GrapheneInfo
    ) -> datetime:
        return block_time_release.release_time.lower

    @staticmethod
    async def resolve_end_time(
        block_time_release: BlockTimeReleaseModel, info: GrapheneInfo
    ) -> datetime:
        return block_time_release.release_time.upper

    @staticmethod
    async def resolve_block_time(
        block_time_release: BlockTimeReleaseModel, info: GrapheneInfo
    ) -> Optional[BlockTimeModel]:
        return await info.context.block_time_loader.load(str(block_time_release.block_time_id))


class BlockTimeAvailableInterval(graphene.ObjectType):
    id = graphene.ID(required=True)
    block_time_id = graphene.ID(required=True)
    start_time = graphene.DateTime(required=True)
    end_time = graphene.DateTime(required=True)
    room_id = graphene.ID(required=True)
    surgeon_ids = graphene.List(graphene.NonNull(graphene.ID), required=False)


BlockTimeAvailableIntervalConnection = pagination_connection_factory(BlockTimeAvailableInterval)


class BlockTime(graphene.ObjectType):
    id = graphene.ID(required=True)
    block_id = graphene.ID(required=True)
    start_time = graphene.DateTime(required=True)
    end_time = graphene.DateTime(required=True)
    room_id = graphene.ID(required=True)
    room = graphene.Field(lambda: room_schema.Room, required=True)
    block = graphene.Field(lambda: Block, required=True)
    released_from = graphene.String(required=False)
    created_time = graphene.DateTime(required=False)
    releases = graphene.List(
        graphene.NonNull(BlockTimeRelease),
        required=True,
        args={"include_unreleased": graphene.Boolean(default_value=False)},
    )

    surgeon_ids = graphene.List(graphene.NonNull(graphene.ID), required=False)
    available_intervals = graphene.List(
        graphene.NonNull(BlockTimeAvailableInterval), required=False
    )

    @staticmethod
    async def resolve_start_time(block_time: BlockTimeModel, info: GrapheneInfo) -> datetime:
        return block_time.block_time.lower

    @staticmethod
    async def resolve_end_time(block_time: BlockTimeModel, info: GrapheneInfo) -> datetime:
        return block_time.block_time.upper

    @staticmethod
    async def resolve_room(block_time: BlockTimeModel, info: GrapheneInfo) -> Optional[RoomModel]:
        return await info.context.room_loader.load(block_time.room_id)

    @staticmethod
    async def resolve_block(block_time: BlockTimeModel, info: GrapheneInfo) -> Optional[BlockModel]:
        return await info.context.block_loader.load(str(block_time.block_id))

    @staticmethod
    async def resolve_released_from(
        block_time: BlockTimeModel, info: GrapheneInfo
    ) -> Optional[str]:
        if block_time.released_from:
            block_time_model = await info.context.block_service.get_block_time(
                str(block_time.released_from)
            )
            block = await info.context.block_service.get_block(str(block_time_model.block_id))
            return block.name
        return None

    @staticmethod
    async def resolve_releases(
        block_time: BlockTimeModel, info: GrapheneInfo, include_unreleased: bool
    ) -> list[BlockTimeReleaseModel]:
        releases = await info.context.block_time_release_for_block_time_loader.load(
            str(block_time.id)
        )
        if include_unreleased:
            return releases
        return [release for release in releases if release.unreleased_time is None]

    @staticmethod
    async def resolve_surgeon_ids(
        block_time: BlockTimeModel, info: GrapheneInfo
    ) -> Optional[list[str]]:
        block = await info.context.block_loader.load(str(block_time.block_id))
        if block is None:
            raise GraphQLError(
                message=f"Unable to find block for block time with block id: {block_time.block_id}"
            )
        return block.surgeon_ids

    @staticmethod
    async def resolve_available_intervals(
        block_time: BlockTimeModel, info: GrapheneInfo
    ) -> list[BlockTimeAvailableInterval]:
        available_intervals = await info.context.block_service.get_block_time_available_intervals(
            block_time
        )
        return [
            BlockTimeAvailableInterval(
                id=interval.id,
                block_time_id=interval.block_time_id,
                start_time=interval.start_time,
                end_time=interval.end_time,
                room_id=interval.room_id,
                surgeon_ids=interval.surgeon_ids,
            )
            for interval in available_intervals
        ]


BlockTimeConnection = pagination_connection_factory(BlockTime)


class BlockTimeQueryInput(graphene.InputObjectType):
    site_id = graphene.ID(required=False)
    room_ids = graphene.List(graphene.NonNull(graphene.ID), required=False)
    min_end_time = graphene.DateTime(required=True)
    max_start_time = graphene.DateTime(required=True)
    block_id = graphene.String(required=False)

    def to_dto(self, room: Optional[RoomModel] = None) -> BlockTimeQueryDto:
        return BlockTimeQueryDto(
            room_id=room.id if room is not None else None,
            min_end_time=self.min_end_time,
            max_start_time=self.max_start_time,
        )

    def to_frozen_dto(self, room: RoomModel) -> FrozenRoomBlockTimesQueryDto:
        return FrozenRoomBlockTimesQueryDto(
            room_id=room.id,
            min_end_time=self.min_end_time,
            max_start_time=self.max_start_time,
        )


class BlockTimeAvailableIntervalInput(graphene.InputObjectType):
    site_id = graphene.ID(required=False)
    room_ids = graphene.List(graphene.NonNull(graphene.ID), required=False)
    min_end_time = graphene.DateTime(required=True)
    max_start_time = graphene.DateTime(required=True)


class BlockTimesBulkQueryInput(graphene.InputObjectType):
    ids = graphene.List(graphene.NonNull(graphene.ID), required=True)


class Block(graphene.ObjectType):
    id = graphene.ID(required=True)
    name = graphene.String(required=True)
    color = graphene.String(required=True)
    archived_time = graphene.DateTime()

    org_id = graphene.ID(required=True)
    site_ids = graphene.List(graphene.NonNull(graphene.ID), required=False)
    organization = graphene.Field(lambda: organization_schema.Organization, required=True)

    block_times = graphene.List(graphene.NonNull(BlockTime), required=True)

    surgeons = graphene.List(graphene.NonNull(lambda: staff_schema.Staff), required=True)
    surgeon_ids = graphene.List(graphene.NonNull(graphene.ID), required=True)

    @staticmethod
    async def resolve_organization(block: BlockModel, info: GrapheneInfo) -> OrganizationModel:
        organization: Optional[OrganizationModel] = await info.context.org_loader.load(block.org_id)
        if organization is None:
            raise GraphQLError(
                message=f"Unable to find organization for block with org id: {block.org_id}"
            )
        return organization

    @staticmethod
    async def resolve_block_times(block: BlockModel, info: GrapheneInfo) -> list[BlockTimeModel]:
        return await info.context.block_time_for_block_loader.load(str(block.id))

    @staticmethod
    async def resolve_surgeons(block: BlockModel, info: GrapheneInfo) -> list[StaffModel]:
        surgeons: list[Optional[StaffModel]] = await info.context.staff_loader.load_many(
            block.surgeon_ids
        )
        return [surgeon for surgeon in surgeons if surgeon is not None]

    @staticmethod
    async def resolve_surgeon_ids(block: BlockModel, info: GrapheneInfo) -> list[str]:
        return block.surgeon_ids


BlockConnection = pagination_connection_factory(Block)


class BlockQueryInput(graphene.InputObjectType):
    min_end_time = graphene.DateTime(required=False)
    max_start_time = graphene.DateTime(required=False)

    ids = graphene.List(graphene.NonNull(graphene.ID), required=False)
    names = graphene.List(graphene.NonNull(graphene.String), required=False)
    days_of_week = graphene.List(graphene.NonNull(graphene.Int), required=False)
    org_ids = graphene.List(graphene.NonNull(graphene.ID), required=False)
    site_ids = graphene.List(graphene.NonNull(graphene.ID), required=False)
    room_ids = graphene.List(graphene.NonNull(graphene.ID), required=False)
    surgeon_ids = graphene.List(graphene.NonNull(graphene.ID), required=False)

    include_archived = graphene.Boolean(required=False, default_value=False)

    def to_dto(self) -> BlockQueryDto:
        return BlockQueryDto(
            min_end_time=self.min_end_time,
            max_start_time=self.max_start_time,
            ids=self.ids,
            names=self.names,
            days_of_week=self.days_of_week,
            org_ids=self.org_ids,
            site_ids=self.site_ids,
            room_ids=self.room_ids,
            surgeon_ids=self.surgeon_ids,
            include_archived=self.include_archived,
        )
