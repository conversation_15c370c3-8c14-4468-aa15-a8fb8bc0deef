import base64
import json
import logging
from http import HTTPStatus

from typing import Dict, Any, Final
from fastapi import APIRouter, Request, Depends

from api_server.app_service_provider.apella_app_container import app_container
from api_server.services.block.block_release_processing_service import (
    BlockReleaseProcessingService,
)
from api_server.services.block.block_router import BlockRouter
from api_server.services.block.block_schedule_processing_service import (
    BlockScheduleProcessingService,
)
from auth.auth import Auth
from api_server.services.block.block_models import BlockFile
from api_server.services.block.block_csv_asset_store import BlockCsvAssetStore
from apella_cloud_api.exceptions import ClientError
from api_server.services.block.block_slack_notifier import BlockSlackNotifier

blocks_api_router: Final = APIRouter(prefix="/v1")

logger: Final = logging.getLogger(__name__)

UNKNOWN_ORG: Final = "unknown"
SUPPORTED_ORGS: Final = ["houston_methodist", "tampa_general"]


def get_auth() -> Auth:
    return app_container.provide_app_scope_type(Auth)


def get_block_release_processing_service() -> BlockReleaseProcessingService:
    return app_container.provide_app_scope_type(BlockReleaseProcessingService)


def get_block_schedule_processing_service() -> BlockScheduleProcessingService:
    return app_container.provide_app_scope_type(BlockScheduleProcessingService)


def get_block_router() -> BlockRouter:
    return BlockRouter(
        block_release_processing_service=get_block_release_processing_service(),
        block_schedule_processing_service=get_block_schedule_processing_service(),
    )


@blocks_api_router.post("/blocks/releases")
async def process_block_release_csv_upload(
    request: Request,
    block_router: BlockRouter = Depends(get_block_router),
) -> Dict[str, Any]:
    request_data = await request.body()
    slack_notifier = BlockSlackNotifier()

    if not request_data:
        raise ClientError(HTTPStatus.BAD_REQUEST, "Request data is required")

    try:
        envelope = json.loads(request_data.decode("utf-8"))
        message_data_bytes = base64.b64decode(envelope["message"]["data"])
        message_data = json.loads(message_data_bytes.decode("utf-8"))
        message_attributes = envelope["message"].get("attributes", {})
    except (json.JSONDecodeError, KeyError):
        logger.warning(f"Invalid request data", exc_info=True)
        await slack_notifier.log_error("Invalid request data")
        raise ClientError(HTTPStatus.BAD_REQUEST, "Invalid request data")

    if not message_data:
        await slack_notifier.log_error("Message data is required")
        raise ClientError(HTTPStatus.BAD_REQUEST, "Message data is required")

    # Check if the file already processed
    md5_hash = message_data.get("md5Hash")

    if not md5_hash:
        logger.warning(
            f"md5 hash is required. File name: {message_data.get('name', 'unknown')}", exc_info=True
        )
        raise ClientError(HTTPStatus.BAD_REQUEST, "md5 hash is required")

    # "apella/apella/apella_internal_0.csv" or "hmh/houston-methodist/releases.csv"
    file_name = message_data.get("name")
    if file_name is None:
        logger.warning("File name not provided in message attributes")
        await slack_notifier.log_error("Invalid file name")
        raise ClientError(HTTPStatus.INTERNAL_SERVER_ERROR, "Invalid file name")

    org_id = message_attributes.get("orgId", UNKNOWN_ORG)
    # Download the csv file
    bucket_name = message_data.get("bucket")
    csv_gs_url = f"gs://{bucket_name}/{file_name}"

    await slack_notifier.notify_file_upload(
        username=org_id,
        file_name=file_name,
        file_size=int(message_data.get("size", -1)),
    )

    try:
        processor = block_router.route(file_path=file_name)
        if processor:
            # Save the url and md5 hash of the processed file
            file = BlockFile(
                file_name=message_data.get("name"),
                bucket_name=message_data.get("bucket"),
                generation=message_data.get("generation"),
                size=message_data.get("size"),
                md5_hash=md5_hash,
                media_link=csv_gs_url,
                org_id=org_id,
            )
            if org_id in SUPPORTED_ORGS:
                await processor.save_and_process_file(file)
    except Exception as e:
        logger.error(f"Error occurred while processing block releases: {str(e)}", exc_info=True)
        await slack_notifier.log_error(str(e))
        raise e

    return {"message": "File processed"}


@blocks_api_router.post("/blocks/pre-releases")
async def process_block_release_csv_frontend(
    request: Request,
    auth: Auth = Depends(get_auth),
    block_release_processing_service: BlockReleaseProcessingService = Depends(
        get_block_release_processing_service
    ),
) -> Dict[str, Any]:
    request_data = await request.body()

    slack_notifier = BlockSlackNotifier()

    if not request_data:
        raise ClientError(HTTPStatus.BAD_REQUEST, "Request data is required")

    try:
        request_body = json.loads(request_data.decode("utf-8"))
        file_content = request_body.get("fileContent")
    except (json.JSONDecodeError, KeyError):
        logger.warning(f"Invalid request data", exc_info=True)
        await slack_notifier.log_error("Invalid request data")
        raise ClientError(HTTPStatus.BAD_REQUEST, "Invalid request data")

    if not file_content:
        logger.warning(f"fileContent is required. Request data: {request_body}", exc_info=True)
        raise ClientError(HTTPStatus.BAD_REQUEST, "fileContent is required")

    await slack_notifier.notify_file_upload(
        username=auth.get_calling_user_id(),
        file_size=len(file_content),
    )

    # Transform csv content to BlockReleaseBulkCreateMutation by using the correct transformer
    try:
        adapter = block_release_processing_service.auto_select_file_adapter(file_content)

        block_release_bulk_create_data_input = await adapter.transform(file_content, None)

        # Process the csv file
        await block_release_processing_service.process_releases(
            releases=block_release_bulk_create_data_input.releases,
        )

        # Upload releases to bucket
        signed_processed_media_link = await BlockCsvAssetStore().upload_processed_releases(
            releases=block_release_bulk_create_data_input.releases, source="upload"
        )

    except Exception as e:
        logger.error(f"Error occurred while processing block releases: {str(e)}", exc_info=True)
        await slack_notifier.log_error(str(e))
        raise e

    # Notify the slack channel
    await slack_notifier.report(
        releases=block_release_bulk_create_data_input.releases,
        uploader=auth.get_calling_user_id(),
        output_media_link=signed_processed_media_link,
    )

    return {
        "success": True,  # TODO: deprecate this field
        "releases": block_release_bulk_create_data_input.releases,
        "timezone": block_release_bulk_create_data_input.timezone,
        "org_id": block_release_bulk_create_data_input.org_id,
    }
