import asyncio
from collections import defaultdict
from dataclasses import dataclass
from datetime import date, datetime, time, timedelta
from itertools import chain
from typing import DefaultDict, Optional
import uuid
from zoneinfo import ZoneInfo

from apella_cloud_api.api_server_schema import BlockTime
from apella_cloud_api.dtos import AvailableSecondsForBlockTime, BlockUtilizationDto, CaseToBlock
from api_server.logging.audit import log_calls_to_audit
from api_server.services.block.block_models import BlockTimeInterval
from api_server.services.block.block_service import BlockService
from api_server.services.block_utilization.block_utilization_helpers import (
    get_block_time_intervals_by_day,
    get_block_time_overrides_by_block_time_id,
    get_block_times_by_day,
    get_cases_for_block_by_day,
)
from api_server.services.block_utilization.block_utilization_store import (
    BlockTimeOverrideModel,
    BlockUtilizationStore,
)
from api_server.services.block_utilization.constants import (
    RELEASE_CUTOFF_THRESHOLD,
    TURNOVER_SECONDS,
    VALID_SCORE_THRESHOLD,
)
from api_server.services.case_to_block.case_to_block_service import CaseToBlockService
from api_server.services.site.site_service import SiteService
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import READ_ANY_BLOCK, READ_ANY_CASE, WRITE_ANY_BLOCK


@dataclass
class BlockUtilizationService:
    auth: Auth
    block_service: BlockService
    case_to_block_service: CaseToBlockService
    site_service: SiteService
    block_utilization_store: BlockUtilizationStore

    async def _get_block_utilizations_by_day(
        self,
        cases_for_block_day: list[CaseToBlock],
        block_time_intervals: list[BlockTimeInterval],
        block_times: list[BlockTime],
        block_time_overrides_by_id: defaultdict[str, BlockTimeOverrideModel],
        day: date,
    ) -> list[BlockUtilizationDto]:
        total_actual_case_seconds_by_block = defaultdict(list)
        utilized_seconds_by_block = defaultdict(list)
        total_scheduled_case_seconds_by_block = defaultdict(list)
        utilized_scheduled_seconds_by_block = defaultdict(list)
        all_case_to_block_by_block = defaultdict(list)
        turnover_seconds_by_block = defaultdict(list)
        for case_to_block in cases_for_block_day:
            if (
                case_to_block.score >= VALID_SCORE_THRESHOLD
                and case_to_block.block_id.lower() != "unblocked"
            ):
                case_to_block_uuid = uuid.UUID(case_to_block.block_id)
                total_scheduled_case_seconds_by_block[case_to_block_uuid].append(
                    case_to_block.scheduled_case_seconds
                )
                utilized_scheduled_seconds_by_block[case_to_block_uuid].append(
                    case_to_block.utilized_scheduled_case_seconds
                    if case_to_block.utilized_scheduled_case_seconds
                    else case_to_block.scheduled_case_seconds
                )

                if case_to_block.actual_case_seconds:
                    total_actual_case_seconds_by_block[case_to_block_uuid].append(
                        case_to_block.actual_case_seconds
                    )

                utilized_seconds_by_block[case_to_block_uuid].append(
                    case_to_block.overridden_utilized_case_seconds
                    if case_to_block.overridden_utilized_case_seconds
                    else case_to_block.utilized_case_seconds
                    if case_to_block.utilized_case_seconds
                    else 0
                )

                all_case_to_block_by_block[case_to_block_uuid].append(case_to_block)

                turnover_seconds_for_case = (
                    case_to_block.overridden_turnover_seconds
                    if case_to_block.overridden_turnover_seconds is not None
                    else TURNOVER_SECONDS
                )
                turnover_seconds_by_block[case_to_block_uuid].append(turnover_seconds_for_case)

        total_block_durations_by_block = defaultdict(list)
        available_durations_by_block: DefaultDict[uuid.UUID, DefaultDict[str, list[timedelta]]] = (
            defaultdict(lambda: defaultdict(list))
        )
        overridden_block_time_ids = list(block_time_overrides_by_id.keys())
        for block_time in block_times:
            total_block_durations_by_block[block_time.block_id].append(
                block_time.end_time - block_time.start_time
            )
            if str(block_time.id) in overridden_block_time_ids:
                override_block_time = block_time_overrides_by_id[str(block_time.id)]
                available_durations_by_block[block_time.block_id][str(block_time.id)].append(
                    timedelta(minutes=override_block_time.block_time_minutes)
                )

        for block_time_interval in block_time_intervals:
            if block_time_interval.block_time_id not in overridden_block_time_ids:
                available_durations_by_block[block_time_interval.block_id][
                    block_time_interval.block_time_id
                ].append(block_time_interval.end_time - block_time_interval.start_time)

        block_ids = total_block_durations_by_block.keys()
        block_utilizations = []
        for block_id in block_ids:
            utilized_seconds = (
                sum(
                    utilized_seconds_by_block[block_id] + turnover_seconds_by_block[block_id],
                    0,
                )
                if block_id in utilized_seconds_by_block
                else 0
            )
            total_actual_case_seconds = (
                sum(total_actual_case_seconds_by_block[block_id], 0)
                if block_id in total_actual_case_seconds_by_block
                else 0
            )

            utilized_scheduled_seconds = (
                sum(utilized_scheduled_seconds_by_block[block_id], 0)
                if block_id in utilized_scheduled_seconds_by_block
                else 0
            )
            total_scheduled_case_seconds = (
                sum(total_scheduled_case_seconds_by_block[block_id], 0)
                if block_id in total_scheduled_case_seconds_by_block
                else 0
            )

            available_seconds = 0
            available_seconds_by_block_time_id: list[AvailableSecondsForBlockTime] = []
            if block_id in available_durations_by_block:
                for block_time_id in available_durations_by_block[block_id]:
                    available_seconds_for_block_time_id = int(
                        (
                            sum(available_durations_by_block[block_id][block_time_id], timedelta(0))
                        ).total_seconds()
                    )
                    available_seconds += available_seconds_for_block_time_id
                    available_seconds_by_block_time_id.append(
                        AvailableSecondsForBlockTime(
                            block_time_id=block_time_id,
                            available_seconds=available_seconds_for_block_time_id,
                            overridden=block_time_id in overridden_block_time_ids,
                        )
                    )

            total_block_seconds = int(
                (
                    sum(total_block_durations_by_block[block_id], timedelta(0))
                    if block_id in total_block_durations_by_block
                    else timedelta(0)
                ).total_seconds()
            )

            block_utilizations.append(
                BlockUtilizationDto(
                    date=day,
                    utilized_seconds=utilized_seconds,
                    total_actual_case_seconds=total_actual_case_seconds,
                    utilized_scheduled_seconds=utilized_scheduled_seconds,
                    total_scheduled_case_seconds=total_scheduled_case_seconds,
                    available_seconds=available_seconds,
                    total_block_seconds=total_block_seconds,
                    block_id=block_id,
                    cases_for_block_day=all_case_to_block_by_block[block_id]
                    if block_id in all_case_to_block_by_block
                    else [],
                    total_case_seconds=0,
                    available_seconds_by_block_time_id=available_seconds_by_block_time_id,
                )
            )
        return block_utilizations

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE, READ_ANY_BLOCK)
    async def get_block_utilization_for_site(
        self, min_date: datetime, max_date: datetime, site_id: str, block_id: Optional[str] = None
    ) -> list[BlockUtilizationDto]:
        site = await self.site_service.get_site(site_id)
        (
            all_cases_to_blocks,
            all_block_time_intervals,
            all_block_times,
        ) = await asyncio.gather(
            self.case_to_block_service.get_cases_to_blocks_for_date_range(
                min_date=min_date,
                max_date=max_date,
                site_id=site_id,
                exclude_cancelled=True,
                block_id=block_id,
            ),
            self.block_service.query_block_times_available_intervals(
                site_id=site_id,
                min_end_time=datetime.combine(min_date, time.min, tzinfo=ZoneInfo(site.timezone)),
                max_start_time=datetime.combine(max_date, time.max, tzinfo=ZoneInfo(site.timezone)),
                release_cutoff=RELEASE_CUTOFF_THRESHOLD,
                block_id=block_id,
            ),
            self.block_service.query_block_times(
                site_id=site_id,
                room_ids=None,
                min_end_time=datetime.combine(min_date, time.min, tzinfo=ZoneInfo(site.timezone)),
                max_start_time=datetime.combine(max_date, time.max, tzinfo=ZoneInfo(site.timezone)),
                block_id=block_id,
            ),
        )

        all_block_time_overrides = await self.block_utilization_store.get_block_time_overrides(
            block_time_ids=[block_time.id for block_time in all_block_times]
        )

        block_times_by_day = get_block_times_by_day(all_block_times, site.timezone_info)
        block_time_overrides_by_id = get_block_time_overrides_by_block_time_id(
            all_block_time_overrides
        )

        block_time_intervals_by_day = get_block_time_intervals_by_day(
            all_block_time_intervals, site.timezone_info
        )
        cases_for_block_by_day = get_cases_for_block_by_day(all_cases_to_blocks)

        # in parallel get block utilizations for each day for each block
        block_utilizations = await asyncio.gather(
            *[
                self._get_block_utilizations_by_day(
                    cases_for_block_day=cases_for_block_by_day[day],
                    block_times=block_times_by_day[day],
                    block_time_intervals=block_time_intervals_by_day[day],
                    block_time_overrides_by_id=block_time_overrides_by_id,
                    day=day,
                )
                for day in block_times_by_day.keys()
            ]
        )
        return list(chain(*block_utilizations))

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def upsert_block_time_overrides(
        self, block_time_overrides: list[BlockTimeOverrideModel]
    ) -> None:
        return await self.block_utilization_store.upsert_block_time_overrides(
            block_time_overrides=block_time_overrides
        )
