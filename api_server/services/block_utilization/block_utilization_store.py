import uuid
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String, Integer, select
from databases.sql import Base, new_async_session
from sqlalchemy.orm import mapped_column, Mapped

from databases.sql.mixins import TimestampMixin
from utils.history_meta import Versioned


class BlockTimeOverrideModel(Base, TimestampMixin, Versioned):
    __tablename__ = "block_time_overrides"

    block_time_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("block_times.id"), nullable=False, primary_key=True
    )

    block_time_minutes: Mapped[int] = mapped_column(Integer, nullable=False)

    user_id: Mapped[str] = mapped_column(String, nullable=False)

    note: Mapped[str] = mapped_column(String, nullable=True)


BlockTimeOverrideHistoryModel = BlockTimeOverrideModel.__history_mapper__.class_  # type: ignore [attr-defined]


class BlockUtilizationStore:
    async def get_block_time_overrides(
        self, block_time_ids: list[uuid.UUID]
    ) -> list[BlockTimeOverrideModel]:
        async with new_async_session() as session:
            query = select(BlockTimeOverrideModel).filter(
                BlockTimeOverrideModel.block_time_id.in_(block_time_ids)
            )

            results = await session.scalars(query)
            return list(results.all())

    async def upsert_block_time_overrides(
        self, block_time_overrides: list[BlockTimeOverrideModel]
    ) -> None:
        async with new_async_session() as session:
            for override in block_time_overrides:
                await session.merge(override)
            await session.commit()
            return

    async def get_block_time_override_history(
        self, block_time_id: uuid.UUID
    ) -> list[BlockTimeOverrideModel]:
        async with new_async_session() as session:
            base_results = list(
                await session.scalars(
                    select(BlockTimeOverrideHistoryModel).filter(
                        BlockTimeOverrideHistoryModel.block_time_id == block_time_id
                    )
                )
            )
            history_results = []
            for override_history in base_results:
                # For each history row, we create a new BlockTimeOverrideModel instance
                override = BlockTimeOverrideModel()
                # And use dict to copy all of the properties except for `_sa_instance_state`
                _sa_instance_state = override.__dict__["_sa_instance_state"]
                override.__dict__ = override_history.__dict__
                override.__dict__["_sa_instance_state"] = _sa_instance_state
                history_results.append(override)
            return history_results
