from typing import Any, List
import graphene
from graphql import GraphQLError

from apella_cloud_api.exceptions import ClientError
from api_server.graphql.context import GrapheneInfo
from api_server.services.block_utilization.block_utilization_store import BlockTimeOverrideModel


class BlockTimeOverrideUpsertInput(graphene.InputObjectType):
    block_time_id = graphene.UUID(required=True)
    user_id = graphene.String(required=True)
    block_time_minutes = graphene.Int(required=True)
    note = graphene.String()


class BlockTimeOverridesUpsert(graphene.Mutation):
    class Arguments:
        input = graphene.NonNull(graphene.List(graphene.NonNull(BlockTimeOverrideUpsertInput)))

    success = graphene.Boolean()

    @staticmethod
    async def mutate(
        parent: Any, info: GrapheneInfo, input: List[BlockTimeOverrideUpsertInput]
    ) -> "BlockTimeOverridesUpsert":
        try:
            block_time_override_to_create = [
                BlockTimeOverrideModel(
                    block_time_id=block_time_override.block_time_id,
                    user_id=block_time_override.user_id,
                    block_time_minutes=block_time_override.block_time_minutes,
                    note=block_time_override.note,
                )
                for block_time_override in input
            ]

            await info.context.block_utilization_service.upsert_block_time_overrides(
                block_time_overrides=block_time_override_to_create
            )
            return BlockTimeOverridesUpsert(success=True)
        except ClientError as e:
            raise GraphQLError(e.message)
