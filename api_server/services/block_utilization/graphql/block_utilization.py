from typing import Optional
import graphene
from graphql import GraphQLError

from apella_cloud_api.dtos import BlockUtilizationDto
from api_server.graphql.context import GrapheneInfo
from api_server.services.block.block_models import BlockModel
from api_server.services.block.graphql.blocks import Block
from api_server.services.case_to_block.graphql.case_to_block import CaseToBlock


class BlockUtilizationInput(graphene.InputObjectType):
    min_date = graphene.DateTime(required=True)
    max_date = graphene.DateTime(required=True)
    site_id = graphene.String(required=True)
    block_id = graphene.String()


class AvailableSecondsForBlockTime(graphene.ObjectType):
    block_time_id = graphene.String(required=True)
    available_seconds = graphene.Int(required=True)
    overridden = graphene.Boolean(required=True)


class BlockUtilization(graphene.ObjectType):
    date = graphene.Date(required=True)
    block_id = graphene.String(required=True)
    cases_for_block_day = graphene.List(graphene.NonNull(CaseToBlock), required=True)
    utilized_scheduled_seconds = graphene.Int(required=True)
    total_scheduled_case_seconds = graphene.Int(required=True)
    utilized_seconds = graphene.Int(required=True)
    total_case_seconds = graphene.Int()  # to deprecate
    total_actual_case_seconds = graphene.Int(required=True)
    available_seconds = graphene.Int(required=True)
    total_block_seconds = graphene.Int(required=True)
    available_seconds_by_block_time_id = graphene.List(
        graphene.NonNull(AvailableSecondsForBlockTime), required=True
    )

    block = graphene.Field(lambda: Block, required=True)

    @staticmethod
    async def resolve_block(parent: BlockUtilizationDto, info: GrapheneInfo) -> BlockModel:
        block: Optional[BlockModel] = await info.context.block_loader.load(str(parent.block_id))
        if block is None:
            raise GraphQLError(message=f"Unable to find block with block id: {parent.block_id}")
        return block
