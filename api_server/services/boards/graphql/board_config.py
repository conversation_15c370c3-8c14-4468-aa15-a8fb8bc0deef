from typing import Optional

import graphene
from graphql import GraphQLError

from apella_cloud_api.exceptions import NotFound
from api_server.graphql.context import GrapheneInfo
from api_server.graphql.pagination.fields import pagination_connection_factory
from api_server.services.boards.board_store import BoardConfigModel, BoardViewType
from api_server.services.organization.graphql.organization import Organization
from api_server.services.room.graphql.room import Room
from api_server.services.room.room_store import RoomModel
from api_server.services.site.graphql.site import Site
from api_server.services.site.site_store import Site as SiteModel
from api_server.services.organization.organization_db import Organization as OrganizationModel
from api_server.services.users.user_store import User
from api_server.services.users.graphql.user_type import User as UserType

BoardViewTypeGraphene = graphene.Enum.from_enum(BoardViewType)


def get_board_view_type_enum(board_view_type: int) -> BoardViewType:
    # Graphene parses the enum value (usually an int), rather than the enum itself.
    # So we need to convert the value and do some null checking. Graphene 3.0 fixes this.
    # https://github.com/graphql-python/graphene/issues/1151
    return None if board_view_type is None else BoardViewType(board_view_type)


class BoardConfigQueryInput(graphene.InputObjectType):
    site_ids = graphene.List(graphene.NonNull(graphene.String), required=False)
    room_ids = graphene.List(graphene.NonNull(graphene.String), required=False)
    organization_ids = graphene.List(graphene.NonNull(graphene.String), required=False)


class BoardConfig(graphene.ObjectType):
    id = graphene.ID(required=True)
    name = graphene.String(required=True)
    page_size = graphene.Int(required=True)
    page_duration = graphene.Int(required=True)
    blur_video = graphene.Boolean(required=True)
    updated_time = graphene.DateTime(required=True)
    updated_by_user = graphene.Field(lambda: UserType, required=False)
    site = graphene.Field(lambda: Site, required=True)
    organization = graphene.Field(lambda: Organization, required=True)
    rooms = graphene.Field(graphene.List(graphene.NonNull(lambda: Room)), required=False)
    enable_video = graphene.Boolean(required=True)
    board_view_type = graphene.Field(BoardViewTypeGraphene, required=True)
    zoom_percent = graphene.Int(required=True)
    show_closed_rooms = graphene.Boolean(required=False)

    @staticmethod
    async def resolve_updated_by_user(
        board_config: BoardConfigModel,
        info: GrapheneInfo,
    ) -> Optional[User]:
        try:
            user = await info.context.user_loader.load(board_config.updated_by_user_id)
            assert isinstance(user, User)
            return user
        except NotFound:
            return None

    @staticmethod
    async def resolve_site(
        board_config: BoardConfigModel,
        info: GrapheneInfo,
    ) -> SiteModel:
        site: Optional[SiteModel] = await info.context.site_loader.load(board_config.site_id)
        if site is None:
            raise GraphQLError(
                message=f"Unable to find site for board config with site id: {board_config.site_id}"
            )
        return site

    @staticmethod
    async def resolve_rooms(
        board_config: BoardConfigModel,
        info: GrapheneInfo,
    ) -> Optional[list[RoomModel]]:
        return await info.context.board_rooms_loader.load(str(board_config.id))

    @staticmethod
    async def resolve_organization(
        board_config: BoardConfigModel,
        info: GrapheneInfo,
    ) -> OrganizationModel:
        organization: Optional[OrganizationModel] = await info.context.org_loader.load(
            str(board_config.org_id)
        )
        if organization is None:
            raise GraphQLError(
                message=f"Unable to find organization for board config with org id: {board_config.org_id}"
            )
        return organization


BoardConfigConnection = pagination_connection_factory(BoardConfig)
