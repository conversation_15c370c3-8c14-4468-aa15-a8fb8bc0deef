from dataclasses import dataclass
from typing import Any, List, Optional, Sequence, Iterable


from api_server.logging.audit import log_calls_to_audit
from api_server.services.camera.camera_store import (
    CameraModel,
    CameraStore,
    PatientBoundingBoxModel,
    PatientBoundingBoxStore,
)
from auth.auth import Auth
from auth.auth_decorator import requires_permission_prefix, requires_permissions
from auth.permissions import READ_ANY_CAMERA, READ_CAMERA_PREFIX, WRITE_ANY_CAMERA
from databases.sql import new_async_session


@dataclass
class CameraService:
    camera_store: CameraStore
    auth: Auth
    patient_bounding_box_store: PatientBoundingBoxStore

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_CAMERA, enforce_universal_user=True)
    async def create_camera(self, camera: CameraModel) -> CameraModel:
        async with new_async_session() as session:
            return await self.camera_store.create_camera(camera, session)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_CAMERA, enforce_universal_user=True)
    async def create_cameras(self, cameras: List[CameraModel]) -> List[CameraModel]:
        return await self.camera_store.create_cameras(cameras)

    @log_calls_to_audit()
    @requires_permission_prefix(READ_CAMERA_PREFIX)
    async def get_camera(self, camera_id: str) -> Optional[CameraModel]:
        if self.auth.has_permission(READ_ANY_CAMERA):
            return await self.camera_store.get_camera(camera_id=camera_id)
        else:
            default_cameras = {
                camera.id: camera for camera in await self.get_cameras(keys=[camera_id])
            }
            return default_cameras.get(camera_id, None)

    @log_calls_to_audit()
    @requires_permission_prefix(READ_CAMERA_PREFIX)
    async def get_cameras(self, keys: List[str]) -> Iterable[CameraModel]:
        if self.auth.has_permission(READ_ANY_CAMERA):
            return await self.camera_store.get_cameras(camera_ids=keys)
        else:
            return await self.camera_store.get_user_room_default_cameras(
                camera_ids=keys, user_id=self.auth.get_calling_user_id()
            )

    @log_calls_to_audit()
    @requires_permission_prefix(READ_CAMERA_PREFIX)
    async def get_camera_ids_in_room(self, room_id: str) -> List[str]:
        cameras_in_room = await self.get_cameras_in_room(room_ids=[room_id])
        return [camera.id for camera in cameras_in_room]

    @log_calls_to_audit()
    @requires_permission_prefix(READ_CAMERA_PREFIX)
    async def get_cameras_in_room(
        self, room_ids: Optional[List[str]] = None
    ) -> Iterable[CameraModel]:
        if self.auth.has_permission(READ_ANY_CAMERA):
            return await self.camera_store.get_cameras_in_room(room_ids=room_ids)
        return await self.get_user_room_default_cameras(room_ids=room_ids)

    @log_calls_to_audit()
    @requires_permission_prefix(READ_CAMERA_PREFIX)
    async def get_user_room_default_cameras(
        self, room_ids: Optional[List[str]]
    ) -> Iterable[CameraModel]:
        return await self.camera_store.get_user_room_default_cameras(
            room_ids=room_ids, user_id=self.auth.get_calling_user_id()
        )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CAMERA)
    async def query_cameras(
        self,
        camera_ids: Optional[List[str]] = None,
        families: Optional[List[str]] = None,
        room_ids: Optional[List[str]] = None,
        site_ids: Optional[List[str]] = None,
        org_id: Optional[str] = None,
    ) -> Sequence[CameraModel]:
        return await self.camera_store.query_cameras(
            camera_ids=camera_ids,
            families=families,
            room_ids=room_ids,
            site_ids=site_ids,
            org_id=org_id,
        )

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_CAMERA, enforce_universal_user=True)
    async def patch_camera(
        self,
        camera_id: str,
        camera_name: Optional[str],
        rtsp_url: Optional[str],
        labels: Optional[dict[str, Any]],
        family: Optional[str],
    ) -> CameraModel:
        camera = await self.camera_store.get_camera(camera_id=camera_id)
        # Fields which are None, are not meant to be updated.
        if camera_name is not None:
            camera.name = camera_name
        if rtsp_url is not None:
            camera.rtsp_url = rtsp_url
        if labels is not None:
            camera.labels = labels
        if family is not None:
            camera.family = family
        return await self.camera_store.replace_camera(camera)

    @log_calls_to_audit()
    @requires_permission_prefix(READ_CAMERA_PREFIX)
    async def get_patient_bounding_box(self, camera_id: str) -> PatientBoundingBoxModel:
        box = await self.patient_bounding_box_store.get_camera_static_blur_box(camera_id)
        if box is None:
            box = PatientBoundingBoxModel(
                camera_id=camera_id,
                leftPct=0.28,
                bottomPct=0.19,
                widthPct=0.5,
                heightPct=0.46,
            )
        return box
