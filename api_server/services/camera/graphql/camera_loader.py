from typing import Optional

from aiodataloader import DataLoader

from api_server.services.camera.camera_service import CameraService
from api_server.services.camera.camera_store import CameraModel
from api_server.services.utils.loader.util_functions import sort_loader_results


class CameraLoader(DataLoader[str, Optional[CameraModel]]):
    camera_service: CameraService

    def __init__(self, camera_service: CameraService):
        super(CameraLoader, self).__init__()
        self.camera_service = camera_service

    async def batch_load_fn(self, keys: list[str]) -> list[Optional[CameraModel]]:
        cameras = list(await self.camera_service.get_cameras(keys))
        sorted_results = sort_loader_results(keys, cameras)

        return sorted_results
