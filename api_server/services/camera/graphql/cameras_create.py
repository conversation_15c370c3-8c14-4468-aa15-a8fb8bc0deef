from __future__ import annotations

from typing import Any, List

import graphene
from graphql import GraphQLError

import api_server.services.camera.graphql.camera as camera_schema
from apella_cloud_api.exceptions import ClientError
from api_server.graphql.context import GrapheneInfo
from api_server.services.camera.camera_store import CameraModel
from api_server.services.camera.graphql.camera_create import CameraCreateInput


class CamerasCreate(graphene.Mutation):
    class Arguments:
        input = graphene.List(graphene.NonNull(CameraCreateInput), required=True)

    success = graphene.Boolean()
    created_cameras = graphene.List(camera_schema.Camera)

    @staticmethod
    async def mutate(_: Any, info: GrapheneInfo, input: List[CameraCreateInput]) -> CamerasCreate:
        cameras: List[CameraModel] = []
        for camera_input in input:
            camera: CameraModel = CameraModel()
            camera.id = camera_input.id
            camera.name = camera_input.name
            camera.org_id = camera_input.organization_id
            camera.site_id = camera_input.site_id
            camera.room_id = camera_input.room_id
            camera.rtsp_url = camera_input.rtsp_url
            camera.labels = camera_input.labels
            camera.family = camera_input.family
            cameras.append(camera)

        try:
            new_cameras = await info.context.camera_service.create_cameras(cameras)

            return CamerasCreate(success=True, created_cameras=new_cameras)
        except ClientError as e:
            raise GraphQLError(e.message)
