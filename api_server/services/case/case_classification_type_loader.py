from typing import Optional
from aiodataloader import DataLoader
from api_server.services.case.case_classification_type_service import CaseClassificationTypeService
from api_server.services.case.case_classification_type_store import CaseClassificationTypeModel
from api_server.services.utils.loader.util_functions import sort_loader_results


class CaseClassificationTypeLoader(DataLoader[str, Optional[CaseClassificationTypeModel]]):
    case_classification_type_service: CaseClassificationTypeService

    def __init__(self, case_classification_type_service: CaseClassificationTypeService):
        super().__init__()
        self.case_classification_type_service = case_classification_type_service

    async def batch_load_fn(self, keys: list[str]) -> list[Optional[CaseClassificationTypeModel]]:
        case_classification_types = (
            await self.case_classification_type_service.get_case_classification_types(ids=keys)
        )
        return sort_loader_results(keys, case_classification_types)
