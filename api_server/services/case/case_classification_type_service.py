from dataclasses import dataclass
from typing import Optional
from api_server.logging.audit import log_calls_to_audit
from api_server.services.case.case_classification_type_store import (
    CaseClassificationTypeModel,
    CaseClassificationTypeStore,
)
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import READ_ANY_CASE


@dataclass
class CaseClassificationTypeService:
    auth: Auth
    case_classification_type_store: CaseClassificationTypeStore

    @requires_permissions(READ_ANY_CASE)
    @log_calls_to_audit()
    async def get_case_classification_types(
        self, ids: Optional[list[str]] = None
    ) -> list[CaseClassificationTypeModel]:
        return await self.case_classification_type_store.get_case_classification_types(ids=ids)
