import uuid
from typing import Collection
from uuid import uuid4

import graphene
import sqlalchemy
from sqlalchemy import UniqueConstraint, String, ForeignKey, select
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column

from databases.sql import Base, new_async_session
from databases.sql.enums import CaseMatchingStatus
from databases.sql.mixins import TimestampMixin
from utils.history_meta import Versioned

CaseMatchingStatusGraphene = graphene.Enum.from_enum(CaseMatchingStatus)


class CaseMatchingStatusReasonModel(Base, TimestampMixin, Versioned):
    __tablename__ = "case_matching_status_reason"
    __table_args__ = (  # type: ignore
        UniqueConstraint(
            "case_id",
            name="uq_caseId_matching_status_reason",
        ),
    )
    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    explanation_for_change: Mapped[str] = mapped_column(String, nullable=False, default=str)
    case_id: Mapped[str] = mapped_column(
        String, ForeignKey("cases.case_id", ondelete="CASCADE"), nullable=False, index=True
    )

    case_matching_status: Mapped[CaseMatchingStatus] = mapped_column(
        sqlalchemy.Enum(CaseMatchingStatus),
        nullable=False,
        index=True,
        default=CaseMatchingStatus.AUTOMATIC,
    )


class CaseMatchingStore:
    async def set_cases_matching_status(
        self, matching_status_map: Collection[CaseMatchingStatusReasonModel]
    ) -> list[CaseMatchingStatusReasonModel]:
        """
        Set the matching status for cases using the given map of case_id to matching status
        """
        updated_case_matching_statuses: list[CaseMatchingStatusReasonModel] = []
        async with new_async_session() as session:
            for case_matching_status_with_reason in matching_status_map:
                updated_case_matching_statuses.append(
                    await session.merge(case_matching_status_with_reason)
                )

            await session.commit()
            for case in updated_case_matching_statuses:
                await session.refresh(case)
            return updated_case_matching_statuses

    async def get_case_matching_reasons(
        self,
        case_ids: Collection[str],
    ) -> list[CaseMatchingStatusReasonModel]:
        """
        Get the Matching Status Reasons for a given collection of case ids
        """
        query = select(CaseMatchingStatusReasonModel).filter(
            CaseMatchingStatusReasonModel.case_id.in_(case_ids),
        )
        async with new_async_session() as session:
            results = await session.scalars(query)
            return list(results.all())
