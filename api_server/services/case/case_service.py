# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs
import asyncio
import logging
import uuid
from dataclasses import dataclass
from datetime import datetime
from http import HTTPStatus
from typing import Any, List, Optional, Union, Collection, Sequence
from uuid import uuid4
from zoneinfo import ZoneInfo

from apella_cloud_api import CaseSearchDto, CaseUpdateDto
from apella_cloud_api.dtos import CaseRawDto, EventChangeDto, MatchCaseDTO, EventChangelogAction
from apella_cloud_api.exceptions import ClientError, NotFound
from api_server.logging.audit import log_calls_to_audit
from api_server.services.case.case_classification_type_store import (
    CaseClassificationTypeModel,
    CaseClassificationTypeStore,
)
from api_server.services.case.case_flag_store import CaseFlagStore, CaseFlagQueryDto, CaseFlagModel
from api_server.services.case.case_procedure_store import (
    CaseProcedureModel,
    CaseProcedureStore,
)
from api_server.services.case.case_staff_store import CaseStaffModel, CaseStaffStore, CaseStaffQuery
from api_server.services.case.case_store import (
    Case,
    CaseRaw,
    CaseStore,
)
from api_server.services.case.case_matching_store import (
    CaseMatchingStatusReasonModel,
    CaseMatchingStore,
)
from databases.sql.enums import CaseMatchingStatus
from api_server.services.case_derived_properties.case_derived_properties_service import (
    CaseDerivedPropertiesService,
)
from api_server.services.case_derived_properties.case_derived_properties_store import (
    ProcessCaseDerivedPropertiesDto,
)
from api_server.services.ehr_interfaces.ehr_interfaces_models import (
    CaseEhrMessagesQuery,
)
from api_server.services.events.event_pubsub_store import EventPubSubStore
from api_server.services.phases.phase_service import PhaseService
from api_server.services.phases.phase_store import PhaseModel, PhaseQuery
from api_server.services.service_lines.service_line_store import ServiceLineStore, ServiceLineModel
from api_server.services.site.site_models import SiteQuery
from api_server.services.site.site_service import SiteService
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import (
    READ_ANY_CASE,
    WRITE_ANY_CASE,
    WRITE_ANY_CASE_STAFF_PLAN,
    READ_ANY_CASE_STAFF_PLAN,
    EDIT_DASHBOARD_SCHEDULE,
)
from databases.sql.mixins import EventMatchingStatus


@dataclass
class CaseService:
    case_store: CaseStore
    case_procedure_store: CaseProcedureStore
    auth: Auth
    case_staff_store: CaseStaffStore
    service_line_store: ServiceLineStore
    case_classification_type_store: CaseClassificationTypeStore
    case_derived_properties_service: CaseDerivedPropertiesService
    case_flag_store: CaseFlagStore
    event_pub_sub_store: EventPubSubStore
    phase_service: PhaseService
    site_service: SiteService
    case_matching_store: CaseMatchingStore

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE)
    async def get_case(self, case_id: str) -> Case:
        return await self.case_store.get_case(case_id=case_id)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE)
    async def get_case_by_external_id(self, external_case_id: str, org_id: str) -> Case:
        return await self.case_store.get_case_by_external_id(
            external_case_id=external_case_id, org_id=org_id
        )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE)
    async def get_cases(self, case_ids: List[str]):
        return await self.case_store.get_cases(case_ids=case_ids)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_CASE, enforce_universal_user=True)
    async def upsert_case(self, dto: CaseUpdateDto):
        if dto.case is None:
            raise ClientError(HTTPStatus.UNPROCESSABLE_ENTITY, message="'case' must be provided")

        if dto.case.organization_id is None:
            raise ClientError(
                HTTPStatus.UNPROCESSABLE_ENTITY, "organization_id is required to upsert a case"
            )

        if dto.case.case_id is None:
            # For an upsert, we need a case_id, and if it is not known, we may have to find it
            if dto.case.external_case_id is None:
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    "Either 'case_id' or 'external_case_id' is required to upsert a case",
                )
            else:
                try:
                    case = await self.case_store.get_case_by_external_id(
                        external_case_id=dto.case.external_case_id, org_id=dto.case.organization_id
                    )
                    dto.case.case_id = case.case_id
                except NotFound:
                    # There is no case for this case_id yet, that is fine
                    dto.case.case_id = str(uuid4())

        # Apply the patch to the current state
        case = Case().apply_dto(dto.case)
        if dto.case.external_service_line_id is not None:
            try:
                case.service_line_id = (
                    await self.service_line_store.get_service_line(
                        org_id=dto.case.organization_id,
                        external_service_line_id=dto.case.external_service_line_id,
                    )
                ).id
            except NotFound:
                service_line: ServiceLineModel = ServiceLineModel()
                service_line.org_id = dto.case.organization_id
                service_line.external_service_line_id = dto.case.external_service_line_id
                case.service_line_id = (
                    await self.service_line_store.create_or_update_service_line(service_line)
                ).id

        case.validate()
        case = await self.case_store.replace_case(case=case)

        # Eventually we'll want to offload this processing to be done async, but as of 4/20/23 we only have
        # one property so we're processing it synchronously on every case upsert
        await self.case_derived_properties_service.process_case_properties(
            [ProcessCaseDerivedPropertiesDto(site_id=case.site_id, date=case.scheduled_start_time)]
        )

        # Associate the case_raw entry with this case_id. Ideally this is done as a separate API.
        await self.case_store.add_case_id_to_raw(
            case_external_message_id=dto.external_message_id, case_id=case.case_id
        )

        return case

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_CASE)
    async def set_cases_matching_status(
        self, matching_status_map: Collection[CaseMatchingStatusReasonModel]
    ):
        """
        Set the matching status for cases using the provided map of case_id to matching status
        """
        return await self.case_matching_store.set_cases_matching_status(matching_status_map)

    # Since this raw data might have private data that we don't normally expose
    # We only really want super-admins / service accounts to be able to read it
    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE, enforce_universal_user=True)
    async def get_case_raw(
        self, external_case_id: str, org_id: str
    ) -> List[Union[str, dict[str, Any]]]:
        return await self.case_store.get_all_case_raw_for_external_case_id(
            external_case_id=external_case_id, org_id=org_id
        )

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_CASE, enforce_universal_user=True)
    async def create_case_raw(self, case_raw_dto: CaseRawDto):
        case_raw = CaseRaw().from_dto(case_raw_dto)
        return (await self.case_store.upsert_case_raw(case_raw)).to_dto()

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE)
    async def query_cases(self, search_query: CaseSearchDto) -> List[Case]:
        # Need to keep site_id and room_id for backwards compatability.
        if search_query.site_id is not None:
            search_query.site_ids = [search_query.site_id]
        if search_query.room_id is not None:
            search_query.room_ids = [search_query.room_id]
        return await self.case_store.query_cases(
            search_query,
        )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE)
    async def query_cases_history(self, search_query: CaseSearchDto) -> List[Case]:
        return await self.case_store.query_cases_history(
            search_query,
        )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE, enforce_universal_user=True)
    async def query_ehr_messages(self, ehr_messages_query: CaseEhrMessagesQuery) -> List[CaseRaw]:
        return await self.case_store.query_case_raw(case_raw_query=ehr_messages_query)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_CASE, enforce_universal_user=True)
    async def upsert_case_procedures(self, case_procedures: List[CaseProcedureModel]) -> None:
        await self.case_procedure_store.upsert_case_procedures(case_procedures=case_procedures)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE, enforce_universal_user=True)
    async def get_case_procedure_relationships_for_cases(
        self, case_ids: List[str]
    ) -> List[CaseProcedureModel]:
        return await self.case_procedure_store.get_case_procedure_relationships_for_cases(
            case_ids=case_ids
        )

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_CASE, enforce_universal_user=True)
    async def upsert_case_staff(self, case_staff: List[CaseStaffModel]) -> None:
        await self.case_staff_store.upsert_case_staff(case_staff=case_staff)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE, enforce_universal_user=True)
    async def get_case_staff_relationships_for_cases(
        self, case_ids: List[str], only_primary_surgeons: Optional[bool] = False
    ) -> List[CaseStaffModel]:
        return await self.case_staff_store.get_case_staff_relationships_for_cases(
            CaseStaffQuery(case_ids=case_ids, only_primary_surgeons=only_primary_surgeons)
        )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE)
    async def get_case_classification_types(
        self, ids: Optional[List[str]] = None
    ) -> List[CaseClassificationTypeModel]:
        return await self.case_classification_type_store.get_case_classification_types(ids)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_CASE_STAFF_PLAN)
    async def upsert_case_flags(self, case_flags: list[CaseFlagModel]) -> list[CaseFlagModel]:
        case_flag_results: list[CaseFlagModel] = []
        for case_flag in case_flags:
            self.auth.check_resource_matches_auth(
                org_id=case_flag.org_id, site_id=case_flag.site_id
            )
        case_flags_with_id = [case_flag for case_flag in case_flags if case_flag.id is not None]
        if case_flags_with_id:
            case_flag_results.extend(
                await self.case_flag_store.upsert_case_flags(case_flags_with_id)
            )

        case_flags_without_id = [case_flag for case_flag in case_flags if case_flag.id is None]
        if case_flags_without_id:
            case_flag_results.extend(
                await self.case_flag_store.upsert_case_flags(case_flags_without_id)
            )

        return case_flag_results

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE_STAFF_PLAN)
    async def query_case_flags(self, query_dto: CaseFlagQueryDto) -> Sequence[CaseFlagModel]:
        if not query_dto.case_ids:
            raise ClientError(
                HTTPStatus.UNPROCESSABLE_ENTITY,
                "Must provide at least one case_id to query case_flags",
            )

        return await self.case_flag_store.query_case_flags(query_dto)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_CASE)
    async def _publish_changes_to_case_matching(
        self, event_changes: Collection[EventChangeDto]
    ) -> None:
        """
        This publishes the event changes to the event pub sub store
        """
        for event_change in event_changes:
            self.event_pub_sub_store.publish_event_changelog(event_change)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE)
    async def get_case_matching_reasons(
        self, case_ids: Collection[str]
    ) -> list[CaseMatchingStatusReasonModel]:
        return await self.case_matching_store.get_case_matching_reasons(case_ids=case_ids)

    @log_calls_to_audit()
    @requires_permissions(EDIT_DASHBOARD_SCHEDULE)
    async def match_cases(self, match_cases_dtos: list[MatchCaseDTO]) -> list[PhaseModel]:
        """
        This mutation is used to manually match cases to phases. It takes a list of MatchCaseInput objects, each of which
        contains a case id and a phase id. The mutation will then update the case with the provided phase id. If the
        phase id is None, the case will be unassigned from any phase. The mutation will return a list of the updated
        phases.
        It will also unassign any phases that were previously assigned to the case, but are not in the list of provided
        phase ids.
        Then it will trigger the Event Changes Processor to update the rooms for the relevant times
        """
        # We need to track all of the site ids so we can get the relevant timezones to update the Event Changes Processor with
        site_ids = set()
        new_case_phase_ids = set()
        case_ids = set()

        duplicate_phase_ids = set()
        duplicate_case_ids = set()
        # Step 1 - We get all the provided case and phase ids, and check for duplicates
        for match_case_input in match_cases_dtos:
            new_case_phase_id = match_case_input.phase_id

            if new_case_phase_id in new_case_phase_ids:
                duplicate_phase_ids.add(str(new_case_phase_id))

            new_case_id = str(match_case_input.case_id)
            if new_case_id in case_ids:
                duplicate_case_ids.add(new_case_id)

            if new_case_phase_id is not None:
                new_case_phase_ids.add(new_case_phase_id)
            case_ids.add(new_case_id)

        # Throw an error if there are any duplicate case or phase ids
        if len(duplicate_case_ids) > 0 or len(duplicate_phase_ids) > 0:
            raise ClientError(
                HTTPStatus.BAD_REQUEST,
                f"Duplicate case ids: {duplicate_case_ids}, duplicate phase ids: {duplicate_phase_ids}",
            )

        # Step 2 - We get the cases for the provided case ids
        existing_cases: list[Case] = await self.get_cases(list(case_ids))
        existing_cases_map: dict[str, Case] = {}
        matching_status_reasons: list[
            CaseMatchingStatusReasonModel
        ] = await self.get_case_matching_reasons(case_ids=case_ids)
        case_id_to_matching_status_reason_map: dict[str, CaseMatchingStatusReasonModel] = {
            matching_status_reason.case_id: matching_status_reason
            for matching_status_reason in matching_status_reasons
        }
        for case in existing_cases:
            site_ids.add(case.site_id)
            existing_cases_map[case.case_id] = case

        # Step 3 - We get all of the phases for the provided phase ids and the phases for the provided case ids

        # In order to reduce the number of queries, we will create a map of the existing case phases
        # This will allow us to quickly find the existing case phase for a given phase id in the
        # scenario where a phase id that is to be matched is already matched to a case in the provided
        # input
        phases_by_ids, queried_phases = await asyncio.gather(
            self.phase_service.get_phases_by_ids(
                [str(phase_id) for phase_id in new_case_phase_ids]
            ),
            self.phase_service.query_phases(
                PhaseQuery(case_ids=list(case_ids), source_type="unified"),
            ),
        )
        existing_phase_map: dict[uuid.UUID, PhaseModel] = {
            **{new_case_phase.id: new_case_phase for new_case_phase in phases_by_ids},
            **{existing_phase.id: existing_phase for existing_phase in queried_phases},
        }

        # We check if all the provided case and phase ids exist
        non_existent_phase_ids = {
            str(phase_id) for phase_id in new_case_phase_ids - set(existing_phase_map.keys())
        }
        non_existent_case_ids = case_ids - {
            existing_case.case_id for existing_case in existing_cases
        }

        if len(non_existent_phase_ids) > 0 or len(non_existent_case_ids) > 0:
            raise ClientError(
                HTTPStatus.NOT_FOUND,
                f"Phase ids {non_existent_phase_ids}, case ids {non_existent_case_ids} not found",
            )

        forecasting_phase_ids = {
            str(check_phase.id)
            for check_phase in existing_phase_map.values()
            if check_phase.source_type == "forecasting"
        }

        if len(forecasting_phase_ids) > 0:
            raise ClientError(
                HTTPStatus.BAD_REQUEST,
                f"Cannot match cases to forecasting phases, phase ids {forecasting_phase_ids} are forecasting phases",
            )
        child_phases_map = await self.phase_service.get_child_phases_for_ids(
            [str(phase_id) for phase_id in existing_phase_map.keys()]
        )

        # Step 4 - We remove the case id from the existing phase and set the event_matching_status to automatic
        # so that the Event Changes Processor will update the phase as needed
        for existing_phase in existing_phase_map.values():
            existing_phase.case_id = None
            existing_phase.event_matching_status = EventMatchingStatus.AUTOMATIC
            site_ids.add(existing_phase.site_id)
            for child_phase in child_phases_map.get(str(existing_phase.id), []):
                child_phase.case_id = None
                child_phase.event_matching_status = EventMatchingStatus.AUTOMATIC

        # Step 5 - We generate a set of room dates to update the Event Changes Processor with
        sites_map = {
            site.id: site
            for site in await self.site_service.query_sites(SiteQuery(site_ids=list(site_ids)))
        }

        event_changes_set: set[EventChangeDto] = set()

        for existing_phase in existing_phase_map.values():
            site = sites_map[existing_phase.site_id]
            if not site.timezone:
                logging.warning(f"Site {site.id} does not have a timezone set, defaulting to UTC.")
            site_timezone = ZoneInfo(site.timezone or "UTC")
            # We add the dates to the set of event changes
            start_time = existing_phase.start_time().astimezone(site_timezone)
            event_changes_set.add(
                EventChangeDto(
                    organization_id=existing_phase.org_id,
                    room_id=existing_phase.room_id,
                    site_id=existing_phase.site_id,
                    start_time=datetime(start_time.year, start_time.month, start_time.day),
                    action=EventChangelogAction.UPDATE,
                )
            )

            end_time = existing_phase.end_time()
            if end_time is not None:
                end_time = end_time.astimezone(site_timezone)
                event_changes_set.add(
                    EventChangeDto(
                        organization_id=existing_phase.org_id,
                        room_id=existing_phase.room_id,
                        site_id=existing_phase.site_id,
                        start_time=datetime(end_time.year, end_time.month, end_time.day),
                        action=EventChangelogAction.UPDATE,
                    )
                )

        for case in existing_cases:
            site = sites_map[case.site_id]
            site_timezone = ZoneInfo(site.timezone or "UTC")
            # We add the dates to the set of event changes
            scheduled_start_time = case.scheduled_start_time.astimezone(site_timezone)
            scheduled_end_time = case.scheduled_end_time.astimezone(site_timezone)

            event_changes_set.add(
                EventChangeDto(
                    organization_id=case.org_id,
                    room_id=case.room_id,
                    site_id=case.site_id,
                    start_time=datetime(
                        scheduled_start_time.year,
                        scheduled_start_time.month,
                        scheduled_start_time.day,
                    ),
                    action=EventChangelogAction.UPDATE,
                )
            )

            event_changes_set.add(
                EventChangeDto(
                    organization_id=case.org_id,
                    room_id=case.room_id,
                    site_id=case.site_id,
                    start_time=datetime(
                        scheduled_end_time.year, scheduled_end_time.month, scheduled_end_time.day
                    ),
                    action=EventChangelogAction.UPDATE,
                )
            )

        # Step 6 - We update the cases and phases
        cases_matching_statuses = []
        for match_case_input in match_cases_dtos:
            case_id = str(match_case_input.case_id)
            phase_id = match_case_input.phase_id
            phase_etag = match_case_input.phase_etag

            phase_matching_status = (
                EventMatchingStatus.AUTOMATIC
                if match_case_input.case_match_type == CaseMatchingStatus.AUTOMATIC.value
                else EventMatchingStatus.OVERRIDE
            )

            case_matching_status = (
                CaseMatchingStatus(match_case_input.case_match_type)
                if match_case_input.case_match_type
                else CaseMatchingStatus.OVERRIDE
            )

            if phase_id is not None:
                if phase_id not in existing_phase_map:
                    raise ClientError(HTTPStatus.NOT_FOUND, f"Phase with id {phase_id} not found")

                try:
                    existing_case = existing_cases_map[case_id]
                except KeyError:
                    raise ClientError(HTTPStatus.NOT_FOUND, f"Case with id {case_id} not found")

                existing_phase = existing_phase_map[phase_id]

                if str(existing_case.room_id) != str(existing_phase.room_id):
                    raise ClientError(
                        HTTPStatus.BAD_REQUEST,
                        f"Case {case_id} and Phase {phase_id} are not in the same room, this is an unsupported operation",
                    )

                existing_phase.case_id = case_id
                existing_phase.etag = phase_etag  # type: ignore
                existing_phase.event_matching_status = phase_matching_status

                for child_phase in child_phases_map.get(str(phase_id), []):
                    child_phase.case_id = case_id
                    child_phase.event_matching_status = phase_matching_status
                    existing_phase_map[child_phase.id] = child_phase

            existing_matching_status_reason = case_id_to_matching_status_reason_map.get(case_id)
            new_matching_status_with_reason = CaseMatchingStatusReasonModel(
                case_matching_status=case_matching_status,
                explanation_for_change=match_case_input.explanation_for_change,
                case_id=case_id,
            )
            if existing_matching_status_reason is not None:
                new_matching_status_with_reason.id = existing_matching_status_reason.id
            cases_matching_statuses.append(new_matching_status_with_reason)

        await self.set_cases_matching_status(cases_matching_statuses)
        phases_updated = await self.phase_service.upsert_phases(list(existing_phase_map.values()))

        # Step 7 - We pass the room dates to the Event Changes Processor
        await self._publish_changes_to_case_matching(event_changes_set)

        return phases_updated
