from collections import namedtuple
from datetime import timed<PERSON><PERSON>
import json
from typing import List, Optional

from api_server.logging.audit import log_calls_to_audit
from api_server.services.case.case_staff_store import (
    CaseStaffModel,
    CaseStaffStore,
    CaseStaffQuery,
)
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import READ_ANY_CASE
import config
from databases.cache.persistent_cache import PersistentCache

StaffSiteAppearanceCount = namedtuple("StaffSiteAppearanceCount", ["site_id", "appearance_count"])


class CaseStaffService:
    case_staff_store: CaseStaffStore
    auth: Auth
    persistent_cache: PersistentCache

    def __init__(
        self, auth: Auth, case_staff_store: CaseStaffStore, persistent_cache: PersistentCache
    ):
        self.case_staff_store = case_staff_store
        self.auth = auth
        self.persistent_cache = persistent_cache

    def __gen_cache_key(self, org_id: str, staff_id: str) -> str:
        return f"{org_id}_{staff_id}__most_frequent_site"

    @requires_permissions(READ_ANY_CASE)
    @log_calls_to_audit()
    async def query_case_staff(
        self, case_id: str, only_primary_surgeons: Optional[bool] = False
    ) -> List[CaseStaffModel]:
        return await self.case_staff_store.query_case_staff(case_id, only_primary_surgeons)

    @requires_permissions(READ_ANY_CASE)
    @log_calls_to_audit()
    async def query_case_staff_relationships(
        self,
        query_obj: CaseStaffQuery,
    ) -> List[CaseStaffModel]:
        return await self.case_staff_store.get_case_staff_relationships_for_cases(query_obj)

    @requires_permissions(READ_ANY_CASE)
    @log_calls_to_audit()
    async def get_staff_most_frequent_site(
        self, staff_id: str
    ) -> Optional[StaffSiteAppearanceCount]:
        table = config.bigquery_staff_site_frequency_table()
        org_id = self.auth.get_calling_org_id()
        if not org_id:
            raise ValueError("Organization ID is required to query most frequent site.")

        if not table:
            raise ValueError("BigQuery table for staff site frequency is not configured.")

        cache_key = self.__gen_cache_key(org_id, staff_id)

        resp = self.persistent_cache.get(cache_key)
        if not resp:
            resp = await self.case_staff_store.get_staff_most_frequent_site(staff_id, table)
            if resp is None:
                return None

            resp = json.dumps(resp)
            self.persistent_cache.set(cache_key, resp, expiration=timedelta(days=7))
        resp = json.loads(resp)

        return StaffSiteAppearanceCount(
            site_id=resp["site_id"],
            appearance_count=resp["appearance_count"],
        )
