import dataclasses

# mypy: allow-untyped-defs
import uuid
from dataclasses import dataclass
from enum import Enum, auto
from typing import List, Optional, Union

from sqlalchemy import Foreign<PERSON><PERSON>, String, select
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column

from databases.sql import Base, new_async_session
from databases.sql.mixins import ArchivedTimeMixin, TimestampMixin
from databases.big_query import BigQueryClient
from google.cloud.bigquery import QueryJobConfig, ScalarQueryParameter


# added to avoid circular import
def _resolve_staff_model():
    from api_server.services.staff.staff_store import StaffModel

    return StaffModel


PRIMARY_SURGEON_ROLES = ["Primary", "Primary Surgeon", "Physician", "Surgeon", "Surgeon 1"]

ANESTHESIA_ROLES = [
    "Anesthesiologist",
    "Primary Anesthesiologist",
    "CRNA",
    "Anesthesia - CRNA",
    "Anesthesia - CRNA Relief",
    "Anesthesia - MD",
]
ALL_ANESTHESIA_ROLES = ANESTHESIA_ROLES + [
    "Anesthesia Resident",
    "CRNA Student",
    "Anesthesia - SRNA",
    "Anesthesia - Resident",
]

# values duplicated in:
# https://github.com/Apella-Technology/cubejs/blob/main/src/model/CaseStaffCirculator.js
CIRCULATOR_ROLES = [
    "Circulator",
    "Circulator - First",
    "Circulator - Second",
    "Circulator - Relief",
]

# values duplicated in:
# https://github.com/Apella-Technology/cubejs/blob/main/src/model/CaseStaffScrubTech.js
SCRUB_TECH_ROLES = [
    "Scrub Person",
    "Surg Tech",
    "Scrub Tech",
    "Scrub - First",
    "Scrub - Second",
    "Scrub - Relief",
]


class CaseStaffRole(Enum):
    PRIMARY_SURGEON = auto()
    ANESTHESIA = auto()
    ALL_ANESTHESIA = auto()
    CIRCULATOR = auto()
    SCRUB_TECH = auto()


@dataclass
class CaseStaffQuery:
    query_id: uuid.UUID = dataclasses.field(default_factory=uuid.uuid4)
    case_ids: Optional[list[str]] = None
    staff_ids: Optional[list[str]] = None
    only_primary_surgeons: Optional[bool] = False


ROLE_LIST_MAPPING = {
    CaseStaffRole.PRIMARY_SURGEON: PRIMARY_SURGEON_ROLES,
    CaseStaffRole.ANESTHESIA: ANESTHESIA_ROLES,
    CaseStaffRole.ALL_ANESTHESIA: ALL_ANESTHESIA_ROLES,
    CaseStaffRole.CIRCULATOR: CIRCULATOR_ROLES,
    CaseStaffRole.SCRUB_TECH: SCRUB_TECH_ROLES,
}


class CaseStaffModel(Base, TimestampMixin, ArchivedTimeMixin):
    __tablename__ = "case_staff"

    case_id: Mapped[str] = mapped_column(
        String, ForeignKey("cases.case_id"), primary_key=True, index=True
    )
    staff_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("staff.id"), primary_key=True, index=True
    )
    role: Mapped[Union[str, None]] = mapped_column(String, nullable=True, index=True)

    staff = relationship(_resolve_staff_model, lazy="joined")

    def __repr__(self):
        return f"<CaseStaff(case_id='{self.case_id}', staff_id='{self.staff_id}')>"


@dataclass(frozen=True, eq=True)
class CaseStaffIdPair:
    case_id: str
    staff_id: uuid.UUID

    def __repr__(self):
        return f"<CaseStaffIdPair(case_id='{self.case_id}', staff_id='{self.staff_id}')>"


class CaseStaffStore:
    async def upsert_case_staff(self, case_staff: List[CaseStaffModel]) -> None:
        """
        Insert all case_staff into the database. We use session.merge() since primary keys are
        known and stable to clients.
        """
        async with new_async_session() as session:
            for cs in case_staff:
                await session.merge(cs)
            await session.commit()

    async def get_case_staff_relationships_for_cases(
        self,
        query_obj: CaseStaffQuery,
    ) -> List[CaseStaffModel]:
        """
        Get all staff associated to a given case (Excluding archived)
        """
        query = select(CaseStaffModel).filter(CaseStaffModel.archived_time.is_(None))

        if query_obj.case_ids:
            query = query.filter(CaseStaffModel.case_id.in_(query_obj.case_ids))

        if query_obj.staff_ids:
            query = query.filter(CaseStaffModel.staff_id.in_(query_obj.staff_ids))

        if query_obj.only_primary_surgeons:
            query = query.filter(CaseStaffModel.role.in_(PRIMARY_SURGEON_ROLES))

        async with new_async_session() as session:
            results = await session.scalars(query)
            return list(results.all())

    async def query_case_staff(
        self,
        case_id: str,
        only_primary_surgeons: Optional[bool],
    ) -> List[CaseStaffModel]:
        query = select(CaseStaffModel).filter(
            CaseStaffModel.archived_time.is_(None), CaseStaffModel.case_id == case_id
        )

        if only_primary_surgeons:
            query = query.filter(CaseStaffModel.role.in_(PRIMARY_SURGEON_ROLES))

        async with new_async_session() as session:
            results = await session.scalars(query)
            return list(results.all())

    async def get_staff_most_frequent_site(
        self, staff_id: str, table: str
    ) -> Optional[dict[str, Union[str, int]]]:
        client = BigQueryClient.get_client()
        # Sum the case_count of each surgeon's procedures per site
        query = f"""
            SELECT
                site_id,
                SUM(case_count) AS appearance_count
            FROM `{table}`
            WHERE staff_id = @staff_id
            GROUP BY site_id, staff_id
            ORDER BY appearance_count DESC, site_id ASC
            LIMIT 1
        """
        query_parameters = [
            ScalarQueryParameter("staff_id", "STRING", staff_id),
        ]
        job_config = QueryJobConfig(query_parameters=query_parameters)

        result = client.query(query, job_config).result()
        row = next(iter(result), None)

        if row is None:
            return None

        serializable_result = {"site_id": row.site_id, "appearance_count": row.appearance_count}
        return serializable_result
