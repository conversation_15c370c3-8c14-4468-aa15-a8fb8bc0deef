from dataclasses import dataclass
from typing import Optional, Sequence


from api_server.services.case.case_flag_store import CaseFlagModel, CaseFlagQueryDto
from api_server.services.case.case_service import CaseService
from api_server.services.utils.loader.base_classes import (
    AbstractDataLoader,
)
from api_server.services.utils.loader.util_functions import (
    async_create_loader_queries,
)


@dataclass(frozen=True)
class CaseFlagLoaderQuery:
    # The id of the case
    id: str
    flag_types: Optional[list[str]] = None
    include_archived: bool = False


class CaseFlagLoader(AbstractDataLoader[CaseFlagLoaderQuery, Sequence[CaseFlagModel]]):
    case_service: CaseService

    def compare_key_to_model(
        self,
        key: CaseFlagLoaderQuery,
        value: CaseFlagModel,
    ) -> bool:
        return key.id == str(value.case_id)

    def __init__(self, case_service: CaseService):
        super().__init__()
        self.case_service = case_service

    async def batch_load_fn(self, keys: list[CaseFlagLoaderQuery]) -> list[Sequence[CaseFlagModel]]:
        result_dict: dict[
            CaseFlagLoaderQuery, Sequence[CaseFlagModel]
        ] = await async_create_loader_queries(
            keys=keys,
            key_type=CaseFlagLoaderQuery,
            query_type=CaseFlagQueryDto,
            service_query_fn=self.case_service.query_case_flags,
            filter_fn=self.compare_key_to_model,
            pks={"id": "case_ids"},
        )

        # Based on the ordering of the keys passed into the function re-sort the returned procedures
        return [result_dict.get(key, []) for key in keys]
