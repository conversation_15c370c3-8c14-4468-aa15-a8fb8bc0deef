# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

from typing import List

import graphene

from api_server.graphql.context import GrapheneInfo
from api_server.services.case.case_procedure_store import CaseProcedureModel


class CaseProceduresUpsertInput(graphene.InputObjectType):
    case_id = graphene.String(required=True)
    procedure_id = graphene.String(required=True)
    hierarchy = graphene.Int(required=False)
    anesthesia_id = graphene.UUID(required=False)


class CaseProceduresUpsert(graphene.Mutation):
    class Arguments:
        input = graphene.NonNull(graphene.List(graphene.NonNull(CaseProceduresUpsertInput)))

    success: bool = graphene.Boolean()

    @staticmethod
    async def mutate(parent, info: GrapheneInfo, input: List[CaseProceduresUpsertInput]):
        case_procedures = [
            CaseProcedureModel(
                case_id=cp.case_id,
                procedure_id=cp.procedure_id,
                hierarchy=cp.hierarchy,
                anesthesia_id=cp.anesthesia_id,
            )
            for cp in input
        ]
        await info.context.case_service.upsert_case_procedures(case_procedures=case_procedures)
        return CaseProceduresUpsert(success=True)
