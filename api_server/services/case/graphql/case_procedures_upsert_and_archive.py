# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

from datetime import datetime
from typing import List

import graphene

from api_server.graphql.context import GrapheneInfo
from api_server.services.case.case_procedure_store import (
    CaseProcedureDto,
    CaseProcedureModel,
)


class CaseProceduresUpsertAndArchiveInput(graphene.InputObjectType):
    case_id = graphene.String(required=True)
    procedure_id = graphene.UUID(required=True)
    hierarchy = graphene.Int(required=False)
    anesthesia_id = graphene.UUID(required=False)


class CaseProceduresUpsertAndArchive(graphene.Mutation):
    class Arguments:
        input = graphene.NonNull(
            graphene.List(graphene.NonNull(CaseProceduresUpsertAndArchiveInput))
        )

    success = graphene.Boolean()

    # TODO https://linear.app/apella/issue/RT-320/remove-explicit-cast-of-hierarchy-to-intnone
    @staticmethod
    def int_or_none(input_val):
        # because a cast of int to None throws an exception!
        try:
            return int(input_val)
        except (TypeError, ValueError):
            return None

    @staticmethod
    async def mutate(parent, info: GrapheneInfo, input: List[CaseProceduresUpsertAndArchiveInput]):
        case_procedures = [
            CaseProcedureModel(
                case_id=case_procedure.case_id,
                procedure_id=case_procedure.procedure_id,
                hierarchy=CaseProceduresUpsertAndArchive.int_or_none(case_procedure.hierarchy),
                archived_time=None,
                anesthesia_id=case_procedure.anesthesia_id or None,
            )
            for case_procedure in input
        ]
        # We want to keep only the given case procedures and archive the case procedures that are
        # Assigned but not given in this message
        case_procedures_given = {
            CaseProcedureDto(
                procedure_id=case_procedure.procedure_id,
                case_id=str(case_procedure.case_id),
                hierarchy=CaseProceduresUpsertAndArchive.int_or_none(case_procedure.hierarchy),
            )
            for case_procedure in input
        }

        case_ids_given = {case_procedure.case_id for case_procedure in input}

        case_procedure_ids_all = {
            CaseProcedureDto(
                procedure_id=case_procedure.procedure_id,
                case_id=str(case_procedure.case_id),
                hierarchy=CaseProceduresUpsertAndArchive.int_or_none(case_procedure.hierarchy),
            )
            for case_procedure in await info.context.case_service.get_case_procedure_relationships_for_cases(
                case_ids=list(case_ids_given)
            )
            if not case_procedure.archived_time
        }

        case_procedure_ids_archive = case_procedure_ids_all - case_procedures_given

        case_procedures += [
            CaseProcedureModel(
                case_id=case_procedure.case_id,
                procedure_id=case_procedure.procedure_id,
                hierarchy=CaseProceduresUpsertAndArchive.int_or_none(case_procedure.hierarchy),
                archived_time=datetime.now(),
            )
            for case_procedure in case_procedure_ids_archive
        ]

        await info.context.case_service.upsert_case_procedures(case_procedures=case_procedures)

        return CaseProceduresUpsertAndArchive(success=True)
