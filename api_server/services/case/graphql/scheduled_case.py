# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs
import asyncio
from typing import Optional, Sequence

from graphql import GraphQLError

from api_server.services.case.case_classification_type_store import CaseClassificationTypeModel
from api_server.services.case.case_procedure_store import CaseProcedureModel
from api_server.services.case.case_staff_store import CaseStaffModel
from api_server.services.case.graphql.case_staff_loader import FrozenCaseStaffQueryDto
from api_server.services.case_derived_properties.case_derived_properties_store import (
    CaseDerivedProperties,
)
from api_server.services.case_labels.case_label_store import CaseLabelDTO
from api_server.services.case_labels.graphql.case_labels import CaseLabel
from api_server.services.contact_information.staff_event_notification_contact_information_store import (
    StaffEventNotificationModel,
)
from api_server.services.observations.observation_store import ObservationModel
from api_server.services.patients.graphql.patient_loader import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from api_server.services.plan.case_note_plan_store import CaseNotePlanModel
from api_server.services.plan.case_staff_plan_store import CaseStaffPlanModel
from api_server.services.procedures.procedure_store import ProcedureModel, CaseProcedureInfoDto
from api_server.services.room.room_store import RoomModel
from api_server.services.service_lines.graphql.service_line import ServiceLine, ServiceLineModel

import graphene

from api_server.graphql.sorting.arguments import SortedPaginationConnectionField

import api_server.services.observations.graphql.observation as observation_schema
from api_server.services.case.graphql.case_flag import CaseFlag, CaseFlagModel
import api_server.services.plan.graphql.case_note_plan as case_note_plan_schema
from api_server.services.patients.graphql.patient import (
    Patient,
)
from api_server.services.plan.graphql.case_staff_plan_loader import (
    FrozenCaseStaffPlanQueryDTO,
)
from api_server.services.plan.graphql.case_staff_plan import (
    CaseStaffPlanConnection,
    CaseStaffPlanInput,
)
import api_server.services.room.graphql.room as room_schema
import api_server.services.site.graphql.site as site_schema
from api_server.graphql.context import GrapheneInfo
from api_server.graphql.pagination.fields import pagination_connection_factory
from api_server.services.case.case_store import Case, PatientClass
from api_server.services.case.case_matching_store import (
    CaseMatchingStatusGraphene,
    CaseMatchingStatusReasonModel,
)
from api_server.services.case.graphql.case_flag_loader import CaseFlagLoaderQuery
from api_server.services.case.graphql.case_procedure import CaseProcedure
from api_server.services.case.graphql.case_procedure_loader import (
    CaseProcedureLoaderQuery,
)
from api_server.services.case.graphql.case_staff import CaseStaff
from api_server.services.procedures.graphql.procedure import ProcedureConnection
from api_server.services.site.site_store import Site
from api_server.services.staff.graphql.staff import StaffConnection
from databases.sql.enums import CaseMatchingStatus

PatientClassGraphene = graphene.Enum.from_enum(PatientClass)


def import_event_notification():
    from api_server.services.contact_information.graphql.contact_information import (
        EventNotification,
    )

    return EventNotification


class CaseClassificationType(graphene.ObjectType):
    id = graphene.ID(resolver=lambda cct, _: cct.id, required=True)
    org_id = graphene.String(required=True)

    name = graphene.String(required=True)
    decription = graphene.String()

    created_time = graphene.DateTime(required=True)
    updated_time = graphene.DateTime(required=False)


class ScheduledCaseQueryInput(graphene.InputObjectType):
    min_time = graphene.DateTime(deprecated=True, deprecated_reason="Use `min_end_time` instead")
    max_time = graphene.DateTime(deprecated=True, deprecated_reason="Use `max_start_time` instead")

    min_start_time = graphene.DateTime()
    max_start_time = graphene.DateTime()
    min_end_time = graphene.DateTime()
    max_end_time = graphene.DateTime()

    min_updated_time = graphene.DateTime()
    max_updated_time = graphene.DateTime()

    min_created_time = graphene.DateTime()
    max_created_time = graphene.DateTime()

    site_ids = graphene.List(graphene.NonNull(graphene.ID))
    room_ids = graphene.List(graphene.NonNull(graphene.ID))
    staff_ids = graphene.List(graphene.NonNull(graphene.ID))
    procedure_ids = graphene.List(graphene.NonNull(graphene.ID))

    case_classification_types_ids = graphene.List(graphene.ID)
    case_ids = graphene.List(graphene.NonNull(graphene.ID))

    status = graphene.String()
    is_add_ons = graphene.List(graphene.Boolean)
    case_flags = graphene.List(graphene.NonNull(graphene.String))


class CaseHistoryQueryInput(ScheduledCaseQueryInput):
    case_ids = graphene.List(graphene.NonNull(graphene.ID))


class MatchingStatusReason(graphene.ObjectType):
    id = graphene.ID(required=True)
    explanation_for_change = graphene.String(required=True)
    case_matching_status = CaseMatchingStatusGraphene(required=True)


class ScheduledCase(graphene.ObjectType):
    id = graphene.ID(resolver=lambda case, info: case.case_id, required=True)

    scheduled_start_time = graphene.DateTime(required=True)
    scheduled_end_time = graphene.DateTime(required=True)
    updated_time = graphene.DateTime(required=False)
    created_time = graphene.DateTime(required=True)

    site = graphene.Field(lambda: site_schema.Site, required=True)
    room = graphene.Field(lambda: room_schema.Room, required=True)

    case_classification_type = graphene.Field(lambda: CaseClassificationType, required=False)

    status = graphene.String(required=True)

    case_staff = graphene.Field(
        graphene.NonNull(graphene.List(graphene.NonNull(CaseStaff))),
        only_primary_surgeons=graphene.Boolean(required=False),
    )

    case_staff_plan = SortedPaginationConnectionField(
        lambda: CaseStaffPlanConnection,
        query=graphene.Argument(CaseStaffPlanInput, required=True),
        required=True,
    )
    note_plan = graphene.Field(lambda: case_note_plan_schema.CaseNotePlan, required=False)

    staff = SortedPaginationConnectionField(
        lambda: StaffConnection,
        only_primary_surgeons=graphene.Boolean(required=False),
        required=True,
        deprecation_reason="Deprecated in favor of `case_staff`",
    )

    is_in_flip_room = graphene.Boolean(required=False)

    preceding_case = graphene.Field(lambda: ScheduledCase, required=False)

    is_first_case = graphene.Boolean(required=False)

    case_procedures = graphene.Field(
        graphene.NonNull(graphene.List(graphene.NonNull(CaseProcedure))),
        hierarchy=graphene.Int(required=False),
    )
    procedures = SortedPaginationConnectionField(
        lambda: ProcedureConnection,
        hierarchy=graphene.Int(required=False),
        required=True,
        deprecation_reason="Deprecated in favor of `case_procedures`",
    )
    is_add_on = graphene.Boolean(required=False)
    patient = graphene.Field(lambda: Patient, required=False)
    patient_class = PatientClassGraphene(required=False)
    version = graphene.Int(required=False)
    observations = SortedPaginationConnectionField(
        lambda: observation_schema.ObservationConnection,
        required=True,
        types=graphene.List(graphene.NonNull(graphene.String)),
    )
    external_case_id = graphene.String(required=True)
    primary_case_procedures = graphene.Field(
        graphene.NonNull(graphene.List(graphene.NonNull(CaseProcedure))),
    )
    case_flags = graphene.List(
        graphene.NonNull(lambda: CaseFlag),
        required=True,
        flag_types=graphene.List(graphene.String),
        include_archived=graphene.Boolean(required=False),
    )

    service_line = graphene.Field(lambda: ServiceLine, required=False)
    case_matching_status = CaseMatchingStatusGraphene(required=True)
    matching_status_reason = graphene.Field(MatchingStatusReason, required=False)

    event_notifications = graphene.List(graphene.NonNull(import_event_notification), required=False)
    case_labels = graphene.NonNull(graphene.List(graphene.NonNull(CaseLabel)))

    @staticmethod
    async def resolve_case_labels(case: Case, info: GrapheneInfo, *args, **kwargs):
        results = await info.context.case_label_loader.load(case.case_id)

        return [
            CaseLabelDTO(
                id=result.id,
                option_id=result.option.id,
                case_id=result.case_id,
                updated_by_user_id=result.updated_by_user_id,
                field_id=result.option.field_id,
                color=result.option.color,
                abbreviation=result.option.abbreviation,
                value=result.option.value,
                archived_time=result.archived_time,
            )
            for result in results
        ]

    @staticmethod
    async def resolve_event_notifications(
        case: Case, info: GrapheneInfo, *args, **kwargs
    ) -> list[StaffEventNotificationModel]:
        return await info.context.event_notification_subscription_loader.load(case.case_id)

    @staticmethod
    async def resolve_service_line(
        case: Case, info: GrapheneInfo, *args, **kwargs
    ) -> Optional[ServiceLineModel]:
        if case.service_line_id is None:
            return None

        return await info.context.service_line_loader.load(str(case.service_line_id))

    @staticmethod
    async def resolve_case_classification_type(
        case: Case, info: GrapheneInfo, *args, **kwargs
    ) -> Optional[CaseClassificationTypeModel]:
        if case.case_classification_types_id is None:
            return None
        return await info.context.case_classification_type_loader.load(
            str(case.case_classification_types_id)
        )

    @staticmethod
    async def resolve_site(case: Case, info: GrapheneInfo, **kwargs) -> Site:
        site: Optional[Site] = await info.context.site_loader.load(case.site_id)
        if site is None:
            raise GraphQLError(message=f"Unable to find site for case with site id: {case.site_id}")
        return site

    @staticmethod
    async def resolve_room(case: Case, info: GrapheneInfo, **kwargs) -> RoomModel:
        room: Optional[RoomModel] = await info.context.room_loader.load(case.room_id)
        if room is None:
            raise GraphQLError(message=f"Unable to find room for case with room id: {case.room_id}")
        return room

    @staticmethod
    async def resolve_is_in_flip_room(case: Case, info: GrapheneInfo, **kwargs) -> Optional[bool]:
        case_properties: Optional[
            CaseDerivedProperties
        ] = await info.context.case_derived_properties_loader.load(case.case_id)
        return None if case_properties is None else case_properties.is_in_flip_room

    @staticmethod
    async def resolve_preceding_case(case: Case, info: GrapheneInfo, **kwargs) -> Optional[Case]:
        case_properties: Optional[
            CaseDerivedProperties
        ] = await info.context.case_derived_properties_loader.load(case.case_id)
        if case_properties is None or case_properties.preceding_case_id is None:
            return None
        return await info.context.case_loader.load(case_properties.preceding_case_id)

    @staticmethod
    async def resolve_is_first_case(case: Case, info: GrapheneInfo, **kwargs) -> Optional[bool]:
        case_properties: Optional[
            CaseDerivedProperties
        ] = await info.context.case_derived_properties_loader.load(case.case_id)
        return None if case_properties is None else case_properties.is_first_case

    @staticmethod
    async def resolve_note_plan(
        case: Case, info: GrapheneInfo, **kwargs
    ) -> Optional[CaseNotePlanModel]:
        return await info.context.case_note_plan_loader.load(case.case_id)

    @staticmethod
    async def resolve_case_staff(
        case: Case, info: GrapheneInfo, only_primary_surgeons=False
    ) -> list[CaseStaffModel]:
        return list(
            await info.context.case_staff_loader.load(
                FrozenCaseStaffQueryDto(case.case_id, only_primary_surgeons)
            )
        )

    @staticmethod
    async def resolve_case_staff_plan(
        case: Case, info: GrapheneInfo, query: CaseStaffPlanInput, *args, **kwargs
    ) -> Sequence[CaseStaffPlanModel]:
        csp_query = FrozenCaseStaffPlanQueryDTO(
            case_id=case.case_id,
            staff_ids=tuple(query.staff_ids) if query.staff_ids else None,
            include_archived=query.include_archived,
        )
        return await info.context.case_staff_plan_loader.load(csp_query)

    @staticmethod
    async def resolve_staff(
        case: Case, info: GrapheneInfo, only_primary_surgeons=False, *args, **kwargs
    ):
        return await info.context.staff_service.query_staff(
            case_ids=[case.case_id], only_primary_surgeons=only_primary_surgeons
        )

    @staticmethod
    async def resolve_procedures(
        case: Case, info: GrapheneInfo, hierarchy=None, *args, **kwargs
    ) -> list[Optional[ProcedureModel]]:
        case_procedures = await info.context.case_procedure_loader.load(
            CaseProcedureLoaderQuery(case.case_id, hierarchy)
        )

        all_case_futures = [
            info.context.procedure_loader.load(str(case_procedure.procedure_id))
            for case_procedure in case_procedures
        ]
        return await asyncio.gather(*all_case_futures)

    @staticmethod
    async def resolve_patient(case: Case, info: GrapheneInfo, *args, **kwargs):
        if not case.external_case_id:
            return None

        patients = await info.context.patient_loader.load(
            PatientLoaderQuery(external_case_id=str(case.external_case_id), org_id=case.org_id)
        )
        return patients[0] if patients else None

    @staticmethod
    async def resolve_case_procedures(
        case: Case, info: GrapheneInfo, hierarchy=None, *args, **kwargs
    ) -> Sequence[CaseProcedureModel]:
        return await info.context.case_procedure_loader.load(
            CaseProcedureLoaderQuery(case.case_id, hierarchy)
        )

    @staticmethod
    async def resolve_primary_case_procedures(
        case: Case, info: GrapheneInfo, *args, **kwargs
    ) -> list[CaseProcedureInfoDto]:
        return await info.context.primary_case_procedure_loader.load(case.case_id)

    @staticmethod
    async def resolve_observations(
        case: Case, info: GrapheneInfo, types=None, *args, **kwargs
    ) -> list[ObservationModel]:
        observation_types = frozenset(types) if types is not None else None
        return await info.context.observation_loader.load((case.case_id, observation_types, None))

    @staticmethod
    async def resolve_case_flags(
        case: Case,
        info: GrapheneInfo,
        flag_types: Optional[list[str]] = None,
        include_archived: bool = False,
        *args,
        **kwargs,
    ) -> Sequence[CaseFlagModel]:
        return await info.context.case_flag_loader.load(
            CaseFlagLoaderQuery(case.case_id, flag_types, include_archived)
        )

    @staticmethod
    async def resolve_case_matching_status(
        case: Case, info: GrapheneInfo, *args, **kwargs
    ) -> CaseMatchingStatus:
        matching_status_reason = await info.context.matching_status_reason_loader.load(case.case_id)
        return (
            CaseMatchingStatus.AUTOMATIC
            if matching_status_reason is None
            else matching_status_reason.case_matching_status
        )

    @staticmethod
    async def resolve_matching_status_reason(
        case: Case, info: GrapheneInfo, *args, **kwargs
    ) -> Optional[CaseMatchingStatusReasonModel]:
        return await info.context.matching_status_reason_loader.load(case.case_id)


ScheduledCaseConnection = pagination_connection_factory(ScheduledCase)
