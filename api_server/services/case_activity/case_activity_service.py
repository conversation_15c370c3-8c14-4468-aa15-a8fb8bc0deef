from datetime import datetime
from typing import Optional, Sequence
from api_server.logging.audit import async_log_calls_to_audit
from api_server.services.case_activity.case_activity_store import (
    CaseActivityModel,
    CaseActivityStore,
)


class CaseActivityService:
    def __init__(self) -> None:
        self.store = CaseActivityStore()

    @async_log_calls_to_audit()
    async def get_case_activities(
        self,
        site_id: str,
        min_time: datetime,
        max_time: datetime,
        include_source_type: Optional[str] = None,
    ) -> Sequence[CaseActivityModel]:
        """Retrieves case activity data for a given site within a time range, optionally filtering out a specific source_type."""
        return await self.store.fetch_case_activities(
            min_start_time=min_time,
            max_end_time=max_time,
            site_id=site_id,
            include_source_type=include_source_type,
        )
