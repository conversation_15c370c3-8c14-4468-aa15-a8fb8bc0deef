from databases.sql.mixins import Decodable, OrgIdMixin, SiteIdMixin, TimestampMixin
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy import String, DateTime
from datetime import datetime
from sqlalchemy.future import select
from typing import Optional, Sequence
from databases.sql import new_async_session, Base
from sqlalchemy.sql import func


class CaseActivityModel(Base, Decodable, TimestampMixin, OrgIdMixin, SiteIdMixin):
    __tablename__ = "case_activity"

    # Override org_id and site_id to match streaming table schema (nullable=True)
    org_id: Mapped[str] = mapped_column(String, nullable=True, index=True)
    site_id: Mapped[str] = mapped_column(String, nullable=True, index=True)

    # Override updated_time to ensure it's always non-nullable
    updated_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,  # Ensures consistency with original behavior
        index=True,
    )

    # Composite Primary Key
    id: Mapped[str] = mapped_column(String, nullable=False, primary_key=True)
    source_type: Mapped[str] = mapped_column(String, nullable=False, primary_key=True)

    # Other Fields
    room_id: Mapped[str] = mapped_column(String, nullable=True)
    deleted_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
    event_type_id: Mapped[str] = mapped_column(String, nullable=False)
    source: Mapped[str] = mapped_column(String, nullable=False)
    case_id: Mapped[str] = mapped_column(String, nullable=True)
    start_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, index=True
    )
    process_timestamp: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)


class CaseActivityStore:
    async def fetch_case_activities(
        self,
        min_start_time: datetime,
        max_end_time: datetime,
        site_id: str,
        include_source_type: Optional[str] = None,
    ) -> Sequence[CaseActivityModel]:
        """Fetches case activities within a given time range and optional filters."""

        async with new_async_session() as session:
            filters = [
                CaseActivityModel.start_time >= min_start_time,
                CaseActivityModel.start_time <= max_end_time,
                CaseActivityModel.deleted_time.is_(None),
                CaseActivityModel.site_id == site_id,
            ]

            if include_source_type:
                filters.append(CaseActivityModel.source_type == include_source_type)

            statement = select(CaseActivityModel).where(*filters)

            return (await session.scalars(statement)).all()
