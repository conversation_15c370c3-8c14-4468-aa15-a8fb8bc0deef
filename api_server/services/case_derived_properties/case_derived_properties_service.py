from collections import defaultdict
from dataclasses import dataclass
from datetime import date, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple
from zoneinfo import ZoneInfo

from api_server.logging.audit import log_calls_to_audit
from api_server.services.case.case_staff_store import (
    PRIMARY_SURGEON_ROLES,
    CaseStaffModel,
)
from api_server.services.case.case_status import SCHEDULED
from api_server.services.case.case_store import Case
from api_server.services.case_derived_properties.case_derived_properties_store import (
    CaseDerivedProperties,
    CaseDerivedPropertiesStore,
    ProcessCaseDerivedPropertiesDto,
)
from api_server.services.phases.phase_service import PhaseService
from api_server.services.phases.phase_store import PhaseModel, PhaseQuery, PhaseStatus
from api_server.services.site.site_store import Site
from auth.auth import Auth
from auth.auth_decorator import requires_permission_prefix
from auth.permissions import READ_ANY_CASE, WRITE_ANY_CASE

import re


@dataclass(frozen=True)
class CaseWithStaff:
    case: Case
    staff_list: list[CaseStaffModel]
    site: Site


class CaseDerivedPropertiesService:
    auth: Auth
    case_derived_properties_store: CaseDerivedPropertiesStore
    phase_service: PhaseService
    PRECEDING_CASE_CUTOFF_IN_MINS = 60
    FLIP_ROOM_CASE_CUTOFF_IN_MINS = 60

    def __init__(
        self,
        auth: Auth,
        case_derived_properties_store: CaseDerivedPropertiesStore,
        phase_service: PhaseService,
    ):
        self.auth = auth
        self.case_derived_properties_store = case_derived_properties_store
        self.phase_service = phase_service

    @log_calls_to_audit()
    @requires_permission_prefix(READ_ANY_CASE)
    async def get_properties_by_case_ids(self, case_ids: List[str]) -> List[CaseDerivedProperties]:
        return await self.case_derived_properties_store.get_by_case_ids(case_ids=case_ids)

    @log_calls_to_audit()
    @requires_permission_prefix(WRITE_ANY_CASE)
    async def process_case_properties(
        self, case_properties_update_dto: List[ProcessCaseDerivedPropertiesDto]
    ) -> None:
        """
        Process the case derived properties for cases. Case derived properties include first case,
        preceding case/following case, and flip room case.

        https://www.notion.so/apella/Surfacing-case-derivations-60-c2575e4318064f73998fa715c875c7a9

        Args:
            case_properties_update_dto: List of ProcessCaseDerivedPropertiesDto

        """
        cases_to_process = await self.case_derived_properties_store.get_cases_to_process(
            case_properties_update_dto=case_properties_update_dto
        )

        first_cases: set[Case] = set()
        preceding_cases: Dict[str, str] = {}
        flip_room_cases: Dict[str, bool] = {}

        # Group cases by staff_id
        cases_by_staff_id: Dict[str, List[Tuple[Case, Optional[CaseStaffModel], Site]]] = (
            defaultdict(list)
        )
        for case, case_staff_model, site in cases_to_process:
            if (
                case_staff_model is None
                or CaseDerivedPropertiesService._is_categorically_excluded_case(
                    case=case, case_staff=case_staff_model
                )
            ):
                continue
            cases_by_staff_id[str(case_staff_model.staff_id)].append((case, case_staff_model, site))

        # Process each staff's cases chronologically
        for staff_id in cases_by_staff_id.keys():
            # get unique cases for staff and sort them by case start time
            staff_cases = list(cases_by_staff_id.get(staff_id, []))
            staff_cases.sort(key=lambda case: (case[0].scheduled_start_time))

            # surgeon's first case by day
            staff_first_cases: Dict[int, Case] = {}

            # case_0 has no 'previous', we handle separately first to check for first Case criteria
            case_0, case_staff_0, site_0 = staff_cases[0]
            if case_staff_0:
                staff_first_cases = staff_first_cases | await self._evaluate_and_update_first_case(
                    case=case_0,
                    case_staff=case_staff_0,
                    site=site_0,
                    staff_first_cases_per_day=staff_first_cases,
                )
                first_cases |= set(staff_first_cases.values())

            # Zip remaining cases, looking at previous case
            for previous, (case, case_staff, site) in zip(staff_cases, staff_cases[1:]):
                previous_case, previous_case_staff, previous_site = previous

                if case_staff:
                    # Check against first case criteria and update first_cases
                    staff_first_cases = (
                        staff_first_cases
                        | await self._evaluate_and_update_first_case(
                            case=case,
                            case_staff=case_staff,
                            site=site,
                            staff_first_cases_per_day=staff_first_cases,
                        )
                    )
                    first_cases |= set(staff_first_cases.values())

                # Check against preceding case/following
                if CaseDerivedPropertiesService._evaluate_preceding_case_criteria(
                    case, previous_case, first_cases
                ):
                    preceding_cases[case.case_id] = previous_case.case_id

                # Check against flip room logic
                elif CaseDerivedPropertiesService._evaluate_flip_room_criteria(
                    case, previous_case, preceding_cases
                ):
                    flip_room_cases[case.case_id] = True

        first_cases_per_room = await self.resolve_first_case_per_room(first_cases)
        first_case_ids_per_room = {case.case_id for case in first_cases_per_room.values()}

        # Remove overlaps between first_cases, preceding_cases, and flip_room_cases
        # Giving priority to first case, preceding cases and flip room cases
        preceding_cases = {
            k: v for k, v in preceding_cases.items() if k not in first_case_ids_per_room
        }
        flip_room_cases = {
            k: v for k, v in flip_room_cases.items() if k not in first_case_ids_per_room
        }
        flip_room_cases = {k: v for k, v in flip_room_cases.items() if k not in preceding_cases}

        # Collect all unique case IDs
        case_ids = {case.case_id for case, _, _ in cases_to_process}

        cases_to_upsert = [
            CaseDerivedProperties(
                case_id=case_id,
                is_first_case=case_id in first_case_ids_per_room,
                preceding_case_id=preceding_cases.get(case_id),
                is_in_flip_room=flip_room_cases.get(case_id, False),
            )
            for case_id in case_ids
        ]
        cases_to_upsert.sort(key=lambda case: case.case_id)

        await self.case_derived_properties_store.upsert_case_derived_properties(cases_to_upsert)

    @staticmethod
    def _is_categorically_excluded_case(case: Case, case_staff: CaseStaffModel) -> bool:
        """
        Some cases are categorically exempted from qualifying for certain derived properties. E.g a case classified as an add-on is excluded. For more details see:
        https://www.notion.so/apella/Business-Rules-Determining-a-Flip-Room-mini-PRD-24d0ea8c67c04866815c91b6c1d03de0?pvs=4#6ff0215ea563489384471ceb553ab349

        Args:
            case (Case): DB Case entity
            case_staff (CaseStaffModel): DB case staff entity

        Returns:
            bool: Is excluded true or false
        """
        if case.status != SCHEDULED:
            return True
        if case_staff.role not in PRIMARY_SURGEON_ROLES:
            return True
        return False

    @staticmethod
    def _is_categorically_excluded_from_first_cases(
        case_with_staff: CaseWithStaff, date: date
    ) -> bool:
        """
        Check if a case is categorically excluded from being a first case.
        """

        fcots_start_time, fcots_end_time = (
            case_with_staff.case.room.first_case_config.get_start_and_end_time_for_day_of_week(
                date.weekday()
            )
        )

        if fcots_end_time is None or fcots_start_time is None:
            return True

        for case_staff_model in case_with_staff.staff_list:
            if CaseDerivedPropertiesService._is_categorically_excluded_case(
                case_with_staff.case, case_staff_model
            ):
                return True
        if (
            case_with_staff.case.case_classification_types_id is not None
            and case_with_staff.case.case_classification_types_id != "CASE_CLASSIFICATION_ELECTIVE"
        ):
            return True

        # Filter out cases that are not in FCOTS hours.
        if not (
            fcots_start_time
            <= case_with_staff.case.scheduled_start_time.astimezone(
                ZoneInfo(case_with_staff.site.timezone)
            ).time()
            <= fcots_end_time
        ):
            return True

        return False

    async def _query_phases_for_first_cases(
        self, first_case: Case, second_case: Case
    ) -> Optional[Dict[str, PhaseModel]]:
        """
        Query for the associated phases for the given cases. Return None if the query returns
        anything other than one case phase for each case.
        """
        phase_query = PhaseQuery(
            org_id=None,
            site_ids=None,
            room_ids=None,
            case_ids=[first_case.case_id, second_case.case_id],
            min_time=None,
            max_time=None,
            min_start_time=first_case.scheduled_start_time + timedelta(days=-1),
            max_start_time=first_case.scheduled_start_time + timedelta(days=1),
            min_end_time=None,
            max_end_time=None,
            show_human_ground_truth_data=None,
            phase_type="CASE",
            max_duration=None,
            statuses=[PhaseStatus.VALID],
            source_type="unified",
            min_updated_time=None,
            max_updated_time=None,
            min_created_time=None,
            max_created_time=None,
            ensure_phase_start_time_has_not_elapsed=None,
        )
        case_phases = await self.phase_service.query_phases(phase_query=phase_query)
        if len(case_phases) != 2:
            return None
        if (
            case_phases[0].case_id == first_case.case_id
            and case_phases[1].case_id == second_case.case_id
            or case_phases[0].case_id == second_case.case_id
            and case_phases[1].case_id == first_case.case_id
        ):
            return {str(case_phase.case_id): case_phase for case_phase in case_phases}
        # We did not find a case phase for at least one of cases.
        return None

    @staticmethod
    def _get_room_number(room_id: str) -> int:
        """
        Extract the room number from the room_id.

        Args: room_id: The room_id to extract the room number from.

        Returns: int: The room number extracted from the room_id. Otherwise 0
        """
        match = re.search(r"\d+", room_id)
        return int(match.group()) if match else 0

    async def _determine_first_case(self, existing_case: Case, new_case: Case) -> Case:
        """
        Determine which case should be the first case based on scheduled_time, actual_time and room number

        Args:
            existing_case: current first case
            new_case: new case to compare with the current first case

        Returns:
            Case: The case that should be the first case.
        """
        if new_case.scheduled_start_time < existing_case.scheduled_start_time:
            return new_case

        elif new_case.scheduled_start_time == existing_case.scheduled_start_time:
            case_phases = await self._query_phases_for_first_cases(
                first_case=existing_case, second_case=new_case
            )
            # If no phases, we tiebreak on larger room id
            if case_phases is None:
                return (
                    existing_case
                    if CaseDerivedPropertiesService._get_room_number(existing_case.room_id)
                    > CaseDerivedPropertiesService._get_room_number(new_case.room_id)
                    else new_case
                )

            existing_case_phase = case_phases.get(existing_case.case_id)
            new_case_phase = case_phases.get(new_case.case_id)

            if existing_case_phase is None:
                return new_case
            if new_case_phase is None:
                return existing_case

            # Otherwise, keep the case with the earlier actual start time
            return (
                new_case
                if new_case_phase.start_time() < existing_case_phase.start_time()
                else existing_case
            )

        return existing_case

    async def resolve_first_case_per_room(self, first_cases: set[Case]) -> Dict[str, Case]:
        """
        Resolve the first case per room from a set of first cases

        Args:
            first_cases: a set of Cases

        Returns:
            Dict[str, Case]: A dictionary of room_id -> Case for the first case in each room.

        """
        first_case_per_room = {}

        for case in first_cases:
            room_id = case.room_id
            if room_id not in first_case_per_room:
                first_case_per_room[room_id] = case
            else:
                first_case_per_room[room_id] = await self._determine_first_case(
                    existing_case=first_case_per_room[room_id], new_case=case
                )

        return first_case_per_room

    async def _evaluate_and_update_first_case(
        self,
        case: Case,
        case_staff: CaseStaffModel,
        site: Site,
        staff_first_cases_per_day: Dict[int, Case],
    ) -> Dict[int, Case]:
        """
        Evaluate if a case is a first case and update the staffs_first_cases dictionary.
        Args:
            case: The case being evaluated.
            case_staff: staff on the case
            site: site of the case
            staff_first_cases: dict of ordinal date to Case for the staff's first cases. Each staff can only have one first case per day

        Returns:
            Dict[int, Case], an updated staff_first_cases dictionary
        """

        case_date = case.scheduled_start_time.astimezone(ZoneInfo(site.timezone)).date()
        ordinal_date = case_date.toordinal()
        existing_case = staff_first_cases_per_day.get(ordinal_date)
        if self._is_categorically_excluded_from_first_cases(
            CaseWithStaff(case=case, staff_list=[case_staff], site=site), case_date
        ):
            return staff_first_cases_per_day

        entry = (
            case if existing_case is None else await self._determine_first_case(existing_case, case)
        )

        return staff_first_cases_per_day | {ordinal_date: entry}

    @staticmethod
    def _within_preceding_time_cutoff(first_case: Case, second_case: Case, cutoff: int) -> bool:
        """
        Check if two cases are within a certain time cutoff of each other.

        Args:
            first_case: The first case being evaluated.
            second_case: The second case being evaluated.
            cutoff: The maximum allowed time difference (in minutes) between the two cases.

        Returns:
            bool: True if the time gap between the two cases is within the cutoff, False otherwise.
        """

        earlier_case, later_case = (
            (first_case, second_case)
            if second_case.scheduled_start_time >= first_case.scheduled_start_time
            else (second_case, first_case)
        )

        delta_in_minutes = (
            later_case.scheduled_start_time - earlier_case.scheduled_end_time
        ).total_seconds() / 60

        # a negative delta means the cases overlap, which is allowed for Flip room cases
        if delta_in_minutes <= cutoff:
            return True

        return False

    @staticmethod
    def _evaluate_preceding_case_criteria(
        case: Case, previous_case: Case, first_cases: set[Case]
    ) -> bool:
        """
        Given two cases, determine if the second case is a preceding case to the first case by checking against preceding case criteria.
        Args:
            case: The case being evaluated.
            previous_case: The chronologically ordered previous case to compare against.
            first_cases: Set of case IDs that are first cases to be excluded from evaluating.

        Returns:
            bool: True if the case is a preceding case to the previous case, False otherwise.
        """

        if case.case_id in [first_case.case_id for first_case in first_cases]:
            return False

        is_within_cutoff = CaseDerivedPropertiesService._within_preceding_time_cutoff(
            first_case=previous_case,
            second_case=case,
            cutoff=CaseDerivedPropertiesService.PRECEDING_CASE_CUTOFF_IN_MINS,
        )
        is_same_room = case.room_id == previous_case.room_id

        return is_within_cutoff and is_same_room

    @staticmethod
    def _evaluate_flip_room_criteria(
        case: Case, previous_case: Case, preceding_cases: Dict[str, str]
    ) -> bool:
        """
        Given two cases, determine if the second case is a flip room case to the first case by checking against flip room criteria.
        Args:
            case: The case being evaluated.
            previous_case: The chronologically ordered previous case to compare against.
            preceding_cases: Dict of case_id -> preceding_case_id to exclude from evaluating.

        Returns:
            bool: True if the case is a flip room case to the previous case, False otherwise.
        """

        # exclude Following Cases
        if case.case_id in preceding_cases:
            return False

        cases_overlap = (
            previous_case.scheduled_start_time
            <= case.scheduled_start_time
            <= previous_case.scheduled_end_time
        ) or (
            case.scheduled_start_time
            <= previous_case.scheduled_start_time
            <= case.scheduled_end_time
        )

        within_cutoff = CaseDerivedPropertiesService._within_preceding_time_cutoff(
            first_case=previous_case,
            second_case=case,
            cutoff=CaseDerivedPropertiesService.FLIP_ROOM_CASE_CUTOFF_IN_MINS,
        )

        is_different_room = case.room_id != previous_case.room_id

        return (cases_overlap or within_cutoff) and is_different_room
