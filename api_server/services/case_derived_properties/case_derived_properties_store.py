from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from typing import List, Tuple, Union, Optional, Any
from zoneinfo import ZoneInfo

from api_server.services.room.room_store import RoomModel
from sqlalchemy import <PERSON><PERSON><PERSON>, ForeignKey, String, and_, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import mapped_column, Mapped, selectinload

from api_server.services.case.case_staff_store import PRIMARY_SURGEON_ROLES, CaseStaffModel
from api_server.services.case.case_status import SCHEDULED
from api_server.services.case.case_store import Case
from api_server.services.site.site_store import Site
from databases.sql import Base, new_async_session
from databases.sql.helpers import (
    async_postgres_insert_on_conflict_do_update_helper,
)
from databases.sql.mixins import TimestampMixin


class CaseDerivedProperties(Base, TimestampMixin):
    __tablename__ = "case_derived_properties"
    case_id: Mapped[str] = mapped_column(
        String, ForeignKey("cases.case_id"), nullable=False, index=True, primary_key=True
    )
    is_in_flip_room: Mapped[bool] = mapped_column(<PERSON><PERSON><PERSON>, nullable=False, server_default="false")
    preceding_case_id: Mapped[Union[str, None]] = mapped_column(String, nullable=True)
    is_first_case: Mapped[bool] = mapped_column(Boolean, nullable=False, server_default="false")

    def __repr__(self) -> str:
        return (
            f"<CaseDerivedProperties(case_id='{self.case_id}',"
            f" is_first_case='{self.is_first_case}',"
            f" is_in_flip_room='{self.is_in_flip_room}')>"
        )

    def __eq__(self, other: Any) -> bool:
        return (
            isinstance(other, CaseDerivedProperties)
            and self.case_id == other.case_id
            and self.is_in_flip_room == other.is_in_flip_room
            and self.preceding_case_id == other.preceding_case_id
            and self.is_first_case == other.is_first_case
        )


@dataclass
class ProcessCaseDerivedPropertiesDto:
    site_id: str
    date: datetime


class CaseDerivedPropertiesStore:
    async def get_by_case_ids(
        self, case_ids: List[str], session: Optional[AsyncSession] = None
    ) -> List[CaseDerivedProperties]:
        if session is not None:
            return await self._get_by_case_ids(case_ids, session)
        async with new_async_session() as new_session:
            return await self._get_by_case_ids(case_ids, new_session)

    async def _get_by_case_ids(
        self, case_ids: List[str], session: AsyncSession
    ) -> List[CaseDerivedProperties]:
        query = select(CaseDerivedProperties).filter(CaseDerivedProperties.case_id.in_(case_ids))
        query_result = await session.scalars(query)
        return list(query_result.all())

    async def get_cases_to_process(
        self,
        case_properties_update_dto: List[ProcessCaseDerivedPropertiesDto],
        session: Optional[AsyncSession] = None,
    ) -> List[Tuple[Case, Optional[CaseStaffModel], Site]]:
        if session is not None:
            return await self._get_cases_to_process(case_properties_update_dto, session)
        async with new_async_session() as new_session:
            return await self._get_cases_to_process(case_properties_update_dto, new_session)

    async def _get_cases_to_process(
        self,
        case_properties_update_dto: List[ProcessCaseDerivedPropertiesDto],
        session: AsyncSession,
    ) -> List[Tuple[Case, Optional[CaseStaffModel], Site]]:
        filters = []
        query = (
            select(Case, CaseStaffModel, Site)
            .options(selectinload(Case.room).selectinload(RoomModel.first_case_config_record))
            .options(
                selectinload(Case.room)
                .selectinload(RoomModel.site)
                .selectinload(Site.first_case_config_record)
            )
            .join(
                CaseStaffModel,
                and_(
                    CaseStaffModel.case_id == Case.case_id,
                    CaseStaffModel.archived_time.is_(None),
                ),
                isouter=True,
            )
            .join(Site, Site.id == Case.site_id, isouter=True)
            .filter(
                (CaseStaffModel.role.in_(PRIMARY_SURGEON_ROLES)) | (CaseStaffModel.role is None)
            )
            .order_by(
                CaseStaffModel.staff_id.asc(),
                Case.scheduled_start_time.asc(),
            )
            .distinct()
        )
        for update in case_properties_update_dto:
            site_statement = select(Site).filter(Site.id == update.site_id)
            site: Site | None = (await session.scalars(site_statement)).one_or_none()
            if site is None:
                continue

            date_time = self._to_start_of_day_with_timezone(
                date=update.date, time_zone=ZoneInfo(site.timezone)
            )
            end = date_time + timedelta(days=1)

            filters.append(
                and_(
                    Case.status == SCHEDULED,
                    Case.scheduled_end_time >= date_time,
                    Case.scheduled_start_time < end,
                    Case.site_id == update.site_id,
                )
            )
        query = query.filter(or_(*filters))
        result = await session.execute(query)
        return list(result.tuples())

    async def upsert_case_derived_properties(
        self, case_derived_properties: List[CaseDerivedProperties]
    ) -> List[str]:
        case_ids = set()
        if len(case_derived_properties) == 0:
            return []

        cases_to_upsert = []

        for case_derived_property in case_derived_properties:
            if case_derived_property.case_id in case_ids:
                continue
            case_ids.add(case_derived_property.case_id)
            cases_to_upsert.append(case_derived_property)

        # Because multiple threads can be processing the same day at once we upsert.
        # Additionally, we use postgres native upsert as opposed to merging, because
        # of session race conditions
        async with new_async_session() as session:
            ids = await async_postgres_insert_on_conflict_do_update_helper(
                session=session,
                subject=CaseDerivedProperties,
                values=sorted(cases_to_upsert, key=lambda case: case.case_id),
                returning=CaseDerivedProperties.case_id,
            )
            await session.commit()

        return ids

    # This converts any timezone to the start of the day at the target timezone.
    # E.g. a datetime of 2023-04-02T01:00:00+0000 with a target of -7 UTC (i.e. pacific time)
    # gets converted to 2023-04-01T07:00:00+0000
    def _to_start_of_day_with_timezone(self, date: datetime, time_zone: ZoneInfo) -> datetime:
        new_date = (
            datetime(year=date.year, month=date.month, day=date.day, tzinfo=time_zone)
            if date.tzinfo is None
            else date.astimezone(tz=time_zone)
        )

        return datetime(
            year=new_date.year,
            day=new_date.day,
            month=new_date.month,
            tzinfo=time_zone,
        ).astimezone(timezone.utc if date.tzinfo is None else date.tzinfo)
