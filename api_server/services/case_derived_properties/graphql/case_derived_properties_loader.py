from __future__ import annotations

from typing import Optional

from aiodataloader import <PERSON>Loader

from api_server.services.case_derived_properties.case_derived_properties_service import (
    CaseDerivedPropertiesService,
)
from api_server.services.case_derived_properties.case_derived_properties_store import (
    CaseDerivedProperties,
)


class CaseDerivedPropertiesLoader(DataLoader[str, Optional[CaseDerivedProperties]]):
    case_derived_properties_service: CaseDerivedPropertiesService

    def __init__(self, case_derived_properties_service: CaseDerivedPropertiesService):
        super().__init__()
        self.case_derived_properties_service = case_derived_properties_service

    async def batch_load_fn(self, keys: list[str]) -> list[Optional[CaseDerivedProperties]]:
        # Fetch the site by the set of keys
        case_derived_properties_list = (
            await self.case_derived_properties_service.get_properties_by_case_ids(case_ids=keys)
        )
        # Create a dictionary of the site id to the site
        result_dict: dict[str, CaseDerivedProperties] = {
            str(case_derived_properties.case_id): case_derived_properties
            for case_derived_properties in case_derived_properties_list
        }

        # Based on the ordering of the keys passed into the function re-sort the returned site
        return [result_dict.get(str(case_id)) for case_id in keys]
