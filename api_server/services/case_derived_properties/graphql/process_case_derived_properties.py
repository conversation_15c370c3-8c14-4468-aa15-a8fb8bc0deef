from __future__ import annotations

from datetime import datetime
from typing import List

import graphene

from api_server.graphql.context import GrapheneInfo
from api_server.services.case_derived_properties.case_derived_properties_store import (
    ProcessCaseDerivedPropertiesDto,
)


class ProcessCaseDerivedPropertiesInput(graphene.InputObjectType):
    site_id = graphene.String(required=True)
    date = graphene.Date()


class ProcessCaseDerivedProperties(graphene.Mutation):
    class Arguments:
        input = graphene.NonNull(graphene.List(graphene.NonNull(ProcessCaseDerivedPropertiesInput)))

    success = graphene.Boolean()

    @staticmethod
    async def mutate(
        parent: None, info: GrapheneInfo, input: List[ProcessCaseDerivedPropertiesInput]
    ) -> ProcessCaseDerivedProperties:
        # Call process_case_properties once per site + date to refrain from overwhelming the database.
        for cdp in input:
            await info.context.case_derived_properties_service.process_case_properties(
                [
                    ProcessCaseDerivedPropertiesDto(
                        site_id=cdp.site_id,
                        date=datetime(year=cdp.date.year, month=cdp.date.month, day=cdp.date.day),
                    )
                ]
            )

        return ProcessCaseDerivedProperties(success=True)
