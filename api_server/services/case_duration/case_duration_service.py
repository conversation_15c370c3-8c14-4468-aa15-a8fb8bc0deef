import asyncio
from statistics import median, quantiles
from uuid import UUID
import config
from typing import Optional
import aiohttp
from datetime import date, datetime


from api_server.logging.audit import log_calls_to_audit
from api_server.services.case.case_staff_service import CaseStaffService
from api_server.services.case_duration.case_duration_store import (
    CaseDurationStore,
    SurgeonOption,
    ProcedureOption,
)
from api_server.services.staff.staff_service import StaffService
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import READ_ANY_CASE, READ_ANY_CASE_DURATION

from dataclasses import dataclass

from databases.cache.persistent_cache import PersistentCache


DEFAULT_TOT_PREP_DURATION = 20 * 60  # 20 minutes in seconds
DEFAULT_TOT_CLEAN_DURATION = 20 * 60  # 20 minutes in seconds


@dataclass
class SurgeonProcedureMapping:
    surgeon_id: str
    surgeon: str
    procedure: str


@dataclass
class PredictionMetadata:
    surgeon_name: str
    procedure_name: str
    additional_procedures: Optional[list[str]]


@dataclass
class CaseDurationSlotQueryDto:
    surgeon_name: str
    procedure_name: str
    surgeon_id: str
    site_id: str
    site_timezone: str
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    now: Optional[datetime] = None


@dataclass
class TurnoverPrediction:
    meta: PredictionMetadata
    before_case: int
    after_case: int
    open_before_case: int
    clean_after_case: int


@dataclass
class DurationPredictions:
    meta: PredictionMetadata
    standard: int
    complex: int
    samples: list[float]


@dataclass
class CaseDurationService:
    auth: Auth
    persistent_cache: PersistentCache
    case_duration_store: CaseDurationStore
    staff_service: StaffService
    case_staff_service: CaseStaffService

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE, READ_ANY_CASE_DURATION)
    async def get_turnover_prediction(
        self, surgeon_id: Optional[UUID], procedure: str
    ) -> TurnoverPrediction:
        url = config.case_duration_turnover_endpoint()

        # Remove this fallback once case duration model can accept surgeon_id
        if surgeon_id:
            staff_result = await self.staff_service.get_all_staff(staff_ids=[str(surgeon_id)])
            if staff_result:
                surgeon_model = staff_result[0]
            else:
                raise ValueError(f"Surgeon not found")
            surgeon_name = (surgeon_model.last_name + ", " + surgeon_model.first_name).upper()
        else:
            raise ValueError("Surgeon_id must be provided")

        override_org_id = config.case_duration_org_override()
        org_id = override_org_id if override_org_id else self.auth.get_calling_org_id()

        inputs = {
            "inputs": {
                "org_id": org_id,
                "first_primary_surgeon": surgeon_name,
                "first_primary_procedure": procedure,
            }
        }
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url,
                json=inputs,
            ) as response:
                response.raise_for_status()
                result = await response.json()
                # As we transition away from "open_before_case" and "clean_after_case" to "before_case" and "after_case" we will just return the same value for both
                return TurnoverPrediction(
                    PredictionMetadata(surgeon_name, procedure, additional_procedures=[]),
                    round(result.get("prediction_turnover_before_case", 0)),
                    round(result.get("prediction_turnover_after_case", 0)),
                    round(result.get("prediction_turnover_before_case", 0)),
                    round(result.get("prediction_turnover_after_case", 0)),
                )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE, READ_ANY_CASE_DURATION)
    async def get_case_duration_procedures(
        self, procedure_term: Optional[str], surgeon_id: Optional[str]
    ) -> list[ProcedureOption]:
        table = config.bigquery_schedule_assistant_surgeon_procedures_table()
        override_org_id = config.case_duration_org_override()
        org_id = override_org_id if override_org_id else self.auth.get_calling_org_id()

        if procedure_term is None and surgeon_id is None:
            raise ValueError("Either procedureTerm or surgeonId must be provided")

        if org_id is None:
            return []

        if table is None:
            return []

        return self.case_duration_store.get_case_duration_procedures(
            table, org_id, surgeon_id, procedure_term
        )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE, READ_ANY_CASE_DURATION)
    async def get_case_duration_surgeons(
        self, surgeon_term: Optional[str] = None, surgeon_id: Optional[UUID] = None
    ) -> list[SurgeonOption]:
        table = config.bigquery_schedule_assistant_surgeon_procedures_table()
        override_org_id = config.case_duration_org_override()
        org_id = override_org_id if override_org_id else self.auth.get_calling_org_id()

        if org_id is None:
            return []

        if table is None:
            return []

        if surgeon_term is None and surgeon_id is None:
            raise ValueError("Either surgeonTerm or surgeonId must be provided")

        if surgeon_id is not None:
            staff = await self.staff_service.get_all_staff(staff_ids=[str(surgeon_id)])
            if staff:
                return [
                    SurgeonOption(
                        surgeon_id=str(staff[0].id),
                        surgeon_name=(staff[0].last_name + ", " + staff[0].first_name).upper(),
                    )
                ]
            else:
                return []
        if surgeon_term is not None:
            return self.case_duration_store.get_case_duration_surgeons(org_id, surgeon_term, table)
        return []

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE, READ_ANY_CASE_DURATION)
    async def get_duration_predictions(
        self,
        surgeon_id: Optional[UUID],
        procedure: str,
        additional_procedures: Optional[list[str]] = None,
    ) -> DurationPredictions:
        if additional_procedures is None:
            additional_procedures = []

        url = config.case_duration_prediction_bayesian_endpoint()

        # Remove this fallback once case duration model can accept surgeon_id
        if surgeon_id:
            staff_result, staff_appearance_count = await asyncio.gather(
                self.staff_service.get_all_staff(staff_ids=[str(surgeon_id)]),
                self.case_staff_service.get_staff_most_frequent_site(str(surgeon_id)),
            )
            most_frequent_site_id = (
                staff_appearance_count.site_id if staff_appearance_count else None
            )
            if staff_result:
                surgeon_model = staff_result[0]
            else:
                raise ValueError(f"Surgeon not found")
            surgeon_name = (surgeon_model.last_name + ", " + surgeon_model.first_name).upper()
        else:
            raise ValueError("Surgeon_id must be provided")

        override_org_id = config.case_duration_org_override()
        org_id = override_org_id if override_org_id else self.auth.get_calling_org_id()

        inputs = {
            "case_features": {
                "org_id": org_id,
                "first_primary_surgeon": surgeon_name,
                "first_primary_procedure": procedure,
                "additional_procedures": additional_procedures,
                "site_id": most_frequent_site_id,
            }
        }
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url,
                json=inputs,
            ) as response:
                response.raise_for_status()
                result = await response.json()
                return DurationPredictions(
                    meta=PredictionMetadata(surgeon_name, procedure, additional_procedures),
                    standard=round(median(result.get("prediction", [0]))),
                    complex=(
                        # 75th percentile of the prediction samples
                        round(quantiles(result.get("prediction", [0, 0]), n=100)[74])
                    ),
                    samples=result.get("prediction", []),
                )
