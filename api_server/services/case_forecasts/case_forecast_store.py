import datetime
import uuid
from dataclasses import dataclass, field
from enum import Enum, auto
from http import HTT<PERSON>tatus
from typing import Optional, Collection

import graphene
import sqlalchemy
from asyncpg import UniqueViolationError
from sqlalchemy import UniqueConstraint, String, ForeignKey, DateTime, select, UUID, Float, <PERSON><PERSON>an
from sqlalchemy.orm import Mapped, mapped_column, relationship

from apella_cloud_api.exceptions import NotFound, ClientError
from api_server.services.room.room_store import RoomModel
from databases.sql import Base, new_async_session
from databases.sql.helpers import check_exception
from databases.sql.mixins import TimestampMixin, OrgIdMixin, SiteIdMixin
from utils.history_meta import Versioned


class CaseForecastStatus(Enum):
    INVALID = auto()
    VALID = auto()

    def __hash__(self) -> int:
        return self.value.__hash__()

    # Allow sorting of PhaseStatus
    def __eq__(self, other: object) -> bool:
        if not isinstance(other, CaseForecastStatus):
            return NotImplemented
        return self.value.__eq__(other.value)

    def __ne__(self, other: object) -> bool:
        if not isinstance(other, CaseForecastStatus):
            return NotImplemented
        return self.value.__ne__(other.value)

    def __gt__(self, other: object) -> bool:
        if not isinstance(other, CaseForecastStatus):
            return NotImplemented
        return self.value.__gt__(other.value)

    def __lt__(self, other: object) -> bool:
        if not isinstance(other, CaseForecastStatus):
            return NotImplemented
        return self.value.__lt__(other.value)


CaseForecastGraphene = graphene.Enum.from_enum(CaseForecastStatus)


def get_case_forecast_status_enum_from_value(status: int) -> CaseForecastStatus:
    # Graphene parses the enum value (usually an int), rather than the enum itself.
    # So we need to convert the value and do some null checking. Graphene 3.0 fixes this.
    # https://github.com/graphql-python/graphene/issues/1151
    return None if status is None else CaseForecastStatus(status)


class CaseForecastModel(Base, TimestampMixin, OrgIdMixin, SiteIdMixin, Versioned):
    __tablename__ = "case_forecast"
    __table_args__ = (  # type: ignore
        UniqueConstraint(
            "case_id",
            "forecast_variant",
            name="uq_case_id_forecast_variant",
        ),
    )
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), default=uuid.uuid4, index=True, nullable=False, primary_key=True
    )
    case_id: Mapped[str] = mapped_column(
        String, ForeignKey("cases.case_id", ondelete="CASCADE"), nullable=False, index=True
    )
    forecast_start_time: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, index=True
    )
    forecast_end_time: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, index=True
    )
    forecast_variant: Mapped[str] = mapped_column(
        String, nullable=False, index=True, default="stable"
    )
    room_id: Mapped[str] = mapped_column(String, ForeignKey("rooms.id"), nullable=False)
    forecast_status: Mapped[CaseForecastStatus] = mapped_column(
        sqlalchemy.Enum(CaseForecastStatus),
        nullable=False,
        index=True,
        server_default=CaseForecastStatus.VALID.name,
    )

    static_duration_minutes: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True, index=True
    )
    pythia_duration_minutes: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True, index=True
    )

    static_duration_end_time: Mapped[Optional[datetime.datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, index=True
    )
    transformer_end_time: Mapped[Optional[datetime.datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, index=True
    )
    pythia_end_time: Mapped[Optional[datetime.datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, index=True
    )

    turnover_duration_minutes: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True, index=True
    )
    static_start_offset_minutes: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True, index=True
    )

    is_auto_follow: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True, index=True)
    is_overtime: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True, index=True)
    case_start_source: Mapped[Optional[str]] = mapped_column(String, nullable=True, index=True)

    pythia_prediction_tag: Mapped[Optional[str]] = mapped_column(String, nullable=True, index=True)
    bayesian_duration_minutes: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True, index=True
    )
    bayesian_end_time: Mapped[Optional[datetime.datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, index=True
    )

    room = relationship(RoomModel, foreign_keys=[room_id], innerjoin=True)


class CaseForecastStore:
    async def get_forecast_by_id(self, id: str) -> CaseForecastModel:
        """
        Get a forecast by its id
        """
        async with new_async_session() as session:
            try:
                query = select(CaseForecastModel).filter(CaseForecastModel.id == id)
                return (await session.scalars(query)).one()

            except sqlalchemy.orm.exc.NoResultFound:
                raise NotFound(f"No forecast found with id {id}")

    async def get_forecast_for_case_and_variant(
        self, case_id: str, variant: str = "stable"
    ) -> CaseForecastModel:
        """
        Get a forecast by case_id and variant
        """
        async with new_async_session() as session:
            try:
                query = (
                    select(CaseForecastModel)
                    .filter(CaseForecastModel.case_id == case_id)
                    .filter(CaseForecastModel.forecast_variant == variant)
                )
                return (await session.scalars(query)).one()

            except sqlalchemy.orm.exc.NoResultFound:
                raise NotFound(f"No forecast found with case_id {case_id} and variant {variant}")

    async def get_forecasts_by_ids(self, ids: Collection[str]) -> list[CaseForecastModel]:
        """
        Get a list of forecasts by their ids
        """
        async with new_async_session() as session:
            query = select(CaseForecastModel).filter(CaseForecastModel.id.in_(ids))
            return list((await session.scalars(query)).all())

    async def query_forecasts(self, forecast_query: "ForecastQuery") -> list[CaseForecastModel]:
        """
        Returns forecasts based on the given query
        """
        async with new_async_session() as session:
            query = select(CaseForecastModel)

            if forecast_query.min_start_time:
                query = query.filter(
                    CaseForecastModel.forecast_start_time >= forecast_query.min_start_time
                )
            if forecast_query.max_start_time:
                query = query.filter(
                    CaseForecastModel.forecast_start_time <= forecast_query.max_start_time
                )
            if forecast_query.ids:
                query = query.filter(CaseForecastModel.id.in_(forecast_query.ids))
            if forecast_query.case_ids:
                query = query.filter(CaseForecastModel.case_id.in_(forecast_query.case_ids))
            if forecast_query.room_ids:
                query = query.filter(CaseForecastModel.room_id.in_(forecast_query.room_ids))

            if forecast_query.site_ids:
                query = query.filter(CaseForecastModel.site_id.in_(forecast_query.site_ids))

            if forecast_query.forecast_statuses:
                query = query.filter(
                    CaseForecastModel.forecast_status.in_(forecast_query.forecast_statuses)
                )

            # By default, we only return stable forecasts
            if forecast_query.forecast_variants is None:
                forecast_query.forecast_variants = ["stable"]

            if forecast_query.forecast_variants:
                query = query.filter(
                    CaseForecastModel.forecast_variant.in_(forecast_query.forecast_variants)
                )
            return list((await session.scalars(query)).all())

    async def upsert_case_forecasts(
        self, forecasts: list[CaseForecastModel]
    ) -> list[CaseForecastModel]:
        """
        Upsert forecasts
        """
        async with new_async_session() as session:
            for forecast in forecasts:
                await session.merge(forecast)
            try:
                await session.commit()
            except sqlalchemy.exc.IntegrityError as e:
                if check_exception(UniqueViolationError, e):
                    # We could do some parsing on str(e.orig) to extract the id from:
                    # UniqueViolation('duplicate key value violates unique constraint "events_pkey"\n
                    # DETAIL:  Key (id)=(9fc7b91a-ae35-4c8c-a311-585a179acd9c) already exists.\n')
                    # But since this is not meant for customers, we can return the db error
                    raise ClientError(HTTPStatus.UNPROCESSABLE_ENTITY, str(e.orig))
                raise e

            new_forecast_ids = {str(forecast.id) for forecast in forecasts}
            return await self.get_forecasts_by_ids(new_forecast_ids)


@dataclass
class ForecastQuery:
    min_start_time: Optional[datetime.datetime] = None
    max_start_time: Optional[datetime.datetime] = None
    case_ids: Optional[list[str]] = None
    room_ids: Optional[list[str]] = None
    site_ids: Optional[list[str]] = None
    ids: Optional[list[str]] = None
    forecast_variants: Optional[list[str]] = None
    forecast_statuses: Optional[list[CaseForecastStatus]] = None
    query_id: uuid.UUID = field(default_factory=uuid.uuid4)
