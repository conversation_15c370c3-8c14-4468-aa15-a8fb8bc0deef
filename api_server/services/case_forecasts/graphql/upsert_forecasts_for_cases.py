from typing import List

import graphene

from api_server.graphql.context import GrapheneInfo
from api_server.services.case_forecasts.case_forecast_store import (
    CaseForecastModel,
    CaseForecastGraphene,
    get_case_forecast_status_enum_from_value,
)
from api_server.services.case_forecasts.graphql.case_forecast import CaseForecast


class CaseForecastForCaseInput(graphene.InputObjectType):
    case_id = graphene.ID(required=True)
    room_id = graphene.ID(required=True)
    site_id = graphene.ID(required=True)
    organization_id = graphene.ID(required=True)

    forecast_start_time = graphene.DateTime(required=True)
    forecast_end_time = graphene.DateTime(required=True)

    forecast_variant = graphene.String(required=True)
    forecast_status = graphene.Field(CaseForecastGraphene, required=True)

    static_duration_minutes = graphene.Float(required=False)
    pythia_duration_minutes = graphene.Float(required=False)

    static_duration_end_time = graphene.types.datetime.DateTime(required=False)
    transformer_end_time = graphene.types.datetime.DateTime(required=False)
    pythia_end_time = graphene.types.datetime.DateTime(required=False)

    turnover_duration_minutes = graphene.Float(required=False)
    static_start_offset_minutes = graphene.Float(required=False)

    is_overtime = graphene.Boolean(required=False)
    is_auto_follow = graphene.Boolean(required=False)
    pythia_prediction_tag = graphene.String(required=False)
    case_start_source = graphene.String(required=False)

    bayesian_duration_minutes = graphene.Float(required=False)
    bayesian_end_time = graphene.types.datetime.DateTime(required=False)


class UpsertForecastsForCases(graphene.Mutation):
    class Arguments:
        input = graphene.NonNull(graphene.List(graphene.NonNull(CaseForecastForCaseInput)))
        temporality = graphene.String(
            required=False,
        )

    success: bool = graphene.Boolean(required=True)
    created_forecasts: list[CaseForecastModel] = graphene.NonNull(
        graphene.List(graphene.NonNull(CaseForecast))
    )

    @staticmethod
    async def mutate(
        parent: object,
        info: GrapheneInfo,
        input: List[CaseForecastForCaseInput],
        temporality: str | None = None,
    ) -> "UpsertForecastsForCases":
        case_forecast_models = [
            CaseForecastModel(
                org_id=case_forecast.organization_id,
                site_id=case_forecast.site_id,
                room_id=case_forecast.room_id,
                case_id=case_forecast.case_id,
                forecast_start_time=case_forecast.forecast_start_time,
                forecast_end_time=case_forecast.forecast_end_time,
                forecast_variant=case_forecast.forecast_variant,
                forecast_status=get_case_forecast_status_enum_from_value(
                    case_forecast.forecast_status
                ),
                static_duration_minutes=case_forecast.static_duration_minutes,
                transformer_end_time=case_forecast.transformer_end_time,
                pythia_end_time=case_forecast.pythia_end_time,
                is_auto_follow=case_forecast.is_auto_follow,
                turnover_duration_minutes=case_forecast.turnover_duration_minutes,
                static_start_offset_minutes=case_forecast.static_start_offset_minutes,
                is_overtime=case_forecast.is_overtime,
                pythia_duration_minutes=case_forecast.pythia_duration_minutes,
                static_duration_end_time=case_forecast.static_duration_end_time,
                pythia_prediction_tag=case_forecast.pythia_prediction_tag,
                bayesian_duration_minutes=case_forecast.bayesian_duration_minutes,
                bayesian_end_time=case_forecast.bayesian_end_time,
                case_start_source=case_forecast.case_start_source,
            )
            for case_forecast in input
        ]

        return_value = UpsertForecastsForCases()
        return_value.success = True
        return_value.created_forecasts = (
            await info.context.case_forecast_service.upsert_forecasts_for_cases(
                case_forecast_models
            )
        )

        return return_value
