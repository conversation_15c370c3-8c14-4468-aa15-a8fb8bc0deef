from dataclasses import dataclass

from api_server.logging.audit import log_calls_to_audit
from api_server.services.case_labels.case_label_store import (
    CaseLabelStore,
    CaseLabelCategoryModel,
    CaseLabelAssocModel,
)
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import READ_ANY_CASE_NOTE_PLAN, WRITE_ANY_CASE_NOTE_PLAN


@dataclass
class CaseLabelService:
    auth: Auth
    case_label_store: CaseLabelStore

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE_NOTE_PLAN)
    async def query_case_label_form(
        self, org_id: str, site_id: str
    ) -> list[CaseLabelCategoryModel]:
        return await self.case_label_store.query_case_label_form(org_id, site_id)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE_NOTE_PLAN)
    async def query_case_labels(self, case_ids: list[str]) -> list[CaseLabelAssocModel]:
        return await self.case_label_store.query_case_labels(case_ids)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_CASE_NOTE_PLAN)
    async def upsert_case_label_assocs(
        self, models: list[CaseLabelAssocModel]
    ) -> list[CaseLabelAssocModel]:
        return await self.case_label_store.upsert_case_label_assocs(models)
