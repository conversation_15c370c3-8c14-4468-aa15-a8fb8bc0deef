import datetime
import enum
import uuid
from dataclasses import dataclass
from typing import Optional

from sqlalchemy import text, String, <PERSON><PERSON><PERSON>, Integer, Enum, select, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from databases.sql import Base, new_async_session
from databases.sql.helpers import async_postgres_insert_on_conflict_do_update_helper
from databases.sql.mixins import SiteIdMixin, TimestampMixin, ArchivedTimeMixin, OrgIdMixin


class CaseLabelFieldType(enum.Enum):
    BOOLEAN = enum.auto()
    SINGLE_SELECT = enum.auto()


class CaseLabelBooleanValues(enum.Enum):
    TRUE = "CASE_LABEL_BOOLEAN_VALUE_TRUE"
    FALSE = "CASE_LABEL_BOOLEAN_VALUE_FALSE"


class CaseLabelCategoryModel(Base, SiteIdMixin, OrgIdMixin):
    __tablename__ = "case_label_categories"
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=text("gen_random_uuid()"),
    )
    name: Mapped[str] = mapped_column(String, nullable=False)

    fields: Mapped[list["CaseLabelFieldModel"]] = relationship(
        lazy="selectin", order_by="CaseLabelFieldModel.ordinal"
    )


class CaseLabelFieldModel(Base, SiteIdMixin, OrgIdMixin):
    __tablename__ = "case_label_fields"
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=text("gen_random_uuid()"),
    )
    name: Mapped[str] = mapped_column(String, nullable=False)
    type: Mapped[CaseLabelFieldType] = mapped_column(Enum(CaseLabelFieldType), nullable=False)
    category_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("case_label_categories.id"),
        nullable=False,
        index=True,
    )
    ordinal: Mapped[int] = mapped_column(Integer, nullable=False, default=0)

    options: Mapped[list["CaseLabelFieldOptionModel"]] = relationship(lazy="selectin")


class CaseLabelFieldOptionModel(Base):
    __tablename__ = "case_label_field_options"
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=text("gen_random_uuid()"),
    )
    field_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("case_label_fields.id"),
        nullable=False,
        index=True,
    )
    color: Mapped[str] = mapped_column(String, nullable=False)
    abbreviation: Mapped[str] = mapped_column(String, nullable=False)
    value: Mapped[str] = mapped_column(String, nullable=False)


class CaseLabelAssocModel(Base, TimestampMixin, ArchivedTimeMixin):
    __tablename__ = "case_label_assoc"
    __table_args__ = (
        Index(
            "ix_unique_option_case_archived",
            "option_id",
            "case_id",
            unique=True,
            postgresql_where="archived_time IS NULL",
        ),
    )
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=text("gen_random_uuid()"),
    )
    option_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("case_label_field_options.id"),
        nullable=False,
        index=True,
    )
    case_id: Mapped[str] = mapped_column(
        String,
        ForeignKey("cases.case_id"),
        nullable=False,
        index=True,
    )
    updated_by_user_id: Mapped[str] = mapped_column(String, nullable=False)

    option: Mapped["CaseLabelFieldOptionModel"] = relationship(lazy="selectin")


@dataclass
class CaseLabelDTO:
    id: uuid.UUID
    option_id: uuid.UUID
    case_id: str
    updated_by_user_id: str
    field_id: uuid.UUID
    color: str
    abbreviation: str
    value: str
    archived_time: Optional[datetime.datetime]


class CaseLabelStore:
    async def query_case_label_form(
        self, org_id: str, site_id: str
    ) -> list[CaseLabelCategoryModel]:
        async with new_async_session() as session:
            query = select(CaseLabelCategoryModel).filter(
                CaseLabelCategoryModel.org_id == org_id,
                CaseLabelCategoryModel.site_id == site_id,
            )
            return list((await session.scalars(query)).unique().all())

    async def query_case_field(self, field_id: str | uuid.UUID) -> CaseLabelFieldModel:
        async with new_async_session() as session:
            query = select(CaseLabelFieldModel).filter(CaseLabelFieldModel.id == field_id)
            return (await session.scalars(query)).unique().one()

    async def query_case_labels(self, case_ids: list[str]) -> list[CaseLabelAssocModel]:
        async with new_async_session() as session:
            query = (
                select(CaseLabelAssocModel)
                .join(
                    CaseLabelFieldOptionModel,
                    CaseLabelFieldOptionModel.id == CaseLabelAssocModel.option_id,
                )
                .filter(
                    CaseLabelAssocModel.case_id.in_(case_ids),
                    CaseLabelAssocModel.archived_time.is_(None),
                    CaseLabelFieldOptionModel.value != CaseLabelBooleanValues.FALSE.value,
                )
            )
            return list((await session.scalars(query)).unique().all())

    async def upsert_case_label_category(
        self, model: CaseLabelCategoryModel
    ) -> CaseLabelCategoryModel:
        async with new_async_session() as session:
            new_model_ids = await async_postgres_insert_on_conflict_do_update_helper(
                session=session,
                subject=CaseLabelCategoryModel,
                values=[model],
                returning=CaseLabelCategoryModel.id,
            )
            await session.commit()

            assert len(new_model_ids) == 1

            return (
                (
                    await session.scalars(
                        select(CaseLabelCategoryModel).filter(
                            CaseLabelCategoryModel.id == new_model_ids[0]
                        )
                    )
                )
                .unique()
                .one()
            )

    async def upsert_case_label_fields(
        self, models: list[CaseLabelFieldModel]
    ) -> list[CaseLabelFieldModel]:
        async with new_async_session() as session:
            new_model_ids = await async_postgres_insert_on_conflict_do_update_helper(
                session=session,
                subject=CaseLabelFieldModel,
                values=models,
                returning=CaseLabelFieldModel.id,
            )
            await session.commit()

            assert len(new_model_ids) == len(models)

            return list(
                (
                    await session.scalars(
                        select(CaseLabelFieldModel).filter(
                            CaseLabelFieldModel.id.in_(new_model_ids)
                        )
                    )
                )
                .unique()
                .all()
            )

    async def upsert_case_label_field_options(
        self, models: list[CaseLabelFieldOptionModel]
    ) -> list[CaseLabelFieldOptionModel]:
        async with new_async_session() as session:
            new_model_ids = await async_postgres_insert_on_conflict_do_update_helper(
                session=session,
                subject=CaseLabelFieldOptionModel,
                values=models,
                returning=CaseLabelFieldOptionModel.id,
            )
            await session.commit()

            assert len(new_model_ids) == len(models)

            return list(
                (
                    await session.scalars(
                        select(CaseLabelFieldOptionModel).filter(
                            CaseLabelFieldOptionModel.id.in_(new_model_ids)
                        )
                    )
                )
                .unique()
                .all()
            )

    async def upsert_case_label_assocs(
        self, models: list[CaseLabelAssocModel]
    ) -> list[CaseLabelAssocModel]:
        async with new_async_session() as session:
            new_model_ids = await async_postgres_insert_on_conflict_do_update_helper(
                session=session,
                subject=CaseLabelAssocModel,
                values=models,
                returning=CaseLabelAssocModel.id,
            )
            await session.commit()

            assert len(new_model_ids) == len(models)

            return list(
                (
                    await session.scalars(
                        select(CaseLabelAssocModel).filter(
                            CaseLabelAssocModel.id.in_(new_model_ids)
                        )
                    )
                )
                .unique()
                .all()
            )
