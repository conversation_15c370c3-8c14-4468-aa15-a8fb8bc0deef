from aiodataloader import <PERSON><PERSON>oa<PERSON>
from collections import defaultdict

from api_server.services.case_labels.case_label_service import CaseLabelService
from api_server.services.case_labels.case_label_store import (
    CaseLabelAssocModel,
)


class CaseLabelLoader(DataLoader[str, list[CaseLabelAssocModel]]):
    case_label_service: CaseLabelService

    def __init__(self, case_label_service: CaseLabelService):
        super().__init__()
        self.case_label_service = case_label_service

    async def batch_load_fn(self, keys: list[str]) -> list[list[CaseLabelAssocModel]]:
        label_data = await self.case_label_service.query_case_labels(keys)

        # Group results by case id
        case_to_labels = defaultdict(list)
        for row in label_data:
            case_to_labels[row.case_id].append(row)

        # Map results back to input keys
        return [case_to_labels.get(case_id, []) for case_id in keys]
