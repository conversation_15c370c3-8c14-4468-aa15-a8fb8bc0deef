from __future__ import annotations
from typing import Any
import graphene

from api_server.graphql.context import GrapheneInfo
from api_server.services.case_labels.case_label_store import CaseLabelAssocModel, CaseLabelDTO
from api_server.services.case_labels.graphql.case_labels import CaseLabel


class CaseLabelUpsertInput(graphene.InputObjectType):
    case_id = graphene.String(required=True)
    option_id = graphene.String(required=True)
    id = graphene.ID(required=True)
    archived_time = graphene.DateTime(required=False)


class CaseLabelUpsert(graphene.Mutation):
    class Arguments:
        input = graphene.NonNull(graphene.List(graphene.NonNull(CaseLabelUpsertInput)))

    success = graphene.Boolean()
    case_labels = graphene.NonNull(graphene.List(graphene.NonNull(CaseLabel)))

    @staticmethod
    async def mutate(
        root: Any, info: GrapheneInfo, input: list[CaseLabelUpsertInput]
    ) -> CaseLabelUpsert:
        calling_user = info.context.auth.get_calling_user_id()
        models_to_upsert = [
            CaseLabelAssocModel(
                id=cla.id,
                option_id=cla.option_id,
                case_id=cla.case_id,
                updated_by_user_id=calling_user,
                archived_time=cla.archived_time,
            )
            for cla in input
        ]
        upserted_models = await info.context.case_label_service.upsert_case_label_assocs(
            models_to_upsert
        )

        return CaseLabelUpsert(
            success=True,
            case_labels=[
                CaseLabelDTO(
                    id=result.id,
                    option_id=result.option.id,
                    case_id=result.case_id,
                    updated_by_user_id=result.updated_by_user_id,
                    field_id=result.option.field_id,
                    color=result.option.color,
                    abbreviation=result.option.abbreviation,
                    value=result.option.value,
                    archived_time=result.archived_time,
                )
                for result in upserted_models
            ],
        )
