from typing import Optional, Any

import graphene

from api_server.services.case_labels.case_label_store import (
    CaseLabelFieldType,
    CaseLabelBooleanValues,
    CaseLabelDTO,
)

CaseLabelFieldTypeGraphene = graphene.Enum.from_enum(CaseLabelFieldType)


class CaseLabelFieldOptionValue(graphene.Union):
    class Meta:
        types = (graphene.Boolean, graphene.String)


class CaseLabelFieldOption(graphene.ObjectType):
    id = graphene.ID(required=True)
    field_id = graphene.ID(required=True)
    color = graphene.String(required=True)
    abbreviation = graphene.String(required=True)
    value = graphene.String(required=True)
    boolean_value = graphene.Boolean(required=False)

    @staticmethod
    async def resolve_boolean_value(
        option: CaseLabelDTO, *args: Any, **kwargs: Any
    ) -> Optional[bool]:
        if option.value == CaseLabelBooleanValues.TRUE.value:
            return True
        elif option.value == CaseLabelBooleanValues.FALSE.value:
            return False
        else:
            return None


class CaseLabelField(graphene.ObjectType):
    id = graphene.ID(required=True)
    name = graphene.String(required=True)
    type = graphene.Field(CaseLabelFieldTypeGraphene, required=True)
    ordinal = graphene.Int(required=True)

    options = graphene.NonNull(graphene.List(graphene.NonNull(CaseLabelFieldOption)))


class CaseLabelCategory(graphene.ObjectType):
    id = graphene.ID(required=True)
    name = graphene.String(required=True)

    fields = graphene.NonNull(graphene.List(graphene.NonNull(CaseLabelField)))


class CaseLabel(CaseLabelFieldOption):
    case_id = graphene.ID(required=True)
    option_id = graphene.ID(required=True)
    updated_by_user_id = graphene.String(required=True)
    archived_time = graphene.DateTime(required=False)
