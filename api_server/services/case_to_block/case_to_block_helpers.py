from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime
from typing import Optional

from api_server.services.apella_case.apella_case_store import ApellaCase, CaseType
from api_server.services.case.case_staff_store import CaseStaffModel


@dataclass
class ScheduledAndActualCase:
    site_id: str
    room_id: str
    case_id: str
    scheduled_start_time: datetime
    scheduled_end_time: datetime
    status: str
    staff: list[CaseStaffModel]
    actual_start_time: Optional[datetime]
    actual_end_time: Optional[datetime]


def get_cases_formatted(
    apella_case_results: list[ApellaCase],
    case_staff_results: list[CaseStaffModel],
) -> list[ScheduledAndActualCase]:
    case_dict = defaultdict(list)
    for case_staff in case_staff_results:
        case_dict[case_staff.case_id].append(case_staff)

    formatted_cases = []
    for case in apella_case_results:
        if (
            case.case_id is not None
            and case.scheduled_start_time
            and case.scheduled_end_time
            and case.scheduled_case_status
        ):
            formatted_cases.append(
                ScheduledAndActualCase(
                    site_id=case.site_id,
                    room_id=case.room_id,
                    case_id=case.case_id,
                    scheduled_start_time=case.scheduled_start_time,
                    scheduled_end_time=case.scheduled_end_time,
                    actual_start_time=case.start_time if case.type != CaseType.FORECAST else None,
                    actual_end_time=case.end_time if case.type == CaseType.COMPLETE else None,
                    staff=case_dict[case.case_id] or [],
                    status=case.scheduled_case_status,
                )
            )
    return formatted_cases
