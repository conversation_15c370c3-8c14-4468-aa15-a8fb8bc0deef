import asyncio
from dataclasses import dataclass
from datetime import date, datetime, time, timedelta
from itertools import chain
from typing import Optional
from zoneinfo import ZoneInfo

from api_server.logging.audit import log_calls_to_audit
from api_server.services.apella_case.apella_case_service import ApellaCaseService
from api_server.services.apella_case.apella_case_store import ApellaCaseQuery
from api_server.services.available_time_slot.interval import Interval
from api_server.services.block.block_models import BlockTimeInterval
from api_server.services.block.block_service import BlockService
from api_server.services.block_utilization.constants import (
    RELEASE_CUTOFF_THRESHOLD,
)
from api_server.services.case.case_staff_service import CaseStaffService
from api_server.services.case.case_staff_store import (
    CaseStaffQuery,
)
from api_server.services.case_to_block.case_to_block_helpers import (
    ScheduledAndActualCase,
    get_cases_formatted,
)
from api_server.services.case_to_block.case_to_block_models import (
    CaseToBlockOverridesQuery,
)
from apella_cloud_api.dtos import CaseToBlock
from api_server.services.case_to_block.case_to_block_store import (
    CaseToBlockOverrideModel,
    CaseToBlockStore,
)
from api_server.services.case_to_block.constants import HIGH_CONFIDENCE_USER_OVERRIDE_SCORE
from api_server.services.site.site_service import SiteService
from api_server.services.site.site_store import Site
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import READ_ANY_BLOCK, READ_ANY_CASE

from api_server.services.case.case_status import CANCELLED_STATUSES, SCHEDULED


import config


@dataclass
class CaseToBlockService:
    auth: Auth
    block_service: BlockService
    apella_case_service: ApellaCaseService
    case_staff_service: CaseStaffService
    site_service: SiteService

    case_to_block_store: CaseToBlockStore

    def _get_clipped_seconds_for_intervals(
        self,
        interval_start_time: datetime,
        interval_end_time: datetime,
        start_time: datetime,
        end_time: datetime,
    ) -> int:
        if start_time < interval_end_time and end_time > interval_start_time:
            # Clip the actual case time to ensure it stays within the block interval boundaries.
            # This handles cases that start before or end after the corresponding block time interval.
            clipped_actual_start = max(interval_start_time, start_time)
            clipped_actual_end = min(interval_end_time, end_time)
            return int((clipped_actual_end - clipped_actual_start).total_seconds())
        return 0

    # for all case to block_time_intervals mappings get the best one based off surgeon match, overlap time, room_match, same_day, etc.
    def _get_best_block_time_interval_for_case(
        self,
        case: ScheduledAndActualCase,
        block_time_intervals: list[BlockTimeInterval],
        site: Site,
        day: date,
        block_id: Optional[str],
    ) -> Optional[CaseToBlock]:
        if case.actual_start_time and case.actual_end_time:
            case_interval = Interval(
                start_time=case.actual_start_time, end_time=case.actual_end_time
            )
            case_day_interval = Interval(
                start_time=datetime.combine(
                    case.actual_start_time.date(), time.min, tzinfo=ZoneInfo(site.timezone)
                ),
                end_time=datetime.combine(
                    case.actual_end_time.date(), time.max, tzinfo=ZoneInfo(site.timezone)
                ),
            )
        else:
            case_interval = Interval(
                start_time=case.scheduled_start_time, end_time=case.scheduled_end_time
            )
            case_day_interval = Interval(
                start_time=datetime.combine(
                    case.scheduled_start_time.date(), time.min, tzinfo=ZoneInfo(site.timezone)
                ),
                end_time=datetime.combine(
                    case.scheduled_end_time.date(), time.max, tzinfo=ZoneInfo(site.timezone)
                ),
            )

        max_score = 0
        best_block_time_interval = None
        max_overlap_bt = timedelta(0)
        for bti in block_time_intervals:
            bti_interval = Interval(start_time=bti.start_time, end_time=bti.end_time)
            overlap_duration = bti_interval.overlap_between(case_interval)
            score = 0
            surgeon_match = any(
                staff.staff_id in bti.surgeon_ids
                for staff in case.staff
                if bti.surgeon_ids is not None
            )
            room_match = case.room_id == bti.room_id

            overlap_exists = overlap_duration > timedelta(seconds=0)

            same_day = bti_interval.overlaps(case_day_interval)
            conditions = [
                (overlap_exists and surgeon_match and room_match, 5),
                (overlap_exists and surgeon_match, 4),
                (surgeon_match and same_day, 3),
                (overlap_exists and room_match, 2),
                (same_day and room_match, 1),
            ]
            score = next((score for condition, score in conditions if condition), 0)

            if score > max_score:
                max_score = score
                best_block_time_interval = bti
                max_overlap_bt = overlap_duration
            elif score == max_score and overlap_duration > max_overlap_bt:
                max_overlap_bt = overlap_duration
                best_block_time_interval = bti

        if (max_score == 0 or best_block_time_interval is None) or (
            block_id and block_id != best_block_time_interval.block_id
        ):
            return None

        actual_case_seconds = (
            int((case.actual_end_time - case.actual_start_time).total_seconds())
            if case.actual_start_time and case.actual_end_time
            else None
        )

        return CaseToBlock(
            site_id=site.id,
            case_id=case.case_id,
            score=max_score,
            block_id=str(best_block_time_interval.block_id),
            block_date=day,
            utilized_scheduled_case_seconds=self._get_clipped_seconds_for_intervals(
                interval_start_time=best_block_time_interval.start_time,
                interval_end_time=best_block_time_interval.end_time,
                start_time=case.scheduled_start_time,
                end_time=case.scheduled_end_time,
            ),
            scheduled_case_seconds=int(
                (case.scheduled_end_time - case.scheduled_start_time).total_seconds()
            ),
            actual_case_seconds=actual_case_seconds,
            utilized_case_seconds=self._get_clipped_seconds_for_intervals(
                interval_start_time=best_block_time_interval.start_time,
                interval_end_time=best_block_time_interval.end_time,
                start_time=case.actual_start_time,
                end_time=case.actual_end_time,
            )
            if case.actual_start_time and case.actual_end_time
            else None,
            status=case.status,
            overridden=False,
        )

    def _get_cases_to_block_time_intervals_for_day(
        self,
        block_time_intervals: list[BlockTimeInterval],
        cases: list[ScheduledAndActualCase],
        site: Site,
        day: date,
        case_overrides: dict[str, CaseToBlockOverrideModel],
        block_id: Optional[str],
        exclude_cancelled: Optional[bool] = False,
    ) -> list[CaseToBlock]:
        cases_to_block_times = []
        for case in cases:
            # override found
            if case.case_id in case_overrides:
                override = case_overrides[case.case_id]
                scheduled_case_seconds = int(
                    (case.scheduled_end_time - case.scheduled_start_time).total_seconds()
                )
                actual_case_seconds = (
                    int((case.actual_end_time - case.actual_start_time).total_seconds())
                    if case.actual_start_time and case.actual_end_time
                    else None
                )
                if block_id and block_id != override.block_id:
                    continue
                cases_to_block_times.append(
                    CaseToBlock(
                        block_date=override.block_date,
                        block_id=override.block_id,
                        case_id=override.case_id,
                        score=HIGH_CONFIDENCE_USER_OVERRIDE_SCORE,
                        site_id=site.id,
                        overridden_turnover_seconds=override.utilized_turnover_minutes * 60
                        if override and override.utilized_turnover_minutes is not None
                        else None,
                        overridden_utilized_case_seconds=override.utilized_procedure_minutes * 60
                        if override and override.utilized_procedure_minutes is not None
                        else None,
                        utilized_case_seconds=override.utilized_procedure_minutes * 60
                        if override and override.utilized_procedure_minutes is not None
                        else actual_case_seconds
                        if actual_case_seconds
                        else 0,
                        actual_case_seconds=actual_case_seconds,
                        scheduled_case_seconds=scheduled_case_seconds,
                        status=case.status,
                        overridden=True,
                    )
                )
                continue
            if case.status in CANCELLED_STATUSES and exclude_cancelled:
                continue
            # for all block_time_intervals in the same day as the case get the best match
            case_to_block_time: Optional[CaseToBlock] = self._get_best_block_time_interval_for_case(
                case=case,
                block_time_intervals=block_time_intervals,
                site=site,
                day=day,
                block_id=block_id,
            )
            # none means the case scores 0 across all block time intervals for day
            if case_to_block_time:
                cases_to_block_times.append(case_to_block_time)
        return list(chain(cases_to_block_times))

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE, READ_ANY_BLOCK)
    async def get_cases_to_blocks_for_date_range(
        self,
        min_date: datetime,
        max_date: datetime,
        site_id: str,
        block_id: Optional[str],
        exclude_cancelled: Optional[bool] = False,
    ) -> list[CaseToBlock]:
        site = await self.site_service.get_site(site_id)

        min_end_time = datetime.combine(min_date, time.min, tzinfo=ZoneInfo(site.timezone))
        max_start_time = datetime.combine(max_date, time.max, tzinfo=ZoneInfo(site.timezone))

        today_min = datetime.combine(datetime.now(), time.min, tzinfo=ZoneInfo(site.timezone))
        today_max = datetime.combine(datetime.now(), time.max, tzinfo=ZoneInfo(site.timezone))

        overrides = await self.case_to_block_store.query_case_to_block_overrides(
            case_to_block_overrides_query=CaseToBlockOverridesQuery(
                min_date=min_date, max_date=max_date
            )
        )
        cases_to_block_today = []
        # includes today
        if min_end_time <= today_min <= max_start_time:
            # query the data all block time interval and case data all at once
            (
                today_block_time_intervals,
                today_case_data,
            ) = await asyncio.gather(
                self.block_service.query_block_times_available_intervals(
                    site_id=site.id,
                    min_end_time=today_min,
                    max_start_time=today_max,
                    release_cutoff=RELEASE_CUTOFF_THRESHOLD,
                ),
                self.apella_case_service.query_cases(
                    query=ApellaCaseQuery(
                        site_ids=[site_id],
                        min_end_time=today_min,
                        max_start_time=today_max,
                        # otherwise defaults to scheduled only
                        scheduled_case_status=CANCELLED_STATUSES + [SCHEDULED],
                    )
                ),
            )

            case_ids = [
                apella_case.case_id for apella_case in today_case_data if apella_case.case_id
            ]
            staff_results = []
            if len(case_ids) != 0:
                staff_results = await self.case_staff_service.query_case_staff_relationships(
                    CaseStaffQuery(
                        case_ids=case_ids,
                    )
                )
            case_overrides = {override.case_id: override for override in overrides}
            formatted_cases = get_cases_formatted(
                apella_case_results=today_case_data,
                case_staff_results=staff_results,
            )
            cases_to_block_today = self._get_cases_to_block_time_intervals_for_day(
                cases=formatted_cases,
                block_time_intervals=today_block_time_intervals,
                site=site,
                day=today_min.date(),
                case_overrides=case_overrides,
                exclude_cancelled=exclude_cancelled,
                block_id=block_id,
            )

        # get all values other than today
        table = config.bigquery_block_time_cases()
        case_to_match = (
            {}
            if table is None
            else await self.case_to_block_store.get_cases_to_blocks_without_today(
                min_date=min_date,
                max_date=max_date,
                table=table,
                today=today_min,
                block_id=block_id,
            )
        )

        # if overrides found then replace the case to block match for the corresponding case_id there will always be less overrides than the number of case matchesfound
        case_overrides = {override.case_id: override for override in overrides}
        for case_id in case_overrides:
            if case_id in case_to_match:
                override = case_overrides[case_id]
                actual_case_seconds = case_to_match[case_id].actual_case_seconds
                scheduled_case_seconds = case_to_match[case_id].scheduled_case_seconds
                if block_id and block_id != override.block_id:
                    continue
                case_to_match[case_id] = CaseToBlock(
                    block_date=override.block_date,
                    block_id=override.block_id,
                    case_id=override.case_id,
                    score=HIGH_CONFIDENCE_USER_OVERRIDE_SCORE,
                    site_id=site.id,
                    overridden_turnover_seconds=override.utilized_turnover_minutes * 60
                    if override and override.utilized_turnover_minutes is not None
                    else None,
                    overridden_utilized_case_seconds=override.utilized_procedure_minutes * 60
                    if override and override.utilized_procedure_minutes is not None
                    else None,
                    utilized_case_seconds=override.utilized_procedure_minutes * 60
                    if override and override.utilized_procedure_minutes is not None
                    else actual_case_seconds
                    if actual_case_seconds
                    else 0,
                    actual_case_seconds=actual_case_seconds,
                    scheduled_case_seconds=scheduled_case_seconds,
                    status=case_to_match[case_id].status,
                    overridden=True,
                )

        # exclude cancelled cases if boolean is set to true
        cases_to_block_all_dates_minus_today = [
            case_to_block
            for case_to_block in case_to_match.values()
            if not (
                case_to_block.status in CANCELLED_STATUSES
                and not case_to_block.overridden
                and exclude_cancelled
            )
        ]
        return cases_to_block_today + cases_to_block_all_dates_minus_today

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE, READ_ANY_BLOCK)
    async def get_case_to_block_override(self, case_id: str) -> Optional[CaseToBlockOverrideModel]:
        return await self.case_to_block_store.get_case_to_block_override(case_id)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE, READ_ANY_BLOCK)
    async def query_case_to_block_overrides(
        self, query: CaseToBlockOverridesQuery
    ) -> list[CaseToBlockOverrideModel]:
        return await self.case_to_block_store.query_case_to_block_overrides(query)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE, READ_ANY_BLOCK)
    async def upsert_case_to_block_overrides(
        self, case_to_block_overrides: list[CaseToBlockOverrideModel]
    ) -> list[CaseToBlockOverrideModel]:
        return await self.case_to_block_store.upsert_case_to_block_overrides(
            case_to_block_overrides=case_to_block_overrides
        )
