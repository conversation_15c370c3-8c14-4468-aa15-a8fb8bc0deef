from datetime import date, datetime
from typing import Optional
from dateutil import rrule

from sqlalchemy import String, Integer, Date, select
from apella_cloud_api.dtos import CaseToBlock
from api_server.services.case_to_block.case_to_block_models import (
    CaseToBlockOverridesQuery,
)
from databases.big_query import BigQueryClient
from databases.sql import Base, new_async_session
from sqlalchemy.orm import mapped_column, Mapped
from sqlalchemy.ext.asyncio import AsyncSession

from databases.sql.mixins import TimestampMixin
from utils.history_meta import Versioned
from google.cloud.bigquery import QueryJobConfig, ScalarQueryParameter


class CaseToBlockOverrideModel(Base, TimestampMixin, Versioned):
    __tablename__ = "case_to_block_overrides"

    block_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)

    case_id: Mapped[str] = mapped_column(String, nullable=False, primary_key=True)
    block_id: Mapped[str] = mapped_column(String, nullable=False)

    utilized_procedure_minutes: Mapped[int] = mapped_column(Integer, nullable=True)
    utilized_turnover_minutes: Mapped[int] = mapped_column(Integer, nullable=True)

    user_id: Mapped[str] = mapped_column(String, nullable=False)

    note: Mapped[str] = mapped_column(String, nullable=True)


CaseToBlockOverrideHistoryModel = CaseToBlockOverrideModel.__history_mapper__.class_  # type: ignore [attr-defined]


class CaseToBlockStore:
    async def _query_case_to_block_overrides(
        self, session: AsyncSession, case_to_block_overrides_query: CaseToBlockOverridesQuery
    ) -> list[CaseToBlockOverrideModel]:
        query = select(CaseToBlockOverrideModel)
        if case_to_block_overrides_query.min_date and case_to_block_overrides_query.max_date:
            all_dates: list[date] = [
                dt.date()
                for dt in list(
                    rrule.rrule(
                        rrule.DAILY,
                        dtstart=case_to_block_overrides_query.min_date,
                        until=case_to_block_overrides_query.max_date,
                    )
                )
            ]
            query = query.filter(CaseToBlockOverrideModel.block_date.in_(all_dates))

        if case_to_block_overrides_query.block_ids:
            query = query.filter(
                CaseToBlockOverrideModel.block_id.in_(case_to_block_overrides_query.block_ids)
            )

        if case_to_block_overrides_query.case_ids:
            query = query.filter(
                CaseToBlockOverrideModel.case_id.in_(case_to_block_overrides_query.case_ids)
            )

        return list((await session.scalars(query)).all())

    async def query_case_to_block_overrides(
        self, case_to_block_overrides_query: CaseToBlockOverridesQuery
    ) -> list[CaseToBlockOverrideModel]:
        async with new_async_session() as session:
            return await self._query_case_to_block_overrides(
                session=session, case_to_block_overrides_query=case_to_block_overrides_query
            )

    async def get_case_to_block_override(self, case_id: str) -> Optional[CaseToBlockOverrideModel]:
        async with new_async_session() as session:
            query = select(CaseToBlockOverrideModel).filter(
                CaseToBlockOverrideModel.case_id == case_id
            )
            return (await session.scalars(query)).one_or_none()

    async def upsert_case_to_block_overrides(
        self, case_to_block_overrides: list[CaseToBlockOverrideModel]
    ) -> list[CaseToBlockOverrideModel]:
        async with new_async_session() as session:
            results = []
            for override in case_to_block_overrides:
                new_override = await session.merge(override)
                results.append(new_override)
            await session.commit()
            return results

    async def get_case_to_block_override_history(
        self, case_id: str
    ) -> list[CaseToBlockOverrideModel]:
        async with new_async_session() as session:
            base_results = list(
                await session.scalars(
                    select(CaseToBlockOverrideHistoryModel).filter(
                        CaseToBlockOverrideHistoryModel.case_id == case_id
                    )
                )
            )
            history_results = []
            for override_history in base_results:
                # For each history row, we create a new CaseToBlockOverrideModel instance
                override = CaseToBlockOverrideModel()
                # And use dict to copy all of the properties except for `_sa_instance_state`
                _sa_instance_state = override.__dict__["_sa_instance_state"]
                override.__dict__ = override_history.__dict__
                override.__dict__["_sa_instance_state"] = _sa_instance_state
                history_results.append(override)
            return history_results

    async def get_cases_to_blocks_without_today(
        self, min_date: date, max_date: date, table: str, today: datetime, block_id: Optional[str]
    ) -> dict[str, CaseToBlock]:
        client = BigQueryClient.get_client()
        # exclude todays matches
        query = f"""
            SELECT
                score,
                block_id,
                site_id,
                status,
                apella_case_id as case_id,
                block_time_date as date,
                actual_case_seconds,
                scheduled_case_seconds,
                utilized_actual_seconds,
                utilized_scheduled_seconds
            FROM `{table}`
            WHERE @min_date <= block_time_date AND block_time_date <= @max_date AND block_time_date != @today
        """

        query_parameters = [
            ScalarQueryParameter("min_date", "STRING", min_date.strftime("%Y-%m-%d")),
            ScalarQueryParameter("max_date", "STRING", max_date.strftime("%Y-%m-%d")),
            ScalarQueryParameter("today", "STRING", today.strftime("%Y-%m-%d")),
        ]
        if block_id:
            query += " AND @selected_block_id = block_id"
            query_parameters.append(
                ScalarQueryParameter("selected_block_id", "STRING", block_id),
            )

        job_config = QueryJobConfig(query_parameters=query_parameters)
        result = client.query(query, job_config).result()

        return {
            row.case_id: CaseToBlock(
                case_id=row.case_id,
                site_id=row.site_id,
                block_id=row.block_id,
                block_date=row.date,
                score=row.score,
                scheduled_case_seconds=row.scheduled_case_seconds,
                actual_case_seconds=row.actual_case_seconds,
                utilized_scheduled_case_seconds=row.utilized_scheduled_seconds,
                utilized_case_seconds=row.utilized_actual_seconds,
                status=row.status,
                overridden=False,
            )
            for row in result
        }
