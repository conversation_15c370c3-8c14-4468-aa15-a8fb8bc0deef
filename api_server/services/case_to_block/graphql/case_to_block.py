from typing import Optional
import graphene

import apella_cloud_api.dtos
from api_server.services.case.graphql.scheduled_case import ScheduledCase
from api_server.services.case.case_store import Case

from api_server.graphql.context import GrapheneInfo
from api_server.graphql.pagination.fields import PaginationConnection


class CaseToBlockInput(graphene.InputObjectType):
    min_date = graphene.DateTime(required=True)
    max_date = graphene.DateTime(required=True)
    site_id = graphene.String(required=True)
    block_id = graphene.String(default=None)


class CaseToBlock(graphene.ObjectType):
    case_id = graphene.String(required=True)
    scheduled_case = graphene.Field(lambda: ScheduledCase)
    block_id = graphene.String(required=True)

    block_date = graphene.Date(required=True)
    score = graphene.Int(required=True)
    overridden = graphene.Boolean(required=True)

    utilized_case_seconds = graphene.Int()
    actual_case_seconds = graphene.Int()
    overridden_turnover_seconds = graphene.Int()
    overridden_utilized_case_seconds = graphene.Int()

    @staticmethod
    async def resolve_scheduled_case(
        case_to_block: apella_cloud_api.dtos.CaseToBlock, info: GrapheneInfo
    ) -> Optional[Case]:
        return await info.context.case_loader.load(str(case_to_block.case_id))


class CaseToBlockConnection(PaginationConnection):
    class Meta:
        node = CaseToBlock

    class CaseToBlockEdge(graphene.ObjectType):
        node = graphene.Field(CaseToBlock, required=True)
        cursor = graphene.String(required=True)

    edges = graphene.List(graphene.NonNull(CaseToBlockEdge), required=True)
