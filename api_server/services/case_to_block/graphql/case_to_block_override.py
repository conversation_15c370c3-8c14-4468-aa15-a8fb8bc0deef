from typing import Optional

import graphene

from apella_cloud_api.exceptions import NotFound
from api_server.graphql.context import GrapheneInfo
import api_server.services.users.user_store as user_store
from api_server.services.case_to_block import case_to_block_store
import api_server.services.users.graphql.user_type as user_schema


class CaseToBlockOverrideInput(graphene.InputObjectType):
    min_date = graphene.DateTime()
    max_date = graphene.DateTime()
    block_ids = graphene.List(graphene.NonNull(graphene.String))
    case_ids = graphene.List(graphene.NonNull(graphene.String))


class CaseToBlockOverride(graphene.ObjectType):
    case_id = graphene.String(required=True)
    block_id = graphene.String(required=True)
    block_date = graphene.Date(required=True)
    utilized_procedure_minutes = graphene.Int()
    utilized_turnover_minutes = graphene.Int()
    user_id = graphene.String(required=True)
    note = graphene.String()
    user = graphene.Field(lambda: user_schema.User, required=False)

    @staticmethod
    async def resolve_user(
        parent: case_to_block_store.CaseToBlockOverrideModel, info: GrapheneInfo
    ) -> Optional[user_store.User]:
        try:
            user = await info.context.user_loader.load(parent.user_id)
            assert isinstance(user, user_store.User)
            return user
        except NotFound:
            return None
