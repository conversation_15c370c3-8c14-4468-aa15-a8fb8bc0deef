from dataclasses import dataclass
from datetime import datetime, date
from typing import Sequence, List

import holidays

from apella_cloud_api.dtos import RoomClosureQueryDto, SiteClosureQueryDto
from api_server.logging.audit import log_calls_to_audit
from api_server.services.closures.closure_store import ClosureStore, RoomClosure, SiteClosure
from api_server.services.room.room_store import RoomModel
from api_server.services.site.site_service import SiteService
from api_server.services.site.site_store import Site
from auth.auth import Auth
from auth.auth_decorator import requires_permission_prefix, requires_permissions
from auth.permissions import (
    READ_ANY_ROOM,
    READ_SITE_PREFIX,
    WRITE_ANY_SITE,
    WRITE_ANY_BLOCK,
)

DEFAULT_HOLIDAYS = [
    "New Year's Day",
    "Memorial Day",
    "Independence Day",
    "Labor Day",
    "Thanksgiving",
    "Christmas Day",
]


@dataclass
class ClosureService:
    auth: Auth
    closure_store: ClosureStore
    site_service: SiteService

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_ROOM)
    async def query_room_closures(
        self,
        query: RoomClosureQueryDto,
    ) -> Sequence[RoomClosure]:
        return await self.closure_store.query_room_closures(
            room_id=query.room_id,
            room_ids=query.room_ids,
            site_id=query.site_id,
            min_end_time=query.min_end_time,
            max_start_time=query.max_start_time,
        )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_ROOM)
    async def get_room_by_closure_id(self, room_closure_id: str) -> RoomModel:
        return await self.closure_store.get_room_by_closure_id(room_closure_id=room_closure_id)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def delete_room_closure(self, room_closure_id: str) -> None:
        return await self.closure_store.delete_room_closure(room_closure_id=room_closure_id)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_BLOCK)
    async def create_room_closure(
        self,
        room_id: str,
        start_time: datetime,
        end_time: datetime,
    ) -> RoomClosure:
        return await self.closure_store.create_room_closure(
            RoomClosure(room_id=room_id, start_time=start_time, end_time=end_time)
        )

    @log_calls_to_audit()
    @requires_permission_prefix(READ_SITE_PREFIX)
    async def query_site_closures(
        self,
        query: SiteClosureQueryDto,
    ) -> Sequence[SiteClosure]:
        return await self.closure_store.query_site_closures(
            site_id=query.site_id,
            closure_date=query.closure_date,
            start_date=query.start_date,
            end_date=query.end_date,
        )

    @log_calls_to_audit()
    @requires_permission_prefix(READ_SITE_PREFIX)
    async def get_site_by_closure_id(self, site_closure_id: str) -> Site:
        return await self.closure_store.get_site_by_closure_id(site_closure_id=site_closure_id)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_SITE, enforce_universal_user=True)
    async def delete_site_closure(self, site_closure_id: str) -> None:
        return await self.closure_store.delete_site_closure(site_closure_id=site_closure_id)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_SITE, enforce_universal_user=True)
    async def create_site_closure(
        self,
        site_id: str,
        closure_date: date,
        reason: str,
    ) -> SiteClosure:
        try:
            return await self.closure_store.create_site_closure(
                SiteClosure(site_id=site_id, closure_date=closure_date, reason=reason.strip())
            )
        except ValueError as e:
            raise e

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_SITE, enforce_universal_user=True)
    async def create_default_site_closures(
        self, start_date: date, end_date: date
    ) -> Sequence[SiteClosure]:
        try:
            end_year = end_date.year + 1

            us_holidays = holidays.country_holidays(
                country="US", years=[*range(start_date.year, end_year)]
            )

            site_closures: List[SiteClosure] = []

            sites = await self.site_service.query_sites()
            for site in sites:
                for holiday in us_holidays.items():
                    current = await self.closure_store.query_site_closures(
                        site_id=site.id,
                        closure_date=holiday[0],
                    )
                    if (
                        not current
                        and holiday[1] in DEFAULT_HOLIDAYS
                        and start_date < holiday[0] <= end_date
                    ):
                        closure = await self.closure_store.create_site_closure(
                            SiteClosure(site_id=site.id, closure_date=holiday[0], reason=holiday[1])
                        )
                        site_closures.append(closure)

            return site_closures

        except ValueError as e:
            raise e
