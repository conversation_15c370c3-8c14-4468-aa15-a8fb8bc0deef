from datetime import datetime, date
import uuid
from typing import Optional, Sequence

from apella_cloud_api.exceptions import NotFound
import sqlalchemy
from sqlalchemy import CheckConstraint, DateTime, ForeignKey, select, String, Date, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship, selectinload

from api_server.services.room.room_store import RoomModel
from api_server.services.site.site_store import Site
from databases.sql import Base, new_async_session
from databases.sql.mixins import SiteIdMixin, TimestampMixin


class RoomClosure(Base, TimestampMixin):
    __tablename__ = "room_closures"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=sqlalchemy.text("gen_random_uuid()"),
        nullable=False,
    )

    room_id: Mapped[str] = mapped_column(
        ForeignKey("rooms.id", ondelete="CASCADE"), primary_key=True, index=True, nullable=False
    )
    room: Mapped["RoomModel"] = relationship(back_populates="closures")

    start_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, index=True
    )
    end_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, index=True)

    reason: Mapped[str] = mapped_column(String, nullable=True)

    __table_args__ = (
        CheckConstraint("start_time < end_time", name="ck_room_closures_start_time_lt_end_time"),
    )

    def __repr__(self) -> str:
        return f"<RoomClosure(id='{self.id}')>"


class SiteClosure(Base, TimestampMixin, SiteIdMixin):
    __tablename__ = "site_closures"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=sqlalchemy.text("gen_random_uuid()"),
        nullable=False,
    )

    site_id: Mapped[str] = mapped_column(
        ForeignKey("sites.id", ondelete="CASCADE"), primary_key=True, index=True, nullable=False
    )
    site: Mapped["Site"] = relationship(back_populates="closures")

    closure_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)

    reason: Mapped[str] = mapped_column(String, nullable=False)

    def __repr__(self) -> str:
        return f"<SiteClosure(id='{self.id}', closureDate='{self.closure_date}')>"


class ClosureStore:
    async def create_room_closure(self, room_closure: RoomClosure) -> RoomClosure:
        async with new_async_session() as session:
            record = (
                (
                    await session.execute(
                        select(RoomClosure)
                        .filter(RoomClosure.room_id == room_closure.room_id)
                        .filter(RoomClosure.start_time <= room_closure.end_time)
                        .filter(RoomClosure.end_time >= room_closure.start_time)
                    )
                )
                .scalars()
                .first()
            )
            if not record:
                session.add(room_closure)
                await session.commit()
                await session.refresh(room_closure)
            return room_closure

    async def delete_room_closure(self, room_closure_id: str) -> None:
        async with new_async_session() as session:
            record = (
                (
                    await session.execute(
                        select(RoomClosure).filter(RoomClosure.id == room_closure_id)
                    )
                )
                .scalars()
                .first()
            )

            if record:
                await session.delete(record)
                await session.commit()
            else:
                raise NotFound(f"No room closure found with id: {room_closure_id}")

    async def get_room_by_closure_id(self, room_closure_id: str) -> "RoomModel":
        async with new_async_session() as session:
            room_closure = (
                await session.scalars(
                    select(RoomClosure)
                    .options(selectinload(RoomClosure.room))
                    .filter(RoomClosure.id == room_closure_id)
                )
            ).one()

            return room_closure.room

    async def query_room_closures(
        self,
        room_id: Optional[str] = None,
        room_ids: Optional[Sequence[str]] = None,
        site_id: Optional[str] = None,
        min_end_time: Optional[datetime] = None,
        max_start_time: Optional[datetime] = None,
    ) -> Sequence[RoomClosure]:
        async with new_async_session() as session:
            statement = select(RoomClosure)

            if site_id is not None:
                statement = statement.join(RoomModel).filter(RoomModel.site_id == site_id)
            if room_id is not None:
                statement = statement.filter(RoomClosure.room_id == room_id)
            if room_ids is not None:
                statement = statement.filter(RoomClosure.room_id.in_(room_ids))
            if min_end_time is not None:
                statement = statement.filter(RoomClosure.end_time >= min_end_time)
            if max_start_time is not None:
                statement = statement.filter(RoomClosure.start_time <= max_start_time)

            return (await session.scalars(statement)).all()

    async def create_site_closure(self, site_closure: SiteClosure) -> SiteClosure:
        try:
            if site_closure.reason and len(site_closure.reason) < 5:
                raise ValueError("Reason must be at least 5 characters long.")
            async with new_async_session() as session:
                session.add(site_closure)
                await session.commit()
                await session.refresh(site_closure)
                return site_closure
        except ValueError as e:
            raise e

    async def delete_site_closure(self, site_closure_id: str) -> None:
        async with new_async_session() as session:
            closure = (
                (
                    await session.execute(
                        select(SiteClosure).filter(SiteClosure.id == site_closure_id)
                    )
                )
                .scalars()
                .first()
            )

            if closure:
                await session.delete(closure)
                await session.commit()
            else:
                raise NotFound(f"No site closure found with id: {site_closure_id}")

    async def query_site_closures(
        self,
        site_id: Optional[str] = None,
        closure_date: Optional[date] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
    ) -> Sequence[SiteClosure]:
        async with new_async_session() as session:
            statement = select(SiteClosure)

            if site_id is not None:
                statement = statement.filter(SiteClosure.site_id == site_id)
            if closure_date is not None:
                statement = statement.filter(func.date(SiteClosure.closure_date) == closure_date)
            if start_date is not None:
                statement = statement.filter(func.date(SiteClosure.closure_date) >= start_date)
            if end_date is not None:
                statement = statement.filter(func.date(SiteClosure.closure_date) <= end_date)

            return (await session.scalars(statement)).all()

    async def get_site_by_closure_id(self, site_closure_id: str) -> Site:
        async with new_async_session() as session:
            site_closure = (
                await session.scalars(
                    select(SiteClosure)
                    .options(selectinload(SiteClosure.site))
                    .filter(SiteClosure.id == site_closure_id)
                )
            ).one()

            return site_closure.site
