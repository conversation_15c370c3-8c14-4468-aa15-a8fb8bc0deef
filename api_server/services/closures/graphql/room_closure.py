from typing import Any, Optional

from graphql import GraphQL<PERSON>rror
from api_server.graphql.context import GrapheneInfo
from api_server.graphql.pagination.fields import pagination_connection_factory
import graphene
from apella_cloud_api.dtos import RoomClosureQueryDto
import api_server.services.room.graphql.room as room_schema

from api_server.services.room.room_store import RoomModel
from api_server.services.closures.closure_store import RoomClosure as RoomClosureModel


class RoomClosureQueryInput(graphene.InputObjectType):
    room_id = graphene.String()
    room_ids = graphene.List(graphene.String)
    site_id = graphene.String()
    min_end_time = graphene.DateTime()
    max_start_time = graphene.DateTime()

    def to_query_dto(self) -> RoomClosureQueryDto:
        return RoomClosureQueryDto(
            room_id=self.room_id,
            room_ids=self.room_ids,
            site_id=self.site_id,
            min_end_time=self.min_end_time,
            max_start_time=self.max_start_time,
        )


class RoomClosure(graphene.ObjectType):
    id = graphene.ID(required=True)

    room_id = room_id = graphene.String(required=True)
    room = graphene.Field(lambda: room_schema.Room, required=True)

    start_time = graphene.DateTime(required=True)
    end_time = graphene.DateTime(required=True)

    reason = graphene.String(required=False)

    @staticmethod
    async def resolve_room(
        room_closure: RoomClosureModel, info: GrapheneInfo, **kwargs: Any
    ) -> RoomModel:
        room: Optional[RoomModel] = await info.context.room_loader.load(room_closure.room_id)
        if room is None:
            raise GraphQLError(
                message=f"Unable to find room for room closure with room id: {room_closure.room_id}"
            )

        return room


RoomClosureConnection = pagination_connection_factory(RoomClosure)
