from __future__ import annotations

from typing import Any

import graphene

from graphql import GraphQLError

import api_server.services.closures.graphql.room_closure as room_closure_schema

import api_server.services.room.graphql.room as room_schema
from apella_cloud_api.exceptions import Client<PERSON>rror
from api_server.graphql.context import GrapheneInfo


class RoomClosureCreateInput(graphene.InputObjectType):
    room_id = graphene.ID(required=True)
    start_time = graphene.DateTime(required=True)
    end_time = graphene.DateTime(required=True)


class RoomClosureCreate(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(RoomClosureCreateInput, required=True)

    success = graphene.Boolean()
    room = graphene.Field(lambda: room_schema.Room, required=True)
    created_room_closure = graphene.Field(room_closure_schema.RoomClosure)

    @staticmethod
    async def mutate(
        _: Any, info: GrapheneInfo, input: RoomClosureCreateInput
    ) -> RoomClosureCreate:
        try:
            new_room_closure = await info.context.closure_service.create_room_closure(
                start_time=input.start_time, end_time=input.end_time, room_id=input.room_id
            )

            room = await info.context.room_service.get_room(new_room_closure.room_id)

            return RoomClosureCreate(success=True, created_room_closure=new_room_closure, room=room)
        except ClientError as e:
            raise GraphQLError(e.message)
