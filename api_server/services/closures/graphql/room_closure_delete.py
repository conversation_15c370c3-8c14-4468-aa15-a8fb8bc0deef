from __future__ import annotations

from typing import Any

import graphene

from graphql import GraphQLError


import api_server.services.room.graphql.room as room_schema
from apella_cloud_api.exceptions import ClientError
from api_server.graphql.context import GrapheneInfo


class RoomClosureDeleteInput(graphene.InputObjectType):
    room_closure_id = graphene.ID(required=True)


class RoomClosureDelete(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(RoomClosureDeleteInput, required=True)

    success = graphene.Boolean()
    room = graphene.Field(lambda: room_schema.Room, required=True)

    @staticmethod
    async def mutate(
        _: Any, info: GrapheneInfo, input: RoomClosureDeleteInput
    ) -> RoomClosureDelete:
        try:
            room = await info.context.closure_service.get_room_by_closure_id(input.room_closure_id)

            await info.context.closure_service.delete_room_closure(
                room_closure_id=input.room_closure_id
            )

            return RoomClosureDelete(success=True, room=room)
        except ClientError as e:
            raise GraphQLError(e.message)
