from typing import Any, Optional

from graphql import GraphQLError
from api_server.graphql.context import GrapheneInfo
from api_server.graphql.pagination.fields import pagination_connection_factory
import graphene
from apella_cloud_api.dtos import SiteClosureQueryDto
from api_server.services.closures.closure_store import SiteClosure as SiteClosureModel

import api_server.services.site.graphql.site as site_schema
from api_server.services.site.site_store import Site


class SiteClosureQueryInput(graphene.InputObjectType):
    site_id = graphene.String()
    closure_date = graphene.Date()

    def to_query_dto(self) -> SiteClosureQueryDto:
        return SiteClosureQueryDto(site_id=self.site_id, closure_date=self.closure_date)


class SiteClosure(graphene.ObjectType):
    id = graphene.ID(required=True)

    site_id = graphene.String(required=True)
    site = graphene.Field(lambda: site_schema.Site, required=True)
    closure_date = graphene.DateTime(required=True)
    reason = graphene.String(required=True)

    @staticmethod
    async def resolve_site(
        site_closure: SiteClosureModel, info: GrapheneInfo, **kwargs: Any
    ) -> Site:
        site: Optional[Site] = await info.context.site_loader.load(site_closure.site_id)
        if site is None:
            raise GraphQLError(
                message=f"Unable to find site for site closure with site id: {site_closure.site_id}"
            )

        return site


SiteClosureConnection = pagination_connection_factory(SiteClosure)
