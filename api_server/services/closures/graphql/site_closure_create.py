from __future__ import annotations

from typing import Any

import graphene

from graphql import GraphQLError

from apella_cloud_api.exceptions import Client<PERSON>rror
from api_server.graphql.context import GrapheneInfo
from api_server.services.closures.graphql.site_closure import SiteClosure
from api_server.services.site.graphql.site import Site


class SiteClosureCreateInput(graphene.InputObjectType):
    site_id = graphene.ID(required=True)
    closure_date = graphene.Date(required=True)
    reason = graphene.String(required=True)


class SiteClosureCreate(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(SiteClosureCreateInput, required=True)

    success = graphene.Boolean()
    site = graphene.Field(lambda: Site, required=True)
    created_site_closure = graphene.Field(SiteClosure)

    @staticmethod
    async def mutate(
        _: Any, info: GrapheneInfo, input: SiteClosureCreateInput
    ) -> SiteClosureCreate:
        try:
            new_site_closure = await info.context.closure_service.create_site_closure(
                site_id=input.site_id, closure_date=input.closure_date, reason=input.reason
            )

            site = await info.context.site_service.get_site(new_site_closure.site_id)

            return SiteClosureCreate(success=True, created_site_closure=new_site_closure, site=site)
        except ClientError as e:
            raise GraphQLError(e.message)
