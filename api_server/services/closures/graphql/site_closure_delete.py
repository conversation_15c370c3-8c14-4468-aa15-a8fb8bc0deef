from __future__ import annotations

from typing import Any

import graphene

from graphql import GraphQLError


from apella_cloud_api.exceptions import Client<PERSON>rror
from api_server.graphql.context import GrapheneInfo
from api_server.services.site.graphql.site import Site


class SiteClosureDeleteInput(graphene.InputObjectType):
    site_closure_id = graphene.ID(required=True)


class SiteClosureDelete(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(SiteClosureDeleteInput, required=True)

    success = graphene.Boolean()
    site = graphene.Field(lambda: Site, required=True)

    @staticmethod
    async def mutate(
        _: Any, info: GrapheneInfo, input: SiteClosureDeleteInput
    ) -> SiteClosureDelete:
        try:
            site = await info.context.closure_service.get_site_by_closure_id(input.site_closure_id)

            await info.context.closure_service.delete_site_closure(
                site_closure_id=input.site_closure_id
            )

            return SiteClosureDelete(success=True, site=site)
        except Client<PERSON>rror as e:
            raise GraphQLError(e.message)
