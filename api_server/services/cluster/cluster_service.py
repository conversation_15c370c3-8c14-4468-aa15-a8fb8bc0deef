from dataclasses import dataclass
from typing import List, Optional, Sequence

from api_server.logging.audit import log_calls_to_audit
from api_server.services.cluster.cluster_models import (
    ClusterMappingDbModel,
    ClusterDbModel,
    ClusterMappingModel,
    ClusterModel,
)
from api_server.services.cluster.cluster_store import ClusterStore
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import READ_ANY_CLUSTER, WRITE_ANY_CLUSTER


@dataclass
class ClusterService:
    auth: Auth
    cluster_store: ClusterStore

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CLUSTER)
    async def get_cluster(self, id: str) -> ClusterDbModel:
        return await self.cluster_store.get_cluster(id)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CLUSTER)
    async def get_clusters(self, ids: Optional[List[str]]) -> Sequence[ClusterDbModel]:
        return await self.cluster_store.query_clusters(ids)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CLUSTER)
    async def get_cluster_mappings(self, ids: List[str]) -> Sequence[ClusterMappingDbModel]:
        return await self.cluster_store.query_cluster_mappings(ids)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_CLUSTER)
    async def insert_cluster(self, cluster: ClusterModel) -> ClusterDbModel:
        return await self.cluster_store.insert_cluster(cluster)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_CLUSTER)
    async def insert_cluster_mappings(
        self, cluster_mapping: ClusterMappingModel
    ) -> Sequence[ClusterMappingDbModel]:
        return await self.cluster_store.upsert_cluster_mappings([cluster_mapping])

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_CLUSTER)
    async def update_cluster(self, cluster: ClusterModel) -> Optional[ClusterDbModel]:
        return await self.cluster_store.update_cluster(cluster)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_CLUSTER)
    async def update_cluster_mapping(
        self, cluster_mapping: ClusterMappingModel
    ) -> Sequence[ClusterMappingDbModel]:
        current_cluster_mappings = await self.get_cluster_mappings(
            [str(cluster_mapping.cluster_id)]
        )
        cluster_mappings_delete = [
            cluster_mapping_delete.site_id
            for cluster_mapping_delete in current_cluster_mappings
            if cluster_mapping_delete.site_id not in cluster_mapping.sites
        ]
        if len(cluster_mappings_delete) > 0:
            await self.cluster_store.delete_cluster_mappings(
                cluster_mapping=ClusterMappingModel(
                    cluster_mapping.cluster_id, cluster_mappings_delete
                )
            )
        return await self.cluster_store.upsert_cluster_mappings([cluster_mapping])
