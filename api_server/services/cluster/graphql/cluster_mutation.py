from typing import Optional
from uuid import uuid4

import graphene
from graphene import String

from api_server.graphql.context import GrapheneInfo
from api_server.services.cluster.graphql.cluster_gql_models import Cluster
from api_server.services.cluster.cluster_models import (
    ClusterModel,
    ClusterMappingModel,
    ClusterDbModel,
)


class ClusterCreateInput(graphene.InputObjectType):
    id = graphene.ID(required=False)
    name = graphene.String(required=True)
    enable_audio = graphene.Boolean(required=False, default_value=False)
    sites = graphene.List(graphene.NonNull(String), required=True, default_value=[])


class ClusterUpdateInput(graphene.InputObjectType):
    id = graphene.ID(required=True)
    name = graphene.String(required=True)
    enable_audio = graphene.Boolean(required=False, default_value=False)
    sites = graphene.List(graphene.NonNull(String), required=True, default_value=[])


class ClusterCreate(graphene.Mutation):
    class Arguments:
        input = ClusterCreateInput(required=True)

    success = graphene.Boolean()
    cluster = graphene.Field(Cluster)

    @staticmethod
    async def mutate(
        parent: None, info: GrapheneInfo, input: ClusterCreateInput
    ) -> "ClusterCreate":
        cluster_id = input.id or uuid4()
        cluster = await info.context.cluster_service.insert_cluster(
            cluster=ClusterModel(id=cluster_id, name=input.name, enable_audio=input.enable_audio)
        )
        await info.context.cluster_service.insert_cluster_mappings(
            cluster_mapping=ClusterMappingModel(cluster_id=cluster.id, sites=input.sites)
        )

        return ClusterCreate(
            success=True,
            cluster=Cluster(id=cluster_id, name=cluster.name, enable_audio=cluster.enable_audio),
        )


class ClusterUpdate(graphene.Mutation):
    class Arguments:
        input = ClusterUpdateInput(required=True)

    success = graphene.Boolean()
    cluster = graphene.Field(Cluster)

    @staticmethod
    async def mutate(
        parent: None, info: GrapheneInfo, input: ClusterUpdateInput
    ) -> "ClusterUpdate":
        cluster: Optional[ClusterDbModel] = await info.context.cluster_service.update_cluster(
            cluster=ClusterModel(id=input.id, name=input.name, enable_audio=input.enable_audio)
        )
        await info.context.cluster_service.update_cluster_mapping(
            cluster_mapping=ClusterMappingModel(cluster_id=input.id, sites=input.sites)
        )

        if cluster is None:
            cluster_id = input.id
            cluster_name = input.name
            enable_audio = input.enable_audio
        else:
            cluster_id = cluster.id
            cluster_name = cluster.name
            enable_audio = cluster.enable_audio

        return ClusterUpdate(
            success=True,
            cluster=Cluster(id=cluster_id, name=cluster_name, enable_audio=enable_audio),
        )
