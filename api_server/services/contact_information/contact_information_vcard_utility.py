import logging
import os
import traceback
from typing import Final, Optional, <PERSON><PERSON>
from urllib import parse

import requests_cache
from requests import HTTP<PERSON>rror
from vobject import vCard, vcard

from api_server.logging import log

VCARD_CACHE_NAME: Final[str] = "vcard-cache"
VCARD_EMAIL: Final[str] = "<EMAIL>"
VCARD_ORG: Final[str] = "Apella Technology Inc."
VCARD_NAME: Final[str] = "Apella"
VCARD_PHOTO_URI: Final[str] = "https://assets.apella.io/logos/mark/dark-on-white.png"
VCARD_VERSION: Final[str] = "3.0"
VCARD_WEBSITE: Final[str] = "https://apella.io"

vcard_cache_session = requests_cache.CachedSession(
    cache_name=VCARD_CACHE_NAME, backend="filesystem"
)


def get_vcard(
    *,
    telephone: Optional[str],
    name: Optional[str] = VCARD_NAME,
    email: Optional[str] = VCARD_EMAIL,
    org: Optional[str] = VCARD_ORG,
    website: Optional[str] = VCARD_WEBSITE,
    photo_uri: Optional[str] = VCARD_PHOTO_URI,
) -> str:
    """Get .vcf file contents for Apella"""
    generated = vCard()
    generated.add("version")
    generated.version.value = VCARD_VERSION

    if name:
        gen_name = generated.add("n")
        gen_name.value = vcard.Name(given=name)
        gen_name.charset_param = "UTF-8"
        gen_fullname = generated.add("fn")
        gen_fullname.value = name
        gen_fullname.charset_param = "UTF-8"

    if email:
        gen_email = generated.add("email")
        gen_email.value = email
        gen_email.type_param = "INTERNET"
        gen_email.charset_param = "UTF-8"

    if org:
        gen_org = generated.add("org")
        gen_org.value = [org]
        gen_org.charset_param = "UTF-8"

    if telephone:
        gen_tel = generated.add("tel")
        gen_tel.value = telephone
        gen_tel.type_param = "WORK"

    if website:
        gen_url = generated.add("url")
        gen_url.value = website
        gen_url.charset_param = "UTF-8"

    if photo_uri:
        photo_data: Optional[Tuple[str, bytes]] = get_vcard_photo_data(photo_uri)
        if photo_data:
            photo_type, photo_bytes = photo_data
            gen_photo = generated.add("photo")
            gen_photo.value = photo_bytes
            gen_photo.encoding_param = "b"
            gen_photo.type_param = photo_type

    return generated.serialize()


def get_vcard_photo_data(uri: str) -> Optional[Tuple[str, bytes]]:
    """Get photo data from URI"""

    parsed_uri = parse.urlparse(uri)
    photo_type = os.path.splitext(parsed_uri.path)[1]

    if photo_type.lower() in [".jpg", ".jpeg", ".png", ".gif"]:
        photo_type = photo_type.upper()[1:]
    else:
        return None

    try:
        if parsed_uri.scheme in ["http", "https"]:
            with vcard_cache_session.get(uri) as response:
                response.raise_for_status()
                return photo_type, response.content

    except HTTPError as e:
        log(logging.WARN, f"Failed to get photo from {uri}: {e}")
        log(logging.WARN, traceback.format_exc())
        return None

    try:
        with open(parsed_uri.path, "rb") as photo_file:
            return photo_type, photo_file.read()

    except FileNotFoundError:
        log(logging.WARN, f"Photo file not found: {parsed_uri.path}")
        log(logging.WARN, traceback.format_exc())
        return None
