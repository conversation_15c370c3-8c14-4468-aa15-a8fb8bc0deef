# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs
from typing import Any, Optional, Sequence

import dacite
import graphene
from graphql import GraphQLError

import api_server.services.contact_information.contact_information_store as contact_information_store
from api_server.graphql.context import GrapheneInfo
from api_server.graphql.pagination.fields import pagination_connection_factory
from api_server.services.case.case_store import Case
from api_server.services.contact_information.graphql.staff_event_notification_contact_information_loader import (
    FrozenEventNotificationQuery,
)
from api_server.services.contact_information.staff_event_notification_contact_information_store import (
    ContactInformationSubscriptionQuery,
    StaffEventNotificationContactInformationModel,
    StaffEventNotificationModel,
)
from api_server.services.events.event_store import EventModel, EventTypeModel
from api_server.services.events.graphql.event import Event, EventType
from api_server.services.observations.graphql.observation import ObservationType, Observation
from api_server.services.observations.observation_store import (
    ObservationTypeModel,
    ObservationModel,
)
from api_server.services.staff.staff_store import StaffModel


def get_staff():
    from api_server.services.staff.graphql.staff import Staff

    return Staff


# This is a workaround for a circular import
def import_scheduled_case():
    from api_server.services.case.graphql.scheduled_case import ScheduledCase

    return ScheduledCase


class EventNotification(graphene.ObjectType):
    id = graphene.ID(required=True)
    event = graphene.Field(lambda: Event, required=False)
    observation = graphene.Field(lambda: Observation, required=False)
    message_id = graphene.String(required=True)
    case = graphene.Field(import_scheduled_case, required=False)
    event_time = graphene.DateTime(required=True)
    created_time = graphene.DateTime(required=True)
    sent_time = graphene.DateTime(required=False)
    staff_event_contact_information = graphene.Field(
        lambda: StaffEventNotificationContactInformation, required=True
    )

    @staticmethod
    async def resolve_event(
        staff_event_notification: StaffEventNotificationModel,
        info: GrapheneInfo,
    ) -> Optional[EventModel]:
        event: Optional[EventModel] = await info.context.event_loader.load(
            staff_event_notification.event_id
        )
        return event

    @staticmethod
    async def resolve_observation(
        staff_event_notification: StaffEventNotificationModel, info: GrapheneInfo
    ) -> Optional[ObservationModel]:
        observation_id = staff_event_notification.event_id
        observations = await info.context.observation_loader.load(
            (staff_event_notification.case_id, None, observation_id)
        )
        if len(observations) == 1:
            return observations[0]
        return None

    @staticmethod
    async def resolve_case(
        staff_event_notification: StaffEventNotificationModel,
        info: GrapheneInfo,
    ) -> Optional[Case]:
        if staff_event_notification.case_id is None:
            return None
        return await info.context.case_loader.load(staff_event_notification.case_id)

    @staticmethod
    async def resolve_staff_event_contact_information(
        staff_event_notification: StaffEventNotificationModel,
        info: GrapheneInfo,
    ) -> StaffEventNotificationContactInformationModel:
        subscription = await info.context.staff_event_notification_subscription_loader.load(
            staff_event_notification.staff_event_contact_information_id
        )
        if subscription is None:
            raise GraphQLError(
                message=f"Unable to find staff event notification subscription with id:"
                f" {staff_event_notification.staff_event_contact_information_id}"
            )
        return subscription


class EventNotficationQueryInput(graphene.InputObjectType):
    staff_ids = graphene.List(graphene.NonNull(graphene.String), required=False)
    event_type_ids = graphene.List(graphene.NonNull(graphene.String), required=False)
    case_ids = graphene.List(graphene.NonNull(graphene.String), required=False)
    room_ids = graphene.List(graphene.NonNull(graphene.String), required=False)
    event_ids = graphene.List(graphene.NonNull(graphene.String), required=False)


class StaffEventNotificationContactInformation(graphene.ObjectType):
    id = graphene.ID(required=True)
    staff_id = graphene.ID(required=True)
    staff = graphene.Field(get_staff, required=True)
    event_type_id = graphene.String(required=True)
    event_type = graphene.Field(lambda: EventType, required=True)
    observation_type = graphene.Field(lambda: ObservationType, required=False)
    contact_information = graphene.Field(lambda: ContactInformation, required=True)
    contact_information_value = graphene.String(required=True)
    contact_information_type = contact_information_store.ContactInformationTypeGraphene(
        required=True
    )
    contact_information_initialized = graphene.Boolean(required=True)
    event_notifications = graphene.List(
        graphene.NonNull(EventNotification),
        required=True,
        event_ids=graphene.List(graphene.NonNull(graphene.String), required=False),
    )
    staff_event_notifications = graphene.List(
        graphene.NonNull(EventNotification),
        required=True,
        query=graphene.Argument(EventNotficationQueryInput, required=False),
    )

    @staticmethod
    async def resolve_event_notifications(
        staff_event_notification_contact_information: StaffEventNotificationContactInformationModel,
        info: GrapheneInfo,
        event_ids: Optional[list[str]] = None,
    ) -> list[StaffEventNotificationModel]:
        staff_notification_events: list[
            StaffEventNotificationModel
        ] = await info.context.event_notification_loader.load(
            str(staff_event_notification_contact_information.id)
        )

        return [
            notification
            for notification in staff_notification_events
            # I tried to do this in a separate if, but mypy complained
            if event_ids is None or notification.event_id in event_ids
        ]

    @staticmethod
    async def resolve_staff_event_notifications(
        staff_event_notification_contact_information: StaffEventNotificationContactInformationModel,
        info: GrapheneInfo,
        query: Optional[dict[str, Any]] = None,
    ):
        staff_event_notification_dict: dict[str, Any] = {
            "staff_event_notification_contact_information_id": str(
                staff_event_notification_contact_information.id
            ),
        }
        if query is not None:
            staff_event_notification_dict = {
                **staff_event_notification_dict,
                "staff_ids": tuple(query["staff_ids"]) if "staff_ids" in query else None,
                "event_type_ids": tuple(query["event_type_ids"])
                if "event_type_ids" in query
                else None,
                "case_ids": tuple(query["case_ids"]) if "case_ids" in query else None,
                "room_ids": tuple(query["room_ids"]) if "room_ids" in query else None,
                "event_ids": tuple(query["event_ids"]) if "event_ids" in query else None,
            }
        key = dacite.from_dict(
            data_class=FrozenEventNotificationQuery, data=staff_event_notification_dict
        )
        return await info.context.staff_event_notification_loader.load(key)

    @staticmethod
    async def resolve_contact_information_value(
        staff_event_notification_contact_information: StaffEventNotificationContactInformationModel,
        info: GrapheneInfo,
    ) -> str:
        contact_information: Optional[
            contact_information_store.ContactInformationModel
        ] = await info.context.contact_information_loader.load(
            str(staff_event_notification_contact_information.contact_information_id)
        )
        if contact_information is None:
            raise GraphQLError(
                message=f"Unable to find contact information for staff with contact id: {staff_event_notification_contact_information.contact_information_id}"
            )
        return contact_information.contact_information_value

    @staticmethod
    async def resolve_contact_information_type(
        staff_event_notification_contact_information: StaffEventNotificationContactInformationModel,
        info: GrapheneInfo,
    ) -> contact_information_store.ContactInformationType:
        contact_information: Optional[
            contact_information_store.ContactInformationModel
        ] = await info.context.contact_information_loader.load(
            str(staff_event_notification_contact_information.contact_information_id)
        )
        if contact_information is None:
            raise GraphQLError(
                message=f"Unable to find contact information for staff with contact id: {staff_event_notification_contact_information.contact_information_id}"
            )
        return contact_information.type

    @staticmethod
    async def resolve_contact_information_initialized(
        staff_event_notification_contact_information: StaffEventNotificationContactInformationModel,
        info: GrapheneInfo,
    ) -> bool:
        contact_information: Optional[
            contact_information_store.ContactInformationModel
        ] = await info.context.contact_information_loader.load(
            str(staff_event_notification_contact_information.contact_information_id)
        )
        if contact_information is None:
            raise GraphQLError(
                message=f"Unable to find contact information for staff with contact id: "
                f"{staff_event_notification_contact_information.contact_information_id}"
            )
        return bool(contact_information.initial_notification_sent)

    @staticmethod
    async def resolve_contact_information(
        staff_event_notification_contact_information: StaffEventNotificationContactInformationModel,
        info: GrapheneInfo,
    ) -> contact_information_store.ContactInformationModel:
        contact_information: Optional[
            contact_information_store.ContactInformationModel
        ] = await info.context.contact_information_loader.load(
            str(staff_event_notification_contact_information.contact_information_id)
        )
        if contact_information is None:
            raise GraphQLError(
                message=f"Unable to find contact information for staff with contact id:"
                f" {staff_event_notification_contact_information.contact_information_id}"
            )
        return contact_information

    @staticmethod
    async def resolve_staff(
        staff_event_notification_contact_information: StaffEventNotificationContactInformationModel,
        info: GrapheneInfo,
    ) -> StaffModel:
        staff: Optional[StaffModel] = await info.context.staff_loader.load(
            str(staff_event_notification_contact_information.staff_id)
        )
        if staff is None:
            raise GraphQLError(
                message=f"Unable to find staff with staff id: {staff_event_notification_contact_information.staff_id}"
            )
        return staff

    @staticmethod
    async def resolve_event_type(
        staff_event_notification_contact_information: StaffEventNotificationContactInformationModel,
        info: GrapheneInfo,
        **kwargs,
    ) -> EventTypeModel:
        event_type: Optional[EventTypeModel] = await info.context.event_attrs_loader.load(
            staff_event_notification_contact_information.event_type_id
        )
        if event_type is not None:
            return event_type

        return EventTypeModel(id="", name="", type="")

    @staticmethod
    async def resolve_observation_type(
        staff_event_notification_contact_information: StaffEventNotificationContactInformationModel,
        info: GrapheneInfo,
        **kwargs,
    ) -> Optional[ObservationTypeModel]:
        obx_event_type: Optional[
            ObservationTypeModel
        ] = await info.context.observation_type_loader.load(
            staff_event_notification_contact_information.event_type_id
        )

        return obx_event_type


class ContactInformationSearchInput(graphene.InputObjectType):
    case_ids = graphene.List(graphene.NonNull(graphene.ID), required=False)
    staff_ids = graphene.List(graphene.NonNull(graphene.ID), required=False)
    ids = graphene.List(graphene.NonNull(graphene.ID), required=False)
    contact_information_values = graphene.List(graphene.NonNull(graphene.String), required=False)
    types = graphene.List(graphene.NonNull(graphene.String), required=False)
    event_type_ids = graphene.List(graphene.NonNull(graphene.String), required=False)


class StaffEventNotificationContactInformationSearchInput(graphene.InputObjectType):
    staff_ids = graphene.List(graphene.NonNull(graphene.ID), required=False)
    event_type_ids = graphene.List(graphene.NonNull(graphene.String), required=False)
    org_ids = graphene.List(graphene.NonNull(graphene.ID), required=False)


class ContactInformation(graphene.ObjectType):
    id = graphene.ID(required=True)
    contact_information_value = graphene.String()
    type = contact_information_store.ContactInformationTypeGraphene(required=False)
    initial_notification_sent = graphene.DateTime(required=False)
    first_name = graphene.String(required=False)
    last_name = graphene.String(required=False)
    staff_event_contact_information = graphene.List(
        graphene.NonNull(StaffEventNotificationContactInformation)
    )
    is_apella_employee = graphene.Boolean(required=False)

    @staticmethod
    async def resolve_staff_event_contact_information(
        contact_information: contact_information_store.ContactInformationModel,
        info: GrapheneInfo,
        **kwargs,
    ) -> Sequence[StaffEventNotificationContactInformationModel]:
        loader_key = ContactInformationSubscriptionQuery(
            id=str(contact_information.id),
        )
        return await info.context.contact_information_subscription_loader.load(loader_key)


ContactInformationConnection = pagination_connection_factory(ContactInformation)
StaffEventNotificationContactInformationConnection = pagination_connection_factory(
    StaffEventNotificationContactInformation
)
EventNotificationConnection = pagination_connection_factory(EventNotification)
