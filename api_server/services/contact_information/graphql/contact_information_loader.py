from typing import Optional

from aiodataloader import DataLoader

from api_server.services.contact_information.contact_information_service import (
    ContactInformationService,
)
from api_server.services.contact_information.contact_information_store import (
    ContactInformationModel,
)
from api_server.services.utils.loader.util_functions import sort_loader_results


class ContactInformationLoader(DataLoader[str, Optional[ContactInformationModel]]):
    contact_information_service: ContactInformationService

    def __init__(self, contact_information_service: ContactInformationService):
        super().__init__()
        self.contact_information_service = contact_information_service

    async def batch_load_fn(self, keys: list[str]) -> list[Optional[ContactInformationModel]]:
        # Create a set of the keys to remove duplicates
        op_keys = list(set(keys))
        # Fetch the contact information by the set of keys
        contact_information = await self.contact_information_service.get_contact_information_by_ids(
            op_keys
        )
        return sort_loader_results(keys, contact_information)
