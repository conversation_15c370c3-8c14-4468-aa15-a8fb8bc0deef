import datetime
import logging
import traceback
from datetime import timedelta
from typing import Any
from zoneinfo import ZoneInfo
from api_server.services.contact_information.graphql.staff_events_notification_data_module import (
    StaffEventNotificationBlock,
)
from api_server.services.contact_information.graphql.staff_events_processor_using_case_activity import (
    StaffEventsNotificationProcessorUsingCaseActivity,
)

import graphene
from api_server.graphql.context import GrapheneInfo
from api_server.logging import log
from api_server.services.contact_information.contact_information_store import (
    ContactInformationType,
)
from api_server.services.contact_information.graphql.staff_events_processor import (
    StaffEventsNotificationProcessor,
    StaffEventsNotificationsInput,
)
from api_server.services.contact_information.staff_event_notification_contact_information_store import (
    StaffEventNotificationModel,
    SentNotificationStatus,
)


class NotifyStaffForEvents(graphene.Mutation):
    class Arguments:
        input = graphene.NonNull(StaffEventsNotificationsInput)

    success = graphene.Boolean(required=True)
    sent_count = graphene.Int(required=True)
    failed_event_ids = graphene.List(graphene.NonNull(graphene.String), required=True)

    @staticmethod
    def _get_delay_tag(
        time_to_check: datetime.datetime, event_start_time: datetime.datetime
    ) -> str:
        timediff = time_to_check - event_start_time
        delay_tag = ""
        if timediff > timedelta(minutes=15):
            delay_tag = f"\nNotification delayed by {int(timediff.total_seconds() // 60)} minutes."
        return delay_tag

    @staticmethod
    def _log_notifications(
        level: int, msg: str, notifications: set[StaffEventNotificationBlock]
    ) -> None:
        if len(notifications) == 0:
            return

        kwargs: dict[str, object] = {}
        for notification in notifications:
            kwargs[str(notification.notification_id)] = notification.to_dict()
        log(
            level,
            msg,
            None,
            **kwargs,
        )

    @staticmethod
    async def mutate(
        parent: Any,
        info: GrapheneInfo,
        input: StaffEventsNotificationsInput,
    ) -> "NotifyStaffForEvents":
        """
        Check if there are any events that have notifications that need to be sent out
        If there are, send them out
        Use the confidence threshold and time threshold to determine which events to send notifications for
        """
        now = datetime.datetime.now(tz=datetime.timezone.utc)
        site = await info.context.site_service.get_site(input.site_id)

        # Retrieve the feature flag variation list from LaunchDarkly
        streaming_sites: list[str] = (
            info.context.launch_darkly_service.get_feature_flag(
                "use-case-activity-streaming-table-sites", default=[]
            )
            or []
        )  # Expected to return a list like ["lab_1", "palo_alto_1"]

        # Determine if we should use the Case Activity processor
        # (used for streaming sites or test sites that start with '_')
        is_use_case_activity_processor = (
            input.site_id in streaming_sites or input.site_id.startswith("_")
        )

        if is_use_case_activity_processor:
            processor_phase_events = StaffEventsNotificationProcessor(input, info, now, site)
            notifications_from_phase_events = await processor_phase_events.process()

            NotifyStaffForEvents._log_notifications(
                logging.INFO, "notifications_from_phase_event", notifications_from_phase_events
            )

            processor_case_activity = StaffEventsNotificationProcessorUsingCaseActivity(
                input, info, now, site
            )
            notifications_from_case_activity = await processor_case_activity.process()

            NotifyStaffForEvents._log_notifications(
                logging.INFO,
                "notifications_from_case_activity",
                notifications_from_case_activity,
            )
            # Combine both lists
            notifications_to_send = (
                notifications_from_phase_events | notifications_from_case_activity
            )

        else:
            # Use default PhaseEvents processor
            processor = StaffEventsNotificationProcessor(input, info, now, site)
            notifications_to_send = await processor.process()
            NotifyStaffForEvents._log_notifications(
                logging.INFO, "notifications_from_phase_event", notifications_to_send
            )

        # Add an early termination condition if there are no notifications to send
        if not notifications_to_send:
            return NotifyStaffForEvents(
                success=True,
                sent_count=0,
                failed_event_ids=[],
            )

        # Get the site timezone, so we format the time correctly in the notification
        timezone = ZoneInfo(site.timezone)

        # Since debouncing relies on event type, we need to get the event types
        # Also we send the human-readable event type name to the client
        event_type_id_to_name = {
            event_type.id: event_type.name
            for event_type in await info.context.event_service.get_event_types([], [])
        }
        obx_type_id_to_name = {
            event_type.id: event_type.name
            for event_type in await info.context.observation_service.query_observation_types([])
        }
        # Check for key collisions
        collision_ids = set(event_type_id_to_name.keys()) & set(obx_type_id_to_name.keys())
        if collision_ids:
            logging.warning(
                f"Duplicate event type IDs found between event and OBX types: {collision_ids}"
            )
        # Merge the two dictionaries, OBX types will overwrite event types if they have the same ID
        event_type_id_to_name.update(obx_type_id_to_name)
        notification_blocks_sent: dict[tuple[str, str, ContactInformationType], str] = {}
        notifications_to_store: list[StaffEventNotificationModel] = []
        failed_event_ids: list[str] = []
        success = True
        logging_level = logging.WARN
        notifications_sent = 0

        for notification_block in notifications_to_send:
            # While the notifications block is unique, the notification itself may not be
            # So we need to track the unique notifications sent and only send the unique ones
            notification_key = (
                notification_block.event_id,
                notification_block.contact_information_value,
                notification_block.contact_information_type,
            )

            if notification_key not in notification_blocks_sent:
                if (
                    notification_block.contact_information_type
                    == ContactInformationType.PHONE_NUMBER
                ):
                    event_time_formatted = notification_block.event_start_time.astimezone(
                        timezone
                    ).strftime("%H:%M")

                    # Check if the event is an EHR event, two event types are considered EHR events for V0 release
                    is_ehr_event = notification_block.event_type_id in {
                        "OBSERVED_IN_PRE_PROCEDURE",
                        "OBSERVED_PRE_PROCEDURE_COMPLETE",
                    }

                    # If the notification is delayed, we want to add a tag to the message
                    delay_tag = NotifyStaffForEvents._get_delay_tag(
                        now, notification_block.event_start_time
                    )
                    # Base message prefix
                    text_message_body = f"{notification_block.room_symbol}{notification_block.site_name} {notification_block.room_name}: "
                    if is_use_case_activity_processor:
                        if is_ehr_event:
                            case = await info.context.case_service.get_case(
                                case_id=notification_block.case_id
                            )
                            scheduled_start_time = case.scheduled_start_time

                            if scheduled_start_time is None:
                                logging.warning(
                                    f"Case {notification_block.case_id} does not have a scheduled start time."
                                )
                                # No scheduled start time — fallback format
                                text_message_body += (
                                    f"Pt {event_type_id_to_name.get(notification_block.event_type_id)}: "
                                    f"{event_time_formatted} (EHR)"
                                    f"{delay_tag}"
                                )
                            else:
                                scheduled_start_time_formatted = scheduled_start_time.astimezone(
                                    timezone
                                ).strftime("%H:%M")
                                text_message_body += (
                                    f"Pt for {scheduled_start_time_formatted} Case "
                                    f"{event_type_id_to_name.get(notification_block.event_type_id)}: "
                                    f"{event_time_formatted} (EHR)"
                                    f"{delay_tag}"
                                )
                        else:
                            # Apella event
                            text_message_body += (
                                f"{event_type_id_to_name.get(notification_block.event_type_id)}: {event_time_formatted} (Apella)"
                                f"{delay_tag}"
                            )
                    else:
                        # legacy Apella event
                        text_message_body += (
                            f"{event_type_id_to_name.get(notification_block.event_type_id)}: {event_time_formatted}"
                            f"{delay_tag}"
                        )

                    try:
                        message = info.context.contact_information_service.send_text_message(
                            notification_block.contact_information_value,
                            text_message_body,
                        )
                        notification_blocks_sent[notification_key] = message.sid

                        # We need to store the notification in the database to prevent duplicates
                        # And to debounce
                        notifications_to_store.append(
                            StaffEventNotificationModel(
                                id=notification_block.notification_id,
                                message_id=message.sid,
                                staff_event_contact_information_id=notification_block.staff_event_contact_information_id,
                                event_id=notification_block.event_id,
                                case_id=notification_block.case_id,
                                event_time=notification_block.event_start_time,
                                sent_status=SentNotificationStatus.SENT.name,
                                sent_time=now,
                                attempts=notification_block.attempts + 1,
                            )
                        )
                        notifications_sent += 1

                    except Exception as e:
                        success = False
                        failed_event_ids.append(notification_block.event_id)
                        log(logging_level, str(e), exc_info=True)
                        log(logging_level, traceback.format_exc())
                        log(
                            logging_level,
                            f"Failed to send notification for event {notification_block.event_id}",
                        )
                        notifications_to_store.append(
                            StaffEventNotificationModel(
                                id=notification_block.notification_id,
                                message_id="failed",
                                staff_event_contact_information_id=notification_block.staff_event_contact_information_id,
                                event_id=notification_block.event_id,
                                case_id=notification_block.case_id,
                                event_time=notification_block.event_start_time,
                                sent_status=SentNotificationStatus.FAILED.name,
                                attempts=notification_block.attempts + 1,
                            )
                        )

            else:
                # We need to store the notification in the database to prevent duplicates
                # And to debounce
                notifications_to_store.append(
                    StaffEventNotificationModel(
                        id=notification_block.notification_id,
                        message_id=notification_blocks_sent[notification_key],
                        staff_event_contact_information_id=notification_block.staff_event_contact_information_id,
                        event_id=notification_block.event_id,
                        case_id=notification_block.case_id,
                        event_time=notification_block.event_start_time,
                        sent_status=SentNotificationStatus.SENT.name,
                        attempts=notification_block.attempts,
                    )
                )

        await info.context.contact_information_service.upsert_staff_event_notifications(
            notifications_to_store
        )

        return NotifyStaffForEvents(
            success=success,
            sent_count=notifications_sent,
            failed_event_ids=failed_event_ids,
        )
