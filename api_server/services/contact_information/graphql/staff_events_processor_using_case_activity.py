from __future__ import annotations

import datetime
import re
import uuid
from collections import defaultdict
from datetime import timed<PERSON><PERSON>
from typing import Sequence
from zoneinfo import ZoneInfo

from api_server.services.case_activity.case_activity_store import CaseActivityModel

from apella_cloud_api.dtos import ContactInformationQueryDto
from api_server.graphql.context import GrapheneInfo
from api_server.services.case.case_staff_store import CaseStaffModel, CaseStaffQuery
from api_server.services.contact_information.contact_information_store import (
    ContactInformationModel,
    StaffEventNotificationQueryDto,
)
from api_server.services.contact_information.staff_event_notification_contact_information_store import (
    StaffEventNotificationContactInformationModel,
    StaffEventNotificationModel,
    EventNotificationQueryDto,
    SentNotificationStatus,
)
from api_server.services.plan.case_staff_plan_store import CaseStaffPlanModel, CaseStaffPlanQueryDTO
from api_server.services.site.site_store import Site
from api_server.services.staff.staff_store import Staff<PERSON>ode<PERSON>
from api_server.services.contact_information.graphql.staff_events_notification_data_module import (
    ExcessNotificationData,
    DuplicateNotificationData,
    StaffEventsNotificationsInput,
    StaffEventNotificationBlock,
    DEFAULT_NOTIFICATION_TIME_WINDOW,
    DEFAULT_NOTIFICATION_TIME_CONFIDENCE_THRESHOLD,
    ROOM_SYMBOL_MAP,
)


class StaffEventsNotificationProcessorUsingCaseActivity:
    _event_id_to_event: dict[str, CaseActivityModel]
    _case_ids_to_check: set[str]
    _staff_to_check: list[CaseStaffModel | CaseStaffPlanModel]
    _staff_ids_to_check: set[str]
    _case_id_to_staff: dict[str, list[CaseStaffModel | CaseStaffPlanModel]]

    _staff_contact_informations: list[StaffEventNotificationContactInformationModel]
    _contact_informations: list[ContactInformationModel]

    _staff_contact_information_id_to_staff_contact_information: dict[
        uuid.UUID, StaffEventNotificationContactInformationModel
    ]
    _staff_id_event_type_id_to_staff_contact_information: dict[
        tuple[uuid.UUID, str], list[StaffEventNotificationContactInformationModel]
    ]
    _staff_event_notification_id_to_contact_information: dict[uuid.UUID, ContactInformationModel]

    _already_sent_notifications: Sequence[StaffEventNotificationModel]
    _case_id_event_types_already_notified: dict[tuple[str, str], list[StaffEventNotificationModel]]

    _notifications_to_send: set[StaffEventNotificationBlock]

    _info: GrapheneInfo

    _confidence_threshold: float
    _time_threshold: timedelta
    _now: datetime.datetime

    _time_to_check: datetime.datetime
    _time_window_to_search: timedelta

    _notifications_should_not_have_been_sent: list[ExcessNotificationData]
    _room_id_to_room_name: dict[str, str]

    _site_model: Site

    def __init__(
        self,
        input: StaffEventsNotificationsInput,
        info: GrapheneInfo,
        now: datetime.datetime,
        site: Site,
    ):
        self._confidence_threshold = input.confidence_threshold
        self._now = now

        self._site_id = input.site_id
        self._site_model = site

        self._time_to_check = input.time_to_check or now

        self._time_window_to_search = (
            input.time_window_to_search or DEFAULT_NOTIFICATION_TIME_WINDOW
        )

        self._time_threshold = (
            input.time_threshold or DEFAULT_NOTIFICATION_TIME_CONFIDENCE_THRESHOLD
        )

        self._info = info

    async def event_id_to_event(self) -> dict[str, CaseActivityModel]:
        """
        Returns a dict of event ids to events with the current case ids
        """
        if not hasattr(self, "_event_id_to_event"):
            await self._get_events_with_case_ids()
        return self._event_id_to_event

    async def case_ids_to_check(self) -> set[str]:
        """
        Returns a set of case IDs to check.
        Excludes cases where `case_id` is None.
        """
        if not hasattr(self, "_case_ids_to_check"):
            self._case_ids_to_check = {
                str(event.case_id)
                for event in (await self.event_id_to_event()).values()
                if event.case_id is not None  # Exclude None values
            }
        return self._case_ids_to_check

    async def staff_to_check(self) -> list[CaseStaffModel | CaseStaffPlanModel]:
        """
        Returns a list of staff objects relevant to current case ids
        """
        if not hasattr(self, "_staff_to_check"):
            case_ids = list(await self.case_ids_to_check())
            case_staff = await self._info.context.case_staff_service.query_case_staff_relationships(
                CaseStaffQuery(case_ids=case_ids)
            )
            case_staff_plan = await self._info.context.case_staff_plan_service.query(
                query=CaseStaffPlanQueryDTO(case_ids=case_ids)
            )
            self._staff_to_check = case_staff + list(case_staff_plan)
        return self._staff_to_check

    async def staff_id_to_staff_model(self) -> dict[str, StaffModel]:
        """
        Returns a list of staff objects relevant to current staff ids
        """
        if not hasattr(self, "_staff_id_to_staff_model"):
            staff_ids = {
                str(staff_contact_information.staff_id)
                for staff_contact_information in await self.staff_contact_informations()
            }
            staff_models = await self._info.context.staff_service.query_staff(
                ids=list(staff_ids),
            )
            self._staff_id_to_staff_model = {
                str(staff_model.id): staff_model for staff_model in staff_models
            }
        return self._staff_id_to_staff_model

    async def staff_contact_informations(
        self,
    ) -> list[StaffEventNotificationContactInformationModel]:
        """
        Returns a list of staff contact information objects relevant to the current staff
        """
        if not hasattr(self, "_staff_contact_informations"):
            self._staff_contact_informations: list[
                StaffEventNotificationContactInformationModel
            ] = await self._info.context.contact_information_service.query_many_staff_event_notification_contact_information_relationships(
                queries=StaffEventNotificationQueryDto(org_ids=[str(self._site_model.org_id)])
            )
        return self._staff_contact_informations

    async def staff_contact_information_id_to_staff_contact_information(
        self,
    ) -> dict[uuid.UUID, StaffEventNotificationContactInformationModel]:
        """
        Returns a dictionary of staff contact information id to staff contact information relevant to the current staff
        This is needed for debouncing
        """
        if not hasattr(self, "_staff_contact_information_id_to_staff_contact_information"):
            self._staff_contact_information_id_to_staff_contact_information = {
                staff_contact_information.id: staff_contact_information
                for staff_contact_information in await self.staff_contact_informations()
            }
        return self._staff_contact_information_id_to_staff_contact_information

    async def staff_contact_information_id_to_contact_information(
        self,
    ) -> dict[uuid.UUID, ContactInformationModel]:
        """
        Returns a dictionary of staff event notification id to contact information
        This is needed for debouncing
        """
        if not hasattr(self, "_staff_event_notification_id_to_contact_information"):
            contact_informations = (
                await self._info.context.contact_information_service.query_contact_information(
                    ContactInformationQueryDto(
                        ids=list(
                            {
                                str(sci.contact_information_id)
                                for sci in await self.staff_contact_informations()
                            }
                        ),
                    )
                )
            )

            contact_information_id_to_contact_information = {
                contact_information.id: contact_information
                for contact_information in contact_informations
            }

            self._staff_event_notification_id_to_contact_information = {
                staff_event_notification.id: contact_information_id_to_contact_information[
                    staff_event_notification.contact_information_id
                ]
                for staff_event_notification in await self.staff_contact_informations()
            }
        return self._staff_event_notification_id_to_contact_information

    async def staff_id_event_type_id_to_staff_contact_information(
        self,
    ) -> dict[tuple[uuid.UUID, str], list[StaffEventNotificationContactInformationModel]]:
        """
        Returns a dictionary of staff id and event type id combination to a list of staff contact information
        This is needed for debouncing
        """

        if not hasattr(self, "_staff_id_event_type_id_to_staff_contact_information"):
            self._staff_id_event_type_id_to_staff_contact_information = defaultdict(list)
            for staff_contact_information in await self.staff_contact_informations():
                self._staff_id_event_type_id_to_staff_contact_information[
                    (staff_contact_information.staff_id, staff_contact_information.event_type_id)
                ].append(staff_contact_information)
        return self._staff_id_event_type_id_to_staff_contact_information

    async def case_id_to_staff(self) -> dict[str, list[CaseStaffModel | CaseStaffPlanModel]]:
        """
        Returns a dictionary of case ids to a list of staff assigned to that case id
        """
        if not hasattr(self, "_case_id_to_staff"):
            self._case_id_to_staff = defaultdict(list)
            for staff in await self.staff_to_check():
                self._case_id_to_staff[str(staff.case_id)].append(staff)
        return self._case_id_to_staff

    async def already_sent_notifications(self) -> Sequence[StaffEventNotificationModel]:
        if not hasattr(self, "_already_sent_notifications"):
            self._already_sent_notifications = await self._info.context.contact_information_service.query_staff_event_notifications_history(
                EventNotificationQueryDto(
                    case_ids=list(await self.case_ids_to_check()),
                )
            )
        return self._already_sent_notifications

    async def case_id_event_types_already_notified(
        self,
    ) -> dict[tuple[str, str], list[StaffEventNotificationModel]]:
        """
        Returns a dictionary of the notifications sent for a case id and event type id combination
        We need these for debouncing, so we don't send the same notification on the same event type twice
        """

        if not hasattr(self, "_case_id_event_types_already_notified"):
            # Get the events that have already been sent for the cases so we can check if we've already sent a notification for the event type
            self._case_id_event_types_already_notified = defaultdict(list)
            notifications_promise = self.already_sent_notifications
            for notification in await notifications_promise():
                if (
                    notification.staff_event_contact_information_id
                    in await self.staff_contact_information_id_to_staff_contact_information()
                ):
                    # We use the type of the event from the staff contact information since we
                    # already have it in memory
                    self._case_id_event_types_already_notified[
                        (
                            (
                                await self.staff_contact_information_id_to_staff_contact_information()
                            )[notification.staff_event_contact_information_id].event_type_id,
                            notification.case_id,
                        )
                    ].append(notification)
        return self._case_id_event_types_already_notified

    async def notifications_to_send(self) -> set[StaffEventNotificationBlock]:
        """
        Returns a set of StaffEventNotificationBlocks that need to be sent
        """
        if not hasattr(self, "_notifications_to_send"):
            self._notifications_to_send = set()
            events = await self.event_id_to_event()
            cases = await self.case_id_to_staff()
            # We need to iterate over all events
            for event in events.values():
                # For each event we need to get all the staff that are assigned to the case
                # that way we can determine if we need to send a notification
                for staff in cases.get(str(event.case_id), []):
                    self._notifications_to_send.update(
                        await self._create_notification_blocks(
                            event,
                            staff.staff_id,
                            str(event.case_id),
                        )
                    )

        return self._notifications_to_send

    async def notifications_should_not_have_been_sent(self) -> list[ExcessNotificationData]:
        """
        Returns a list of notifications that should not have been sent
        """
        if not hasattr(self, "_notifications_should_not_have_been_sent"):
            already_sent_notifications_for_site_all: Sequence[
                StaffEventNotificationModel
            ] = await self._info.context.contact_information_service.query_staff_event_notifications_history(
                EventNotificationQueryDto(
                    site_ids=[self._site_id],
                    min_sent_time=self._time_to_check - self._time_window_to_search,
                    max_sent_time=self._time_to_check,
                    sent_status=[SentNotificationStatus.SENT],
                )
            )
            already_sent_notifications_for_site_map = {
                notification.id: notification
                for notification in already_sent_notifications_for_site_all
            }
            already_sent_notifications_for_site = list(
                already_sent_notifications_for_site_map.values()
            )
            staff_contact_information_ids = {
                sci.id for sci in await self.staff_contact_informations()
            }
            notifications_should_have_been_sent_ids = {
                sen.id
                for sen in await self.already_sent_notifications()
                if sen.staff_event_contact_information_id in staff_contact_information_ids
            }

            # Pre-fetch all mappings to avoid repeated awaits
            staff_contact_information_map = (
                await self.staff_contact_information_id_to_staff_contact_information()
            )
            staff_id_to_staff_model = await self.staff_id_to_staff_model()
            contact_information_map = (
                await self.staff_contact_information_id_to_contact_information()
            )

            excess_notifications: list[ExcessNotificationData] = []
            for notification in already_sent_notifications_for_site:
                if notification.id in notifications_should_have_been_sent_ids:
                    continue

                staff_contact_information = staff_contact_information_map.get(
                    notification.staff_event_contact_information_id
                )
                if not staff_contact_information:
                    continue

                staff_model = staff_id_to_staff_model.get(str(staff_contact_information.staff_id))
                if not staff_model:
                    continue

                contact_information_model = contact_information_map.get(
                    notification.staff_event_contact_information_id
                )
                if not contact_information_model:
                    continue

                excess_notifications.append(
                    ExcessNotificationData(
                        staff_event_notification=notification,
                        recipient_name=f"{contact_information_model.first_name} {contact_information_model.last_name}",
                        staff_name=f"{staff_model.first_name} {staff_model.last_name}",
                        event_type_id=staff_contact_information.event_type_id,
                    )
                )

            self._notifications_should_not_have_been_sent = excess_notifications
        return self._notifications_should_not_have_been_sent

    async def duplicate_notifications(self) -> list[DuplicateNotificationData]:
        """
        Returns a list of notifications that are duplicates
        """
        if not hasattr(self, "_duplicate_notifications"):
            self._duplicate_notifications = []
            event_id_to_event_already_sent_for = {
                event.id: event
                for event in await self._info.context.event_service.get_events(
                    [
                        notification.event_id
                        for notification in await self.already_sent_notifications()
                    ]
                )
            }
            duplicated_notifications = [
                notification
                for notification in await self.already_sent_notifications()
                if notification.duplicated_id
            ]
            for notification in duplicated_notifications:
                notification_event = event_id_to_event_already_sent_for[notification.event_id]
                event_type_id = notification_event.event_type_id

                contact_information = (
                    await self.staff_contact_information_id_to_contact_information()
                )[notification.staff_event_contact_information_id]
                staff_id = (await self.staff_contact_information_id_to_staff_contact_information())[
                    notification.staff_event_contact_information_id
                ].staff_id
                # FIXME: why is the dict empty?
                staff_id_to_staff_model = await self.staff_id_to_staff_model()
                staff_model = staff_id_to_staff_model[str(staff_id)]
                self._duplicate_notifications.append(
                    DuplicateNotificationData(
                        staff_event_notification=notification,
                        event_type_id=event_type_id,
                        staff_name=f"{staff_model.first_name} {staff_model.last_name}",
                        recipient_name=f"{contact_information.first_name} {contact_information.last_name}",
                    )
                )
        return self._duplicate_notifications

    @property
    async def room_id_to_room_name(self) -> dict[str, str]:
        """
        Returns a dictionary of room_id to room_name
        """
        if not hasattr(self, "_room_id_to_room_name"):
            rooms = await self._info.context.room_service.get_rooms_in_site(site_id=self._site_id)
            self._room_id_to_room_name = {room.id: room.name for room in rooms}
        return self._room_id_to_room_name

    async def process(self) -> set[StaffEventNotificationBlock]:
        """
        This is a helper method that will process the events and return the notifications that need to be sent
        With early termination if possible
        """
        # First we need to get all the relevant events so we can early terminate if possible
        # Previously, this was done via phases matched to Cases.
        # Now, events are fetched directly from the case_activity table.

        # Get local time based on site timezone
        local_time = self._time_to_check.astimezone(tz=ZoneInfo(self._site_model.timezone))

        # Don't send notifications on weekends at Health First
        if self._site_id == "HF-VH02" and local_time.weekday() >= 5:
            return set()

        # Retrieve the feature flag variation list from LaunchDarkly
        quiet_hour_sites: list[str] = (
            self._info.context.launch_darkly_service.get_feature_flag(
                "quiet-hours-notification-sites", default=[]
            )
            or []
        )
        # Check for quiet hours (11PM–6AM local time) for applicable sites
        if self._site_id in quiet_hour_sites and (local_time.hour < 6 or local_time.hour >= 23):
            # Skip sending notifications during quiet hours
            return set()

        if not await self.event_id_to_event():
            return set()

        # If there are no staff with contact information, we don't need to send any notifications
        if not [
            True
            for _, contact_information in (
                await self.staff_id_event_type_id_to_staff_contact_information()
            ).items()
            if len(contact_information) > 0
        ]:
            return set()

        return await self.notifications_to_send()

    async def _get_events_with_case_ids(self) -> None:
        """
        Retrieves all events with associated cases from the case_activity table.
        Previously, this method used the phase service to get case_id.
        It now directly queries the CaseActivity service
        """

        # Fetch case activities within the defined time range for the site
        case_activities = await self._info.context.case_activity_service.get_case_activities(
            site_id=str(self._site_id),
            min_time=self._time_to_check - self._time_window_to_search,
            max_time=self._time_to_check + timedelta(minutes=5),
            include_source_type="ehr",  # only include 'ehr' source_type for v0 release
        )

        # Store event_id -> CaseActivityModel mapping
        self._event_id_to_event: dict[str, CaseActivityModel] = {
            case_activity.id: case_activity for case_activity in case_activities
        }

    async def _should_notify(
        self,
        event: CaseActivityModel,
        last_notifications: list[StaffEventNotificationModel],
        staff_contact_informations: list[StaffEventNotificationContactInformationModel],
    ) -> list[StaffEventNotificationContactInformationModel]:
        """
        This method determines who we should notify for a given event
        """
        # If we shouldn't notify, return an empty list
        if event.source_type == "forecasting":
            return []

        contact_informations_to_notify = []
        contact_information_id_to_notifications = defaultdict(list)
        for notification in last_notifications:
            contact_info_id = (await self.staff_contact_information_id_to_contact_information())[
                notification.staff_event_contact_information_id
            ].id
            contact_information_id_to_notifications[contact_info_id].append(notification)

        # Here we add the staff_contact_informations that have contact infos we haven't seen to the list of contact_informations_to_notify
        for staff_contact_information in staff_contact_informations:
            should_notify = True

            # If we've already notified for this contact information, we don't need to notify again
            # Unless the event is more than 10 minutes after the last notification
            contact_info_id = (await self.staff_contact_information_id_to_contact_information())[
                staff_contact_information.id
            ].id

            for notification in contact_information_id_to_notifications[contact_info_id]:
                # If the notification is for the same contact information and of the same
                # event_type_id, we don't need to notify again
                if (
                    abs(event.start_time - notification.event_time) < timedelta(minutes=10)
                    and notification.sent_status == SentNotificationStatus.SENT
                ):
                    should_notify = False
                    break
            if should_notify:
                contact_informations_to_notify.append(staff_contact_information)

        return contact_informations_to_notify

    async def _create_notification_blocks(
        self,
        event: CaseActivityModel,
        staff_id: uuid.UUID,
        case_id: str,
    ) -> set[StaffEventNotificationBlock]:
        """
        This method creates the notification blocks for a given event
        These are used to send notifications to staff and prevent duplicate notifications
        """
        notification_blocks = set()

        # Get the last notifications for this event type and case id
        # We need to get all the staff event notifications for this event type and case id
        # So we don't notify the same contact information twice
        last_notifications = (await self.case_id_event_types_already_notified()).get(
            (event.event_type_id, case_id), []
        )

        # For each staff_event_notification, create a notification block
        for staff_event_notification_contact_information in await self._should_notify(
            event,
            last_notifications,
            (await self.staff_id_event_type_id_to_staff_contact_information()).get(
                (staff_id, event.event_type_id), []
            ),
        ):
            _last_notification = [
                n
                for n in last_notifications
                if n.event_id == event.id
                and n.case_id == case_id
                and n.staff_event_contact_information_id
                == staff_event_notification_contact_information.id
            ]
            _last_notification.sort(key=lambda x: x.attempts)
            attempts = _last_notification[-1].attempts if _last_notification else 0
            # any item in the list will do
            notification_id = _last_notification[0].id if _last_notification else uuid.uuid4()
            contact_information = (
                await self.staff_contact_information_id_to_contact_information()
            ).get(staff_event_notification_contact_information.id)
            # duplicate id, check if last notification items that are of type set
            # sort last notification by updated_time and get the first time that are of type SENT
            # if not of type SENT is not a duplicated.
            duplicates = sorted(
                [n for n in last_notifications if n.sent_status == SentNotificationStatus.SENT],
                key=lambda x: x.event_time,
            )
            duplicated_id = duplicates[0].id if duplicates else None
            if (
                contact_information
                and self._time_to_check - event.start_time < self._time_window_to_search
            ):
                staff_id_to_staff_model = await self.staff_id_to_staff_model()
                staff_model = staff_id_to_staff_model[str(staff_id)]
                room_name = (await self.room_id_to_room_name).get(event.room_id, "OR")
                room_symbol = ""
                if (
                    room_name is not None
                    and len((room_numbers := re.findall(r"\d+$", room_name))) > 0
                ):
                    # Gotta offset the room number by 1 since the room numbers are 1 indexed
                    room_index = int(room_numbers[-1]) - 1
                    room_symbol = ROOM_SYMBOL_MAP[room_index % len(ROOM_SYMBOL_MAP)]
                notification_blocks.add(
                    StaffEventNotificationBlock(
                        notification_id=notification_id,
                        staff_id=staff_id,
                        event_id=event.id,
                        site_id=self._site_model.id,
                        site_name=self._site_model.name,
                        event_type_id=event.event_type_id,
                        event_start_time=event.start_time,
                        case_id=case_id,
                        contact_information_value=contact_information.contact_information_value,
                        contact_information_type=contact_information.type,
                        room_name=room_name,
                        room_symbol=room_symbol,
                        staff_event_contact_information_id=staff_event_notification_contact_information.id,
                        staff_name=f"{staff_model.first_name} {staff_model.last_name}",
                        recipient_name=f"{contact_information.first_name} {contact_information.last_name}",
                        duplicated_id=duplicated_id,
                        attempts=attempts,
                        sent_time=datetime.datetime.utcnow(),
                    )
                )

        return notification_blocks
