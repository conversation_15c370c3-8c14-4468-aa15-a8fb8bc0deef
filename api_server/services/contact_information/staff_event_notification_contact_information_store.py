import dataclasses
import datetime
import uuid
from dataclasses import dataclass
from typing import FrozenSet, List, Optional, Sequence

import sqlalchemy
from dataclasses_json import DataClassJsonMixin
from sqlalchemy import (
    DateTime,
    ForeignKey,
    PrimaryKeyConstraint,
    String,
    UniqueConstraint,
    cast,
    select,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import mapped_column, Mapped

from apella_cloud_api.exceptions import ClientError
from api_server.services.events.event_store import EventModel
from api_server.services.observations.observation_store import ObservationModel
from databases.sql import Base, new_async_session
from databases.sql.helpers import get_exception_message
from databases.sql.mixins import ArchivedTimeMixin, TimestampMixin
from asyncpg.exceptions import IntegrityConstraintViolationError

from utils.history_meta import Versioned
from enum import auto, Enum


class SentNotificationStatus(Enum):
    SENT = auto()
    FAILED = auto()
    NOT_ATTEMPTED = auto()
    MISSED = auto()


@dataclass
class EventNotificationQueryDto(DataClassJsonMixin):
    query_id: uuid.UUID = dataclasses.field(default_factory=uuid.uuid4)
    staff_event_notification_contact_information_ids: Optional[List[str]] = None
    staff_ids: Optional[List[str]] = None
    case_ids: Optional[List[str]] = None
    event_type_ids: Optional[List[str]] = None
    room_ids: Optional[List[str]] = None
    event_ids: Optional[List[str]] = None
    site_ids: Optional[List[str]] = None
    min_sent_time: Optional[datetime.datetime] = None
    max_sent_time: Optional[datetime.datetime] = None
    sent_status: Optional[List[SentNotificationStatus]] = None


@dataclass(frozen=True, eq=True)
class StaffEventNotificationContactInformationFrozen:
    contact_information_id: uuid.UUID
    staff_id: uuid.UUID
    event_type_id: str


@dataclass(frozen=True, eq=True)
class StaffEventNotificationContactInformationEventTypeStaffQuery:
    id: str
    event_type_ids: Optional[FrozenSet[str]] = None
    initialized: Optional[bool] = None


@dataclass(frozen=True, eq=True)
class ContactInformationSubscriptionQuery:
    id: str


class StaffEventNotificationContactInformationModel(Base, TimestampMixin, ArchivedTimeMixin):
    __tablename__ = "staff_event_notification_contact_information"
    __table_args__ = (
        PrimaryKeyConstraint(
            "contact_information_id",
            "staff_id",
            "event_type_id",
            name="pk_contactInfoId_staffId_eventTypeId",
        ),
        UniqueConstraint(
            "id",
            name="uq_staffEventNotification_id",
        ),
    )
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), default=uuid.uuid4, index=True, nullable=True
    )
    staff_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("staff.id", ondelete="CASCADE"), nullable=False, index=True
    )
    contact_information_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("contact_information.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    event_type_id: Mapped[str] = mapped_column(
        String,
        nullable=False,
        index=True,
    )

    def __repr__(self) -> str:
        return f"<StaffEventContactInformation(contact_information_id='{self.contact_information_id}', staff_id='{self.staff_id}', event_type_id='{self.event_type_id}', archived_time='{self.archived_time}')>"


class StaffEventNotificationOldModel(Base, TimestampMixin):
    __tablename__ = "staff_event_notification_old_01"
    __table_args__ = (
        PrimaryKeyConstraint(
            "message_id",
            "staff_event_contact_information_id",
            name="pk_staffEventNotification_msgId_senciID_old",
        ),
    )
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), default=uuid.uuid4, index=True, nullable=False
    )
    message_id: Mapped[str] = mapped_column(String, nullable=False, index=True)
    staff_event_contact_information_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("staff_event_notification_contact_information.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    event_id: Mapped[str] = mapped_column(
        String,
        nullable=False,
        index=True,
    )
    case_id: Mapped[str] = mapped_column(
        String,
        ForeignKey("cases.case_id", ondelete="SET NULL", name="notification_case_id_fkey"),
        nullable=True,
        index=True,
    )
    event_time: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, index=True
    )

    def __repr__(self) -> str:
        return f"<StaffEventNotification(staff_event_notification_id='{self.staff_event_contact_information_id}', event_id='{self.event_id}', case_id='{self.case_id}')>"


class StaffEventNotificationModel(Base, Versioned, TimestampMixin):
    __tablename__ = "staff_event_notification"
    __table_args__ = (  # type: ignore
        PrimaryKeyConstraint(
            "id",
            name="pk_staffEventNotification_id_sen",
        ),
        UniqueConstraint(
            "staff_event_contact_information_id",
            "event_id",
            "case_id",
            name="uq_staffEventNotification_senciID_event_id_case_id",
        ),
    )
    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), default=uuid.uuid4, index=True)
    message_id: Mapped[str] = mapped_column(String, nullable=False, index=True)
    staff_event_contact_information_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("staff_event_notification_contact_information.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    event_id: Mapped[str] = mapped_column(
        String,
        nullable=False,
        index=True,
    )
    case_id: Mapped[str] = mapped_column(
        String,
        ForeignKey("cases.case_id", ondelete="SET NULL", name="notification_case_id_fkey"),
        nullable=True,
        index=True,
    )
    event_time: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, index=True
    )
    sent_status: Mapped[SentNotificationStatus] = mapped_column(
        sqlalchemy.Enum(SentNotificationStatus),
        nullable=False,
        server_default=SentNotificationStatus.SENT.name,
    )
    attempts: Mapped[int] = mapped_column(sqlalchemy.Integer, nullable=False, server_default="1")
    duplicated_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("staff_event_notification.id", name="notification_duplicated_id_fkey"),
        nullable=True,
    )
    is_excess: Mapped[bool] = mapped_column(sqlalchemy.Boolean, nullable=False, default=False)
    sent_time: Mapped[datetime.datetime] = mapped_column(
        sqlalchemy.DateTime(timezone=True), nullable=True
    )

    def __repr__(self) -> str:
        return f"<StaffEventNotification(staff_event_notification_id='{self.staff_event_contact_information_id}', event_id='{self.event_id}', case_id='{self.case_id}')>"


StaffEventNotificationModelHistory = StaffEventNotificationModel.__history_mapper__.class_  # type: ignore [attr-defined]


class StaffEventNotificationContactInformationStore:
    async def upsert_staff_event_notification_contact_information(
        self,
        staff_event_notification_contact_information: List[
            StaffEventNotificationContactInformationModel
        ],
    ) -> List[StaffEventNotificationContactInformationModel]:
        """
        Upsert all staff_event_notification_contact_information into the database
        """
        async with new_async_session() as session:
            try:
                updated_staff_event_notification_contact_informations = [
                    await session.merge(staff_event_notification_contact_info)
                    for staff_event_notification_contact_info in staff_event_notification_contact_information
                ]

                await session.commit()

                for (
                    staff_event_notification_contact_info
                ) in updated_staff_event_notification_contact_informations:
                    await session.refresh(staff_event_notification_contact_info)

            except IntegrityConstraintViolationError as e:
                error_params = e.params  # type: ignore [attr-defined]
                raise ClientError(
                    status_code=409,
                    message=f"Tried to create duplicate subscriber information, error on params {error_params}",
                )

        return updated_staff_event_notification_contact_informations

    async def upsert_staff_event_notifications(
        self,
        staff_event_notifications: list[StaffEventNotificationModel],
    ) -> list[StaffEventNotificationModel]:
        """
        Upsert all staff_event_notification_message into the database
        """
        upserted_notifications: list[StaffEventNotificationModel] = []
        async with new_async_session() as session:
            try:
                upserted_notifications = []
                for staff_event_notification in staff_event_notifications:
                    staff_event_notification.id = staff_event_notification.id or uuid.uuid4()
                    upserted_notifications.append(await session.merge(staff_event_notification))

                await session.commit()
                for notification in upserted_notifications:
                    await session.refresh(notification)
            except sqlalchemy.exc.IntegrityError as e:
                raise ClientError(
                    status_code=409,
                    message=f"Tried to create duplicate notification, error on params {get_exception_message(e)}",
                )

        return upserted_notifications

    async def get_messages_for_staff_event_notification_contact_informations(
        self,
        staff_event_notification_contact_information_id: list[str],
    ) -> Sequence[StaffEventNotificationModel]:
        """
        Get all messages for a staff_event_notification_id
        """
        async with new_async_session() as session:
            return (
                await session.scalars(
                    select(StaffEventNotificationModel).filter(
                        StaffEventNotificationModel.staff_event_contact_information_id.in_(
                            staff_event_notification_contact_information_id
                        )
                    )
                )
            ).all()

    async def query_staff_event_notifications(
        self,
        request: EventNotificationQueryDto,
    ) -> Sequence[StaffEventNotificationModel]:
        """
        Query staff event notifications using the given dto
        """
        async with new_async_session() as session:
            return await self._apply_staff_event_notification_query(
                StaffEventNotificationModel, request, session
            )

    async def query_staff_event_notifications_history(
        self, request: EventNotificationQueryDto
    ) -> List[StaffEventNotificationModel]:
        async with new_async_session() as session:
            base_results = await self._apply_staff_event_notification_query(
                StaffEventNotificationModelHistory, request, session
            )

            result = []
            for notification_history in base_results:
                # For each history row, we create a new StaffEventNotificationModel instance
                notification = StaffEventNotificationModel()
                # And use dict to copy all of the properties except for `_sa_instance_state`
                _sa_instance_state = notification.__dict__["_sa_instance_state"]
                notification.__dict__ = notification_history.__dict__
                notification.__dict__["_sa_instance_state"] = _sa_instance_state
                result.append(notification)
            return result

    async def _apply_staff_event_notification_query(
        self,
        model: type[StaffEventNotificationModel],
        query_dto: EventNotificationQueryDto,
        session: AsyncSession,
    ) -> Sequence[StaffEventNotificationModel]:
        # Query with EventModel join
        event_query = (
            select(model)
            .join(
                StaffEventNotificationContactInformationModel,
                model.staff_event_contact_information_id
                == StaffEventNotificationContactInformationModel.id,
            )
            .join(EventModel, model.event_id == EventModel.id)
        )

        if query_dto.staff_event_notification_contact_information_ids:
            event_query = event_query.filter(
                model.staff_event_contact_information_id.in_(
                    query_dto.staff_event_notification_contact_information_ids
                )
            )
        if query_dto.staff_ids:
            event_query = event_query.filter(
                StaffEventNotificationContactInformationModel.staff_id.in_(query_dto.staff_ids)
            )
        if query_dto.event_type_ids:
            event_query = event_query.filter(EventModel.event_type_id.in_(query_dto.event_type_ids))
        if query_dto.room_ids:
            event_query = event_query.filter(EventModel.room_id.in_(query_dto.room_ids))
        if query_dto.case_ids:
            event_query = event_query.filter(model.case_id.in_(query_dto.case_ids))
        if query_dto.event_ids:
            event_query = event_query.filter(model.event_id.in_(query_dto.event_ids))
        if query_dto.site_ids:
            event_query = event_query.filter(EventModel.site_id.in_(query_dto.site_ids))
        if query_dto.min_sent_time:
            event_query = event_query.filter(model.created_time >= query_dto.min_sent_time)
        if query_dto.max_sent_time:
            event_query = event_query.filter(model.created_time <= query_dto.max_sent_time)
        if query_dto.sent_status:
            event_query = event_query.filter(model.sent_status.in_(query_dto.sent_status))

        event_results = (await session.scalars(event_query)).all()

        # Query with ObservationModel join
        observation_query = (
            select(model)
            .join(
                StaffEventNotificationContactInformationModel,
                model.staff_event_contact_information_id
                == StaffEventNotificationContactInformationModel.id,
            )
            .join(ObservationModel, cast(model.event_id, UUID) == ObservationModel.id)
        )

        if query_dto.staff_event_notification_contact_information_ids:
            observation_query = observation_query.filter(
                model.staff_event_contact_information_id.in_(
                    query_dto.staff_event_notification_contact_information_ids
                )
            )
        if query_dto.staff_ids:
            observation_query = observation_query.filter(
                StaffEventNotificationContactInformationModel.staff_id.in_(query_dto.staff_ids)
            )
        if query_dto.event_type_ids:
            observation_query = observation_query.filter(
                ObservationModel.type_id.in_(query_dto.event_type_ids)
            )
        if query_dto.case_ids:
            observation_query = observation_query.filter(model.case_id.in_(query_dto.case_ids))
        if query_dto.event_ids:
            observation_query = observation_query.filter(model.event_id.in_(query_dto.event_ids))
        if query_dto.min_sent_time:
            observation_query = observation_query.filter(
                model.created_time >= query_dto.min_sent_time
            )
        if query_dto.max_sent_time:
            observation_query = observation_query.filter(
                model.created_time <= query_dto.max_sent_time
            )
        if query_dto.sent_status:
            observation_query = observation_query.filter(
                model.sent_status.in_(query_dto.sent_status)
            )

        observation_results = (await session.scalars(observation_query)).all()

        return list(event_results) + list(observation_results)
