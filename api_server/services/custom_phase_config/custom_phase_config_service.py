from dataclasses import dataclass
from api_server.logging.audit import log_calls_to_audit
from auth.auth import Auth
from typing import List, Optional

from api_server.services.custom_phase_config.custom_phase_config_store import (
    CustomPhaseConfigStore,
    CustomPhaseConfigModel,
)


@dataclass
class CustomPhaseConfigService:
    auth: Auth
    custom_phase_config_store: CustomPhaseConfigStore

    @log_calls_to_audit()
    async def query_custom_phase_configs(self) -> List[CustomPhaseConfigModel]:
        org_id = self.auth.get_calling_org_id()

        return (
            await self.custom_phase_config_store.query_custom_phase_configs(org_id=org_id)
            if org_id is not None
            else []
        )

    @log_calls_to_audit()
    async def upsert_custom_phase_config(
        self,
        custom_phase_id: Optional[str],
        start_event_type: str,
        end_event_type: str,
        name: str,
        description: str,
    ) -> CustomPhaseConfigModel:
        return await self.custom_phase_config_store.upsert_custom_phase_config(
            CustomPhaseConfigModel(
                org_id=self.auth.get_calling_org_id(),
                id=custom_phase_id,
                start_event_type=start_event_type,
                end_event_type=end_event_type,
                name=name,
                description=description,
            )
        )

    @log_calls_to_audit()
    async def delete_custom_phase_config(self, custom_phase_id: str) -> bool:
        return await self.custom_phase_config_store.delete_custom_phase_config(custom_phase_id)
