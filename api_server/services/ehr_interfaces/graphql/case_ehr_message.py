# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs
from typing import Optional

import graphene
from graphql import GraphQLError
from graphql.type import GraphQLResolveInfo

from api_server.graphql.context import GrapheneInfo
from api_server.graphql.pagination import PaginationConnection
from api_server.services.case.case_store import Case, CaseRaw
from api_server.services.case.graphql.scheduled_case import ScheduledCase
from api_server.services.organization.graphql.organization import Organization
from api_server.services.organization.organization_db import (
    Organization as OrganizationModel,
)


class CaseEhrMessageQueryInput(graphene.InputObjectType):
    org_id = graphene.String(required=True)
    min_event_time = graphene.DateTime(required=True)
    max_event_time = graphene.DateTime()
    case_id = graphene.String()
    event_types = graphene.List(graphene.String)
    raw_message_text_search = graphene.String()
    no_case_id = graphene.Boolean()
    latest_per_case_id = graphene.Boolean()


class CaseEhrMessage(graphene.ObjectType):
    id = graphene.ID(resolver=lambda case_raw, info: case_raw.external_message_id, required=True)
    message = graphene.String(required=True)
    organization = graphene.Field(lambda: Organization, required=True)
    event_type = graphene.String()
    event_time = graphene.DateTime()
    scheduled_case = graphene.Field(lambda: ScheduledCase)

    @staticmethod
    def resolve_message(case_raw: CaseRaw, info: GraphQLResolveInfo, *args, **kwargs) -> str:
        return str(case_raw.raw)

    @staticmethod
    async def resolve_organization(case_raw: CaseRaw, info: GrapheneInfo, *args, **kwargs):
        org: Optional[OrganizationModel] = await info.context.org_loader.load(case_raw.org_id)
        if org is None:
            raise GraphQLError(
                message=f"Unable to find organization for case {case_raw.case_id} with org id: {case_raw.org_id}"
            )
        return org

    @staticmethod
    async def resolve_scheduled_case(
        case_raw: CaseRaw, info: GrapheneInfo, *args, **kwargs
    ) -> Optional[Case]:
        if case_raw.case_id:
            return await info.context.case_loader.load(str(case_raw.case_id))
        return None


class CaseEhrMessageConnection(PaginationConnection):
    class Meta:
        node = CaseEhrMessage

    class CaseEhrMessageEdge(graphene.ObjectType):
        node = graphene.Field(CaseEhrMessage, required=True)
        cursor = graphene.String(required=True)

    edges = graphene.List(graphene.NonNull(CaseEhrMessageEdge), required=True)
