# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs
from dataclasses import dataclass

from api_server.services.ehr_interfaces.mapping_store import MappingStore
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import READ_MAPPINGS, WRITE_MAPPINGS


@dataclass
class MappingService:
    auth: Auth
    mapping_store: MappingStore

    @requires_permissions(READ_MAPPINGS, enforce_universal_user=True)
    async def get_mapping(
        self, external_id_type: str, external_id: str, ignore_no_room_mapping: bool = False
    ) -> str:
        return await self.mapping_store.get_mapping(
            external_id_type=external_id_type,
            external_id=external_id,
            ignore_no_room_mapping=ignore_no_room_mapping,
        )

    @requires_permissions(WRITE_MAPPINGS, enforce_universal_user=True)
    async def set_mapping(self, external_id_type: str, external_id: str, internal_id: str):
        await self.mapping_store.set_mapping(
            external_id_type=external_id_type,
            external_id=external_id,
            internal_id=internal_id,
        )
