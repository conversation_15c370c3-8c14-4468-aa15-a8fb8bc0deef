# Please add type annotations to function definitions in this file and remove this line!
import json
from dataclasses import dataclass
from typing import Iterable
import sqlalchemy.exc
import sqlalchemy.orm.exc
from sqlalchemy import String, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Mapped, mapped_column

from apella_cloud_api.exceptions import NotFound
from api_server.services.room.room_store import RoomStore
from api_server.services.site.site_store import Site
from databases.sql import Base, new_async_session
from databases.sql.mixins import TimestampMixin

# This should be moved to a separate file / store later.
from databases.sql.sync_as_async_session import async_db_session_provider

LOCAL_ROOM_MAPPING = {
    "Room 0": "room-id-0",
    "Room 1": "room_id_1",
    "Room 2": "room_id_2",
    "Room 3": "room_id_3",
}


@dataclass
class RoomInfoDto:
    room_id: str
    site_id: str
    timezone: str


class IdentifierMapping(Base, TimestampMixin):
    __tablename__ = "identifier_mapping"

    # Composite key of external id/type
    external_id_type: Mapped[str] = mapped_column(String, primary_key=True, nullable=False)
    external_id: Mapped[str] = mapped_column(String, primary_key=True, nullable=False)

    internal_id: Mapped[str] = mapped_column(String, nullable=False)


class MappingStore:
    @async_db_session_provider
    async def set_mapping(
        self, external_id_type: str, external_id: str, internal_id: str, session: AsyncSession
    ) -> None:
        sql_mapping = IdentifierMapping()
        sql_mapping.external_id_type = external_id_type
        sql_mapping.external_id = external_id
        sql_mapping.internal_id = internal_id
        await session.merge(sql_mapping)
        await session.commit()

    async def get_mapping(
        self, external_id_type: str, external_id: str, ignore_no_room_mapping: bool = False
    ) -> str:
        try:
            query = select(IdentifierMapping)
            query = query.filter(IdentifierMapping.external_id_type == external_id_type)
            query = query.filter(IdentifierMapping.external_id == external_id)
            async with new_async_session() as session:
                results = await session.scalars(query)
                return results.one().internal_id
        except sqlalchemy.exc.NoResultFound:
            # See https://linear.app/apella/issue/EHR-820/update-identifier-mappings-to-not-return-404
            if ignore_no_room_mapping and external_id_type.endswith("::room_mapping"):
                return json.dumps(
                    {
                        "error": f"No room mapping found with external id: {external_id_type} {external_id}",
                    }
                )
            raise NotFound(f"No mapping found with external id: {external_id_type} {external_id}")

    async def get_multiple_mappings(
        self, external_id_type: str, external_ids: Iterable[str]
    ) -> dict[str, str]:
        query = select(IdentifierMapping)
        query = query.filter(IdentifierMapping.external_id_type == external_id_type)
        query = query.filter(IdentifierMapping.external_id.in_(external_ids))
        async with new_async_session() as session:
            results = await session.scalars(query)
            return {mapping.external_id: mapping.internal_id for mapping in results.all()}

    async def populate_room_info_mapping(
        self, org_id: str, room_names: Iterable[str], sites: list[Site]
    ) -> dict[str, RoomInfoDto]:
        external_id_type = (
            f"{org_id}::room_mapping"
            if org_id != "apella_internal_0"
            else "redox_facility_department_room"
        )
        room_mapping = await self.get_multiple_mappings(
            external_id_type=external_id_type,
            external_ids=room_names,
        )

        if len(room_mapping) == 0:
            room_info_mapping = {
                k: RoomInfoDto(room_id=v, timezone="America/Los_Angeles", site_id="site_id_0")
                for k, v in LOCAL_ROOM_MAPPING.items()
            }
        else:
            site_id_to_site_mapping = {site.id: site for site in sites}
            rooms = await RoomStore().query_rooms(org_id=org_id)
            room_id_to_room_mapping = {room.id: room for room in rooms}

            room_info_mapping = {}

            for room_name in room_mapping:
                room_id = room_mapping[room_name]
                room = room_id_to_room_mapping[room_id]
                site = site_id_to_site_mapping[room.site_id]

                room_info_mapping[room_name] = RoomInfoDto(
                    room_id=room_id,
                    site_id=site.id,
                    timezone=site.timezone,
                )

        return room_info_mapping
