from typing import Final

from fastapi import APIRouter, Request, Response, Depends
from fastapi.responses import PlainTextResponse
from api_server.app_service_provider.apella_app_container import app_container
from api_server.services.ehr_interfaces.mapping_service import MappingService

mapping_api_router: Final = APIRouter(prefix="/v1")


def get_mapping_service() -> MappingService:
    return app_container.provide_app_scope_type(MappingService)


@mapping_api_router.put("/mapping/{external_id_type}/{external_id}")
async def set_mapping(
    request: Request,
    external_id_type: str,
    external_id: str,
    mapping_service: MappingService = Depends(get_mapping_service),
) -> Response:
    body = await request.body()
    await mapping_service.set_mapping(
        external_id_type=external_id_type,
        external_id=external_id,
        internal_id=body.decode("utf-8"),
    )
    return Response()


@mapping_api_router.get(
    "/mapping/{external_id_type}/{external_id}", response_class=PlainTextResponse
)
async def get_mapping(
    external_id_type: str,
    external_id: str,
    mapping_service: MappingService = Depends(get_mapping_service),
) -> str:
    return await mapping_service.get_mapping(
        external_id_type=external_id_type, external_id=external_id
    )


mapping_api_router_v2: Final = APIRouter(prefix="/v2")


@mapping_api_router_v2.get(
    "/mapping/{external_id_type}/{external_id}", response_class=PlainTextResponse
)
async def get_mapping_v2(
    external_id_type: str,
    external_id: str,
    mapping_service: MappingService = Depends(get_mapping_service),
) -> str:
    return await mapping_service.get_mapping(
        external_id_type=external_id_type, external_id=external_id, ignore_no_room_mapping=True
    )
