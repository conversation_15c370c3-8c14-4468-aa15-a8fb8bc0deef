# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs
from dataclasses import dataclass
from datetime import datetime
from http import HTTPStatus
from typing import List, Optional, Sequence

from apella_cloud_api.dtos import (
    EventChangelogAction,
    EventHistoryQueryDto,
    EventQueryDto,
)
from apella_cloud_api.exceptions import ClientError
from api_server.logging.audit import async_log_calls_to_audit, log_calls_to_audit
from api_server.services.events.event_pubsub_store import EventPubSubStore
from api_server.services.events.event_models import (
    EventModel,
    EventTypeModel,
    EventWithPublish,
    EventLabelOptionModel,
    EventDashboardVisibilityModel,
)
from api_server.services.events.event_store import (
    PHASE_NAMES,
    EventStore,
)
from api_server.services.organization.organization_service import OrganizationService
from api_server.services.site.site_service import SiteService
from auth.auth import Auth
from auth.auth_decorator import async_requires_permissions, requires_permissions
from auth.permissions import READ_ANY_EVENT, WRITE_ANY_EVENT, WRITE_ANY_EVENT_TYPE


@dataclass
class EventService:
    auth: Auth
    event_store: EventStore
    site_service: SiteService
    event_pub_sub_store: EventPubSubStore
    organization_service: OrganizationService

    @async_log_calls_to_audit()
    @async_requires_permissions(WRITE_ANY_EVENT)
    async def create_event(self, event: EventModel):
        self.auth.check_resource_matches_auth(org_id=event.org_id, site_id=event.site_id)

        # Validate the contents of the event
        event.validate()

        await self.event_store.create_event(event=event)
        self._publish_event_changelog(EventChangelogAction.CREATE, event)

        return event

    @async_log_calls_to_audit()
    @async_requires_permissions(WRITE_ANY_EVENT)
    async def upsert_events(self, event_upserts: List[EventWithPublish]):
        events = []
        for event_upsert in event_upserts:
            event = event_upsert.event
            self.auth.check_resource_matches_auth(org_id=event.org_id, site_id=event.site_id)
            # Validate the contents of the event
            event.validate()
            events.append(event)

        result = await self.event_store.upsert_events(events)

        for event_upsert in event_upserts:
            if event_upsert.publish_changelog:
                self._publish_event_changelog(EventChangelogAction.CREATE, event_upsert.event)

        return result

    @async_log_calls_to_audit()
    @async_requires_permissions(WRITE_ANY_EVENT, enforce_universal_user=True)
    async def create_events(self, events: List[EventModel], publish_changelog: bool = True):
        for event in events:
            event.validate()
            if publish_changelog:
                self._publish_event_changelog(EventChangelogAction.CREATE, event)

        result = await self.event_store.create_events(events)
        return result

    @async_log_calls_to_audit()
    @async_requires_permissions(READ_ANY_EVENT)
    async def get_event(self, event_id: str) -> EventModel:
        return await self.event_store.get_event(event_id=event_id)

    @async_log_calls_to_audit()
    @async_requires_permissions(READ_ANY_EVENT)
    async def get_events(self, event_ids: List[str]) -> List[EventModel]:
        return await self.event_store.get_events(event_ids=event_ids)

    @async_log_calls_to_audit()
    @async_requires_permissions(WRITE_ANY_EVENT)
    async def replace_event(self, event: EventModel) -> EventModel:
        self.auth.check_resource_matches_auth(org_id=event.org_id, site_id=event.site_id)
        event.validate()
        new_event = await self.event_store.replace_event(event)
        self._publish_event_changelog(EventChangelogAction.UPDATE, new_event)
        return new_event

    @async_log_calls_to_audit()
    @async_requires_permissions(WRITE_ANY_EVENT)
    async def patch_event(
        self,
        event_id: str,
        event_type_id: str,
        start_time: datetime,
        labels: List[str],
        notes: str,
        source: str,
        source_type: str,
        camera_id: Optional[str],
        publish_changelog: bool = True,
    ) -> EventModel:
        event = await self.event_store.get_event(event_id=event_id)

        if source is None or source_type is None:
            raise ClientError(423, "EventUpdate must include source and source_type")

        if event_type_id is not None:
            event.event_type_id = event_type_id
        if start_time is not None:
            event.start_time = start_time
        if labels is not None:
            event.labels = labels
        if notes is not None:
            event.notes = notes
        if source is not None:
            event.source = source
        if source_type is not None:
            event.source_type = source_type
        if camera_id is not None:
            event.camera_id = camera_id

        event.validate()
        new_event = await self.event_store.replace_event(event)
        if publish_changelog:
            self._publish_event_changelog(EventChangelogAction.UPDATE, new_event)
        return new_event

    @async_log_calls_to_audit()
    @async_requires_permissions(WRITE_ANY_EVENT)
    async def delete_event(
        self,
        event_id: str,
        source: str,
        source_type: str,
        publish_changelog: bool = True,
    ):
        if source is None or source_type is None:
            raise ClientError(423, "EventUpdate must include source and source_type")

        event = await self.event_store.get_event(event_id=event_id)

        if publish_changelog:
            self._publish_event_changelog(EventChangelogAction.DELETE, event)

        await self.event_store.delete_event(event_id, source, source_type)

    def validate_event_query(self, query: EventQueryDto):
        """
        Validates the base EventQuery dto
        """
        if query.min_time and query.max_time:
            if query.min_time >= query.max_time:
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    message="'max_time' must be after 'min_time'",
                )
        elif query.min_start_time and query.max_start_time:
            if query.min_start_time >= query.max_start_time:
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    message="'max_start_time' must be after 'min_start_time'",
                )
        elif query.min_created_time and query.max_created_time:
            if query.min_created_time >= query.max_created_time:
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    message="'max_created_time' must be after 'min_created_time'",
                )
        elif isinstance(query, EventHistoryQueryDto) and query.event_ids is not None:
            if len(query.event_ids) == 0:
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    message="'event_ids' must not be an empty list",
                )
        else:
            raise ClientError(
                HTTPStatus.UNPROCESSABLE_ENTITY,
                message="A time range must be specified with min and max start/end/created times",
            )

        if query.room_id and query.room_ids:
            raise ClientError(
                HTTPStatus.UNPROCESSABLE_ENTITY,
                message="Please specify either room_id or room_ids, not both",
            )
        if query.site_id and query.site_ids:
            raise ClientError(
                HTTPStatus.UNPROCESSABLE_ENTITY,
                message="Please specify either site_id or site_ids, not both",
            )

        if query.include_dashboard_events_only and query.organization_id is None:
            raise ClientError(
                HTTPStatus.UNPROCESSABLE_ENTITY,
                message="Please specify an organization_id when using include_dashboard_events_only",
            )

    @async_log_calls_to_audit()
    @async_requires_permissions(READ_ANY_EVENT)
    async def query_events(self, query: EventQueryDto) -> List[EventModel]:
        if query.include_dashboard_events_only and query.organization_id is None:
            query.organization_id = self.auth.get_calling_org_id()
        self.validate_event_query(query)

        events = await self.event_store.query_events(query)

        return events

    @async_log_calls_to_audit()
    @async_requires_permissions(READ_ANY_EVENT)
    async def query_event_history(self, query: EventHistoryQueryDto) -> List[EventModel]:
        if query.include_dashboard_events_only and query.organization_id is None:
            query.organization_id = self.auth.get_calling_org_id()
        self.validate_event_query(query)

        events = await self.event_store.query_event_history(query)

        return events

    def _publish_event_changelog(self, action: EventChangelogAction, event: EventModel):
        # TODO update this as we're no longer creating events of type phase since 2022-01
        if event.event_type_id not in PHASE_NAMES.keys() and event.source_type in [
            "human_gt",
            "prediction",
        ]:
            event_change = event.to_change_dto(action)
            self.event_pub_sub_store.publish_event_changelog(event_change)

    @async_log_calls_to_audit()
    @async_requires_permissions(READ_ANY_EVENT)
    async def get_event_type(self, event_type_id: str) -> EventTypeModel:
        return await self.event_store.get_event_type(event_type_id=event_type_id)

    @async_log_calls_to_audit()
    @async_requires_permissions(READ_ANY_EVENT)
    async def get_event_types(self, types, ids) -> List[EventTypeModel]:
        return await self.event_store.get_event_types(types=types, ids=ids)

    @async_log_calls_to_audit()
    @async_requires_permissions(WRITE_ANY_EVENT_TYPE)
    async def create_event_type(self, new_event_type: EventTypeModel) -> EventTypeModel:
        new_event_type.validate()
        return await self.event_store.create_event_type(event_type=new_event_type)

    @async_log_calls_to_audit()
    @async_requires_permissions(WRITE_ANY_EVENT_TYPE)
    async def update_event_type(self, new_event_type: EventTypeModel) -> EventTypeModel:
        new_event_type.validate()
        return await self.event_store.update_event_type(event_type=new_event_type)

    @async_log_calls_to_audit()
    @async_requires_permissions(WRITE_ANY_EVENT_TYPE)
    async def delete_event_type(self, event_type_id: str):
        return await self.event_store.delete_event_type(event_type_id=event_type_id)

    @async_requires_permissions(READ_ANY_EVENT)
    async def get_event_label_options(self) -> List[EventLabelOptionModel]:
        return await self.event_store.get_event_label_options()

    @async_log_calls_to_audit()
    @async_requires_permissions(WRITE_ANY_EVENT)
    async def create_event_label_option(
        self, name: str, id: Optional[str] = None
    ) -> EventLabelOptionModel:
        return await self.event_store.create_event_label_option(id=id, name=name)

    @async_log_calls_to_audit()
    @async_requires_permissions(WRITE_ANY_EVENT)
    async def update_event_label_option(self, id: str, name: str) -> EventLabelOptionModel:
        return await self.event_store.update_event_label_option(id, name)

    @async_log_calls_to_audit()
    @async_requires_permissions(WRITE_ANY_EVENT)
    async def delete_event_label_option(self, id: str):
        return await self.event_store.delete_event_label_option(id)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_EVENT_TYPE)
    async def upsert_event_dashboard_visibility(
        self, model: EventDashboardVisibilityModel
    ) -> EventDashboardVisibilityModel:
        if model.org_id_filter is not None and len(model.org_id_filter) > 0:
            orgs = await self.organization_service.get_organizations(model.org_id_filter)
            assert len(orgs) == len(model.org_id_filter)

        return await self.event_store.upsert_event_dashboard_visibility(model)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_EVENT_TYPE)
    async def delete_event_dashboard_visibility(self, event_type_id: str) -> None:
        await self.event_store.delete_event_dashboard_visibility(event_type_id)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_EVENT)
    async def query_event_dashboard_visiblity(
        self, org_id: Optional[str] = None
    ) -> Sequence[EventDashboardVisibilityModel]:
        return await self.event_store.query_event_dashboard_visibility(org_id)
