# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

import datetime
import time
from http import HTTPStatus
from typing import Any, List, Optional, Sequence, Type

from asyncpg.exceptions import UniqueViolationError, ForeignKeyViolationError
import sqlalchemy.exc
import sqlalchemy.orm.exc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import String, cast, select, or_, delete
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy.sql import Select

from apella_cloud_api.dtos import (
    BatchEventCreateResultDto,
    EventHistoryQueryDto,
    EventQueryDto,
)
from apella_cloud_api.exceptions import ClientError, NotFound
from sqlalchemy.orm import registry as _reg

from databases.sql import new_async_session
from databases.sql.helpers import (
    merge_with_etag_async,
    check_exception,
    get_exception_message,
    async_postgres_insert_on_conflict_do_update_helper,
)
from databases.sql.sync_as_async_session import async_db_session_provider
from api_server.services.events.event_models import (
    EventModel,
    EventTypeModel,
    EventLabelOptionModel,
    EventDashboardVisibilityModel,
)

registry = _reg()
PHASE_NAMES = {
    "pre_operative": "Pre-Operative",
    "surgery": "Surgery",
    "post_operative": "Post-Operative",
    "turn_over_clean": "Turn Over Clean",
    "turn_over_idle": "Turn Over Idle",
    "turn_over_open": "Turn Over Open",
}


class EventStore:
    @async_db_session_provider
    async def create_event(self, event: EventModel, session: AsyncSession) -> EventModel:
        try:
            session.add(event)
            await session.commit()
            await session.refresh(event)
            return event
        except sqlalchemy.exc.IntegrityError as e:
            if check_exception(UniqueViolationError, e):
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY, f'id "{event.id}" already exists'
                )
            if check_exception(ForeignKeyViolationError, e):
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    f"Foreign key violation; {get_exception_message(e)}",
                )
            raise e

    @async_db_session_provider
    async def create_events(self, events: List[EventModel], session: AsyncSession):
        result = BatchEventCreateResultDto()
        start_time = time.monotonic()
        try:
            for event in events:
                session.add(event)
            await session.commit()
        except sqlalchemy.exc.IntegrityError as e:
            if check_exception(UniqueViolationError, e):
                # We could do some parsing on str(e.orig) to extract the id from:
                # UniqueViolation('duplicate key value violates unique constraint "events_pkey"\n
                # DETAIL:  Key (id)=(9fc7b91a-ae35-4c8c-a311-585a179acd9c) already exists.\n')
                # But since this is not meant for customers, we can return the db error
                raise ClientError(HTTPStatus.UNPROCESSABLE_ENTITY, str(e.orig))
            raise e

        duration = time.monotonic() - start_time
        result.events_created = len(events)
        result.duration_seconds = duration
        return result

    @async_db_session_provider
    async def upsert_events(self, events: List[EventModel], session: AsyncSession):
        # Get the existing events by id
        event_ids = list({event.id for event in events if event.id is not None})
        existing_events_map = {
            str(event.id): event
            for event in await self.__get_events(session=session, event_ids=event_ids)
        }

        try:
            for event in events:
                await merge_with_etag_async(event, existing_events_map.get(event.id), session)

            await session.commit()

            # We gotta refresh events so that event.attrs is joined to resolve GraphQL field requests
            return await self.__get_events(
                session=session, event_ids=[event.id for event in events]
            )
        except sqlalchemy.exc.IntegrityError as e:
            if check_exception(UniqueViolationError, e):
                # We could do some parsing on str(e.orig) to extract the id from:
                # UniqueViolation('duplicate key value violates unique constraint "events_pkey"\n
                # DETAIL:  Key (id)=(9fc7b91a-ae35-4c8c-a311-585a179acd9c) already exists.\n')
                # But since this is not meant for customers, we can return the db error
                raise ClientError(HTTPStatus.UNPROCESSABLE_ENTITY, str(e.orig))
            raise e

    @async_db_session_provider
    async def replace_event(self, event: EventModel, session: AsyncSession) -> EventModel:
        # First need to query the old event to ensure it is a valid event to replace
        old_event: EventModel = await self.__get_event(event_id=event.id, session=session)
        await session.merge(event)
        await session.commit()
        await session.refresh(old_event)
        return old_event

    @async_db_session_provider
    async def get_events(self, event_ids: List[str], session: AsyncSession) -> List[EventModel]:
        events: List[EventModel] = await self.__get_events(session=session, event_ids=event_ids)
        return events

    async def __get_events(self, event_ids: List[str], session: AsyncSession) -> List[EventModel]:
        return list(
            (await session.scalars(select(EventModel).filter(EventModel.id.in_(event_ids)))).all()
        )

    @async_db_session_provider
    async def get_event(self, event_id: str, session: AsyncSession) -> EventModel:
        event: EventModel = await self.__get_event(event_id=event_id, session=session)
        return event

    async def __get_event(self, event_id: str, session: AsyncSession) -> EventModel:
        query = select(EventModel).filter(EventModel.id == event_id)

        try:
            return (await session.scalars(query)).one()
        except sqlalchemy.orm.exc.NoResultFound:
            raise NotFound(f"No event found with id: {event_id}")

    async def _apply_event_query(
        self,
        model: Type[EventModel],
        query: Select[Any],
        request: EventQueryDto,
        session: AsyncSession,
    ) -> Select[Any]:
        if request.organization_id is not None:
            query = query.filter(model.org_id == request.organization_id)
        if request.site_id is not None:
            query = query.filter(model.site_id == request.site_id)
        elif request.site_ids is not None:
            query = query.filter(model.site_id.in_(request.site_ids))
        if request.room_id is not None:
            query = query.filter(model.room_id == request.room_id)
        elif request.room_ids is not None:
            query = query.filter(model.room_id.in_(request.room_ids))
        if request.camera_id is not None:
            query = query.filter(model.camera_id == request.camera_id)
        if request.min_time is not None:
            query = query.filter(model.start_time >= request.min_time)
        if request.max_time is not None:
            query = query.filter(model.start_time <= request.max_time)
        if request.min_start_time is not None:
            query = query.filter(model.start_time >= request.min_start_time)
        if request.max_start_time is not None:
            query = query.filter(model.start_time <= request.max_start_time)
        if request.min_created_time is not None:
            query = query.filter(model.created_time >= request.min_created_time)
        if request.max_created_time is not None:
            query = query.filter(model.created_time <= request.max_created_time)
        if request.source_type is not None:
            query = query.filter(model.source_type == request.source_type)
        elif request.source_types is not None:
            query = query.filter(model.source_type.in_(request.source_types))
        if request.model_version is not None:
            query = query.filter(model.model_version == request.model_version)
        if request.min_confidence is not None:
            query = query.filter(model.confidence >= request.min_confidence)
        if request.event_type is not None:
            types = await self.get_event_types(types=[request.event_type], session=session)
            query = query.filter(model.event_type_id.in_([type.id for type in types]))
        if request.event_names is not None and len(request.event_names) > 0:
            query = query.filter(
                model.event_type_id.in_(request.event_names),
            )
        if request.labels is not None and len(request.labels) > 0:
            query = query.filter(model.labels.overlap(cast(request.labels, ARRAY(String))))
        if request.exclude_event_names:
            query = query.filter(model.event_type_id.notin_(request.exclude_event_names))

        if not request.include_deleted:
            query = query.filter(model.deleted_at.is_(None))
        if isinstance(request, EventHistoryQueryDto):
            request_casted: EventHistoryQueryDto = request
            if request_casted.event_ids is not None:
                query = query.filter(model.id.in_(request_casted.event_ids))
        if request.notes is not None and len(request.notes) > 0:
            query = query.filter(model.notes.ilike(f"%{request.notes}%"))

        if request.include_dashboard_events_only:
            query = query.join(
                EventDashboardVisibilityModel,
                model.event_type_id == EventDashboardVisibilityModel.event_type_id,
            ).filter(
                or_(
                    EventDashboardVisibilityModel.org_id_filter.contains([request.organization_id]),
                    EventDashboardVisibilityModel.org_id_filter.is_(None),
                )
            )

        return query

    @async_db_session_provider
    async def query_events(self, request: EventQueryDto, session: AsyncSession) -> List[EventModel]:
        query = select(EventModel).order_by(EventModel.start_time)
        query = await self._apply_event_query(EventModel, query, request, session)
        return list((await session.scalars(query)).all())

    @async_db_session_provider
    async def query_event_history(
        self, request: EventHistoryQueryDto, session: AsyncSession
    ) -> List[EventModel]:
        history_class = getattr(EventModel, "__history_mapper__").class_
        query = select(history_class).order_by(history_class.start_time)
        query = await self._apply_event_query(history_class, query, request, session)

        # The query essentially returns List[EventHistoryModel], except we want to return
        # a List[EventModel], because graphql and other callers use the methods like `name()`,
        # `attrs()`, etc, that are part of EventModel, but are not part of the programatically
        # created EventHistoryModel.  So, to convert the event histories to event models,
        # we could have a method with each field copied over, or...
        result = []
        for event_history in (await session.scalars(query)).all():
            # For each history row, we create a new EventModel instance
            event = EventModel()
            # And use dict to copy all of the properties except for `_sa_instance_state`
            _sa_instance_state = event.__dict__["_sa_instance_state"]
            event.__dict__ = event_history.__dict__
            event.__dict__["_sa_instance_state"] = _sa_instance_state
            result.append(event)
        return result

    @async_db_session_provider
    async def delete_event(
        self,
        event_id: str,
        source: Optional[str],
        source_type: Optional[str],
        session: AsyncSession,
    ) -> EventModel:
        event = await self.__get_event(session=session, event_id=event_id)
        if source is not None:
            event.source = source
        if source_type is not None:
            event.source_type = source_type

        event.deleted_at = datetime.datetime.now()
        await session.commit()
        await session.refresh(event)
        return event

    @async_db_session_provider
    async def create_event_type(
        self, event_type: EventTypeModel, session: AsyncSession
    ) -> EventTypeModel:
        try:
            session.add(event_type)
            await session.commit()
            await session.refresh(event_type)
            return event_type
        except sqlalchemy.exc.IntegrityError as e:
            if check_exception(UniqueViolationError, e):
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    f'event type with id "{event_type.id}" already exists',
                )
            raise e

    @async_db_session_provider
    async def update_event_type(
        self, event_type: EventTypeModel, session: AsyncSession
    ) -> EventTypeModel:
        old_event_type: EventTypeModel = await self.__get_event_type(session, event_type.id)
        if event_type.name is not None:
            old_event_type.name = event_type.name
        if event_type.description is not None:
            old_event_type.description = event_type.description
        if event_type.type is not None:
            old_event_type.type = event_type.type
        if event_type.color is not None:
            old_event_type.color = event_type.color
        if event_type.hidden is not None:
            old_event_type.hidden = event_type.hidden

        await session.commit()
        await session.refresh(old_event_type)
        return old_event_type

    @async_db_session_provider
    async def get_event_type(self, event_type_id: str, session: AsyncSession) -> EventTypeModel:
        event_type: EventTypeModel = await self.__get_event_type(session, event_type_id)
        return event_type

    async def __get_event_type(self, session: AsyncSession, event_type_id: str) -> EventTypeModel:
        query = select(EventTypeModel).filter(EventTypeModel.id == event_type_id)

        try:
            return (await session.scalars(query)).one()
        except sqlalchemy.orm.exc.NoResultFound:
            raise NotFound(f"No event type found with id: {event_type_id}")

    @async_db_session_provider
    async def get_event_types(
        self, session: AsyncSession, types: List[str] = [], ids: List[str] = []
    ) -> List[EventTypeModel]:
        query = select(EventTypeModel).order_by(EventTypeModel.type).order_by(EventTypeModel.id)

        if len(types) > 0:
            query = query.where(EventTypeModel.type.in_(types))
        if len(ids) > 0:
            query = query.where(EventTypeModel.id.in_(ids))

        return list((await session.scalars(query)).all())

    @async_db_session_provider
    async def delete_event_type(self, event_type_id: str, session: AsyncSession) -> None:
        await session.execute(delete(EventTypeModel).where(EventTypeModel.id == event_type_id))
        await session.commit()

    @async_db_session_provider
    async def get_event_label_options(self, session: AsyncSession) -> List[EventLabelOptionModel]:
        return list((await session.scalars(select(EventLabelOptionModel))).all())

    @async_db_session_provider
    async def create_event_label_option(
        self, name: str, session: AsyncSession, id: Optional[str] = None
    ) -> EventLabelOptionModel:
        try:
            option = EventLabelOptionModel(id=id, name=name)
            session.add(option)
            await session.commit()
            await session.refresh(option)

            return option
        except sqlalchemy.exc.IntegrityError as e:
            if check_exception(UniqueViolationError, e):
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    f"Label option already exists",
                )
            raise e

    async def __get_event_label_option(
        self, id: str, session: AsyncSession
    ) -> EventLabelOptionModel:
        query = select(EventLabelOptionModel).filter(EventLabelOptionModel.id == id)
        return (await session.scalars(query)).one()

    @async_db_session_provider
    async def update_event_label_option(
        self, id: str, name: str, session: AsyncSession
    ) -> EventLabelOptionModel:
        try:
            option = await self.__get_event_label_option(id, session)
            option.name = name
            await session.commit()
            await session.refresh(option)

            return option
        except sqlalchemy.orm.exc.NoResultFound:
            raise NotFound(f'Label option with id "{id}" not found')
        except sqlalchemy.exc.IntegrityError as e:
            if check_exception(UniqueViolationError, e):
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    f'Label option with name "{name}" already exists',
                )
            raise e

    @async_db_session_provider
    async def delete_event_label_option(self, id: str, session: AsyncSession) -> str:
        try:
            label = await self.__get_event_label_option(id, session)
            await session.delete(label)
            await session.commit()

            return id
        except sqlalchemy.orm.exc.NoResultFound:
            raise NotFound(f'Label option with id "{id}" not found')

    async def query_event_dashboard_visibility(
        self, org_id: Optional[str] = None
    ) -> Sequence[EventDashboardVisibilityModel]:
        async with new_async_session() as session:
            query = select(EventDashboardVisibilityModel)
            if org_id is not None:
                query = query.filter(
                    or_(
                        EventDashboardVisibilityModel.org_id_filter.contains([org_id]),
                        EventDashboardVisibilityModel.org_id_filter.is_(None),
                    )
                )
            return (await session.scalars(query)).all()

    async def upsert_event_dashboard_visibility(
        self, model: EventDashboardVisibilityModel
    ) -> EventDashboardVisibilityModel:
        async with new_async_session() as session:
            new_model_ids = await async_postgres_insert_on_conflict_do_update_helper(
                session=session,
                subject=EventDashboardVisibilityModel,
                values=[model],
                returning=EventDashboardVisibilityModel.event_type_id,
                index_elements=[EventDashboardVisibilityModel.event_type_id],
            )
            await session.commit()

            assert len(new_model_ids) == 1

            return (
                await session.scalars(
                    select(EventDashboardVisibilityModel).filter(
                        EventDashboardVisibilityModel.event_type_id == new_model_ids[0]
                    )
                )
            ).one()

    async def delete_event_dashboard_visibility(self, event_type_id: str) -> None:
        async with new_async_session() as session:
            await session.execute(
                delete(EventDashboardVisibilityModel).where(
                    EventDashboardVisibilityModel.event_type_id == event_type_id
                )
            )
            await session.commit()
