# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

import graphene
from graphql import GraphQLError

import api_server.services.events.graphql.event as event_schema
from apella_cloud_api.exceptions import ClientError
from api_server.graphql.context import GrapheneInfo
from api_server.services.events.event_store import EventModel


class EventCreateInput(graphene.InputObjectType):
    id = graphene.ID(required=True)
    name = graphene.String(required=True)
    start_time = graphene.DateTime(required=True)
    process_time = graphene.DateTime(required=True)

    organization_id = graphene.String(required=True)
    site_id = graphene.String(required=True)
    room_id = graphene.String(required=True)
    camera_id = graphene.String(required=False)

    source = graphene.String(required=True)
    source_type = graphene.String(required=True)
    model_version = graphene.String(required=False)
    confidence = graphene.Float(required=False)

    labels = graphene.List(graphene.NonNull(graphene.String), required=False)
    notes = graphene.String(required=False)


class EventCreate(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(EventCreateInput)

    success: bool = graphene.Boolean(required=True)
    created_event: EventModel = graphene.Field(event_schema.Event)

    @staticmethod
    async def mutate(parent, info: GrapheneInfo, input: EventCreateInput):
        # Copy all the fields over from the input type
        event: EventModel = EventModel()
        event.id = input.id
        event.event_type_id = input.name

        event.start_time = input.start_time
        event.process_timestamp = input.process_time

        event.org_id = input.organization_id
        event.site_id = input.site_id
        event.room_id = input.room_id
        if input.camera_id is not None:
            event.camera_id = input.camera_id

        event.source = input.source
        event.source_type = input.source_type
        event.model_version = input.model_version
        event.confidence = input.confidence

        event.labels = input.labels
        event.notes = input.notes

        try:
            new_event = await info.context.event_service.create_event(event)

            return EventCreate(success=True, created_event=new_event)
        except ClientError as e:
            raise GraphQLError(e.message)
