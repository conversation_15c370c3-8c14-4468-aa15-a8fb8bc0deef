# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

import graphene

from api_server.graphql.context import GrapheneInfo


class EventDeleteInput(graphene.InputObjectType):
    id = graphene.ID(required=True)

    source = graphene.String(required=True)
    source_type = graphene.String(required=True)
    publish_changelog = graphene.Boolean(required=False, default_value=True)


class EventDelete(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(EventDeleteInput)

    success: bool = graphene.Boolean(required=True)

    async def mutate(parent, info: GrapheneInfo, input: EventDeleteInput):
        await info.context.event_service.delete_event(
            event_id=input.id,
            source=input.source,
            source_type=input.source_type,
            publish_changelog=input.publish_changelog,
        )
        return EventDelete(success=True)
