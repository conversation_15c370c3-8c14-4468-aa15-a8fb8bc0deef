from __future__ import annotations

from typing import Any

import graphene

from api_server.graphql.context import GrapheneInfo


class EventDashboardVisibilityDeleteInput(graphene.InputObjectType):
    event_type_id = graphene.ID(required=True)


class EventDashboardVisibilityDelete(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(EventDashboardVisibilityDeleteInput, required=True)

    success: bool = graphene.Boolean(required=True)

    @staticmethod
    async def mutate(
        _: Any, info: GrapheneInfo, input: EventDashboardVisibilityDeleteInput
    ) -> EventDashboardVisibilityDelete:
        await info.context.event_service.delete_event_dashboard_visibility(input.event_type_id)

        return EventDashboardVisibilityDelete(success=True)
