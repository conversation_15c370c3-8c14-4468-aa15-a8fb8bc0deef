from __future__ import annotations

from typing import Any

import graphene

from api_server.graphql.context import GrapheneInfo
from api_server.services.events.event_models import EventDashboardVisibilityModel


class EventDashboardVisibilityUpsertInput(graphene.InputObjectType):
    event_type_id = graphene.String(required=True)
    org_id_filter = graphene.List(graphene.NonNull(graphene.String), required=False)


class EventDashboardVisibilityUpsert(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(EventDashboardVisibilityUpsertInput, required=True)

    success: bool = graphene.Boolean(required=True)

    @staticmethod
    async def mutate(
        _: Any, info: GrapheneInfo, input: EventDashboardVisibilityUpsertInput
    ) -> EventDashboardVisibilityUpsert:
        model = EventDashboardVisibilityModel()
        model.event_type_id = input.event_type_id

        assert input.org_id_filter is None or len(input.org_id_filter) > 0
        model.org_id_filter = input.org_id_filter

        await info.context.event_service.upsert_event_dashboard_visibility(model)

        return EventDashboardVisibilityUpsert(success=True)
