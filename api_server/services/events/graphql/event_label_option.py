import graphene
from uuid import UUID

from api_server.graphql.context import GrapheneInfo
from api_server.services.events.event_store import EventLabelOptionModel


class EventLabelOption(graphene.ObjectType):
    id = graphene.ID(resolver=lambda event_label_option, info: event_label_option.id, required=True)
    name = graphene.String(
        resolver=lambda event_label_option, info: event_label_option.name, required=True
    )


class EventLabelOptionCreateInput(graphene.InputObjectType):
    id = graphene.ID()
    name = graphene.String(required=True)


class EventLabelOptionUpdateInput(graphene.InputObjectType):
    id = graphene.ID(required=True)
    name = graphene.String(required=True)


class EventLabelOptionDeleteInput(graphene.InputObjectType):
    id = graphene.ID(required=True)


class EventLabelOptionCreate(graphene.Mutation):
    class Arguments:
        input = EventLabelOptionCreateInput(required=True)

    event_label_option: EventLabelOptionModel = graphene.Field(EventLabelOption)

    @staticmethod
    async def mutate(
        parent: None, info: GrapheneInfo, input: EventLabelOptionCreateInput
    ) -> "EventLabelOptionCreate":
        event_label_option = await info.context.event_service.create_event_label_option(
            id=input.id, name=input.name
        )
        return EventLabelOptionCreate(event_label_option=event_label_option)


class EventLabelOptionUpdate(graphene.Mutation):
    class Arguments:
        input = EventLabelOptionUpdateInput(required=True)

    event_label_option: EventLabelOptionModel = graphene.Field(EventLabelOption)

    @staticmethod
    async def mutate(
        parent: None, info: GrapheneInfo, input: EventLabelOptionUpdateInput
    ) -> "EventLabelOptionUpdate":
        event_label_option = await info.context.event_service.update_event_label_option(
            id=input.id, name=input.name
        )
        return EventLabelOptionUpdate(event_label_option=event_label_option)


class EventLabelOptionDelete(graphene.Mutation):
    class Arguments:
        input = EventLabelOptionDeleteInput(required=True)

    id: UUID = graphene.ID()

    @staticmethod
    async def mutate(
        parent: None, info: GrapheneInfo, input: EventLabelOptionDeleteInput
    ) -> "EventLabelOptionDelete":
        deleted_id = await info.context.event_service.delete_event_label_option(input.id)
        return EventLabelOptionDelete(id=deleted_id)
