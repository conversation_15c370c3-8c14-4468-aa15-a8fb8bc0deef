from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Sequence

from aiodataloader import DataLoader

from apella_cloud_api.dtos import EventQueryDto
from api_server.services.events.event_service import EventService
from api_server.services.events.event_store import EventModel, EventTypeModel
from api_server.services.utils.loader.base_classes import (
    AbstractDataLoader,
)
from api_server.services.utils.loader.util_functions import (
    async_create_loader_queries,
    sort_loader_results,
)


@dataclass(frozen=True, eq=True)
class FrozenRoomEventsQueryDto:
    organization_id: Optional[str] = None
    site_id: Optional[str] = None
    room_id: Optional[str] = None
    camera_id: Optional[str] = None

    min_time: Optional[datetime] = None
    max_time: Optional[datetime] = None
    min_start_time: Optional[datetime] = None
    max_start_time: Optional[datetime] = None

    source_type: Optional[str] = None
    model_version: Optional[str] = None

    event_names: Optional[tuple[str, ...]] = None
    source_types: Optional[tuple[str, ...]] = None
    event_type: Optional[str] = None
    exclude_event_names: Optional[tuple[str, ...]] = None
    min_confidence: Optional[float] = None
    labels: Optional[tuple[str, ...]] = None
    notes: Optional[str] = None
    include_deleted: Optional[bool] = None
    include_dashboard_events_only: Optional[bool] = None

    @property
    def id(self) -> str:
        return f"{self.room_id}"


class EventAttrsLoader(DataLoader[str, Optional[EventTypeModel]]):
    event_service: EventService

    def __init__(self, event_service: EventService):
        super().__init__()
        self.event_service = event_service

    async def batch_load_fn(self, keys: list[str]) -> list[Optional[EventTypeModel]]:
        event_types = await self.event_service.get_event_types(types=[], ids=keys)
        return sort_loader_results(keys, event_types)


class EventLoader(DataLoader[str, Optional[EventModel]]):
    event_service: EventService

    def __init__(self, event_service: EventService):
        super().__init__()
        self.event_service = event_service

    async def batch_load_fn(self, keys: list[str]) -> list[Optional[EventModel]]:
        # Fetch the events by id
        events = await self.event_service.get_events(event_ids=keys)
        return sort_loader_results(keys, events)


class RoomEventsLoader(AbstractDataLoader[FrozenRoomEventsQueryDto, Sequence[EventModel]]):
    event_service: EventService

    def compare_key_to_model(
        self,
        key: FrozenRoomEventsQueryDto,
        value: EventModel,
    ) -> bool:
        return key.id == f"{value.room_id}"

    def __init__(self, event_service: EventService):
        super().__init__()
        self.event_service = event_service

    async def batch_load_fn(
        self, keys: list[FrozenRoomEventsQueryDto]
    ) -> list[Sequence[EventModel]]:
        result_dict: dict[
            FrozenRoomEventsQueryDto, Sequence[EventModel]
        ] = await async_create_loader_queries(
            keys=keys,
            key_type=FrozenRoomEventsQueryDto,
            query_type=EventQueryDto,
            service_query_fn=self.event_service.query_events,
            filter_fn=self.compare_key_to_model,
            pks={
                "room_id": "room_ids",
            },
        )

        return [result_dict.get(key, []) for key in keys]
