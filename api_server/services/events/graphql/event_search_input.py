from typing import Optional

import graphene

from apella_cloud_api.dtos import Event<PERSON>istoryQueryDto, EventQueryDto
from api_server.services.events.graphql.event_loaders import FrozenRoomEventsQueryDto
from api_server.services.room.room_store import RoomModel


class RoomEventSearchInput(graphene.InputObjectType):
    min_time = graphene.DateTime()
    max_time = graphene.DateTime()
    min_start_time = graphene.DateTime()
    max_start_time = graphene.DateTime()
    camera_id = graphene.ID()
    source_type = graphene.String()
    source_types = graphene.List(graphene.NonNull(graphene.String))
    model_version = graphene.String()
    min_confidence = graphene.Float()
    event_type = graphene.String()
    event_names = graphene.List(graphene.NonNull(graphene.String))
    exclude_event_names = graphene.List(graphene.NonNull(graphene.String))
    include_dashboard_events_only = graphene.Boolean()
    labels = graphene.List(graphene.NonNull(graphene.String))
    notes = graphene.String()
    include_deleted = graphene.Boolean()

    def fill_query_dto(self, dto: EventQueryDto, room: Optional[RoomModel] = None) -> None:
        dto.organization_id = room.org_id if room is not None else None
        dto.site_id = room.site_id if room is not None else None
        dto.room_id = room.id if room is not None else None
        dto.camera_id = self.camera_id
        dto.min_time = self.min_time
        dto.max_time = self.max_time
        dto.min_start_time = self.min_start_time
        dto.max_start_time = self.max_start_time
        dto.source_type = self.source_type
        dto.source_types = self.source_types
        dto.model_version = self.model_version
        dto.event_type = self.event_type
        dto.event_names = self.event_names
        dto.exclude_event_names = self.exclude_event_names
        dto.min_confidence = self.min_confidence
        dto.labels = self.labels
        dto.notes = self.notes
        dto.include_deleted = self.include_deleted

    def to_query_dto(self, room: Optional[RoomModel] = None) -> EventQueryDto:
        dto = EventQueryDto()
        self.fill_query_dto(dto, room)
        return dto

    def fill_frozen_dto(self, room: Optional[RoomModel] = None) -> FrozenRoomEventsQueryDto:
        return FrozenRoomEventsQueryDto(
            organization_id=room.org_id if room is not None else None,
            site_id=room.site_id if room is not None else None,
            room_id=room.id if room is not None else None,
            camera_id=self.camera_id if self.camera_id is not None else None,
            min_time=self.min_time if self.min_time is not None else None,
            max_time=self.max_time if self.max_time is not None else None,
            min_start_time=self.min_start_time if self.min_start_time is not None else None,
            max_start_time=self.max_start_time if self.max_start_time is not None else None,
            source_type=self.source_type if self.source_type is not None else None,
            source_types=tuple(self.source_types) if self.source_types is not None else None,
            model_version=self.model_version if self.model_version is not None else None,
            event_type=self.event_type if self.event_type is not None else None,
            event_names=tuple(self.event_names) if self.event_names is not None else None,
            exclude_event_names=tuple(self.exclude_event_names)
            if self.exclude_event_names is not None
            else None,
            min_confidence=self.min_confidence if self.min_confidence is not None else None,
            labels=tuple(self.labels) if self.labels is not None else None,
            notes=self.notes if self.notes is not None else None,
            include_deleted=self.include_deleted if self.include_deleted is not None else None,
            include_dashboard_events_only=self.include_dashboard_events_only,
        )

    def to_frozen_dto(self, room: Optional[RoomModel] = None) -> FrozenRoomEventsQueryDto:
        return self.fill_frozen_dto(room)


class EventSearchInput(RoomEventSearchInput):
    organization_id = graphene.ID()
    site_id = graphene.ID()
    site_ids = graphene.List(graphene.ID)
    room_id = graphene.ID()
    room_ids = graphene.List(graphene.ID)

    def fill_query_dto(self, dto: EventQueryDto, room: Optional[RoomModel] = None) -> None:
        dto.organization_id = self.organization_id
        dto.site_id = self.site_id
        dto.site_ids = self.site_ids
        dto.room_id = self.room_id
        dto.room_ids = self.room_ids
        dto.camera_id = self.camera_id
        dto.min_time = self.min_time
        dto.max_time = self.max_time
        dto.min_start_time = self.min_start_time
        dto.max_start_time = self.max_start_time
        dto.source_type = self.source_type
        dto.source_types = self.source_types
        dto.model_version = self.model_version
        dto.event_type = self.event_type
        dto.event_names = self.event_names
        dto.exclude_event_names = self.exclude_event_names
        dto.min_confidence = self.min_confidence
        dto.labels = self.labels
        dto.notes = self.notes
        dto.include_deleted = self.include_deleted

    def to_query_dto(self, room: Optional[RoomModel] = None) -> EventQueryDto:
        dto = EventQueryDto()
        self.fill_query_dto(dto)
        return dto


class EventHistorySearchInput(EventSearchInput):
    event_ids = graphene.List(graphene.ID)

    def to_query_dto(self, room: Optional[RoomModel] = None) -> EventHistoryQueryDto:
        dto = EventHistoryQueryDto()
        self.fill_query_dto(dto)
        dto.event_ids = self.event_ids
        return dto
