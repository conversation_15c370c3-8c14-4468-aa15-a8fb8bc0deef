import graphene

from api_server.graphql.context import GrapheneInfo
from api_server.services.events.event_store import EventTypeModel
from api_server.services.events.graphql.event import EventType


class EventTypeCreateInput(graphene.InputObjectType):
    id = graphene.ID()
    type = graphene.String(required=True)
    name = graphene.String(required=True)
    description = graphene.String()
    color = graphene.String(required=True)
    hidden = graphene.Boolean(required=True)


class EventTypeCreate(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(EventTypeCreateInput, required=True)

    success: bool = graphene.Boolean(required=True)
    created_event_type: EventTypeModel = graphene.Field(EventType, required=True)

    @staticmethod
    async def mutate(
        parent: None, info: GrapheneInfo, input: EventTypeCreateInput
    ) -> "EventTypeCreate":
        event_type = EventTypeModel(
            id=input.id,
            name=input.name,
            type=input.type,
            description=input.description,
            color=input.color,
            hidden=input.hidden,
        )

        new_event_type = await info.context.event_service.create_event_type(event_type)

        return EventTypeCreate(success=True, created_event_type=new_event_type)
