import graphene

from api_server.graphql.context import GrapheneInfo
from api_server.services.events.event_store import EventTypeModel
from api_server.services.events.graphql.event import EventType


class EventTypeUpdateInput(graphene.InputObjectType):
    id = graphene.ID(required=True)
    type = graphene.String()
    name = graphene.String()
    description = graphene.String()
    color = graphene.String()
    hidden = graphene.Boolean()


class EventTypeUpdate(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(EventTypeUpdateInput, required=True)

    success: bool = graphene.Boolean(required=True)
    updated_event_type: EventTypeModel = graphene.Field(EventType, required=True)

    @staticmethod
    async def mutate(
        parent: None, info: GrapheneInfo, input: EventTypeUpdateInput
    ) -> "EventTypeUpdate":
        event_type_update = EventTypeModel(
            id=input.id,
            name=input.name,
            type=input.type,
            description=input.description,
            color=input.color,
            hidden=input.hidden,
        )

        event_type = await info.context.event_service.update_event_type(event_type_update)

        return EventTypeUpdate(success=True, updated_event_type=event_type)
