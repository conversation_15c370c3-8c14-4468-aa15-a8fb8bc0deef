# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs
from http import H<PERSON><PERSON>tat<PERSON>
from typing import List

import graphene

from apella_cloud_api import UNSET_TOKEN
from apella_cloud_api.exceptions import ClientError
from api_server.graphql.context import GrapheneInfo
from api_server.graphql.partial_updates import interpret_field_value
from api_server.services.events.event_models import EventModel, EventWithPublish
from api_server.services.events.graphql.event import Event
from databases.sql.mixins import EventMatchingStatusGraphene, EventMatchingStatus


class EventUpsertInput(graphene.InputObjectType):
    id = graphene.ID(required=False)
    event_type = graphene.String(deprecated=True)
    event_name = graphene.String(required=True)
    start_time = graphene.DateTime(required=True)
    process_timestamp = graphene.DateTime(required=True)

    organization_id = graphene.String(required=True)
    site_id = graphene.String(required=True)
    room_id = graphene.String(required=True)
    camera_id = graphene.String(required=False)
    event_type_id = graphene.String(deprecated=True)

    source = graphene.String(required=True)
    source_type = graphene.String(required=True)
    model_version = graphene.String(required=False)
    confidence = graphene.Float(required=False)

    labels = graphene.List(graphene.NonNull(graphene.String), required=False)
    notes = graphene.String(required=False)

    etag = graphene.String(required=False)
    publish_changelog = graphene.Boolean(default=True)
    event_matching_status = EventMatchingStatusGraphene(required=False)


class EventUpsert(graphene.Mutation):
    class Arguments:
        input = graphene.NonNull(graphene.List(graphene.NonNull(EventUpsertInput)))

    success: bool = graphene.Boolean(required=True)
    created_events: List[EventModel] = graphene.NonNull(graphene.List(graphene.NonNull(Event)))

    @staticmethod
    async def mutate(parent, info: GrapheneInfo, input: List[EventUpsertInput]):
        event_upserts = []
        for event in input:
            if event.id == UNSET_TOKEN:
                raise ClientError(
                    HTTPStatus.BAD_REQUEST,
                    f"Id cannot be unset, tried to unset id for event: {event}",
                )
            event_model = EventModel(
                id=event.id,
                event_type_id=event.event_name,
                start_time=event.start_time,
                process_timestamp=event.process_timestamp,
                org_id=event.organization_id,
                site_id=event.site_id,
                room_id=event.room_id,
                camera_id=interpret_field_value(event.camera_id),
                source=event.source,
                model_version=interpret_field_value(event.model_version),
                source_type=event.source_type,
                confidence=interpret_field_value(event.confidence),
                labels=interpret_field_value(event.labels),
                notes=interpret_field_value(event.notes),
            )

            if event.etag:
                if event.etag == UNSET_TOKEN:
                    raise ClientError(
                        HTTPStatus.BAD_REQUEST,
                        f"Etag cannot be unset, tried to unset etag for event with id: {event.id}",
                    )
                event_model.etag = event.etag
            if event.event_matching_status:
                event_model.event_matching_status = EventMatchingStatus(event.event_matching_status)

            event_upserts.append(EventWithPublish(event_model, event.publish_changelog))

        created_events = await info.context.event_service.upsert_events(event_upserts=event_upserts)
        return EventUpsert(success=True, created_events=created_events)
