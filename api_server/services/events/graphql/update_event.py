# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

import graphene
from graphql import GraphQLError

import api_server.services.events.graphql.event as event_schema
from apella_cloud_api.exceptions import ClientError
from api_server.graphql.context import GrapheneInfo
from api_server.services.events.event_store import EventModel


class EventUpdateInput(graphene.InputObjectType):
    id = graphene.ID(required=True)
    name = graphene.String()

    start_time = graphene.DateTime()

    camera_id = graphene.ID()
    labels = graphene.List(graphene.NonNull(graphene.String))
    notes = graphene.String()

    source = graphene.String(required=True)
    source_type = graphene.String(required=True)

    publish_changelog = graphene.Boolean(default_value=True)


class EventUpdate(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(EventUpdateInput)

    success: bool = graphene.Boolean(required=True)
    updated_event: EventModel = graphene.Field(event_schema.Event)

    @staticmethod
    async def mutate(parent, info: GrapheneInfo, input: EventUpdateInput):
        try:
            updated_event: EventModel = await info.context.event_service.patch_event(
                event_id=input.id,
                event_type_id=input.name,
                start_time=input.start_time,
                labels=input.labels,
                notes=input.notes,
                source=input.source,
                source_type=input.source_type,
                camera_id=input.camera_id,
                publish_changelog=input.publish_changelog,
            )
        except ClientError as e:
            raise GraphQLError(e.message)

        return EventUpdate(success=True, updated_event=updated_event)
