from http import HTTPStatus

from fastapi import APIRouter, Depends
from starlette.requests import Request
from starlette.responses import Response, JSONResponse

from apella_cloud_api.dtos import (
    BatchEventCreateRequestDto,
    EventDto,
    EventQueryDto,
    EventQueryResultDto,
)
from apella_cloud_api.exceptions import ClientError
from api_server.app_service_provider.apella_app_container import app_container
from api_server.services.events.event_service import EventService
from api_server.services.events.event_store import EventModel

event_service: EventService = None  # type: ignore
events_router = APIRouter(prefix="/v1")


def get_event_service() -> EventService:
    return app_container.provide_app_scope_type(EventService)


@events_router.post("/events")
async def create_event_endpoint(
    request: Request,
    event_service: EventService = Depends(get_event_service),
) -> Response:
    request_body = await request.json()
    event_dto = EventDto().from_dict(request_body)
    event = EventModel.from_dto(event_dto)
    result = await event_service.create_event(event)
    return result.to_dto().to_dict()


@events_router.post("/batch/events")
async def create_events_endpoint(
    publish_changelog: bool,
    request: Request,
    event_service: EventService = Depends(get_event_service),
) -> Response:
    request_body = await request.json()

    bulk_events = BatchEventCreateRequestDto().from_dict(request_body)

    if bulk_events is None:
        raise ClientError(HTTPStatus.BAD_REQUEST, message="Failed to parse body")

    assert bulk_events.events is not None
    events = [EventModel.from_dto(event) for event in bulk_events.events]
    result = await event_service.create_events(events, publish_changelog)
    return result.to_dict()


@events_router.get("/events/query")
async def query_events_endpoint(
    request: Request,
    event_service: EventService = Depends(get_event_service),
) -> JSONResponse:
    """
    Order matters here. This endpoint must be defined before the one for /events/{event_id} otherwise FastAPI doesn't
    know how to route.
    event_query_dto: Annotated[EventQueryDto, Query()],
    request: Request,
    """
    query_params = request.query_params
    event_query_dto = EventQueryDto.from_dict(dict(query_params))
    # Need to get this list args parsed in properly
    event_query_dto.event_names = query_params.getlist("event_names")
    event_query_dto.labels = query_params.getlist("labels")
    event_query_dto.exclude_event_names = query_params.getlist("exclude_event_names")
    events = await event_service.query_events(event_query_dto)
    result = EventQueryResultDto()
    result.events = [event.to_dto() for event in events]
    return JSONResponse(content=result.to_dict())


@events_router.get("/events/{event_id}")
async def get_event_endpoint(
    event_id: str, event_service: EventService = Depends(get_event_service)
) -> JSONResponse:
    event = await event_service.get_event(event_id)
    return JSONResponse(content=event.to_dto().to_dict())


@events_router.put("/events/{event_id}")
async def put_event_endpoint(
    event_id: str,
    request: Request,
    event_service: EventService = Depends(get_event_service),
) -> JSONResponse:
    request_body = await request.json()
    event_dto = EventDto().from_dict(request_body)
    event_dto.event_id = event_id
    event = EventModel.from_dto(event_dto)
    result = await event_service.replace_event(event)
    return JSONResponse(
        content=result.to_dto().to_dict(),
    )
