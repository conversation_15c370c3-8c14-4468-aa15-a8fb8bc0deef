from dataclasses import dataclass
from datetime import time
from enum import Enum, auto
from typing import Optional

from api_server.services.day_of_week_schema.dtos import DayOfWeekConfigObject
import graphene


@dataclass
class DayOfWeekConfig:
    start_time: time
    end_time: time


@dataclass
class SiteFirstCaseConfigUpdateDto:
    site_id: str
    sunday: Optional[DayOfWeekConfig] = None
    monday: Optional[DayOfWeekConfig] = None
    tuesday: Optional[DayOfWeekConfig] = None
    wednesday: Optional[DayOfWeekConfig] = None
    thursday: Optional[DayOfWeekConfig] = None
    friday: Optional[DayOfWeekConfig] = None
    saturday: Optional[DayOfWeekConfig] = None


class FirstCaseConfigSource(Enum):
    ROOM = auto()
    SITE = auto()
    DEFAULT = auto()


FirstCaseConfigSourceGraphene = graphene.Enum.from_enum(FirstCaseConfigSource)


class SiteFirstCaseConfigSource(Enum):
    SITE = FirstCaseConfigSource.SITE.value
    DEFAULT = FirstCaseConfigSource.DEFAULT.value


SiteFirstCaseConfigSourceGraphene = graphene.Enum.from_enum(SiteFirstCaseConfigSource)


@dataclass
class RoomFirstCaseConfigDto(DayOfWeekConfigObject):
    id: str  # field exists for backwards compatibility

    room_id: str
    site_id: str
    source: FirstCaseConfigSource


@dataclass
class SiteFirstCaseConfigDto(DayOfWeekConfigObject):
    id: str  # field exists for backwards compatibility

    site_id: str
    source: SiteFirstCaseConfigSource
