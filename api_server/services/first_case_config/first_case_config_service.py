from dataclasses import dataclass
import dataclasses
from datetime import time
from typing import Optional, Sequence, <PERSON><PERSON>
import uuid

from dataclasses_json import DataClassJsonMixin
from api_server.logging.audit import log_calls_to_audit
from api_server.services.first_case_config.dtos import (
    RoomFirstCaseConfigDto,
    SiteFirstCaseConfigDto,
)
from api_server.services.first_case_config.first_case_config_store import (
    FirstCaseConfigStore,
    RoomFirstCaseConfig,
    SiteFirstCaseConfig,
)

from auth.auth import Auth
from auth.auth_decorator import requires_permission_prefix, requires_permissions
from auth.permissions import READ_SITE_PREFIX, WRITE_ANY_ROOM, WRITE_ANY_SITE


@dataclass
class RoomSiteFirstCaseConfigConfigQueryDto(DataClassJsonMixin):
    room_ids: Optional[list[str]]
    query_id: uuid.UUID = dataclasses.field(default_factory=uuid.uuid4)


@dataclass
class SiteFirstCaseConfigConfigQueryDto(DataClassJsonMixin):
    site_ids: Optional[list[str]]
    query_id: uuid.UUID = dataclasses.field(default_factory=uuid.uuid4)


@dataclass
class SiteCapacityConstraintQueryDto(DataClassJsonMixin):
    site_ids: Optional[list[str]]
    query_id: uuid.UUID = dataclasses.field(default_factory=uuid.uuid4)


@dataclass
class FirstCaseConfigService:
    auth: Auth
    first_case_config_store: FirstCaseConfigStore

    @log_calls_to_audit()
    @requires_permission_prefix(READ_SITE_PREFIX)
    async def query_site_first_case_configs(
        self, site_ids: list[str]
    ) -> Sequence[SiteFirstCaseConfigDto]:
        return await self.first_case_config_store.query_site_first_case_configs(site_ids=site_ids)

    @log_calls_to_audit()
    @requires_permission_prefix(READ_SITE_PREFIX)
    async def query_room_first_case_configs(
        self, room_ids: list[str]
    ) -> Sequence[RoomFirstCaseConfigDto]:
        return await self.first_case_config_store.query_room_first_case_configs(room_ids=room_ids)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_SITE)
    async def upsert_site_first_case_config(
        self,
        site_id: str,
        sunday: Optional[Tuple[time, time]] = None,
        monday: Optional[Tuple[time, time]] = None,
        tuesday: Optional[Tuple[time, time]] = None,
        wednesday: Optional[Tuple[time, time]] = None,
        thursday: Optional[Tuple[time, time]] = None,
        friday: Optional[Tuple[time, time]] = None,
        saturday: Optional[Tuple[time, time]] = None,
    ) -> SiteFirstCaseConfig:
        sunday_start_time, sunday_end_time = sunday if sunday else (None, None)
        monday_start_time, monday_end_time = monday if monday else (None, None)
        tuesday_start_time, tuesday_end_time = tuesday if tuesday else (None, None)
        wednesday_start_time, wednesday_end_time = wednesday if wednesday else (None, None)
        thursday_start_time, thursday_end_time = thursday if thursday else (None, None)
        friday_start_time, friday_end_time = friday if friday else (None, None)
        saturday_start_time, saturday_end_time = saturday if saturday else (None, None)

        return await self.first_case_config_store.upsert_site_first_case_config(
            SiteFirstCaseConfig(
                site_id=site_id,
                sunday_start_time=sunday_start_time,
                sunday_end_time=sunday_end_time,
                monday_start_time=monday_start_time,
                monday_end_time=monday_end_time,
                tuesday_start_time=tuesday_start_time,
                tuesday_end_time=tuesday_end_time,
                wednesday_start_time=wednesday_start_time,
                wednesday_end_time=wednesday_end_time,
                thursday_start_time=thursday_start_time,
                thursday_end_time=thursday_end_time,
                friday_start_time=friday_start_time,
                friday_end_time=friday_end_time,
                saturday_start_time=saturday_start_time,
                saturday_end_time=saturday_end_time,
            )
        )

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_ROOM)
    async def upsert_room_first_case_config(
        self,
        room_id: str,
        sunday: Optional[Tuple[time, time]] = None,
        monday: Optional[Tuple[time, time]] = None,
        tuesday: Optional[Tuple[time, time]] = None,
        wednesday: Optional[Tuple[time, time]] = None,
        thursday: Optional[Tuple[time, time]] = None,
        friday: Optional[Tuple[time, time]] = None,
        saturday: Optional[Tuple[time, time]] = None,
    ) -> RoomFirstCaseConfig:
        sunday_start_time, sunday_end_time = sunday if sunday else (None, None)
        monday_start_time, monday_end_time = monday if monday else (None, None)
        tuesday_start_time, tuesday_end_time = tuesday if tuesday else (None, None)
        wednesday_start_time, wednesday_end_time = wednesday if wednesday else (None, None)
        thursday_start_time, thursday_end_time = thursday if thursday else (None, None)
        friday_start_time, friday_end_time = friday if friday else (None, None)
        saturday_start_time, saturday_end_time = saturday if saturday else (None, None)

        return await self.first_case_config_store.upsert_room_first_case_config(
            RoomFirstCaseConfig(
                room_id=room_id,
                sunday_start_time=sunday_start_time,
                sunday_end_time=sunday_end_time,
                monday_start_time=monday_start_time,
                monday_end_time=monday_end_time,
                tuesday_start_time=tuesday_start_time,
                tuesday_end_time=tuesday_end_time,
                wednesday_start_time=wednesday_start_time,
                wednesday_end_time=wednesday_end_time,
                thursday_start_time=thursday_start_time,
                thursday_end_time=thursday_end_time,
                friday_start_time=friday_start_time,
                friday_end_time=friday_end_time,
                saturday_start_time=saturday_start_time,
                saturday_end_time=saturday_end_time,
            )
        )

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_ROOM)
    async def delete_room_first_case_config(self, room_id: str) -> None:
        return await self.first_case_config_store.delete_room_first_case_config(room_id=room_id)
