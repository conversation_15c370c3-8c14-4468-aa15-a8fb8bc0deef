from datetime import time
import uuid
from typing import Optional, Sequence

from apella_cloud_api.exceptions import NotFound
from api_server.services.day_of_week_schema.day_of_week_schema_mixin import DayOfWeekSchemaMixin
from api_server.services.first_case_config.dtos import (
    FirstCaseConfigSource,
    RoomFirstCaseConfigDto,
    SiteFirstCaseConfigDto,
    SiteFirstCaseConfigSource,
)
from api_server.services.room.room_store import RoomModel
from api_server.services.site.site_store import Site
import sqlalchemy
from sqlalchemy import (
    ForeignKey,
    select,
    delete,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship, selectinload
from sqlalchemy.ext.asyncio import AsyncSession

from databases.sql import Base, new_async_session
from databases.sql.mixins import SiteIdMixin, TimestampMixin


FIRST_CASE_HOURS_DEFAULT_START_TIME = time(hour=7)
FIRST_CASE_HOURS_DEFAULT_END_TIME = time(hour=9)


class RoomFirstCaseConfig(Base, TimestampMixin, DayOfWeekSchemaMixin):
    __tablename__ = "room_first_case_configs"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=sqlalchemy.text("gen_random_uuid()"),
        nullable=False,
    )

    room_id: Mapped[str] = mapped_column(
        ForeignKey("rooms.id", ondelete="CASCADE"),
        primary_key=True,
        index=True,
        nullable=False,
        unique=True,
    )
    room: Mapped["RoomModel"] = relationship(back_populates="first_case_config_record")

    @classmethod
    def build_instance(
        cls, room_id: str, start_time: time, end_time: time
    ) -> "RoomFirstCaseConfig":
        """
        Convenience method to build a RoomFirstCaseConfig instance with the same
        start and end times for all days of the week.
        """
        return cls(
            room_id=room_id,
            sunday_start_time=start_time,
            sunday_end_time=end_time,
            monday_start_time=start_time,
            monday_end_time=end_time,
            tuesday_start_time=start_time,
            tuesday_end_time=end_time,
            wednesday_start_time=start_time,
            wednesday_end_time=end_time,
            thursday_start_time=start_time,
            thursday_end_time=end_time,
            friday_start_time=start_time,
            friday_end_time=end_time,
            saturday_start_time=start_time,
            saturday_end_time=end_time,
        )

    def __repr__(self) -> str:
        return f"<RoomFirstCaseConfig(id='{self.id}')>"


class SiteFirstCaseConfig(Base, TimestampMixin, SiteIdMixin, DayOfWeekSchemaMixin):
    __tablename__ = "site_first_case_configs"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=sqlalchemy.text("gen_random_uuid()"),
        nullable=False,
    )

    site_id: Mapped[str] = mapped_column(
        ForeignKey("sites.id", ondelete="CASCADE"),
        primary_key=True,
        index=True,
        nullable=False,
        unique=True,
    )
    site: Mapped["Site"] = relationship(back_populates="first_case_config_record")

    @classmethod
    def default_instance(cls, site_id: str) -> "SiteFirstCaseConfig":
        """
        Returns First case config with default start and end times.
        """
        return cls.build_instance(
            site_id, FIRST_CASE_HOURS_DEFAULT_START_TIME, FIRST_CASE_HOURS_DEFAULT_END_TIME
        )

    @classmethod
    def build_instance(
        cls, site_id: str, start_time: time, end_time: time
    ) -> "SiteFirstCaseConfig":
        """
        Convenience method to build a SiteFirstCaseConfig instance with the same
        start and end times for all days of the week.
        """
        return cls(
            site_id=site_id,
            sunday_start_time=start_time,
            sunday_end_time=end_time,
            monday_start_time=start_time,
            monday_end_time=end_time,
            tuesday_start_time=start_time,
            tuesday_end_time=end_time,
            wednesday_start_time=start_time,
            wednesday_end_time=end_time,
            thursday_start_time=start_time,
            thursday_end_time=end_time,
            friday_start_time=start_time,
            friday_end_time=end_time,
            saturday_start_time=start_time,
            saturday_end_time=end_time,
        )

    def __repr__(self) -> str:
        return f"<SiteFirstCaseConfig(id='{self.id}')>"


class FirstCaseConfigStore:
    async def get_site(self, site_id: str) -> Site:
        async with new_async_session() as session:
            try:
                query = (
                    select(Site)
                    .options(
                        selectinload(Site.first_case_config_record),
                    )
                    .where(Site.id == site_id)
                )

                return (await session.execute(query)).scalar_one()

            except sqlalchemy.orm.exc.NoResultFound:
                raise NotFound(f"No site found with id: {site_id}")

    async def upsert_site_first_case_config(
        self, first_case_config: SiteFirstCaseConfig
    ) -> SiteFirstCaseConfig:
        async with new_async_session() as session:
            return await self._upsert_site_first_case_config(first_case_config, session)

    async def _upsert_site_first_case_config(
        self, first_case_config: SiteFirstCaseConfig, session: AsyncSession
    ) -> SiteFirstCaseConfig:
        existing_first_case_config = (
            await session.scalars(
                select(SiteFirstCaseConfig).filter(
                    SiteFirstCaseConfig.site_id == first_case_config.site_id
                )
            )
        ).one_or_none()

        if existing_first_case_config:
            existing_first_case_config.sunday_start_time = first_case_config.sunday_start_time
            existing_first_case_config.sunday_end_time = first_case_config.sunday_end_time
            existing_first_case_config.monday_start_time = first_case_config.monday_start_time
            existing_first_case_config.monday_end_time = first_case_config.monday_end_time
            existing_first_case_config.tuesday_start_time = first_case_config.tuesday_start_time
            existing_first_case_config.tuesday_end_time = first_case_config.tuesday_end_time
            existing_first_case_config.wednesday_start_time = first_case_config.wednesday_start_time
            existing_first_case_config.wednesday_end_time = first_case_config.wednesday_end_time
            existing_first_case_config.thursday_start_time = first_case_config.thursday_start_time
            existing_first_case_config.thursday_end_time = first_case_config.thursday_end_time
            existing_first_case_config.friday_start_time = first_case_config.friday_start_time
            existing_first_case_config.friday_end_time = first_case_config.friday_end_time
            existing_first_case_config.saturday_start_time = first_case_config.saturday_start_time
            existing_first_case_config.saturday_end_time = first_case_config.saturday_end_time
            await session.commit()
            await session.refresh(existing_first_case_config)
            return existing_first_case_config
        else:
            session.add(first_case_config)
            await session.commit()
            await session.refresh(first_case_config)
            return first_case_config

    async def upsert_room_first_case_config(
        self, first_case_config: RoomFirstCaseConfig
    ) -> RoomFirstCaseConfig:
        async with new_async_session() as session:
            return await self._upsert_room_first_case_config(first_case_config, session)

    async def _upsert_room_first_case_config(
        self, first_case_config: RoomFirstCaseConfig, session: AsyncSession
    ) -> RoomFirstCaseConfig:
        existing_first_case_config = (
            await session.scalars(
                select(RoomFirstCaseConfig).filter(
                    RoomFirstCaseConfig.room_id == first_case_config.room_id
                )
            )
        ).one_or_none()

        if existing_first_case_config:
            existing_first_case_config.sunday_start_time = first_case_config.sunday_start_time
            existing_first_case_config.sunday_end_time = first_case_config.sunday_end_time
            existing_first_case_config.monday_start_time = first_case_config.monday_start_time
            existing_first_case_config.monday_end_time = first_case_config.monday_end_time
            existing_first_case_config.tuesday_start_time = first_case_config.tuesday_start_time
            existing_first_case_config.tuesday_end_time = first_case_config.tuesday_end_time
            existing_first_case_config.wednesday_start_time = first_case_config.wednesday_start_time
            existing_first_case_config.wednesday_end_time = first_case_config.wednesday_end_time
            existing_first_case_config.thursday_start_time = first_case_config.thursday_start_time
            existing_first_case_config.thursday_end_time = first_case_config.thursday_end_time
            existing_first_case_config.friday_start_time = first_case_config.friday_start_time
            existing_first_case_config.friday_end_time = first_case_config.friday_end_time
            existing_first_case_config.saturday_start_time = first_case_config.saturday_start_time
            existing_first_case_config.saturday_end_time = first_case_config.saturday_end_time
            await session.commit()
            await session.refresh(existing_first_case_config)
            return existing_first_case_config
        else:
            session.add(first_case_config)
            await session.commit()
            await session.refresh(first_case_config)
            return first_case_config

    async def query_site_first_case_configs(
        self, site_ids: list[str]
    ) -> Sequence[SiteFirstCaseConfigDto]:
        async with new_async_session() as session:
            site_query = (
                select(Site)
                .options(selectinload(Site.first_case_config_record))
                .where(Site.id.in_(site_ids))
            )

            sites = (await session.scalars(site_query)).all()

            site_config_map = {}
            for site in sites:
                first_case_config = site.first_case_config
                site_config_map[site.id] = SiteFirstCaseConfigDto(
                    id=site.id,
                    site_id=site.id,
                    source=site_first_case_config_source(site),
                    sunday_start_time=first_case_config.sunday_start_time,
                    sunday_end_time=first_case_config.sunday_end_time,
                    monday_start_time=first_case_config.monday_start_time,
                    monday_end_time=first_case_config.monday_end_time,
                    tuesday_start_time=first_case_config.tuesday_start_time,
                    tuesday_end_time=first_case_config.tuesday_end_time,
                    wednesday_start_time=first_case_config.wednesday_start_time,
                    wednesday_end_time=first_case_config.wednesday_end_time,
                    thursday_start_time=first_case_config.thursday_start_time,
                    thursday_end_time=first_case_config.thursday_end_time,
                    friday_start_time=first_case_config.friday_start_time,
                    friday_end_time=first_case_config.friday_end_time,
                    saturday_start_time=first_case_config.saturday_start_time,
                    saturday_end_time=first_case_config.saturday_end_time,
                )

            return [site_config_map[site_id] for site_id in site_ids]

    async def query_room_first_case_config_records(
        self, room_ids: Optional[list[str]]
    ) -> Sequence[RoomFirstCaseConfig]:
        async with new_async_session() as session:
            query = select(RoomFirstCaseConfig)
            if room_ids:
                query = query.filter(RoomFirstCaseConfig.room_id.in_(room_ids))

            return (await session.scalars(query)).all()

    async def query_room_first_case_configs(
        self, room_ids: list[str]
    ) -> Sequence[RoomFirstCaseConfigDto]:
        async with new_async_session() as session:
            room_query = (
                select(RoomModel)
                .options(selectinload(RoomModel.first_case_config_record))
                .options(selectinload(RoomModel.site).selectinload(Site.first_case_config_record))
                .where(RoomModel.id.in_(room_ids))
            )

            rooms = (await session.scalars(room_query)).all()
            room_config_map = {room.id: room.first_case_config for room in rooms}
            for room in rooms:
                first_case_config = room.first_case_config
                room_config_map[room.id] = RoomFirstCaseConfigDto(
                    id=room.id,
                    room_id=room.id,
                    site_id=room.site.id,
                    source=room_first_case_config_source(room),
                    sunday_start_time=first_case_config.sunday_start_time,
                    sunday_end_time=first_case_config.sunday_end_time,
                    monday_start_time=first_case_config.monday_start_time,
                    monday_end_time=first_case_config.monday_end_time,
                    tuesday_start_time=first_case_config.tuesday_start_time,
                    tuesday_end_time=first_case_config.tuesday_end_time,
                    wednesday_start_time=first_case_config.wednesday_start_time,
                    wednesday_end_time=first_case_config.wednesday_end_time,
                    thursday_start_time=first_case_config.thursday_start_time,
                    thursday_end_time=first_case_config.thursday_end_time,
                    friday_start_time=first_case_config.friday_start_time,
                    friday_end_time=first_case_config.friday_end_time,
                    saturday_start_time=first_case_config.saturday_start_time,
                    saturday_end_time=first_case_config.saturday_end_time,
                )
            return [room_config_map[room_id] for room_id in room_ids]

    async def delete_room_first_case_config(self, room_id: str) -> None:
        async with new_async_session() as session:
            await session.execute(
                delete(RoomFirstCaseConfig).where(RoomFirstCaseConfig.room_id == room_id)
            )
            await session.commit()


def room_first_case_config_source(room: RoomModel) -> FirstCaseConfigSource:
    if room.first_case_config_record is not None:
        return FirstCaseConfigSource.ROOM
    elif room.site.first_case_config_record is not None:
        return FirstCaseConfigSource.SITE
    else:
        return FirstCaseConfigSource.DEFAULT


def site_first_case_config_source(site: Site) -> SiteFirstCaseConfigSource:
    if site.first_case_config_record is not None:
        return SiteFirstCaseConfigSource.SITE
    else:
        return SiteFirstCaseConfigSource.DEFAULT
