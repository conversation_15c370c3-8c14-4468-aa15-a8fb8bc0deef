from __future__ import annotations

from typing import Any

import graphene

from graphql import GraphQLError


import api_server.services.room.graphql.room as room_schema
from apella_cloud_api.exceptions import ClientError
from api_server.graphql.context import GrapheneInfo


class RoomFirstCaseConfigDeleteInput(graphene.InputObjectType):
    room_id = graphene.ID(required=True)


class RoomFirstCaseConfigDelete(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(RoomFirstCaseConfigDeleteInput, required=True)

    success = graphene.Boolean()
    room = graphene.Field(lambda: room_schema.Room, required=True)

    @staticmethod
    async def mutate(
        _: Any, info: GrapheneInfo, input: RoomFirstCaseConfigDeleteInput
    ) -> RoomFirstCaseConfigDelete:
        try:
            room_id = input.room_id
            await info.context.first_case_config_service.delete_room_first_case_config(
                room_id=room_id
            )

            room = await info.context.room_service.get_room(room_id)

            return RoomFirstCaseConfigDelete(success=True, room=room)
        except ClientError as e:
            raise GraphQLError(e.message)
