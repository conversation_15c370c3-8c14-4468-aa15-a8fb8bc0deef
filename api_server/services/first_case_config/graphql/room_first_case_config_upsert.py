from __future__ import annotations

from datetime import time
from typing import Any, Optional, <PERSON><PERSON>

import graphene

from graphql import GraphQLError

import api_server.services.first_case_config.graphql.room_first_case_config as first_case_config_schema

import api_server.services.room.graphql.room as room_schema
from apella_cloud_api.exceptions import ClientError
from api_server.graphql.context import GrapheneInfo


class DayOfWeekInput(graphene.InputObjectType):
    start_time = graphene.Time(required=True)
    end_time = graphene.Time(required=True)


class RoomFirstCaseConfigUpsertInput(graphene.InputObjectType):
    room_id = graphene.ID(required=True)

    sunday = DayOfWeekInput(required=False)
    monday = DayOfWeekInput(required=False)
    tuesday = DayOfWeekInput(required=False)
    wednesday = DayOfWeekInput(required=False)
    thursday = DayOfWeekInput(required=False)
    friday = DayOfWeekInput(required=False)
    saturday = DayOfWeekInput(required=False)


class RoomFirstCaseConfigUpsert(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(RoomFirstCaseConfigUpsertInput, required=True)

    success = graphene.Boolean()
    room = graphene.Field(lambda: room_schema.Room, required=True)
    first_case_config = graphene.Field(first_case_config_schema.RoomFirstCaseConfig)

    @staticmethod
    async def mutate(
        _: Any, info: GrapheneInfo, input: RoomFirstCaseConfigUpsertInput
    ) -> RoomFirstCaseConfigUpsert:
        def _process_day_of_week_config(
            day_of_week_input: Optional[DayOfWeekInput],
        ) -> Optional[Tuple[time, time]]:
            if day_of_week_input is None:
                return None

            return day_of_week_input.start_time, day_of_week_input.end_time

        try:
            first_case_config = (
                await info.context.first_case_config_service.upsert_room_first_case_config(
                    room_id=input.room_id,
                    monday=_process_day_of_week_config(input.monday),
                    tuesday=_process_day_of_week_config(input.tuesday),
                    wednesday=_process_day_of_week_config(input.wednesday),
                    thursday=_process_day_of_week_config(input.thursday),
                    friday=_process_day_of_week_config(input.friday),
                    saturday=_process_day_of_week_config(input.saturday),
                    sunday=_process_day_of_week_config(input.sunday),
                )
            )

            room = await info.context.room_service.get_room(input.room_id)

            return RoomFirstCaseConfigUpsert(
                success=True, first_case_config=first_case_config, room=room
            )
        except ClientError as e:
            raise GraphQLError(e.message)
