from __future__ import annotations

from datetime import time
from typing import Any, Optional, <PERSON><PERSON>

import graphene

from graphql import GraphQLError

import api_server.services.first_case_config.graphql.site_first_case_config as first_case_config_schema

import api_server.services.site.graphql.site as site_schema
from apella_cloud_api.exceptions import ClientError
from api_server.graphql.context import GrapheneInfo


class DayOfWeekInput(graphene.InputObjectType):
    start_time = graphene.Time(required=True)
    end_time = graphene.Time(required=True)


class SiteFirstCaseConfigUpsertInput(graphene.InputObjectType):
    site_id = graphene.ID(required=True)

    sunday = DayOfWeekInput(required=False)
    monday = DayOfWeekInput(required=False)
    tuesday = DayOfWeekInput(required=False)
    wednesday = DayOfWeekInput(required=False)
    thursday = DayOfWeekInput(required=False)
    friday = DayOfWeekInput(required=False)
    saturday = DayOfWeekInput(required=False)


class SiteFirstCaseConfigUpsert(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(SiteFirstCaseConfigUpsertInput, required=True)

    success = graphene.Boolean()
    site = graphene.Field(lambda: site_schema.Site, required=True)
    first_case_config = graphene.Field(first_case_config_schema.SiteFirstCaseConfig)

    @staticmethod
    async def mutate(
        _: Any, info: GrapheneInfo, input: SiteFirstCaseConfigUpsertInput
    ) -> SiteFirstCaseConfigUpsert:
        def _process_day_of_week_config(
            day_of_week_input: Optional[DayOfWeekInput],
        ) -> Optional[Tuple[time, time]]:
            if day_of_week_input is None:
                return None

            return day_of_week_input.start_time, day_of_week_input.end_time

        try:
            first_case_config = (
                await info.context.first_case_config_service.upsert_site_first_case_config(
                    site_id=input.site_id,
                    monday=_process_day_of_week_config(input.monday),
                    tuesday=_process_day_of_week_config(input.tuesday),
                    wednesday=_process_day_of_week_config(input.wednesday),
                    thursday=_process_day_of_week_config(input.thursday),
                    friday=_process_day_of_week_config(input.friday),
                    saturday=_process_day_of_week_config(input.saturday),
                    sunday=_process_day_of_week_config(input.sunday),
                )
            )

            site = await info.context.site_service.get_site(input.site_id)

            return SiteFirstCaseConfigUpsert(
                success=True, first_case_config=first_case_config, site=site
            )
        except ClientError as e:
            raise GraphQLError(e.message)
