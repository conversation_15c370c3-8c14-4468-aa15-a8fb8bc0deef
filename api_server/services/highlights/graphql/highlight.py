from typing import Any, Optional

import graphene
from graphql import GraphQLError

from apella_cloud_api.exceptions import NotFound
from api_server.graphql.sorting.arguments import SortedPaginationConnectionField

import api_server.services.highlights.graphql.highlight_feedback as feedback_schema
import api_server.services.organization.graphql.organization as organization_schema
import api_server.services.room.graphql.room as room_schema
import api_server.services.site.graphql.site as site_schema
import api_server.services.users.graphql.user_type as user_schema
from api_server.graphql import scalar
from api_server.graphql.context import GrapheneInfo
from api_server.graphql.pagination import PaginationConnection
from api_server.services.highlights import highlight_feedback_store, highlight_store
from api_server.services.organization import organization_store
from api_server.services.room import room_store
from api_server.services.site import site_store
from api_server.services.users import user_store


class Highlight(graphene.ObjectType):
    id = graphene.ID(required=True)
    description = graphene.String(required=True)
    organization_id = graphene.String(required=True)
    site_id = graphene.String(required=True)
    room_id = graphene.String(required=True)
    start_time = graphene.DateTime(required=True)
    end_time = graphene.DateTime(required=True)
    category = graphene.String(required=True)
    archived_time = graphene.DateTime()

    feedback = SortedPaginationConnectionField(
        lambda: feedback_schema.HighlightFeedbackConnection, required=True
    )
    my_feedback = graphene.Field(lambda: feedback_schema.HighlightFeedback)
    users = SortedPaginationConnectionField(lambda: user_schema.UserConnection, required=True)

    organization = graphene.Field(lambda: organization_schema.Organization, required=True)
    site = graphene.Field(lambda: site_schema.Site, required=True)
    room = graphene.Field(lambda: room_schema.Room, required=True)
    has_video_available = graphene.Boolean(required=True)
    duration = scalar.Duration()

    @staticmethod
    def resolve_organization_id(highlight: highlight_store.Highlight, info: GrapheneInfo) -> str:
        return highlight.org_id

    @staticmethod
    async def resolve_feedback(
        highlight: highlight_store.Highlight, info: GrapheneInfo, *args: Any, **kwargs: Any
    ) -> list[highlight_feedback_store.HighlightFeedback]:
        return await info.context.highlight_service.query_feedback(
            highlight_id=highlight.id, org_id=highlight.org_id
        )

    @staticmethod
    async def resolve_my_feedback(
        highlight: highlight_store.Highlight, info: GrapheneInfo
    ) -> Optional[highlight_feedback_store.HighlightFeedback]:
        feedback = await info.context.highlight_service.query_feedback(
            org_id=highlight.org_id,
            highlight_id=highlight.id,
            user_id=info.context.auth.get_calling_user_id(),
        )
        return feedback[0] if feedback else None

    @staticmethod
    async def resolve_users(
        highlight: highlight_store.Highlight, info: GrapheneInfo, *args: Any, **kwargs: Any
    ) -> list[user_store.User]:
        highlight_users = await info.context.highlight_service.query_highlight_users(
            highlight_ids=[highlight.id]
        )
        users = []
        for highlight_user in highlight_users:
            try:
                user = await info.context.user_loader.load(highlight_user.user_id)
                assert isinstance(user, user_store.User)
                users.append(user)
            except NotFound:
                pass

        return users

    @staticmethod
    async def resolve_organization(
        highlight: highlight_store.Highlight, info: GrapheneInfo
    ) -> Optional[organization_store.Organization]:
        return await info.context.org_loader.load(highlight.org_id)

    @staticmethod
    async def resolve_site(
        highlight: highlight_store.Highlight, info: GrapheneInfo
    ) -> site_store.Site:
        site: Optional[site_store.Site] = await info.context.site_loader.load(highlight.site_id)
        if site is None:
            raise GraphQLError(
                message=f"Unable to find site for highlight with site id: {highlight.site_id}"
            )
        return site

    @staticmethod
    async def resolve_room(
        highlight: highlight_store.Highlight, info: GrapheneInfo
    ) -> Optional[room_store.RoomModel]:
        return await info.context.room_loader.load(highlight.room_id)

    @staticmethod
    async def resolve_has_video_available(
        highlight: highlight_store.Highlight, info: GrapheneInfo
    ) -> bool:
        if highlight.start_time is not None and highlight.end_time is not None:
            return (
                await info.context.media_asset_service.has_media_asset_hls_playlist_video_available(
                    room_id=highlight.room_id,
                    start_time=highlight.start_time,
                    end_time=highlight.end_time,
                )
            )
        return False


class HighlightConnection(PaginationConnection):
    class Meta:
        node = Highlight

    class HighlightEdge(graphene.ObjectType):
        node = graphene.Field(Highlight, required=True)
        cursor = graphene.String(required=True)

    edges = graphene.List(graphene.NonNull(HighlightEdge), required=True)
