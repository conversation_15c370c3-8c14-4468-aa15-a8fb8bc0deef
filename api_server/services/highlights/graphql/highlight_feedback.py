from datetime import datetime
from typing import Optional

import graphene
from graphql import GraphQLError

import api_server.services.highlights.graphql.highlight as highlight_schema
import api_server.services.highlights.highlight_feedback_store as highlight_feedback_store
import api_server.services.highlights.highlight_store as highlight_store
import api_server.services.users.graphql.user_type as user_schema
import api_server.services.users.user_store as user_store
from apella_cloud_api.exceptions import NotFound
from api_server.graphql.context import GrapheneInfo
from api_server.graphql.pagination import PaginationConnection


class HighlightFeedback(graphene.ObjectType):
    id = graphene.ID(required=True)
    comment = graphene.String()
    rating = graphene.Int()
    submitted_date = graphene.DateTime()

    highlight_id: str = graphene.String()
    highlight = graphene.Field(lambda: highlight_schema.Highlight, required=True)

    user_id: str = graphene.String()
    user = graphene.Field(lambda: user_schema.User, required=False)

    @staticmethod
    def resolve_submitted_date(
        highlight_feedback: highlight_feedback_store.HighlightFeedback, info: GrapheneInfo
    ) -> datetime:
        return highlight_feedback.created_time

    @staticmethod
    async def resolve_highlight(
        parent: highlight_feedback_store.HighlightFeedback, info: GrapheneInfo
    ) -> highlight_store.Highlight:
        highlight: Optional[highlight_store.Highlight] = await info.context.highlight_loader.load(
            parent.highlight_id
        )
        if highlight is None:
            raise GraphQLError(
                message=f"Unable to find highlight for parent {parent.id} with highlight id: {parent.highlight_id}"
            )
        return highlight

    @staticmethod
    async def resolve_user(
        parent: highlight_feedback_store.HighlightFeedback, info: GrapheneInfo
    ) -> Optional[user_store.User]:
        try:
            user = await info.context.user_loader.load(parent.user_id)
            assert isinstance(user, user_store.User)
            return user
        except NotFound:
            return None


class HighlightFeedbackConnection(PaginationConnection):
    class Meta:
        node = HighlightFeedback

    class HighlightFeedbackEdge(graphene.ObjectType):
        node = graphene.Field(HighlightFeedback, required=True)
        cursor = graphene.String(required=True)

    edges = graphene.List(graphene.NonNull(HighlightFeedbackEdge), required=True)
