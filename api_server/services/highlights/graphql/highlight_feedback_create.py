import graphene
from graphql import GraphQLError

import api_server.services.highlights.graphql.highlight_feedback as highlight_feedback_type
from apella_cloud_api.exceptions import ClientError
from api_server.graphql.context import GrapheneInfo
from api_server.services.highlights.highlight_feedback_store import HighlightFeedback


class HighlightFeedbackCreateInput(graphene.InputObjectType):
    id = graphene.ID(required=True)
    comment = graphene.String()
    rating = graphene.Int()
    highlight_id = graphene.UUID(required=True)


class HighlightFeedbackCreate(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(HighlightFeedbackCreateInput)

    success = graphene.Boolean()
    created_highlight_feedback = graphene.Field(highlight_feedback_type.HighlightFeedback)

    @staticmethod
    async def mutate(
        parent: object, info: GrapheneInfo, input: HighlightFeedbackCreateInput
    ) -> "HighlightFeedbackCreate":
        highlight_feedback = HighlightFeedback()
        highlight_feedback.id = input.id
        highlight_feedback.comment = input.comment
        highlight_feedback.rating = input.rating
        highlight_feedback.highlight_id = input.highlight_id
        highlight_feedback.user_id = info.context.auth.get_calling_user_id()

        try:
            highlight = await info.context.highlight_service.get_highlight(
                highlight_id=highlight_feedback.highlight_id
            )
            highlight_feedback.org_id = highlight.org_id

            new_highlight_feedback = await info.context.highlight_service.create_feedback(
                highlight_feedback
            )

            return HighlightFeedbackCreate(
                success=True, created_highlight_feedback=new_highlight_feedback
            )
        except ClientError as e:
            raise GraphQLError(e.message)
