import graphene
from graphql import GraphQLError

import api_server.services.highlights.graphql.highlight_feedback as highlight_feedback_type
from apella_cloud_api.exceptions import ClientError
from api_server.graphql.context import GrapheneInfo
from api_server.services.highlights.highlight_feedback_store import HighlightFeedback


class HighlightFeedbackUpdateInput(graphene.InputObjectType):
    id = graphene.ID(required=True)
    comment = graphene.String()
    rating = graphene.Int()
    highlight_id = graphene.UUID(required=True)


class HighlightFeedbackUpdate(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(HighlightFeedbackUpdateInput)

    success = graphene.Boolean()
    updated_highlight_feedback = graphene.Field(highlight_feedback_type.HighlightFeedback)

    @staticmethod
    async def mutate(
        parent: object, info: GrapheneInfo, input: HighlightFeedbackUpdateInput
    ) -> "HighlightFeedbackUpdate":
        org_id = info.context.auth.get_calling_org_id()

        highlight_feedback = HighlightFeedback()
        highlight_feedback.id = input.id
        highlight_feedback.comment = input.comment
        highlight_feedback.rating = input.rating
        highlight_feedback.highlight_id = input.highlight_id
        highlight_feedback.user_id = info.context.auth.get_calling_user_id()
        highlight_feedback.org_id = str(org_id)

        try:
            new_highlight_feedback = await info.context.highlight_service.replace_feedback(
                highlight_feedback
            )

            return HighlightFeedbackUpdate(
                success=True, updated_highlight_feedback=new_highlight_feedback
            )
        except ClientError as e:
            raise GraphQLError(e.message)
