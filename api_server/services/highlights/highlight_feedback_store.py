import uuid
from datetime import datetime
from http import HTTPStatus
from typing import List, Optional, Union

import sqlalchemy.exc
from sqlalchemy import <PERSON><PERSON><PERSON>, Index, Integer, String, select
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import mapped_column, Mapped
from sqlalchemy.ext.asyncio import AsyncSession

from apella_cloud_api.exceptions import ClientError, NotFound
from databases.sql import Base, new_async_session
from databases.sql.helpers import check_exception
from databases.sql.mixins import OrgIdMixin, TimestampMixin
from asyncpg.exceptions import UniqueViolationError


class HighlightFeedback(Base, TimestampMixin, OrgIdMixin):
    __tablename__ = "highlight_feedback"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4()
    )
    comment: Mapped[str] = mapped_column(String, nullable=True)
    rating: Mapped[int] = mapped_column(Integer, nullable=True)
    highlight_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("highlights.id"), nullable=False
    )
    user_id: Mapped[str] = mapped_column(String, index=True, nullable=False)
    Index(
        "idx_highlight_feedback_highlight_id_user_id",
        highlight_id,
        user_id,
        unique=True,
    )

    def __repr__(self) -> str:
        return f"<HighlightFeedback(id='{self.id}')>"


class HighlightFeedbackStore:
    async def create_feedback(
        self, feedback: HighlightFeedback, session: AsyncSession
    ) -> HighlightFeedback:
        try:
            session.add(feedback)
            await session.commit()
            await session.refresh(feedback)
            return feedback
        except sqlalchemy.exc.IntegrityError as e:
            if check_exception(UniqueViolationError, e):
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    f'feedback id "{feedback.id}" already exists',
                )
            raise e

    async def replace_feedback(self, feedback: HighlightFeedback) -> HighlightFeedback:
        async with new_async_session() as session:
            old_feedback: HighlightFeedback = await self.__get_feedback(session, feedback.id)
            await session.merge(feedback)
            await session.commit()
            await session.refresh(old_feedback)
            return old_feedback

    async def get_feedback(self, id: str) -> HighlightFeedback:
        async with new_async_session() as session:
            return await self.__get_feedback(session, id=id)

    async def get_feedbacks(self, ids: List[uuid.UUID]) -> List[HighlightFeedback]:
        async with new_async_session() as session:
            query = select(HighlightFeedback).filter(HighlightFeedback.id.in_(ids))
            return list((await session.scalars(query)).all())

    async def __get_feedback(
        self, session: AsyncSession, id: Union[str, uuid.UUID]
    ) -> HighlightFeedback:
        query = select(HighlightFeedback).filter(HighlightFeedback.id == id)

        try:
            return (await session.scalars(query)).one()
        except sqlalchemy.orm.exc.NoResultFound:
            raise NotFound(f"No feedback found with id: {id}")

    async def query_feedback(
        self,
        highlight_id: Optional[uuid.UUID] = None,
        user_id: Optional[str] = None,
        min_time: Optional[datetime] = None,
        max_time: Optional[datetime] = None,
        org_id: Optional[str] = None,
    ) -> List[HighlightFeedback]:
        async with new_async_session() as session:
            query = select(HighlightFeedback).order_by(HighlightFeedback.created_time)

            if highlight_id is not None:
                query = query.filter(HighlightFeedback.highlight_id == highlight_id)
            if user_id is not None:
                query = query.filter(HighlightFeedback.user_id == user_id)
            if min_time is not None:
                query = query.filter(HighlightFeedback.updated_time >= min_time)
            if max_time is not None:
                query = query.filter(HighlightFeedback.updated_time <= max_time)
            if org_id is not None:
                query = query.filter(HighlightFeedback.org_id == org_id)

            return list((await session.scalars(query)).all())
