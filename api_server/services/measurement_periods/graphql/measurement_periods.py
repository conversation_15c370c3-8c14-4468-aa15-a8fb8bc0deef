from datetime import date
from typing import Any, Type, Optional

import graphene
from graphql import GraphQLError

import api_server.services.site.graphql.site as site_schema
from api_server.graphql.context import GrapheneInfo
from api_server.graphql.pagination.fields import (
    PaginationConnection,
    pagination_connection_factory,
)
from api_server.services.annotation_tasks.annotation_task_store import AnnotationTaskTypeModel
from api_server.services.annotation_tasks.graphql.annotation_tasks import (
    AnnotationTaskType,
)
from api_server.services.measurement_periods.measurement_period_store import (
    DayOfWeek,
    MeasurementPeriodModel,
)
from api_server.services.site import site_store

DayOfWeekGraphene = graphene.Enum.from_enum(DayOfWeek)


def get_day_of_week_enum(day_of_week: int) -> DayOfWeek:
    # Graphene parses the enum value (usually an int), rather than the enum itself.
    # So we need to convert the value and do some null checking. Graphene 3.0 fixes this.
    # https://github.com/graphql-python/graphene/issues/1151
    return None if day_of_week is None else DayOfWeek(day_of_week)


class MeasurementPeriod(graphene.ObjectType):
    id = graphene.ID(required=True)
    name = graphene.String(required=True)
    measurement_period_start = graphene.Date(required=True)
    measurement_period_end = graphene.Date(required=True)

    site = graphene.Field(lambda: site_schema.Site, required=True)
    annotation_task_type = graphene.Field(lambda: AnnotationTaskType, required=True)

    room_ids = graphene.List(graphene.NonNull(graphene.ID), required=True)
    days_of_week = graphene.List(graphene.NonNull(DayOfWeekGraphene), required=True)
    iso_days_of_week = graphene.List(graphene.NonNull(graphene.Int), required=True)

    @staticmethod
    async def resolve_site(
        measurement_period: MeasurementPeriodModel, info: GrapheneInfo, **kwargs: dict[str, Any]
    ) -> site_store.Site:
        site: Optional[site_store.Site] = await info.context.site_loader.load(
            str(measurement_period.site_id)
        )
        if site is None:
            raise GraphQLError(
                message=f"Unable to find site for measurement_period with site id: {measurement_period.site_id}"
            )
        return site

    @staticmethod
    async def resolve_annotation_task_type(
        measurement_period: MeasurementPeriodModel, info: GrapheneInfo, **kwargs: dict[str, Any]
    ) -> AnnotationTaskTypeModel:
        task: Optional[
            AnnotationTaskTypeModel
        ] = await info.context.annotation_task_type_loader.load(
            str(measurement_period.annotation_task_type_id)
        )
        if task is None:
            raise GraphQLError(
                message=f"Unable to find site for annotation task with id: {measurement_period.annotation_task_type_id}"
            )
        return task

    @staticmethod
    def resolve_measurement_period_start(
        measurement_period: MeasurementPeriodModel, info: GrapheneInfo, **kwargs: dict[str, Any]
    ) -> Optional[date]:
        return measurement_period.measurement_period.lower

    @staticmethod
    def resolve_measurement_period_end(
        measurement_period: MeasurementPeriodModel, info: GrapheneInfo, **kwargs: dict[str, Any]
    ) -> Optional[date]:
        return measurement_period.measurement_period.upper

    @staticmethod
    def resolve_iso_days_of_week(
        measurement_period: MeasurementPeriodModel, info: GrapheneInfo, **kwargs: dict[str, Any]
    ) -> list[int]:
        return [day.value for day in measurement_period.days_of_week]


MeasurementPeriodConnection: Type[PaginationConnection] = pagination_connection_factory(
    MeasurementPeriod
)


class MeasurementPeriodQueryInput(graphene.InputObjectType):
    site_id = graphene.String(required=True)
    annotation_task_type_id = graphene.String()

    measurement_period_start = graphene.Date(required=True)
    measurement_period_end = graphene.Date(required=True)

    names = graphene.List(graphene.NonNull(graphene.String))
