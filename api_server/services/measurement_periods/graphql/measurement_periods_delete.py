import graphene

from api_server.graphql.context import GrapheneInfo


class MeasurementPeriodDeleteInput(graphene.InputObjectType):
    id = graphene.ID(required=True)


class MeasurementPeriodDelete(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(MeasurementPeriodDeleteInput)

    success = graphene.Boolean()

    @staticmethod
    async def mutate(
        parent: None, info: GrapheneInfo, input: MeasurementPeriodDeleteInput
    ) -> "MeasurementPeriodDelete":
        await info.context.measurement_period_service.delete_measurement_period(
            id=input.id,
        )
        return MeasurementPeriodDelete(success=True)
