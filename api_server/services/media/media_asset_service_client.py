from __future__ import annotations

import asyncio
import datetime
from dataclasses import dataclass
from enum import Enum
from http import HTTPStatus
from typing import Any, Final, Union, Iterable

import aiohttp

from apella_cloud_api.exceptions import ClientError
from api_server import logging
from api_server.logging.audit import log_calls_to_audit
from api_server.server.middleware.request_claims_provider import get_jwt_token
from api_server.services.camera.camera_service import CameraService
from api_server.services.camera.camera_store import CameraModel
from api_server.services.media.asset import ImageAsset, VideoAsset
from api_server.services.media.hls.hls_playlist_v2 import MediaAssetHlsPlaylist
from api_server.tracing.trace_decorator import traced, async_traced
from auth.auth import Auth, AuthError
from auth.auth_decorator import requires_any_permission, requires_permissions
from auth.permissions import READ_ANY_LIVE_STREAM, READ_ANY_MEDIA_ASSET

_IMAGES_ENDPOINT: Final = "/images/"
_VIDEOS_ENDPOINT: Final = "/videos/"
_PLAYLIST_ENDPOINT: Final = "/playlist/"
_SEGMENT_ENDPOINT: Final = "/segment/"
_VIDEO_EXISTS_ENDPOINT: Final = "/video-exists/"
_DEFAULT_WINDOW_DURATION: Final = datetime.timedelta(minutes=2)
_DEFAULT_SEGMENT_DURATION: Final = datetime.timedelta(seconds=10)


@dataclass
class SegmentId:
    org_id: str
    site_id: str
    room_id: str
    camera_id: str
    timestamp: int


class ContentType(Enum):
    IMAGE = "image/jpeg"
    HLS_SEGMENT = "video/mp2t"


class MediaAssetService:
    auth: Auth
    _camera_service: Final[CameraService]
    _endpoint: Final[str]

    def __init__(
        self,
        auth: Auth,
        endpoint: str,
        camera_service: CameraService,
    ):
        self.auth = auth
        self._camera_service = camera_service
        self._endpoint = endpoint

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_MEDIA_ASSET)
    async def query_assets(
        self,
        organization_id: str,
        site_id: str,
        room_id: str,
        camera_id: str,
        content_type: ContentType,
        min_time: datetime.datetime,
        max_time: datetime.datetime,
    ) -> list[VideoAsset | ImageAsset]:
        endpoint = self._endpoint + MediaAssetService._get_endpoint(content_type)
        jwt_token = get_jwt_token()
        async with aiohttp.ClientSession() as session:
            async with session.get(
                url=endpoint,
                params={
                    "organization_id": organization_id,
                    "site_id": site_id,
                    "room_id": room_id,
                    "camera_id": camera_id,
                    "start_time": min_time.isoformat(),
                    "end_time": max_time.isoformat(),
                },
                headers={"Authorization": f"Bearer {jwt_token}"},
            ) as response:
                response.raise_for_status()
                data = await response.json()
                if content_type == ContentType.HLS_SEGMENT:
                    all_videos: list[dict[str, Any]] = data["videos"]
                    return [VideoAsset.from_dict(video_data) for video_data in all_videos]
                else:
                    all_images: list[dict[str, Any]] = data["images"]
                    return [ImageAsset.from_dict(image_data) for image_data in all_images]

    @async_traced()
    @log_calls_to_audit()
    async def has_media_asset_hls_playlist_video_available(
        self, room_id: str, start_time: datetime.datetime, end_time: datetime.datetime
    ) -> bool:
        """
        Iterate over available cameras for a room and return if there is a valid playlist available.
        """
        cameras: Iterable[CameraModel] = await self._camera_service.get_cameras_in_room(
            room_ids=[room_id]
        )
        start_time = start_time.astimezone(datetime.timezone.utc)
        end_time = end_time.astimezone(datetime.timezone.utc)
        jwt_token = get_jwt_token()

        async def issue_video_exists_request(camera_to_check: CameraModel) -> bool:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url=self._endpoint + _VIDEO_EXISTS_ENDPOINT,
                    params={
                        "organization_id": camera_to_check.org_id,
                        "site_id": camera_to_check.site_id,
                        "room_id": camera_to_check.room_id,
                        "camera_id": camera_to_check.id,
                        "start_time": start_time.isoformat(),
                        "end_time": end_time.isoformat(),
                    },
                    headers={"Authorization": f"Bearer {jwt_token}"},
                ) as response:
                    response.raise_for_status()
                    json_response = await response.json()
                    return json_response.get("video_exists", False)

        video_exists_futures = [issue_video_exists_request(camera) for camera in cameras]
        video_exists_results: list[Union[bool, BaseException]] = await asyncio.gather(
            *video_exists_futures, return_exceptions=True
        )
        return next(
            (result for result in video_exists_results if isinstance(result, bool) and result),
            False,
        )

    @log_calls_to_audit()
    async def get_media_asset_hls_playlist(
        self, camera_id: str, start_time: datetime.datetime, end_time: datetime.datetime
    ) -> MediaAssetHlsPlaylist | None:
        # If the end time requested is more than window duration in the past
        vod_request = (
            end_time.astimezone(tz=datetime.timezone.utc)
            < datetime.datetime.now(tz=datetime.timezone.utc) - _DEFAULT_WINDOW_DURATION
        )

        if self.auth.has_permission(READ_ANY_MEDIA_ASSET):
            return await self._get_media_asset_hls_playlist(
                camera_id=camera_id, start_time=start_time, end_time=end_time
            )
        elif self.auth.has_permission(READ_ANY_LIVE_STREAM) and not vod_request:
            return await self._get_media_asset_sliding_hls_playlist(
                camera_id=camera_id, start_time=start_time, end_time=end_time
            )
        else:
            raise AuthError(
                "not_authorized",
                f"User does not have permission '{READ_ANY_MEDIA_ASSET}' or '{READ_ANY_LIVE_STREAM}'",
                HTTPStatus.UNAUTHORIZED,
            )

    # Broad requirement to be able to read any media asset from the org in question
    # This will need to be more restrictive in the future,
    # but today it's unclear what that restriction should be
    @requires_permissions(READ_ANY_MEDIA_ASSET)
    async def _get_media_asset_hls_playlist(
        self, camera_id: str, start_time: datetime.datetime, end_time: datetime.datetime
    ) -> MediaAssetHlsPlaylist | None:
        # Fetch all hls playlists for the camera that contain the start and end time
        camera = await self._camera_service.get_camera(camera_id)
        return (
            await self._request_media_asset_hls_playlist(
                camera=camera, start_time=start_time, end_time=end_time
            )
            if camera is not None
            else None
        )

    # Broad requirement to be able to read any media asset from the org in question
    # This will need to be more restrictive in the future,
    # but today it's unclear what that restriction should be
    @requires_permissions(READ_ANY_LIVE_STREAM)
    async def _get_media_asset_sliding_hls_playlist(
        self,
        camera_id: str,
        start_time: datetime.datetime,
        end_time: datetime.datetime,
        window_duration: datetime.timedelta = _DEFAULT_WINDOW_DURATION,
    ) -> MediaAssetHlsPlaylist | None:
        # Fetch all hls playlists for the camera that contain the start and end time
        camera = await self._camera_service.get_camera(camera_id)

        truncated_start_time: datetime.datetime = (
            datetime.datetime.now(tz=datetime.timezone.utc) - window_duration
        )

        logging.info(
            f"Requesting truncated playlist start_time: {start_time.isoformat()} truncated_start_time: {truncated_start_time.isoformat()} end_time: {end_time.isoformat()}",
        )

        return (
            await self._request_media_asset_hls_playlist(
                camera=camera,
                start_time=start_time,
                end_time=end_time,
                truncated_start_time=truncated_start_time,
            )
            if camera is not None
            else None
        )

    async def _request_media_asset_hls_playlist(
        self,
        camera: CameraModel,
        start_time: datetime.datetime,
        end_time: datetime.datetime,
        truncated_start_time: datetime.datetime | None = None,
    ) -> MediaAssetHlsPlaylist | None:
        playlist_endpoint = self._endpoint + _PLAYLIST_ENDPOINT
        params = {
            "organization_id": camera.org_id,
            "site_id": camera.site_id,
            "room_id": camera.room_id,
            "camera_id": camera.id,
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
        }
        if truncated_start_time is not None:
            params["truncated_start_time"] = truncated_start_time.isoformat()
        jwt_token = get_jwt_token()
        async with aiohttp.ClientSession() as session:
            async with session.get(
                url=playlist_endpoint,
                params=params,
                headers={"Authorization": f"Bearer {jwt_token}"},
            ) as response:
                if response.status == HTTPStatus.NO_CONTENT:
                    return None
                response.raise_for_status()
                playlist_data = await response.json()
                # Convert playlist data into a format we are expecting.
                return MediaAssetHlsPlaylist.from_dict(playlist_data)

    @log_calls_to_audit()
    @requires_any_permission([READ_ANY_MEDIA_ASSET, READ_ANY_LIVE_STREAM])
    @traced()
    async def get_media_asset_hls_playlist_segment(
        self, segment_id: str, segment_file: str
    ) -> bytes:
        parsed_segment_id = self._parse_segment_id(segment_id=segment_id)

        self.auth.check_resource_matches_auth(parsed_segment_id.org_id, parsed_segment_id.site_id)

        # Restrict READ_ANY_LIVE_STREAM permissions to only be able to pull segments in the last window duration seconds
        if not self.auth.has_permission(READ_ANY_MEDIA_ASSET):
            segment_start_time = datetime.datetime.fromtimestamp(
                parsed_segment_id.timestamp / 1e6, tz=datetime.timezone.utc
            )
            now = datetime.datetime.now(tz=datetime.timezone.utc)
            # If they only have the live stream permission and
            # the end time of the segment is not within the default window duration plus two segment durations
            # then block them from downloading the segment
            if segment_start_time < now - _DEFAULT_WINDOW_DURATION - (
                _DEFAULT_SEGMENT_DURATION * 2
            ):
                raise AuthError(
                    "not_authorized",
                    f"User does not have permission '{READ_ANY_MEDIA_ASSET}' or '{READ_ANY_LIVE_STREAM}'",
                    HTTPStatus.UNAUTHORIZED,
                )

        try:
            file_name = f"{segment_id}/{segment_file}.ts"
            endpoint = self._endpoint + _SEGMENT_ENDPOINT + file_name
            jwt_token = get_jwt_token()
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url=endpoint,
                    headers={"Authorization": f"Bearer {jwt_token}"},
                ) as response:
                    if response.status == HTTPStatus.NOT_FOUND:
                        raise ClientError(
                            HTTPStatus.UNPROCESSABLE_ENTITY,
                            message="No video available for the requested segment",
                        )
                    response.raise_for_status()
                    return await response.read()
        except Exception:
            raise ClientError(
                HTTPStatus.UNPROCESSABLE_ENTITY,
                message="No video available for the requested segment",
            )

    def _parse_segment_id(self, segment_id: str) -> SegmentId:
        try:
            org_id, site_id, room_id, camera_id, timestamp = segment_id.split(".")
        except ValueError:
            raise ClientError(HTTPStatus.UNPROCESSABLE_ENTITY, "Improper segment name")

        org_id, site_id, room_id, camera_id, timestamp = segment_id.split(".")
        return SegmentId(org_id, site_id, room_id, camera_id, int(timestamp))

    @staticmethod
    def _get_endpoint(content_type: ContentType) -> str:
        if content_type == ContentType.IMAGE:
            return _IMAGES_ENDPOINT
        elif content_type == ContentType.HLS_SEGMENT:
            return _VIDEOS_ENDPOINT
        raise ValueError("Unsupported media asset type to query")
