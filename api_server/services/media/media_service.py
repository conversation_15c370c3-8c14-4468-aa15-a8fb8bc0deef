# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

import base64
import datetime
from http import HTTPStatus
from typing import Optional
from uuid import UUID

from apella_cloud_api.exceptions import ClientError
from api_server.logging.audit import log_calls_to_audit
from api_server.services.camera.camera_service import CameraService
from api_server.services.highlights.highlight_service import HighlightService
from api_server.services.media.media_asset_store import MediaAssetStore
from api_server.services.media.models import LatestImage
from auth.auth import Auth, AuthError
from auth.auth_decorator import async_requires_permissions
from auth.permissions import (
    READ_ANY_LIVE_STREAM,
    READ_ANY_MEDIA_ASSET,
    READ_MEDIA_ASSET_IF_ASSIGNED,
    WRITE_ANY_MEDIA_ASSET,
)
from databases.cache.persistent_cache import PersistentCache


class MediaService:
    auth: Auth
    camera_service: CameraService
    media_asset_store: MediaAssetStore
    highlight_service: HighlightService
    persistent_cache: Optional[PersistentCache]
    latest_image_cache_key = "latest-image"

    def __init__(
        self,
        auth: Auth,
        media_asset_store: MediaAssetStore,
        camera_service: CameraService,
        highlight_service: HighlightService,
        persistent_cache: Optional[PersistentCache],
    ):
        self.auth = auth
        self.media_asset_store = media_asset_store
        self.camera_service = camera_service
        self.highlight_service = highlight_service
        self.persistent_cache = persistent_cache

    @async_requires_permissions(WRITE_ANY_MEDIA_ASSET, enforce_universal_user=True)
    async def set_latest_image_for_camera(
        self, camera_id: str, image_uri: str, capture_time: datetime.datetime
    ):
        # Cloud API server now reads directly from the data bucket through the GCS pubsub notifier.
        # See https://linear.app/apella/issue/PS-462
        pass

    @log_calls_to_audit()
    @async_requires_permissions(READ_ANY_LIVE_STREAM)
    async def get_latest_image_for_camera(self, camera_id: str) -> bytes:
        image = await self.__get_latest_image_for_camera(camera_id)

        if image is not None:
            self.auth.check_resource_matches_auth(image.organization_id, image.site_id)

            assert image.image_uri is not None
            image_bytes = self.media_asset_store.download_media_asset_bytes(image.image_uri)

            return image_bytes

        raise ClientError(
            HTTPStatus.NOT_FOUND, message="No image available for the requested camera"
        )

    @log_calls_to_audit()
    @async_requires_permissions(READ_ANY_LIVE_STREAM)
    async def get_image_base_64(self, image_uri: str) -> str:
        image_bytes = self.media_asset_store.download_media_asset_bytes(image_uri)

        base64_encoded_image = base64.b64encode(image_bytes)
        base64_image = base64_encoded_image.decode("utf-8")

        return base64_image

    @log_calls_to_audit()
    @async_requires_permissions(READ_ANY_LIVE_STREAM)
    async def get_latest_image_object_for_camera(self, camera_id: str) -> Optional[LatestImage]:
        return await self.__get_latest_image_for_camera(camera_id=camera_id)

    async def __get_latest_image_for_camera(self, camera_id: str) -> Optional[LatestImage]:
        if self.persistent_cache is not None:
            cache_key = self.__get_latest_image_cache_key(camera_id)
            response = self.persistent_cache.get(key=cache_key)

            if response is not None:
                response = LatestImage.from_json(response)

            return response
        return None

    def __get_latest_image_cache_key(self, camera_id: str):
        return f"{self.latest_image_cache_key}-{camera_id}"

    async def allowed_to_read_media_asset(self, asset_id: Optional[UUID] = None) -> bool:
        if self.auth.has_permission(READ_ANY_MEDIA_ASSET):
            return True
        if self.auth.has_permission(READ_MEDIA_ASSET_IF_ASSIGNED):
            # Get all the highlights associated with that asset id
            assert asset_id is not None
            highlights_with_asset = (
                await self.highlight_service.highlight_store.query_highlight_assets(
                    asset_ids=[asset_id]
                )
            )

            # Extract the ids
            highlight_ids = [
                highlight_asset.highlight_id for highlight_asset in highlights_with_asset
            ]

            # Get all the highlights in that list the user has access to
            highlights_user_has_access_to = (
                await self.highlight_service.highlight_store.query_highlight_users(
                    highlight_ids=highlight_ids, user_ids=[self.auth.get_calling_user_id()]
                )
            )

            # If there are any that the user has access to then allow them to read the media
            # asset
            if len(highlights_user_has_access_to) > 0:
                return True
        return False

    async def requires_permission_to_read_media_asset(self, asset_id=None):
        if not await self.allowed_to_read_media_asset(asset_id=asset_id):
            raise AuthError(
                "not_authorized", "Not authorized to read media asset", HTTPStatus.UNAUTHORIZED
            )
