from prometheus_client import CollectorRegistry, multiprocess, start_http_server


def start_prometheus_server_for_multiprocess_preload(port: int) -> None:
    """
    Start the prometheus server.
    This setup works with gun<PERSON>'s preload_app=True option, and runs
    the server in the master process.
    """
    registry = CollectorRegistry()
    multiprocess.MultiProcessCollector(registry)
    start_http_server(port, registry=registry)
