# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

import graphene

from api_server.graphql.context import GrapheneInfo
from api_server.services.utils.metrics.graphql import (
    MetricBucketFloat,
    MultiMetricBucketFloat,
)


class OccupancyBucketInput(graphene.InputObjectType):
    room_id = graphene.Field(graphene.String)
    min_time = graphene.Field(graphene.DateTime)
    max_time = graphene.Field(graphene.DateTime)


class ObjectMetrics(graphene.ObjectType):
    count_occupancy_per_bucket = graphene.Field(
        graphene.List(graphene.NonNull(MetricBucketFloat)),
        query=graphene.Argument(OccupancyBucketInput, required=True),
        required=False,
    )

    count_occupancy_and_outage_per_bucket = graphene.Field(
        graphene.List(graphene.NonNull(MultiMetricBucketFloat)),
        query=graphene.Argument(OccupancyBucketInput, required=True),
        required=False,
    )

    @staticmethod
    def resolve_count_occupancy_per_bucket(parent, info: GrapheneInfo, query, *args, **kwargs):
        return info.context.objects_service.get_occupancy_buckets(
            room_id=query.room_id,
            min_time=query.min_time,
            max_time=query.max_time,
        )

    @staticmethod
    def resolve_count_occupancy_and_outage_per_bucket(
        parent, info: GrapheneInfo, query, *args, **kwargs
    ):
        return info.context.objects_service.get_occupancy_and_outage_buckets(
            room_id=query.room_id,
            min_time=query.min_time,
            max_time=query.max_time,
        )
