from datetime import datetime, timedelta
from typing import List

from google.cloud.bigquery import QueryJobConfig, ScalarQueryParameter

import config
from api_server.services.utils.metrics import (
    MetricBucket,
    MetricFloat,
    MultiMetricBucketFloat,
)
from databases.big_query import BigQueryClient

TIMEOUT_SECONDS = 5


class ObjectsStore:
    CONFIDENCE_THRESHOLD = 0.5
    EXPECTED_FRAME_INTERVAL_SECONDS = 5
    EXPECTED_CAMERAS_PER_ROOM = 4

    def __init__(self) -> None:
        self.client = BigQueryClient.get_client()

    def get_occupancy_buckets(
        self, room_id: str, min_time: datetime, max_time: datetime
    ) -> List[MetricBucket]:
        # TODO: create variable bucket sizes based on min/max times
        bucket_size = timedelta(minutes=1)
        # This is a format string being provided to sql/bigquery
        # However it doesn't accept user input so it shouldn't be a security issue
        base_query = f"""
            SELECT
                count(*) as count,
                TIMESTAMP_SECONDS(@total_seconds *
                    DIV(UNIX_SECONDS(frame_time), @total_seconds))
                        as bucket,
            FROM `{config.bigquery_objects_table()}`, unnest(object_model_output)
            WHERE
                room_id = @room_id and
                (label_display_name = "scrubbed" OR label_display_name = "unscrubbed") and
                frame_time > @min_time AND
                frame_time < @max_time AND
                confidence >= @confidence_threshold
            group by
                bucket
            order by
                bucket
            """

        job_config = QueryJobConfig(
            query_parameters=[
                ScalarQueryParameter(
                    "total_seconds",
                    "INT64",
                    int(bucket_size.total_seconds()),
                ),
                ScalarQueryParameter(
                    "room_id",
                    "STRING",
                    room_id,
                ),
                ScalarQueryParameter(
                    "min_time",
                    "STRING",
                    min_time.isoformat(),
                ),
                ScalarQueryParameter(
                    "max_time",
                    "STRING",
                    max_time.isoformat(),
                ),
                ScalarQueryParameter(
                    "confidence_threshold",
                    "FLOAT64",
                    self.CONFIDENCE_THRESHOLD,
                ),
            ]
        )

        query_job = self.client.query(base_query, timeout=TIMEOUT_SECONDS, job_config=job_config)
        bq_result = query_job.result()

        return [
            MetricBucket(date=row["bucket"], value=row["count"], bucket_size=bucket_size)
            for row in bq_result
        ]

    def get_occupancy_and_outage_buckets(
        self,
        room_id: str,
        min_time: datetime,
        max_time: datetime,
        bucket_size: timedelta = timedelta(minutes=1),
    ) -> List[MultiMetricBucketFloat]:
        bucket_size_seconds = int(bucket_size.total_seconds())
        expected_frames_per_bucket = self.EXPECTED_CAMERAS_PER_ROOM * int(
            bucket_size_seconds / self.EXPECTED_FRAME_INTERVAL_SECONDS
        )

        # This is a format string being provided to sql/bigquery
        # However it doesn't accept user input so it shouldn't be a security issue
        base_query = f"""
            with potential_buckets as (
                select *
                from unnest(generate_timestamp_array(
                    @min_time,
                    TIMESTAMP(@max_time) - INTERVAL @bucket_size_seconds SECOND,
                    INTERVAL @bucket_size_seconds SECOND
                )) bucket
            ), object_counts as (
                select
                    sum(case when label_display_name = "scrubbed" OR label_display_name = "unscrubbed" then 1 else 0 end) as people_count,
                    sum(case when label_display_name = "unscrubbed" then 1 else 0 end) as unscrubbed_count,
                    sum(case when label_display_name = "scrubbed" then 1 else 0 end) as scrubbed_count,
                    sum(case when label_display_name = "mop_bucket" OR label_display_name = "mop_head" then 1 else 0 end) as mop_count,
                    timestamp_seconds(
                        @bucket_size_seconds *
                        div(unix_seconds(frame_time), @bucket_size_seconds)
                    ) as bucket
                from `{config.bigquery_objects_table()}`, unnest(object_model_output)
                where
                    frame_time >= @min_time and
                    frame_time <= @max_time and
                    room_id = @room_id and
                    (label_display_name = "scrubbed" OR label_display_name = "unscrubbed" OR
                    label_display_name = "mop_bucket" OR
                    label_display_name = "mop_head" ) and
                    confidence >= @confidence_threshold
                group by bucket
            ), frame_counts as (
                select
                    count(*) as frame_count,
                    timestamp_seconds(
                        @bucket_size_seconds *
                        div(unix_seconds(frame_time), @bucket_size_seconds)
                    ) as bucket
                from `{config.bigquery_objects_table()}`
                where
                    frame_time >= @min_time and
                    frame_time <= @max_time and
                    room_id = @room_id
                group by bucket
            )
            select
                bucket,
                cast(ifnull(frame_count, 0) as FLOAT64) / @expected_frames_per_bucket as media_availability,
                cast(ifnull(people_count, 0) as FLOAT64) / ifnull(frame_count, 1) as occupancy,
                cast(ifnull(unscrubbed_count, 0) as FLOAT64) / ifnull(frame_count, 1) as unscrubbed,
                cast(ifnull(scrubbed_count, 0) as FLOAT64) / ifnull(frame_count, 1) as scrubbed,
                cast(ifnull(mop_count, 0) as FLOAT64) / ifnull(frame_count, 1) as mops
            from potential_buckets
            full outer join object_counts using(bucket)
            full outer join frame_counts using(bucket)
            order by bucket
        """

        job_config = QueryJobConfig(
            query_parameters=[
                ScalarQueryParameter(
                    "bucket_size_seconds",
                    "INT64",
                    int(bucket_size.total_seconds()),
                ),
                ScalarQueryParameter(
                    "room_id",
                    "STRING",
                    room_id,
                ),
                ScalarQueryParameter(
                    "min_time",
                    "STRING",
                    min_time.isoformat(),
                ),
                ScalarQueryParameter(
                    "max_time",
                    "STRING",
                    max_time.isoformat(),
                ),
                ScalarQueryParameter(
                    "confidence_threshold",
                    "FLOAT64",
                    self.CONFIDENCE_THRESHOLD,
                ),
                ScalarQueryParameter(
                    "expected_frames_per_bucket",
                    "FLOAT64",
                    expected_frames_per_bucket,
                ),
            ]
        )

        bq_result = self.client.query(
            base_query,
            timeout=TIMEOUT_SECONDS,
            job_config=job_config,
        ).result()

        return [
            MultiMetricBucketFloat(
                date=row["bucket"],
                bucket_size=bucket_size,
                metrics=[
                    MetricFloat(metric="occupancy", value=row["occupancy"]),
                    MetricFloat(metric="unscrubbed", value=row["unscrubbed"]),
                    MetricFloat(metric="scrubbed", value=row["scrubbed"]),
                    MetricFloat(metric="media_availability", value=row["media_availability"]),
                    MetricFloat(metric="mops", value=row["mops"]),
                ],
            )
            for row in bq_result
        ]
