from typing import Optional

from aiodataloader import DataLoader

from api_server.services.observations.observation_service import ObservationService
from api_server.services.observations.observation_store import ObservationTypeModel
from api_server.services.utils.loader.util_functions import sort_loader_results


class ObservationTypeLoader(DataLoader[str, Optional[ObservationTypeModel]]):
    observation_service: ObservationService

    def __init__(self, observation_service: ObservationService):
        super().__init__()
        self.observation_service = observation_service

    async def batch_load_fn(self, keys: list[str]) -> list[Optional[ObservationTypeModel]]:
        # Create a set of the keys to remove duplicates
        op_keys = set(keys)

        # Fetch the observations by the set of keys
        observations_types = await self.observation_service.query_observation_types(
            type_ids=op_keys
        )
        return sort_loader_results(keys, observations_types)
