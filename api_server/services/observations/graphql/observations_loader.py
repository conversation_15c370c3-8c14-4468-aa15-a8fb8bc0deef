from collections import defaultdict
from typing import Optional

from aiodataloader import <PERSON><PERSON>oader

from api_server.services.observations.observation_service import ObservationService
from api_server.services.observations.observation_store import (
    ObservationModel,
    ObservationQuery,
)
from api_server.tracing.trace_decorator import async_traced


class ObservationLoader(
    DataLoader[tuple[str, Optional[frozenset[str]], Optional[str]], list[ObservationModel]]
):
    observation_service: ObservationService

    def __init__(self, observation_service: ObservationService):
        super().__init__()
        self.observation_service = observation_service

    @async_traced("observation_loader.batch_load_fn")
    async def batch_load_fn(
        self, keys: list[tuple[str, Optional[frozenset[str]], Optional[str]]]
    ) -> list[list[ObservationModel]]:
        # Create a set of the keys to remove duplicates
        op_keys = {case_id for case_id, _, _ in keys}

        query = ObservationQuery(case_ids=op_keys)

        # Fetch the observations by the set of keys
        observations = await self.observation_service.query_observations(query)
        observations_by_case = defaultdict(list)
        for observation in observations:
            observations_by_case[observation.case_id].append(observation)

        results_by_key = {}

        for case_id, obx_types, observation_id in keys:
            results_by_key[(case_id, obx_types, observation_id)] = [
                observation
                for observation in observations_by_case[case_id]
                if (obx_types is None or observation.type_id in obx_types)
                and (observation_id is None or str(observation.id) == observation_id)
            ]

        return [results_by_key[key] for key in keys]
