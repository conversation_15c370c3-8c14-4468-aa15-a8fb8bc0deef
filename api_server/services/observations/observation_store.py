import uuid
from dataclasses import dataclass
from datetime import datetime
from http import HTT<PERSON>tatus
from typing import Iterable, List, Optional, Union
import sqlalchemy
import sqlalchemy.exc
from asyncpg import UniqueViolationError
from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    CheckConstraint,
    DateTime,
    ForeignKey,
    String,
    PrimaryKeyConstraint,
    UniqueConstraint,
    select,
    delete,
)
from sqlalchemy.dialects.postgresql import UUID, insert
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import relationship, mapped_column, Mapped

from apella_cloud_api.exceptions import ClientError, NotFound
from api_server.services.case.case_store import Case
from databases.sql import Base, new_async_session
from databases.sql.helpers import check_exception
from databases.sql.mixins import OrgIdMixin, TimestampMixin

DEFAULT_COLOR = "#C0997B"


class ObservationModel(Base, TimestampMixin, OrgIdMixin):
    __tablename__ = "observations"
    __table_args__ = (
        UniqueConstraint(
            "org_id",
            "case_id",
            "type_id",
            "observation_time",
            name="uq_case_observation",
        ),
    )
    id: Mapped[Union[uuid.UUID, str, UUID[str]]] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    case_id: Mapped[str] = mapped_column(
        String, ForeignKey("cases.case_id"), nullable=False, index=True
    )
    type_id: Mapped[str] = mapped_column(
        String, ForeignKey("observation_types.id"), nullable=False, index=True
    )

    observation_type = relationship(
        "ObservationTypeModel", foreign_keys=[type_id], lazy="joined", innerjoin=True
    )

    observation_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, index=True
    )
    # TODO: This should not be nullable as it is an index column. However, we've implemented features that rely on
    # this being allowed to be null.
    recorded_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), index=True, nullable=True
    )

    def __repr__(self) -> str:
        return f"<Observation(id='{self.id}')>"


class ObservationTypeModel(Base, TimestampMixin):
    __tablename__ = "observation_types"
    __table_args__ = (
        # This is a check constraint to make sure that the color is a valid hex color
        CheckConstraint(
            "(color ~ '^#[0-9a-fA-F]{6}$')",
            name="ck_observationtype_color",
        ),
    )
    id: Mapped[str] = mapped_column(String, nullable=False, primary_key=True)
    name: Mapped[str] = mapped_column(String, nullable=False)
    description: Mapped[Union[str, None]] = mapped_column(String, nullable=True)
    color: Mapped[str] = mapped_column(String, nullable=False, server_default=DEFAULT_COLOR)

    def __repr__(self) -> str:
        return f"<ObservationType(id='{self.id}')>"


class ObservationTypeNameModel(Base, TimestampMixin, OrgIdMixin):
    __tablename__ = "observation_type_names"
    __table_args__ = (
        PrimaryKeyConstraint("org_id", "type_id"),
        UniqueConstraint("org_id", "type_id", "name", name="uq_observation_type"),
        # This is a check constraint to make sure that the color is a valid hex color
        CheckConstraint(
            "(color ~ '^#[0-9a-fA-F]{6}$')",
            name="ck_observationtype_color",
        ),
    )
    type_id: Mapped[str] = mapped_column(String, ForeignKey("observation_types.id"), nullable=False)
    name: Mapped[str] = mapped_column(String, nullable=False)
    color: Mapped[str] = mapped_column(String, nullable=False, server_default=DEFAULT_COLOR)
    is_custom_phase_end_point: Mapped[bool] = mapped_column(
        Boolean, nullable=False, server_default="false"
    )

    def __repr__(self) -> str:
        return f"<ObservationTypeName(org_id='{self.org_id}', type_id='{self.type_id}', name='{self.name}')>"


class ObservationStore:
    async def create_observation(
        self, observation: ObservationModel, session: Optional[AsyncSession] = None
    ) -> ObservationModel:
        if session is not None:
            return await self._create_observation(observation, session)
        async with new_async_session() as new_session:
            return await self._create_observation(observation, new_session)

    async def _create_observation(
        self, observation: ObservationModel, session: AsyncSession
    ) -> ObservationModel:
        try:
            session.add(observation)
            await session.commit()
            await session.refresh(observation)
            return observation
        except sqlalchemy.exc.IntegrityError as e:
            if check_exception(UniqueViolationError, e):
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    f'observation id "{observation.id}" already exists',
                )
            raise e

    async def get_observation(self, observation_id: str) -> ObservationModel:
        async with new_async_session() as session:
            try:
                results = await session.scalars(
                    select(ObservationModel).filter(ObservationModel.id == observation_id)
                )

                return results.one()
            except sqlalchemy.orm.exc.NoResultFound:
                raise NotFound(f"No observation found with id: {observation_id}")

    async def get_observations(
        self, observation_ids: List[str], session: AsyncSession
    ) -> List[ObservationModel]:
        try:
            query = (
                select(ObservationModel)
                .filter(ObservationModel.id.in_(observation_ids))
                .order_by(ObservationModel.observation_time)
            )

            results = await session.scalars(query)

            return list(results.all())
        except sqlalchemy.orm.exc.NoResultFound:
            raise NotFound(f"No observation found with id in: {observation_ids}")

    async def upsert_observations(
        self, observations: List[ObservationModel]
    ) -> List[ObservationModel]:
        async with new_async_session() as session:
            # We use Insert statement rather than session.add() to use on_conflict_do_nothing().
            # session.merge() cannot be used because it assumes a stable primary key across updates.
            # It seems sqlalchemy doesn't have an easy way to derive the insert row values either,
            # so we create a dictionary of values.
            row_values = [
                {
                    "org_id": observation.org_id,
                    "case_id": observation.case_id,
                    "type_id": observation.type_id,
                    "observation_time": observation.observation_time,
                    "recorded_time": observation.recorded_time,
                }
                for observation in observations
            ]
            stmt = (
                insert(ObservationModel)
                .values(row_values)
                .on_conflict_do_nothing()
                .returning(ObservationModel.id)
            )
            results = await session.execute(stmt)
            response = results.all()

            ids = [str(observation_id[0]) for observation_id in response]
            await session.commit()

            return await self.get_observations(observation_ids=ids, session=session)

    async def query_observations(
        self,
        org_id: Optional[str] = None,
        site_ids: Optional[List[str]] = None,
        room_ids: Optional[List[str]] = None,
        case_ids: Optional[Iterable[str]] = None,
        type_ids: Optional[List[str]] = None,
        min_observation_time: Optional[datetime] = None,
        max_observation_time: Optional[datetime] = None,
        min_recorded_time: Optional[datetime] = None,
        max_recorded_time: Optional[datetime] = None,
    ) -> List[ObservationModel]:
        query = select(ObservationModel)

        if org_id is not None:
            query = query.filter(ObservationModel.org_id == org_id)
        if site_ids or room_ids:
            query = query.join(Case, Case.case_id == ObservationModel.case_id)
            if site_ids is not None:
                query = query.filter(Case.site_id.in_(site_ids))
            if room_ids is not None:
                query = query.filter(Case.room_id.in_(room_ids))
        if case_ids is not None:
            query = query.filter(ObservationModel.case_id.in_(case_ids))
        if type_ids is not None:
            query = query.filter(ObservationModel.type_id.in_(type_ids))
        if min_observation_time is not None:
            query = query.filter(ObservationModel.observation_time >= min_observation_time)
        if max_observation_time is not None:
            query = query.filter(ObservationModel.observation_time <= max_observation_time)
        if min_recorded_time is not None:
            query = query.filter(ObservationModel.recorded_time >= min_recorded_time)
        if max_recorded_time is not None:
            query = query.filter(ObservationModel.recorded_time <= max_recorded_time)

        query = query.order_by(ObservationModel.observation_time)
        async with new_async_session() as session:
            results = await session.scalars(query)
            return list(results.all())

    @staticmethod
    async def delete_observation(observation_id: str) -> None:
        async with new_async_session() as session:
            stmt = delete(ObservationModel).where(ObservationModel.id == observation_id)
            await session.execute(stmt)
            await session.commit()

    async def query_observation_types(self, type_ids: Iterable[str]) -> List[ObservationTypeModel]:
        async with new_async_session() as session:
            if not type_ids:
                query = select(ObservationTypeModel)
            else:
                query = select(ObservationTypeModel).filter(ObservationTypeModel.id.in_(type_ids))
            results = await session.scalars(query)
            return list(results.all())


class ObservationTypeNameStore:
    async def create_or_update_observation_type_name(
        self,
        observation_type_name: ObservationTypeNameModel,
        session: AsyncSession,
    ) -> ObservationTypeNameModel:
        query = (
            select(ObservationTypeNameModel)
            .filter(ObservationTypeNameModel.type_id == observation_type_name.type_id)
            .filter(ObservationTypeNameModel.org_id == observation_type_name.org_id)
        )
        query_results = (await session.scalars(query)).all()
        if query_results:
            old_observation_type_name: ObservationTypeNameModel = query_results[0]
            old_observation_type_name.name = observation_type_name.name
            old_observation_type_name.color = observation_type_name.color
            old_observation_type_name.is_custom_phase_end_point = (
                observation_type_name.is_custom_phase_end_point
            )
            old_observation_type_name = await session.merge(old_observation_type_name)
            await session.commit()
            return old_observation_type_name
        try:
            session.add(observation_type_name)
            await session.commit()
            await session.refresh(observation_type_name)
            return observation_type_name
        except sqlalchemy.exc.IntegrityError as e:
            if check_exception(UniqueViolationError, e):
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    f'observation type_id "{observation_type_name.type_id}" for org_id "{observation_type_name.org_id}" already exists',
                )
            raise e

    async def get_observation_type_name(
        self, org_id: str, observation_type_id: str
    ) -> ObservationTypeNameModel:
        async with new_async_session() as session:
            try:
                results = await session.scalars(
                    select(ObservationTypeNameModel)
                    .filter(ObservationTypeNameModel.org_id == org_id)
                    .filter(ObservationTypeNameModel.type_id == observation_type_id)
                )
                return results.one()
            except sqlalchemy.orm.exc.NoResultFound:
                raise NotFound(
                    f"No observation type name found with org_id: {org_id} and type id: {observation_type_id}"
                )

    async def get_observation_type_names(
        self, org_id: str, type_ids: list[str]
    ) -> list[ObservationTypeNameModel]:
        async with new_async_session() as session:
            try:
                results = await session.scalars(
                    select(ObservationTypeNameModel)
                    .filter(ObservationTypeNameModel.org_id == org_id)
                    .filter(ObservationTypeNameModel.type_id.in_(type_ids))
                )
                return list(results.all())
            except sqlalchemy.orm.exc.NoResultFound:
                raise NotFound(
                    f"No observation type names found with org_id: {org_id} and type ids: {type_ids}"
                )

    async def get_observation_type_names_for_custom_phases(
        self, org_id: str
    ) -> list[ObservationTypeNameModel]:
        async with new_async_session() as session:
            results = await session.scalars(
                select(ObservationTypeNameModel)
                .filter(ObservationTypeNameModel.org_id == org_id)
                .filter(ObservationTypeNameModel.is_custom_phase_end_point)
            )
            if not results:
                return []
            return list(results.all())


@dataclass
class ObservationQuery:
    org_id: Optional[str] = None
    site_ids: Optional[List[str]] = None
    room_ids: Optional[List[str]] = None
    case_ids: Optional[Iterable[str]] = None
    type_ids: Optional[List[str]] = None
    min_observation_time: Optional[datetime] = None
    max_observation_time: Optional[datetime] = None
    min_recorded_time: Optional[datetime] = None
    max_recorded_time: Optional[datetime] = None
