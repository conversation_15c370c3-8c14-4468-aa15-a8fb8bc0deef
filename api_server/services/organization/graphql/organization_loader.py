from dataclasses import dataclass
from typing import Optional, Sequence

from aiodataloader import DataLoader

from api_server.services.organization.organization_db import Organization
from api_server.services.organization.organization_service import OrganizationService
from api_server.services.site.site_models import SiteQuery
from api_server.services.site.site_service import SiteService
from api_server.services.site.site_store import Site
from api_server.services.utils.loader.base_classes import AbstractDataLoader
from api_server.services.utils.loader.util_functions import (
    sort_loader_results,
    async_create_loader_queries,
)
from api_server.tracing.trace_decorator import async_traced


@dataclass(frozen=True, eq=True)
class FrozenOrganizationSitesDto:
    organization_id: Optional[str]
    site_ids: Optional[tuple[str, ...]]

    @property
    def id(self) -> str:
        return f"{self.organization_id}"


class OrganizationLoader(DataLoader[str, Optional[Organization]]):
    organization_service: OrganizationService

    def __init__(self, organization_service: OrganizationService):
        super().__init__()
        self.organization_service = organization_service

    async def batch_load_fn(self, keys: list[str]) -> list[Optional[Organization]]:
        organizations = await self.organization_service.get_organizations(org_ids=keys)
        sorted_results = sort_loader_results(keys, organizations)

        return sorted_results


class OrganizationSitesLoader(AbstractDataLoader[FrozenOrganizationSitesDto, Sequence[Site]]):
    site_service: SiteService

    def __init__(self, site_service: SiteService):
        super().__init__()
        self.site_service = site_service

    def compare_key_to_model(
        self,
        key: FrozenOrganizationSitesDto,
        value: Site,
    ) -> bool:
        return key.id == value.org_id

    @async_traced("organization_sites_loader.batch_load_fn")
    async def batch_load_fn(self, keys: list[FrozenOrganizationSitesDto]) -> list[Sequence[Site]]:
        results_dict = await async_create_loader_queries(
            keys=keys,
            key_type=FrozenOrganizationSitesDto,
            query_type=SiteQuery,
            service_query_fn=self.site_service.query_sites,
            filter_fn=self.compare_key_to_model,
            pks={"organization_id": "organization_id"},
        )
        return [results_dict.get(key, []) for key in keys]
