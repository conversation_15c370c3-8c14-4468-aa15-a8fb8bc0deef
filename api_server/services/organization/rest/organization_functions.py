from typing import Dict, Any, Final

from api_server.services.organization.organization_service import OrganizationService
from api_server.services.site.site_models import SiteQuery
from api_server.services.site.site_service import SiteService

ORG_ID: Final = "organization_id"
ORG_NAME: Final = "organization_name"
SITES_ARRAY: Final = "sites"


async def get_org_info(
    org_id: str, organization_service: OrganizationService, site_service: SiteService
) -> Dict[str, Any]:
    org = await organization_service.get_organization(org_id)
    site_query = SiteQuery(organization_id=org.id)
    sites = await site_service.query_sites(site_query=site_query)
    site_ids_in_org = [site.id for site in sites]

    result = {ORG_ID: org.id, ORG_NAME: org.name, SITES_ARRAY: site_ids_in_org}
    return result
