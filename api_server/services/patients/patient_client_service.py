import enum
from dataclasses import dataclass
from typing import Final, Dict, Any, Optional

from aiohttp import ClientResponseError

from api_server import logging

import aiohttp
import config

from api_server.logging.audit import log_calls_to_audit
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import READ_ALL_PATIENT


_PATIENTS_PATH: Final = "/patients"
_ORG_PATH: Final = "/org/"
_APPOINTMENT_PATH: Final = "/appointment/"


# https://hl7-definition.caristix.com/v2/HL7v2.8/Tables/0001
class AdministrativeSexType(enum.Enum):
    AMBIGUOUS = "A"
    FEMALE = "F"
    MALE = "M"
    NOT_APPLICABLE = "N"
    OTHER = "O"
    UNKNOWN = "U"


class AdministrativeSex:
    def __init__(self, admin_sex_type: AdministrativeSexType):
        self.type = admin_sex_type.name
        self.text = admin_sex_type.value


@dataclass
class AgeRange:
    min: Optional[int]
    max: Optional[int]


@dataclass
class PersonalInfo:
    first_name_abbreviated: str
    last_name_abbreviated: str
    fullname: Optional[str]
    age: Optional[int]
    age_range: Optional[AgeRange]
    administrative_sex: Optional[AdministrativeSex]


@dataclass
class PatientModel:
    id: str
    personal_info: PersonalInfo


class PatientService:
    auth: Auth

    def __init__(self, auth: Auth, endpoint: Optional[str] = None):
        self.auth = auth
        self.endpoint = endpoint or config.patient_endpoint()

    @requires_permissions(READ_ALL_PATIENT)
    async def get_patients_by_external_case_ids(
        self, org_id: str, external_case_ids: list[str]
    ) -> dict[str, Optional[list[PatientModel]]]:
        if config.use_ehr_scribe_patient_data():
            return await self.query_patients_by_external_case_ids(org_id, external_case_ids)
        else:
            return {external_case_id: None for external_case_id in external_case_ids}

    @log_calls_to_audit()
    @requires_permissions(READ_ALL_PATIENT)
    async def query_patients_by_external_case_ids(
        self, org_id: str, external_case_ids: list[str]
    ) -> dict[str, Optional[list[PatientModel]]]:
        if self.endpoint is None:
            raise ValueError("EHR_SCRIBE_ENDPOINT is not set")

        path = f"{_ORG_PATH}{org_id}{_PATIENTS_PATH}"
        endpoint = f"{self.endpoint}{path}"
        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(
                    url=endpoint,
                    json={"external_appointment_ids": external_case_ids},
                ) as response:
                    response.raise_for_status()
                    result = await response.json()
                    external_case_id_patients: dict[str, Optional[list[PatientModel]]] = {}
                    for appointment in result.keys():
                        if len(result[appointment]) > 0:
                            external_case_id_patients[str(appointment)] = [
                                self._construct_patient_model(patient)
                                for patient in result[appointment]
                                if patient.get("id")
                            ]
                        else:
                            external_case_id_patients[str(appointment)] = None

                    return external_case_id_patients
            except ClientResponseError as e:
                logging.warning(f"Failed to get patients from ehr scribe", exception=e)
                raise e

    def _construct_patient_model(self, response: Dict[str, Any]) -> PatientModel:
        age_range_values = response.get("age_range", [None, None])
        age_range = AgeRange(min=age_range_values[0], max=age_range_values[1])

        administrative_sex_value = response.get("administrative_sex")
        administrative_sex = (
            AdministrativeSex(AdministrativeSexType(administrative_sex_value))
            if administrative_sex_value is not None
            else None
        )

        fullname = f"{response.get('first_name', '')} {response.get('last_name', '')}"

        personal_info = PersonalInfo(
            first_name_abbreviated=response.get("first_name_abbreviated", ""),
            last_name_abbreviated=response.get("last_name_abbreviated", ""),
            fullname=fullname,
            age_range=age_range,
            age=response.get("age", None),
            administrative_sex=administrative_sex,
        )

        return PatientModel(id=response.get("id", None), personal_info=personal_info)
