from enum import Enum, auto

import graphene


class PhaseType(Enum):
    TURNOVER = auto()
    TURNOVER_CLEAN = auto()
    TURNOVER_CLEAN_V2 = auto()
    TURNOVER_CLEANED = auto()
    TURNOVER_OPEN = auto()
    DRAPE_DRAPE_TURNOVER = auto()
    PRE_OPERATIVE = auto()
    POST_OPERATIVE = auto()
    CASE = auto()
    TERMINAL_CLEAN = auto()
    INTRA_OPERATIVE = auto()
    ANESTHESIA_PROCEDURE = auto()
    ANESTHESIA_PREP = auto()


PhaseTypeGraphene = graphene.Enum.from_enum(PhaseType)


def get_phase_type_enum_from_name(status: str) -> PhaseType:
    # Graphene parses the enum value (usually an int), rather than the enum itself.
    # So we need to convert the value and do some null checking. Graphene 3.0 fixes this.
    # https://github.com/graphql-python/graphene/issues/1151
    return PhaseType[status]


def get_phase_type_enum_from_value(status: int) -> PhaseType:
    # <PERSON><PERSON>hen<PERSON> parses the enum value (usually an int), rather than the enum itself.
    # So we need to convert the value and do some null checking. Graphene 3.0 fixes this.
    # https://github.com/graphql-python/graphene/issues/1151
    return PhaseType(status)
