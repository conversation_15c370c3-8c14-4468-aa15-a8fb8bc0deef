from dataclasses import dataclass

from api_server.logging.audit import log_calls_to_audit
from api_server.services.plan.case_note_plan_store import CaseNotePlanModel, CaseNotePlanStore
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import READ_ANY_CASE_NOTE_PLAN, WRITE_ANY_CASE_NOTE_PLAN


@dataclass
class CaseNotePlanService:
    auth: Auth
    case_note_plan_store: CaseNotePlanStore

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE_NOTE_PLAN)
    async def get_case_note_plan_by_ids(self, case_ids: list[str]) -> list[CaseNotePlanModel]:
        return await self.case_note_plan_store.get_case_note_plan_by_ids(case_ids)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_CASE_NOTE_PLAN)
    async def upsert(self, case_note_plan: CaseNotePlanModel) -> CaseNotePlanModel:
        self.auth.check_resource_matches_auth(
            org_id=case_note_plan.org_id, site_id=case_note_plan.site_id
        )
        return await self.case_note_plan_store.upsert(case_note_plan)
