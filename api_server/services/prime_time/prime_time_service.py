from dataclasses import dataclass
import dataclasses
from datetime import time
from http import HTTPStatus
from typing import Optional, Sequence, <PERSON><PERSON>
import uuid

from dataclasses_json import DataClassJsonMixin
from apella_cloud_api.exceptions import ClientError
from api_server.logging.audit import log_calls_to_audit
from api_server.services.prime_time.dtos import (
    RoomPrimeTime,
    SitePrimeTime,
    SitePrimeTimeConfigUpdateDto,
)
from api_server.services.prime_time.prime_time_store import (
    PrimeTimeStore,
    SiteCapacityConstraint,
    SitePrimeTimeConfig,
    RoomPrimeTimeConfig,
)

from api_server.services.site.site_store import Site
from auth.auth import Auth
from auth.auth_decorator import requires_permission_prefix, requires_permissions
from auth.permissions import READ_SITE_PREFIX, WRITE_ANY_SITE


from auth.permissions import READ_ANY_ROOM, WRITE_ANY_ROOM


@dataclass
class RoomPrimeTimeConfigQueryDto(DataClassJsonMixin):
    room_ids: Optional[list[str]]
    query_id: uuid.UUID = dataclasses.field(default_factory=uuid.uuid4)


@dataclass
class SitePrimeTimeConfigQueryDto(DataClassJsonMixin):
    site_ids: Optional[list[str]]
    query_id: uuid.UUID = dataclasses.field(default_factory=uuid.uuid4)


@dataclass
class SiteCapacityConstraintQueryDto(DataClassJsonMixin):
    site_ids: Optional[list[str]]
    query_id: uuid.UUID = dataclasses.field(default_factory=uuid.uuid4)


@dataclass
class PrimeTimeService:
    auth: Auth
    prime_time_store: PrimeTimeStore

    @log_calls_to_audit()
    @requires_permission_prefix(READ_SITE_PREFIX)
    async def query_site_prime_time_config(
        self,
        query: SitePrimeTimeConfigQueryDto,
    ) -> Sequence[SitePrimeTimeConfig]:
        return await self.prime_time_store.query_site_prime_time_configs(site_ids=query.site_ids)

    @log_calls_to_audit()
    @requires_permission_prefix(READ_SITE_PREFIX)
    async def query_prime_time_for_sites(
        self,
        site_ids: list[str],
    ) -> list[SitePrimeTime]:
        return await self.prime_time_store.query_prime_time_for_sites(site_ids=site_ids)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_SITE)
    async def upsert_site_prime_time_config(
        self,
        site_id: str,
        sunday: Optional[Tuple[time, time]] = None,
        monday: Optional[Tuple[time, time]] = None,
        tuesday: Optional[Tuple[time, time]] = None,
        wednesday: Optional[Tuple[time, time]] = None,
        thursday: Optional[Tuple[time, time]] = None,
        friday: Optional[Tuple[time, time]] = None,
        saturday: Optional[Tuple[time, time]] = None,
    ) -> SitePrimeTimeConfig:
        sunday_start_time, sunday_end_time = sunday if sunday else (None, None)
        monday_start_time, monday_end_time = monday if monday else (None, None)
        tuesday_start_time, tuesday_end_time = tuesday if tuesday else (None, None)
        wednesday_start_time, wednesday_end_time = wednesday if wednesday else (None, None)
        thursday_start_time, thursday_end_time = thursday if thursday else (None, None)
        friday_start_time, friday_end_time = friday if friday else (None, None)
        saturday_start_time, saturday_end_time = saturday if saturday else (None, None)

        return await self.prime_time_store.upsert_site_prime_time_config(
            SitePrimeTimeConfig(
                site_id=site_id,
                sunday_start_time=sunday_start_time,
                sunday_end_time=sunday_end_time,
                monday_start_time=monday_start_time,
                monday_end_time=monday_end_time,
                tuesday_start_time=tuesday_start_time,
                tuesday_end_time=tuesday_end_time,
                wednesday_start_time=wednesday_start_time,
                wednesday_end_time=wednesday_end_time,
                thursday_start_time=thursday_start_time,
                thursday_end_time=thursday_end_time,
                friday_start_time=friday_start_time,
                friday_end_time=friday_end_time,
                saturday_start_time=saturday_start_time,
                saturday_end_time=saturday_end_time,
            )
        )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_ROOM)
    async def get_site_with_constraints(self, site_id: str) -> Site:
        return await self.prime_time_store.get_site_with_constraints(site_id=site_id)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_ROOM)
    async def query_prime_time_for_rooms(
        self,
        room_ids: list[str],
    ) -> list[RoomPrimeTime]:
        return await self.prime_time_store.query_prime_time_for_rooms(room_ids=room_ids)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_ROOM)
    async def upsert_room_prime_time_config(
        self,
        room_id: str,
        sunday: Optional[Tuple[time, time]] = None,
        monday: Optional[Tuple[time, time]] = None,
        tuesday: Optional[Tuple[time, time]] = None,
        wednesday: Optional[Tuple[time, time]] = None,
        thursday: Optional[Tuple[time, time]] = None,
        friday: Optional[Tuple[time, time]] = None,
        saturday: Optional[Tuple[time, time]] = None,
    ) -> RoomPrimeTimeConfig:
        sunday_start_time, sunday_end_time = sunday if sunday else (None, None)
        monday_start_time, monday_end_time = monday if monday else (None, None)
        tuesday_start_time, tuesday_end_time = tuesday if tuesday else (None, None)
        wednesday_start_time, wednesday_end_time = wednesday if wednesday else (None, None)
        thursday_start_time, thursday_end_time = thursday if thursday else (None, None)
        friday_start_time, friday_end_time = friday if friday else (None, None)
        saturday_start_time, saturday_end_time = saturday if saturday else (None, None)

        return await self.prime_time_store.upsert_room_prime_time_config(
            RoomPrimeTimeConfig(
                room_id=room_id,
                sunday_start_time=sunday_start_time,
                sunday_end_time=sunday_end_time,
                monday_start_time=monday_start_time,
                monday_end_time=monday_end_time,
                tuesday_start_time=tuesday_start_time,
                tuesday_end_time=tuesday_end_time,
                wednesday_start_time=wednesday_start_time,
                wednesday_end_time=wednesday_end_time,
                thursday_start_time=thursday_start_time,
                thursday_end_time=thursday_end_time,
                friday_start_time=friday_start_time,
                friday_end_time=friday_end_time,
                saturday_start_time=saturday_start_time,
                saturday_end_time=saturday_end_time,
            )
        )

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_ROOM)
    async def delete_room_prime_time_config(self, room_id: str) -> None:
        return await self.prime_time_store.delete_room_prime_time_config(room_id=room_id)

    @log_calls_to_audit()
    @requires_permission_prefix(READ_SITE_PREFIX)
    async def query_site_capacity_constraints(
        self,
        query: SiteCapacityConstraintQueryDto,
    ) -> Sequence[SiteCapacityConstraint]:
        return await self.prime_time_store.query_site_capacity_constraints(site_ids=query.site_ids)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_ROOM)
    async def upsert_site_prime_time_and_capacity_constraints(
        self,
        input: SitePrimeTimeConfigUpdateDto,
    ) -> SitePrimeTimeConfig:
        try:
            return await self.prime_time_store.upsert_site_prime_time_and_capacity_constraints(
                input
            )
        except ValueError as e:
            raise ClientError(HTTPStatus.UNPROCESSABLE_ENTITY, str(e))
