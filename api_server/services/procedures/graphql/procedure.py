# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs
from typing import Type, Optional

import graphene
from graphql import GraphQLError

import api_server.services.organization.graphql.organization as organization_schema
from api_server.graphql.context import GrapheneInfo
from api_server.graphql.pagination.fields import (
    PaginationConnection,
    pagination_connection_factory,
)
from api_server.services.organization.organization_db import Organization
from api_server.services.procedures.procedure_store import ProcedureModel


class Procedure(graphene.ObjectType):
    id = graphene.ID(required=True)
    organization = graphene.Field(lambda: organization_schema.Organization, required=True)
    name = graphene.String(required=True)
    hierarchy = graphene.Int(required=False)

    @staticmethod
    async def resolve_organization(
        procedure: ProcedureModel, info: GrapheneInfo, **kwargs
    ) -> Organization:
        organization: Optional[Organization] = await info.context.org_loader.load(procedure.org_id)
        if organization is None:
            raise GraphQLError(
                message=f"Unable to find organization for procedure with org id: {procedure.org_id}"
            )
        return organization


ProcedureConnection: Type[PaginationConnection] = pagination_connection_factory(Procedure)


class ProcedureQueryInput(graphene.InputObjectType):
    org_id = graphene.String()
    case_id = graphene.String()
    names = graphene.List(graphene.NonNull(graphene.String))
    hierarchy = graphene.Int()
