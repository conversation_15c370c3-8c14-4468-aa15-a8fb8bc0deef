from dataclasses import dataclass
from typing import Iterable, List, Optional

from api_server.logging.audit import log_calls_to_audit
from api_server.services.case.case_procedure_store import CaseProcedureModel
from api_server.services.procedures.procedure_store import (
    CaseProcedureInfoDto,
    CaseProcedureQueryDTO,
    ProcedureModel,
    ProcedureStore,
)
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import READ_ANY_CASE, WRITE_ANY_CASE


@dataclass
class ProcedureService:
    auth: Auth
    procedure_store: ProcedureStore

    @log_calls_to_audit()
    # TODO: Create a procedure-specific permission
    @requires_permissions(READ_ANY_CASE)
    async def get_procedure(self, procedure_id: str) -> ProcedureModel:
        return await self.procedure_store.get_procedure(procedure_id=procedure_id)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE)
    async def get_procedures(self, procedure_ids: List[str]) -> List[ProcedureModel]:
        return await self.procedure_store.get_procedures(procedure_ids=procedure_ids)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE)
    async def query_procedures(
        self,
        names: Optional[List[str]] = None,
        org_id: Optional[str] = None,
        case_id: Optional[str] = None,
        hierarchy: Optional[int] = None,
    ) -> List[ProcedureModel]:
        """
        Query the procedures from the procedures table.
        If the calling user is restricted to only access specific sites, the returned procedures
        are those which have appeared in cases for those sites.
        """
        user_auth_site_ids = self.auth.get_calling_site_ids()
        return await self.procedure_store.query_procedures(
            names=names,
            org_id=org_id,
            case_id=case_id,
            hierarchy=hierarchy,
            site_ids=user_auth_site_ids,
        )

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_CASE, enforce_universal_user=True)
    async def upsert_procedures(self, procedures: List[ProcedureModel]) -> List[ProcedureModel]:
        return await self.procedure_store.upsert_procedures(procedures=procedures)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE)
    async def get_procedures_for_cases(self, case_ids: Iterable[str]) -> list[CaseProcedureInfoDto]:
        return await self.procedure_store.get_procedures_for_cases(case_ids)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_CASE)
    async def get_procedure_relationships_for_cases(
        self, query_dtos: CaseProcedureQueryDTO
    ) -> list[CaseProcedureModel]:
        return await self.procedure_store.get_procedure_relationships_for_cases(query_dtos)
