# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

import uuid
from dataclasses import dataclass
from typing import Iterable, List, Optional

import sqlalchemy
from marshmallow import fields
from sqlalchemy import String, UniqueConstraint, or_, select
from sqlalchemy.dialects.postgresql import UUID, insert
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import mapped_column, Mapped

from apella_cloud_api.dataclass import field
from apella_cloud_api.exceptions import NotFound
from api_server.services.anesthesia.anesthesia_store import AnesthesiaModel
from api_server.services.case.case_procedure_store import CaseProcedureModel
from api_server.services.case.case_store import Case
from databases.sql import Base, new_async_session
from databases.sql.mixins import OrgIdMixin, TimestampMixin


class ProcedureModel(Base, TimestampMixin, OrgIdMixin):
    __tablename__ = "procedures"
    # Procedure names must be unique across organizations. In the future, we should consider
    # storing unique codes (ie CPT) rather than unique names.
    __table_args__ = (UniqueConstraint("org_id", "name", name="uq_orgId_name"),)
    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # The name of this procedure
    name: Mapped[str] = mapped_column(String, nullable=False, index=True)

    def __repr__(self):
        return f"<Procedure(id='{self.id}')>"


@dataclass(frozen=True)
class CaseProcedureInfoDto:
    procedure_model: ProcedureModel
    procedure_id: str
    case_id: str
    hierarchy: Optional[int] = None
    anesthesia_model: Optional[AnesthesiaModel] = None
    anesthesia_id: Optional[str] = None


@dataclass
class CaseProcedureQueryDTO:
    query_id: uuid.UUID
    ids: list[str] = field(marshmallow_field=fields.List(fields.Str()))
    hierarchy: Optional[int] = None


class ProcedureStore:
    async def get_procedure(self, procedure_id: str) -> ProcedureModel:
        try:
            query = select(ProcedureModel).filter(ProcedureModel.id == procedure_id)

            async with new_async_session() as session:
                results = await session.scalars(query)
                return results.one()
        except sqlalchemy.orm.exc.NoResultFound:
            raise NotFound(f"No procedure found with id: {procedure_id}")

    async def get_procedures(self, procedure_ids: List[str]) -> List[ProcedureModel]:
        query = select(ProcedureModel).filter(ProcedureModel.id.in_(procedure_ids))
        async with new_async_session() as session:
            results = await session.scalars(query)
            return list(results.all())

    async def upsert_procedures(
        self, procedures: List[ProcedureModel], session: Optional[AsyncSession] = None
    ) -> List[ProcedureModel]:
        if session is not None:
            return await self._upsert_procedures(procedures, session)
        async with new_async_session() as new_session:
            return await self._upsert_procedures(procedures, new_session)

    async def _upsert_procedures(
        self, procedures: List[ProcedureModel], session: AsyncSession
    ) -> List[ProcedureModel]:
        # We use Insert statement rather than session.add() to use on_conflict_do_nothing().
        # session.merge() cannot be used because it assumes a stable primary key across updates.
        # It seems sqlalchemy doesn't have an easy way to derive the insert row values either,
        # so we create a dictionary of values.
        row_values = [
            {
                "name": procedure.name,
                "org_id": procedure.org_id,
            }
            for procedure in procedures
        ]
        await session.execute(
            insert(ProcedureModel)
            .values(row_values)
            .on_conflict_do_nothing()
            .returning(ProcedureModel.id)
        )

        await session.commit()

        return await self.query_procedures(
            session=session,
            names=[p.name for p in procedures],
            org_id=procedures[0].org_id,
        )

    async def query_procedures(
        self,
        session: Optional[AsyncSession] = None,
        names: Optional[List[str]] = None,
        org_id: Optional[str] = None,
        case_id: Optional[str] = None,
        hierarchy: Optional[int] = None,
        site_ids: Optional[List[str]] = None,
    ) -> List[ProcedureModel]:
        if session is not None:
            return await self._query_procedures(
                session, names, org_id, case_id, hierarchy, site_ids
            )
        async with new_async_session() as new_session:
            return await self._query_procedures(
                new_session, names, org_id, case_id, hierarchy, site_ids
            )

    async def _query_procedures(
        self,
        session: AsyncSession,
        names: Optional[List[str]] = None,
        org_id: Optional[str] = None,
        case_id: Optional[str] = None,
        hierarchy: Optional[int] = None,
        site_ids: Optional[List[str]] = None,
    ) -> List[ProcedureModel]:
        query = select(ProcedureModel).distinct(ProcedureModel.id)

        if names is not None:
            query = query.filter(ProcedureModel.name.in_(names))
        if org_id is not None:
            query = query.filter(ProcedureModel.org_id == org_id)
        if case_id is not None or site_ids is not None or hierarchy is not None:
            query = query.join(
                CaseProcedureModel, CaseProcedureModel.procedure_id == ProcedureModel.id
            ).filter(
                CaseProcedureModel.archived_time.is_(None),
            )

            if hierarchy == 1:
                query = query.filter(
                    or_(
                        CaseProcedureModel.hierarchy == 1,
                        CaseProcedureModel.hierarchy.is_(None),
                    )
                )
            elif hierarchy is not None:
                query = query.filter(CaseProcedureModel.hierarchy == hierarchy)

            if case_id is not None:
                query = query.filter(CaseProcedureModel.case_id == case_id)

            # Restrict procedures to those which have been assigned to cases in the provided sites
            if site_ids is not None:
                query = query.join(Case, Case.case_id == CaseProcedureModel.case_id).filter(
                    Case.site_id.in_(site_ids)
                )

        results = await session.scalars(query)
        return list(results.all())

    async def get_procedures_for_cases(self, case_ids: Iterable[str]) -> list[CaseProcedureInfoDto]:
        query = (
            select(
                ProcedureModel,
                CaseProcedureModel.case_id.label("case_id"),
                CaseProcedureModel.hierarchy.label("hierarchy"),
                AnesthesiaModel,
            )
            .join(CaseProcedureModel, CaseProcedureModel.procedure_id == ProcedureModel.id)
            .outerjoin(AnesthesiaModel, CaseProcedureModel.anesthesia_id == AnesthesiaModel.id)
            .filter(
                CaseProcedureModel.case_id.in_(case_ids),
                CaseProcedureModel.archived_time.is_(None),
            )
        )

        async with new_async_session() as session:
            results = await session.execute(query)
            return [
                CaseProcedureInfoDto(
                    procedure_model=procedure,
                    case_id=case_id,
                    hierarchy=hierarchy,
                    procedure_id=procedure.id,
                    anesthesia_model=anesthesia,
                    anesthesia_id=anesthesia.id if anesthesia else None,
                )
                for procedure, case_id, hierarchy, anesthesia in results.all()
            ]

    async def get_procedure_relationships_for_cases(
        self, query_dto: CaseProcedureQueryDTO
    ) -> list[CaseProcedureModel]:
        query = select(
            CaseProcedureModel,
        ).filter(
            CaseProcedureModel.case_id.in_(query_dto.ids),
            CaseProcedureModel.archived_time.is_(None),
        )
        if query_dto.hierarchy is not None:
            query = query.filter(CaseProcedureModel.hierarchy == query_dto.hierarchy)

        async with new_async_session() as session:
            results = await session.scalars(query)
            return list(results.all())
