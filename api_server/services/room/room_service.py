# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Iterable, List, Optional

import graphene

from api_server.graphql.partial_updates import interpret_field_value
from api_server.logging.audit import log_calls_to_audit
from api_server.services.apella_case.apella_case_store import ApellaCase, CaseType
from api_server.services.room.room_store import RoomModel, RoomStore, RoomTag
from api_server.services.turnover.turnover_service import (
    DEFAULT_MAX_TURNOVER_MINS,
    TurnoverService,
)
from api_server.services.turnover.turnover_models import Turnover
from api_server.tracing.trace_decorator import class_traced
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import READ_ANY_ROOM, WRITE_ANY_ROOM, WRITE_CONFIGURATION_ROOM


class RoomStatusName(Enum):
    IN_CASE = "IN_CASE"
    IDLE = "IDLE"
    CLOSED = "CLOSED"
    TURNOVER = "TURNOVER"


RoomStatusNameGraphene = graphene.Enum.from_enum(RoomStatusName)


@dataclass
class RoomStatus:
    name: RoomStatusName
    since: datetime
    computed_at: datetime
    in_progress_apella_case: Optional[ApellaCase] = None
    in_progress_turnover: Optional[Turnover] = None
    next_case: Optional[ApellaCase] = None


@dataclass
@class_traced()
class RoomService:
    auth: Auth
    room_store: RoomStore
    turnover_service: TurnoverService

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_ROOM, enforce_universal_user=True)
    async def create_room(self, room: RoomModel) -> RoomModel:
        return await self.room_store.create_room(room)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_ROOM, enforce_universal_user=True)
    async def create_rooms(self, rooms: List[RoomModel]) -> List[RoomModel]:
        return await self.room_store.create_rooms(rooms)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_ROOM)
    async def get_room(self, room_id: str) -> RoomModel:
        return await self.room_store.get_room(room_id=room_id)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_ROOM)
    async def get_rooms(self, keys: List[str]) -> List[RoomModel]:
        return await self.room_store.get_rooms(room_ids=keys)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_ROOM)
    async def get_room_ids_in_site(self, site_id: str) -> List[str]:
        rooms_in_site = await self.get_rooms_in_site(site_id=site_id)
        return [room.id for room in rooms_in_site]

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_ROOM)
    async def get_rooms_in_site(self, site_id: str) -> List[RoomModel]:
        return await self.room_store.get_rooms_in_site(site_id=site_id)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_ROOM)
    async def query_rooms(
        self,
        room_ids: Optional[List[str]] = None,
        site_id: Optional[str] = None,
        org_id: Optional[str] = None,
        site_ids: Optional[List[str]] = None,
    ) -> List[RoomModel]:
        if site_ids is None and site_id is not None:
            site_ids = [site_id]
        return await self.room_store.query_rooms(
            room_ids=room_ids, site_ids=site_ids, org_id=org_id
        )

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_ROOM, enforce_universal_user=True)
    async def patch_room(
        self,
        room_id: str,
        room_name: Optional[str],
        room_default_camera_id: Optional[str],
        privacy_enabled: Optional[bool],
        sort_key: Optional[str],
        labels: Optional[dict[str, Any]],
        is_forecasting_enabled: Optional[bool],
    ) -> RoomModel:
        room = await self.room_store.get_room(room_id=room_id)
        # Fields which are None, are not meant to be updated.
        if room_name is not None:
            room.name = room_name
        if room_default_camera_id is not None:
            await room.set_default_camera(interpret_field_value(room_default_camera_id))
        if labels is not None:
            room.labels = labels
        room.sort_key = sort_key or None

        current_privacy_enabled = True if room.privacy_enabled_at is not None else False
        if privacy_enabled is not None and privacy_enabled != current_privacy_enabled:
            room.privacy_enabled_at = datetime.now() if privacy_enabled else None
            room.privacy_updated_by_user_id = self.auth.get_calling_user_id()

        if is_forecasting_enabled is not None:
            room.is_forecasting_enabled = is_forecasting_enabled

        return await self.room_store.replace_room(room)

    @log_calls_to_audit()
    @requires_permissions(WRITE_CONFIGURATION_ROOM)
    async def patch_room_configuration(
        self, room_id: str, privacy_enabled: Optional[bool]
    ) -> RoomModel:
        room = await self.room_store.get_room(room_id=room_id)

        current_privacy_enabled = True if room.privacy_enabled_at is not None else False
        if privacy_enabled is not None and privacy_enabled != current_privacy_enabled:
            room.privacy_enabled_at = datetime.now() if privacy_enabled else None
            room.privacy_updated_by_user_id = self.auth.get_calling_user_id()

        return await self.room_store.replace_room(room)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_ROOM)
    async def create_room_tag(self, room_tag: RoomTag):
        return await self.room_store.create_room_tag(room_tag=room_tag)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_ROOM)
    async def set_room_tags(self, room_id: str, tag_ids: List[str]):
        return await self.room_store.set_room_tags(room_id=room_id, tag_ids=tag_ids)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_ROOM)
    async def rename_room_tag(self, tag_id: str, new_name: str):
        return await self.room_store.rename_room_tag(tag_id=tag_id, new_name=new_name)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_ROOM)
    async def update_room_tag(
        self, tag_id: str, name: Optional[str] = None, color: Optional[str] = None
    ):
        return await self.room_store.update_room_tag(tag_id=tag_id, name=name, color=color)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_ROOM)
    async def query_room_tags(
        self,
        tag_ids: Optional[List[str]] = None,
        org_id: Optional[str] = None,
        room_id: Optional[str] = None,
    ) -> Iterable[RoomTag]:
        return await self.room_store.query_room_tags(
            tag_ids=tag_ids, org_id=org_id, room_id=room_id
        )

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_ROOM)
    async def get_room_tags_for_rooms(self, room_ids: List[str]) -> List[List[RoomTag]]:
        return await self.room_store.get_room_tags_for_rooms(room_ids=room_ids)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_ROOM)
    async def get_room_status(
        self,
        cases: List[ApellaCase],
        computed_at: datetime,
        max_turnover_minutes: Optional[int] = None,
    ) -> RoomStatus:
        most_recent_case = None
        live_case = None
        next_case = None
        for case in cases:
            if case.type == CaseType.LIVE:
                live_case = case
            elif case.type == CaseType.COMPLETE and (
                most_recent_case is None or case.start_time > most_recent_case.start_time
            ):
                most_recent_case = case
            elif case.type == CaseType.FORECAST and (
                next_case is None or case.start_time < next_case.start_time
            ):
                next_case = case

        if live_case:
            return RoomStatus(
                name=RoomStatusName.IN_CASE,
                since=live_case.start_time,
                computed_at=computed_at,
                in_progress_apella_case=live_case,
                next_case=next_case,
            )

        start_of_day = computed_at.replace(hour=0, minute=0, microsecond=0)
        since = (
            most_recent_case.end_time
            if most_recent_case and most_recent_case.end_time is not None
            else start_of_day
        )

        if next_case is None:
            return RoomStatus(name=RoomStatusName.CLOSED, since=since, computed_at=computed_at)

        in_progress_turnover = (
            await self.turnover_service.generate_turnover(most_recent_case, next_case)
            if most_recent_case is not None
            and most_recent_case.end_time is not None
            and next_case.start_time > most_recent_case.end_time
            else None
        )

        if (
            most_recent_case is not None
            and in_progress_turnover is not None
            and most_recent_case.end_time is not None
            and next_case.start_time - most_recent_case.end_time
            <= timedelta(minutes=float(max_turnover_minutes or DEFAULT_MAX_TURNOVER_MINS))
        ):
            return RoomStatus(
                name=RoomStatusName.TURNOVER,
                since=most_recent_case.end_time,
                computed_at=computed_at,
                in_progress_turnover=in_progress_turnover,
                next_case=next_case,
            )

        return RoomStatus(
            name=RoomStatusName.IDLE,
            since=since,
            computed_at=computed_at,
            next_case=next_case,
            in_progress_turnover=in_progress_turnover,
        )
