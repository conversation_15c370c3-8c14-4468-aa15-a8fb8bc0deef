# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs
import datetime
from http import HTTPStatus
from typing import List, Optional, Iterable, Union
import uuid

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from api_server.services.site.site_store import Site
from asyncpg.exceptions import (
    UniqueViolationError,
)
import sqlalchemy

from sqlalchemy.orm import relationship, mapped_column, Mapped
from sqlalchemy import Foreign<PERSON>ey, JSON, String, DateTime, UniqueConstraint, case, select, Boolean
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.dialects.postgresql import UUID

from apella_cloud_api.dtos import RoomInfoDto
from apella_cloud_api.exceptions import ClientError, NotFound
from databases.sql import Base, new_async_session
from databases.sql.helpers import check_exception
from databases.sql.mixins import OrgIdMixin, SiteIdMixin, TimestampMixin
from databases.sql.sync_as_async_session import async_db_session_provider


def _resolve_camera_model():
    from api_server.services.camera.camera_store import CameraModel

    return CameraModel


def _resolve_room_closure():
    from api_server.services.closures.closure_store import RoomClosure

    return RoomClosure


def _resolve_room_prime_time_config():
    from api_server.services.prime_time.prime_time_store import RoomPrimeTimeConfig

    return RoomPrimeTimeConfig


def _resolve_first_case_config():
    from api_server.services.first_case_config.first_case_config_store import RoomFirstCaseConfig

    return RoomFirstCaseConfig


class RoomTagging(Base, TimestampMixin):
    __tablename__ = "room_taggings"

    room_id: Mapped[str] = mapped_column(
        ForeignKey("rooms.id", ondelete="CASCADE"), primary_key=True, index=True, nullable=False
    )
    room: Mapped["RoomModel"] = relationship(back_populates="taggings")

    tag_id: Mapped[uuid.UUID] = mapped_column(
        ForeignKey("room_tags.id", ondelete="CASCADE"), primary_key=True, index=True, nullable=False
    )
    tag: Mapped["RoomTag"] = relationship(back_populates="taggings")

    def __repr__(self):
        return f"<RoomTagging(room_id='{self.room_id}', tag_id='{self.tag_id})>"


class RoomModel(Base, TimestampMixin, OrgIdMixin, SiteIdMixin):
    __tablename__ = "rooms"
    # The unique ID of this room
    id: Mapped[str] = mapped_column(String, primary_key=True)
    # The display name of this room
    name: Mapped[str] = mapped_column(String, nullable=False)
    sort_key: Mapped[str] = mapped_column(String, nullable=True)
    # The "default" camera that should be used in this room
    default_camera = relationship(
        "RoomDefaultCameraModel",
        primaryjoin="RoomModel.id==RoomDefaultCameraModel.room_id",
        lazy="subquery",
        innerjoin=True,
        uselist=False,
    )

    site: Mapped[Site] = relationship(Site, back_populates="rooms")

    privacy_enabled_at: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
    privacy_updated_by_user_id: Mapped[Union[str, None]] = mapped_column(String, nullable=True)

    is_forecasting_enabled: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True)

    labels: Mapped[JSON] = mapped_column(MutableDict.as_mutable(JSON), nullable=True)  # type: ignore [arg-type]

    taggings: Mapped[List["RoomTagging"]] = relationship(back_populates="room")
    tags: Mapped[List["RoomTag"]] = relationship(
        secondary="room_taggings", back_populates="rooms", viewonly=True
    )

    closures = relationship(_resolve_room_closure, back_populates="room")

    prime_time_config = relationship(
        _resolve_room_prime_time_config, back_populates="room", uselist=False
    )

    first_case_config_record = relationship(
        _resolve_first_case_config, back_populates="room", uselist=False
    )

    @hybrid_property
    def first_case_config(self):
        """
        Returns first case config for the site. This may be defined at the site level or
        at the room level.
        """
        if self.first_case_config_record is not None:
            return self.first_case_config_record

        return self.site.first_case_config

    @hybrid_property
    def prime_time(self):
        """
        Returns prime time for the room. This may be defined at the site level or at
        the room level.
        """
        if self.prime_time_config:
            return self.prime_time_config

        return self.site.prime_time

    def to_dto(self) -> RoomInfoDto:
        dto = RoomInfoDto()
        dto.room_name = self.name
        dto.room_id = self.id
        dto.organization_id = self.org_id
        dto.site_id = self.site_id
        dto.privacy_enabled_at = self.privacy_enabled_at
        dto.privacy_updated_by_user_id = self.privacy_updated_by_user_id
        dto.is_forecasting_enabled = self.is_forecasting_enabled
        if self.default_camera is not None:
            dto.default_camera_id = self.default_camera.camera_id
        return dto

    def __repr__(self):
        return f"<Room(id='{self.id}')>"

    @async_db_session_provider
    async def set_default_camera(
        self, camera_id: Optional[str], session: AsyncSession, description: Optional[str] = None
    ):
        if self.default_camera is not None and self.default_camera.camera_id != camera_id:
            query = (
                select(RoomDefaultCameraModel)
                .filter(RoomDefaultCameraModel.room_id == self.id)
                .filter(RoomDefaultCameraModel.camera_id == self.default_camera.camera_id)
            )
            old_default_camera = (await session.scalars(query)).one()
            await session.delete(old_default_camera)

        default_camera = None
        if camera_id is not None:
            default_camera = RoomDefaultCameraModel()
            default_camera.room_id = self.id
            default_camera.camera_id = camera_id
            default_camera.description = str(description)

        self.default_camera = default_camera
        await session.merge(self)
        await session.commit()


class RoomTag(Base, TimestampMixin, OrgIdMixin):
    __tablename__ = "room_tags"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=sqlalchemy.text("gen_random_uuid()"),
        nullable=False,
    )
    name: Mapped[str] = mapped_column(String, nullable=False)
    color: Mapped[str] = mapped_column(String, nullable=False, server_default="#999999")

    __table_args__ = (UniqueConstraint("name", "org_id"),)

    taggings: Mapped[List["RoomTagging"]] = relationship(back_populates="tag")
    rooms: Mapped[List["RoomModel"]] = relationship(
        secondary="room_taggings", back_populates="tags", viewonly=True
    )

    def __repr__(self):
        return f"<RoomTag(id='{self.id}')>"


class RoomDefaultCameraModel(Base, TimestampMixin):
    __tablename__ = "room_default_camera"
    room_id: Mapped[str] = mapped_column(
        ForeignKey("rooms.id", ondelete="CASCADE"), primary_key=True
    )
    camera_id: Mapped[str] = mapped_column(
        ForeignKey("cameras.id", ondelete="CASCADE"),
        primary_key=True,
    )
    description: Mapped[Union[str, None]] = mapped_column(String, nullable=True)

    camera = relationship(_resolve_camera_model)

    def __repr__(self):
        return f"<RoomDefaultCamera(room_id='{self.room_id}',camera_id='{self.camera_id}')>"


class RoomStore:
    @async_db_session_provider
    async def get_room(self, room_id: str, session: AsyncSession) -> RoomModel:
        return await self.__get_room(session, room_id=room_id)

    async def __get_room(self, session: AsyncSession, room_id: str) -> RoomModel:
        try:
            query = select(RoomModel).filter(RoomModel.id == room_id)
            return (await session.scalars(query)).one()
        except sqlalchemy.orm.exc.NoResultFound:
            raise NotFound(f"No room found with id: {room_id}")

    async def get_rooms(self, room_ids: List[str]) -> List[RoomModel]:
        async with new_async_session() as session:
            query = (
                select(RoomModel)
                .filter(RoomModel.id.in_(room_ids))
                .order_by(
                    case(
                        (RoomModel.sort_key.is_(None), RoomModel.id),
                        else_=RoomModel.sort_key,
                    ),
                )
            )
            return list((await session.scalars(query)).all())

    async def get_rooms_in_site(self, site_id: str) -> List[RoomModel]:
        async with new_async_session() as session:
            query = (
                select(RoomModel)
                .filter(RoomModel.site_id == site_id)
                .order_by(
                    case(
                        (RoomModel.sort_key.is_(None), RoomModel.id),
                        else_=RoomModel.sort_key,
                    ),
                )
            )
            return list((await session.scalars(query)).all())

    @async_db_session_provider
    async def create_room(self, room: RoomModel, session: AsyncSession) -> RoomModel:
        try:
            session.add(room)
            await session.commit()
            await session.refresh(room)
            return room
        except sqlalchemy.exc.IntegrityError as e:
            if check_exception(UniqueViolationError, e):
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    f'Room with id "{room.id}" already exists',
                )
            raise e

    async def create_rooms(self, rooms: List[RoomModel]) -> List[RoomModel]:
        async with new_async_session() as session:
            try:
                for room in rooms:
                    session.add(room)

                await session.commit()

                for room in rooms:
                    await session.refresh(room)
                return rooms
            except sqlalchemy.exc.IntegrityError as e:
                if check_exception(UniqueViolationError, e):
                    error_message = " ".join(map(str, e.args))

                    raise ClientError(
                        HTTPStatus.UNPROCESSABLE_ENTITY,
                        f"Create rooms failed with {error_message}",
                    )
                raise e

    async def replace_room(self, room: RoomModel) -> RoomModel:
        async with new_async_session() as session:
            # First need to query the old room to ensure it is a valid room to replace
            old_room: RoomModel = await self.__get_room(session, room_id=room.id)
            await session.merge(room)
            await session.commit()
            await session.refresh(old_room)
            return old_room

    async def query_rooms(
        self,
        room_ids: Optional[List[str]] = None,
        site_ids: Optional[Iterable[str]] = None,
        org_id: Optional[str] = None,
    ) -> List[RoomModel]:
        async with new_async_session() as session:
            query = select(RoomModel)
            if room_ids is not None:
                query = query.filter(RoomModel.id.in_(room_ids))
            if site_ids is not None:
                query = query.filter(RoomModel.site_id.in_(site_ids))
            if org_id is not None:
                query = query.filter(RoomModel.org_id == org_id)
            query = query.order_by(
                case(
                    (RoomModel.sort_key.is_(None), RoomModel.id),
                    else_=RoomModel.sort_key,
                ),
            )
            return list((await session.scalars(query)).all())

    async def create_room_tag(self, room_tag: RoomTag) -> RoomTag:
        async with new_async_session() as session:
            try:
                session.add(room_tag)
                await session.commit()
                await session.refresh(room_tag)
                return room_tag
            except sqlalchemy.exc.IntegrityError as e:
                if check_exception(UniqueViolationError, e):
                    raise ClientError(
                        HTTPStatus.UNPROCESSABLE_ENTITY,
                        f'RoomTag with name "{room_tag.id}" already exists',
                    )
                raise e

    async def set_room_tags(self, room_id: str, tag_ids: List[str]) -> RoomModel:
        async with new_async_session() as session:
            new_room_tags = (
                await session.scalars(select(RoomTag).filter(RoomTag.id.in_(tag_ids)))
            ).all()

            room = (await session.scalars(select(RoomModel).filter(RoomModel.id == room_id))).one()

            existing_taggings = (
                await session.scalars(select(RoomTagging).filter(RoomTagging.room_id == room_id))
            ).all()

            for tag in new_room_tags:
                if not any(tagging.tag_id == tag.id for tagging in existing_taggings):
                    session.add(RoomTagging(room_id=room_id, tag_id=tag.id))

            for tagging in existing_taggings:
                if str(tagging.tag_id) not in tag_ids:
                    await session.delete(tagging)

            await session.commit()
            await session.refresh(room)
            return room

    async def query_room_tags(
        self,
        tag_ids: Optional[List[str]] = None,
        org_id: Optional[str] = None,
        room_id: Optional[str] = None,
    ) -> Iterable[RoomTag]:
        async with new_async_session() as session:
            statement = select(RoomTag)
            if tag_ids is not None:
                statement = statement.filter(RoomTag.id.in_(tag_ids))
            if room_id is not None:
                statement = statement.filter(RoomTag.rooms.any(RoomModel.id == room_id))
            if org_id is not None:
                statement = statement.filter(RoomTag.org_id == org_id)

            return (await session.scalars(statement)).all()

    async def get_room_tags_for_rooms(self, room_ids: List[str]) -> List[List[RoomTag]]:
        async with new_async_session() as session:
            statement = (
                select(RoomTag)
                .filter(RoomTag.rooms.any(RoomModel.id.in_(room_ids)))
                .options(joinedload(RoomTag.rooms))
            )
            room_tags = list((await session.scalars(statement)).unique().all())

            result_dict: dict[str, list[RoomTag]] = {room_id: [] for room_id in room_ids}
            for room_tag in room_tags:
                for room in room_tag.rooms:
                    if room.id in result_dict:
                        result_dict[room.id].append(room_tag)
            return [result_dict.get(room_id, []) for room_id in room_ids]

    @staticmethod
    async def rename_room_tag(tag_id: str, new_name: str) -> RoomTag:
        async with new_async_session() as session:
            try:
                result = await session.execute(select(RoomTag).filter_by(id=tag_id))
                room_tag = result.scalar_one_or_none()

                if room_tag is None:
                    raise ValueError("RoomTag not found")

                room_tag.name = new_name
                await session.commit()
                await session.refresh(room_tag)
                return room_tag

            except sqlalchemy.exc.IntegrityError as e:
                if check_exception(UniqueViolationError, e):
                    raise ClientError(
                        HTTPStatus.UNPROCESSABLE_ENTITY,
                        f'RoomTag with name "{new_name}" already exists',
                    )
                raise e
            except ValueError as e:
                raise e

    @staticmethod
    async def update_room_tag(
        tag_id: str, name: Optional[str] = None, color: Optional[str] = None
    ) -> RoomTag:
        async with new_async_session() as session:
            try:
                result = await session.execute(select(RoomTag).filter_by(id=tag_id))
                room_tag = result.scalar_one_or_none()

                if room_tag is None:
                    raise ValueError("RoomTag not found")

                if name is not None:
                    room_tag.name = name
                if color is not None:
                    room_tag.color = color

                await session.commit()
                await session.refresh(room_tag)
                return room_tag

            except sqlalchemy.exc.IntegrityError as e:
                if check_exception(UniqueViolationError, e):
                    raise ClientError(
                        HTTPStatus.UNPROCESSABLE_ENTITY,
                        f'RoomTag with name "{name}" already exists',
                    )
                raise e
            except ValueError as e:
                raise e
