import logging
import traceback
from typing import Any

import graphene

from api_server.graphql import scalar
from api_server.graphql.context import GrapheneInfo
from api_server.logging import log


class AvailableTimeSlotInput(graphene.InputObjectType):
    room_id = graphene.String(required=True)
    start_time = graphene.DateTime(required=True)
    end_time = graphene.DateTime(required=True)
    max_available_duration = scalar.Duration(required=True)


class EmailAvailableTimesInput(graphene.InputObjectType):
    start_date = graphene.Date(required=True)
    end_date = graphene.Date(required=True)
    sender_name = graphene.String(required=False)
    sender_email = graphene.String(required=False)
    recipients = graphene.List(graphene.NonNull(graphene.String), required=True)
    slots = graphene.List(graphene.NonNull(AvailableTimeSlotInput), required=True)


class EmailAvailableTimes(graphene.Mutation):
    class Arguments:
        input = graphene.NonNull(EmailAvailableTimesInput)

    success = graphene.Boolean(required=True)

    @staticmethod
    async def mutate(
        _: Any,
        info: GrapheneInfo,
        input: EmailAvailableTimesInput,
    ) -> "EmailAvailableTimes":
        try:
            await info.context.schedule_assistant_email_service.send_email(
                recipients=input.recipients,
                start_date=input.start_date,
                end_date=input.end_date,
                slots=input.slots,
                sender_email=input.sender_email,
                sender_name=input.sender_name,
            )
            return EmailAvailableTimes(success=True)

        except Exception as e:
            log(logging.WARN, str(e), exc_info=True)
            log(logging.WARN, traceback.format_exc())

            return EmailAvailableTimes(success=False)


class EmailAvailableTimesHtmlInput(graphene.InputObjectType):
    start_date = graphene.Date(required=True)
    end_date = graphene.Date(required=True)
    slots = graphene.List(graphene.NonNull(AvailableTimeSlotInput), required=True)


class EmailAvailableTimesHtml(graphene.Mutation):
    class Arguments:
        input = graphene.NonNull(EmailAvailableTimesHtmlInput)

    success = graphene.Boolean(required=True)
    email = graphene.String()
    subject = graphene.String()

    @staticmethod
    async def mutate(
        _: Any, info: GrapheneInfo, input: EmailAvailableTimesHtmlInput
    ) -> "EmailAvailableTimesHtml":
        try:
            email_html = await info.context.schedule_assistant_email_service.get_email_html(
                start_date=input.start_date, end_date=input.end_date, slots=input.slots
            )

            return EmailAvailableTimesHtml(
                success=True,
                email=email_html["email"],
                subject=email_html["subject"],
            )
        except Exception as e:
            log(logging.WARN, str(e), exc_info=True)
            log(logging.WARN, traceback.format_exc())

            return EmailAvailableTimesHtml(success=False)
