from dataclasses import dataclass
from datetime import datetime
from typing import Any, Iterable
from zoneinfo import ZoneInfo


from api_server.logging.audit import log_calls_to_audit
from api_server.services.room.room_service import RoomService
from api_server.services.room.room_store import RoomTag
from api_server.services.schedule_assistant_email_builder.models.schedule_assistant_email_builder_models import (
    SlotInput,
)
from api_server.services.site.site_service import SiteService
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import EMAIL_ANY_AVAILABLE_TIME


@dataclass
class ScheduleAssistantEmailBuilderService:
    auth: Auth
    room_service: RoomService
    site_service: SiteService

    TIME_FORMAT = "%H:%M"
    DATE_FORMAT = "%m/%d"
    TITLE_DATES_FORMAT = "%m/%d/%y"

    @log_calls_to_audit()
    @requires_permissions(EMAIL_ANY_AVAILABLE_TIME)
    async def get_email_message_data(
        self, start_date: datetime, end_date: datetime, slots: list[SlotInput]
    ) -> dict[str, Any]:
        try:
            site_to_slots: dict[str, Any] = {}

            for slot in slots:
                room_tags: dict[str, str] = {}

                room = await self.room_service.get_room(slot.room_id)
                site = await self.site_service.get_site(room.site_id)
                tags: Iterable[RoomTag] = await self.room_service.query_room_tags(
                    room_id=slot.room_id
                )

                timezone = ZoneInfo(site.timezone)

                start_time = slot.start_time.astimezone(timezone).strftime(self.TIME_FORMAT)
                end_time = slot.end_time.astimezone(timezone).strftime(self.TIME_FORMAT)

                for tag in tags:
                    room_tags[tag.name] = tag.color

                formatted_room_tags = [
                    {"name": name, "color": color} for name, color in room_tags.items()
                ]

                duration_minutes = int((slot.end_time - slot.start_time).total_seconds() / 60)
                formatted_slot = {
                    "date": slot.start_time.strftime(self.DATE_FORMAT),
                    "time": f"{start_time} - {end_time}",
                    "duration": str(duration_minutes),
                    "room": room.name,
                    "roomtags": formatted_room_tags,
                }
                site_to_slots.setdefault(site.name, []).append(formatted_slot)

            formatted_start_date = start_date.strftime(self.TITLE_DATES_FORMAT)
            formatted_end_date = end_date.strftime(self.TITLE_DATES_FORMAT)
            formatted_available_slots = [
                {"site_name": site, "slots": slots} for site, slots in site_to_slots.items()
            ]

            return {
                "start_date": formatted_start_date,
                "end_date": formatted_end_date,
                "available_slots": formatted_available_slots,
            }
        except Exception as e:
            raise e
