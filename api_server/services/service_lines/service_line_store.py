from typing import Optional
import uuid
from http import HTTPStatus
from asyncpg.exceptions import (
    UniqueViolationError,
)
import sqlalchemy
from sqlalchemy import String, UniqueConstraint, select
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import relationship, mapped_column, Mapped
from sqlalchemy.sql.expression import func

from apella_cloud_api.exceptions import ClientError, NotFound
from databases.sql import Base, new_async_session
from databases.sql.helpers import check_exception
from databases.sql.mixins import OrgIdMixin, TimestampMixin


class ServiceLineModel(Base, TimestampMixin, OrgIdMixin):
    __tablename__ = "service_lines"

    __table_args__ = (
        UniqueConstraint(
            "org_id", "external_service_line_id", name="uq_orgId_external_service_line_id"
        ),
    )

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    external_service_line_id: Mapped[str] = mapped_column(String, nullable=False, index=True)
    # TODO: This should not be nullable but it seems we rely on a commit on a null value and then updating
    # the name value which causes a non nullable value exception.
    name: Mapped[str] = mapped_column(String, index=True, nullable=True)

    cases = relationship("Case")

    def __repr__(self) -> str:
        return f"<ServiceLine(id='{self.id}')>"


class ServiceLineStore:
    async def get_service_line_by_id(self, service_line_id: str) -> ServiceLineModel:
        try:
            async with new_async_session() as session:
                query = select(ServiceLineModel).filter(ServiceLineModel.id == service_line_id)
                results = await session.scalars(query)
                return results.one()
        except sqlalchemy.exc.NoResultFound:
            raise NotFound(f"Unknown service line ID: '{service_line_id}'")

    async def query_service_lines(
        self, ids: Optional[list[str]] = None, org_id: Optional[str] = None
    ) -> list[ServiceLineModel]:
        query = select(ServiceLineModel)

        if ids is not None:
            query = query.filter(ServiceLineModel.id.in_(ids))
        elif org_id is not None:
            query = query.filter(ServiceLineModel.org_id == org_id)
        async with new_async_session() as session:
            results = await session.scalars(query)
            return list(results.all())

    async def get_service_line(
        self, org_id: str, external_service_line_id: str, session: Optional[AsyncSession] = None
    ) -> ServiceLineModel:
        if session is not None:
            return await self._get_service_line(org_id, external_service_line_id, session)
        async with new_async_session() as new_session:
            return await self._get_service_line(org_id, external_service_line_id, new_session)

    async def _get_service_line(
        self, org_id: str, external_service_line_id: str, session: AsyncSession
    ) -> ServiceLineModel:
        try:
            query = (
                select(ServiceLineModel)
                .filter(
                    func.upper(ServiceLineModel.external_service_line_id)
                    == func.upper(external_service_line_id)
                )
                .filter(ServiceLineModel.org_id == org_id)
            )
            results = await session.scalars(query)
            return results.one()
        except sqlalchemy.exc.NoResultFound:
            raise NotFound(
                f"Unknown service line code: '{external_service_line_id}' for org ID '{org_id}'"
            )

    async def create_or_update_service_line(
        self, service_line: ServiceLineModel, session: Optional[AsyncSession] = None
    ) -> ServiceLineModel:
        if session is not None:
            return await self._create_or_update_service_line(service_line, session)
        async with new_async_session() as new_session:
            return await self._create_or_update_service_line(service_line, new_session)

    async def _create_or_update_service_line(
        self, service_line: ServiceLineModel, session: AsyncSession
    ) -> ServiceLineModel:
        query = (
            select(ServiceLineModel)
            .filter(
                func.upper(ServiceLineModel.external_service_line_id)
                == func.upper(service_line.external_service_line_id)
            )
            .filter(ServiceLineModel.org_id == service_line.org_id)
        )
        query_results = (await session.scalars(query)).all()
        if query_results:
            old_service_line: ServiceLineModel = query_results[0]
            old_service_line.name = service_line.name
            old_service_line = await session.merge(old_service_line)
            await session.commit()
            await session.refresh(old_service_line)
            return old_service_line
        try:
            session.add(service_line)
            await session.commit()
            await session.refresh(service_line)
            return service_line
        except sqlalchemy.exc.IntegrityError as e:
            if check_exception(UniqueViolationError, e):
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY,
                    f'Service Line with id "{service_line.id}" already exists',
                )
            raise e
