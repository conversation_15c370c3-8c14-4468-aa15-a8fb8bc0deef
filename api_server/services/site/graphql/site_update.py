from __future__ import annotations

from typing import Any

import graphene
from graphql import GraphQLError

import api_server.services.site.graphql.site as site_schema
from apella_cloud_api.exceptions import ClientError
from api_server.graphql.context import GrapheneInfo
from api_server.services.site.site_store import Site as SiteModel


class SiteUpdateInput(graphene.InputObjectType):
    id = graphene.ID(required=True)
    name = graphene.String()
    organization_id = graphene.String()
    timezone = graphene.String()


class SiteUpdate(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(SiteUpdateInput, required=True)

    success: bool = graphene.Boolean()
    updated_site: SiteModel = graphene.Field(site_schema.Site)

    @staticmethod
    async def mutate(_: Any, info: GrapheneInfo, input: SiteUpdateInput) -> SiteUpdate:
        site: SiteModel = SiteModel()
        site.id = input.id
        site.name = input.name
        site.org_id = input.organization_id
        site.timezone = input.timezone

        try:
            updated_site = await info.context.site_service.update_site(site)
            return_value = SiteUpdate()
            return_value.success = True
            return_value.updated_site = updated_site
            return return_value
        except ClientError as e:
            raise GraphQLError(e.message)
