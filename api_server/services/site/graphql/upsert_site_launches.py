from typing import List, Any

import graphene

from api_server.graphql.context import GrapheneInfo
from api_server.services.site.site_store import SiteLaunch


class SiteLaunchUpsertInput(graphene.InputObjectType):
    site_id = graphene.String(required=False)
    site_name = graphene.String(required=True)
    actual_launch_date = graphene.Date(required=False)
    anticipated_launch_date = graphene.Date(required=False)
    notion_id = graphene.String(required=False, description="Notion ID for the site launch record.")


class SiteLaunchesUpsert(graphene.Mutation):
    class Arguments:
        input = graphene.NonNull(graphene.List(graphene.NonNull(SiteLaunchUpsertInput)))

    success = graphene.Boolean()

    @staticmethod
    async def mutate(
        parent: Any,
        info: GrapheneInfo,
        input: List[SiteLaunchUpsertInput],
    ) -> "SiteLaunchesUpsert":
        """
        Given a list of site_launches, upsert them
        """
        await info.context.site_service.upsert_site_launches(
            [
                SiteLaunch(
                    site_id=site_launch_input.site_id,
                    site_name=site_launch_input.site_name,
                    actual_launch_date=site_launch_input.actual_launch_date,
                    anticipated_launch_date=site_launch_input.anticipated_launch_date,
                    notion_id=site_launch_input.notion_id,
                )
                for site_launch_input in input
            ]
        )
        return SiteLaunchesUpsert(success=True)
