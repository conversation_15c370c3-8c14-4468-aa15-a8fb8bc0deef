from fastapi import APIRouter, Depends

from api_server.app_service_provider.apella_app_container import app_container
from apella_cloud_api import SiteInfoDto
from api_server.services.room.room_service import RoomService
from api_server.services.site.site_service import SiteService

site_api_router = APIRouter(prefix="/v1")


def get_site_service() -> SiteService:
    return app_container.provide_app_scope_type(SiteService)


def get_room_service() -> RoomService:
    return app_container.provide_app_scope_type(RoomService)


@site_api_router.get("/site/{site_id}")
async def get_site_info_endpoint(
    site_id: str,
    site_service: SiteService = Depends(get_site_service),
    room_service: RoomService = Depends(get_room_service),
) -> SiteInfoDto:
    site = await site_service.get_site(site_id=site_id)
    dto = site.to_dto()
    dto.rooms = await room_service.get_room_ids_in_site(site_id=site.id)
    return dto


@site_api_router.get("/sites")
async def get_all_sites_endpoint(
    site_service: SiteService = Depends(get_site_service),
) -> list[str]:
    sites = await site_service.query_sites()
    return [site.id for site in sites]
