from dataclasses import dataclass
from typing import Optional

from api_server.logging.audit import log_calls_to_audit
from api_server.services.site.site_models import SiteQuery
from api_server.services.site.site_store import Site, SiteStore, SiteLaunch
from api_server.tracing.trace_decorator import class_traced
from auth.auth import Auth
from auth.auth_decorator import requires_permission_prefix, requires_permissions
from auth.permissions import READ_SITE_PREFIX, WRITE_ANY_SITE, WRITE_SITE_LAUNCH


@class_traced()
@dataclass
class SiteService:
    auth: Auth
    site_store: SiteStore

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_SITE, enforce_universal_user=True)
    async def create_site(self, site: Site) -> Site:
        return self.site_store.create_site(site)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_SITE, enforce_universal_user=True)
    async def update_site(self, site: Site) -> Site:
        return await self.site_store.update_site(site)

    @log_calls_to_audit()
    @requires_permission_prefix(READ_SITE_PREFIX)
    async def get_site(self, site_id: str) -> Site:
        return await self.site_store.get_site(site_id=site_id)

    @log_calls_to_audit()
    @requires_permission_prefix(READ_SITE_PREFIX)
    async def query_sites(self, site_query: Optional[SiteQuery] = None) -> list[Site]:
        site_query = site_query or SiteQuery()
        return await self.site_store.query_sites(site_query=site_query)

    @log_calls_to_audit()
    @requires_permissions(WRITE_SITE_LAUNCH, enforce_universal_user=True)
    async def upsert_site_launches(self, site_launches: list[SiteLaunch]) -> bool:
        """
        Upsert site launches. This will create new launches or update existing ones.
        """
        return await self.site_store.upsert_site_launches(site_launches)

    @log_calls_to_audit()
    @requires_permissions(READ_SITE_PREFIX, enforce_universal_user=True)
    async def get_site_launches(self, site_names: list[str]) -> list[SiteLaunch]:
        """
        Get all launches for a given site.
        """
        return await self.site_store.get_site_launches(site_names)
