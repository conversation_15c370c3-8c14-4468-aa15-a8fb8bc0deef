# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs
import datetime
from http import HTT<PERSON>tatus
from zoneinfo import ZoneInfo

import sqlalchemy
from asyncpg.exceptions import UniqueViolationError
from sqlalchemy import String, select, ForeignKey, Date
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.ext.asyncio import AsyncSession

from apella_cloud_api import SiteInfoDto
from apella_cloud_api.exceptions import ClientError, NotFound
from api_server.services.site.site_models import SiteQuery
from databases.sql import Base, new_async_session
from databases.sql.helpers import check_exception
from databases.sql.mixins import OrgIdMixin, TimestampMixin
from databases.sql.sync_as_async_session import async_db_session_provider


def _resolve_room():
    from api_server.services.room.room_store import RoomModel

    return RoomModel


def _resolve_first_case_config():
    from api_server.services.first_case_config.first_case_config_store import SiteFirstCaseConfig

    return SiteFirstCaseConfig


def _resolve_site_prime_time_config():
    from api_server.services.prime_time.prime_time_store import SitePrimeTimeConfig

    return SitePrimeTimeConfig


def _resolve_site_capacity_constraint():
    from api_server.services.prime_time.prime_time_store import SiteCapacityConstraint

    return SiteCapacityConstraint


def _resolve_site_closure():
    from api_server.services.closures.closure_store import SiteClosure

    return SiteClosure


class Site(Base, TimestampMixin, OrgIdMixin):
    __tablename__ = "sites"
    # The unique ID of this site
    id: Mapped[str] = mapped_column(String, primary_key=True)
    # The display name of this site
    name: Mapped[str] = mapped_column(String, nullable=False)
    # The timezone name for this site, from the tz database
    timezone: Mapped[str] = mapped_column(String, nullable=False)

    prime_time_config = relationship(
        _resolve_site_prime_time_config, back_populates="site", uselist=False
    )

    first_case_config_record = relationship(
        _resolve_first_case_config, back_populates="site", uselist=False
    )

    rooms = relationship(_resolve_room, back_populates="site")

    capacity_constraints = relationship(_resolve_site_capacity_constraint, back_populates="site")

    closures = relationship(_resolve_site_closure, back_populates="site")

    @property
    def timezone_info(self):
        return ZoneInfo(self.timezone)

    @hybrid_property
    def first_case_config(self):
        """
        Returns first case config for the site.  If no record exists, returns the
        default first case config
        """
        if self.first_case_config_record is not None:
            return self.first_case_config_record

        return _resolve_first_case_config().default_instance(str(self.id))

    @hybrid_property
    def prime_time(self):
        """
        Returns prime time for the site. If no record exists, returns the
        default prime time
        """
        if self.prime_time_config is not None:
            return self.prime_time_config

        return _resolve_site_prime_time_config().default_instance(self.id)

    def to_dto(self):
        return SiteInfoDto(
            site_id=self.id,
            organization_id=self.org_id,
            site_name=self.name,
            timezone=self.timezone,
        )

    def from_dto(self, dto: SiteInfoDto):
        self.id = dto.site_id
        self.org_id = dto.organization_id
        self.name = dto.site_name
        self.timezone = dto.timezone
        return self

    def __repr__(self):
        return f"<Site(id='{self.id}')>"


class SiteLaunch(Base, TimestampMixin):
    __tablename__ = "site_launch_information"
    site_id: Mapped[str] = mapped_column(String, ForeignKey("sites.id"), nullable=True)
    actual_launch_date: Mapped[datetime.date] = mapped_column(Date, nullable=True, index=True)
    anticipated_launch_date: Mapped[datetime.date] = mapped_column(Date, nullable=True, index=True)
    site_name: Mapped[str] = mapped_column(String, nullable=False, index=True)
    notion_id: Mapped[str] = mapped_column(String, index=True, primary_key=True)


class SiteStore:
    async def __get_site(self, site_id: str, session: AsyncSession) -> Site:
        try:
            query = select(Site).filter(Site.id == site_id)
            return (await session.scalars(query)).unique().one()
        except sqlalchemy.orm.exc.NoResultFound:
            raise NotFound(f"No site found with id: {site_id}")

    async def get_site(self, site_id: str) -> Site:
        async with new_async_session() as session:
            return await self.__get_site(site_id, session)

    async def query_sites(self, site_query: SiteQuery) -> list[Site]:
        async with new_async_session() as session:
            statement = select(Site).order_by(Site.id.asc())

            if site_query.organization_id is not None:
                statement = statement.filter(Site.org_id == site_query.organization_id)
            if site_query.organization_ids is not None:
                statement = statement.filter(Site.org_id.in_(site_query.organization_ids))
            if site_query.site_ids is not None:
                statement = statement.filter(Site.id.in_(site_query.site_ids))

            return list((await session.scalars(statement)).all())

    @async_db_session_provider
    async def create_site(self, site: Site, session: AsyncSession):
        try:
            session.add(site)
            await session.commit()
            await session.refresh(site)
            return site
        except sqlalchemy.exc.IntegrityError as e:
            if check_exception(UniqueViolationError, e):
                raise ClientError(
                    HTTPStatus.UNPROCESSABLE_ENTITY, f'Site with id "{site.id}" already exists'
                )
            raise e

    async def update_site(self, site: Site):
        async with new_async_session() as session:
            new_site = await session.merge(site)

            await session.commit()
            await session.refresh(new_site)
            return new_site

    async def delete_site(self, site_id: str) -> None:
        async with new_async_session() as session:
            site = await self.__get_site(site_id, session)
            await session.delete(site)
            await session.commit()

    @async_db_session_provider
    async def upsert_site_launches(
        self, site_launches: list[SiteLaunch], session: AsyncSession
    ) -> list[SiteLaunch]:
        """
        Upsert site launches. If the site launch already exists, it will be updated.
        """
        upserted_launches = []
        for site_launch in site_launches:
            try:
                launch = await session.merge(site_launch)
                await session.commit()
                await session.refresh(launch)
                upserted_launches.append(launch)
            except sqlalchemy.exc.IntegrityError as e:
                if check_exception(UniqueViolationError, e):
                    raise ClientError(
                        HTTPStatus.UNPROCESSABLE_ENTITY,
                        f'Site launch with id "{site_launch.site_id}" already exists',
                    )
                raise e
        return upserted_launches

    @async_db_session_provider
    async def get_site_launches(
        self, site_names: list[str], session: AsyncSession
    ) -> list[SiteLaunch]:
        """
        Get site launches for a given site.
        """
        statement = select(SiteLaunch).filter(SiteLaunch.site_name.in_(site_names))
        return list((await session.scalars(statement)).all())
