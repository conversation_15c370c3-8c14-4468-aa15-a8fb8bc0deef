# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

from typing import List

import graphene

from api_server.graphql.context import GrapheneInfo
from api_server.services.staff.graphql.staff import StaffConnection
from api_server.services.staff.staff_store import StaffModel


class StaffUpsertInput(graphene.InputObjectType):
    org_id = graphene.String(required=True)
    external_staff_id = graphene.String(required=True)
    first_name = graphene.String(required=True)
    last_name = graphene.String(required=True)


class StaffUpsert(graphene.Mutation):
    class Arguments:
        staff = graphene.List(graphene.NonNull(StaffUpsertInput), required=True)

    success = graphene.Boolean()
    created_staff = graphene.ConnectionField(lambda: StaffConnection, required=True)

    @staticmethod
    async def mutate(parent, info: GrapheneInfo, staff: List[StaffUpsertInput]):
        staff_to_create = [
            StaffModel(
                org_id=s.org_id,
                first_name=s.first_name,
                last_name=s.last_name,
                external_staff_id=s.external_staff_id,
            )
            for s in staff
        ]
        created_staff = await info.context.staff_service.upsert_staff(staff=staff_to_create)
        return StaffUpsert(success=True, created_staff=created_staff)
