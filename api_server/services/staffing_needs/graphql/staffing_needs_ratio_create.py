import graphene

from api_server.graphql.context import GrapheneInfo
from api_server.services.staffing_needs.graphql.staffing_needs_ratio import (
    StaffingNeedsRatio,
)
from api_server.services.staffing_needs.staffing_needs_store import StaffingNeedsModel


class StaffingNeedsRatioCreateInput(graphene.InputObjectType):
    id = graphene.ID(required=True)
    organization_id = graphene.String(required=True)
    site_id = graphene.String(required=True)

    # The API will use the calling user's id if one is not provided
    set_by_user_id = graphene.String(required=False)
    ratio = graphene.Float(required=False)
    staff_role_id = graphene.String(required=True)


class StaffingNeedsRatioCreate(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(StaffingNeedsRatioCreateInput)

    success = graphene.Boolean()
    created_staffing_needs_ratio = graphene.Field(StaffingNeedsRatio)

    @staticmethod
    async def mutate(
        parent: None, info: GrapheneInfo, input: StaffingNeedsRatioCreateInput
    ) -> "StaffingNeedsRatioCreate":
        # Copy all the fields over from the input type
        model_to_insert = StaffingNeedsModel()
        model_to_insert.id = input.id
        model_to_insert.org_id = input.organization_id
        model_to_insert.site_id = input.site_id
        if input.set_by_user_id is not None:
            model_to_insert.set_by_user_id = input.set_by_user_id
        else:
            model_to_insert.set_by_user_id = info.context.auth.get_calling_user_id()
        model_to_insert.ratio = input.ratio
        model_to_insert.staff_role_id = input.staff_role_id

        new_ratio = await info.context.staffing_needs_service.set_staffing_needs_ratio(
            model_to_insert
        )

        return StaffingNeedsRatioCreate(success=True, created_staffing_needs_ratio=new_ratio)
