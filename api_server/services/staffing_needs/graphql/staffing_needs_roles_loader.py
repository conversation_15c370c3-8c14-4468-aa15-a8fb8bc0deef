from typing import Optional

from aiodataloader import DataLoader

from api_server.services.staff_role.staff_role_store import StaffRoleModel
from api_server.services.staffing_needs.staffing_needs_service import (
    StaffingNeedsService,
)
from api_server.services.utils.loader.util_functions import sort_loader_results


# TODO: This isn't used anywhere, can this be removed?
class StaffingNeedsRolesLoader(DataLoader[str, Optional[StaffRoleModel]]):
    staffing_needs_service: StaffingNeedsService

    def __init__(self, staffing_needs_service: StaffingNeedsService):
        super().__init__()
        self.staffing_needs_service = staffing_needs_service

    async def batch_load_fn(self, keys: list[str]) -> list[Optional[StaffRoleModel]]:
        roles = await self.staffing_needs_service.get_staffing_needs_roles()
        return sort_loader_results(keys, roles)
