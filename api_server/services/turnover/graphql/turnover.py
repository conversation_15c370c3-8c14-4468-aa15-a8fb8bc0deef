from typing import Optional
import graphene
from api_server.graphql.context import GrapheneInfo
from api_server.services.apella_case.graphql.apella_case import ApellaCase
from api_server.services.events.graphql.event_loaders import FrozenRoomEventsQueryDto
from api_server.services.events.source_type import PREDICTION, HUMAN_GROUND_TRUTH
from api_server.services.turnover.graphql.turnover_label import (
    TurnoverLabel,
)
from api_server.services.turnover.turnover_label_store import TurnoverLabelModel
from api_server.services.turnover.turnover_models import (
    Turnover as TurnoverModel,
    TurnoverStatusName as TurnoverStatusEnum,
    TurnoverType,
    TurnoverStatus,
)
from api_server.services.turnover.turnover_utils import TURNOVER_EVENT_TYPES


TurnoverTypeGraphene = graphene.Enum.from_enum(TurnoverType)
TurnoverStatusEnumGraphene = graphene.Enum.from_enum(TurnoverStatusEnum)


class TurnoverStatusGraphene(graphene.ObjectType):
    name = TurnoverStatusEnumGraphene(required=True)
    since = graphene.DateTime(required=False)


class TurnoverQueryInput(graphene.InputObjectType):
    min_end_time = graphene.DateTime(required=True)
    max_start_time = graphene.DateTime(required=True)
    case_ids = graphene.List(graphene.NonNull(graphene.ID))
    phase_ids = graphene.List(graphene.NonNull(graphene.ID))
    turnover_id = graphene.String()
    meets_inclusion_criteria = graphene.Boolean()


class Turnover(graphene.ObjectType):
    id = graphene.ID(required=True)
    type = TurnoverTypeGraphene(required=True)
    status = graphene.Field(TurnoverStatusGraphene, required=False)

    start_time = graphene.DateTime(required=True)
    end_time = graphene.DateTime(required=True)

    preceding_case = graphene.Field(ApellaCase, required=True)
    following_case = graphene.Field(ApellaCase, required=True)

    meets_inclusion_criteria = graphene.Boolean(required=True)

    labels = graphene.List(graphene.NonNull(TurnoverLabel), required=False)
    note = graphene.String(required=False)

    @staticmethod
    async def resolve_status(
        turnover: TurnoverModel, info: GrapheneInfo
    ) -> Optional[TurnoverStatus]:
        # To be able to optimize the number of queries we make, we can query using a loader.
        # The loader joins queries by room id if every other query param is the same.
        start_of_day = turnover.start_time.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_day = turnover.end_time.replace(hour=23, minute=59, second=59, microsecond=999)
        events = await info.context.room_events_loader.load(
            FrozenRoomEventsQueryDto(
                room_id=turnover.room_id,
                min_time=start_of_day,
                max_time=end_of_day,
                event_names=TURNOVER_EVENT_TYPES,
                include_deleted=False,
                source_types=(PREDICTION, HUMAN_GROUND_TRUTH),
            )
        )

        # filter for events that happened during the turnover
        events_during_turnover = [
            event
            for event in events
            if turnover.start_time <= event.start_time and turnover.end_time >= event.start_time
        ]

        return await info.context.turnover_service.get_status(turnover, events_during_turnover)

    @staticmethod
    async def resolve_labels(
        turnover: TurnoverModel,
        info: GrapheneInfo,
    ) -> Optional[list[TurnoverLabelModel]]:
        result = await info.context.turnover_label_loader.load(
            (turnover.preceding_case.id, turnover.following_case.id)
        )
        return result

    @staticmethod
    async def resolve_note(
        turnover: TurnoverModel,
        info: GrapheneInfo,
    ) -> Optional[str]:
        turnoverNoteModel = await info.context.turnover_note_loader.load(
            (turnover.preceding_case.id, turnover.following_case.id)
        )
        return turnoverNoteModel.note if turnoverNoteModel else None
