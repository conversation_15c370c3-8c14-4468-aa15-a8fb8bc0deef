from __future__ import annotations

from typing import Any

import graphene

import api_server.services.site.graphql.site as site_schema
from api_server.graphql.context import GrapheneInfo
from api_server.services.turnover.turnover_service import DEFAULT_MAX_TURNOVER_MINS
from api_server.services.turnover.turnover_store import TurnoverGoal


class TurnoverGoalsUpdateInput(graphene.InputObjectType):
    site_id = graphene.ID(required=True)
    org_id = graphene.String(required=True)

    goal_minutes = graphene.Int(required=False)
    max_minutes = graphene.Int(required=False)


class TurnoverGoalsUpdate(graphene.Mutation):
    class Arguments:
        input = graphene.Argument(TurnoverGoalsUpdateInput, required=True)

    success = graphene.Boolean()
    updated_turnover_goals = graphene.Field(site_schema.TurnoverGoals)

    @staticmethod
    async def mutate(
        _: Any, info: GrapheneInfo, input: TurnoverGoalsUpdateInput
    ) -> TurnoverGoalsUpdate:
        turnover_goals_model = TurnoverGoal()
        turnover_goals_model.site_id = input.site_id
        turnover_goals_model.org_id = input.org_id
        turnover_goals_model.goal_minutes = input.goal_minutes
        turnover_goals_model.max_minutes = (
            input.max_minutes if input.max_minutes is not None else DEFAULT_MAX_TURNOVER_MINS
        )

        updated_turnover_goals = await info.context.turnover_service.update_goals(
            turnover_goals_model
        )

        return TurnoverGoalsUpdate(success=True, updated_turnover_goals=updated_turnover_goals)
