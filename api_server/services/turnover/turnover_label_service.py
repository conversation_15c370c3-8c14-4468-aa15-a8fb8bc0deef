from dataclasses import dataclass
from typing import Optional
from api_server.services.turnover.turnover_label_store import TurnoverLabelModel, TurnoverLabelStore


@dataclass
class TurnoverLabelService:
    turnover_label_store: TurnoverLabelStore

    # Get all turnover labels by the given type
    async def query_turnover_labels(
        self, label_type: Optional[str] = None
    ) -> list[TurnoverLabelModel]:
        return await self.turnover_label_store.query_turnover_labels(label_type)
