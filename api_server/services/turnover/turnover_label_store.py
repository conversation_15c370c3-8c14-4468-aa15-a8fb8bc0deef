from typing import Optional
import uuid
from sqlalchemy import String, UniqueConstraint, text, select
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.dialects.postgresql import UUID

from databases.sql import Base, new_async_session
from databases.sql.helpers import async_postgres_insert_on_conflict_do_update_helper


class TurnoverLabelModel(Base):
    __tablename__ = "turnover_labels"
    __table_args__ = (
        UniqueConstraint(
            "name",
            "type",
            name="turnover_label_constraint",
        ),
    )
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=text("gen_random_uuid()"),
        nullable=False,
    )
    name: Mapped[str] = mapped_column(String, nullable=False)
    type: Mapped[str] = mapped_column(String, nullable=False)


class TurnoverLabelStore:
    # Retrieve turnover labels, filtering by type. If label_type is None, return all labels
    async def query_turnover_labels(
        self, label_type: Optional[str] = None
    ) -> list[TurnoverLabelModel]:
        async with new_async_session() as session:
            query = select(TurnoverLabelModel)
            if label_type:
                query = query.where(TurnoverLabelModel.type == label_type)

            return list((await session.scalars(query)).unique().all())

    async def upsert_turnover_label(self, model: TurnoverLabelModel) -> TurnoverLabelModel:
        async with new_async_session() as session:
            new_model_ids = await async_postgres_insert_on_conflict_do_update_helper(
                session=session,
                subject=TurnoverLabelModel,
                values=[model],
                returning=TurnoverLabelModel.id,
                index_elements=[TurnoverLabelModel.name, TurnoverLabelModel.type],
            )
            await session.commit()

            assert len(new_model_ids) == 1

            return (
                (
                    await session.scalars(
                        select(TurnoverLabelModel).filter(TurnoverLabelModel.id == new_model_ids[0])
                    )
                )
                .unique()
                .one()
            )
