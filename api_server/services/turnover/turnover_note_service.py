from dataclasses import dataclass
from typing import Op<PERSON>, <PERSON><PERSON>
from api_server.logging.audit import log_calls_to_audit
from api_server.services.turnover.turnover_note_store import (
    TurnoverNoteModel,
    TurnoverNoteStore,
)


@dataclass
class TurnoverNoteService:
    turnover_note_store: TurnoverNoteStore

    @log_calls_to_audit()
    async def query_turnover_note(
        self,
        preceding_case_id: str,
        following_case_id: str,
    ) -> Optional[str]:
        return await self.turnover_note_store.query_turnover_note(
            preceding_case_id, following_case_id
        )

    @log_calls_to_audit()
    async def query_turnover_notes_by_cases(
        self, case_ids: list[Tuple[str, str]]
    ) -> list[TurnoverNoteModel]:
        return await self.turnover_note_store.query_turnover_notes_by_cases(case_ids)

    @log_calls_to_audit()
    async def upsert_turnover_note(self, model: TurnoverNoteModel) -> TurnoverNoteModel:
        return await self.turnover_note_store.upsert_turnover_note(model)

    @log_calls_to_audit()
    async def delete_turnover_note_by_case_id(
        self, preceding_case_id: str, following_case_id: str
    ) -> int:
        return await self.turnover_note_store.delete_turnover_note_by_case_id(
            preceding_case_id, following_case_id
        )
