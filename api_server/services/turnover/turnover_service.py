from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from typing import List, Sequence, Optional

from api_server.logging.audit import log_calls_to_audit
from api_server.services.apella_case.apella_case_service import ApellaCaseService
from api_server.services.apella_case.apella_case_store import (
    ApellaCase,
)
from api_server.services.events.event_models import EventModel
from api_server.services.phases.phase_type import MAX_PHASE_LENGTH
from api_server.services.turnover.turnover_models import Turnover, TurnoverType, TurnoverStatus
from api_server.services.turnover.turnover_note_service import TurnoverNoteService
from api_server.services.turnover.turnover_store import TurnoverGoal, TurnoverStore
from auth.auth import Auth
from auth.auth_decorator import requires_permissions
from auth.permissions import READ_ANY_EVENT, WRITE_ANY_EVENT
from api_server.services.turnover.turnover_utils import generate_turnover_id, get_turnover_status

DEFAULT_MAX_TURNOVER_MINS = 120


@dataclass
class TurnoverQuery:
    min_end_time: datetime
    max_start_time: datetime


@dataclass
class TurnoverService:
    auth: Auth
    apella_case_service: ApellaCaseService
    turnover_store: TurnoverStore
    turnover_note_service: TurnoverNoteService

    @requires_permissions(READ_ANY_EVENT)
    async def generate_turnover(
        self, preceding_case: ApellaCase, following_case: ApellaCase
    ) -> Turnover:
        if preceding_case.end_time is None:
            raise ValueError("preceding_case.end_time may not be None when generating turnover")

        if preceding_case.end_time > following_case.start_time:
            raise ValueError("preceding_case must end before following case starts")

        now_utc = datetime.now(timezone.utc)
        turnover_type = (
            TurnoverType.FORECAST
            if preceding_case.end_time > now_utc
            else TurnoverType.COMPLETE
            if following_case.start_time < now_utc
            else TurnoverType.LIVE
        )

        is_within_turnover_time_limit = (
            following_case.start_time - preceding_case.end_time
            <= timedelta(minutes=DEFAULT_MAX_TURNOVER_MINS)
        )

        return Turnover(
            id=generate_turnover_id(preceding_case.id, following_case.id),
            room_id=preceding_case.room_id,
            start_time=preceding_case.end_time,
            end_time=following_case.start_time,
            preceding_case=preceding_case,
            following_case=following_case,
            meets_inclusion_criteria=is_within_turnover_time_limit,
            type=turnover_type,
        )

    async def get_status(
        self, turnover: Turnover, events: list[EventModel]
    ) -> Optional[TurnoverStatus]:
        return get_turnover_status(turnover, events)

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_EVENT)
    async def calculate_turnovers_for_room(self, cases: Sequence[ApellaCase]) -> List[Turnover]:
        turnovers = []
        sorted_cases = sorted(cases, key=lambda p: p.start_time)

        for i, case in enumerate(sorted_cases[:-1]):
            next_case = sorted_cases[i + 1]

            if (
                case.end_time is not None
                and next_case is not None
                and case.end_time < next_case.start_time
                and (next_case.start_time - case.end_time) <= MAX_PHASE_LENGTH
            ):
                turnovers.append(await self.generate_turnover(case, next_case))

        return turnovers

    @log_calls_to_audit()
    @requires_permissions(READ_ANY_EVENT)
    async def query_goals(self, site_ids: List[str]) -> Sequence[TurnoverGoal]:
        return await self.turnover_store.query_goals(site_ids)

    @log_calls_to_audit()
    @requires_permissions(WRITE_ANY_EVENT, enforce_universal_user=True)
    async def update_goals(self, turnover_goal: TurnoverGoal) -> TurnoverGoal:
        return await self.turnover_store.upsert_goals(turnover_goal)
