import uuid

import sqlalchemy
from sqlalchemy import String, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import mapped_column, Mapped

from databases.sql import Base
from databases.sql.mixins import (
    OrgIdMixin,
    TimestampMixin,
)
from http import HTTPStatus
from apella_cloud_api.exceptions import ClientError
from utils.helpers import _check_if_valid_uuid


class UserFilterViewModel(Base, OrgIdMixin, TimestampMixin):
    __tablename__ = "user_filter_views"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=sqlalchemy.text("gen_random_uuid()"),
    )
    name: Mapped[str] = mapped_column(String(128), nullable=False, index=True)
    url: Mapped[str] = mapped_column(String, nullable=False)
    user_id: Mapped[str] = mapped_column(String, primary_key=True, index=True)

    __table_args__ = (
        UniqueConstraint(
            name,
            user_id,
        ),
    )

    def __repr__(self) -> str:
        return (
            f"<UserFilterViewModel(id='{self.id}', name='{self.name}', user_id='{self.user_id}')>"
        )

    def validate(self) -> None:
        if self.id is None:
            self.id = str(uuid.uuid4())
        elif not _check_if_valid_uuid(str(self.id)):
            raise ClientError(
                status_code=HTTPStatus.UNPROCESSABLE_ENTITY,
                message="'id' must be a valid uuid",
            )

        trimmed_name = self.name.strip()
        if len(trimmed_name) == 0:
            raise ClientError(
                status_code=HTTPStatus.UNPROCESSABLE_ENTITY,
                message="'name' must not be empty",
            )
        if len(trimmed_name) < 3:
            raise ClientError(
                status_code=HTTPStatus.UNPROCESSABLE_ENTITY,
                message="'name' must be at least 3 characters",
            )
        if len(trimmed_name) > 128:
            raise ClientError(
                status_code=HTTPStatus.UNPROCESSABLE_ENTITY,
                message="'name' must be less than 128 characters",
            )
        self.name = trimmed_name

        trimmed_url = self.url.strip()
        if len(trimmed_url) == 0:
            raise ClientError(
                status_code=HTTPStatus.UNPROCESSABLE_ENTITY,
                message="'url' must not be empty",
            )
        self.url = trimmed_url
