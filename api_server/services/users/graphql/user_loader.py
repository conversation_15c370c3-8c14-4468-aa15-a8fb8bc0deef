import asyncio
from aiodataloader import DataLoader

from api_server.services.users.user_service import UserService
from api_server.services.users.user_store import User


class UserLoader(DataLoader[str, User | BaseException]):
    user_service: UserService

    def __init__(self, user_service: UserService):
        super().__init__()
        self.user_service = user_service

    async def batch_load_fn(self, keys: list[str]) -> list[User | BaseException]:
        return await asyncio.gather(
            *[self.user_service.get_user(user_id=user_id) for user_id in keys],
            return_exceptions=True,
        )
