# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

import datetime
from dataclasses import dataclass
from datetime import date, timedelta
from enum import Enum, auto
from typing import Union


class BucketSize(str, Enum):
    MINUTE = auto()
    DAY = auto()
    WEEK = auto()
    MONTH = auto()


@dataclass
class MetricBucket:
    date: date
    value: float
    # TODO: Deprecate BucketSize in favor of timedelta.
    bucket_size: Union[BucketSize, timedelta]


@dataclass
class MetricFloat:
    metric: str
    value: float


@dataclass
class MultiMetricBucketFloat:
    date: date
    bucket_size: timedelta
    metrics: list[MetricFloat]


@dataclass
class MetricBucketDuration:
    date: date
    value: timedelta
    bucket_size: Union[BucketSize, timedelta]


def get_bucket_date(date: datetime.date, bucket_size: BucketSize):
    """Returns the first day of the bucket that a given date corresponds to"""
    if bucket_size == BucketSize.WEEK:
        # Weeks are Monday - Sunday
        num_days_from_monday = date.weekday()
        return date - datetime.timedelta(days=num_days_from_monday)
    elif bucket_size == BucketSize.MONTH:
        num_days_from_beginning_of_month = date.day - 1
        return date - datetime.timedelta(days=num_days_from_beginning_of_month)
    else:
        return date
