from typing import Optional, Sequence

from opentelemetry.sdk.trace.sampling import (
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    TraceIdRatioBased,
    ParentBasedTraceIdRatio,
)
from opentelemetry.context import Context
from opentelemetry.trace import Link, SpanKind
from opentelemetry.trace.span import TraceState
from opentelemetry.util.types import Attributes

_SPAN_SAMPLE_RATE = 0.01


class ApellaTraceSampler(Sampler):
    """
    A request sampler that determines whether a trace should be exported based on properties of a span.

    Current logic:
    - If the span has a defined custom rate then we sample based on that rate in `_rates_by_operation_names`.
    - Otherwise, fallback to a probabilistic sampler.

    In the future, this can be expanded to automatically sample based on incoming request's
    HTTP header values, useful for doing distributed tracing.
    """

    _rates_by_operation_names: dict[str, float]

    def __init__(self, rates_by_operation_names: dict[str, float]):
        self._rates_by_operation_names = (
            rates_by_operation_names if rates_by_operation_names else {}
        )
        self._default_sampler = ParentBasedTraceIdRatio(_SPAN_SAMPLE_RATE)

    def add_custom_rate(self, operation_name: str, rate: float) -> None:
        self._rates_by_operation_names[operation_name] = rate

    def should_sample(
        self,
        parent_context: Optional["Context"],
        trace_id: int,
        name: str,
        kind: Optional[SpanKind] = None,
        attributes: Attributes = None,
        links: Optional[Sequence["Link"]] = None,
        trace_state: Optional["TraceState"] = None,
    ) -> SamplingResult:
        operation_name = str(attributes.get("operationName", "")) if attributes else ""
        if operation_name in self._rates_by_operation_names:
            sample_rate: float = self._rates_by_operation_names[operation_name]
            return TraceIdRatioBased(sample_rate).should_sample(
                parent_context,
                trace_id,
                name,
                kind,
                attributes,
                links,
                trace_state,
            )
        # Fallback to the default sampler
        return self._default_sampler.should_sample(
            parent_context, trace_id, name, kind, attributes, links, trace_state
        )

    def get_description(self) -> str:
        return "ApellaTraceSampler"
