from contextlib import contextmanager
from functools import wraps
from typing import (
    Callable,
    Optional,
    TypeVar,
    Coroutine,
    Any,
    Generator,
)

from typing_extensions import ParamSpec

from api_server.tracing import ApellaTracer

P = ParamSpec("P")
T = TypeVar("T")


@contextmanager
def start_span(span_name_override: Optional[str], func_name: str) -> Generator[None, None, None]:
    span_name = span_name_override if span_name_override is not None else func_name
    with ApellaTracer.tracer.start_as_current_span(span_name):
        yield


def traced(span_name_override: Optional[str] = None) -> Callable[[Callable[P, T]], Callable[P, T]]:
    def decorator(func: Callable[P, T]) -> Callable[P, T]:
        @wraps(func)
        def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            with start_span(span_name_override, func.__qualname__):
                return func(*args, **kwargs)

        return wrapper

    return decorator


def async_traced(
    span_name_override: Optional[str] = None,
) -> Callable[[Callable[P, Coroutine[Any, Any, T]]], Callable[P, Coroutine[Any, Any, T]]]:
    def decorator(func: Callable[P, Coroutine[Any, Any, T]]) -> Callable[P, Coroutine[Any, Any, T]]:
        @wraps(func)
        async def async_wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            with start_span(span_name_override, func.__qualname__):
                return await func(*args, **kwargs)

        return async_wrapper

    return decorator


def class_traced() -> Callable[[type], type]:
    """
    Traces all callable methods in a class, sync and async.
    Does not trace:
     * staticmethods. Apply traced() directly from within the staticmethod invocation.
    Preserves static methods while applying tracing.
    """

    def decorator(cls: type) -> type:
        for attr_name, attr_value in cls.__dict__.items():
            if not callable(attr_value):
                continue

            if isinstance(attr_value, staticmethod):
                continue

            if hasattr(attr_value, "_is_coroutine") or hasattr(attr_value, "__await__"):
                wrapped = async_traced()(attr_value)
            else:
                wrapped = traced()(attr_value)

            setattr(cls, attr_name, wrapped)
        return cls

    return decorator
