from http import HTTPStatus
from typing import Any, List, Mapping, Optional

import jwt
from fastapi import <PERSON><PERSON><PERSON><PERSON>xception
from jwt import PyJWKClient

import config
from apella_cloud_api.exceptions import NotFound
from api_server import logging
from api_server.server.middleware.request_claims_provider import (
    get_jwt_cookie_claims,
    get_request_auth_header_claims,
)
from auth import AuthUser, claims
from auth.auth0 import Auth0

# Error handler
from auth.permissions import READ_ANY_SITE, READ_SITE_PREFIX


class AuthError(HTTPException):
    def __init__(self, error_code: str, message: str, status_code: HTTPStatus):
        super(AuthError, self).__init__(
            status_code=status_code,
            detail=message,
        )
        self.error_code = error_code
        self.message = message

    def __str__(self) -> str:
        return self.message


class Auth:
    auth0_client: Auth0
    auth_audience: str
    jwks_clients: dict[str, PyJWKClient]

    def __init__(self, auth0_client: Auth0, auth_audience: str, jwt_issuers: dict[str, str]):
        self.auth0_client = auth0_client
        self.auth_audience = auth_audience

        # Convert the issuer definitions to JWK clients
        # Each JWT header has a `kid` (Key ID) which is the id of the key in the public key
        # `jwks.json` This is because the public key can rotate, and there may be multiple in
        # the file at one time.  So this JWKS Client will fetch the right signing key from this
        # file, and it caches keyed by `kid`
        # See https://pyjwt.readthedocs.io/en/latest/usage.html
        self.jwks_clients = {}
        for issuer, uri in jwt_issuers.items():
            self.jwks_clients[issuer] = PyJWKClient(uri)

    def get_verified_jwt_claims(self) -> dict[str, Any]:
        token = get_jwt_from_request()
        try:
            # First get unverified claims to get the issuer of the token
            unverified_claims = jwt.decode(token, options={"verify_signature": False})
            issuer = unverified_claims.get("iss", None)
            if issuer is None:
                raise AuthError("missing_jwt_iss", "JWT Issuer Missing", HTTPStatus.UNAUTHORIZED)

            # And get the client for the issuer
            jwks_client = self.jwks_clients.get(issuer)
            if jwks_client is None:
                raise AuthError(
                    "unknown_jwt_iss", f"JWT Issuer Unknown: {issuer}", HTTPStatus.UNAUTHORIZED
                )

            # Get the public key used to sign this jwt
            signing_key = jwks_client.get_signing_key_from_jwt(token).key
            algorithm = "RS256"

            # Then, we use this key to verify during decode
            claims = jwt.decode(
                token, signing_key, algorithms=[algorithm], audience=self.auth_audience
            )
            return claims
        except jwt.exceptions.InvalidAudienceError:
            raise AuthError("invalid_jwt_aud", "JWT Audience Invalid", HTTPStatus.UNAUTHORIZED)
        except jwt.exceptions.InvalidIssuerError:
            raise AuthError("invalid_jwt_iss", "JWT Issuer Invalid", HTTPStatus.UNAUTHORIZED)
        except jwt.exceptions.InvalidAlgorithmError:
            raise AuthError("invalid_jwt_alg", "JWT Algorithm Invalid", HTTPStatus.UNAUTHORIZED)
        except jwt.exceptions.InvalidSignatureError:
            raise AuthError("invalid_jwt_sig", "JWT Signature Invalid", HTTPStatus.UNAUTHORIZED)
        except jwt.exceptions.DecodeError:
            raise AuthError("invalid_jwt_dec", "JWT Decode Error", HTTPStatus.UNAUTHORIZED)
        except jwt.exceptions.ExpiredSignatureError:
            raise AuthError("expired_jwt_sig", "JWT Signature Expired", HTTPStatus.UNAUTHORIZED)

    def get_calling_user_id(self) -> str:
        """
        Gets the Auth0 user_id associated with this authenticated HTTP request
        """
        jwt_claims = self.get_verified_jwt_claims()

        issuer = jwt_claims["iss"]
        if self.is_issuer_from_auth0(issuer=issuer):
            # This is coming from auth0, where the user id in the subject of the jwt token
            return jwt_claims["sub"]

        if self.is_issuer_from_gcp(issuer):
            # This is coming from google, where the Auth0 user id is in the email of the jwt token.
            # The `sub` is the GCP user ID. We chose to make the user ID unique and discoverable
            # by selecting the email address.
            email = jwt_claims.get("email")
            if email is not None:
                return email

        # This is not a user, or the issue is unknown.
        raise AuthError(
            "not_authorized", "No user associated with request", HTTPStatus.UNAUTHORIZED
        )

    def is_issuer_from_auth0(self, issuer: str) -> bool:
        auth0_iss_claim = f"{config.auth0_url()}/"
        return issuer == auth0_iss_claim

    def is_issuer_from_gcp(self, issuer: str) -> bool:
        return issuer == "https://accounts.google.com"

    def safe_get_calling_user_id(self) -> str:
        """
        Gets the user_id or identity associated with the caller, safely.
        Returns "Unknown" if there's an exception. Useful for callers like logging.

        Should not ever cause an exception.
        """
        try:
            return self.get_calling_user_id()
        except Exception as e:
            return f"Unknown: {str(e)}"

    def get_claim_value(self, claim_key: str) -> Optional[str]:
        """
        Retrieves the specified claim value from the JWT claims.

        Args:
            claim_key (str): The key of the claim to retrieve.

        Returns:
            Optional[str]: The value of the claim if present, otherwise None.
        """
        try:
            jwt_claims = self.get_verified_jwt_claims()
            return jwt_claims.get(claim_key)
        except AuthError:
            # Handle auth errors gracefully to allow non-authorized environments to work
            return None

    def get_calling_org_id(self) -> Optional[str]:
        """
        Gets the user's org_id claim value from the `https://apella.io/org_id` claim.
        """
        return self.get_claim_value(claims.APELLA_ORG_ID)

    def get_calling_auth0_org_id(self) -> Optional[str]:
        """
        Gets the user's specific authenticated auth0_org_id claim value.
        """
        return self.get_claim_value(claims.AUTH0_ORG_ID)

    def get_calling_site_ids(self) -> Optional[list[str]]:
        """
        Gets the user's specific authenticated site_ids.
        The authenticated site_ids are stored as permissions in the form `site:read:{site_id}`.
        If the user has the `site:read:any` permission, this returns None.
        """
        try:
            permissions = self.get_permissions()
            site_id_permissions = list(
                filter(
                    lambda permission: permission.startswith(READ_SITE_PREFIX)
                    and permission != READ_ANY_SITE,
                    permissions,
                )
            )
            site_ids = [permission[len(READ_SITE_PREFIX) :] for permission in site_id_permissions]
            return None if len(site_ids) == 0 else site_ids
        except AuthError:
            # If there is some issue with auth, then there is no site specified.
            # We don't want to pass the error along, because that would prevent
            # local (non-authorized) environments from working.
            return None

    def get_auth0_permissions(
        self, verified_claims: Mapping[str, Any], enforce_universal_user: bool
    ) -> list[str]:
        jwt_auth0_org_id = verified_claims.get(claims.AUTH0_ORG_ID)

        # The user is authenticated through auth0.
        # The user's permissions should be in the JWT's `permissions` claims.
        # If they are not there, or empty, fallback to querying them from Auth0 directly.
        permissions_from_jwt = verified_claims.get(claims.PERMISSIONS, [])
        if len(permissions_from_jwt) > 0:
            return permissions_from_jwt

        logging.info("Retrieving permissions from Auth0")
        # No org_id is specified. Fetch the user's global permissions
        if jwt_auth0_org_id is None:
            return self.auth0_client.get_permissions(verified_claims)

        # The user is logged into an org. Fetch permissions in org.
        return self.auth0_client.get_permissions_in_org(
            jwt_token=verified_claims, auth0_org_id=jwt_auth0_org_id
        )

    def get_permissions(self, enforce_universal_user: bool = False) -> list[str]:
        """
        Get the user's permissions.
        1. If the user is authenticated through Auth0, get permissions from JWT's `permissions` claims or Auth0
        2. If the user is a service account, also get permissions from Auth0
        3. Otherwise, read permissions from the JWT's `permission` claim
        """
        # First check the caller org id, and see if this permissions should be universal
        caller_org_id = self.get_calling_org_id()

        if enforce_universal_user and caller_org_id is not None:
            return []

        # Get the jwt token from the header and parse it
        verified_claims = self.get_verified_jwt_claims()

        issuer: str = verified_claims[claims.ISSUER]

        if self.is_issuer_from_auth0(issuer=issuer):
            return self.get_auth0_permissions(
                verified_claims=verified_claims,
                enforce_universal_user=enforce_universal_user,
            )

        if self.is_issuer_from_gcp(issuer):
            # This is coming from google.  We need a way to find permissions for these accounts.
            # We can get the email address
            email = verified_claims.get("email")
            if email is None:
                # Very strange but can happen for compute instances
                raise AuthError(
                    error_code="email_unknown",
                    status_code=HTTPStatus.FORBIDDEN,
                    message="email is not provided in JWT",
                )

            if not email.endswith(".iam.gserviceaccount.com"):
                # Currently, we only want to grant service account permissions this way
                # But in the future this check could be removed to allow personal accounts, too
                raise AuthError(
                    error_code="email_unknown",
                    status_code=HTTPStatus.FORBIDDEN,
                    message="email provider is unknown in JWT",
                )

            # We also have some service accounts permissions defined in Auth0 under the same email
            try:
                user: AuthUser = self.auth0_client.get_user_info_for_email(email)
            except NotFound:
                raise AuthError(
                    "not_authorized", f"Service account not found: {email}", HTTPStatus.UNAUTHORIZED
                )

            return self.auth0_client.get_permissions_for_subject(user.user_id)

        # This JWT was signed by a certificate that we trust,
        # but from an issuer that we cannot verify the permissions via a separate API request.
        # This generally happens when a service account signs its own JWT token,
        # so we can just trust that the permissions are up-to-date.
        return verified_claims.get(claims.PERMISSIONS, [])

    def has_permission(
        self, required_permission: str, enforce_universal_user: bool = False
    ) -> bool:
        """
        Checks if the calling user has the specified permission.
        """
        permissions = self.get_permissions(enforce_universal_user)
        return required_permission in permissions

    def has_permission_prefix(
        self, required_permission_prefix: str, enforce_universal_user: bool = False
    ) -> bool:
        """
        Checks if the calling user has any permission with a specified prefix.
        This method is useful for checking permissions with IDs, ie: `site:read:{site_id}`
        """
        permissions = self.get_permissions(enforce_universal_user)

        for permission in permissions:
            if permission.startswith(required_permission_prefix):
                return True
        return False

    def requires(self, *permissions: str, enforce_universal_user: bool = False) -> None:
        for permission in permissions:
            if not self.has_permission(permission, enforce_universal_user):
                raise AuthError(
                    "not_authorized",
                    f"User does not have permission '{permission}'",
                    HTTPStatus.UNAUTHORIZED,
                )

    def requires_any(self, permissions: List[str], enforce_universal_user: bool = False) -> None:
        for permission in permissions:
            if self.has_permission(permission, enforce_universal_user):
                return
        raise AuthError(
            "not_authorized",
            f"User does not have any permission in '{permissions}'",
            HTTPStatus.UNAUTHORIZED,
        )

    def requires_permission_prefix(
        self, permission_prefix: str, enforce_universal_user: bool = False
    ) -> None:
        if not self.has_permission_prefix(permission_prefix, enforce_universal_user):
            raise AuthError(
                "not_authorized",
                f"User does not have permission '{permission_prefix}'",
                HTTPStatus.UNAUTHORIZED,
            )

    def check_resource_matches_auth(self, org_id: str, site_id: Optional[str]) -> None:
        """
        Check the entity's org_id and site_id to ensure it matches the user's authorized org_id and site_ids.
        """
        # Organization ID check
        caller_org_id = self.get_calling_org_id()
        if caller_org_id is not None and caller_org_id != org_id:
            raise AuthError(
                "not_authorized",
                f"User does not have permission to access or "
                f"create resource with org_id: {org_id}.",
                HTTPStatus.UNAUTHORIZED,
            )

        # Site ID check
        caller_site_ids = self.get_calling_site_ids()
        if site_id is not None and caller_site_ids is not None and site_id not in caller_site_ids:
            raise AuthError(
                "not_authorized",
                f"User does not have permission to access or "
                f"create resource with site_id: {site_id}.",
                HTTPStatus.UNAUTHORIZED,
            )

    async def require_permission_if_not_self(
        self, user_id: str, permission: str, enforce_universal_user: bool = False
    ) -> None:
        if user_id != self.get_calling_user_id():
            self.requires(permission, enforce_universal_user=enforce_universal_user)


def get_jwt_from_request() -> str:
    """
    Obtains the JWT from various header fields
    """

    # Sometimes in GCP, such as with API Gateway, GCP does authentication
    # and then forwards the
    # API Gateway authenticates, and then forwards the header as "X-Forwarded-Authorization"
    # We do not use API gateway anymore, but this might as well be kept in.
    auth_header = get_request_auth_header_claims()

    if auth_header:
        # The Authorization and X-Forwarded-Authorization headers have the format
        # "Bearer $JWT", so we need to extract the JWT from it
        parts = auth_header.split()

        if parts[0].lower() != "bearer":
            raise AuthError(
                "invalid_header",
                "Authorization header must start with Bearer",
                HTTPStatus.UNAUTHORIZED,
            )
        elif len(parts) == 1:
            raise AuthError("invalid_header", "Token not found", HTTPStatus.UNAUTHORIZED)
        elif len(parts) > 2:
            raise AuthError(
                "invalid_header",
                "Authorization header must be Bearer token",
                HTTPStatus.UNAUTHORIZED,
            )

        token = parts[1]
        return token

    # Sometimes we do auth using cookies, so also try there:
    cookie_jwt = get_jwt_cookie_claims()
    if cookie_jwt:
        # The JWT is just stored as-is in this cookie
        return cookie_jwt

    raise AuthError(
        "not_authorized", "Not Authorized: No authorization found", HTTPStatus.UNAUTHORIZED
    )
