import logging
import time
from http import HTTPStatus
from typing import Any, List, Mapping, Optional, Sequence
from urllib.parse import quote

import aiohttp
import requests
from jose import jwt

from apella_cloud_api.exceptions import ClientError, NotFound
from auth import AuthOrganization, AuthUser
from auth.auth0_retry_cache import (
    auth0_retry_cache,
    async_auth0_retry_cache,
    auth0_retry_cache_with_network_retry,
)

# To explore other apis auth0 has, see https://auth0.com/docs/api/management/v2


# The number of seconds before the jwt token expires to get a new one
JWT_EXPIRATION_BUFFER = 30


class Auth0:
    """
    Auth0 class acts as a basic Auth0 client.
    """

    # Timeout for request completion. In seconds.
    request_timeout = 15

    def __init__(
        self,
        auth0_url: str,
        auth_audience: str,
        auth0_client_id: str,
        auth0_client_secret: str,
        auth0_default_domain: str,
    ):
        self.auth0_url = auth0_url
        self.auth_audience = auth_audience
        self.auth0_client_id = auth0_client_id
        self.auth0_client_secret = auth0_client_secret
        self.auth0_default_domain = auth0_default_domain

        self.jwt: Optional[str] = None
        self.jwt_expiration = 0.0

    def get_permissions(self, jwt_token: Mapping[str, Any]) -> List[str]:
        """
        Gets up to date permissions for the user authenticated with the jwt token
        """
        if "sub" not in jwt_token:
            raise RuntimeError("JWT token does not have a subject")
        permissions = self.get_permissions_for_subject(jwt_token["sub"])
        return permissions

    def get_permissions_in_org(self, jwt_token: Mapping[str, Any], auth0_org_id: str) -> List[str]:
        """
        Gets up to date permissions for the user authenticated with the jwt token,
        in the org specified
        """
        # For some reason, there is not currently an API to get these permissions in a
        # single call, so we first have to get the roles
        if "sub" not in jwt_token:
            raise RuntimeError("JWT token does not have a subject")
        roles = self.get_users_roles_in_org(jwt_token["sub"], auth0_org_id)
        # Then from the roles, get all the permissions
        permissions = set()
        for role in roles:
            permissions.update(self.get_permissions_granted_by_role(role))
        return list(permissions)

    # This decorator adds a cache for this function, which expires the permissions based on the
    # configuration
    @auth0_retry_cache
    def get_permissions_for_subject(self, sub: str) -> list[str]:
        """
        Gets the permissions for the jwt subject.
        This function is expensive, so it should be cached.
        """

        # sub is from the jwt token
        # and it can be different things
        if sub.endswith("@clients"):
            # Then its a machine client, so first remove this suffix
            client_id = sub[:-8]  # 8 is length of '@clients'
            # Then use the client-grants endpoint, filtered by audience and client_id
            req = requests.request(
                "GET",
                url=f"{self.auth0_url}/api/v2/client-grants",
                headers=self.get_auth0_management_api_headers(),
                params={"audience": self.auth_audience, "client_id": client_id},
                timeout=self.request_timeout,
            )
            if req.status_code == HTTPStatus.TOO_MANY_REQUESTS:
                raise ClientError(
                    HTTPStatus.TOO_MANY_REQUESTS, "Failed to get permissions: Too many requests"
                )
            if req.status_code != HTTPStatus.OK:
                raise RuntimeError(f"Failed to get permissions: {req.status_code} {req.text}")

            grants: list[dict[str, Any]] = req.json()

            if len(grants) > 0:
                # the permissions are in the 'scope'
                grant = grants[0]
                if "scope" not in grant:
                    raise RuntimeError(f"Client grant {grant} does not have a scope")
                return grant["scope"]
            return []
        else:
            # 'sub' for a user has an authentication scheme,
            # like auth0, or google-oauth2, then an id, examples:
            # - auth0|123456
            # - google-oath2|4567890
            # Instead of having a list for all these different types,
            # we assume anything thats not a machine must
            # be a user.  Hopefully this assumption holds.
            req = requests.request(
                "GET",
                url=f"{self.auth0_url}/api/v2/users/{sub}/permissions",
                headers=self.get_auth0_management_api_headers(),
                timeout=self.request_timeout,
            )
            if req.status_code == HTTPStatus.TOO_MANY_REQUESTS:
                raise ClientError(
                    HTTPStatus.TOO_MANY_REQUESTS, "Failed to get permissions: Too many requests"
                )
            if req.status_code != HTTPStatus.OK:
                raise RuntimeError(f"Failed to get permissions: {req.status_code} {req.text}")

            all_permissions = req.json()
            # These permissions are across all backends, so filter by this backend
            permissions = []
            for permission in all_permissions:
                if (
                    permission.get("resource_server_identifier") == self.auth_audience
                    and "permission_name" in permission
                ):
                    permissions.append(permission["permission_name"])
            return permissions

    @auth0_retry_cache_with_network_retry
    def get_user_info_for_email(self, email_address: str) -> AuthUser:
        response = requests.request(
            "GET",
            url=f"{self.auth0_url}/api/v2/users-by-email",
            headers=self.get_auth0_management_api_headers(),
            params={"email": email_address},
            timeout=self.request_timeout,
        )
        if response.status_code == HTTPStatus.TOO_MANY_REQUESTS:
            raise ClientError(HTTPStatus.TOO_MANY_REQUESTS, "Failed to get user: Too many requests")
        if response.status_code != HTTPStatus.OK:
            raise RuntimeError(f"Failed to get user: {response.status_code} {response.text}")

        response_obj = response.json()
        if len(response_obj) > 1:
            raise ClientError(
                HTTPStatus.BAD_REQUEST, f"More than one user found with email: {email_address}"
            )
        if len(response_obj) == 0:
            raise NotFound(f"No user found with email: {email_address}")

        user_obj = response_obj[0]

        result: AuthUser = AuthUser(
            email=email_address,
            user_id=user_obj.get("user_id") or "",
            display_name=user_obj.get("name") or "",
        )
        return result

    @async_auth0_retry_cache
    async def get_user(self, auth0_user_id: str) -> AuthUser:
        timeout = aiohttp.ClientTimeout(total=self.request_timeout)
        headers = self.get_auth0_management_api_headers()
        url = f"{self.auth0_url}/api/v2/users/{auth0_user_id}"

        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers, timeout=timeout) as response:
                if response.status == HTTPStatus.TOO_MANY_REQUESTS:
                    raise ClientError(
                        HTTPStatus.TOO_MANY_REQUESTS, "Failed to get user: Too many requests"
                    )
                if response.status == HTTPStatus.NOT_FOUND:
                    raise NotFound(f"No user found with id: {auth0_user_id}")
                if response.status != HTTPStatus.OK:
                    raise RuntimeError(
                        f"Failed to get user: {response.status} {await response.text()}"
                    )

                user_obj = await response.json()

                return AuthUser(
                    email=user_obj.get("email") or "",
                    user_id=user_obj.get("user_id") or "",
                    display_name=user_obj.get("name") or "",
                )

    @auth0_retry_cache
    def get_users_roles_in_org(self, sub: str, auth0_org_id: str) -> list[str]:
        response = requests.request(
            "GET",
            url=f"{self.auth0_url}/api/v2/organizations/{auth0_org_id}/members/{sub}/roles",
            headers=self.get_auth0_management_api_headers(),
            timeout=self.request_timeout,
        )
        if response.status_code == HTTPStatus.TOO_MANY_REQUESTS:
            raise ClientError(
                HTTPStatus.TOO_MANY_REQUESTS, "Failed to get user roles in org: Too many requests"
            )
        if response.status_code != HTTPStatus.OK:
            raise RuntimeError(
                f"Failed to get user roles in org: {response.status_code} {response.text}"
            )

        return [role["id"] for role in response.json() if "id" in role]

    @auth0_retry_cache
    def get_permissions_granted_by_role(self, role_id: str) -> list[str]:
        response = requests.request(
            "GET",
            url=f"{self.auth0_url}/api/v2/roles/{role_id}/permissions",
            headers=self.get_auth0_management_api_headers(),
            timeout=self.request_timeout,
        )
        if response.status_code == HTTPStatus.TOO_MANY_REQUESTS:
            raise ClientError(
                HTTPStatus.TOO_MANY_REQUESTS,
                "Failed to get permissions granted by roles: Too many requests",
            )
        if response.status_code != HTTPStatus.OK:
            raise RuntimeError(
                "Failed to get permissions granted by roles: "
                f"{response.status_code} {response.text}"
            )

        return [
            permission["permission_name"]
            for permission in response.json()
            if "permission_name" in permission
        ]

    def assign_roles_to_user(self, auth_id: str, roles: Sequence[str]) -> None:
        response = requests.request(
            "POST",
            url=f"{self.auth0_url}/api/v2/users/{auth_id}/roles",
            headers=self.get_auth0_management_api_headers(),
            json={"roles": roles},
            timeout=self.request_timeout,
        )
        if response.status_code == HTTPStatus.TOO_MANY_REQUESTS:
            raise ClientError(
                HTTPStatus.TOO_MANY_REQUESTS, "Failed to assign roles: Too many requests"
            )
        if response.status_code != HTTPStatus.NO_CONTENT:
            raise RuntimeError(f"Failed to assign roles: {response.status_code} {response.text}")

    def get_auth0_management_api_headers(self) -> dict[str, str]:
        """Gets the headers required to access Auth0 Management API"""
        # Buffer to generate new jwt before old one expires
        if self.jwt_expiration < time.time() + JWT_EXPIRATION_BUFFER:
            self.get_jwt_from_auth()

        return {"Authorization": "Bearer {}".format(self.jwt)}

    def get_jwt_from_auth(self) -> None:
        """
        This function actually authenticates with Auth0 to get the API management JWT
        It stores the values in the `self.jwt` and `self.jwt_expiration` properties
        """
        response = requests.request(
            "POST",
            url=f"{self.auth0_url}/oauth/token",
            headers={"content-type": "application/json"},
            json={
                "client_id": self.auth0_client_id,
                "client_secret": self.auth0_client_secret,
                "audience": f"{self.auth0_default_domain}/api/v2/",
                "grant_type": "client_credentials",
            },
            timeout=self.request_timeout,
        )
        if response.status_code != HTTPStatus.OK:
            raise RuntimeError(
                f"Could not authorize with Auth0 backend: {response.status_code} {response.text}"
            )

        self.jwt = response.json().get("access_token")
        unverified_claims = jwt.get_unverified_claims(self.jwt)
        if "exp" not in unverified_claims:
            raise RuntimeError("Could not get expiration from jwt token")
        self.jwt_expiration = unverified_claims["exp"]

    @auth0_retry_cache
    def get_organizations_for_user(self, auth0_user_id: str) -> list[AuthOrganization]:
        response = requests.request(
            "GET",
            url=f"{self.auth0_url}/api/v2/users/{auth0_user_id}/organizations",
            headers=self.get_auth0_management_api_headers(),
            timeout=self.request_timeout,
        )
        if response.status_code != HTTPStatus.OK:
            raise RuntimeError(
                "Failed to get organizations belonging to user: "
                f"{response.status_code} {response.text}"
            )

        return [
            AuthOrganization(
                id=organization.get("id") or "",
                name=organization.get("name") or "",
                display_name=organization.get("display_name") or "",
            )
            for organization in response.json()
        ]

    async def get_users_in_organization(self, org_id: str) -> List[AuthUser]:
        return await self._get_users(
            f"{self.auth0_url}/api/v2/organizations/{org_id}/members", "members"
        )

    async def get_users_with_role(self, role_id: str) -> List[AuthUser]:
        return await self._get_users(f"{self.auth0_url}/api/v2/roles/{role_id}/users", "users")

    def get_role_id_by_name(self, role_name: str) -> str:
        response = requests.request(
            "GET",
            url=f"{self.auth0_url}/api/v2/roles?name_filter={quote(role_name)}",
            headers=self.get_auth0_management_api_headers(),
            timeout=self.request_timeout,
        )
        if response.status_code != HTTPStatus.OK:
            raise RuntimeError(f"Failed to get roles: {response.status_code} {response.text}")

        json = response.json()
        if len(json) != 1:
            raise RuntimeError(f"Expected 1 role with name {role_name}, got {len(json)}")

        role = json[0]
        if "id" not in role:
            raise RuntimeError(f"Role {role_name} does not have an id")

        return role["id"]

    # See https://auth0.com/docs/manage-users/user-search/view-search-results-by-page
    async def _get_users(
        self, url: str, key: str, per_page: int = 100, page: int = 0
    ) -> List[AuthUser]:
        request_url = f"{url}?per_page={per_page}&page={page}&include_totals=true"
        headers = self.get_auth0_management_api_headers()
        timeout = aiohttp.ClientTimeout(total=self.request_timeout)

        async with aiohttp.ClientSession() as session:
            async with session.get(request_url, headers=headers, timeout=timeout) as response:
                if response.status == HTTPStatus.TOO_MANY_REQUESTS:
                    raise ClientError(
                        HTTPStatus.TOO_MANY_REQUESTS, "Failed to get user: Too many requests"
                    )
                if response.status != HTTPStatus.OK:
                    raise RuntimeError(
                        f"Failed to get users: {response.status} {await response.text()}"
                    )

                json: Mapping[str, Any] = await response.json()
                user_response: Sequence[Mapping[str, Any]] = json[key]
                total: int = json.get("total") or 0

                result = [
                    AuthUser(
                        email=user.get("email") or "",
                        user_id=user.get("user_id") or "",
                        display_name=user.get("name") or "",
                    )
                    for user in user_response
                ]

                if (total >= 1000) or len(result) >= 1000:
                    logging.error(
                        "Auth0 returned 1000 or more users which isn't currently supported by their API."
                        " Likely users are missing from a response and we should investigate and implement"
                        f" a fix. url={request_url}"
                    )

                if len(user_response) < per_page or len(result) >= total:
                    return result

                next_page_users = await self._get_users(url, key, per_page, page + 1)
                return result + next_page_users
