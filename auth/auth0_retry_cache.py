from http import HTT<PERSON>tatus
from typing import Awaitable, Callable, TypeVar

import requests
from tenacity import (
    AsyncRetrying,
    retry,
    retry_if_exception,
    stop_after_attempt,
    wait_random_exponential,
)

import config
from apella_cloud_api.exceptions import ClientError
from utils.synchronized_ttl_cache import synchronized_ttl_cache, async_synchronized_ttl_cache

T = TypeVar("T")


MAX_REQUEST_RETRIES = 3
MAX_REQUEST_RETRY_DELAY_SECONDS = 5
MIN_REQUEST_RETRY_DELAY_SECONDS = 0.1

CACHE_TTL = config.permission_expiration_duration()


def auth0_retry_cache(func: Callable[..., T]) -> Callable[..., T]:
    """
    Store successful responses in an in-memory cache with a TTL.

    When Auth0 returns a `429 - Too Many Requests` response, retry the in-memory cache and the request
    with exponential backoff. For other exceptions, fail immediately.
    """

    return retry(
        retry=retry_if_exception(_too_many_requests_response),
        stop=stop_after_attempt(MAX_REQUEST_RETRIES),
        wait=wait_random_exponential(
            max=MAX_REQUEST_RETRY_DELAY_SECONDS,
            min=MIN_REQUEST_RETRY_DELAY_SECONDS,
            multiplier=1,
        ),
    )(synchronized_ttl_cache(ttl=CACHE_TTL)(func))


def async_auth0_retry_cache(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
    cached = async_synchronized_ttl_cache(ttl=CACHE_TTL)(func)

    async def wrapper(*args: object, **kwargs: object) -> T:
        retrying = AsyncRetrying(
            retry=retry_if_exception(_too_many_requests_response),
            stop=stop_after_attempt(MAX_REQUEST_RETRIES),
            wait=wait_random_exponential(
                max=MAX_REQUEST_RETRY_DELAY_SECONDS,
                min=MIN_REQUEST_RETRY_DELAY_SECONDS,
                multiplier=1,
            ),
        )

        async for attempt in retrying:
            with attempt:
                return await cached(*args, **kwargs)
        raise RuntimeError("Exceeded maximum retries")

    return wrapper


def _too_many_requests_response(exception: BaseException) -> bool:
    return (
        isinstance(exception, ClientError) and exception.status_code == HTTPStatus.TOO_MANY_REQUESTS
    )


def _should_retry_auth0_exception(e: BaseException) -> bool:
    """Retry on:
    - 429 rate limits (via reusable `_too_many_requests_response`)
    - Connection reset or timeout errors
    """
    return _too_many_requests_response(e) or isinstance(
        e, (requests.exceptions.ConnectionError, requests.exceptions.Timeout)
    )


def auth0_retry_cache_with_network_retry(func: Callable[..., T]) -> Callable[..., T]:
    """
    Store responses in a TTL cache and retry on:
    - 429 Too Many Requests
    - ConnectionError
    - Timeout
    """
    return retry(
        retry=retry_if_exception(_should_retry_auth0_exception),
        stop=stop_after_attempt(MAX_REQUEST_RETRIES),
        wait=wait_random_exponential(
            max=MAX_REQUEST_RETRY_DELAY_SECONDS,
            min=MIN_REQUEST_RETRY_DELAY_SECONDS,
            multiplier=1,
        ),
        reraise=True,
    )(synchronized_ttl_cache(ttl=CACHE_TTL)(func))
