# === Users ===
# Users are able to read their own account information without any explicit permission granted.
# For access to all users, such as for a service account that reads notification settings, it needs
# the permission:
READ_ANY_USER = "user:read:any"

# === Organizations ===
READ_ANY_ORG = "org:read:any"
WRITE_ANY_ORG = "org:write:any"

# === Sites ===
READ_ANY_SITE = "site:read:any"
WRITE_ANY_SITE = "site:write:any"
READ_SITE_PREFIX = "site:read:"
WRITE_SITE_LAUNCH = "site:write:launch"

# === ROOMS ===
READ_ANY_ROOM = "room:read:any"
WRITE_ANY_ROOM = "room:write:any"
WRITE_CONFIGURATION_ROOM = "room:write:configuration"

# === Cameras ===
READ_CAMERA_PREFIX = "camera:read"
READ_ANY_CAMERA = "camera:read:any"
WRITE_ANY_CAMERA = "camera:write:any"
READ_CAMERA_DEFAULT = "camera:read:default"

# === CASES ===
# Most users will be able to read cases in their org
READ_ANY_CASE = "case:read:any"
# The case schedule importing service account needs to view and modify all cases, so it would have:
WRITE_ANY_CASE = "case:write:any"

# === STAFFING NEEDS ===
READ_ANY_STAFFING_NEEDS = "staffing_needs:read:any"
WRITE_ANY_STAFFING_NEEDS = "staffing_needs:write:any"

# === CASE NOTES PLAN ===
READ_ANY_CASE_NOTE_PLAN = "case_note_plan:read:any"
WRITE_ANY_CASE_NOTE_PLAN = "case_note_plan:write:any"

# === CASE STAFF PLAN ===
READ_ANY_CASE_STAFF_PLAN = "case_staff_plan:read:any"
WRITE_ANY_CASE_STAFF_PLAN = "case_staff_plan:write:any"

# === EVENTS ===
WRITE_ANY_EVENT = "event:write:any"
READ_ANY_EVENT = "event:read:any"

# === EVENT TYPES ===
WRITE_ANY_EVENT_TYPE = "event_type:write:any"

# === MAPPINGS ===
# Service accounts may have this permission to get mapping from external to internal ids
READ_MAPPINGS = "mapping:read:any"
# Apella devs may have this permission to add that mapping when onboarding new customers
WRITE_MAPPINGS = "mapping:write:any"

# === MEDIA_ASSETS ===
# Permissions to create or read media assets
WRITE_ANY_MEDIA_ASSET = "media_asset:write:any"
READ_ANY_MEDIA_ASSET = "media_asset:read:any"
READ_MEDIA_ASSET_IF_ASSIGNED = "media_asset:read_if_assigned"

# === OBJECT ===
# Permissions to read objects data stream
READ_ANY_OBJECT = "object:read:any"

# === LIVE STREAM ===
READ_ANY_LIVE_STREAM = "live_stream:read:any"

# === HIGHLIGHTS ===
WRITE_ANY_HIGHLIGHT = "highlight:write:any"
READ_ANY_HIGHLIGHT = "highlight:read:any"
READ_HIGHLIGHT_IF_ASSIGNED = "highlight:read_if_assigned"

# === FEEDBACK ===
READ_ANY_FEEDBACK = "feedback:read:any"
WRITE_FEEDBACK_IF_ASSIGNED = "feedback:write_if_assigned"

# === ANNOTATION TASKS ===
READ_ANY_ANNOTATION_TASK = "annotation_task:read:any"
WRITE_ANY_ANNOTATION_TASK = "annotation_task:write:any"

# === ANNOTATION TASK TYPES ===
WRITE_ANY_ANNOTATION_TASK_TYPE = "annotation_task_type:write:any"

# === DASHBOARD WEB APP ===
READ_DASHBOARD_SCHEDULE = "dashboard:schedule:read:any"
READ_DASHBOARD_INSIGHTS = "dashboard:insights:read:any"
READ_DASHBOARD_LIVE = "dashboard:live:read:any"
READ_DASHBOARD_LIVE_FROM_SCHEDULE = "dashboard:live_from_schedule:read:any"
READ_TERMINAL_CLEANS = "dashboard:terminal_cleans:read:any"
EDIT_DASHBOARD_SCHEDULE = "dashboard:schedule:edit:any"

# === BIG BOARD ===
READ_ANY_BIG_BOARD = "big_board:read:any"
WRITE_ANY_BIG_BOARD = "big_board:write:any"

# === MEASUREMENT PERIODS ===
READ_ANY_MEASUREMENT_PERIOD = "measurement_period:read:any"
WRITE_ANY_MEASUREMENT_PERIOD = "measurement_period:write:any"

# === BLOCKS ===
READ_ANY_BLOCK = "block:read:any"
WRITE_ANY_BLOCK = "block:write:any"

# === CONTACT INFORMATION ===
READ_ANY_CONTACT_INFORMATION = "contact_information:read:any"
WRITE_ANY_CONTACT_INFORMATION = "contact_information:write:any"

# === NOTIFICATIONS ===
READ_ANY_NOTIFICATION = "notification:read:any"
WRITE_ANY_NOTIFICATION = "notification:write:any"

# === SCHEDULE ASSISTANT ===
READ_ANY_CASE_DURATION = "case_duration:read:any"
EMAIL_ANY_AVAILABLE_TIME = "available_times:email:any"

# === PATIENT DATA ===
READ_ALL_PATIENT = "patient:read:all"

# === CLUSTER ===
READ_ANY_CLUSTER = "cluster:read:any"
WRITE_ANY_CLUSTER = "cluster:write:any"
