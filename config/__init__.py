import functools
import json
import logging
import os
from typing import Optional

from lib_python_config import get_secret_value

from config.run_mode import RunMode

""" Environment variables referenced below are set in
https://github.com/Apella-Technology/k8s-product/blob/main/applications/api-server/values-prod.yaml
and respective dev and staging versions of the values file.
"""


@functools.cache
def version() -> str:
    with open("version", "r") as version_file:
        return version_file.read()


def _boolify_env_variable(var: str) -> bool:
    if var not in os.environ:
        return False

    return os.environ[var].strip().lower() == "true"


def permission_expiration_duration() -> int:
    # This expires permission cache after 60 seconds.
    return int(os.getenv("PERMISSION_EXPIRATION_DURATION", 60))


def auth0_url() -> str:
    return os.environ["AUTH0_URL"]


def auth0_default_domain() -> str:
    return os.environ["AUTH0_DEFAULT_DOMAIN"]


def api_gateway_url() -> str:
    return os.environ["API_GATEWAY_URL"]


def auth0_client_id_key() -> Optional[str]:
    return os.getenv("AUTH0_CLIENT_ID_KEY", None)


def auth0_client_secret_key() -> Optional[str]:
    return os.getenv("AUTH0_CLIENT_SECRET_KEY", None)


def sql_instance_name() -> Optional[str]:
    return os.getenv("SQL_INSTANCE_NAME", None)  # Default for localhost?


def sql_socket_dir() -> Optional[str]:
    return os.getenv("SQL_SOCKET_DIR", "/cloudsql")


def sql_user() -> Optional[str]:
    return os.getenv("SQL_USER", None)


def sql_password() -> Optional[str]:
    password_secret_value = os.getenv("SQL_PASSWORD", None)
    if password_secret_value is None:
        password_secret_value = str(
            get_secret_value(
                name="SQL_PASSWORD_SECRET",
            ),
            "UTF-8",
        )
    return password_secret_value


def sql_database() -> Optional[str]:
    return os.getenv("SQL_DATABASE", None)


def sql_host() -> Optional[str]:
    return os.getenv("SQL_HOST", None)


def sql_port() -> int:
    return int(os.getenv("SQL_PORT", 5432))


def gcp_project() -> Optional[str]:
    return os.getenv("GCP_PROJECT", None)


def data_platform_gcp_project() -> str:
    return os.getenv("DATA_PLATFORM_GCP_PROJECT", "dev-data-platform-439b4c")


def media_asset_service_gcp_project() -> str:
    return os.getenv("MEDIA_ASSET_SERVICE_GCP_PROJECT", "dev-media-asset-93e8c3")


def media_asset_service_cloud_api_upload_subscription_id() -> str:
    return os.getenv(
        "MEDIA_ASSET_SERVICE_CLOUD_API_UPLOAD_SUBSCRIPTION_ID",
        "dev-cloud-api-image-upload-subscription",
    )


def enable_image_upload_subscriber() -> bool:
    return _boolify_env_variable("ENABLE_IMAGE_UPLOAD_SUBSCRIBER")


def mock_secret_store() -> bool:
    # This secret controls whether we are in a test environment or not.
    # It controls whether we use the mock secret store and the mock twilio client
    return _boolify_env_variable("MOCK_SECRET_STORE")


def signed_uri_expiration_minutes() -> int:
    return int(os.getenv("SIGNED_URI_EXPIRATION_MINUTES", 15))


def meta_cache_ttl() -> int:
    return int(os.getenv("META_CACHE_TTL_SECONDS", 60))


def use_gcloud_logging() -> bool:
    return _boolify_env_variable("USE_GCLOUD_LOGGING")


def otlp_trace_exporter_endpoint() -> Optional[str]:
    return os.getenv("OTLP_TRACE_EXPORTER_ENDPOINT", None)


def introspection_enable() -> bool:
    return _boolify_env_variable("INTROSPECTION_ENABLE")


def target_service_account() -> Optional[str]:
    return os.getenv("TARGET_SERVICE_ACCOUNT", None)


def tensorflow_storage_bucket() -> str:
    return os.getenv("TENSORFLOW_STORAGE_BUCKET", "test-tensorflow")


def twilio_account_sid_name() -> str:
    return os.getenv("TWILIO_ACCOUNT_SID_NAME", "TWILIO_ACCOUNT_SID")


def twilio_auth_token_name() -> str:
    return os.getenv("TWILIO_AUTH_TOKEN_NAME", "TWILIO_AUTH_TOKEN")


def twilio_phone_number_name() -> str:
    return os.getenv("TWILIO_PHONE_NUMBER_NAME", "TWILIO_PHONE_NUMBER")


def sendgrid_api_key() -> str:
    return os.getenv("SENDGRID_API_KEY", "SENDGRID_API_KEY")


def mock_pub_sub() -> bool:
    return _boolify_env_variable("MOCK_PUB_SUB")


def mock_big_query_endpoint() -> Optional[str]:
    return os.getenv("MOCK_BIG_QUERY_ENDPOINT", None)


def event_changes_topic() -> Optional[str]:
    return os.getenv("EVENT_CHANGES_TOPIC", None)


def flush_logs_immediately() -> bool:
    return _boolify_env_variable("FLUSH_LOGS_IMMEDIATELY")


def environment_name() -> str:
    return os.getenv("ENVIRONMENT_NAME", "localhost")


def use_sentry_logging() -> bool:
    return _boolify_env_variable("USE_SENTRY_LOGGING")


def bigquery_objects_table() -> Optional[str]:
    return os.getenv("BIGQUERY_OBJECTS_TABLE", None)


def bigquery_schedule_assistant_surgeon_procedures_table() -> Optional[str]:
    return os.getenv("BIGQUERY_SCHEDULE_ASSISTANT_SURGEON_PROCEDURES_TABLE", None)


def bigquery_camera_patient_blur_static_table() -> Optional[str]:
    return os.getenv("BIGQUERY_CAMERA_PATIENT_BLUR_STATIC_TABLE", None)


def bigquery_staff_site_frequency_table() -> Optional[str]:
    return os.getenv("BIGQUERY_STAFF_SITE_FREQUENCY_TABLE", None)


def bigquery_block_time_cases() -> Optional[str]:
    return os.getenv("BIGQUERY_BLOCK_TIME_CASES", None)


def launch_darkly_sdk_key_name() -> Optional[str]:
    return os.getenv("LAUNCH_DARKLY_SDK_KEY_NAME", None)


def redis_host() -> Optional[str]:
    return os.getenv("REDIS_HOST", None)


def redis_port() -> int:
    return int(os.getenv("REDIS_PORT", 6379))


def media_asset_service_endpoint() -> str:
    return os.getenv(
        "MEDIA_ASSET_SERVICE_ENDPOINT",
        "https://media-asset-service.dev.internal.apella.io",
    )


# This override should only be used for testing the Case Duration page locally.
def case_duration_org_override() -> Optional[str]:
    return os.getenv("CASE_DURATION_ORG_OVERRIDE", None)


def case_duration_prediction_bayesian_endpoint() -> str:
    return os.getenv(
        "CASE_DURATION_PREDICTION_BAYESIAN_ENDPOINT",
        "http://model-bayesian-case-duration.model-bayesian-case-duration.svc.cluster.local/v3/standalone/sample",
    )


def case_duration_turnover_endpoint() -> str:
    return os.getenv(
        "CASE_DURATION_TURNOVER_ENDPOINT",
        "http://model-turnover.model-turnover.svc.cluster.local/predict",
    )


def case_duration_slotting_endpoint() -> str:
    return os.getenv(
        "CASE_DURATION_SLOTTING_ENDPOINT",
        "http://model-case-slotting.model-case-slotting.svc.cluster.local/suggest",
    )


def web_server_error_log_file() -> str:
    return os.getenv("WEB_SERVER_ERROR_LOG_FILE", "")


def web_server_worker_count() -> int:
    return int(os.getenv("WEB_SERVER_WORKER_COUNT", 2))


def web_server_port() -> int:
    return int(os.getenv("PORT", 8080))


def web_server_address() -> str:
    return os.getenv("WEB_SERVER_ADDRESS", "0.0.0.0")


def web_server_log_level() -> str:
    return os.getenv("WEB_SERVER_LOG_LEVEL", "WARNING")


def prometheus_port() -> int:
    return int(os.getenv("PROMETHEUS_PORT", 9090))


def oracle_block_release_slack_notification_url() -> Optional[str]:
    return os.getenv(
        "ORACLE_BLOCK_RELEASE_SLACK_NOTIFICATION_URL",
        None,
    )


def use_ehr_scribe_patient_data() -> bool:
    return _boolify_env_variable("USE_EHR_SCRIBE_PATIENT_DATA")


def patient_endpoint() -> str:
    return os.getenv("EHR_SCRIBE_ENDPOINT", "http://ehr-scribe.ehr-scribe.svc.cluster.local")


def run_mode() -> RunMode:
    return RunMode.from_string(os.getenv("RUN_MODE", str(RunMode.WEB_SERVER.value)))


def notification_processor_cloud_api_upload_topic_id() -> str:
    return os.getenv(
        "NOTIFICATION_PROCESSOR_CLOUD_API_UPLOAD_TOPIC_ID",
        "dev-cloud-api-notification-topic",
    )


def notifications_time_to_run() -> int:
    return int(os.getenv("NOTIFICATIONS_TIME_TO_RUN", 10))


def sample_rates_by_operation_names() -> dict[str, float]:
    try:
        json_str = os.getenv("SAMPLE_RATES_BY_OPERATION_NAMES")
        if not json_str:
            return {}
        json_str = json_str.strip().replace("\n", "").replace("\t", "").replace("\r", "")
        rates = json.loads(json_str)
        return {k: float(v) for k, v in rates.items()}
    except Exception:
        logging.error("Unable to parse sample rates config value.")
    return {}


def get_notion_api_key_name() -> str:
    return os.getenv("NOTION_READONLY_API_KEY_NAME", "NOTION_API_KEY")


def get_notion_db_id() -> str:
    return os.getenv("NOTION_DB_ID", "NOTION_DB_ID")


def notion_time_to_run() -> int:
    return int(os.getenv("NOTION_TIME_TO_RUN", 10))
