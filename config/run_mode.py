from __future__ import annotations

from enum import Enum


class RunMode(str, Enum):
    DEV_MODE_WEB_SERVER = "DEV_MODE_WEB_SERVER"
    WEB_SERVER = "WEB_SERVER"
    NOTIFICATION_DAEMON = "NOTIFICATION_DAEMON"
    NOTION_DAEMON = "NOTION_DAEMON"

    def equals(self, string: str) -> bool:
        return self.name == string

    @staticmethod
    def from_string(string: str) -> RunMode:
        if string == RunMode.DEV_MODE_WEB_SERVER.value:
            return RunMode.DEV_MODE_WEB_SERVER
        if string == RunMode.WEB_SERVER.value:
            return RunMode.WEB_SERVER
        if string == RunMode.NOTIFICATION_DAEMON.value:
            return RunMode.NOTIFICATION_DAEMON
        if string == RunMode.NOTION_DAEMON.value:
            return RunMode.NOTION_DAEMON
        raise Exception(f"Unknown run mode: `{string}`")
