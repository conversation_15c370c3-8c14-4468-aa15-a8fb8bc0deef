import logging
import time
from typing import Iterator

import pytest
from _pytest.fixtures import SubRequest

"""
Defines project-level pytest fixtures.

In order to hook lower-level fixtures (such as those in tests_component/conftest.py), we have to
define the hooks at the topmost level.
"""

logger = logging.getLogger(__name__)


@pytest.hookimpl(hookwrapper=True)
def pytest_fixture_setup(fixturedef: object, request: SubRequest) -> Iterator[None]:
    start = time.perf_counter()
    yield
    duration = time.perf_counter() - start

    logger.debug(f"Fixture setup for {request.fixturename} took {duration:.3f} seconds")
