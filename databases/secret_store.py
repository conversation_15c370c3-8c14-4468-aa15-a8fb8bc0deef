# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

from typing import Optional

import google.api_core.exceptions
import google.api_core.retry
from google.cloud.secretmanager import SecretManagerServiceClient


class SecretStore:
    def __init__(self, project_id, prefix):
        self.project_id = project_id
        self.prefix = prefix
        self.secret_manager_client = SecretManagerServiceClient()

    def get_media_server_stream_secret(self, server_id):
        secret_id = f"{self.prefix}servers_{server_id}_stream_secret"
        return self.get_secret(secret_id)

    def store_media_server_stream_secret(self, server_id, secret_value):
        secret_id = f"{self.prefix}servers_{server_id}_stream_secret"
        self.__store_secret(secret_id, secret_value)

    def get_kafka_forwarder_key(self, server_id):
        secret_id = f"{self.prefix}servers_{server_id}_kafka_forwarder_key"
        return self.get_secret(secret_id)

    def get_kafka_forwarder_secret(self, server_id):
        secret_id = f"{self.prefix}servers_{server_id}_kafka_forwarder_secret"
        return self.get_secret(secret_id)

    def get_ehr_interface_secret(self, organization_id: str) -> Optional[str]:
        secret_id = f"{organization_id}_ehr_interface_secret"
        try:
            return self.get_secret(secret_id=secret_id)
        except google.api_core.exceptions.NotFound:
            return None

    def get_secret(self, secret_id, project_id: Optional[str] = None) -> str:
        if project_id is None:
            project_id = self.project_id
        # We could change this API to specify a secret version
        # But for now, just get the latest version
        secret_id = f"projects/{project_id}/secrets/{secret_id}/versions/latest"
        response = self.secret_manager_client.access_secret_version(name=secret_id)
        return response.payload.data.decode("UTF-8")

    def __store_secret(self, requested_secret_id: str, secret_value: str):
        # requested_secret_id may have "/versions/*" at the end, but it shouldn't
        index_of_versions = requested_secret_id.find("/versions/")
        if index_of_versions != -1:
            # Trim it off
            requested_secret_id = requested_secret_id[:index_of_versions]

        # Convert the string secret_value into a bytes. This step can be omitted if you
        # pass in bytes instead of a str for the secret_value argument.
        secret_value_bytes = secret_value.encode("UTF-8")

        # Update the secret
        try:
            # Check if it exists first
            name = f"projects/{self.project_id}/secrets/{requested_secret_id}"
            self.secret_manager_client.get_secret(name=name)

        except google.api_core.exceptions.NotFound:
            # Secret doesn't exist, so just create it
            self.secret_manager_client.create_secret(
                parent=f"projects/{self.project_id}",
                secret_id=requested_secret_id,
                secret={"replication": {"automatic": {}}},
            )

        # The secret exists, update the secret
        self.secret_manager_client.add_secret_version(
            request={"parent": name, "payload": {"data": secret_value_bytes}}
        )
