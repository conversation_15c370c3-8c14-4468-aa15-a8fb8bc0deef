import os
import textwrap
from opentelemetry import trace
from typing import Any, Callable, Final, Optional, Type, TypeVar

from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from sqlalchemy import event, exc, text, Pool, StaticPool
from sqlalchemy import URL
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.orm.session import sessionmaker, Session
from sqlalchemy.pool import _ConnectionFairy, _ConnectionRecord
from sqlalchemy.types import JSON
from sqlalchemy.ext.asyncio import (
    create_async_engine,
    AsyncAttrs,
    async_sessionmaker,
    AsyncSession,
    AsyncEngine,
)

import config
from api_server import logging
from api_server.server.middleware.fast_api_graphql_query_name_middleware import (
    get_graphql_operation_name,
)
from api_server.server.middleware.sql_alchemy_connection_count_middleware import (
    increment_sql_alchemy_connection_count,
)
from utils.history_meta import versioned_session
from prometheus_client import Gauge

from urllib.parse import quote
from opentelemetry.trace.span import INVALID_SPAN


class Base(AsyncAtt<PERSON>, DeclarativeBase):
    type_annotation_map = {dict[str, Any]: JSON}


# SQLAlchemy's connection pool logic is:
# 1. Create an empty queue to hold connections
# 2. When a request comes in, check the pool for a connection
#    a) If there is a connection available, use it
#    b) if no available connection, but our total connections < pool_size + max_overflow
#        then try to connect again.  If this fails, we get the "too many connections" error.
#    c) if no available connections, and total connections >= pool_size + max_overflow
#        then wait up to pool_timeout
# 3. After the connection is used, return it to the queue
# 4. Eventually, if the queue size is > pool_size, free some connections down to pool_size.

# So, if we are not at pool_size + max_overflow, then it tries to construct an additional
# connection, and fails and doesn't catch the exception. So since our max connections from
# SQL is 100, then our # of API servers * pool_size * max_overflow cannot exceed 100.  And,
# according to cloud-run docs, during traffic spikes, we can actually exceed # of max_instances
# specified.  So we need to give more of a buffer.
# (Max instances is defined in the deploy scripts)

# pool_size is the number of connections to keep open inside the connection pool
# Default is 5
POOL_SIZE: Final = 13

# max_overflow is the number of connections to allow in connection pool "overflow",
# that is connections that can be opened above and beyond the pool_size setting
# Default is 10
MAX_OVERFLOW = 2

# pool_recycle causes the pool to recycle connections after the given number of seconds has passed
# Default is -1
# We set this to 600 (10 minutes) because we know that cloud-run and sql have issues staying
# connected over long periods of time.  So just recycle them if they are getting old.
pool_recycle = 600

# pool_pre_ping will test connections for liveness upon each checkout.
# see https://docs.sqlalchemy.org/en/13/core/pooling.html#disconnect-handling-pessimistic
# Default is False
# We set to True because it should eliminate the "server closed the connection unexpectedly" error
pool_pre_ping = True

# Number of seconds to wait before giving up on getting a connection from the pool.
POOL_TIMEOUT = 25

# Abort any statement that takes more than the specified amount of time. In milliseconds.
statement_timeout = 15000

DECODABLE_SCHEMA = "decodable"

KEY_VALUE_DELIMITER = ","


def url_quote(url: str | bytes) -> str:
    if not isinstance(url, (str, bytes)):
        raise TypeError(f"Expected str or bytes, got {type(url)}")
    quoted = quote(url)
    # Since SQL uses '%' as a keyword, '%' is a by-product of url quoting
    # e.g. foo,bar --> foo%2Cbar
    # thus in our quoting, we need to escape it too to finally give
    #      foo,bar --> foo%%2Cbar
    return quoted.replace("%", "%%")


def generate_sql_comment(data: dict[str, str]) -> str:
    """
    Return a SQL comment with comma delimited key=value pairs created from
    **meta kwargs.
    """
    if not data:  # No entries added.
        return ""

    # Sort the keywords to ensure that caching works and that testing is
    # deterministic. It eases visual inspection as well.

    return (
        " /*"
        + KEY_VALUE_DELIMITER.join(
            "{}={!r}".format(url_quote(key), url_quote(value))
            for key, value in sorted(data.items())
            if value is not None
        )
        + "*/"
    )


class EngineProvider:
    """
    Provides an async engine for use in queries. Note that use of this provider means that the callee must maintain
    the engine per process as managing the event loop is outside this class.
    """

    def __call__(
        self,
        pool_class: Optional[Type[Pool]] = None,
        pool_size: int = POOL_SIZE,
        max_overflow: int = MAX_OVERFLOW,
    ) -> AsyncEngine:
        return self._get_engine(pool_class, pool_size, max_overflow)

    def _get_engine(
        self, pool_class: Optional[Type[Pool]], pool_size: int, max_overflow: int
    ) -> AsyncEngine:
        # Lazily create the singleton engine
        all_args = {
            "pool_recycle": pool_recycle,
            "pool_pre_ping": pool_pre_ping,
            "connect_args": {
                "command_timeout": statement_timeout,
                "server_settings": {"application_name": os.environ.get("APP_NAME", "api-server")},
            },
        }
        if pool_class:
            all_args["poolclass"] = pool_class

        if pool_class != StaticPool:
            all_args["pool_size"] = pool_size
            all_args["max_overflow"] = max_overflow
            all_args["pool_timeout"] = POOL_TIMEOUT

        if config.sql_host() is not None:
            logging.info(
                textwrap.dedent(
                    f"""
                    Using SQL_HOST: {config.sql_host()}
                    Using SQL_USER: {config.sql_user()}
                    Using SQL_DATABASE: {config.sql_database()}
                    Using SQL_PORT: {config.sql_port()}
                    """
                )
            )
            url = URL.create(
                "postgresql+asyncpg",
                username=config.sql_user(),
                password=config.sql_password(),
                host=config.sql_host(),
                port=config.sql_port(),
                database=config.sql_database(),
            )
        elif config.sql_socket_dir() is not None:
            logging.info(
                textwrap.dedent(
                    f"""
                    Using SQL_SOCKET_DIR: {config.sql_socket_dir()}
                    Using SQL_USER: {config.sql_user()}
                    Using SQL_DATABASE: {config.sql_database()}
                    Using SQL_INSTANCE_NAME: {config.sql_instance_name()}
                    """
                )
            )
            url = URL.create(
                "postgresql+asyncpg",
                username=config.sql_user(),
                password=config.sql_password(),
                database=config.sql_database(),
                query={"host": f"{config.sql_socket_dir()}/{config.sql_instance_name()}"},
            )
        else:
            raise RuntimeError("SQL configuration not set. See README.md")

        async_engine = create_async_engine(url=url, **all_args)
        SQLAlchemyInstrumentor().instrument(engine=async_engine.sync_engine)

        # Disposing of the sql alchemy connection if it originated in a different pid
        # https://docs.sqlalchemy.org/en/14/core/pooling.html#using-connection-pools-with-multiprocessing-or-os-fork
        @event.listens_for(async_engine.sync_engine, "connect")
        def connect(dbapi_connection: Any, connection_record: _ConnectionRecord) -> None:
            connection_record.info["pid"] = os.getpid()

        @event.listens_for(async_engine.sync_engine, "before_cursor_execute", retval=True)
        def apply_comment(  # type: ignore[no-untyped-def]
            connection,
            cursor,
            statement,
            parameters,
            context,
            executemany,
        ) -> tuple[str, Any]:
            session_info = connection.info.get("session_info", {})
            if "comment" in session_info:
                statement += session_info["comment"]
            return statement, parameters

        @event.listens_for(async_engine.sync_engine, "checkout")
        def checkout(
            dbapi_connection: Any,
            connection_record: _ConnectionRecord,
            connection_proxy: _ConnectionFairy,
        ) -> None:
            connection_pid = connection_record.info["pid"]
            this_pid = os.getpid()
            if connection_pid != this_pid:
                connection_record.dbapi_connection = None
                connection_proxy.dbapi_connection = None  # type: ignore
                raise exc.DisconnectionError(
                    f"Connection record belongs to pid {connection_pid}, attempting to check out in pid {this_pid}"
                )

        return async_engine


engine_provider = EngineProvider()


class EngineSingleton:
    """
    Async Engine singleton. Use this for all new code when dealing with
    """

    def __init__(self) -> None:
        self.instance: Optional[AsyncEngine] = None

    def __call__(self) -> Optional[AsyncEngine]:
        # Lazily create the singleton engine
        # Check if config is initialized. For Alembic and component tests config may not have been initialized.
        # However, the engine singleton is created during code import and before start up.
        # TODO: Move Engine Provider, Singleton, and Session Maker to DI.
        if self.instance is None and config.sql_host() is not None:
            self.instance = engine_provider()

        return self.instance


engine_singleton = EngineSingleton()


def get_session_maker(engine: Optional[AsyncEngine]) -> Callable[[], AsyncSession]:
    # Lazily create the sessionmaker singleton
    """
    https://docs.sqlalchemy.org/en/20/orm/extensions/asyncio.html#using-events-with-the-asyncio-extension
    Binding events is not supported yet in SQLAlchemy 2 to async sessions. So we make an
    async wrapper as the docs mention.
    """
    assert engine is not None
    sync_session_instance = sessionmaker(bind=engine.sync_engine, expire_on_commit=False)
    async_session_instance = async_sessionmaker(
        bind=engine, expire_on_commit=False, sync_session_class=sync_session_instance
    )
    # Adds a "before flush" hook to add version history for versioned tabled
    versioned_session(sync_session_instance)
    return async_session_instance


class SessionMakerSingleton:
    """
    Async Engine singleton. Use this for all new code when dealing with
    """

    def __init__(self) -> None:
        self.instance: Optional[Callable[[], AsyncSession]] = None

    def __call__(self) -> Optional[Callable[[], AsyncSession]]:
        # Lazily create the singleton session maker
        engine = engine_singleton()
        if self.instance is None and engine is not None:
            self.instance = get_session_maker(engine_singleton())

        return self.instance


_SQLALCHEMY_SESSION_USE_COUNT: Final = Gauge(
    "sql_alchemy_session_use_count",
    "Total number of concurrent sessions in use.",
)

_ObservedAsyncSession = TypeVar("_ObservedAsyncSession", bound="ObservedAsyncSession")


@event.listens_for(Session, "after_begin")
def _connection_for_session(session, transaction, connection) -> None:  # type: ignore[no-untyped-def]
    """Share the 'info' dictionary of Session with Connection
    objects.

    This occurs as new Connection objects are associated with the
    Session.   The .info dictionary on Connection is local to the
    DBAPI connection.

    """
    connection.info["session_info"] = session.info


class ObservedAsyncSession:
    _session: AsyncSession

    def __init__(self, session: AsyncSession) -> None:
        self._session = session

    def _generate_comment(self) -> None:
        comment_data: dict[str, str] = {}
        operation_name = get_graphql_operation_name()
        if operation_name:
            comment_data["operation_name"] = operation_name
        try:
            if trace and trace.get_current_span() is not INVALID_SPAN:
                span_context = trace.get_current_span().get_span_context()
                trace_id = trace.format_trace_id(span_context.trace_id)
                comment_data["trace_id"] = trace_id
        except Exception:
            pass
        comment = generate_sql_comment(comment_data)
        self._session.info["comment"] = comment

    async def __aenter__(self: _ObservedAsyncSession) -> AsyncSession:
        _SQLALCHEMY_SESSION_USE_COUNT.inc()
        await self._session.__aenter__()
        self._generate_comment()
        return self._session

    async def __aexit__(self, type_: Any, value: Any, traceback: Any) -> None:
        _SQLALCHEMY_SESSION_USE_COUNT.dec()
        if self._session.info and "comment" in self._session.info:
            del self._session.info["comment"]
        await self._session.__aexit__(type, value, traceback)


session_maker_singleton = SessionMakerSingleton()


def new_async_session() -> ObservedAsyncSession:
    # Create a new session by invoking the sessionmaker
    session_maker = session_maker_singleton()
    increment_sql_alchemy_connection_count()
    if session_maker is None:
        raise RuntimeError("Attempted to initialize session maker before configuration finished.")
    session = session_maker()
    return ObservedAsyncSession(session)


def sync_health_check(session: Session) -> None:
    """
    Using context managers in asyncio run can close the main event loop.
    For initial setup we need a synchronous version of the health check function to not
    cause weird event loop closing behavior.
    """
    try:
        logging.debug("Starting SQL Health Check")
        session.execute(text("SELECT 1"))
        logging.debug("Health Check Successful")
    except Exception as e:
        logging.error("Health Check Failed")
        logging.report_internal_error(e)
        raise e


async def health_check() -> None:
    async with new_async_session() as session:
        try:
            logging.debug("Starting SQL Health Check")
            await session.execute(text("SELECT 1"))
            logging.debug("Health Check Successful")
        except Exception as e:
            logging.error("Health Check Failed")
            logging.report_internal_error(e)
            raise e
