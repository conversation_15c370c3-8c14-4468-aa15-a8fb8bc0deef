import enum
import uuid
import datetime

import graphene
import sqlalchemy
from sqlalchemy import DateT<PERSON>, Foreign<PERSON>ey, String, MetaData
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import declarative_mixin, mapped_column, Mapped
from sqlalchemy.sql import func

from databases.sql import DECODABLE_SCHEMA


class TimestampMixin(object):
    created_time: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), nullable=False, index=True
    )
    updated_time: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=True,
        index=True,
    )


class Decodable:
    __table_args__ = {"schema": DECODABLE_SCHEMA}
    metadata = MetaData(schema=DECODABLE_SCHEMA)


class OrgIdMixin:
    """
    This OrgIdMixin is necessary for `org_id_filter.py` to function and automatically add authZ
    filters for a user's org_id.
    """

    org_id: Mapped[str] = mapped_column(
        String, ForeignKey("organizations.id"), nullable=False, index=True
    )


class SiteIdMixin:
    """
    This SiteIdMixin is necessary for `site_id_filter.py` to function and automatically add authZ
    filters for a user's authorized site_ids.
    """

    site_id: Mapped[str] = mapped_column(String, ForeignKey("sites.id"), nullable=False, index=True)


# This ArchivedTimeMixin is necessary for soft deleting rows in the db
@declarative_mixin
class ArchivedTimeMixin(object):
    archived_time: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True), index=True, nullable=True
    )


class EventMatchingStatus(enum.Enum):
    AUTOMATIC = enum.auto()
    OVERRIDE = enum.auto()


EventMatchingStatusGraphene = graphene.Enum.from_enum(EventMatchingStatus)


@declarative_mixin
class EventMatchingStatusMixin(object):
    event_matching_status: Mapped[EventMatchingStatus] = mapped_column(
        sqlalchemy.Enum(EventMatchingStatus),
        nullable=True,
        index=True,
        default=EventMatchingStatus.AUTOMATIC,
    )


# Etags are used to provide concurrency protection on rows
# When changing a row, if the Etag provided by the write is different from the existing
# Etag then a concurrency violation should be thrown
@declarative_mixin
class ETagMixin(object):
    etag: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), nullable=True, index=True, default=uuid.uuid4, onupdate=uuid.uuid4
    )
