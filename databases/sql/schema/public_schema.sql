--
-- PostgreSQL database dump
--

-- Dumped from database version 13.2 (Debian 13.2-1.pgdg100+1)
-- Dumped by pg_dump version 13.2 (Debian 13.2-1.pgdg100+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: btree_gist; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS btree_gist WITH SCHEMA public;


--
-- Name: EXTENSION btree_gist; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION btree_gist IS 'support for indexing common datatypes in GiST';


--
-- Name: block_processing_statuses; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.block_processing_statuses AS ENUM (
    'PENDING',
    'PROCESSED',
    'REJECTED'
);


ALTER TYPE public.block_processing_statuses OWNER TO postgres;

--
-- Name: blocktypes; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.blocktypes AS ENUM (
    'UNBLOCKED_BLOCK_TYPE',
    'UNAVAILABLE_BLOCK_TYPE',
    'GROUP_BLOCK_TYPE',
    'SURGEON_BLOCK_TYPE',
    'SERVICE_BLOCK_TYPE',
    'ON_HOLD_BLOCK_TYPE',
    'UNKNOWN'
);


ALTER TYPE public.blocktypes OWNER TO postgres;

--
-- Name: boardviewtype; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.boardviewtype AS ENUM (
    'TIMELINE',
    'TILE'
);


ALTER TYPE public.boardviewtype OWNER TO postgres;

--
-- Name: cancelledreason; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.cancelledreason AS ENUM (
    'IDLE',
    'OUTAGE',
    'SENSITIVE_CONTENT',
    'STAFF_IN_TRAINING',
    'CAMERAS_OUT_OF_SYNC',
    'BLOCKED_CAMERAS',
    'SKIP'
);


ALTER TYPE public.cancelledreason OWNER TO postgres;

--
-- Name: caseforecaststatus; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.caseforecaststatus AS ENUM (
    'INVALID',
    'VALID'
);


ALTER TYPE public.caseforecaststatus OWNER TO postgres;

--
-- Name: caselabelfieldtype; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.caselabelfieldtype AS ENUM (
    'BOOLEAN',
    'SINGLE_SELECT'
);


ALTER TYPE public.caselabelfieldtype OWNER TO postgres;

--
-- Name: casematchingstatus; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.casematchingstatus AS ENUM (
    'AUTOMATIC',
    'OVERRIDE',
    'CANCELED',
    'NOT_A_CASE'
);


ALTER TYPE public.casematchingstatus OWNER TO postgres;

--
-- Name: cleanscoreenum; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.cleanscoreenum AS ENUM (
    'COMPLETE',
    'PARTIAL',
    'MISSED'
);


ALTER TYPE public.cleanscoreenum OWNER TO postgres;

--
-- Name: contactinformationtype; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.contactinformationtype AS ENUM (
    'PHONE_NUMBER'
);


ALTER TYPE public.contactinformationtype OWNER TO postgres;

--
-- Name: day_of_week; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.day_of_week AS ENUM (
    'MONDAY',
    'TUESDAY',
    'WEDNESDAY',
    'THURSDAY',
    'FRIDAY',
    'SATURDAY',
    'SUNDAY'
);


ALTER TYPE public.day_of_week OWNER TO postgres;

--
-- Name: eventmatchingstatus; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.eventmatchingstatus AS ENUM (
    'AUTOMATIC',
    'OVERRIDE'
);


ALTER TYPE public.eventmatchingstatus OWNER TO postgres;

--
-- Name: patientclass; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.patientclass AS ENUM (
    'EMERGENCY',
    'HOSPITAL_OUTPATIENT_SURGERY',
    'INPATIENT',
    'SURGERY_ADMIT',
    'PRE_ADMIT',
    'OBSERVATION',
    'OTHER'
);


ALTER TYPE public.patientclass OWNER TO postgres;

--
-- Name: phasestatus; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.phasestatus AS ENUM (
    'INVALID',
    'VALID'
);


ALTER TYPE public.phasestatus OWNER TO postgres;

--
-- Name: releasetypes; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.releasetypes AS ENUM (
    'AUTO',
    'MANUAL',
    'UNKNOWN'
);


ALTER TYPE public.releasetypes OWNER TO postgres;

--
-- Name: sentnotificationstatus; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.sentnotificationstatus AS ENUM (
    'SENT',
    'FAILED',
    'NOT_ATTEMPTED',
    'MISSED'
);


ALTER TYPE public.sentnotificationstatus OWNER TO postgres;

--
-- Name: taskstatus; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.taskstatus AS ENUM (
    'NOT_STARTED',
    'IN_PROGRESS',
    'READY_FOR_REVIEW',
    'IN_REVIEW',
    'DONE',
    'CANCELLED',
    'BLOCKED'
);


ALTER TYPE public.taskstatus OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: alembic_version; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.alembic_version (
    version_num character varying(32) NOT NULL
);


ALTER TABLE public.alembic_version OWNER TO postgres;

--
-- Name: anesthesias; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.anesthesias (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name character varying(255) NOT NULL,
    org_id character varying NOT NULL
);


ALTER TABLE public.anesthesias OWNER TO postgres;

--
-- Name: annotation_task_schedules; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.annotation_task_schedules (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid NOT NULL,
    start_time time without time zone NOT NULL,
    "interval" integer NOT NULL,
    annotation_task_type_id uuid NOT NULL
);


ALTER TABLE public.annotation_task_schedules OWNER TO postgres;

--
-- Name: annotation_task_schedules_rooms; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.annotation_task_schedules_rooms (
    schedule_id uuid NOT NULL,
    room_id character varying NOT NULL
);


ALTER TABLE public.annotation_task_schedules_rooms OWNER TO postgres;

--
-- Name: annotation_task_schedules_sites; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.annotation_task_schedules_sites (
    schedule_id uuid NOT NULL,
    site_id character varying NOT NULL
);


ALTER TABLE public.annotation_task_schedules_sites OWNER TO postgres;

--
-- Name: annotation_task_type_annotators; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.annotation_task_type_annotators (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    annotation_task_type_id uuid NOT NULL,
    user_id character varying NOT NULL
);


ALTER TABLE public.annotation_task_type_annotators OWNER TO postgres;

--
-- Name: annotation_task_type_provisional_annotators; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.annotation_task_type_provisional_annotators (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    annotation_task_type_id uuid NOT NULL,
    user_id character varying NOT NULL
);


ALTER TABLE public.annotation_task_type_provisional_annotators OWNER TO postgres;

--
-- Name: annotation_task_type_reviewers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.annotation_task_type_reviewers (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    annotation_task_type_id uuid NOT NULL,
    user_id character varying NOT NULL
);


ALTER TABLE public.annotation_task_type_reviewers OWNER TO postgres;

--
-- Name: annotation_task_types; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.annotation_task_types (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    name character varying NOT NULL,
    description character varying NOT NULL,
    event_types character varying[] DEFAULT '{}'::character varying[] NOT NULL,
    id uuid NOT NULL,
    archived_time timestamp with time zone,
    priority integer NOT NULL,
    detect_idle boolean DEFAULT false NOT NULL,
    context_event_types character varying[] DEFAULT '{}'::character varying[] NOT NULL,
    allow_skipping_review boolean DEFAULT false NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    optimize_tasks boolean DEFAULT false NOT NULL,
    CONSTRAINT ck_annotation_task_types_description_trimmed CHECK ((btrim((description)::text) = (description)::text)),
    CONSTRAINT ck_annotation_task_types_name_min_len_and_trimmed CHECK (((length(btrim((name)::text)) >= 3) AND (btrim((name)::text) = (name)::text))),
    CONSTRAINT ck_annotation_task_types_priority_is_positive CHECK ((priority > 0))
);


ALTER TABLE public.annotation_task_types OWNER TO postgres;

--
-- Name: annotation_task_types_history; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.annotation_task_types_history (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    archived_time timestamp with time zone,
    id uuid NOT NULL,
    name character varying NOT NULL,
    description character varying NOT NULL,
    event_types character varying[] NOT NULL,
    context_event_types character varying[] NOT NULL,
    priority integer NOT NULL,
    detect_idle boolean NOT NULL,
    allow_skipping_review boolean NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    optimize_tasks boolean DEFAULT false NOT NULL
);


ALTER TABLE public.annotation_task_types_history OWNER TO postgres;

--
-- Name: annotation_tasks; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.annotation_tasks (
    id uuid NOT NULL,
    org_id character varying NOT NULL,
    site_id character varying NOT NULL,
    room_id character varying NOT NULL,
    start_time timestamp with time zone NOT NULL,
    end_time timestamp with time zone NOT NULL,
    status public.taskstatus NOT NULL,
    annotator_user_id character varying,
    reviewer_user_id character varying,
    updated_time timestamp with time zone DEFAULT now(),
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    type_id uuid NOT NULL,
    cancelled_reason public.cancelledreason,
    version integer DEFAULT 1 NOT NULL,
    review_reasons character varying[] DEFAULT '{}'::character varying[] NOT NULL,
    type_version integer NOT NULL,
    updated_by_user_id character varying
);


ALTER TABLE public.annotation_tasks OWNER TO postgres;

--
-- Name: annotation_tasks_history; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.annotation_tasks_history (
    id uuid NOT NULL,
    org_id character varying NOT NULL,
    site_id character varying NOT NULL,
    room_id character varying NOT NULL,
    start_time timestamp with time zone NOT NULL,
    end_time timestamp with time zone NOT NULL,
    status public.taskstatus NOT NULL,
    annotator_user_id character varying,
    reviewer_user_id character varying,
    updated_time timestamp with time zone DEFAULT now(),
    type_id uuid NOT NULL,
    cancelled_reason public.cancelledreason,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    review_reasons character varying[] DEFAULT '{}'::character varying[] NOT NULL,
    type_version integer NOT NULL,
    updated_by_user_id character varying
);


ALTER TABLE public.annotation_tasks_history OWNER TO postgres;

--
-- Name: block_release_file_rows; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.block_release_file_rows (
    block_release_file_id uuid NOT NULL,
    row_number integer NOT NULL,
    external_id character varying,
    org_id character varying NOT NULL,
    site_id character varying,
    room_id character varying,
    room_name character varying,
    timezone character varying,
    start_time timestamp with time zone,
    end_time timestamp with time zone,
    release_length integer,
    released_time timestamp with time zone,
    release_reason character varying,
    released_by character varying,
    block_name character varying,
    block_date timestamp with time zone,
    to_block_name character varying,
    from_block_type public.blocktypes,
    to_block_type public.blocktypes,
    processing_status public.block_processing_statuses NOT NULL,
    rejected_reason character varying,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    release_type public.releasetypes,
    days_prior integer
);


ALTER TABLE public.block_release_file_rows OWNER TO postgres;

--
-- Name: block_release_processed_files; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.block_release_processed_files (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    file_name character varying NOT NULL,
    bucket_name character varying NOT NULL,
    generation character varying NOT NULL,
    size character varying NOT NULL,
    md5_hash character varying NOT NULL,
    media_link character varying NOT NULL,
    org_id character varying NOT NULL
);


ALTER TABLE public.block_release_processed_files OWNER TO postgres;

--
-- Name: block_schedule_file_rows; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.block_schedule_file_rows (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    block_schedule_file_id uuid NOT NULL,
    org_id character varying NOT NULL,
    site_id character varying,
    room_id character varying,
    room_name character varying NOT NULL,
    timezone character varying,
    block_date timestamp with time zone,
    block_name character varying,
    block_type public.blocktypes,
    block_start_time timestamp with time zone,
    block_end_time timestamp with time zone,
    processing_status public.block_processing_statuses NOT NULL,
    rejected_reason character varying,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    row_number integer NOT NULL
);


ALTER TABLE public.block_schedule_file_rows OWNER TO postgres;

--
-- Name: block_schedule_processed_files; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.block_schedule_processed_files (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    file_name character varying NOT NULL,
    bucket_name character varying NOT NULL,
    generation character varying NOT NULL,
    size character varying NOT NULL,
    md5_hash character varying NOT NULL,
    media_link character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    org_id character varying NOT NULL
);


ALTER TABLE public.block_schedule_processed_files OWNER TO postgres;

--
-- Name: block_sites; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.block_sites (
    block_id uuid NOT NULL,
    site_id character varying NOT NULL
);


ALTER TABLE public.block_sites OWNER TO postgres;

--
-- Name: block_surgeons; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.block_surgeons (
    block_id uuid NOT NULL,
    staff_id uuid NOT NULL
);


ALTER TABLE public.block_surgeons OWNER TO postgres;

--
-- Name: block_time_overrides; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.block_time_overrides (
    block_time_id uuid NOT NULL,
    block_time_minutes integer NOT NULL,
    user_id character varying NOT NULL,
    note character varying,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    version integer DEFAULT 1 NOT NULL
);


ALTER TABLE public.block_time_overrides OWNER TO postgres;

--
-- Name: block_time_overrides_history; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.block_time_overrides_history (
    block_time_id uuid NOT NULL,
    block_time_minutes integer NOT NULL,
    user_id character varying NOT NULL,
    note character varying,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    version integer DEFAULT 1 NOT NULL
);


ALTER TABLE public.block_time_overrides_history OWNER TO postgres;

--
-- Name: block_time_releases; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.block_time_releases (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    block_time_id uuid,
    release_time tstzrange NOT NULL,
    reason character varying,
    released_at timestamp with time zone DEFAULT now() NOT NULL,
    source character varying NOT NULL,
    source_type character varying NOT NULL,
    unreleased_time timestamp with time zone,
    unreleased_source character varying,
    start_time timestamp with time zone NOT NULL,
    end_time timestamp with time zone NOT NULL,
    released_time timestamp with time zone DEFAULT now() NOT NULL,
    from_block_type public.blocktypes,
    to_block_type public.blocktypes,
    release_type public.releasetypes,
    days_prior integer,
    room_id character varying,
    to_block_id uuid,
    CONSTRAINT ck_block_time_release_start_time_lt_end_time CHECK ((start_time < end_time))
);


ALTER TABLE public.block_time_releases OWNER TO postgres;

--
-- Name: block_times; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.block_times (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    block_id uuid NOT NULL,
    block_time tstzrange NOT NULL,
    room_id character varying NOT NULL,
    start_time timestamp with time zone NOT NULL,
    end_time timestamp with time zone NOT NULL,
    released_from uuid,
    CONSTRAINT ck_block_time_start_time_lt_end_time CHECK ((start_time < end_time))
);


ALTER TABLE public.block_times OWNER TO postgres;

--
-- Name: blocks; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.blocks (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    archived_time timestamp with time zone,
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name character varying(255) NOT NULL,
    color character varying(16) NOT NULL,
    org_id character varying NOT NULL,
    block_type public.blocktypes,
    CONSTRAINT ck_block_color CHECK (((color)::text ~ '^#[0-9a-fA-F]{6}$'::text))
);


ALTER TABLE public.blocks OWNER TO postgres;

--
-- Name: board_config; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.board_config (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name character varying NOT NULL,
    page_size integer NOT NULL,
    page_duration integer NOT NULL,
    blur_video boolean NOT NULL,
    org_id character varying NOT NULL,
    site_id character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    updated_by_user_id character varying NOT NULL,
    board_view_type public.boardviewtype DEFAULT 'TILE'::public.boardviewtype NOT NULL,
    enable_video boolean DEFAULT true NOT NULL,
    zoom_percent integer DEFAULT 100 NOT NULL,
    show_closed_rooms boolean DEFAULT true NOT NULL,
    CONSTRAINT zoom_percent_positive CHECK ((zoom_percent > 0))
);


ALTER TABLE public.board_config OWNER TO postgres;

--
-- Name: board_room; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.board_room (
    board_config_id uuid NOT NULL,
    room_id character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now()
);


ALTER TABLE public.board_room OWNER TO postgres;

--
-- Name: cameras; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cameras (
    id character varying NOT NULL,
    room_id character varying NOT NULL,
    site_id character varying NOT NULL,
    org_id character varying NOT NULL,
    name character varying NOT NULL,
    rtsp_url character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    labels json,
    family character varying NOT NULL
);


ALTER TABLE public.cameras OWNER TO postgres;

--
-- Name: case_classification_types; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_classification_types (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id character varying NOT NULL,
    name character varying NOT NULL,
    description character varying,
    archived_time timestamp with time zone,
    org_id character varying NOT NULL
);


ALTER TABLE public.case_classification_types OWNER TO postgres;

--
-- Name: case_derived_properties; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_derived_properties (
    case_id character varying NOT NULL,
    is_in_flip_room boolean DEFAULT false NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    preceding_case_id character varying,
    is_first_case boolean DEFAULT false NOT NULL
);


ALTER TABLE public.case_derived_properties OWNER TO postgres;

--
-- Name: case_flag; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_flag (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    archived_time timestamp with time zone,
    id uuid NOT NULL,
    case_id character varying NOT NULL,
    flag_type character varying NOT NULL,
    org_id character varying NOT NULL,
    site_id character varying NOT NULL
);


ALTER TABLE public.case_flag OWNER TO postgres;

--
-- Name: case_forecast; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_forecast (
    id uuid NOT NULL,
    case_id character varying NOT NULL,
    forecast_start_time timestamp with time zone NOT NULL,
    forecast_end_time timestamp with time zone NOT NULL,
    forecast_variant character varying NOT NULL,
    room_id character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    org_id character varying NOT NULL,
    site_id character varying NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    forecast_status public.caseforecaststatus DEFAULT 'VALID'::public.caseforecaststatus NOT NULL,
    static_duration_minutes double precision,
    pythia_duration_minutes double precision,
    static_duration_end_time timestamp with time zone,
    transformer_end_time timestamp with time zone,
    pythia_end_time timestamp with time zone,
    turnover_duration_minutes double precision,
    static_start_offset_minutes double precision,
    is_auto_follow boolean,
    is_overtime boolean,
    pythia_prediction_tag character varying,
    bayesian_duration_minutes double precision,
    bayesian_end_time timestamp with time zone,
    case_start_source character varying
);


ALTER TABLE public.case_forecast OWNER TO postgres;

--
-- Name: case_forecast_history; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_forecast_history (
    id uuid NOT NULL,
    case_id character varying NOT NULL,
    forecast_start_time timestamp with time zone NOT NULL,
    forecast_end_time timestamp with time zone NOT NULL,
    forecast_variant character varying NOT NULL,
    room_id character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    org_id character varying NOT NULL,
    site_id character varying NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    forecast_status public.caseforecaststatus DEFAULT 'VALID'::public.caseforecaststatus NOT NULL,
    static_duration_minutes double precision,
    pythia_duration_minutes double precision,
    static_duration_end_time timestamp with time zone,
    transformer_end_time timestamp with time zone,
    pythia_end_time timestamp with time zone,
    turnover_duration_minutes double precision,
    static_start_offset_minutes double precision,
    is_auto_follow boolean,
    is_overtime boolean,
    pythia_prediction_tag character varying,
    bayesian_duration_minutes double precision,
    bayesian_end_time timestamp with time zone,
    case_start_source character varying
);


ALTER TABLE public.case_forecast_history OWNER TO postgres;

--
-- Name: case_label_assoc; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_label_assoc (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    option_id uuid NOT NULL,
    case_id character varying NOT NULL,
    updated_by_user_id character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    archived_time timestamp with time zone
);


ALTER TABLE public.case_label_assoc OWNER TO postgres;

--
-- Name: case_label_categories; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_label_categories (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name character varying NOT NULL,
    site_id character varying NOT NULL,
    org_id character varying NOT NULL
);


ALTER TABLE public.case_label_categories OWNER TO postgres;

--
-- Name: case_label_field_options; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_label_field_options (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    field_id uuid NOT NULL,
    color character varying NOT NULL,
    abbreviation character varying NOT NULL,
    value character varying NOT NULL
);


ALTER TABLE public.case_label_field_options OWNER TO postgres;

--
-- Name: case_label_fields; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_label_fields (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name character varying NOT NULL,
    type public.caselabelfieldtype NOT NULL,
    category_id uuid NOT NULL,
    ordinal integer DEFAULT 0 NOT NULL,
    site_id character varying NOT NULL,
    org_id character varying NOT NULL
);


ALTER TABLE public.case_label_fields OWNER TO postgres;

--
-- Name: case_matching_status_reason; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_matching_status_reason (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid NOT NULL,
    explanation_for_change character varying NOT NULL,
    case_id character varying NOT NULL,
    case_matching_status public.casematchingstatus NOT NULL,
    version integer DEFAULT 1 NOT NULL
);


ALTER TABLE public.case_matching_status_reason OWNER TO postgres;

--
-- Name: case_matching_status_reason_history; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_matching_status_reason_history (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid NOT NULL,
    explanation_for_change character varying NOT NULL,
    case_id character varying NOT NULL,
    case_matching_status public.casematchingstatus NOT NULL,
    version integer DEFAULT 1 NOT NULL
);


ALTER TABLE public.case_matching_status_reason_history OWNER TO postgres;

--
-- Name: case_note_plan; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_note_plan (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    case_id character varying NOT NULL,
    note character varying NOT NULL,
    org_id character varying NOT NULL,
    site_id character varying NOT NULL
);


ALTER TABLE public.case_note_plan OWNER TO postgres;

--
-- Name: case_procedures; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_procedures (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    case_id character varying NOT NULL,
    procedure_id uuid NOT NULL,
    archived_time timestamp with time zone,
    hierarchy integer,
    anesthesia_id uuid
);


ALTER TABLE public.case_procedures OWNER TO postgres;

--
-- Name: case_raw; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_raw (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    raw_id character varying NOT NULL,
    raw json NOT NULL,
    case_id character varying,
    event_time timestamp with time zone,
    event_type character varying,
    org_id character varying NOT NULL,
    external_message_id character varying NOT NULL,
    external_case_id character varying,
    archived_at timestamp with time zone
);


ALTER TABLE public.case_raw OWNER TO postgres;

--
-- Name: case_staff; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_staff (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    case_id character varying NOT NULL,
    staff_id uuid NOT NULL,
    role character varying,
    archived_time timestamp with time zone
);


ALTER TABLE public.case_staff OWNER TO postgres;

--
-- Name: case_staff_plan; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_staff_plan (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    case_id character varying,
    staff_id uuid,
    role character varying,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    archived_time timestamp with time zone,
    org_id character varying NOT NULL,
    site_id character varying NOT NULL
);


ALTER TABLE public.case_staff_plan OWNER TO postgres;

--
-- Name: case_to_block_overrides; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_to_block_overrides (
    block_date date NOT NULL,
    case_id character varying NOT NULL,
    block_id character varying NOT NULL,
    utilized_procedure_minutes integer,
    utilized_turnover_minutes integer,
    user_id character varying NOT NULL,
    note character varying,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    version integer DEFAULT 1 NOT NULL
);


ALTER TABLE public.case_to_block_overrides OWNER TO postgres;

--
-- Name: case_to_block_overrides_history; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_to_block_overrides_history (
    block_date date NOT NULL,
    case_id character varying NOT NULL,
    block_id character varying NOT NULL,
    utilized_procedure_minutes integer,
    utilized_turnover_minutes integer,
    user_id character varying NOT NULL,
    note character varying,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    version integer DEFAULT 1 NOT NULL
);


ALTER TABLE public.case_to_block_overrides_history OWNER TO postgres;

--
-- Name: cases; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cases (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    case_id character varying NOT NULL,
    external_case_id character varying NOT NULL,
    org_id character varying NOT NULL,
    site_id character varying NOT NULL,
    room_id character varying NOT NULL,
    scheduled_start_time timestamp with time zone NOT NULL,
    scheduled_end_time timestamp with time zone NOT NULL,
    status character varying NOT NULL,
    case_classification_types_id character varying,
    service_line_id uuid,
    is_add_on boolean,
    patient_class public.patientclass,
    version integer DEFAULT 1 NOT NULL,
    cancellation_reason character varying[],
    latest_processed_message_id character varying
);


ALTER TABLE public.cases OWNER TO postgres;

--
-- Name: cases_history; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cases_history (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    case_id character varying NOT NULL,
    external_case_id character varying NOT NULL,
    room_id character varying NOT NULL,
    case_classification_types_id character varying,
    scheduled_start_time timestamp with time zone NOT NULL,
    scheduled_end_time timestamp with time zone NOT NULL,
    status character varying NOT NULL,
    service_line_id uuid,
    is_add_on boolean,
    patient_class public.patientclass,
    org_id character varying NOT NULL,
    site_id character varying NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    cancellation_reason character varying[],
    latest_processed_message_id character varying
);


ALTER TABLE public.cases_history OWNER TO postgres;

--
-- Name: cluster; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cluster (
    id uuid NOT NULL,
    name character varying(255) NOT NULL,
    enable_audio boolean NOT NULL
);


ALTER TABLE public.cluster OWNER TO postgres;

--
-- Name: cluster_mapping; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cluster_mapping (
    cluster_id uuid NOT NULL,
    site_id character varying NOT NULL
);


ALTER TABLE public.cluster_mapping OWNER TO postgres;

--
-- Name: contact_information; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.contact_information (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid NOT NULL,
    type public.contactinformationtype NOT NULL,
    contact_information_value character varying NOT NULL,
    initial_notification_sent timestamp with time zone,
    first_name character varying,
    last_name character varying,
    is_apella_employee boolean,
    CONSTRAINT ck_contactinformation_type CHECK (((type = 'PHONE_NUMBER'::public.contactinformationtype) AND ((contact_information_value)::text ~ '^\+?[0-9]{0,3}[0-9]{10}$'::text)))
);


ALTER TABLE public.contact_information OWNER TO postgres;

--
-- Name: custom_phase_configs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.custom_phase_configs (
    id uuid NOT NULL,
    start_event_type character varying NOT NULL,
    end_event_type character varying NOT NULL,
    name character varying NOT NULL,
    description character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    org_id character varying NOT NULL
);


ALTER TABLE public.custom_phase_configs OWNER TO postgres;

--
-- Name: event_dashboard_visibility; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.event_dashboard_visibility (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    event_type_id character varying NOT NULL,
    org_id_filter character varying[],
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now()
);


ALTER TABLE public.event_dashboard_visibility OWNER TO postgres;

--
-- Name: event_label_options; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.event_label_options (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name character varying(128) NOT NULL
);


ALTER TABLE public.event_label_options OWNER TO postgres;

--
-- Name: event_types; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.event_types (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id character varying NOT NULL,
    name character varying NOT NULL,
    description character varying,
    type character varying NOT NULL,
    color character varying NOT NULL,
    hidden boolean DEFAULT false NOT NULL
);


ALTER TABLE public.event_types OWNER TO postgres;

--
-- Name: events; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.events (
    id character varying NOT NULL,
    start_time timestamp with time zone NOT NULL,
    process_timestamp timestamp with time zone NOT NULL,
    org_id character varying NOT NULL,
    site_id character varying NOT NULL,
    room_id character varying NOT NULL,
    case_id character varying,
    source character varying NOT NULL,
    model_version character varying,
    source_type character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    labels character varying[],
    notes character varying,
    camera_id character varying,
    confidence double precision,
    event_type_id character varying NOT NULL,
    deleted_at timestamp with time zone,
    version integer DEFAULT 1 NOT NULL,
    event_matching_status public.eventmatchingstatus DEFAULT 'AUTOMATIC'::public.eventmatchingstatus,
    etag uuid DEFAULT gen_random_uuid()
);


ALTER TABLE public.events OWNER TO postgres;

--
-- Name: events_history; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.events_history (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id character varying NOT NULL,
    start_time timestamp with time zone NOT NULL,
    process_timestamp timestamp with time zone NOT NULL,
    deleted_at timestamp with time zone,
    event_type_id character varying NOT NULL,
    site_id character varying NOT NULL,
    room_id character varying NOT NULL,
    camera_id character varying,
    case_id character varying,
    source character varying NOT NULL,
    model_version character varying,
    source_type character varying NOT NULL,
    confidence double precision,
    labels character varying[],
    notes character varying,
    org_id character varying NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    event_matching_status public.eventmatchingstatus,
    etag uuid
);


ALTER TABLE public.events_history OWNER TO postgres;

--
-- Name: highlight_assets; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.highlight_assets (
    highlight_id uuid NOT NULL,
    asset_id character varying NOT NULL
);


ALTER TABLE public.highlight_assets OWNER TO postgres;

--
-- Name: highlight_feedback; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.highlight_feedback (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid NOT NULL,
    comment character varying,
    rating integer,
    highlight_id uuid NOT NULL,
    user_id character varying NOT NULL,
    org_id character varying NOT NULL
);


ALTER TABLE public.highlight_feedback OWNER TO postgres;

--
-- Name: highlight_users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.highlight_users (
    highlight_id uuid NOT NULL,
    user_id character varying NOT NULL
);


ALTER TABLE public.highlight_users OWNER TO postgres;

--
-- Name: highlights; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.highlights (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid NOT NULL,
    description character varying NOT NULL,
    org_id character varying NOT NULL,
    end_time timestamp with time zone NOT NULL,
    start_time timestamp with time zone NOT NULL,
    room_id character varying NOT NULL,
    site_id character varying NOT NULL,
    category character varying NOT NULL,
    archived_time timestamp with time zone
);


ALTER TABLE public.highlights OWNER TO postgres;

--
-- Name: identifier_mapping; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.identifier_mapping (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone,
    external_id_type character varying NOT NULL,
    external_id character varying NOT NULL,
    internal_id character varying NOT NULL
);


ALTER TABLE public.identifier_mapping OWNER TO postgres;

--
-- Name: measurement_periods; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.measurement_periods (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name character varying NOT NULL,
    measurement_period daterange NOT NULL,
    site_id character varying NOT NULL,
    annotation_task_type_id uuid,
    room_ids character varying[] NOT NULL,
    days_of_week public.day_of_week[] NOT NULL,
    start_date date NOT NULL,
    end_date date NOT NULL
);


ALTER TABLE public.measurement_periods OWNER TO postgres;

--
-- Name: observation_type_names; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.observation_type_names (
    type_id character varying NOT NULL,
    name character varying NOT NULL,
    color character varying DEFAULT '#C0997B'::character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    org_id character varying NOT NULL,
    is_custom_phase_end_point boolean DEFAULT false NOT NULL,
    CONSTRAINT ck_observationtype_color CHECK (((color)::text ~ '^#[0-9a-fA-F]{6}$'::text))
);


ALTER TABLE public.observation_type_names OWNER TO postgres;

--
-- Name: observation_types; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.observation_types (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id character varying NOT NULL,
    name character varying NOT NULL,
    color character varying DEFAULT '#C0997B'::character varying NOT NULL,
    description character varying,
    CONSTRAINT observation_types_color_check CHECK (((color)::text ~* '^#[0-9a-fA-F]{6}$'::text))
);


ALTER TABLE public.observation_types OWNER TO postgres;

--
-- Name: observations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.observations (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid NOT NULL,
    case_id character varying NOT NULL,
    type_id character varying NOT NULL,
    observation_time timestamp with time zone NOT NULL,
    recorded_time timestamp with time zone,
    org_id character varying NOT NULL
);


ALTER TABLE public.observations OWNER TO postgres;

--
-- Name: organizations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.organizations (
    id character varying NOT NULL,
    name character varying NOT NULL,
    auth0_org_id character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now()
);


ALTER TABLE public.organizations OWNER TO postgres;

--
-- Name: phase_relationships; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.phase_relationships (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    parent_phase_id uuid NOT NULL,
    child_phase_id uuid NOT NULL,
    org_id character varying NOT NULL
);


ALTER TABLE public.phase_relationships OWNER TO postgres;

--
-- Name: phase_types; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.phase_types (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id character varying NOT NULL,
    title character varying NOT NULL,
    slug character varying NOT NULL,
    description character varying NOT NULL
);


ALTER TABLE public.phase_types OWNER TO postgres;

--
-- Name: phases; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.phases (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid NOT NULL,
    site_id character varying NOT NULL,
    room_id character varying NOT NULL,
    type_id character varying NOT NULL,
    start_event_id character varying NOT NULL,
    end_event_id character varying,
    case_id character varying,
    source_type character varying NOT NULL,
    org_id character varying NOT NULL,
    status public.phasestatus DEFAULT 'VALID'::public.phasestatus NOT NULL,
    invalidation_reason character varying,
    event_matching_status public.eventmatchingstatus DEFAULT 'AUTOMATIC'::public.eventmatchingstatus,
    etag uuid DEFAULT gen_random_uuid(),
    version integer DEFAULT 1 NOT NULL
);


ALTER TABLE public.phases OWNER TO postgres;

--
-- Name: phases_history; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.phases_history (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    event_matching_status public.eventmatchingstatus,
    etag uuid,
    id uuid NOT NULL,
    room_id character varying NOT NULL,
    type_id character varying NOT NULL,
    start_event_id character varying NOT NULL,
    end_event_id character varying,
    case_id character varying,
    source_type character varying NOT NULL,
    status public.phasestatus DEFAULT 'VALID'::public.phasestatus NOT NULL,
    invalidation_reason character varying,
    org_id character varying NOT NULL,
    site_id character varying NOT NULL,
    version integer DEFAULT 1 NOT NULL
);


ALTER TABLE public.phases_history OWNER TO postgres;

--
-- Name: procedures; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.procedures (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid NOT NULL,
    name character varying NOT NULL,
    org_id character varying NOT NULL
);


ALTER TABLE public.procedures OWNER TO postgres;

--
-- Name: room_closures; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.room_closures (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    room_id character varying NOT NULL,
    start_time timestamp with time zone NOT NULL,
    end_time timestamp with time zone NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    reason character varying,
    CONSTRAINT ck_room_closures_start_time_lt_end_time CHECK ((start_time < end_time))
);


ALTER TABLE public.room_closures OWNER TO postgres;

--
-- Name: room_default_camera; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.room_default_camera (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    room_id character varying NOT NULL,
    camera_id character varying NOT NULL,
    description character varying
);


ALTER TABLE public.room_default_camera OWNER TO postgres;

--
-- Name: room_first_case_configs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.room_first_case_configs (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    room_id character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    sunday_start_time time without time zone,
    sunday_end_time time without time zone,
    monday_start_time time without time zone,
    monday_end_time time without time zone,
    tuesday_start_time time without time zone,
    tuesday_end_time time without time zone,
    wednesday_start_time time without time zone,
    wednesday_end_time time without time zone,
    thursday_start_time time without time zone,
    thursday_end_time time without time zone,
    friday_start_time time without time zone,
    friday_end_time time without time zone,
    saturday_start_time time without time zone,
    saturday_end_time time without time zone,
    CONSTRAINT ck_room_first_case_configs_friday_valid_times CHECK ((((friday_start_time IS NULL) AND (friday_end_time IS NULL)) OR ((friday_start_time IS NOT NULL) AND (friday_end_time IS NOT NULL) AND (friday_start_time < friday_end_time)))),
    CONSTRAINT ck_room_first_case_configs_monday_valid_times CHECK ((((monday_start_time IS NULL) AND (monday_end_time IS NULL)) OR ((monday_start_time IS NOT NULL) AND (monday_end_time IS NOT NULL) AND (monday_start_time < monday_end_time)))),
    CONSTRAINT ck_room_first_case_configs_saturday_valid_times CHECK ((((saturday_start_time IS NULL) AND (saturday_end_time IS NULL)) OR ((saturday_start_time IS NOT NULL) AND (saturday_end_time IS NOT NULL) AND (saturday_start_time < saturday_end_time)))),
    CONSTRAINT ck_room_first_case_configs_sunday_valid_times CHECK ((((sunday_start_time IS NULL) AND (sunday_end_time IS NULL)) OR ((sunday_start_time IS NOT NULL) AND (sunday_end_time IS NOT NULL) AND (sunday_start_time < sunday_end_time)))),
    CONSTRAINT ck_room_first_case_configs_thursday_valid_times CHECK ((((thursday_start_time IS NULL) AND (thursday_end_time IS NULL)) OR ((thursday_start_time IS NOT NULL) AND (thursday_end_time IS NOT NULL) AND (thursday_start_time < thursday_end_time)))),
    CONSTRAINT ck_room_first_case_configs_tuesday_valid_times CHECK ((((tuesday_start_time IS NULL) AND (tuesday_end_time IS NULL)) OR ((tuesday_start_time IS NOT NULL) AND (tuesday_end_time IS NOT NULL) AND (tuesday_start_time < tuesday_end_time)))),
    CONSTRAINT ck_room_first_case_configs_wednesday_valid_times CHECK ((((wednesday_start_time IS NULL) AND (wednesday_end_time IS NULL)) OR ((wednesday_start_time IS NOT NULL) AND (wednesday_end_time IS NOT NULL) AND (wednesday_start_time < wednesday_end_time))))
);


ALTER TABLE public.room_first_case_configs OWNER TO postgres;

--
-- Name: room_prime_time_configs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.room_prime_time_configs (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    room_id character varying NOT NULL,
    sunday_start_time time without time zone,
    sunday_end_time time without time zone,
    monday_start_time time without time zone,
    monday_end_time time without time zone,
    tuesday_start_time time without time zone,
    tuesday_end_time time without time zone,
    wednesday_start_time time without time zone,
    wednesday_end_time time without time zone,
    thursday_start_time time without time zone,
    thursday_end_time time without time zone,
    friday_start_time time without time zone,
    friday_end_time time without time zone,
    saturday_start_time time without time zone,
    saturday_end_time time without time zone,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    CONSTRAINT ck_room_prime_time_configs_friday_valid_times CHECK ((((friday_start_time IS NULL) AND (friday_end_time IS NULL)) OR ((friday_start_time IS NOT NULL) AND (friday_end_time IS NOT NULL) AND (friday_start_time < friday_end_time)))),
    CONSTRAINT ck_room_prime_time_configs_monday_valid_times CHECK ((((monday_start_time IS NULL) AND (monday_end_time IS NULL)) OR ((monday_start_time IS NOT NULL) AND (monday_end_time IS NOT NULL) AND (monday_start_time < monday_end_time)))),
    CONSTRAINT ck_room_prime_time_configs_saturday_valid_times CHECK ((((saturday_start_time IS NULL) AND (saturday_end_time IS NULL)) OR ((saturday_start_time IS NOT NULL) AND (saturday_end_time IS NOT NULL) AND (saturday_start_time < saturday_end_time)))),
    CONSTRAINT ck_room_prime_time_configs_sunday_valid_times CHECK ((((sunday_start_time IS NULL) AND (sunday_end_time IS NULL)) OR ((sunday_start_time IS NOT NULL) AND (sunday_end_time IS NOT NULL) AND (sunday_start_time < sunday_end_time)))),
    CONSTRAINT ck_room_prime_time_configs_thursday_valid_times CHECK ((((thursday_start_time IS NULL) AND (thursday_end_time IS NULL)) OR ((thursday_start_time IS NOT NULL) AND (thursday_end_time IS NOT NULL) AND (thursday_start_time < thursday_end_time)))),
    CONSTRAINT ck_room_prime_time_configs_tuesday_valid_times CHECK ((((tuesday_start_time IS NULL) AND (tuesday_end_time IS NULL)) OR ((tuesday_start_time IS NOT NULL) AND (tuesday_end_time IS NOT NULL) AND (tuesday_start_time < tuesday_end_time)))),
    CONSTRAINT ck_room_prime_time_configs_wednesday_valid_times CHECK ((((wednesday_start_time IS NULL) AND (wednesday_end_time IS NULL)) OR ((wednesday_start_time IS NOT NULL) AND (wednesday_end_time IS NOT NULL) AND (wednesday_start_time < wednesday_end_time))))
);


ALTER TABLE public.room_prime_time_configs OWNER TO postgres;

--
-- Name: room_taggings; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.room_taggings (
    room_id character varying NOT NULL,
    tag_id uuid NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now()
);


ALTER TABLE public.room_taggings OWNER TO postgres;

--
-- Name: room_tags; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.room_tags (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    org_id character varying NOT NULL,
    color character varying DEFAULT '#999999'::character varying NOT NULL
);


ALTER TABLE public.room_tags OWNER TO postgres;

--
-- Name: rooms; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.rooms (
    id character varying NOT NULL,
    name character varying NOT NULL,
    site_id character varying NOT NULL,
    org_id character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    privacy_enabled_at timestamp with time zone,
    privacy_updated_by_user_id character varying,
    sort_key character varying,
    labels json,
    is_forecasting_enabled boolean DEFAULT true NOT NULL
);


ALTER TABLE public.rooms OWNER TO postgres;

--
-- Name: service_lines; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.service_lines (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid NOT NULL,
    external_service_line_id character varying NOT NULL,
    name character varying,
    org_id character varying NOT NULL
);


ALTER TABLE public.service_lines OWNER TO postgres;

--
-- Name: site_capacity_constraints; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.site_capacity_constraints (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    day_of_week integer NOT NULL,
    count integer NOT NULL,
    start_time time without time zone NOT NULL,
    site_id character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    CONSTRAINT check_count_is_positive CHECK ((count > 0)),
    CONSTRAINT check_day_of_week_valid CHECK (((day_of_week >= 0) AND (day_of_week <= 6)))
);


ALTER TABLE public.site_capacity_constraints OWNER TO postgres;

--
-- Name: site_closures; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.site_closures (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    site_id character varying NOT NULL,
    closure_date date NOT NULL,
    reason character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    CONSTRAINT ck_site_closures_reason_min_len_and_trimmed CHECK (((length(btrim((reason)::text)) >= 5) AND (btrim((reason)::text) = (reason)::text)))
);


ALTER TABLE public.site_closures OWNER TO postgres;

--
-- Name: site_first_case_configs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.site_first_case_configs (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    site_id character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    sunday_start_time time without time zone,
    sunday_end_time time without time zone,
    monday_start_time time without time zone,
    monday_end_time time without time zone,
    tuesday_start_time time without time zone,
    tuesday_end_time time without time zone,
    wednesday_start_time time without time zone,
    wednesday_end_time time without time zone,
    thursday_start_time time without time zone,
    thursday_end_time time without time zone,
    friday_start_time time without time zone,
    friday_end_time time without time zone,
    saturday_start_time time without time zone,
    saturday_end_time time without time zone,
    CONSTRAINT ck_first_case_configs_friday_valid_times CHECK ((((friday_start_time IS NULL) AND (friday_end_time IS NULL)) OR ((friday_start_time IS NOT NULL) AND (friday_end_time IS NOT NULL) AND (friday_start_time < friday_end_time)))),
    CONSTRAINT ck_first_case_configs_monday_valid_times CHECK ((((monday_start_time IS NULL) AND (monday_end_time IS NULL)) OR ((monday_start_time IS NOT NULL) AND (monday_end_time IS NOT NULL) AND (monday_start_time < monday_end_time)))),
    CONSTRAINT ck_first_case_configs_saturday_valid_times CHECK ((((saturday_start_time IS NULL) AND (saturday_end_time IS NULL)) OR ((saturday_start_time IS NOT NULL) AND (saturday_end_time IS NOT NULL) AND (saturday_start_time < saturday_end_time)))),
    CONSTRAINT ck_first_case_configs_sunday_valid_times CHECK ((((sunday_start_time IS NULL) AND (sunday_end_time IS NULL)) OR ((sunday_start_time IS NOT NULL) AND (sunday_end_time IS NOT NULL) AND (sunday_start_time < sunday_end_time)))),
    CONSTRAINT ck_first_case_configs_thursday_valid_times CHECK ((((thursday_start_time IS NULL) AND (thursday_end_time IS NULL)) OR ((thursday_start_time IS NOT NULL) AND (thursday_end_time IS NOT NULL) AND (thursday_start_time < thursday_end_time)))),
    CONSTRAINT ck_first_case_configs_tuesday_valid_times CHECK ((((tuesday_start_time IS NULL) AND (tuesday_end_time IS NULL)) OR ((tuesday_start_time IS NOT NULL) AND (tuesday_end_time IS NOT NULL) AND (tuesday_start_time < tuesday_end_time)))),
    CONSTRAINT ck_first_case_configs_wednesday_valid_times CHECK ((((wednesday_start_time IS NULL) AND (wednesday_end_time IS NULL)) OR ((wednesday_start_time IS NOT NULL) AND (wednesday_end_time IS NOT NULL) AND (wednesday_start_time < wednesday_end_time))))
);


ALTER TABLE public.site_first_case_configs OWNER TO postgres;

--
-- Name: site_launch_information; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.site_launch_information (
    site_id character varying,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    actual_launch_date date,
    anticipated_launch_date date,
    site_name character varying NOT NULL,
    notion_id character varying NOT NULL
);


ALTER TABLE public.site_launch_information OWNER TO postgres;

--
-- Name: site_prime_time_configs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.site_prime_time_configs (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    site_id character varying NOT NULL,
    sunday_start_time time without time zone,
    sunday_end_time time without time zone,
    monday_start_time time without time zone,
    monday_end_time time without time zone,
    tuesday_start_time time without time zone,
    tuesday_end_time time without time zone,
    wednesday_start_time time without time zone,
    wednesday_end_time time without time zone,
    thursday_start_time time without time zone,
    thursday_end_time time without time zone,
    friday_start_time time without time zone,
    friday_end_time time without time zone,
    saturday_start_time time without time zone,
    saturday_end_time time without time zone,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    CONSTRAINT ck_site_prime_time_configs_friday_valid_times CHECK ((((friday_start_time IS NULL) AND (friday_end_time IS NULL)) OR ((friday_start_time IS NOT NULL) AND (friday_end_time IS NOT NULL) AND (friday_start_time < friday_end_time)))),
    CONSTRAINT ck_site_prime_time_configs_monday_valid_times CHECK ((((monday_start_time IS NULL) AND (monday_end_time IS NULL)) OR ((monday_start_time IS NOT NULL) AND (monday_end_time IS NOT NULL) AND (monday_start_time < monday_end_time)))),
    CONSTRAINT ck_site_prime_time_configs_saturday_valid_times CHECK ((((saturday_start_time IS NULL) AND (saturday_end_time IS NULL)) OR ((saturday_start_time IS NOT NULL) AND (saturday_end_time IS NOT NULL) AND (saturday_start_time < saturday_end_time)))),
    CONSTRAINT ck_site_prime_time_configs_sunday_valid_times CHECK ((((sunday_start_time IS NULL) AND (sunday_end_time IS NULL)) OR ((sunday_start_time IS NOT NULL) AND (sunday_end_time IS NOT NULL) AND (sunday_start_time < sunday_end_time)))),
    CONSTRAINT ck_site_prime_time_configs_thursday_valid_times CHECK ((((thursday_start_time IS NULL) AND (thursday_end_time IS NULL)) OR ((thursday_start_time IS NOT NULL) AND (thursday_end_time IS NOT NULL) AND (thursday_start_time < thursday_end_time)))),
    CONSTRAINT ck_site_prime_time_configs_tuesday_valid_times CHECK ((((tuesday_start_time IS NULL) AND (tuesday_end_time IS NULL)) OR ((tuesday_start_time IS NOT NULL) AND (tuesday_end_time IS NOT NULL) AND (tuesday_start_time < tuesday_end_time)))),
    CONSTRAINT ck_site_prime_time_configs_wednesday_valid_times CHECK ((((wednesday_start_time IS NULL) AND (wednesday_end_time IS NULL)) OR ((wednesday_start_time IS NOT NULL) AND (wednesday_end_time IS NOT NULL) AND (wednesday_start_time < wednesday_end_time))))
);


ALTER TABLE public.site_prime_time_configs OWNER TO postgres;

--
-- Name: sites; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.sites (
    id character varying NOT NULL,
    name character varying NOT NULL,
    org_id character varying NOT NULL,
    timezone character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now()
);


ALTER TABLE public.sites OWNER TO postgres;

--
-- Name: staff; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.staff (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid NOT NULL,
    first_name character varying NOT NULL,
    last_name character varying NOT NULL,
    org_id character varying NOT NULL,
    external_staff_id character varying NOT NULL,
    archived_time timestamp with time zone
);


ALTER TABLE public.staff OWNER TO postgres;

--
-- Name: staff_codes; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.staff_codes (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid NOT NULL,
    staff_id uuid,
    coding_system character varying NOT NULL,
    code character varying NOT NULL,
    org_id character varying NOT NULL
);


ALTER TABLE public.staff_codes OWNER TO postgres;

--
-- Name: staff_event_notification; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.staff_event_notification (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid NOT NULL,
    message_id character varying NOT NULL,
    staff_event_contact_information_id uuid NOT NULL,
    event_id character varying NOT NULL,
    case_id character varying,
    event_time timestamp with time zone NOT NULL,
    sent_status public.sentnotificationstatus DEFAULT 'SENT'::public.sentnotificationstatus NOT NULL,
    attempts integer DEFAULT 1 NOT NULL,
    duplicated_id uuid,
    is_excess boolean NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    sent_time timestamp with time zone
);


ALTER TABLE public.staff_event_notification OWNER TO postgres;

--
-- Name: staff_event_notification_contact_information; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.staff_event_notification_contact_information (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    archived_time timestamp with time zone,
    staff_id uuid NOT NULL,
    contact_information_id uuid NOT NULL,
    event_type_id character varying NOT NULL,
    id uuid
);


ALTER TABLE public.staff_event_notification_contact_information OWNER TO postgres;

--
-- Name: staff_event_notification_history; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.staff_event_notification_history (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid NOT NULL,
    message_id character varying NOT NULL,
    staff_event_contact_information_id uuid NOT NULL,
    event_id character varying NOT NULL,
    case_id character varying,
    event_time timestamp with time zone NOT NULL,
    sent_status public.sentnotificationstatus DEFAULT 'SENT'::public.sentnotificationstatus NOT NULL,
    attempts integer DEFAULT 1 NOT NULL,
    duplicated_id uuid,
    is_excess boolean NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    sent_time timestamp with time zone
);


ALTER TABLE public.staff_event_notification_history OWNER TO postgres;

--
-- Name: staff_event_notification_old_01; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.staff_event_notification_old_01 (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid NOT NULL,
    message_id character varying NOT NULL,
    staff_event_contact_information_id uuid NOT NULL,
    event_id character varying NOT NULL,
    case_id character varying,
    event_time timestamp with time zone NOT NULL
);


ALTER TABLE public.staff_event_notification_old_01 OWNER TO postgres;

--
-- Name: staff_role; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.staff_role (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id character varying NOT NULL,
    name character varying NOT NULL
);


ALTER TABLE public.staff_role OWNER TO postgres;

--
-- Name: staffing_needs_ratio; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.staffing_needs_ratio (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    archived_time timestamp with time zone,
    id character varying NOT NULL,
    set_by_user_id character varying NOT NULL,
    archived_by_user_id character varying,
    ratio double precision,
    staff_role_id character varying NOT NULL,
    org_id character varying NOT NULL,
    site_id character varying NOT NULL
);


ALTER TABLE public.staffing_needs_ratio OWNER TO postgres;

--
-- Name: terminal_clean_scores; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.terminal_clean_scores (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    room_id character varying NOT NULL,
    date date NOT NULL,
    comments character varying DEFAULT ''::character varying NOT NULL,
    score public.cleanscoreenum,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now()
);


ALTER TABLE public.terminal_clean_scores OWNER TO postgres;

--
-- Name: turnover_goals; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.turnover_goals (
    id uuid NOT NULL,
    goal_minutes integer,
    max_minutes integer NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    org_id character varying NOT NULL,
    site_id character varying NOT NULL,
    CONSTRAINT goal_minutes_positive CHECK ((goal_minutes > 0)),
    CONSTRAINT max_minutes_positive CHECK ((max_minutes > 0))
);


ALTER TABLE public.turnover_goals OWNER TO postgres;

--
-- Name: turnover_label_assoc; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.turnover_label_assoc (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    preceding_case_id character varying NOT NULL,
    following_case_id character varying NOT NULL,
    turnover_label_id uuid NOT NULL,
    updated_by_user_id character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    org_id character varying NOT NULL,
    archived_time timestamp with time zone
);


ALTER TABLE public.turnover_label_assoc OWNER TO postgres;

--
-- Name: turnover_labels; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.turnover_labels (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name character varying NOT NULL,
    type character varying NOT NULL
);


ALTER TABLE public.turnover_labels OWNER TO postgres;

--
-- Name: turnover_notes; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.turnover_notes (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    preceding_case_id character varying NOT NULL,
    following_case_id character varying NOT NULL,
    note character varying NOT NULL,
    updated_by_user_id character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    org_id character varying NOT NULL
);


ALTER TABLE public.turnover_notes OWNER TO postgres;

--
-- Name: user_filter_views; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_filter_views (
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now(),
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name character varying(128) NOT NULL,
    url character varying NOT NULL,
    user_id character varying NOT NULL,
    org_id character varying NOT NULL
);


ALTER TABLE public.user_filter_views OWNER TO postgres;

--
-- Name: user_room_default_camera; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_room_default_camera (
    room_id character varying NOT NULL,
    camera_id character varying,
    user_id character varying NOT NULL,
    created_time timestamp with time zone DEFAULT now() NOT NULL,
    updated_time timestamp with time zone DEFAULT now()
);


ALTER TABLE public.user_room_default_camera OWNER TO postgres;

--
-- Name: vw_events_gt_and_prediction; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.vw_events_gt_and_prediction AS
 SELECT events.id,
    events.event_type_id,
    events.start_time,
    events.process_timestamp,
    events.created_time,
    events.updated_time,
    events.org_id,
    events.site_id,
    events.room_id,
    events.camera_id,
    events.case_id,
    events.source,
    events.model_version,
    events.source_type,
    events.confidence,
    events.labels,
    events.notes
   FROM public.events
  WHERE ((events.deleted_at IS NULL) AND ((events.source_type)::text <> 'prediction'::text))
UNION ALL
 SELECT events_history.id,
    events_history.event_type_id,
    events_history.start_time,
    events_history.process_timestamp,
    events_history.created_time,
    events_history.updated_time,
    events_history.org_id,
    events_history.site_id,
    events_history.room_id,
    events_history.camera_id,
    events_history.case_id,
    events_history.source,
    events_history.model_version,
    events_history.source_type,
    events_history.confidence,
    events_history.labels,
    events_history.notes
   FROM public.events_history
  WHERE ((events_history.source_type)::text = 'prediction'::text);


ALTER TABLE public.vw_events_gt_and_prediction OWNER TO postgres;

--
-- Data for Name: alembic_version; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.alembic_version (version_num) FROM stdin;
1c25523af161
\.


--
-- Data for Name: anesthesias; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.anesthesias (created_time, updated_time, id, name, org_id) FROM stdin;
\.


--
-- Data for Name: annotation_task_schedules; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.annotation_task_schedules (created_time, updated_time, id, start_time, "interval", annotation_task_type_id) FROM stdin;
\.


--
-- Data for Name: annotation_task_schedules_rooms; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.annotation_task_schedules_rooms (schedule_id, room_id) FROM stdin;
\.


--
-- Data for Name: annotation_task_schedules_sites; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.annotation_task_schedules_sites (schedule_id, site_id) FROM stdin;
\.


--
-- Data for Name: annotation_task_type_annotators; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.annotation_task_type_annotators (created_time, updated_time, annotation_task_type_id, user_id) FROM stdin;
\.


--
-- Data for Name: annotation_task_type_provisional_annotators; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.annotation_task_type_provisional_annotators (created_time, updated_time, annotation_task_type_id, user_id) FROM stdin;
\.


--
-- Data for Name: annotation_task_type_reviewers; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.annotation_task_type_reviewers (created_time, updated_time, annotation_task_type_id, user_id) FROM stdin;
\.


--
-- Data for Name: annotation_task_types; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.annotation_task_types (created_time, updated_time, name, description, event_types, id, archived_time, priority, detect_idle, context_event_types, allow_skipping_review, version, optimize_tasks) FROM stdin;
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	Light	Is it on or off? ¿Porque no los dos?	{lights_off,lights_on}	f775f20b-0aff-48b2-9808-35622ad554ed	\N	5	t	{}	f	1	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	Mop	The mop type concerns all mop-related events	{mop,mop_in,mop_out}	4d426c27-fd3c-4b88-8cb4-fdf2338b97d8	\N	5	t	{}	f	1	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	Surgery	The default task type containing all events	{back_table_open,case_cart_in,case_cart_out,case_cart_visible,cleaning_crew_in,cleaning_crew_out,closing_start,count_end,count_start,endo_pack_open,endoscopy_end,endoscopy_start,extubation,first_incision_start,intubation,lights_off,lights_on,mop,mop_in,mop_out,no_case_cart,no_mop,no_patient,or_table_ready,patient_briefing_end,patient_briefing_start,patient_draped,patient_imaging_end,patient_imaging_start,patient_on_hospital_bed,patient_on_or_table,patient_skin_prep_end,patient_skin_prep_start,patient_undraped,patient_wheels_in,patient_wheels_out,patient_xfer_to_bed,patient_xfer_to_or_table,post_operative,pre_operative,sensitive_content_end,sensitive_content_start,sterile_pack_on_back_table,surgery,terminal_clean_end,terminal_clean_start,timeout_end,timeout_start,turn_over_clean,turn_over_idle,turn_over_open,back_table_cleared}	831027c5-942b-477a-b490-93ab731ebba6	\N	2	t	{}	f	1	f
\.


--
-- Data for Name: annotation_task_types_history; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.annotation_task_types_history (created_time, updated_time, archived_time, id, name, description, event_types, context_event_types, priority, detect_idle, allow_skipping_review, version, optimize_tasks) FROM stdin;
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	\N	f775f20b-0aff-48b2-9808-35622ad554ed	Light	Is it on or off? ¿Porque no los dos?	{lights_off,lights_on}	{}	5	t	f	1	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	\N	4d426c27-fd3c-4b88-8cb4-fdf2338b97d8	Mop	The mop type concerns all mop-related events	{mop,mop_in,mop_out}	{}	5	t	f	1	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	\N	831027c5-942b-477a-b490-93ab731ebba6	Surgery	The default task type containing all events	{back_table_open,case_cart_in,case_cart_out,case_cart_visible,cleaning_crew_in,cleaning_crew_out,closing_start,count_end,count_start,endo_pack_open,endoscopy_end,endoscopy_start,extubation,first_incision_start,intubation,lights_off,lights_on,mop,mop_in,mop_out,no_case_cart,no_mop,no_patient,or_table_ready,patient_briefing_end,patient_briefing_start,patient_draped,patient_imaging_end,patient_imaging_start,patient_on_hospital_bed,patient_on_or_table,patient_skin_prep_end,patient_skin_prep_start,patient_undraped,patient_wheels_in,patient_wheels_out,patient_xfer_to_bed,patient_xfer_to_or_table,post_operative,pre_operative,sensitive_content_end,sensitive_content_start,sterile_pack_on_back_table,surgery,terminal_clean_end,terminal_clean_start,timeout_end,timeout_start,turn_over_clean,turn_over_idle,turn_over_open,back_table_cleared}	{}	2	t	f	1	f
\.


--
-- Data for Name: annotation_tasks; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.annotation_tasks (id, org_id, site_id, room_id, start_time, end_time, status, annotator_user_id, reviewer_user_id, updated_time, created_time, type_id, cancelled_reason, version, review_reasons, type_version, updated_by_user_id) FROM stdin;
\.


--
-- Data for Name: annotation_tasks_history; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.annotation_tasks_history (id, org_id, site_id, room_id, start_time, end_time, status, annotator_user_id, reviewer_user_id, updated_time, type_id, cancelled_reason, created_time, version, review_reasons, type_version, updated_by_user_id) FROM stdin;
\.


--
-- Data for Name: block_release_file_rows; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.block_release_file_rows (block_release_file_id, row_number, external_id, org_id, site_id, room_id, room_name, timezone, start_time, end_time, release_length, released_time, release_reason, released_by, block_name, block_date, to_block_name, from_block_type, to_block_type, processing_status, rejected_reason, created_time, updated_time, release_type, days_prior) FROM stdin;
\.


--
-- Data for Name: block_release_processed_files; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.block_release_processed_files (created_time, updated_time, id, file_name, bucket_name, generation, size, md5_hash, media_link, org_id) FROM stdin;
\.


--
-- Data for Name: block_schedule_file_rows; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.block_schedule_file_rows (id, block_schedule_file_id, org_id, site_id, room_id, room_name, timezone, block_date, block_name, block_type, block_start_time, block_end_time, processing_status, rejected_reason, created_time, updated_time, row_number) FROM stdin;
\.


--
-- Data for Name: block_schedule_processed_files; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.block_schedule_processed_files (id, file_name, bucket_name, generation, size, md5_hash, media_link, created_time, updated_time, org_id) FROM stdin;
\.


--
-- Data for Name: block_sites; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.block_sites (block_id, site_id) FROM stdin;
\.


--
-- Data for Name: block_surgeons; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.block_surgeons (block_id, staff_id) FROM stdin;
\.


--
-- Data for Name: block_time_overrides; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.block_time_overrides (block_time_id, block_time_minutes, user_id, note, created_time, updated_time, version) FROM stdin;
\.


--
-- Data for Name: block_time_overrides_history; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.block_time_overrides_history (block_time_id, block_time_minutes, user_id, note, created_time, updated_time, version) FROM stdin;
\.


--
-- Data for Name: block_time_releases; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.block_time_releases (created_time, updated_time, id, block_time_id, release_time, reason, released_at, source, source_type, unreleased_time, unreleased_source, start_time, end_time, released_time, from_block_type, to_block_type, release_type, days_prior, room_id, to_block_id) FROM stdin;
\.


--
-- Data for Name: block_times; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.block_times (created_time, updated_time, id, block_id, block_time, room_id, start_time, end_time, released_from) FROM stdin;
\.


--
-- Data for Name: blocks; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.blocks (created_time, updated_time, archived_time, id, name, color, org_id, block_type) FROM stdin;
\.


--
-- Data for Name: board_config; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.board_config (id, name, page_size, page_duration, blur_video, org_id, site_id, created_time, updated_time, updated_by_user_id, board_view_type, enable_video, zoom_percent, show_closed_rooms) FROM stdin;
\.


--
-- Data for Name: board_room; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.board_room (board_config_id, room_id, created_time, updated_time) FROM stdin;
\.


--
-- Data for Name: cameras; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.cameras (id, room_id, site_id, org_id, name, rtsp_url, created_time, updated_time, labels, family) FROM stdin;
\.


--
-- Data for Name: case_classification_types; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_classification_types (created_time, updated_time, id, name, description, archived_time, org_id) FROM stdin;
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_EMERGENT	Emergent	\N	\N	health_first
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_URGENT	Urgent	\N	\N	health_first
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_ELECTIVE	Elective	\N	\N	health_first
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_UNKNOWN	Unknown	\N	\N	health_first
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_EMERGENT	Emergent	\N	\N	houston_methodist
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_URGENT	Urgent	\N	\N	houston_methodist
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_ELECTIVE	Elective	\N	\N	houston_methodist
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_UNKNOWN	Unknown	\N	\N	houston_methodist
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_EMERGENT	Emergent	\N	\N	north_bay
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_URGENT	Urgent	\N	\N	north_bay
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_ELECTIVE	Elective	\N	\N	north_bay
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_UNKNOWN	Unknown	\N	\N	north_bay
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_NEWBORN	Newborn (Birth in healthcare facility)	\N	\N	north_bay
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_EMERGENT	Emergent	\N	\N	nyu
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_URGENT	Urgent	\N	\N	nyu
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_ELECTIVE	Elective	\N	\N	nyu
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_UNKNOWN	Unknown	\N	\N	nyu
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_ADD_ON	Add-On	\N	\N	nyu
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_LEVEL1	Level 1	\N	\N	tampa_general
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_LEVEL2	Level 2	\N	\N	tampa_general
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_LEVEL3	Level 3	\N	\N	tampa_general
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_LEVEL4	Level 4	\N	\N	tampa_general
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_LEVEL5	Level 5	\N	\N	tampa_general
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_UNKNOWN	Unknown	\N	\N	tampa_general
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_EMERGENT	Emergent	\N	\N	apella_internal_0
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_URGENT	Urgent	\N	\N	apella_internal_0
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_ELECTIVE	Elective	\N	\N	apella_internal_0
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_UNKNOWN	Unknown	\N	\N	apella_internal_0
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_EMERGENT	Emergent	\N	\N	scrubs
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_URGENT	Urgent	\N	\N	scrubs
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_ELECTIVE	Elective	\N	\N	scrubs
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_UNKNOWN	Unknown	\N	\N	scrubs
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_EMERGENT	Emergent	\N	\N	greys_anatomy
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_URGENT	Urgent	\N	\N	greys_anatomy
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_ELECTIVE	Elective	\N	\N	greys_anatomy
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_UNKNOWN	Unknown	\N	\N	greys_anatomy
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_EMERGENT	Emergent	\N	\N	tampa_general
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_URGENT	Urgent	\N	\N	tampa_general
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_ELECTIVE	Elective	\N	\N	tampa_general
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE_CLASSIFICATION_EXPEDITED	Expedited	\N	\N	tampa_general
\.


--
-- Data for Name: case_derived_properties; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_derived_properties (case_id, is_in_flip_room, created_time, updated_time, preceding_case_id, is_first_case) FROM stdin;
\.


--
-- Data for Name: case_flag; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_flag (created_time, updated_time, archived_time, id, case_id, flag_type, org_id, site_id) FROM stdin;
\.


--
-- Data for Name: case_forecast; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_forecast (id, case_id, forecast_start_time, forecast_end_time, forecast_variant, room_id, created_time, updated_time, org_id, site_id, version, forecast_status, static_duration_minutes, pythia_duration_minutes, static_duration_end_time, transformer_end_time, pythia_end_time, turnover_duration_minutes, static_start_offset_minutes, is_auto_follow, is_overtime, pythia_prediction_tag, bayesian_duration_minutes, bayesian_end_time, case_start_source) FROM stdin;
\.


--
-- Data for Name: case_forecast_history; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_forecast_history (id, case_id, forecast_start_time, forecast_end_time, forecast_variant, room_id, created_time, updated_time, org_id, site_id, version, forecast_status, static_duration_minutes, pythia_duration_minutes, static_duration_end_time, transformer_end_time, pythia_end_time, turnover_duration_minutes, static_start_offset_minutes, is_auto_follow, is_overtime, pythia_prediction_tag, bayesian_duration_minutes, bayesian_end_time, case_start_source) FROM stdin;
\.


--
-- Data for Name: case_label_assoc; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_label_assoc (id, option_id, case_id, updated_by_user_id, created_time, updated_time, archived_time) FROM stdin;
\.


--
-- Data for Name: case_label_categories; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_label_categories (id, name, site_id, org_id) FROM stdin;
\.


--
-- Data for Name: case_label_field_options; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_label_field_options (id, field_id, color, abbreviation, value) FROM stdin;
\.


--
-- Data for Name: case_label_fields; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_label_fields (id, name, type, category_id, ordinal, site_id, org_id) FROM stdin;
\.


--
-- Data for Name: case_matching_status_reason; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_matching_status_reason (created_time, updated_time, id, explanation_for_change, case_id, case_matching_status, version) FROM stdin;
\.


--
-- Data for Name: case_matching_status_reason_history; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_matching_status_reason_history (created_time, updated_time, id, explanation_for_change, case_id, case_matching_status, version) FROM stdin;
\.


--
-- Data for Name: case_note_plan; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_note_plan (created_time, updated_time, id, case_id, note, org_id, site_id) FROM stdin;
\.


--
-- Data for Name: case_procedures; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_procedures (created_time, updated_time, case_id, procedure_id, archived_time, hierarchy, anesthesia_id) FROM stdin;
\.


--
-- Data for Name: case_raw; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_raw (created_time, updated_time, raw_id, raw, case_id, event_time, event_type, org_id, external_message_id, external_case_id, archived_at) FROM stdin;
\.


--
-- Data for Name: case_staff; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_staff (created_time, updated_time, case_id, staff_id, role, archived_time) FROM stdin;
\.


--
-- Data for Name: case_staff_plan; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_staff_plan (id, case_id, staff_id, role, created_time, updated_time, archived_time, org_id, site_id) FROM stdin;
\.


--
-- Data for Name: case_to_block_overrides; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_to_block_overrides (block_date, case_id, block_id, utilized_procedure_minutes, utilized_turnover_minutes, user_id, note, created_time, updated_time, version) FROM stdin;
\.


--
-- Data for Name: case_to_block_overrides_history; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_to_block_overrides_history (block_date, case_id, block_id, utilized_procedure_minutes, utilized_turnover_minutes, user_id, note, created_time, updated_time, version) FROM stdin;
\.


--
-- Data for Name: cases; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.cases (created_time, updated_time, case_id, external_case_id, org_id, site_id, room_id, scheduled_start_time, scheduled_end_time, status, case_classification_types_id, service_line_id, is_add_on, patient_class, version, cancellation_reason, latest_processed_message_id) FROM stdin;
\.


--
-- Data for Name: cases_history; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.cases_history (created_time, updated_time, case_id, external_case_id, room_id, case_classification_types_id, scheduled_start_time, scheduled_end_time, status, service_line_id, is_add_on, patient_class, org_id, site_id, version, cancellation_reason, latest_processed_message_id) FROM stdin;
\.


--
-- Data for Name: cluster; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.cluster (id, name, enable_audio) FROM stdin;
\.


--
-- Data for Name: cluster_mapping; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.cluster_mapping (cluster_id, site_id) FROM stdin;
\.


--
-- Data for Name: contact_information; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.contact_information (created_time, updated_time, id, type, contact_information_value, initial_notification_sent, first_name, last_name, is_apella_employee) FROM stdin;
\.


--
-- Data for Name: custom_phase_configs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.custom_phase_configs (id, start_event_type, end_event_type, name, description, created_time, updated_time, org_id) FROM stdin;
\.


--
-- Data for Name: event_dashboard_visibility; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.event_dashboard_visibility (id, event_type_id, org_id_filter, created_time, updated_time) FROM stdin;
9163c018-1465-4903-b83e-0cfc83868311	patient_wheels_in	\N	2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00
52a53f94-56b3-41dc-8c20-c5118748da92	patient_wheels_out	\N	2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00
bd017541-0daf-4287-ab36-0a9499357bfd	back_table_open	\N	2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00
e60ae851-d3a2-4b72-9f18-c1c7e4af6c9a	endo_pack_open	\N	2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00
e60ac11d-f7c9-4b74-9429-eb46b192628c	patient_draped	\N	2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00
35eb2c2a-a1f6-4d1f-b426-7afb935bf8d2	patient_undraped	\N	2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00
873f8cbb-f428-4c6a-ac36-f02f48ca70d6	anesthesia_draping	\N	2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00
dcad0665-076f-4f70-a9bb-1a89f28fe644	anesthesia_undraping	\N	2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00
8ed6274d-7196-4a82-b3b0-d2c3ff4c7c85	patient_xfer_to_or_table	\N	2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00
bbafe794-7c11-4f17-bc13-5f66e50097fc	patient_xfer_to_bed	\N	2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00
8f0e9d7b-37ff-42fa-bd19-4d38e9b30476	terminal_clean_start	\N	2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00
2f3c86cd-309a-482f-9b4e-d0bedd72c1b1	terminal_clean_end	\N	2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00
\.


--
-- Data for Name: event_label_options; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.event_label_options (created_time, updated_time, id, name) FROM stdin;
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	a12339f0-7e82-4daa-adc1-3c4e5fd6d58a	Needs Review
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	6faec252-37f3-43bf-a673-53a2f3bad594	Edge Case
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	a26ac2a7-a7f0-42b7-a953-3f08115dcf8d	Endo Case
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	72f43548-328a-4aa0-ad96-dee84316a390	Movie Matinee
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	7f6d630a-8878-49f7-9cad-302541fb6e03	Highlights
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	4ddda447-8546-4d40-8c05-eef4bc793ca7	Delay
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	5bd8aa12-4cc7-477c-b699-d3c623b275ef	Terminal Clean
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	6eb3b396-e929-4341-8bfd-bc9241a0617b	Turnover
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	9c2c5743-7e65-4539-ac1b-03e5c5a7fe61	Timeout
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	844030d5-77a5-495b-8b80-ce75e8daa0d2	Patient Carried
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	f82fba37-5b37-4bc8-ace7-27a12756ec71	Patient In Wheelchair
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	a6713258-e1fb-409b-906f-94e82836f8c2	Patient Walked In
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	d0b5dad5-b559-406e-bc80-381fc02a8475	Patient Transported - Case In Progress
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	7709c650-6f72-4a64-9351-10fdc1fc5801	Peri-operative Drape
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	b5c2b494-c8a3-46d7-85ab-b2801f1e6586	Post-operative Drape
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	8d783814-d40e-425b-9ba6-391459834ab2	Potential Dataset
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	67503988-df5a-4c69-ad47-ff0f0554fe8a	Infant Procedure
\.


--
-- Data for Name: event_types; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.event_types (created_time, updated_time, id, name, description, type, color, hidden) FROM stdin;
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	null_event		\N	uncategorized	#000000	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	patient_briefing_start	Patient briefing start	\N	patient_status	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	patient_briefing_end	Patient briefing end	\N	patient_status	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	patient_skin_prep_start	Patient skin prep start	\N	patient_status	#6fcca5	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	turn_over_clean	Turn Over Clean	\N	phase	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	turn_over_open	Turn Over Open	\N	phase	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	turn_over_idle	Turn Over Idle	\N	phase	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	pre_operative	Pre-Operative	\N	phase	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	post_operative	Post-Operative	\N	phase	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	surgery	Surgery	\N	phase	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	patient_skin_prep_end	Patient skin prep end	\N	patient_status	#6fcca5	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	case_cart_in	Case cart in	\N	casecart_status	#0faa6c	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	case_cart_out	Case cart out	\N	casecart_status	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	cleaning_crew_in	Cleaning crew in	\N	uncategorized	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	cleaning_crew_out	Cleaning crew out	\N	uncategorized	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	closing_start	Closing start	\N	uncategorized	#e97500	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	count_start	Count start	\N	uncategorized	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	count_end	Count end	\N	uncategorized	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	endoscopy_start	Endoscopy start	\N	uncategorized	#14a4a4	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	endoscopy_end	Endoscopy end	\N	uncategorized	#14a4a4	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	first_incision_start	First Incision	\N	uncategorized	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	intubation	Intubation	\N	patient_status	#f5c346	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	extubation	Extubation	\N	patient_status	#f5c346	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	mop_in	Mop in	\N	mop_status	#8c8ce3	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	mop_out	Mop out	\N	mop_status	#8c8ce3	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	or_table_ready	OR table ready	\N	uncategorized	#e868a8	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	patient_imaging_start	Patient imaging start	\N	patient_status	#93beff	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	patient_imaging_end	Patient imaging end	\N	patient_status	#93beff	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	timeout_start	Timeout start	\N	uncategorized	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	timeout_end	Timeout end	\N	uncategorized	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	mop	Mop	\N	uncategorized	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	no_patient	No patient	\N	uncategorized	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	case_cart_visible	Case cart visible	\N	uncategorized	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	no_mop	No mop	\N	uncategorized	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	sterile_pack_on_back_table	Sterile pack on back table	\N	backtable_status	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	lights_on	Lights on	\N	brightness	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	lights_off	Lights off	\N	brightness	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	no_case_cart	No case cart	\N	casecart_status	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	patient_on_hospital_bed	Patient on hospital bed	\N	patient_status	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	patient_on_or_table	Patient on OR table	\N	patient_status	#999999	t
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	patient_wheels_in	Patient wheels in	\N	patient_status	#f36f66	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	patient_wheels_out	Patient wheels out	\N	patient_status	#bc0c00	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	patient_xfer_to_bed	Bed occupied	\N	patient_status	#5e49cc	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	patient_xfer_to_or_table	OR table occupied	\N	patient_status	#ad9dff	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	sensitive_content_start	Sensitive content start	\N	uncategorized	#f2ab66	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	sensitive_content_end	Sensitive content end	\N	uncategorized	#ba5c00	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	patient_draped	Patient draped (Procedural)	\N	patient_status	#72c8c8	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	patient_undraped	Patient undraped (Procedural)	\N	patient_status	#108383	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	anesthesia_draping	Anesthesia draping	\N	patient_status	#f89dca	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	anesthesia_undraping	Anesthesia undraping	\N	patient_status	#c34a86	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	ceiling_cleaning_start	Ceiling cleaning start	\N	terminal_cleaning	#9fddc4	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	wall_cleaning_start	Wall cleaning start	\N	terminal_cleaning	#3fbb89	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	floor_cleaning_start	Floor cleaning start	\N	terminal_cleaning	#0FAA6B	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	furniture_cleaning_start	Furniture cleaning start	\N	terminal_cleaning	#096640	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	under_OR_equipment_cleaning_start	Under OR equipment cleaning start	\N	terminal_cleaning	#06442b	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	under_OR_table_pads_cleaning_start	Under OR table pads cleaning start	\N	terminal_cleaning	#a1dbdb	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	nursing_station_cleaning_start	Nursing station cleaning start	\N	terminal_cleaning	#43b6b6	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	door_to_the_core_cleaning_start	Door to the core cleaning start	\N	terminal_cleaning	#14A4A4	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	equipment_cleaning_start	Equipment cleaning start	\N	terminal_cleaning	#14A4A4	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	or_lights_and_booms_cleaning_start	OR lights and booms cleaning start	\N	terminal_cleaning	#0c6262	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	waste_can_cleaning_start	Waste can cleaning start	\N	terminal_cleaning	#a1dbdb	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	instrument_tray_opening	Instrument tray opening	\N	robotic_surgery	#c49b38	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	da_vinci_tray_opening	Da Vinci tray opening	\N	robotic_surgery	#F5C246	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	da_vinci_instrument_opening	Da Vinci instrument opening	\N	robotic_surgery	#f7ce6b	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	da_vinci_instrument_use	Da Vinci instrument use	\N	robotic_surgery	#f9da90	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	terminal_clean_start	Terminal cleaning start	\N	terminal_cleaning	#6fcca6	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	terminal_clean_end	Terminal cleaning end	\N	terminal_cleaning	#0c8856	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	robot_drape_start	Robot draping start	\N	robotic_surgery	#FBE7B5	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	robot_drape_end	Robot draping end	\N	robotic_surgery	#93742A	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	endo_pack_open	Endo pack open	\N	back_table_status	#66a9ff	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	back_table_open	Back table open	\N	back_table_status	#006FFF	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	back_table_cleared	Back table cleared	\N	back_table_status	#004399	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	pt_draped_last_big_drape	PT Draped (Last Big Drape)	\N	surgery	#006fff	f
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	pt_draped_75_percent	PT Draped (75%)	\N	surgery	#eb0f00	f
\.


--
-- Data for Name: events; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.events (id, start_time, process_timestamp, org_id, site_id, room_id, case_id, source, model_version, source_type, created_time, updated_time, labels, notes, camera_id, confidence, event_type_id, deleted_at, version, event_matching_status, etag) FROM stdin;
\.


--
-- Data for Name: events_history; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.events_history (created_time, updated_time, id, start_time, process_timestamp, deleted_at, event_type_id, site_id, room_id, camera_id, case_id, source, model_version, source_type, confidence, labels, notes, org_id, version, event_matching_status, etag) FROM stdin;
\.


--
-- Data for Name: highlight_assets; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.highlight_assets (highlight_id, asset_id) FROM stdin;
\.


--
-- Data for Name: highlight_feedback; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.highlight_feedback (created_time, updated_time, id, comment, rating, highlight_id, user_id, org_id) FROM stdin;
\.


--
-- Data for Name: highlight_users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.highlight_users (highlight_id, user_id) FROM stdin;
\.


--
-- Data for Name: highlights; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.highlights (created_time, updated_time, id, description, org_id, end_time, start_time, room_id, site_id, category, archived_time) FROM stdin;
\.


--
-- Data for Name: identifier_mapping; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.identifier_mapping (created_time, updated_time, external_id_type, external_id, internal_id) FROM stdin;
\.


--
-- Data for Name: measurement_periods; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.measurement_periods (created_time, updated_time, id, name, measurement_period, site_id, annotation_task_type_id, room_ids, days_of_week, start_date, end_date) FROM stdin;
\.


--
-- Data for Name: observation_type_names; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.observation_type_names (type_id, name, color, created_time, updated_time, org_id, is_custom_phase_end_point) FROM stdin;
\.


--
-- Data for Name: observation_types; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.observation_types (created_time, updated_time, id, name, color, description) FROM stdin;
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_ANESTHESIA_START	Observed Anesthesia Start	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_ANESTHESIA_FINISH	Observed Anesthesia Finish	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_CASE_FINISH	Observed Case Finish	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_CLEANUP_START	Observed Cleanup Start	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_CLEANUP_COMPLETE	Observed Cleanup Complete	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_PREP_START	Observed Prep Start	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_PREP_COMPLETE	Observed Prep Complete	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_IN_FACILITY	Observed In Facility	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_IN_PRE_PROCEDURE	Observed In Pre-Procedure	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_PRE_PROCEDURE_COMPLETE	Observed Pre-Procedure Complete	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_OUT_OF_PRE_PROCEDURE	Observed Out of Pre-Procedure	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_ANESTHESIA_AVAILABLE	Observed Anesthesia Available	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_PROCEDURAL_CARE_COMPLETE	Observed Procedural Care Complete	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_IN_PACU	Observed In PACU	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_PATIENT_MOVED_TO_ANOTHER_OR_ROOM	Observed Patient moved to another OR room	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_PHYSICIAN_AVAILABLE	Observed Physician Available	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_SEDATION_END	Observed Sedation End	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_OUT_OF_ROOM	Observed Out of Room	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_IN_ROOM	Observed In Room	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_AT_CECUM	At Cecum	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_CATHLAB_TO_OR	Cathlab to OR	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_DECISION_TIME	Decision Time	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_DISCHARGE_CRITERIA_MET	Discharge Criteria Met	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_EPIDURAL_TO_C_SECTION	Epidural to C-section	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_HYSTEROTOMY	Hysterotomy	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_IN_EXTENDED_RECOVERY	In Extended Recovery 	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_IN_HOLDING_AREA	In Holding Area	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_IN_PHASE_II	In Phase II	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_IN_PROCEDURAL_RECOVERY	In Procedural Recovery 	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_INDUCTION	Induction	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_OUT_OF_PACU	Out of PACU	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_OPNOTE_VERIFIED	OpNote Verified	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_OUT_OF_EXTENDED_RECOVERY	Out of Extended Recovery	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_OUT_OF_HOLDING_AREA	Out of Holding Area	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_OUT_OF_PACU_2ND_TIME	Out of PACU (2nd Time)	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_OUT_OF_PACU_3RD_TIME	Out of PACU (3rd Time)	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_OUT_OF_PHASE_II	Out of Phase II	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_OUT_OF_PHASE_II_2ND_TIME	Out of Phase II (2nd Time)	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_OUT_OF_PROCEDURAL_RECOVERY	Out of Procedural Recovery	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_PATIENT_SENT_FOR	Patient Sent For	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_PATIENT_TRANSFER_TO_HOSPITAL_ROOM	Patient transfer to a hospital room	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_PHASE_II_CARE_COMPLETE	Phase II Care Complete	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_PROCEDURE_FINISH	Procedure Finish	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_RETURN_TO_OR	Return to OR	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_RETURN_TO_PACU_3RD_TIME	Return to PACU (3rd Time)	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_RETURN_TO_PACU	Return to PACU	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_RETURN_TO_PHASE_II	Return to Phase II	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_SEDATION_START	Sedation Start	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_SETUP_COMPLETE	Setup Complete	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_SETUP_START	Setup Start	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_TO_PHASE_II	To Phase II	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_TIMEOUT_ANESTHESIA	TIMEOUT_ANESTHESIA	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_TIMEOUT_DEBRIEF	TIMEOUT_DEBRIEF	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_TIMEOUT_FIRE_SAFETY	TIMEOUT_FIRE_SAFETY	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_TIMEOUT_PREINCISION	TIMEOUT_PREINCISION	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_TIMEOUT_PREPROCEDURE	TIMEOUT_PREPROCEDURE	#C0997B	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_ANESTHESIA_READY	Observed Anesthesia Ready	#FFC0CB	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_CASE_START	Observed Case Start	#008000	\N
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	OBSERVED_CASE_CLOSING	Observed Case Closing	#FFA500	\N
\.


--
-- Data for Name: observations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.observations (created_time, updated_time, id, case_id, type_id, observation_time, recorded_time, org_id) FROM stdin;
\.


--
-- Data for Name: organizations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.organizations (id, name, auth0_org_id, created_time, updated_time) FROM stdin;
\.


--
-- Data for Name: phase_relationships; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.phase_relationships (created_time, updated_time, parent_phase_id, child_phase_id, org_id) FROM stdin;
\.


--
-- Data for Name: phase_types; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.phase_types (created_time, updated_time, id, title, slug, description) FROM stdin;
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CASE	Case	case	A Case phase is measured as the period between when a patient is wheeled into the OR and the time when a patient is wheeled out of the OR.
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	TERMINAL_CLEAN	Terminal Clean	terminal-clean	A Terminal Clean phase is measured as the period between the cleaning crew in to the cleaning crew out, including a mopping of the walls.
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	DRAPE_DRAPE_TURNOVER	Drape to Drape Turnover	drape-drape-turnover	A Drape to Drape Turnover phase is measured as the period between when a patient is undraped and the next patient is draped.
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	TURNOVER	Turnover	turnover	A turnover phase is measured as the period between when a patient wheels out and the next patient wheels in.
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	TURNOVER_OPEN	Opening	turnover-open	An opening phase is measured as the period between when the back table is opened to the patient wheels in.
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	TURNOVER_CLEAN	Cleaning	turnover-clean	A cleaning phase is measured as the period between when a patient wheels out and the back table is opened.
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	ANESTHESIA_PROCEDURE	Anesthesia Procedure	anesthesia-procedure	An Anesthesia Procedure Phase is measured as the period between when a patient is draped for anesthesia and when they are undraped for anesthesia
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	PRE_OPERATIVE	Prep	pre-operative	A Prep (or Pre Operative) phase is measured as the period between when a patient wheels into the OR and the patient is draped.
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	INTRA_OPERATIVE	Surgery	intra-operative	An Surgery (or Intra Operative) phase is measured as the period between when a patient is draped and undraped.
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	POST_OPERATIVE	Wrap-up	post-operative	A Wrap-up (or Post Operative) phase is measured as the period between when a patient is undraped and the patient is wheeled out.
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	ANESTHESIA_PREP	Anesthesia Prep	anesthesia-prep	The phase where the patient is prepped for anesthesia
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	TURNOVER_CLEAN_V2	Turnover Clean V2	turnover-clean-v2	A cleaning phase is measured as the period between when a patient wheels out and the mop leaves the room.
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	TURNOVER_CLEANED	Turnover Cleaned	turnover-cleaned	The cleaned phase is measured as the period between when the mop leaves the room, and\n             the back table is opened for the next case.
\.


--
-- Data for Name: phases; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.phases (created_time, updated_time, id, site_id, room_id, type_id, start_event_id, end_event_id, case_id, source_type, org_id, status, invalidation_reason, event_matching_status, etag, version) FROM stdin;
\.


--
-- Data for Name: phases_history; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.phases_history (created_time, updated_time, event_matching_status, etag, id, room_id, type_id, start_event_id, end_event_id, case_id, source_type, status, invalidation_reason, org_id, site_id, version) FROM stdin;
\.


--
-- Data for Name: procedures; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.procedures (created_time, updated_time, id, name, org_id) FROM stdin;
\.


--
-- Data for Name: room_closures; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.room_closures (id, room_id, start_time, end_time, created_time, updated_time, reason) FROM stdin;
\.


--
-- Data for Name: room_default_camera; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.room_default_camera (created_time, updated_time, room_id, camera_id, description) FROM stdin;
\.


--
-- Data for Name: room_first_case_configs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.room_first_case_configs (id, room_id, created_time, updated_time, sunday_start_time, sunday_end_time, monday_start_time, monday_end_time, tuesday_start_time, tuesday_end_time, wednesday_start_time, wednesday_end_time, thursday_start_time, thursday_end_time, friday_start_time, friday_end_time, saturday_start_time, saturday_end_time) FROM stdin;
\.


--
-- Data for Name: room_prime_time_configs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.room_prime_time_configs (id, room_id, sunday_start_time, sunday_end_time, monday_start_time, monday_end_time, tuesday_start_time, tuesday_end_time, wednesday_start_time, wednesday_end_time, thursday_start_time, thursday_end_time, friday_start_time, friday_end_time, saturday_start_time, saturday_end_time, created_time, updated_time) FROM stdin;
\.


--
-- Data for Name: room_taggings; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.room_taggings (room_id, tag_id, created_time, updated_time) FROM stdin;
\.


--
-- Data for Name: room_tags; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.room_tags (id, name, created_time, updated_time, org_id, color) FROM stdin;
\.


--
-- Data for Name: rooms; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.rooms (id, name, site_id, org_id, created_time, updated_time, privacy_enabled_at, privacy_updated_by_user_id, sort_key, labels, is_forecasting_enabled) FROM stdin;
\.


--
-- Data for Name: service_lines; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.service_lines (created_time, updated_time, id, external_service_line_id, name, org_id) FROM stdin;
\.


--
-- Data for Name: site_capacity_constraints; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.site_capacity_constraints (id, day_of_week, count, start_time, site_id, created_time, updated_time) FROM stdin;
\.


--
-- Data for Name: site_closures; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.site_closures (id, site_id, closure_date, reason, created_time, updated_time) FROM stdin;
\.


--
-- Data for Name: site_first_case_configs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.site_first_case_configs (id, site_id, created_time, updated_time, sunday_start_time, sunday_end_time, monday_start_time, monday_end_time, tuesday_start_time, tuesday_end_time, wednesday_start_time, wednesday_end_time, thursday_start_time, thursday_end_time, friday_start_time, friday_end_time, saturday_start_time, saturday_end_time) FROM stdin;
\.


--
-- Data for Name: site_launch_information; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.site_launch_information (site_id, created_time, updated_time, actual_launch_date, anticipated_launch_date, site_name, notion_id) FROM stdin;
\.


--
-- Data for Name: site_prime_time_configs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.site_prime_time_configs (id, site_id, sunday_start_time, sunday_end_time, monday_start_time, monday_end_time, tuesday_start_time, tuesday_end_time, wednesday_start_time, wednesday_end_time, thursday_start_time, thursday_end_time, friday_start_time, friday_end_time, saturday_start_time, saturday_end_time, created_time, updated_time) FROM stdin;
\.


--
-- Data for Name: sites; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.sites (id, name, org_id, timezone, created_time, updated_time) FROM stdin;
\.


--
-- Data for Name: staff; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.staff (created_time, updated_time, id, first_name, last_name, org_id, external_staff_id, archived_time) FROM stdin;
\.


--
-- Data for Name: staff_codes; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.staff_codes (created_time, updated_time, id, staff_id, coding_system, code, org_id) FROM stdin;
\.


--
-- Data for Name: staff_event_notification; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.staff_event_notification (created_time, updated_time, id, message_id, staff_event_contact_information_id, event_id, case_id, event_time, sent_status, attempts, duplicated_id, is_excess, version, sent_time) FROM stdin;
\.


--
-- Data for Name: staff_event_notification_contact_information; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.staff_event_notification_contact_information (created_time, updated_time, archived_time, staff_id, contact_information_id, event_type_id, id) FROM stdin;
\.


--
-- Data for Name: staff_event_notification_history; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.staff_event_notification_history (created_time, updated_time, id, message_id, staff_event_contact_information_id, event_id, case_id, event_time, sent_status, attempts, duplicated_id, is_excess, version, sent_time) FROM stdin;
\.


--
-- Data for Name: staff_event_notification_old_01; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.staff_event_notification_old_01 (created_time, updated_time, id, message_id, staff_event_contact_information_id, event_id, case_id, event_time) FROM stdin;
\.


--
-- Data for Name: staff_role; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.staff_role (created_time, updated_time, id, name) FROM stdin;
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	ANESTHESIOLOGIST	Anesthesiologist
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CIRCULATOR	Circulator
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	CRNA	CRNA
2025-05-29 22:20:03.241467+00	2025-05-29 22:20:03.241467+00	TECH	Surgical Tech
\.


--
-- Data for Name: staffing_needs_ratio; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.staffing_needs_ratio (created_time, updated_time, archived_time, id, set_by_user_id, archived_by_user_id, ratio, staff_role_id, org_id, site_id) FROM stdin;
\.


--
-- Data for Name: terminal_clean_scores; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.terminal_clean_scores (id, room_id, date, comments, score, created_time, updated_time) FROM stdin;
\.


--
-- Data for Name: turnover_goals; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.turnover_goals (id, goal_minutes, max_minutes, created_time, updated_time, org_id, site_id) FROM stdin;
\.


--
-- Data for Name: turnover_label_assoc; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.turnover_label_assoc (id, preceding_case_id, following_case_id, turnover_label_id, updated_by_user_id, created_time, updated_time, org_id, archived_time) FROM stdin;
\.


--
-- Data for Name: turnover_labels; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.turnover_labels (id, name, type) FROM stdin;
76080595-31c8-413e-88f5-ca2947375363	Patient	delay
33894744-8c1b-48bf-a58a-de83defda978	Surgeon	delay
56c7172c-2e61-436f-9a75-3b37f0f56320	Anesthesia	delay
fee63856-9465-4320-be1b-59bf1a521918	Facility	delay
e2091673-1e5f-499c-b040-fcf3c081ac7f	Other	delay
d48d7f6e-69d4-45ca-bcff-8d9439243d41	Exclude this turnover	exclusion
\.


--
-- Data for Name: turnover_notes; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.turnover_notes (id, preceding_case_id, following_case_id, note, updated_by_user_id, created_time, updated_time, org_id) FROM stdin;
\.


--
-- Data for Name: user_filter_views; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.user_filter_views (created_time, updated_time, id, name, url, user_id, org_id) FROM stdin;
\.


--
-- Data for Name: user_room_default_camera; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.user_room_default_camera (room_id, camera_id, user_id, created_time, updated_time) FROM stdin;
\.


--
-- Name: alembic_version alembic_version_pkc; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.alembic_version
    ADD CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num);


--
-- Name: anesthesias anesthesias_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.anesthesias
    ADD CONSTRAINT anesthesias_pkey PRIMARY KEY (id);


--
-- Name: annotation_task_schedules annotation_task_schedules_annotation_task_type_id_start_tim_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_task_schedules
    ADD CONSTRAINT annotation_task_schedules_annotation_task_type_id_start_tim_key UNIQUE (annotation_task_type_id, start_time, "interval");


--
-- Name: annotation_task_schedules annotation_task_schedules_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_task_schedules
    ADD CONSTRAINT annotation_task_schedules_pkey PRIMARY KEY (id);


--
-- Name: annotation_task_schedules_rooms annotation_task_schedules_rooms_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_task_schedules_rooms
    ADD CONSTRAINT annotation_task_schedules_rooms_pkey PRIMARY KEY (schedule_id, room_id);


--
-- Name: annotation_task_schedules_sites annotation_task_schedules_sites_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_task_schedules_sites
    ADD CONSTRAINT annotation_task_schedules_sites_pkey PRIMARY KEY (schedule_id, site_id);


--
-- Name: annotation_task_type_provisional_annotators annotation_task_type_provisional_annotators_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_task_type_provisional_annotators
    ADD CONSTRAINT annotation_task_type_provisional_annotators_pkey PRIMARY KEY (annotation_task_type_id, user_id);


--
-- Name: annotation_task_type_reviewers annotation_task_type_reviewers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_task_type_reviewers
    ADD CONSTRAINT annotation_task_type_reviewers_pkey PRIMARY KEY (annotation_task_type_id, user_id);


--
-- Name: annotation_task_type_annotators annotation_task_type_users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_task_type_annotators
    ADD CONSTRAINT annotation_task_type_users_pkey PRIMARY KEY (annotation_task_type_id, user_id);


--
-- Name: annotation_task_types_history annotation_task_types_history_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_task_types_history
    ADD CONSTRAINT annotation_task_types_history_pkey PRIMARY KEY (id, version);


--
-- Name: annotation_task_types annotation_task_types_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_task_types
    ADD CONSTRAINT annotation_task_types_pkey PRIMARY KEY (id);


--
-- Name: annotation_tasks_history annotation_tasks_history_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_tasks_history
    ADD CONSTRAINT annotation_tasks_history_pkey PRIMARY KEY (id, version);


--
-- Name: annotation_tasks annotation_tasks_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_tasks
    ADD CONSTRAINT annotation_tasks_pkey PRIMARY KEY (id);


--
-- Name: block_release_file_rows block_release_file_rows_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_release_file_rows
    ADD CONSTRAINT block_release_file_rows_pkey PRIMARY KEY (block_release_file_id, row_number);


--
-- Name: block_release_processed_files block_release_processed_files_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_release_processed_files
    ADD CONSTRAINT block_release_processed_files_pkey PRIMARY KEY (id);


--
-- Name: block_schedule_file_rows block_schedule_file_rows_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_schedule_file_rows
    ADD CONSTRAINT block_schedule_file_rows_pkey PRIMARY KEY (id);


--
-- Name: block_schedule_processed_files block_schedule_processed_files_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_schedule_processed_files
    ADD CONSTRAINT block_schedule_processed_files_pkey PRIMARY KEY (id);


--
-- Name: block_sites block_sites_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_sites
    ADD CONSTRAINT block_sites_pkey PRIMARY KEY (block_id, site_id);


--
-- Name: block_surgeons block_surgeons_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_surgeons
    ADD CONSTRAINT block_surgeons_pkey PRIMARY KEY (block_id, staff_id);


--
-- Name: block_time_overrides_history block_time_overrides_history_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_time_overrides_history
    ADD CONSTRAINT block_time_overrides_history_pkey PRIMARY KEY (block_time_id, version);


--
-- Name: block_time_overrides block_time_overrides_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_time_overrides
    ADD CONSTRAINT block_time_overrides_pkey PRIMARY KEY (block_time_id);


--
-- Name: block_time_releases block_time_releases_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_time_releases
    ADD CONSTRAINT block_time_releases_pkey PRIMARY KEY (id);


--
-- Name: block_times block_times_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_times
    ADD CONSTRAINT block_times_pkey PRIMARY KEY (id);


--
-- Name: blocks blocks_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.blocks
    ADD CONSTRAINT blocks_pkey PRIMARY KEY (id);


--
-- Name: board_config board_config_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.board_config
    ADD CONSTRAINT board_config_pkey PRIMARY KEY (id);


--
-- Name: board_room board_room_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.board_room
    ADD CONSTRAINT board_room_pkey PRIMARY KEY (board_config_id, room_id);


--
-- Name: cameras cameras_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cameras
    ADD CONSTRAINT cameras_pkey PRIMARY KEY (id);


--
-- Name: case_classification_types case_classification_types_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_classification_types
    ADD CONSTRAINT case_classification_types_pkey PRIMARY KEY (org_id, id);


--
-- Name: case_derived_properties case_derived_properties_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_derived_properties
    ADD CONSTRAINT case_derived_properties_pkey PRIMARY KEY (case_id);


--
-- Name: case_flag case_flag_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_flag
    ADD CONSTRAINT case_flag_pkey PRIMARY KEY (id);


--
-- Name: case_forecast_history case_forecast_history_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_forecast_history
    ADD CONSTRAINT case_forecast_history_pkey PRIMARY KEY (id, version);


--
-- Name: case_forecast case_forecast_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_forecast
    ADD CONSTRAINT case_forecast_pkey PRIMARY KEY (id);


--
-- Name: case_label_assoc case_label_assoc_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_label_assoc
    ADD CONSTRAINT case_label_assoc_pkey PRIMARY KEY (id);


--
-- Name: case_label_categories case_label_categories_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_label_categories
    ADD CONSTRAINT case_label_categories_pkey PRIMARY KEY (id);


--
-- Name: case_label_field_options case_label_field_options_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_label_field_options
    ADD CONSTRAINT case_label_field_options_pkey PRIMARY KEY (id);


--
-- Name: case_label_fields case_label_fields_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_label_fields
    ADD CONSTRAINT case_label_fields_pkey PRIMARY KEY (id);


--
-- Name: case_matching_status_reason_history case_matching_status_reason_history_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_matching_status_reason_history
    ADD CONSTRAINT case_matching_status_reason_history_pkey PRIMARY KEY (id, version);


--
-- Name: case_matching_status_reason case_matching_status_reason_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_matching_status_reason
    ADD CONSTRAINT case_matching_status_reason_pkey PRIMARY KEY (id);


--
-- Name: case_note_plan case_note_plan_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_note_plan
    ADD CONSTRAINT case_note_plan_pkey PRIMARY KEY (id);


--
-- Name: case_procedures case_procedures_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_procedures
    ADD CONSTRAINT case_procedures_pkey PRIMARY KEY (case_id, procedure_id);


--
-- Name: case_raw case_raw_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_raw
    ADD CONSTRAINT case_raw_pkey PRIMARY KEY (raw_id);


--
-- Name: case_staff case_staff_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_staff
    ADD CONSTRAINT case_staff_pkey PRIMARY KEY (case_id, staff_id);


--
-- Name: case_staff_plan case_staff_plan_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_staff_plan
    ADD CONSTRAINT case_staff_plan_pkey PRIMARY KEY (id);


--
-- Name: case_to_block_overrides_history case_to_block_overrides_history_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_to_block_overrides_history
    ADD CONSTRAINT case_to_block_overrides_history_pkey PRIMARY KEY (case_id, version);


--
-- Name: case_to_block_overrides case_to_block_overrides_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_to_block_overrides
    ADD CONSTRAINT case_to_block_overrides_pkey PRIMARY KEY (case_id);


--
-- Name: cases_history cases_history_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cases_history
    ADD CONSTRAINT cases_history_pkey PRIMARY KEY (case_id, version);


--
-- Name: cases cases_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cases
    ADD CONSTRAINT cases_pkey PRIMARY KEY (case_id);


--
-- Name: cluster_mapping cluster_mapping_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cluster_mapping
    ADD CONSTRAINT cluster_mapping_pkey PRIMARY KEY (cluster_id, site_id);


--
-- Name: cluster cluster_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cluster
    ADD CONSTRAINT cluster_pkey PRIMARY KEY (id);


--
-- Name: custom_phase_configs custom_phase_configs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.custom_phase_configs
    ADD CONSTRAINT custom_phase_configs_pkey PRIMARY KEY (id);


--
-- Name: event_dashboard_visibility event_dashboard_visibility_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.event_dashboard_visibility
    ADD CONSTRAINT event_dashboard_visibility_pkey PRIMARY KEY (id);


--
-- Name: event_label_options event_label_options_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.event_label_options
    ADD CONSTRAINT event_label_options_pkey PRIMARY KEY (id);


--
-- Name: event_types event_types_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.event_types
    ADD CONSTRAINT event_types_pkey PRIMARY KEY (id);


--
-- Name: events_history events_history_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.events_history
    ADD CONSTRAINT events_history_pkey PRIMARY KEY (id, version);


--
-- Name: events events_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT events_pkey PRIMARY KEY (id);


--
-- Name: site_first_case_configs first_case_configs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.site_first_case_configs
    ADD CONSTRAINT first_case_configs_pkey PRIMARY KEY (id, site_id);


--
-- Name: highlight_assets highlight_assets_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.highlight_assets
    ADD CONSTRAINT highlight_assets_pkey PRIMARY KEY (highlight_id, asset_id);


--
-- Name: highlight_feedback highlight_feedback_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.highlight_feedback
    ADD CONSTRAINT highlight_feedback_pkey PRIMARY KEY (id);


--
-- Name: highlight_users highlight_users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.highlight_users
    ADD CONSTRAINT highlight_users_pkey PRIMARY KEY (highlight_id, user_id);


--
-- Name: highlights highlights_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.highlights
    ADD CONSTRAINT highlights_pkey PRIMARY KEY (id);


--
-- Name: identifier_mapping identifier_mapping_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.identifier_mapping
    ADD CONSTRAINT identifier_mapping_pkey PRIMARY KEY (external_id_type, external_id);


--
-- Name: measurement_periods measurement_periods_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.measurement_periods
    ADD CONSTRAINT measurement_periods_pkey PRIMARY KEY (id);


--
-- Name: observation_type_names observation_type_names_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.observation_type_names
    ADD CONSTRAINT observation_type_names_pkey PRIMARY KEY (org_id, type_id);


--
-- Name: observation_types observation_types_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.observation_types
    ADD CONSTRAINT observation_types_pkey PRIMARY KEY (id);


--
-- Name: observations observations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.observations
    ADD CONSTRAINT observations_pkey PRIMARY KEY (id);


--
-- Name: organizations organizations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT organizations_pkey PRIMARY KEY (id);


--
-- Name: phase_relationships phase_relationships_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.phase_relationships
    ADD CONSTRAINT phase_relationships_pkey PRIMARY KEY (parent_phase_id, child_phase_id);


--
-- Name: phase_types phase_types_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.phase_types
    ADD CONSTRAINT phase_types_pkey PRIMARY KEY (id);


--
-- Name: phases_history phases_history_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.phases_history
    ADD CONSTRAINT phases_history_pkey PRIMARY KEY (id, version);


--
-- Name: phases phases_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.phases
    ADD CONSTRAINT phases_pkey PRIMARY KEY (id);


--
-- Name: case_flag pk_case_id_flag_type; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_flag
    ADD CONSTRAINT pk_case_id_flag_type UNIQUE (case_id, flag_type);


--
-- Name: staff_event_notification_contact_information pk_contactInfoId_staffId_eventTypeId; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff_event_notification_contact_information
    ADD CONSTRAINT "pk_contactInfoId_staffId_eventTypeId" PRIMARY KEY (contact_information_id, staff_id, event_type_id);


--
-- Name: contact_information pk_contactInformationValue_type; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.contact_information
    ADD CONSTRAINT "pk_contactInformationValue_type" PRIMARY KEY (contact_information_value, type);


--
-- Name: site_launch_information pk_site_launch; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.site_launch_information
    ADD CONSTRAINT pk_site_launch PRIMARY KEY (site_name);


--
-- Name: staff_event_notification pk_staffEventNotification_id_sen; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff_event_notification
    ADD CONSTRAINT "pk_staffEventNotification_id_sen" PRIMARY KEY (id);


--
-- Name: staff_event_notification_old_01 pk_staffEventNotification_msgId_senciID_old; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff_event_notification_old_01
    ADD CONSTRAINT "pk_staffEventNotification_msgId_senciID_old" PRIMARY KEY (message_id, staff_event_contact_information_id);


--
-- Name: procedures procedures_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.procedures
    ADD CONSTRAINT procedures_pkey PRIMARY KEY (id);


--
-- Name: room_closures room_closures_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.room_closures
    ADD CONSTRAINT room_closures_pkey PRIMARY KEY (id, room_id);


--
-- Name: room_default_camera room_default_camera_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.room_default_camera
    ADD CONSTRAINT room_default_camera_pkey PRIMARY KEY (room_id, camera_id);


--
-- Name: room_first_case_configs room_first_case_configs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.room_first_case_configs
    ADD CONSTRAINT room_first_case_configs_pkey PRIMARY KEY (id, room_id);


--
-- Name: room_prime_time_configs room_prime_time_configs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.room_prime_time_configs
    ADD CONSTRAINT room_prime_time_configs_pkey PRIMARY KEY (id, room_id);


--
-- Name: room_taggings room_taggings_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.room_taggings
    ADD CONSTRAINT room_taggings_pkey PRIMARY KEY (room_id, tag_id);


--
-- Name: room_tags room_tags_name_org_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.room_tags
    ADD CONSTRAINT room_tags_name_org_id_key UNIQUE (name, org_id);


--
-- Name: room_tags room_tags_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.room_tags
    ADD CONSTRAINT room_tags_pkey PRIMARY KEY (id);


--
-- Name: rooms rooms_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.rooms
    ADD CONSTRAINT rooms_pkey PRIMARY KEY (id);


--
-- Name: service_lines service_lines_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.service_lines
    ADD CONSTRAINT service_lines_pkey PRIMARY KEY (id);


--
-- Name: site_capacity_constraints site_capacity_constraints_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.site_capacity_constraints
    ADD CONSTRAINT site_capacity_constraints_pkey PRIMARY KEY (id, site_id);


--
-- Name: site_closures site_closures_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.site_closures
    ADD CONSTRAINT site_closures_pkey PRIMARY KEY (id, site_id);


--
-- Name: site_prime_time_configs site_prime_time_configs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.site_prime_time_configs
    ADD CONSTRAINT site_prime_time_configs_pkey PRIMARY KEY (id, site_id);


--
-- Name: sites sites_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.sites
    ADD CONSTRAINT sites_pkey PRIMARY KEY (id);


--
-- Name: staff_codes staff_codes_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff_codes
    ADD CONSTRAINT staff_codes_pkey PRIMARY KEY (id);


--
-- Name: staff_event_notification_history staff_event_notification_history_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff_event_notification_history
    ADD CONSTRAINT staff_event_notification_history_pkey PRIMARY KEY (id, version);


--
-- Name: staff staff_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff
    ADD CONSTRAINT staff_pkey PRIMARY KEY (id);


--
-- Name: staff_role staff_role_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff_role
    ADD CONSTRAINT staff_role_pkey PRIMARY KEY (id);


--
-- Name: staffing_needs_ratio staffing_needs_ratio_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staffing_needs_ratio
    ADD CONSTRAINT staffing_needs_ratio_pkey PRIMARY KEY (id);


--
-- Name: terminal_clean_scores terminal_clean_scores_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.terminal_clean_scores
    ADD CONSTRAINT terminal_clean_scores_pkey PRIMARY KEY (id);


--
-- Name: terminal_clean_scores terminal_clean_scores_unique_room_date; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.terminal_clean_scores
    ADD CONSTRAINT terminal_clean_scores_unique_room_date UNIQUE (room_id, date);


--
-- Name: turnover_goals turnover_goals_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.turnover_goals
    ADD CONSTRAINT turnover_goals_pkey PRIMARY KEY (id);


--
-- Name: turnover_label_assoc turnover_label_assoc_constraint; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.turnover_label_assoc
    ADD CONSTRAINT turnover_label_assoc_constraint UNIQUE (preceding_case_id, following_case_id, turnover_label_id);


--
-- Name: turnover_label_assoc turnover_label_assoc_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.turnover_label_assoc
    ADD CONSTRAINT turnover_label_assoc_pkey PRIMARY KEY (id);


--
-- Name: turnover_labels turnover_label_constraint; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.turnover_labels
    ADD CONSTRAINT turnover_label_constraint UNIQUE (name, type);


--
-- Name: turnover_labels turnover_labels_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.turnover_labels
    ADD CONSTRAINT turnover_labels_pkey PRIMARY KEY (id);


--
-- Name: turnover_notes turnover_note_constraint; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.turnover_notes
    ADD CONSTRAINT turnover_note_constraint UNIQUE (preceding_case_id, following_case_id);


--
-- Name: turnover_notes turnover_notes_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.turnover_notes
    ADD CONSTRAINT turnover_notes_pkey PRIMARY KEY (id);


--
-- Name: block_time_releases unique_block_time_release_tsrange_constraint; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_time_releases
    ADD CONSTRAINT unique_block_time_release_tsrange_constraint EXCLUDE USING gist (block_time_id WITH =, release_time WITH &&) WHERE ((unreleased_time IS NULL));


--
-- Name: measurement_periods unique_daterange_constraint; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.measurement_periods
    ADD CONSTRAINT unique_daterange_constraint EXCLUDE USING gist (site_id WITH =, measurement_period WITH &&);


--
-- Name: anesthesias uq_anesthesia_orgId_name; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.anesthesias
    ADD CONSTRAINT "uq_anesthesia_orgId_name" UNIQUE (org_id, name);


--
-- Name: block_schedule_file_rows uq_block_schedule_file_row_number; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_schedule_file_rows
    ADD CONSTRAINT uq_block_schedule_file_row_number UNIQUE (block_schedule_file_id, row_number);


--
-- Name: case_matching_status_reason uq_caseId_matching_status_reason; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_matching_status_reason
    ADD CONSTRAINT "uq_caseId_matching_status_reason" UNIQUE (case_id);


--
-- Name: case_note_plan uq_case_id; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_note_plan
    ADD CONSTRAINT uq_case_id UNIQUE (case_id);


--
-- Name: case_forecast uq_case_id_forecast_variant; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_forecast
    ADD CONSTRAINT uq_case_id_forecast_variant UNIQUE (case_id, forecast_variant);


--
-- Name: observations uq_case_observation; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.observations
    ADD CONSTRAINT uq_case_observation UNIQUE (org_id, case_id, type_id, observation_time);


--
-- Name: case_staff_plan uq_case_staff_role_archived; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_staff_plan
    ADD CONSTRAINT uq_case_staff_role_archived UNIQUE (case_id, staff_id, role, archived_time);


--
-- Name: contact_information uq_contactinformation_id; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.contact_information
    ADD CONSTRAINT uq_contactinformation_id UNIQUE (id);


--
-- Name: observation_type_names uq_observation_type; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.observation_type_names
    ADD CONSTRAINT uq_observation_type UNIQUE (org_id, type_id, name);


--
-- Name: cases uq_orgId_externalCaseId; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cases
    ADD CONSTRAINT "uq_orgId_externalCaseId" UNIQUE (org_id, external_case_id);


--
-- Name: staff uq_orgId_externalStaffId; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff
    ADD CONSTRAINT "uq_orgId_externalStaffId" UNIQUE (org_id, external_staff_id);


--
-- Name: service_lines uq_orgId_external_service_line_id; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.service_lines
    ADD CONSTRAINT "uq_orgId_external_service_line_id" UNIQUE (org_id, external_service_line_id);


--
-- Name: procedures uq_orgId_name; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.procedures
    ADD CONSTRAINT "uq_orgId_name" UNIQUE (org_id, name);


--
-- Name: annotation_tasks uq_orgId_siteId_roomId_startTime_endTime_typeId; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_tasks
    ADD CONSTRAINT "uq_orgId_siteId_roomId_startTime_endTime_typeId" UNIQUE (org_id, site_id, room_id, start_time, end_time, type_id);


--
-- Name: staff_event_notification_contact_information uq_staffEventNotification_id; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff_event_notification_contact_information
    ADD CONSTRAINT "uq_staffEventNotification_id" UNIQUE (id);


--
-- Name: staff_event_notification uq_staffEventNotification_senciID_event_id_case_id; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff_event_notification
    ADD CONSTRAINT "uq_staffEventNotification_senciID_event_id_case_id" UNIQUE (staff_event_contact_information_id, event_id, case_id);


--
-- Name: staff_codes uq_staffId_codingSystem_code; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff_codes
    ADD CONSTRAINT "uq_staffId_codingSystem_code" UNIQUE (staff_id, coding_system, code);


--
-- Name: user_filter_views user_filter_views_name_user_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_filter_views
    ADD CONSTRAINT user_filter_views_name_user_id_key UNIQUE (name, user_id);


--
-- Name: user_filter_views user_filter_views_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_filter_views
    ADD CONSTRAINT user_filter_views_pkey PRIMARY KEY (id, user_id);


--
-- Name: user_room_default_camera user_room_default_camera_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_room_default_camera
    ADD CONSTRAINT user_room_default_camera_pkey PRIMARY KEY (room_id, user_id);


--
-- Name: idx_highlight_feedback_highlight_id_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX idx_highlight_feedback_highlight_id_user_id ON public.highlight_feedback USING btree (highlight_id, user_id);


--
-- Name: ix_anesthesias_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_anesthesias_created_time ON public.anesthesias USING btree (created_time);


--
-- Name: ix_anesthesias_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_anesthesias_name ON public.anesthesias USING btree (name);


--
-- Name: ix_anesthesias_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_anesthesias_org_id ON public.anesthesias USING btree (org_id);


--
-- Name: ix_anesthesias_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_anesthesias_updated_time ON public.anesthesias USING btree (updated_time);


--
-- Name: ix_annotation_task_schedules_annotation_task_type_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_schedules_annotation_task_type_id ON public.annotation_task_schedules USING btree (annotation_task_type_id);


--
-- Name: ix_annotation_task_schedules_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_schedules_created_time ON public.annotation_task_schedules USING btree (created_time);


--
-- Name: ix_annotation_task_schedules_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_schedules_updated_time ON public.annotation_task_schedules USING btree (updated_time);


--
-- Name: ix_annotation_task_type_annotators_annotation_task_type_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_type_annotators_annotation_task_type_id ON public.annotation_task_type_annotators USING btree (annotation_task_type_id);


--
-- Name: ix_annotation_task_type_annotators_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_type_annotators_created_time ON public.annotation_task_type_annotators USING btree (created_time);


--
-- Name: ix_annotation_task_type_annotators_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_type_annotators_updated_time ON public.annotation_task_type_annotators USING btree (updated_time);


--
-- Name: ix_annotation_task_type_annotators_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_type_annotators_user_id ON public.annotation_task_type_annotators USING btree (user_id);


--
-- Name: ix_annotation_task_type_provisional_annotators_annotati_7a09; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_type_provisional_annotators_annotati_7a09 ON public.annotation_task_type_provisional_annotators USING btree (annotation_task_type_id);


--
-- Name: ix_annotation_task_type_provisional_annotators_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_type_provisional_annotators_created_time ON public.annotation_task_type_provisional_annotators USING btree (created_time);


--
-- Name: ix_annotation_task_type_provisional_annotators_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_type_provisional_annotators_updated_time ON public.annotation_task_type_provisional_annotators USING btree (updated_time);


--
-- Name: ix_annotation_task_type_provisional_annotators_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_type_provisional_annotators_user_id ON public.annotation_task_type_provisional_annotators USING btree (user_id);


--
-- Name: ix_annotation_task_type_reviewers_annotation_task_type_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_type_reviewers_annotation_task_type_id ON public.annotation_task_type_reviewers USING btree (annotation_task_type_id);


--
-- Name: ix_annotation_task_type_reviewers_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_type_reviewers_created_time ON public.annotation_task_type_reviewers USING btree (created_time);


--
-- Name: ix_annotation_task_type_reviewers_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_type_reviewers_updated_time ON public.annotation_task_type_reviewers USING btree (updated_time);


--
-- Name: ix_annotation_task_type_reviewers_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_type_reviewers_user_id ON public.annotation_task_type_reviewers USING btree (user_id);


--
-- Name: ix_annotation_task_types_archived_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_types_archived_time ON public.annotation_task_types USING btree (archived_time);


--
-- Name: ix_annotation_task_types_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_types_created_time ON public.annotation_task_types USING btree (created_time);


--
-- Name: ix_annotation_task_types_history_archived_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_types_history_archived_time ON public.annotation_task_types_history USING btree (archived_time);


--
-- Name: ix_annotation_task_types_history_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_types_history_created_time ON public.annotation_task_types_history USING btree (created_time);


--
-- Name: ix_annotation_task_types_history_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_types_history_updated_time ON public.annotation_task_types_history USING btree (updated_time);


--
-- Name: ix_annotation_task_types_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_task_types_updated_time ON public.annotation_task_types USING btree (updated_time);


--
-- Name: ix_annotation_tasks_annotator_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_annotator_user_id ON public.annotation_tasks USING btree (annotator_user_id);


--
-- Name: ix_annotation_tasks_cancelled_reason; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_cancelled_reason ON public.annotation_tasks USING btree (cancelled_reason);


--
-- Name: ix_annotation_tasks_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_created_time ON public.annotation_tasks USING btree (created_time);


--
-- Name: ix_annotation_tasks_end_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_end_time ON public.annotation_tasks USING btree (end_time);


--
-- Name: ix_annotation_tasks_history_annotator_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_history_annotator_user_id ON public.annotation_tasks_history USING btree (annotator_user_id);


--
-- Name: ix_annotation_tasks_history_cancelled_reason; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_history_cancelled_reason ON public.annotation_tasks_history USING btree (cancelled_reason);


--
-- Name: ix_annotation_tasks_history_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_history_created_time ON public.annotation_tasks_history USING btree (created_time);


--
-- Name: ix_annotation_tasks_history_end_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_history_end_time ON public.annotation_tasks_history USING btree (end_time);


--
-- Name: ix_annotation_tasks_history_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_history_org_id ON public.annotation_tasks_history USING btree (org_id);


--
-- Name: ix_annotation_tasks_history_reviewer_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_history_reviewer_user_id ON public.annotation_tasks_history USING btree (reviewer_user_id);


--
-- Name: ix_annotation_tasks_history_room_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_history_room_id ON public.annotation_tasks_history USING btree (room_id);


--
-- Name: ix_annotation_tasks_history_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_history_site_id ON public.annotation_tasks_history USING btree (site_id);


--
-- Name: ix_annotation_tasks_history_start_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_history_start_time ON public.annotation_tasks_history USING btree (start_time);


--
-- Name: ix_annotation_tasks_history_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_history_status ON public.annotation_tasks_history USING btree (status);


--
-- Name: ix_annotation_tasks_history_type_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_history_type_id ON public.annotation_tasks_history USING btree (type_id);


--
-- Name: ix_annotation_tasks_history_type_version; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_history_type_version ON public.annotation_tasks_history USING btree (type_version);


--
-- Name: ix_annotation_tasks_history_updated_by_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_history_updated_by_user_id ON public.annotation_tasks_history USING btree (updated_by_user_id);


--
-- Name: ix_annotation_tasks_history_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_history_updated_time ON public.annotation_tasks_history USING btree (updated_time);


--
-- Name: ix_annotation_tasks_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_org_id ON public.annotation_tasks USING btree (org_id);


--
-- Name: ix_annotation_tasks_reviewer_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_reviewer_user_id ON public.annotation_tasks USING btree (reviewer_user_id);


--
-- Name: ix_annotation_tasks_room_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_room_id ON public.annotation_tasks USING btree (room_id);


--
-- Name: ix_annotation_tasks_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_site_id ON public.annotation_tasks USING btree (site_id);


--
-- Name: ix_annotation_tasks_start_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_start_time ON public.annotation_tasks USING btree (start_time);


--
-- Name: ix_annotation_tasks_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_status ON public.annotation_tasks USING btree (status);


--
-- Name: ix_annotation_tasks_type_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_type_id ON public.annotation_tasks USING btree (type_id);


--
-- Name: ix_annotation_tasks_type_version; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_type_version ON public.annotation_tasks USING btree (type_version);


--
-- Name: ix_annotation_tasks_updated_by_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_updated_by_user_id ON public.annotation_tasks USING btree (updated_by_user_id);


--
-- Name: ix_annotation_tasks_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_annotation_tasks_updated_time ON public.annotation_tasks USING btree (updated_time);


--
-- Name: ix_block_release_file_rows_block_release_file_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_release_file_rows_block_release_file_id ON public.block_release_file_rows USING btree (block_release_file_id);


--
-- Name: ix_block_release_file_rows_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_release_file_rows_created_time ON public.block_release_file_rows USING btree (created_time);


--
-- Name: ix_block_release_file_rows_external_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_release_file_rows_external_id ON public.block_release_file_rows USING btree (external_id);


--
-- Name: ix_block_release_file_rows_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_release_file_rows_org_id ON public.block_release_file_rows USING btree (org_id);


--
-- Name: ix_block_release_file_rows_room_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_release_file_rows_room_id ON public.block_release_file_rows USING btree (room_id);


--
-- Name: ix_block_release_file_rows_room_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_release_file_rows_room_name ON public.block_release_file_rows USING btree (room_name);


--
-- Name: ix_block_release_file_rows_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_release_file_rows_site_id ON public.block_release_file_rows USING btree (site_id);


--
-- Name: ix_block_release_file_rows_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_release_file_rows_updated_time ON public.block_release_file_rows USING btree (updated_time);


--
-- Name: ix_block_release_processed_files_bucket_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_release_processed_files_bucket_name ON public.block_release_processed_files USING btree (bucket_name);


--
-- Name: ix_block_release_processed_files_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_release_processed_files_created_time ON public.block_release_processed_files USING btree (created_time);


--
-- Name: ix_block_release_processed_files_file_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_release_processed_files_file_name ON public.block_release_processed_files USING btree (file_name);


--
-- Name: ix_block_release_processed_files_md5_hash; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_release_processed_files_md5_hash ON public.block_release_processed_files USING btree (md5_hash);


--
-- Name: ix_block_release_processed_files_media_link; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_release_processed_files_media_link ON public.block_release_processed_files USING btree (media_link);


--
-- Name: ix_block_release_processed_files_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_release_processed_files_org_id ON public.block_release_processed_files USING btree (org_id);


--
-- Name: ix_block_release_processed_files_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_release_processed_files_updated_time ON public.block_release_processed_files USING btree (updated_time);


--
-- Name: ix_block_schedule_file_rows_block_schedule_file_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_schedule_file_rows_block_schedule_file_id ON public.block_schedule_file_rows USING btree (block_schedule_file_id);


--
-- Name: ix_block_schedule_file_rows_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_schedule_file_rows_created_time ON public.block_schedule_file_rows USING btree (created_time);


--
-- Name: ix_block_schedule_file_rows_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_schedule_file_rows_org_id ON public.block_schedule_file_rows USING btree (org_id);


--
-- Name: ix_block_schedule_file_rows_room_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_schedule_file_rows_room_id ON public.block_schedule_file_rows USING btree (room_id);


--
-- Name: ix_block_schedule_file_rows_room_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_schedule_file_rows_room_name ON public.block_schedule_file_rows USING btree (room_name);


--
-- Name: ix_block_schedule_file_rows_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_schedule_file_rows_site_id ON public.block_schedule_file_rows USING btree (site_id);


--
-- Name: ix_block_schedule_file_rows_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_schedule_file_rows_updated_time ON public.block_schedule_file_rows USING btree (updated_time);


--
-- Name: ix_block_schedule_processed_files_bucket_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_schedule_processed_files_bucket_name ON public.block_schedule_processed_files USING btree (bucket_name);


--
-- Name: ix_block_schedule_processed_files_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_schedule_processed_files_created_time ON public.block_schedule_processed_files USING btree (created_time);


--
-- Name: ix_block_schedule_processed_files_file_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_schedule_processed_files_file_name ON public.block_schedule_processed_files USING btree (file_name);


--
-- Name: ix_block_schedule_processed_files_md5_hash; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_schedule_processed_files_md5_hash ON public.block_schedule_processed_files USING btree (md5_hash);


--
-- Name: ix_block_schedule_processed_files_media_link; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_schedule_processed_files_media_link ON public.block_schedule_processed_files USING btree (media_link);


--
-- Name: ix_block_schedule_processed_files_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_schedule_processed_files_org_id ON public.block_schedule_processed_files USING btree (org_id);


--
-- Name: ix_block_schedule_processed_files_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_schedule_processed_files_updated_time ON public.block_schedule_processed_files USING btree (updated_time);


--
-- Name: ix_block_sites_block_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_sites_block_id ON public.block_sites USING btree (block_id);


--
-- Name: ix_block_sites_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_sites_site_id ON public.block_sites USING btree (site_id);


--
-- Name: ix_block_surgeons_block_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_surgeons_block_id ON public.block_surgeons USING btree (block_id);


--
-- Name: ix_block_surgeons_staff_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_surgeons_staff_id ON public.block_surgeons USING btree (staff_id);


--
-- Name: ix_block_time_overrides_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_time_overrides_created_time ON public.block_time_overrides USING btree (created_time);


--
-- Name: ix_block_time_overrides_history_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_time_overrides_history_created_time ON public.block_time_overrides_history USING btree (created_time);


--
-- Name: ix_block_time_overrides_history_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_time_overrides_history_updated_time ON public.block_time_overrides_history USING btree (updated_time);


--
-- Name: ix_block_time_overrides_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_time_overrides_updated_time ON public.block_time_overrides USING btree (updated_time);


--
-- Name: ix_block_time_releases_block_time_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_time_releases_block_time_id ON public.block_time_releases USING btree (block_time_id);


--
-- Name: ix_block_time_releases_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_time_releases_created_time ON public.block_time_releases USING btree (created_time);


--
-- Name: ix_block_time_releases_end_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_time_releases_end_time ON public.block_time_releases USING btree (end_time);


--
-- Name: ix_block_time_releases_release_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_time_releases_release_time ON public.block_time_releases USING btree (release_time);


--
-- Name: ix_block_time_releases_released_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_time_releases_released_at ON public.block_time_releases USING btree (released_at);


--
-- Name: ix_block_time_releases_released_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_time_releases_released_time ON public.block_time_releases USING btree (released_time);


--
-- Name: ix_block_time_releases_source_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_time_releases_source_type ON public.block_time_releases USING btree (source_type);


--
-- Name: ix_block_time_releases_start_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_time_releases_start_time ON public.block_time_releases USING btree (start_time);


--
-- Name: ix_block_time_releases_unreleased_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_time_releases_unreleased_time ON public.block_time_releases USING btree (unreleased_time);


--
-- Name: ix_block_time_releases_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_time_releases_updated_time ON public.block_time_releases USING btree (updated_time);


--
-- Name: ix_block_times_block_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_times_block_id ON public.block_times USING btree (block_id);


--
-- Name: ix_block_times_block_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_times_block_time ON public.block_times USING btree (block_time);


--
-- Name: ix_block_times_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_times_created_time ON public.block_times USING btree (created_time);


--
-- Name: ix_block_times_end_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_times_end_time ON public.block_times USING btree (end_time);


--
-- Name: ix_block_times_room_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_times_room_id ON public.block_times USING btree (room_id);


--
-- Name: ix_block_times_start_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_times_start_time ON public.block_times USING btree (start_time);


--
-- Name: ix_block_times_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_block_times_updated_time ON public.block_times USING btree (updated_time);


--
-- Name: ix_blocks_archived_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_blocks_archived_time ON public.blocks USING btree (archived_time);


--
-- Name: ix_blocks_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_blocks_created_time ON public.blocks USING btree (created_time);


--
-- Name: ix_blocks_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_blocks_name ON public.blocks USING btree (name);


--
-- Name: ix_blocks_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_blocks_org_id ON public.blocks USING btree (org_id);


--
-- Name: ix_blocks_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_blocks_updated_time ON public.blocks USING btree (updated_time);


--
-- Name: ix_board_config_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_board_config_created_time ON public.board_config USING btree (created_time);


--
-- Name: ix_board_config_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_board_config_org_id ON public.board_config USING btree (org_id);


--
-- Name: ix_board_config_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_board_config_site_id ON public.board_config USING btree (site_id);


--
-- Name: ix_board_config_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_board_config_updated_time ON public.board_config USING btree (updated_time);


--
-- Name: ix_board_room_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_board_room_created_time ON public.board_room USING btree (created_time);


--
-- Name: ix_board_room_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_board_room_updated_time ON public.board_room USING btree (updated_time);


--
-- Name: ix_cameras_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cameras_created_time ON public.cameras USING btree (created_time);


--
-- Name: ix_cameras_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cameras_org_id ON public.cameras USING btree (org_id);


--
-- Name: ix_cameras_room_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cameras_room_id ON public.cameras USING btree (room_id);


--
-- Name: ix_cameras_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cameras_site_id ON public.cameras USING btree (site_id);


--
-- Name: ix_cameras_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cameras_updated_time ON public.cameras USING btree (updated_time);


--
-- Name: ix_case_classification_types_archived_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_classification_types_archived_time ON public.case_classification_types USING btree (archived_time);


--
-- Name: ix_case_classification_types_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_classification_types_created_time ON public.case_classification_types USING btree (created_time);


--
-- Name: ix_case_classification_types_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_classification_types_updated_time ON public.case_classification_types USING btree (updated_time);


--
-- Name: ix_case_derived_properties_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_derived_properties_case_id ON public.case_derived_properties USING btree (case_id);


--
-- Name: ix_case_derived_properties_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_derived_properties_created_time ON public.case_derived_properties USING btree (created_time);


--
-- Name: ix_case_derived_properties_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_derived_properties_updated_time ON public.case_derived_properties USING btree (updated_time);


--
-- Name: ix_case_flag_archived_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_flag_archived_time ON public.case_flag USING btree (archived_time);


--
-- Name: ix_case_flag_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_flag_case_id ON public.case_flag USING btree (case_id);


--
-- Name: ix_case_flag_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_flag_created_time ON public.case_flag USING btree (created_time);


--
-- Name: ix_case_flag_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_flag_org_id ON public.case_flag USING btree (org_id);


--
-- Name: ix_case_flag_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_flag_site_id ON public.case_flag USING btree (site_id);


--
-- Name: ix_case_flag_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_flag_updated_time ON public.case_flag USING btree (updated_time);


--
-- Name: ix_case_forecast_bayesian_duration_minutes; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_bayesian_duration_minutes ON public.case_forecast USING btree (bayesian_duration_minutes);


--
-- Name: ix_case_forecast_bayesian_end_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_bayesian_end_time ON public.case_forecast USING btree (bayesian_end_time);


--
-- Name: ix_case_forecast_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_case_id ON public.case_forecast USING btree (case_id);


--
-- Name: ix_case_forecast_case_start_source; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_case_start_source ON public.case_forecast USING btree (case_start_source);


--
-- Name: ix_case_forecast_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_created_time ON public.case_forecast USING btree (created_time);


--
-- Name: ix_case_forecast_forecast_end_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_forecast_end_time ON public.case_forecast USING btree (forecast_end_time);


--
-- Name: ix_case_forecast_forecast_start_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_forecast_start_time ON public.case_forecast USING btree (forecast_start_time);


--
-- Name: ix_case_forecast_forecast_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_forecast_status ON public.case_forecast USING btree (forecast_status);


--
-- Name: ix_case_forecast_forecast_variant; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_forecast_variant ON public.case_forecast USING btree (forecast_variant);


--
-- Name: ix_case_forecast_history_bayesian_duration_minutes; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_bayesian_duration_minutes ON public.case_forecast_history USING btree (bayesian_duration_minutes);


--
-- Name: ix_case_forecast_history_bayesian_end_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_bayesian_end_time ON public.case_forecast_history USING btree (bayesian_end_time);


--
-- Name: ix_case_forecast_history_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_case_id ON public.case_forecast_history USING btree (case_id);


--
-- Name: ix_case_forecast_history_case_start_source; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_case_start_source ON public.case_forecast_history USING btree (case_start_source);


--
-- Name: ix_case_forecast_history_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_created_time ON public.case_forecast_history USING btree (created_time);


--
-- Name: ix_case_forecast_history_forecast_end_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_forecast_end_time ON public.case_forecast_history USING btree (forecast_end_time);


--
-- Name: ix_case_forecast_history_forecast_start_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_forecast_start_time ON public.case_forecast_history USING btree (forecast_start_time);


--
-- Name: ix_case_forecast_history_forecast_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_forecast_status ON public.case_forecast_history USING btree (forecast_status);


--
-- Name: ix_case_forecast_history_forecast_variant; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_forecast_variant ON public.case_forecast_history USING btree (forecast_variant);


--
-- Name: ix_case_forecast_history_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_id ON public.case_forecast_history USING btree (id);


--
-- Name: ix_case_forecast_history_is_auto_follow; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_is_auto_follow ON public.case_forecast_history USING btree (is_auto_follow);


--
-- Name: ix_case_forecast_history_is_overtime; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_is_overtime ON public.case_forecast_history USING btree (is_overtime);


--
-- Name: ix_case_forecast_history_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_org_id ON public.case_forecast_history USING btree (org_id);


--
-- Name: ix_case_forecast_history_pythia_duration_minutes; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_pythia_duration_minutes ON public.case_forecast_history USING btree (pythia_duration_minutes);


--
-- Name: ix_case_forecast_history_pythia_end_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_pythia_end_time ON public.case_forecast_history USING btree (pythia_end_time);


--
-- Name: ix_case_forecast_history_pythia_prediction_tag; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_pythia_prediction_tag ON public.case_forecast_history USING btree (pythia_prediction_tag);


--
-- Name: ix_case_forecast_history_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_site_id ON public.case_forecast_history USING btree (site_id);


--
-- Name: ix_case_forecast_history_static_duration_end_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_static_duration_end_time ON public.case_forecast_history USING btree (static_duration_end_time);


--
-- Name: ix_case_forecast_history_static_duration_minutes; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_static_duration_minutes ON public.case_forecast_history USING btree (static_duration_minutes);


--
-- Name: ix_case_forecast_history_static_start_offset_minutes; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_static_start_offset_minutes ON public.case_forecast_history USING btree (static_start_offset_minutes);


--
-- Name: ix_case_forecast_history_transformer_end_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_transformer_end_time ON public.case_forecast_history USING btree (transformer_end_time);


--
-- Name: ix_case_forecast_history_turnover_duration_minutes; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_turnover_duration_minutes ON public.case_forecast_history USING btree (turnover_duration_minutes);


--
-- Name: ix_case_forecast_history_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_history_updated_time ON public.case_forecast_history USING btree (updated_time);


--
-- Name: ix_case_forecast_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_id ON public.case_forecast USING btree (id);


--
-- Name: ix_case_forecast_is_auto_follow; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_is_auto_follow ON public.case_forecast USING btree (is_auto_follow);


--
-- Name: ix_case_forecast_is_overtime; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_is_overtime ON public.case_forecast USING btree (is_overtime);


--
-- Name: ix_case_forecast_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_org_id ON public.case_forecast USING btree (org_id);


--
-- Name: ix_case_forecast_pythia_duration_minutes; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_pythia_duration_minutes ON public.case_forecast USING btree (pythia_duration_minutes);


--
-- Name: ix_case_forecast_pythia_end_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_pythia_end_time ON public.case_forecast USING btree (pythia_end_time);


--
-- Name: ix_case_forecast_pythia_prediction_tag; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_pythia_prediction_tag ON public.case_forecast USING btree (pythia_prediction_tag);


--
-- Name: ix_case_forecast_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_site_id ON public.case_forecast USING btree (site_id);


--
-- Name: ix_case_forecast_static_duration_end_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_static_duration_end_time ON public.case_forecast USING btree (static_duration_end_time);


--
-- Name: ix_case_forecast_static_duration_minutes; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_static_duration_minutes ON public.case_forecast USING btree (static_duration_minutes);


--
-- Name: ix_case_forecast_static_start_offset_minutes; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_static_start_offset_minutes ON public.case_forecast USING btree (static_start_offset_minutes);


--
-- Name: ix_case_forecast_transformer_end_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_transformer_end_time ON public.case_forecast USING btree (transformer_end_time);


--
-- Name: ix_case_forecast_turnover_duration_minutes; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_turnover_duration_minutes ON public.case_forecast USING btree (turnover_duration_minutes);


--
-- Name: ix_case_forecast_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_forecast_updated_time ON public.case_forecast USING btree (updated_time);


--
-- Name: ix_case_label_assoc_archived_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_label_assoc_archived_time ON public.case_label_assoc USING btree (archived_time);


--
-- Name: ix_case_label_assoc_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_label_assoc_case_id ON public.case_label_assoc USING btree (case_id);


--
-- Name: ix_case_label_assoc_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_label_assoc_created_time ON public.case_label_assoc USING btree (created_time);


--
-- Name: ix_case_label_assoc_option_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_label_assoc_option_id ON public.case_label_assoc USING btree (option_id);


--
-- Name: ix_case_label_assoc_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_label_assoc_updated_time ON public.case_label_assoc USING btree (updated_time);


--
-- Name: ix_case_label_categories_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_label_categories_org_id ON public.case_label_categories USING btree (org_id);


--
-- Name: ix_case_label_categories_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_label_categories_site_id ON public.case_label_categories USING btree (site_id);


--
-- Name: ix_case_label_field_options_field_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_label_field_options_field_id ON public.case_label_field_options USING btree (field_id);


--
-- Name: ix_case_label_fields_category_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_label_fields_category_id ON public.case_label_fields USING btree (category_id);


--
-- Name: ix_case_label_fields_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_label_fields_org_id ON public.case_label_fields USING btree (org_id);


--
-- Name: ix_case_label_fields_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_label_fields_site_id ON public.case_label_fields USING btree (site_id);


--
-- Name: ix_case_matching_status_reason_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_matching_status_reason_case_id ON public.case_matching_status_reason USING btree (case_id);


--
-- Name: ix_case_matching_status_reason_case_matching_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_matching_status_reason_case_matching_status ON public.case_matching_status_reason USING btree (case_matching_status);


--
-- Name: ix_case_matching_status_reason_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_matching_status_reason_created_time ON public.case_matching_status_reason USING btree (created_time);


--
-- Name: ix_case_matching_status_reason_history_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_matching_status_reason_history_case_id ON public.case_matching_status_reason_history USING btree (case_id);


--
-- Name: ix_case_matching_status_reason_history_case_matching_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_matching_status_reason_history_case_matching_status ON public.case_matching_status_reason_history USING btree (case_matching_status);


--
-- Name: ix_case_matching_status_reason_history_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_matching_status_reason_history_created_time ON public.case_matching_status_reason_history USING btree (created_time);


--
-- Name: ix_case_matching_status_reason_history_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_matching_status_reason_history_updated_time ON public.case_matching_status_reason_history USING btree (updated_time);


--
-- Name: ix_case_matching_status_reason_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_matching_status_reason_updated_time ON public.case_matching_status_reason USING btree (updated_time);


--
-- Name: ix_case_note_plan_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_note_plan_case_id ON public.case_note_plan USING btree (case_id);


--
-- Name: ix_case_note_plan_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_note_plan_created_time ON public.case_note_plan USING btree (created_time);


--
-- Name: ix_case_note_plan_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_note_plan_org_id ON public.case_note_plan USING btree (org_id);


--
-- Name: ix_case_note_plan_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_note_plan_site_id ON public.case_note_plan USING btree (site_id);


--
-- Name: ix_case_note_plan_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_note_plan_updated_time ON public.case_note_plan USING btree (updated_time);


--
-- Name: ix_case_procedures_archived_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_procedures_archived_time ON public.case_procedures USING btree (archived_time);


--
-- Name: ix_case_procedures_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_procedures_created_time ON public.case_procedures USING btree (created_time);


--
-- Name: ix_case_procedures_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_procedures_updated_time ON public.case_procedures USING btree (updated_time);


--
-- Name: ix_case_raw_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_raw_case_id ON public.case_raw USING btree (case_id);


--
-- Name: ix_case_raw_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_raw_created_time ON public.case_raw USING btree (created_time);


--
-- Name: ix_case_raw_event_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_raw_event_time ON public.case_raw USING btree (event_time);


--
-- Name: ix_case_raw_external_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_raw_external_case_id ON public.case_raw USING btree (external_case_id);


--
-- Name: ix_case_raw_external_message_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_case_raw_external_message_id ON public.case_raw USING btree (external_message_id);


--
-- Name: ix_case_raw_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_raw_org_id ON public.case_raw USING btree (org_id);


--
-- Name: ix_case_raw_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_raw_updated_time ON public.case_raw USING btree (updated_time);


--
-- Name: ix_case_staff_archived_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_staff_archived_time ON public.case_staff USING btree (archived_time);


--
-- Name: ix_case_staff_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_staff_case_id ON public.case_staff USING btree (case_id);


--
-- Name: ix_case_staff_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_staff_created_time ON public.case_staff USING btree (created_time);


--
-- Name: ix_case_staff_plan_archived_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_staff_plan_archived_time ON public.case_staff_plan USING btree (archived_time);


--
-- Name: ix_case_staff_plan_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_staff_plan_case_id ON public.case_staff_plan USING btree (case_id);


--
-- Name: ix_case_staff_plan_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_staff_plan_created_time ON public.case_staff_plan USING btree (created_time);


--
-- Name: ix_case_staff_plan_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_staff_plan_org_id ON public.case_staff_plan USING btree (org_id);


--
-- Name: ix_case_staff_plan_role; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_staff_plan_role ON public.case_staff_plan USING btree (role);


--
-- Name: ix_case_staff_plan_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_staff_plan_site_id ON public.case_staff_plan USING btree (site_id);


--
-- Name: ix_case_staff_plan_staff_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_staff_plan_staff_id ON public.case_staff_plan USING btree (staff_id);


--
-- Name: ix_case_staff_plan_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_staff_plan_updated_time ON public.case_staff_plan USING btree (updated_time);


--
-- Name: ix_case_staff_role; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_staff_role ON public.case_staff USING btree (role);


--
-- Name: ix_case_staff_staff_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_staff_staff_id ON public.case_staff USING btree (staff_id);


--
-- Name: ix_case_staff_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_staff_updated_time ON public.case_staff USING btree (updated_time);


--
-- Name: ix_case_to_block_overrides_block_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_to_block_overrides_block_date ON public.case_to_block_overrides USING btree (block_date);


--
-- Name: ix_case_to_block_overrides_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_to_block_overrides_created_time ON public.case_to_block_overrides USING btree (created_time);


--
-- Name: ix_case_to_block_overrides_history_block_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_to_block_overrides_history_block_date ON public.case_to_block_overrides_history USING btree (block_date);


--
-- Name: ix_case_to_block_overrides_history_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_to_block_overrides_history_created_time ON public.case_to_block_overrides_history USING btree (created_time);


--
-- Name: ix_case_to_block_overrides_history_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_to_block_overrides_history_updated_time ON public.case_to_block_overrides_history USING btree (updated_time);


--
-- Name: ix_case_to_block_overrides_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_case_to_block_overrides_updated_time ON public.case_to_block_overrides USING btree (updated_time);


--
-- Name: ix_cases_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_created_time ON public.cases USING btree (created_time);


--
-- Name: ix_cases_external_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_external_case_id ON public.cases USING btree (external_case_id);


--
-- Name: ix_cases_history_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_history_created_time ON public.cases_history USING btree (created_time);


--
-- Name: ix_cases_history_external_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_history_external_case_id ON public.cases_history USING btree (external_case_id);


--
-- Name: ix_cases_history_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_history_org_id ON public.cases_history USING btree (org_id);


--
-- Name: ix_cases_history_patient_class; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_history_patient_class ON public.cases_history USING btree (patient_class);


--
-- Name: ix_cases_history_scheduled_end_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_history_scheduled_end_time ON public.cases_history USING btree (scheduled_end_time);


--
-- Name: ix_cases_history_scheduled_start_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_history_scheduled_start_time ON public.cases_history USING btree (scheduled_start_time);


--
-- Name: ix_cases_history_service_line_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_history_service_line_id ON public.cases_history USING btree (service_line_id);


--
-- Name: ix_cases_history_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_history_site_id ON public.cases_history USING btree (site_id);


--
-- Name: ix_cases_history_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_history_status ON public.cases_history USING btree (status);


--
-- Name: ix_cases_history_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_history_updated_time ON public.cases_history USING btree (updated_time);


--
-- Name: ix_cases_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_org_id ON public.cases USING btree (org_id);


--
-- Name: ix_cases_patient_class; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_patient_class ON public.cases USING btree (patient_class);


--
-- Name: ix_cases_scheduled_end_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_scheduled_end_time ON public.cases USING btree (scheduled_end_time);


--
-- Name: ix_cases_scheduled_start_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_scheduled_start_time ON public.cases USING btree (scheduled_start_time);


--
-- Name: ix_cases_service_line_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_service_line_id ON public.cases USING btree (service_line_id);


--
-- Name: ix_cases_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_site_id ON public.cases USING btree (site_id);


--
-- Name: ix_cases_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_status ON public.cases USING btree (status);


--
-- Name: ix_cases_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cases_updated_time ON public.cases USING btree (updated_time);


--
-- Name: ix_cluster_enable_audio; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cluster_enable_audio ON public.cluster USING btree (enable_audio);


--
-- Name: ix_cluster_mapping_cluster_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cluster_mapping_cluster_id ON public.cluster_mapping USING btree (cluster_id);


--
-- Name: ix_cluster_mapping_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cluster_mapping_site_id ON public.cluster_mapping USING btree (site_id);


--
-- Name: ix_cluster_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cluster_name ON public.cluster USING btree (name);


--
-- Name: ix_contact_information_contact_information_value; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_contact_information_contact_information_value ON public.contact_information USING btree (contact_information_value);


--
-- Name: ix_contact_information_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_contact_information_created_time ON public.contact_information USING btree (created_time);


--
-- Name: ix_contact_information_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_contact_information_id ON public.contact_information USING btree (id);


--
-- Name: ix_contact_information_initial_notification_sent; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_contact_information_initial_notification_sent ON public.contact_information USING btree (initial_notification_sent);


--
-- Name: ix_contact_information_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_contact_information_type ON public.contact_information USING btree (type);


--
-- Name: ix_contact_information_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_contact_information_updated_time ON public.contact_information USING btree (updated_time);


--
-- Name: ix_custom_phase_configs_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_custom_phase_configs_created_time ON public.custom_phase_configs USING btree (created_time);


--
-- Name: ix_custom_phase_configs_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_custom_phase_configs_org_id ON public.custom_phase_configs USING btree (org_id);


--
-- Name: ix_custom_phase_configs_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_custom_phase_configs_updated_time ON public.custom_phase_configs USING btree (updated_time);


--
-- Name: ix_event_dashboard_visibility_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_event_dashboard_visibility_created_time ON public.event_dashboard_visibility USING btree (created_time);


--
-- Name: ix_event_dashboard_visibility_event_type_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_event_dashboard_visibility_event_type_id ON public.event_dashboard_visibility USING btree (event_type_id);


--
-- Name: ix_event_dashboard_visibility_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_event_dashboard_visibility_updated_time ON public.event_dashboard_visibility USING btree (updated_time);


--
-- Name: ix_event_label_options_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_event_label_options_created_time ON public.event_label_options USING btree (created_time);


--
-- Name: ix_event_label_options_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_event_label_options_name ON public.event_label_options USING btree (name);


--
-- Name: ix_event_label_options_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_event_label_options_updated_time ON public.event_label_options USING btree (updated_time);


--
-- Name: ix_event_types_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_event_types_created_time ON public.event_types USING btree (created_time);


--
-- Name: ix_event_types_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_event_types_type ON public.event_types USING btree (type);


--
-- Name: ix_event_types_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_event_types_updated_time ON public.event_types USING btree (updated_time);


--
-- Name: ix_events_camera_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_camera_id ON public.events USING btree (camera_id);


--
-- Name: ix_events_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_case_id ON public.events USING btree (case_id);


--
-- Name: ix_events_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_created_time ON public.events USING btree (created_time);


--
-- Name: ix_events_deleted_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_deleted_at ON public.events USING btree (deleted_at);


--
-- Name: ix_events_etag; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_etag ON public.events USING btree (etag);


--
-- Name: ix_events_event_matching_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_event_matching_status ON public.events USING btree (event_matching_status);


--
-- Name: ix_events_event_type_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_event_type_id ON public.events USING btree (event_type_id);


--
-- Name: ix_events_history_camera_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_history_camera_id ON public.events_history USING btree (camera_id);


--
-- Name: ix_events_history_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_history_case_id ON public.events_history USING btree (case_id);


--
-- Name: ix_events_history_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_history_created_time ON public.events_history USING btree (created_time);


--
-- Name: ix_events_history_deleted_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_history_deleted_at ON public.events_history USING btree (deleted_at);


--
-- Name: ix_events_history_etag; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_history_etag ON public.events_history USING btree (etag);


--
-- Name: ix_events_history_event_matching_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_history_event_matching_status ON public.events_history USING btree (event_matching_status);


--
-- Name: ix_events_history_event_type_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_history_event_type_id ON public.events_history USING btree (event_type_id);


--
-- Name: ix_events_history_labels; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_history_labels ON public.events_history USING btree (labels);


--
-- Name: ix_events_history_model_version; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_history_model_version ON public.events_history USING btree (model_version);


--
-- Name: ix_events_history_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_history_org_id ON public.events_history USING btree (org_id);


--
-- Name: ix_events_history_room_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_history_room_id ON public.events_history USING btree (room_id);


--
-- Name: ix_events_history_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_history_site_id ON public.events_history USING btree (site_id);


--
-- Name: ix_events_history_source_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_history_source_type ON public.events_history USING btree (source_type);


--
-- Name: ix_events_history_start_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_history_start_time ON public.events_history USING btree (start_time);


--
-- Name: ix_events_history_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_history_updated_time ON public.events_history USING btree (updated_time);


--
-- Name: ix_events_labels; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_labels ON public.events USING btree (labels);


--
-- Name: ix_events_model_version; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_model_version ON public.events USING btree (model_version);


--
-- Name: ix_events_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_org_id ON public.events USING btree (org_id);


--
-- Name: ix_events_room_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_room_id ON public.events USING btree (room_id);


--
-- Name: ix_events_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_site_id ON public.events USING btree (site_id);


--
-- Name: ix_events_source_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_source_type ON public.events USING btree (source_type);


--
-- Name: ix_events_start_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_start_time ON public.events USING btree (start_time);


--
-- Name: ix_events_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_events_updated_time ON public.events USING btree (updated_time);


--
-- Name: ix_highlight_assets_asset_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_highlight_assets_asset_id ON public.highlight_assets USING btree (asset_id);


--
-- Name: ix_highlight_feedback_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_highlight_feedback_created_time ON public.highlight_feedback USING btree (created_time);


--
-- Name: ix_highlight_feedback_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_highlight_feedback_org_id ON public.highlight_feedback USING btree (org_id);


--
-- Name: ix_highlight_feedback_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_highlight_feedback_updated_time ON public.highlight_feedback USING btree (updated_time);


--
-- Name: ix_highlight_feedback_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_highlight_feedback_user_id ON public.highlight_feedback USING btree (user_id);


--
-- Name: ix_highlight_users_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_highlight_users_user_id ON public.highlight_users USING btree (user_id);


--
-- Name: ix_highlights_archived_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_highlights_archived_time ON public.highlights USING btree (archived_time);


--
-- Name: ix_highlights_category; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_highlights_category ON public.highlights USING btree (category);


--
-- Name: ix_highlights_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_highlights_created_time ON public.highlights USING btree (created_time);


--
-- Name: ix_highlights_end_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_highlights_end_time ON public.highlights USING btree (end_time);


--
-- Name: ix_highlights_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_highlights_org_id ON public.highlights USING btree (org_id);


--
-- Name: ix_highlights_room_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_highlights_room_id ON public.highlights USING btree (room_id);


--
-- Name: ix_highlights_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_highlights_site_id ON public.highlights USING btree (site_id);


--
-- Name: ix_highlights_start_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_highlights_start_time ON public.highlights USING btree (start_time);


--
-- Name: ix_highlights_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_highlights_updated_time ON public.highlights USING btree (updated_time);


--
-- Name: ix_identifier_mapping_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_identifier_mapping_created_time ON public.identifier_mapping USING btree (created_time);


--
-- Name: ix_identifier_mapping_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_identifier_mapping_updated_time ON public.identifier_mapping USING btree (updated_time);


--
-- Name: ix_measurement_periods_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_measurement_periods_created_time ON public.measurement_periods USING btree (created_time);


--
-- Name: ix_measurement_periods_end_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_measurement_periods_end_date ON public.measurement_periods USING btree (end_date);


--
-- Name: ix_measurement_periods_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_measurement_periods_name ON public.measurement_periods USING btree (name);


--
-- Name: ix_measurement_periods_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_measurement_periods_site_id ON public.measurement_periods USING btree (site_id);


--
-- Name: ix_measurement_periods_start_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_measurement_periods_start_date ON public.measurement_periods USING btree (start_date);


--
-- Name: ix_measurement_periods_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_measurement_periods_updated_time ON public.measurement_periods USING btree (updated_time);


--
-- Name: ix_observation_type_names_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_observation_type_names_created_time ON public.observation_type_names USING btree (created_time);


--
-- Name: ix_observation_type_names_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_observation_type_names_org_id ON public.observation_type_names USING btree (org_id);


--
-- Name: ix_observation_type_names_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_observation_type_names_updated_time ON public.observation_type_names USING btree (updated_time);


--
-- Name: ix_observation_types_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_observation_types_created_time ON public.observation_types USING btree (created_time);


--
-- Name: ix_observation_types_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_observation_types_updated_time ON public.observation_types USING btree (updated_time);


--
-- Name: ix_observations_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_observations_case_id ON public.observations USING btree (case_id);


--
-- Name: ix_observations_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_observations_created_time ON public.observations USING btree (created_time);


--
-- Name: ix_observations_observation_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_observations_observation_time ON public.observations USING btree (observation_time);


--
-- Name: ix_observations_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_observations_org_id ON public.observations USING btree (org_id);


--
-- Name: ix_observations_recorded_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_observations_recorded_time ON public.observations USING btree (recorded_time);


--
-- Name: ix_observations_type_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_observations_type_id ON public.observations USING btree (type_id);


--
-- Name: ix_observations_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_observations_updated_time ON public.observations USING btree (updated_time);


--
-- Name: ix_organizations_auth0_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_organizations_auth0_org_id ON public.organizations USING btree (auth0_org_id);


--
-- Name: ix_organizations_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_organizations_created_time ON public.organizations USING btree (created_time);


--
-- Name: ix_organizations_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_organizations_updated_time ON public.organizations USING btree (updated_time);


--
-- Name: ix_phase_relationships_child_phase_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phase_relationships_child_phase_id ON public.phase_relationships USING btree (child_phase_id);


--
-- Name: ix_phase_relationships_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phase_relationships_created_time ON public.phase_relationships USING btree (created_time);


--
-- Name: ix_phase_relationships_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phase_relationships_org_id ON public.phase_relationships USING btree (org_id);


--
-- Name: ix_phase_relationships_parent_phase_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phase_relationships_parent_phase_id ON public.phase_relationships USING btree (parent_phase_id);


--
-- Name: ix_phase_relationships_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phase_relationships_updated_time ON public.phase_relationships USING btree (updated_time);


--
-- Name: ix_phase_types_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phase_types_created_time ON public.phase_types USING btree (created_time);


--
-- Name: ix_phase_types_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phase_types_updated_time ON public.phase_types USING btree (updated_time);


--
-- Name: ix_phases_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_case_id ON public.phases USING btree (case_id);


--
-- Name: ix_phases_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_created_time ON public.phases USING btree (created_time);


--
-- Name: ix_phases_end_event_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_end_event_id ON public.phases USING btree (end_event_id);


--
-- Name: ix_phases_etag; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_etag ON public.phases USING btree (etag);


--
-- Name: ix_phases_event_matching_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_event_matching_status ON public.phases USING btree (event_matching_status);


--
-- Name: ix_phases_history_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_history_case_id ON public.phases_history USING btree (case_id);


--
-- Name: ix_phases_history_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_history_created_time ON public.phases_history USING btree (created_time);


--
-- Name: ix_phases_history_end_event_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_history_end_event_id ON public.phases_history USING btree (end_event_id);


--
-- Name: ix_phases_history_etag; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_history_etag ON public.phases_history USING btree (etag);


--
-- Name: ix_phases_history_event_matching_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_history_event_matching_status ON public.phases_history USING btree (event_matching_status);


--
-- Name: ix_phases_history_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_history_org_id ON public.phases_history USING btree (org_id);


--
-- Name: ix_phases_history_room_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_history_room_id ON public.phases_history USING btree (room_id);


--
-- Name: ix_phases_history_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_history_site_id ON public.phases_history USING btree (site_id);


--
-- Name: ix_phases_history_source_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_history_source_type ON public.phases_history USING btree (source_type);


--
-- Name: ix_phases_history_start_event_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_history_start_event_id ON public.phases_history USING btree (start_event_id);


--
-- Name: ix_phases_history_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_history_status ON public.phases_history USING btree (status);


--
-- Name: ix_phases_history_type_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_history_type_id ON public.phases_history USING btree (type_id);


--
-- Name: ix_phases_history_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_history_updated_time ON public.phases_history USING btree (updated_time);


--
-- Name: ix_phases_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_org_id ON public.phases USING btree (org_id);


--
-- Name: ix_phases_room_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_room_id ON public.phases USING btree (room_id);


--
-- Name: ix_phases_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_site_id ON public.phases USING btree (site_id);


--
-- Name: ix_phases_source_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_source_type ON public.phases USING btree (source_type);


--
-- Name: ix_phases_start_event_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_start_event_id ON public.phases USING btree (start_event_id);


--
-- Name: ix_phases_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_status ON public.phases USING btree (status);


--
-- Name: ix_phases_type_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_type_id ON public.phases USING btree (type_id);


--
-- Name: ix_phases_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_phases_updated_time ON public.phases USING btree (updated_time);


--
-- Name: ix_procedures_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_procedures_created_time ON public.procedures USING btree (created_time);


--
-- Name: ix_procedures_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_procedures_name ON public.procedures USING btree (name);


--
-- Name: ix_procedures_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_procedures_org_id ON public.procedures USING btree (org_id);


--
-- Name: ix_procedures_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_procedures_updated_time ON public.procedures USING btree (updated_time);


--
-- Name: ix_room_closures_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_room_closures_created_time ON public.room_closures USING btree (created_time);


--
-- Name: ix_room_closures_end_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_room_closures_end_time ON public.room_closures USING btree (end_time);


--
-- Name: ix_room_closures_room_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_room_closures_room_id ON public.room_closures USING btree (room_id);


--
-- Name: ix_room_closures_start_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_room_closures_start_time ON public.room_closures USING btree (start_time);


--
-- Name: ix_room_closures_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_room_closures_updated_time ON public.room_closures USING btree (updated_time);


--
-- Name: ix_room_default_camera_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_room_default_camera_created_time ON public.room_default_camera USING btree (created_time);


--
-- Name: ix_room_default_camera_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_room_default_camera_updated_time ON public.room_default_camera USING btree (updated_time);


--
-- Name: ix_room_first_case_configs_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_room_first_case_configs_created_time ON public.room_first_case_configs USING btree (created_time);


--
-- Name: ix_room_first_case_configs_room_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_room_first_case_configs_room_id ON public.room_first_case_configs USING btree (room_id);


--
-- Name: ix_room_first_case_configs_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_room_first_case_configs_updated_time ON public.room_first_case_configs USING btree (updated_time);


--
-- Name: ix_room_prime_time_configs_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_room_prime_time_configs_created_time ON public.room_prime_time_configs USING btree (created_time);


--
-- Name: ix_room_prime_time_configs_room_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_room_prime_time_configs_room_id ON public.room_prime_time_configs USING btree (room_id);


--
-- Name: ix_room_prime_time_configs_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_room_prime_time_configs_updated_time ON public.room_prime_time_configs USING btree (updated_time);


--
-- Name: ix_room_taggings_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_room_taggings_created_time ON public.room_taggings USING btree (created_time);


--
-- Name: ix_room_taggings_room_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_room_taggings_room_id ON public.room_taggings USING btree (room_id);


--
-- Name: ix_room_taggings_tag_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_room_taggings_tag_id ON public.room_taggings USING btree (tag_id);


--
-- Name: ix_room_taggings_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_room_taggings_updated_time ON public.room_taggings USING btree (updated_time);


--
-- Name: ix_room_tags_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_room_tags_created_time ON public.room_tags USING btree (created_time);


--
-- Name: ix_room_tags_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_room_tags_org_id ON public.room_tags USING btree (org_id);


--
-- Name: ix_room_tags_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_room_tags_updated_time ON public.room_tags USING btree (updated_time);


--
-- Name: ix_rooms_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_rooms_created_time ON public.rooms USING btree (created_time);


--
-- Name: ix_rooms_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_rooms_org_id ON public.rooms USING btree (org_id);


--
-- Name: ix_rooms_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_rooms_site_id ON public.rooms USING btree (site_id);


--
-- Name: ix_rooms_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_rooms_updated_time ON public.rooms USING btree (updated_time);


--
-- Name: ix_service_lines_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_service_lines_created_time ON public.service_lines USING btree (created_time);


--
-- Name: ix_service_lines_external_service_line_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_service_lines_external_service_line_id ON public.service_lines USING btree (external_service_line_id);


--
-- Name: ix_service_lines_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_service_lines_name ON public.service_lines USING btree (name);


--
-- Name: ix_service_lines_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_service_lines_org_id ON public.service_lines USING btree (org_id);


--
-- Name: ix_service_lines_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_service_lines_updated_time ON public.service_lines USING btree (updated_time);


--
-- Name: ix_site_capacity_constraints_count; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_capacity_constraints_count ON public.site_capacity_constraints USING btree (count);


--
-- Name: ix_site_capacity_constraints_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_capacity_constraints_created_time ON public.site_capacity_constraints USING btree (created_time);


--
-- Name: ix_site_capacity_constraints_day_of_week; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_capacity_constraints_day_of_week ON public.site_capacity_constraints USING btree (day_of_week);


--
-- Name: ix_site_capacity_constraints_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_capacity_constraints_site_id ON public.site_capacity_constraints USING btree (site_id);


--
-- Name: ix_site_capacity_constraints_start_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_capacity_constraints_start_time ON public.site_capacity_constraints USING btree (start_time);


--
-- Name: ix_site_capacity_constraints_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_capacity_constraints_updated_time ON public.site_capacity_constraints USING btree (updated_time);


--
-- Name: ix_site_closures_closure_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_closures_closure_date ON public.site_closures USING btree (closure_date);


--
-- Name: ix_site_closures_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_closures_created_time ON public.site_closures USING btree (created_time);


--
-- Name: ix_site_closures_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_closures_site_id ON public.site_closures USING btree (site_id);


--
-- Name: ix_site_closures_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_closures_updated_time ON public.site_closures USING btree (updated_time);


--
-- Name: ix_site_first_case_configs_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_first_case_configs_created_time ON public.site_first_case_configs USING btree (created_time);


--
-- Name: ix_site_first_case_configs_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_site_first_case_configs_site_id ON public.site_first_case_configs USING btree (site_id);


--
-- Name: ix_site_first_case_configs_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_first_case_configs_updated_time ON public.site_first_case_configs USING btree (updated_time);


--
-- Name: ix_site_launch_information_actual_launch_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_launch_information_actual_launch_date ON public.site_launch_information USING btree (actual_launch_date);


--
-- Name: ix_site_launch_information_anticipated_launch_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_launch_information_anticipated_launch_date ON public.site_launch_information USING btree (anticipated_launch_date);


--
-- Name: ix_site_launch_information_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_launch_information_created_time ON public.site_launch_information USING btree (created_time);


--
-- Name: ix_site_launch_information_notion_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_launch_information_notion_id ON public.site_launch_information USING btree (notion_id);


--
-- Name: ix_site_launch_information_site_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_launch_information_site_name ON public.site_launch_information USING btree (site_name);


--
-- Name: ix_site_launch_information_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_launch_information_updated_time ON public.site_launch_information USING btree (updated_time);


--
-- Name: ix_site_prime_time_configs_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_prime_time_configs_created_time ON public.site_prime_time_configs USING btree (created_time);


--
-- Name: ix_site_prime_time_configs_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_site_prime_time_configs_site_id ON public.site_prime_time_configs USING btree (site_id);


--
-- Name: ix_site_prime_time_configs_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_site_prime_time_configs_updated_time ON public.site_prime_time_configs USING btree (updated_time);


--
-- Name: ix_sites_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_sites_created_time ON public.sites USING btree (created_time);


--
-- Name: ix_sites_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_sites_org_id ON public.sites USING btree (org_id);


--
-- Name: ix_sites_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_sites_updated_time ON public.sites USING btree (updated_time);


--
-- Name: ix_staff_archived_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_archived_time ON public.staff USING btree (archived_time);


--
-- Name: ix_staff_codes_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_codes_code ON public.staff_codes USING btree (code);


--
-- Name: ix_staff_codes_coding_system; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_codes_coding_system ON public.staff_codes USING btree (coding_system);


--
-- Name: ix_staff_codes_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_codes_created_time ON public.staff_codes USING btree (created_time);


--
-- Name: ix_staff_codes_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_codes_org_id ON public.staff_codes USING btree (org_id);


--
-- Name: ix_staff_codes_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_codes_updated_time ON public.staff_codes USING btree (updated_time);


--
-- Name: ix_staff_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_created_time ON public.staff USING btree (created_time);


--
-- Name: ix_staff_event_notification_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_case_id ON public.staff_event_notification USING btree (case_id);


--
-- Name: ix_staff_event_notification_contact_information_archived_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_contact_information_archived_time ON public.staff_event_notification_contact_information USING btree (archived_time);


--
-- Name: ix_staff_event_notification_contact_information_contact_e4df; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_contact_information_contact_e4df ON public.staff_event_notification_contact_information USING btree (contact_information_id);


--
-- Name: ix_staff_event_notification_contact_information_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_contact_information_created_time ON public.staff_event_notification_contact_information USING btree (created_time);


--
-- Name: ix_staff_event_notification_contact_information_event_type_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_contact_information_event_type_id ON public.staff_event_notification_contact_information USING btree (event_type_id);


--
-- Name: ix_staff_event_notification_contact_information_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_contact_information_id ON public.staff_event_notification_contact_information USING btree (id);


--
-- Name: ix_staff_event_notification_contact_information_staff_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_contact_information_staff_id ON public.staff_event_notification_contact_information USING btree (staff_id);


--
-- Name: ix_staff_event_notification_contact_information_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_contact_information_updated_time ON public.staff_event_notification_contact_information USING btree (updated_time);


--
-- Name: ix_staff_event_notification_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_created_time ON public.staff_event_notification USING btree (created_time);


--
-- Name: ix_staff_event_notification_event_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_event_id ON public.staff_event_notification USING btree (event_id);


--
-- Name: ix_staff_event_notification_event_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_event_time ON public.staff_event_notification USING btree (event_time);


--
-- Name: ix_staff_event_notification_history_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_history_case_id ON public.staff_event_notification_history USING btree (case_id);


--
-- Name: ix_staff_event_notification_history_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_history_created_time ON public.staff_event_notification_history USING btree (created_time);


--
-- Name: ix_staff_event_notification_history_event_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_history_event_id ON public.staff_event_notification_history USING btree (event_id);


--
-- Name: ix_staff_event_notification_history_event_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_history_event_time ON public.staff_event_notification_history USING btree (event_time);


--
-- Name: ix_staff_event_notification_history_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_history_id ON public.staff_event_notification_history USING btree (id);


--
-- Name: ix_staff_event_notification_history_message_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_history_message_id ON public.staff_event_notification_history USING btree (message_id);


--
-- Name: ix_staff_event_notification_history_staff_event_contact_46c7; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_history_staff_event_contact_46c7 ON public.staff_event_notification_history USING btree (staff_event_contact_information_id);


--
-- Name: ix_staff_event_notification_history_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_history_updated_time ON public.staff_event_notification_history USING btree (updated_time);


--
-- Name: ix_staff_event_notification_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_id ON public.staff_event_notification USING btree (id);


--
-- Name: ix_staff_event_notification_message_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_message_id ON public.staff_event_notification USING btree (message_id);


--
-- Name: ix_staff_event_notification_old_01_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_old_01_case_id ON public.staff_event_notification_old_01 USING btree (case_id);


--
-- Name: ix_staff_event_notification_old_01_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_old_01_created_time ON public.staff_event_notification_old_01 USING btree (created_time);


--
-- Name: ix_staff_event_notification_old_01_event_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_old_01_event_id ON public.staff_event_notification_old_01 USING btree (event_id);


--
-- Name: ix_staff_event_notification_old_01_event_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_old_01_event_time ON public.staff_event_notification_old_01 USING btree (event_time);


--
-- Name: ix_staff_event_notification_old_01_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_old_01_id ON public.staff_event_notification_old_01 USING btree (id);


--
-- Name: ix_staff_event_notification_old_01_message_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_old_01_message_id ON public.staff_event_notification_old_01 USING btree (message_id);


--
-- Name: ix_staff_event_notification_old_01_staff_event_contact__44ed; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_old_01_staff_event_contact__44ed ON public.staff_event_notification_old_01 USING btree (staff_event_contact_information_id);


--
-- Name: ix_staff_event_notification_old_01_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_old_01_updated_time ON public.staff_event_notification_old_01 USING btree (updated_time);


--
-- Name: ix_staff_event_notification_staff_event_contact_information_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_staff_event_contact_information_id ON public.staff_event_notification USING btree (staff_event_contact_information_id);


--
-- Name: ix_staff_event_notification_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_event_notification_updated_time ON public.staff_event_notification USING btree (updated_time);


--
-- Name: ix_staff_external_staff_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_external_staff_id ON public.staff USING btree (external_staff_id);


--
-- Name: ix_staff_first_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_first_name ON public.staff USING btree (first_name);


--
-- Name: ix_staff_last_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_last_name ON public.staff USING btree (last_name);


--
-- Name: ix_staff_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_org_id ON public.staff USING btree (org_id);


--
-- Name: ix_staff_role_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_role_created_time ON public.staff_role USING btree (created_time);


--
-- Name: ix_staff_role_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_role_updated_time ON public.staff_role USING btree (updated_time);


--
-- Name: ix_staff_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staff_updated_time ON public.staff USING btree (updated_time);


--
-- Name: ix_staffing_needs_ratio_archived_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staffing_needs_ratio_archived_time ON public.staffing_needs_ratio USING btree (archived_time);


--
-- Name: ix_staffing_needs_ratio_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staffing_needs_ratio_created_time ON public.staffing_needs_ratio USING btree (created_time);


--
-- Name: ix_staffing_needs_ratio_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staffing_needs_ratio_org_id ON public.staffing_needs_ratio USING btree (org_id);


--
-- Name: ix_staffing_needs_ratio_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staffing_needs_ratio_site_id ON public.staffing_needs_ratio USING btree (site_id);


--
-- Name: ix_staffing_needs_ratio_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_staffing_needs_ratio_updated_time ON public.staffing_needs_ratio USING btree (updated_time);


--
-- Name: ix_terminal_clean_scores_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_terminal_clean_scores_created_time ON public.terminal_clean_scores USING btree (created_time);


--
-- Name: ix_terminal_clean_scores_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_terminal_clean_scores_date ON public.terminal_clean_scores USING btree (date);


--
-- Name: ix_terminal_clean_scores_room_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_terminal_clean_scores_room_id ON public.terminal_clean_scores USING btree (room_id);


--
-- Name: ix_terminal_clean_scores_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_terminal_clean_scores_updated_time ON public.terminal_clean_scores USING btree (updated_time);


--
-- Name: ix_turnover_goals_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_turnover_goals_created_time ON public.turnover_goals USING btree (created_time);


--
-- Name: ix_turnover_goals_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_turnover_goals_org_id ON public.turnover_goals USING btree (org_id);


--
-- Name: ix_turnover_goals_site_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_turnover_goals_site_id ON public.turnover_goals USING btree (site_id);


--
-- Name: ix_turnover_goals_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_turnover_goals_updated_time ON public.turnover_goals USING btree (updated_time);


--
-- Name: ix_turnover_label_assoc_archived_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_turnover_label_assoc_archived_time ON public.turnover_label_assoc USING btree (archived_time);


--
-- Name: ix_turnover_label_assoc_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_turnover_label_assoc_created_time ON public.turnover_label_assoc USING btree (created_time);


--
-- Name: ix_turnover_label_assoc_following_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_turnover_label_assoc_following_case_id ON public.turnover_label_assoc USING btree (following_case_id);


--
-- Name: ix_turnover_label_assoc_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_turnover_label_assoc_org_id ON public.turnover_label_assoc USING btree (org_id);


--
-- Name: ix_turnover_label_assoc_preceding_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_turnover_label_assoc_preceding_case_id ON public.turnover_label_assoc USING btree (preceding_case_id);


--
-- Name: ix_turnover_label_assoc_turnover_label_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_turnover_label_assoc_turnover_label_id ON public.turnover_label_assoc USING btree (turnover_label_id);


--
-- Name: ix_turnover_label_assoc_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_turnover_label_assoc_updated_time ON public.turnover_label_assoc USING btree (updated_time);


--
-- Name: ix_turnover_notes_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_turnover_notes_created_time ON public.turnover_notes USING btree (created_time);


--
-- Name: ix_turnover_notes_following_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_turnover_notes_following_case_id ON public.turnover_notes USING btree (following_case_id);


--
-- Name: ix_turnover_notes_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_turnover_notes_org_id ON public.turnover_notes USING btree (org_id);


--
-- Name: ix_turnover_notes_preceding_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_turnover_notes_preceding_case_id ON public.turnover_notes USING btree (preceding_case_id);


--
-- Name: ix_turnover_notes_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_turnover_notes_updated_time ON public.turnover_notes USING btree (updated_time);


--
-- Name: ix_unique_block_release_processed_files; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_unique_block_release_processed_files ON public.block_release_processed_files USING btree (file_name, bucket_name, md5_hash);


--
-- Name: ix_unique_block_schedule_processed_files; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_unique_block_schedule_processed_files ON public.block_schedule_processed_files USING btree (file_name, bucket_name, md5_hash);


--
-- Name: ix_unique_block_site; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_unique_block_site ON public.block_sites USING btree (block_id, site_id);


--
-- Name: ix_unique_option_case_archived; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_unique_option_case_archived ON public.case_label_assoc USING btree (option_id, case_id) WHERE (archived_time IS NULL);


--
-- Name: ix_user_filter_views_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_user_filter_views_created_time ON public.user_filter_views USING btree (created_time);


--
-- Name: ix_user_filter_views_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_user_filter_views_name ON public.user_filter_views USING btree (name);


--
-- Name: ix_user_filter_views_org_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_user_filter_views_org_id ON public.user_filter_views USING btree (org_id);


--
-- Name: ix_user_filter_views_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_user_filter_views_updated_time ON public.user_filter_views USING btree (updated_time);


--
-- Name: ix_user_filter_views_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_user_filter_views_user_id ON public.user_filter_views USING btree (user_id);


--
-- Name: ix_user_room_default_camera_camera_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_user_room_default_camera_camera_id ON public.user_room_default_camera USING btree (camera_id);


--
-- Name: ix_user_room_default_camera_created_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_user_room_default_camera_created_time ON public.user_room_default_camera USING btree (created_time);


--
-- Name: ix_user_room_default_camera_updated_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_user_room_default_camera_updated_time ON public.user_room_default_camera USING btree (updated_time);


--
-- Name: ix_user_room_default_camera_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_user_room_default_camera_user_id ON public.user_room_default_camera USING btree (user_id);


--
-- Name: turnover_label_assoc_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX turnover_label_assoc_index ON public.turnover_label_assoc USING btree (preceding_case_id, following_case_id);


--
-- Name: turnover_notes_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX turnover_notes_index ON public.turnover_notes USING btree (preceding_case_id, following_case_id);


--
-- Name: anesthesias anesthesias_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.anesthesias
    ADD CONSTRAINT anesthesias_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: annotation_task_schedules annotation_task_schedules_annotation_task_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_task_schedules
    ADD CONSTRAINT annotation_task_schedules_annotation_task_type_id_fkey FOREIGN KEY (annotation_task_type_id) REFERENCES public.annotation_task_types(id);


--
-- Name: annotation_task_schedules_rooms annotation_task_schedules_rooms_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_task_schedules_rooms
    ADD CONSTRAINT annotation_task_schedules_rooms_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.rooms(id);


--
-- Name: annotation_task_schedules_rooms annotation_task_schedules_rooms_schedule_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_task_schedules_rooms
    ADD CONSTRAINT annotation_task_schedules_rooms_schedule_id_fkey FOREIGN KEY (schedule_id) REFERENCES public.annotation_task_schedules(id);


--
-- Name: annotation_task_schedules_sites annotation_task_schedules_sites_schedule_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_task_schedules_sites
    ADD CONSTRAINT annotation_task_schedules_sites_schedule_id_fkey FOREIGN KEY (schedule_id) REFERENCES public.annotation_task_schedules(id);


--
-- Name: annotation_task_schedules_sites annotation_task_schedules_sites_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_task_schedules_sites
    ADD CONSTRAINT annotation_task_schedules_sites_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: annotation_task_type_provisional_annotators annotation_task_type_provisional_a_annotation_task_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_task_type_provisional_annotators
    ADD CONSTRAINT annotation_task_type_provisional_a_annotation_task_type_id_fkey FOREIGN KEY (annotation_task_type_id) REFERENCES public.annotation_task_types(id);


--
-- Name: annotation_task_type_reviewers annotation_task_type_reviewers_annotation_task_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_task_type_reviewers
    ADD CONSTRAINT annotation_task_type_reviewers_annotation_task_type_id_fkey FOREIGN KEY (annotation_task_type_id) REFERENCES public.annotation_task_types(id);


--
-- Name: annotation_task_type_annotators annotation_task_type_users_annotation_task_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_task_type_annotators
    ADD CONSTRAINT annotation_task_type_users_annotation_task_type_id_fkey FOREIGN KEY (annotation_task_type_id) REFERENCES public.annotation_task_types(id);


--
-- Name: annotation_tasks annotation_tasks_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_tasks
    ADD CONSTRAINT annotation_tasks_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: annotation_tasks annotation_tasks_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_tasks
    ADD CONSTRAINT annotation_tasks_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.rooms(id);


--
-- Name: annotation_tasks annotation_tasks_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_tasks
    ADD CONSTRAINT annotation_tasks_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: annotation_tasks annotation_tasks_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_tasks
    ADD CONSTRAINT annotation_tasks_type_id_fkey FOREIGN KEY (type_id) REFERENCES public.annotation_task_types(id);


--
-- Name: annotation_tasks annotation_tasks_type_id_type_version_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.annotation_tasks
    ADD CONSTRAINT annotation_tasks_type_id_type_version_fkey FOREIGN KEY (type_id, type_version) REFERENCES public.annotation_task_types_history(id, version);


--
-- Name: block_release_file_rows block_release_file_rows_block_release_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_release_file_rows
    ADD CONSTRAINT block_release_file_rows_block_release_file_id_fkey FOREIGN KEY (block_release_file_id) REFERENCES public.block_release_processed_files(id) ON DELETE CASCADE;


--
-- Name: block_release_processed_files block_release_processed_files_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_release_processed_files
    ADD CONSTRAINT block_release_processed_files_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: block_schedule_file_rows block_schedule_file_rows_block_schedule_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_schedule_file_rows
    ADD CONSTRAINT block_schedule_file_rows_block_schedule_file_id_fkey FOREIGN KEY (block_schedule_file_id) REFERENCES public.block_schedule_processed_files(id) ON DELETE CASCADE;


--
-- Name: block_schedule_processed_files block_schedule_processed_files_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_schedule_processed_files
    ADD CONSTRAINT block_schedule_processed_files_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: block_sites block_sites_block_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_sites
    ADD CONSTRAINT block_sites_block_id_fkey FOREIGN KEY (block_id) REFERENCES public.blocks(id);


--
-- Name: block_sites block_sites_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_sites
    ADD CONSTRAINT block_sites_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: block_surgeons block_surgeons_block_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_surgeons
    ADD CONSTRAINT block_surgeons_block_id_fkey FOREIGN KEY (block_id) REFERENCES public.blocks(id);


--
-- Name: block_surgeons block_surgeons_staff_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_surgeons
    ADD CONSTRAINT block_surgeons_staff_id_fkey FOREIGN KEY (staff_id) REFERENCES public.staff(id);


--
-- Name: block_time_overrides block_time_overrides_block_time_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_time_overrides
    ADD CONSTRAINT block_time_overrides_block_time_id_fkey FOREIGN KEY (block_time_id) REFERENCES public.block_times(id);


--
-- Name: block_time_releases block_time_release_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_time_releases
    ADD CONSTRAINT block_time_release_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.rooms(id);


--
-- Name: block_time_releases block_time_release_to_block_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_time_releases
    ADD CONSTRAINT block_time_release_to_block_id_fkey FOREIGN KEY (to_block_id) REFERENCES public.blocks(id) ON DELETE SET NULL;


--
-- Name: block_time_releases block_time_releases_block_time_id_on_delete_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_time_releases
    ADD CONSTRAINT block_time_releases_block_time_id_on_delete_fkey FOREIGN KEY (block_time_id) REFERENCES public.block_times(id) ON DELETE CASCADE;


--
-- Name: block_times block_times_block_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_times
    ADD CONSTRAINT block_times_block_id_fkey FOREIGN KEY (block_id) REFERENCES public.blocks(id);


--
-- Name: block_times block_times_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_times
    ADD CONSTRAINT block_times_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.rooms(id);


--
-- Name: blocks blocks_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.blocks
    ADD CONSTRAINT blocks_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: board_config board_config_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.board_config
    ADD CONSTRAINT board_config_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: board_config board_config_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.board_config
    ADD CONSTRAINT board_config_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: board_room board_room_board_config_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.board_room
    ADD CONSTRAINT board_room_board_config_id_fkey FOREIGN KEY (board_config_id) REFERENCES public.board_config(id);


--
-- Name: board_room board_room_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.board_room
    ADD CONSTRAINT board_room_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.rooms(id);


--
-- Name: cameras cameras_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cameras
    ADD CONSTRAINT cameras_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: cameras cameras_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cameras
    ADD CONSTRAINT cameras_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.rooms(id);


--
-- Name: cameras cameras_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cameras
    ADD CONSTRAINT cameras_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: case_derived_properties case_derived_properties_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_derived_properties
    ADD CONSTRAINT case_derived_properties_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.cases(case_id);


--
-- Name: case_flag case_flag_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_flag
    ADD CONSTRAINT case_flag_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.cases(case_id) ON DELETE CASCADE;


--
-- Name: case_flag case_flag_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_flag
    ADD CONSTRAINT case_flag_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: case_flag case_flag_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_flag
    ADD CONSTRAINT case_flag_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: case_forecast case_forecast_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_forecast
    ADD CONSTRAINT case_forecast_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.cases(case_id) ON DELETE CASCADE;


--
-- Name: case_forecast case_forecast_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_forecast
    ADD CONSTRAINT case_forecast_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: case_forecast case_forecast_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_forecast
    ADD CONSTRAINT case_forecast_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.rooms(id);


--
-- Name: case_forecast case_forecast_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_forecast
    ADD CONSTRAINT case_forecast_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: case_label_assoc case_label_assoc_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_label_assoc
    ADD CONSTRAINT case_label_assoc_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.cases(case_id);


--
-- Name: case_label_assoc case_label_assoc_option_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_label_assoc
    ADD CONSTRAINT case_label_assoc_option_id_fkey FOREIGN KEY (option_id) REFERENCES public.case_label_field_options(id);


--
-- Name: case_label_categories case_label_categories_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_label_categories
    ADD CONSTRAINT case_label_categories_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: case_label_categories case_label_categories_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_label_categories
    ADD CONSTRAINT case_label_categories_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: case_label_field_options case_label_field_options_field_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_label_field_options
    ADD CONSTRAINT case_label_field_options_field_id_fkey FOREIGN KEY (field_id) REFERENCES public.case_label_fields(id);


--
-- Name: case_label_fields case_label_fields_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_label_fields
    ADD CONSTRAINT case_label_fields_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.case_label_categories(id);


--
-- Name: case_label_fields case_label_fields_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_label_fields
    ADD CONSTRAINT case_label_fields_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: case_label_fields case_label_fields_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_label_fields
    ADD CONSTRAINT case_label_fields_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: case_matching_status_reason case_matching_status_reason_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_matching_status_reason
    ADD CONSTRAINT case_matching_status_reason_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.cases(case_id) ON DELETE CASCADE;


--
-- Name: case_note_plan case_note_plan_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_note_plan
    ADD CONSTRAINT case_note_plan_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.cases(case_id);


--
-- Name: case_note_plan case_note_plan_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_note_plan
    ADD CONSTRAINT case_note_plan_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: case_note_plan case_note_plan_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_note_plan
    ADD CONSTRAINT case_note_plan_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: case_procedures case_procedures_anesthesia_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_procedures
    ADD CONSTRAINT case_procedures_anesthesia_id_fkey FOREIGN KEY (anesthesia_id) REFERENCES public.anesthesias(id);


--
-- Name: case_procedures case_procedures_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_procedures
    ADD CONSTRAINT case_procedures_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.cases(case_id);


--
-- Name: case_procedures case_procedures_procedure_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_procedures
    ADD CONSTRAINT case_procedures_procedure_id_fkey FOREIGN KEY (procedure_id) REFERENCES public.procedures(id);


--
-- Name: case_raw case_raw_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_raw
    ADD CONSTRAINT case_raw_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.cases(case_id);


--
-- Name: case_raw case_raw_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_raw
    ADD CONSTRAINT case_raw_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: case_staff case_staff_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_staff
    ADD CONSTRAINT case_staff_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.cases(case_id);


--
-- Name: case_staff_plan case_staff_plan_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_staff_plan
    ADD CONSTRAINT case_staff_plan_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.cases(case_id);


--
-- Name: case_staff_plan case_staff_plan_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_staff_plan
    ADD CONSTRAINT case_staff_plan_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: case_staff_plan case_staff_plan_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_staff_plan
    ADD CONSTRAINT case_staff_plan_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: case_staff_plan case_staff_plan_staff_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_staff_plan
    ADD CONSTRAINT case_staff_plan_staff_id_fkey FOREIGN KEY (staff_id) REFERENCES public.staff(id);


--
-- Name: case_staff case_staff_staff_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_staff
    ADD CONSTRAINT case_staff_staff_id_fkey FOREIGN KEY (staff_id) REFERENCES public.staff(id);


--
-- Name: cases cases_case_classification_types_id_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cases
    ADD CONSTRAINT cases_case_classification_types_id_org_id_fkey FOREIGN KEY (case_classification_types_id, org_id) REFERENCES public.case_classification_types(id, org_id);


--
-- Name: cases cases_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cases
    ADD CONSTRAINT cases_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: cases cases_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cases
    ADD CONSTRAINT cases_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.rooms(id);


--
-- Name: cases cases_service_line_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cases
    ADD CONSTRAINT cases_service_line_id_fkey FOREIGN KEY (service_line_id) REFERENCES public.service_lines(id);


--
-- Name: cases cases_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cases
    ADD CONSTRAINT cases_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: cluster_mapping cluster_mapping_cluster_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cluster_mapping
    ADD CONSTRAINT cluster_mapping_cluster_id_fkey FOREIGN KEY (cluster_id) REFERENCES public.cluster(id);


--
-- Name: cluster_mapping cluster_mapping_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cluster_mapping
    ADD CONSTRAINT cluster_mapping_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: custom_phase_configs custom_phase_configs_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.custom_phase_configs
    ADD CONSTRAINT custom_phase_configs_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: event_dashboard_visibility event_dashboard_visibility_event_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.event_dashboard_visibility
    ADD CONSTRAINT event_dashboard_visibility_event_type_id_fkey FOREIGN KEY (event_type_id) REFERENCES public.event_types(id);


--
-- Name: events events_camera_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT events_camera_id_fkey FOREIGN KEY (camera_id) REFERENCES public.cameras(id);


--
-- Name: events events_event_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT events_event_type_id_fkey FOREIGN KEY (event_type_id) REFERENCES public.event_types(id);


--
-- Name: events events_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT events_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: events events_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT events_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.rooms(id);


--
-- Name: events events_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT events_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: site_first_case_configs first_case_configs_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.site_first_case_configs
    ADD CONSTRAINT first_case_configs_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id) ON DELETE CASCADE;


--
-- Name: block_times fk_block_times_released_from; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.block_times
    ADD CONSTRAINT fk_block_times_released_from FOREIGN KEY (released_from) REFERENCES public.block_times(id) ON DELETE SET NULL;


--
-- Name: highlight_assets highlight_assets_highlight_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.highlight_assets
    ADD CONSTRAINT highlight_assets_highlight_id_fkey FOREIGN KEY (highlight_id) REFERENCES public.highlights(id);


--
-- Name: highlight_feedback highlight_feedback_highlight_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.highlight_feedback
    ADD CONSTRAINT highlight_feedback_highlight_id_fkey FOREIGN KEY (highlight_id) REFERENCES public.highlights(id);


--
-- Name: highlight_feedback highlight_feedback_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.highlight_feedback
    ADD CONSTRAINT highlight_feedback_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: highlight_users highlight_users_highlight_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.highlight_users
    ADD CONSTRAINT highlight_users_highlight_id_fkey FOREIGN KEY (highlight_id) REFERENCES public.highlights(id);


--
-- Name: highlights highlights_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.highlights
    ADD CONSTRAINT highlights_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: highlights highlights_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.highlights
    ADD CONSTRAINT highlights_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.rooms(id);


--
-- Name: highlights highlights_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.highlights
    ADD CONSTRAINT highlights_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: measurement_periods measurement_periods_annotation_task_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.measurement_periods
    ADD CONSTRAINT measurement_periods_annotation_task_type_id_fkey FOREIGN KEY (annotation_task_type_id) REFERENCES public.annotation_task_types(id);


--
-- Name: measurement_periods measurement_periods_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.measurement_periods
    ADD CONSTRAINT measurement_periods_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: staff_event_notification notification_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff_event_notification
    ADD CONSTRAINT notification_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.cases(case_id) ON DELETE SET NULL;


--
-- Name: staff_event_notification_old_01 notification_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff_event_notification_old_01
    ADD CONSTRAINT notification_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.cases(case_id) ON DELETE SET NULL;


--
-- Name: staff_event_notification notification_duplicated_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff_event_notification
    ADD CONSTRAINT notification_duplicated_id_fkey FOREIGN KEY (duplicated_id) REFERENCES public.staff_event_notification(id);


--
-- Name: observation_type_names observation_type_names_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.observation_type_names
    ADD CONSTRAINT observation_type_names_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: observation_type_names observation_type_names_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.observation_type_names
    ADD CONSTRAINT observation_type_names_type_id_fkey FOREIGN KEY (type_id) REFERENCES public.observation_types(id);


--
-- Name: observations observations_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.observations
    ADD CONSTRAINT observations_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.cases(case_id);


--
-- Name: observations observations_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.observations
    ADD CONSTRAINT observations_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: observations observations_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.observations
    ADD CONSTRAINT observations_type_id_fkey FOREIGN KEY (type_id) REFERENCES public.observation_types(id);


--
-- Name: phase_relationships phase_relationships_child_phase_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.phase_relationships
    ADD CONSTRAINT phase_relationships_child_phase_id_fkey FOREIGN KEY (child_phase_id) REFERENCES public.phases(id) ON DELETE CASCADE;


--
-- Name: phase_relationships phase_relationships_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.phase_relationships
    ADD CONSTRAINT phase_relationships_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: phase_relationships phase_relationships_parent_phase_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.phase_relationships
    ADD CONSTRAINT phase_relationships_parent_phase_id_fkey FOREIGN KEY (parent_phase_id) REFERENCES public.phases(id) ON DELETE CASCADE;


--
-- Name: phases phases_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.phases
    ADD CONSTRAINT phases_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.cases(case_id);


--
-- Name: phases phases_end_event_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.phases
    ADD CONSTRAINT phases_end_event_id_fkey FOREIGN KEY (end_event_id) REFERENCES public.events(id) ON DELETE CASCADE;


--
-- Name: phases phases_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.phases
    ADD CONSTRAINT phases_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: phases phases_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.phases
    ADD CONSTRAINT phases_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.rooms(id);


--
-- Name: phases phases_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.phases
    ADD CONSTRAINT phases_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: phases phases_start_event_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.phases
    ADD CONSTRAINT phases_start_event_id_fkey FOREIGN KEY (start_event_id) REFERENCES public.events(id) ON DELETE CASCADE;


--
-- Name: phases phases_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.phases
    ADD CONSTRAINT phases_type_id_fkey FOREIGN KEY (type_id) REFERENCES public.phase_types(id);


--
-- Name: procedures procedures_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.procedures
    ADD CONSTRAINT procedures_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: room_closures room_closures_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.room_closures
    ADD CONSTRAINT room_closures_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.rooms(id) ON DELETE CASCADE;


--
-- Name: room_default_camera room_default_camera_camera_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.room_default_camera
    ADD CONSTRAINT room_default_camera_camera_id_fkey FOREIGN KEY (camera_id) REFERENCES public.cameras(id) ON DELETE CASCADE;


--
-- Name: room_default_camera room_default_camera_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.room_default_camera
    ADD CONSTRAINT room_default_camera_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.rooms(id) ON DELETE CASCADE;


--
-- Name: room_first_case_configs room_first_case_configs_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.room_first_case_configs
    ADD CONSTRAINT room_first_case_configs_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.rooms(id) ON DELETE CASCADE;


--
-- Name: room_prime_time_configs room_prime_time_configs_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.room_prime_time_configs
    ADD CONSTRAINT room_prime_time_configs_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.rooms(id) ON DELETE CASCADE;


--
-- Name: room_taggings room_taggings_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.room_taggings
    ADD CONSTRAINT room_taggings_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.rooms(id) ON DELETE CASCADE;


--
-- Name: room_taggings room_taggings_tag_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.room_taggings
    ADD CONSTRAINT room_taggings_tag_id_fkey FOREIGN KEY (tag_id) REFERENCES public.room_tags(id) ON DELETE CASCADE;


--
-- Name: room_tags room_tags_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.room_tags
    ADD CONSTRAINT room_tags_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: rooms rooms_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.rooms
    ADD CONSTRAINT rooms_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: rooms rooms_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.rooms
    ADD CONSTRAINT rooms_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: service_lines service_lines_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.service_lines
    ADD CONSTRAINT service_lines_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: site_capacity_constraints site_capacity_constraints_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.site_capacity_constraints
    ADD CONSTRAINT site_capacity_constraints_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id) ON DELETE CASCADE;


--
-- Name: site_closures site_closures_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.site_closures
    ADD CONSTRAINT site_closures_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id) ON DELETE CASCADE;


--
-- Name: site_launch_information site_launch_information_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.site_launch_information
    ADD CONSTRAINT site_launch_information_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: site_prime_time_configs site_prime_time_configs_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.site_prime_time_configs
    ADD CONSTRAINT site_prime_time_configs_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id) ON DELETE CASCADE;


--
-- Name: sites sites_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.sites
    ADD CONSTRAINT sites_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: staff_codes staff_codes_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff_codes
    ADD CONSTRAINT staff_codes_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: staff_codes staff_codes_staff_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff_codes
    ADD CONSTRAINT staff_codes_staff_id_fkey FOREIGN KEY (staff_id) REFERENCES public.staff(id);


--
-- Name: staff_event_notification_contact_information staff_event_notification_contact_in_contact_information_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff_event_notification_contact_information
    ADD CONSTRAINT staff_event_notification_contact_in_contact_information_id_fkey FOREIGN KEY (contact_information_id) REFERENCES public.contact_information(id) ON DELETE CASCADE;


--
-- Name: staff_event_notification_contact_information staff_event_notification_contact_information_staff_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff_event_notification_contact_information
    ADD CONSTRAINT staff_event_notification_contact_information_staff_id_fkey FOREIGN KEY (staff_id) REFERENCES public.staff(id) ON DELETE CASCADE;


--
-- Name: staff_event_notification_old_01 staff_event_notification_old__staff_event_contact_informat_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff_event_notification_old_01
    ADD CONSTRAINT staff_event_notification_old__staff_event_contact_informat_fkey FOREIGN KEY (staff_event_contact_information_id) REFERENCES public.staff_event_notification_contact_information(id) ON DELETE CASCADE;


--
-- Name: staff_event_notification staff_event_notification_staff_event_contact_information_i_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff_event_notification
    ADD CONSTRAINT staff_event_notification_staff_event_contact_information_i_fkey FOREIGN KEY (staff_event_contact_information_id) REFERENCES public.staff_event_notification_contact_information(id) ON DELETE CASCADE;


--
-- Name: staff staff_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staff
    ADD CONSTRAINT staff_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: staffing_needs_ratio staffing_needs_ratio_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staffing_needs_ratio
    ADD CONSTRAINT staffing_needs_ratio_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: staffing_needs_ratio staffing_needs_ratio_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staffing_needs_ratio
    ADD CONSTRAINT staffing_needs_ratio_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: staffing_needs_ratio staffing_needs_ratio_staff_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.staffing_needs_ratio
    ADD CONSTRAINT staffing_needs_ratio_staff_role_id_fkey FOREIGN KEY (staff_role_id) REFERENCES public.staff_role(id);


--
-- Name: terminal_clean_scores terminal_clean_scores_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.terminal_clean_scores
    ADD CONSTRAINT terminal_clean_scores_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.rooms(id) ON DELETE CASCADE;


--
-- Name: turnover_goals turnover_goals_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.turnover_goals
    ADD CONSTRAINT turnover_goals_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: turnover_goals turnover_goals_site_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.turnover_goals
    ADD CONSTRAINT turnover_goals_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.sites(id);


--
-- Name: turnover_label_assoc turnover_label_assoc_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.turnover_label_assoc
    ADD CONSTRAINT turnover_label_assoc_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: turnover_label_assoc turnover_label_assoc_turnover_label_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.turnover_label_assoc
    ADD CONSTRAINT turnover_label_assoc_turnover_label_id_fkey FOREIGN KEY (turnover_label_id) REFERENCES public.turnover_labels(id);


--
-- Name: turnover_notes turnover_notes_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.turnover_notes
    ADD CONSTRAINT turnover_notes_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: user_filter_views user_filter_views_org_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_filter_views
    ADD CONSTRAINT user_filter_views_org_id_fkey FOREIGN KEY (org_id) REFERENCES public.organizations(id);


--
-- Name: user_room_default_camera user_room_default_camera_camera_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_room_default_camera
    ADD CONSTRAINT user_room_default_camera_camera_id_fkey FOREIGN KEY (camera_id) REFERENCES public.cameras(id) ON DELETE CASCADE;


--
-- Name: user_room_default_camera user_room_default_camera_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_room_default_camera
    ADD CONSTRAINT user_room_default_camera_room_id_fkey FOREIGN KEY (room_id) REFERENCES public.rooms(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--

