from typing import Any, TypeVar, Callable

from mypy_extensions import <PERSON>w<PERSON><PERSON>, Var<PERSON>rg

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.util import greenlet_spawn

from databases.sql import new_async_session

_T = TypeVar("_T", bound=Any)


def async_db_session_provider(
    function_call: Callable[..., _T],
) -> Callable[[VarArg(Any), KwArg(Any)], Any]:
    """
    !!!!!!!!!!!!!!
    CAREFUL ON USING THIS! THIS SHOULD ONLY BE USED WHEN REFACTORING LEGACY CODE.
    !!!!!!!!!!!!!!
    https://docs.sqlalchemy.org/en/20/orm/extensions/asyncio.html#running-synchronous-methods-and-functions-under-asyncio
    Provides a wrapper that injects an asynchronous session. Use this function to decorate any legacy function which
    used a sync function and needed to be converted and needs a session passed in. Functions may need a session passed in
    if we are starting a async session context manager outside of function and cannot be started in the function itself.
    """

    async def session_provider(*args: Any, **kwargs: Any) -> Any:
        if "session" in kwargs:
            return await function_call(*args, **kwargs)
        async with new_async_session() as session:
            kwargs["session"] = session
            return await function_call(*args, **kwargs)

    return session_provider


async def component_test_db_async_session_helper(
    function_call: Callable[..., _T],
    async_session: AsyncSession,
    *args: Any,
    **kwargs: Any,
) -> _T:
    """
    Given an outside async session, run a
    """
    kwargs["session"] = async_session.sync_session
    return await greenlet_spawn(function_call, *args, **kwargs)
