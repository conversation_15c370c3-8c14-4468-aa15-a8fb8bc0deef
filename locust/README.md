# Load Testing

You can run load tests with [locust](https://docs.locust.io/en/stable/what-is-locust.html) using the apella_cloud_api client.


## Local load testing

You can load test dev or a local server by running locust locally.

Follow the Apella CLI login mechanism to get a token with your permissions.
Make sure your account has appropriate auth0 permissions to execute the tests.
You may need to add `PYTHONPATH=.` if you have not set environment PYTHONPATH.
```
PYTHONPATH=. poetry run python apella_cli login --environment dev
```

You may need to set `PYTHONPATH=.` or w/e your working directory is if not in `cloud-api-server`

Then you can execute the basic test using locust

```
poetry run locust -f locust/tests/basic.py -H https://api.dev.customer.apella.io
```


Tests can also be run in headless mode without the need for the web browser.

Example running with 20 users and 3 minutes
```
poetry run locust -f locust/tests/basic.py -H https://api.dev.customer.apella.io --headless -u 20 -t 3m
```

See poetry documentation and you can run the following for help:

```
poetry run locust --help
```

## Recording tests

Using [har2locust](https://github.com/SvenskaSpel/har2locust) you can convert HAR files into locust tests.

Record a HAR file using a web browser or http proxy tool. You may want to remove third party requests and you can do this with a provided script.

In this example, the downloaded har files are saved with the prefix `internal.dev.apella.io`

```
poetry run python locust/scripts/har_har.py "locust/recordings/internal-*har" --filter-domain api.dev.apella.io --out-file locust/recordings/testing.har
```

Run script against the downloaded har file.

```
poetry run har2locust --loglevel DEBUG -t locust/templates/har2locust/apellalocust.jinja2  locust/recordings/testing.har > locust/tests/testing.py
```

Run the test

```
poetry run locust -f locust/my_har_test.py
```

The HAR will contain your auth token and you do not need to authenticate. You should also not commit this code and it should be run for creating a scenario and executing it as a load test.

# Auth

You can use a recorded auth token but it has an expiration time. Same with a device token authorized by a user. However, if you use the apella jinja template it will attempt to send updated auth headers.


# Next steps

## Executing contract or component tests as load tests

We should consider adding SLOs to endpoints owned by different teams. Once we have SLOs we can make sure those are met during CI and CD.

Additionally, the component tests contain a certain set of contracts for those endpoints. Making those component tests more composable and reusable throughout the CI and CD process would be a great goal. It will allow us to get quicker feedback on failed contracts in multiple levels of the CI/CD process. The tests can be packaged with the apella cloud api client and be executed in load tests but other tests scenarios.


## Using Kubernetes to execute load tests

We may want a varied and higher user count for load tests. We also would like to add a load testing feature to our CI/CD workflows. Locust can be deployed and run in Github actions but prefered would be to do it in Kubernetes.

This will require a distrubuted setup. [k8s-sandbox has a branch](https://github.com/Apella-Technology/k8s-sandbox/compare/main...PS-1085-locust-application) for a POC on what this may look like.



