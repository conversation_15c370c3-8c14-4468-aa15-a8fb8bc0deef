#!/usr/bin/env python
#
# Merge and manipulate har files
#
#
import contextlib
import glob
import json
import os
import sys


from io import TextIOWrapper
from typing import Generator, Optional

import click


@contextlib.contextmanager
def open_fh_or_stdout(filename: Optional[str] = None) -> Generator[TextIOWrapper, None, None]:
    if filename and filename != "-":
        fh = open(filename, "w")
    else:
        fh = sys.stdout  # type: ignore [assignment]

    try:
        yield fh
    finally:
        if fh is not sys.stdout:
            fh.close()


@click.command()
@click.argument("har_path")
@click.option("--filter-domain", default=None, help="Only include entries to this domain")
@click.option("--out-file", default=None, help="Output file instead of stdout")
def har_har(
    har_path: str, filter_domain: Optional[str] = None, out_file: Optional[str] = None
) -> None:
    merged_har_log = {
        "version": "1.1",
        "creator": {
            "name": "har_har",
            "version": "0.1",
        },
        "pages": [],
        "entries": [],
    }
    if os.path.isdir(har_path):
        har_path = os.path.join(har_path, "*.har")

    for fn in glob.glob(har_path, recursive=True):
        with open(fn) as har_fn:
            har_log = json.load(har_fn).get("log", {})
            merged_har_log["browser"] = har_log.get("browser")
            new_entries = []
            for entry in har_log.get("entries", []):
                if filter_domain is not None:
                    if entry["request"]["url"].startswith(f"https://{filter_domain}"):
                        new_entries.append(entry)
                else:
                    new_entries.append(entry)
            merged_har_log["entries"].extend(new_entries)  # type: ignore [attr-defined]
            merged_har_log["pages"].extend(har_log.get("pages", []))  # type: ignore [attr-defined]
    with open_fh_or_stdout(out_file) as fh:
        json.dump({"log": merged_har_log}, fh)


if __name__ == "__main__":
    har_har()
