import multiprocessing
from typing import Optional
from fastapi import FastAPI

from api_server.logging import configure_logger
from api_server.server.app_creator import get_fast_api_app
from api_server.server.server_process_manager import (
    ApellaAppProcessManager,
    start_standalone_apella_app,
)
import config
from api_server.services.metrics import start_prometheus_server_for_multiprocess_preload
from config import RunMode
from notification_daemon.notification_processor import NotificationProcessor
from notion_daemon.notion_processor import NotionProcessor


class AppManager:
    def __init__(self) -> None:
        self._app: Optional[FastAPI] = None

    @property
    def app(self) -> Optional[FastAPI]:
        """
        Lazy initialization of FastAPI app.
        Will not run in ASGI worker mode.
        """
        if self._app is None and config.run_mode() == RunMode.DEV_MODE_WEB_SERVER:
            configure_logger(multiprocessing.Lock())
            self._app = get_fast_api_app(__name__)
        return self._app

    def run_server(self) -> None:
        """Initialize and run the server based on configured mode"""
        # initialize the prometheus metrics server
        start_prometheus_server_for_multiprocess_preload(config.prometheus_port())
        run_mode = config.run_mode()
        match run_mode:
            case RunMode.DEV_MODE_WEB_SERVER:
                fast_api_app = self.app
                if fast_api_app is None:
                    raise RuntimeError("Unable to initialize FastAPI app in development mode")
                start_standalone_apella_app(app=fast_api_app)
            case RunMode.WEB_SERVER:
                app_process_manager = ApellaAppProcessManager(
                    __name__,
                    config.web_server_worker_count(),
                )
                app_process_manager.start_server()

            case RunMode.NOTIFICATION_DAEMON:
                notification_processor = NotificationProcessor()
                notification_processor.start_processor()

            case RunMode.NOTION_DAEMON:
                notion_processor = NotionProcessor()
                notion_processor.start_processor()

            case _:
                raise ValueError(f"Run mode {run_mode} not recognized")


# Create singleton instance
app_manager = AppManager()

# FastAPI will use this when run from CLI
app = app_manager.app

if __name__ == "__main__":
    app_manager.run_server()
