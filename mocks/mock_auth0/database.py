# This is the auth0 database with some example data
from dataclasses import dataclass, field
from typing import Dict, List, Optional, TypedDict

from api_server.services.users.user_store import User as ApiUser
from auth import AuthOrganization, AuthRole, AuthUser


class User(TypedDict):
    user: AuthUser
    permissions: List[str]
    roles: List[str]


class UserRole(TypedDict):
    user_id: str
    role_id: str


class Organization(TypedDict):
    org: AuthOrganization
    user_roles: List[UserRole]


@dataclass
class Database:
    # All permissions are implicitly for this resource_server_identifier, which is set after the API
    # server is created. Conceptually, each permission should have its own
    # resource_server_identifier set.
    resource_server_identifier: Optional[str] = None

    users: Dict[str, User] = field(default_factory=dict)
    roles: Dict[str, AuthRole] = field(default_factory=dict)
    organizations: Dict[str, Organization] = field(default_factory=dict)

    def add_user(
        self,
        user: ApiUser,
        role_ids: Optional[List[str]] = None,
        permissions: Optional[List[str]] = None,
    ) -> None:
        permissions = permissions or []
        role_ids = role_ids or []

        self.users[user.user_id] = {
            "user": AuthUser(
                user_id=user.user_id, display_name=user.display_name, email=user.email
            ),
            "permissions": permissions,
            "roles": role_ids,
        }

    def add_role(self, role: AuthRole) -> None:
        self.roles[role.id] = role

    def add_organization(self, organization: AuthOrganization) -> None:
        self.organizations[organization.id] = {
            "org": AuthOrganization(
                id=organization.id,
                name=organization.name,
                display_name=organization.display_name,
            ),
            "user_roles": [],
        }

    def grant_user_role_in_org(self, user_id: str, org_id: str, role_id: str) -> None:
        self.organizations[org_id]["user_roles"].append({"user_id": user_id, "role_id": role_id})

    def clear(self) -> None:
        self.resource_server_identifier = None
        self.users = {}
        self.roles = {}
        self.organizations = {}
