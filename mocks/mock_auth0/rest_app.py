import threading
import time
import traceback
from contextlib import asynccontextmanager
from http import HTT<PERSON>tatus
from typing import AsyncGenerator

from fastapi import <PERSON><PERSON><PERSON>
from jose import jwt
from starlette.requests import Request
from starlette.responses import JSONResponse, Response
from auth import AuthUser
from mocks.mock_auth0.database import Database
from mocks.mock_auth0.jwt_tokens import mock_auth0_public_key_jwk
from mocks.util.service_util import MockAppThread
from utils.net import allocate_port, wait_for_port


class MockAuth0:
    def __init__(self, mock_auth0_database: Database):
        self.database = mock_auth0_database
        self.resource_server_port = None

    @asynccontextmanager
    async def serving(self, port: int = 0) -> AsyncGenerator[int, None]:
        if port == 0:
            port = allocate_port()
        shutdown_event = threading.Event()
        auth0_app_thread = MockAppThread(create_app, self.database, port, shutdown_event)
        auth0_app_thread.start()
        wait_for_port("127.0.0.1", port)
        try:
            yield port
        finally:
            shutdown_event.set()
            auth0_app_thread.join()


def create_app(db: Database) -> FastAPI:
    """
    Use the application factory pattern to de-globalize the
    application and thereby allow the deglobalization of the in-memory database.
    """
    app = FastAPI()

    @app.exception_handler(Exception)
    async def any_exception(e: Exception) -> Response:
        # We don't want to throw any exceptions in the mock server
        stacktrace = "".join(
            traceback.format_exception(type(e), value=e, tb=e.__traceback__, limit=None, chain=True)
        )
        return Response(content=stacktrace, status_code=HTTPStatus.INTERNAL_SERVER_ERROR)

    @app.post("/oauth/token")
    async def get_oauth(request: Request) -> JSONResponse:
        # This is the oauth endpoint, so we generate a jwt token
        token = jwt.encode(
            {
                "iss": f"{request.url.hostname}/",
                "exp": round(time.time()) + 24 * 60 * 60,  # 1 day
            },
            "secret",
            algorithm="HS256",
        )
        return JSONResponse(content={"access_token": token})

    @app.post("/api/v2/users")
    async def post_user(request: Request) -> JSONResponse:
        user = await request.json()
        user_id = f"auth0|{time.time()}"
        auth_user = AuthUser(user_id=user_id, email=user["email"], display_name=user["name"])
        db.users[user_id] = {"user": auth_user, "roles": [], "permissions": []}
        return JSONResponse(content=auth_user, status_code=HTTPStatus.CREATED)

    @app.get("/api/v2/users/{user_id}")
    async def get_user(user_id: str) -> Response:
        return await _user_operation(user_id=user_id)

    @app.put("/api/v2/users/{user_id}")
    async def put_user(user_id: str) -> Response:
        return await _user_operation(user_id=user_id)

    async def _user_operation(user_id: str) -> Response:
        user_entry = db.users.get(user_id)
        if not user_entry:
            return Response(content="Not Found", status_code=HTTPStatus.NOT_FOUND)
        auth_user = user_entry["user"]
        return JSONResponse(
            content={
                "user_id": auth_user.user_id,
                "name": auth_user.display_name,
                "email": auth_user.email,
            }
        )

    @app.get("/api/v2/users/{user_id}/permissions")
    async def get_user_permissions(user_id: str) -> JSONResponse:
        permissions = list(db.users[user_id]["permissions"])
        for role_id in db.users[user_id]["roles"]:
            for permission in db.roles[role_id].permissions:
                permissions.append(permission)

        assert db.resource_server_identifier is not None, (
            "Must set resource server port prior to requesting permissions"
        )
        result = [
            {
                "resource_server_identifier": db.resource_server_identifier,
                "permission_name": permission,
            }
            for permission in permissions
        ]
        return JSONResponse(content=result)

    @app.post("/api/v2/users/{user_id}/roles")
    async def add_user_role(user_id: str, request: Request) -> Response:
        user = db.users[user_id]
        roles = (await request.json())["roles"]
        for role in roles:
            user["roles"].append(role)
        return Response(content="OK", status_code=HTTPStatus.NO_CONTENT)

    @app.get("/api/v2/users-by-email")
    async def get_users_by_email(request: Request) -> JSONResponse:
        email = request.query_params.get("email")
        result = []
        if email:
            for db_user in db.users.values():
                user = db_user["user"]
                if email == user.email:
                    result.append(
                        {
                            "user_id": user.user_id,
                            "email": user.email,
                            "name": user.display_name,
                        }
                    )
        return JSONResponse(content=result)

    @app.get("/api/v2/roles/{role_id}/permissions")
    async def get_role_permissions(role_id: str) -> JSONResponse:
        result = []
        for permission in db.roles[role_id].permissions:
            result.append(
                {
                    "permission_name": permission,
                }
            )
        return JSONResponse(content=result)

    @app.get("/api/v2/organizations/{org_id}")
    async def get_organization(org_id: str) -> JSONResponse:
        org = db.organizations[org_id]["org"]
        return JSONResponse(
            content={"id": org.id, "name": org.name, "display_name": org.display_name}
        )

    @app.get("/api/v2/users/{user_id}/organizations")
    async def get_orgs_for_user(user_id: str) -> JSONResponse:
        result = []
        for org in db.organizations.values():
            for user_role in org["user_roles"]:
                if user_role["user_id"] == user_id:
                    result.append(
                        {
                            "id": org["org"].id,
                            "display_name": org["org"].display_name,
                            "name": org["org"].name,
                        }
                    )
                    break
        return JSONResponse(content=result)

    @app.get("/api/v2/organizations/{org_id}/members/{user_id}/roles")
    async def get_user_role_in_organization(user_id: str, org_id: str) -> JSONResponse:
        result = []
        for user_role in db.organizations[org_id]["user_roles"]:
            if user_role["user_id"] == user_id:
                result.append(
                    {
                        "id": user_role["role_id"],
                    }
                )
        return JSONResponse(content=result)

    @app.get("/api/v2/organizations/{org_id}/members")
    async def get_users_in_organization(org_id: str) -> JSONResponse:
        # First get the whole list of users
        users_in_org = set()
        for user_role in db.organizations[org_id]["user_roles"]:
            users_in_org.add(user_role["user_id"])

        # We will return an array. Sort so that tests aren't flaky.
        result = [db.users[user_id]["user"] for user_id in sorted(users_in_org)]

        return JSONResponse(
            content={
                "total": len(result),
                "members": [
                    {
                        "user_id": auth_user.user_id,
                        "name": auth_user.display_name,
                        "email": auth_user.email,
                    }
                    for auth_user in result
                ],
            }
        )

    @app.get("/api/v2/roles")
    async def get_role(request: Request) -> JSONResponse:
        # TODO: support other options on this route
        name_filter = request.query_params.get("name_filter", "")
        result = [role for role in db.roles.values() if name_filter in role.name]

        return JSONResponse(content=[{"id": role.id} for role in result])

    @app.get("/api/v2/roles/{role_id}/users")
    async def get_users_with_role(role_id: str) -> JSONResponse:
        result = [db_user["user"] for db_user in db.users.values() if role_id in db_user["roles"]]

        return JSONResponse(
            content={
                "total": len(result),
                "users": [
                    {
                        "user_id": auth_user.user_id,
                        "name": auth_user.display_name,
                        "email": auth_user.email,
                    }
                    for auth_user in result
                ],
            }
        )

    @app.get("/.well-known/jwks.json")
    async def get_jwks() -> JSONResponse:
        return JSONResponse(content={"keys": [mock_auth0_public_key_jwk]})

    return app
