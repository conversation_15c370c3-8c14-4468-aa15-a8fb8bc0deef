from dataclasses import dataclass
from datetime import datetime
from typing import TypedDict

ObjectQueryData = TypedDict(
    "ObjectQueryData",
    {
        "bucket": datetime,
        "mops": float,
        "occupancy": float,
        "media_availability": float,
        "scrubbed": float,
        "unscrubbed": float,
    },
)


@dataclass
class Database:
    _data: list[ObjectQueryData]

    def set_data(self, data: list[ObjectQueryData]) -> None:
        self._data = data

    def get_data(self) -> list[ObjectQueryData]:
        return self._data
