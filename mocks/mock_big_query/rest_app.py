import threading
import traceback
from contextlib import asynccontextmanager
from http import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import AsyncGenerator

from fastapi import <PERSON><PERSON><PERSON>
from starlette.responses import JSONResponse, Response

from mocks.mock_big_query.database import Database

from mocks.util.service_util import MockAppThread
from utils.net import allocate_port


class MockBigQuery:
    def __init__(self, mock_database: Database):
        self.database = mock_database

    @asynccontextmanager
    async def serving(self, port: int = 0) -> AsyncGenerator[int, None]:
        if port == 0:
            port = allocate_port()
        shutdown_event = threading.Event()
        big_query_app_thread = MockAppThread(create_app, self.database, port, shutdown_event)
        big_query_app_thread.start()
        try:
            yield port
        finally:
            shutdown_event.set()
            big_query_app_thread.join()


# https://cloud.google.com/bigquery/docs/reference/rest/v2/JobReference
job_reference = {"jobId": "test_job_id", "projectId": "test_project_id", "location": "US"}
# https://cloud.google.com/bigquery/docs/reference/rest/v2/Job
completed_job_response = {"jobReference": job_reference, "status": {"state": "DONE"}}


def create_app(db: Database) -> FastAPI:
    """
    Use the application factory pattern to de-globalize the
    application and thereby allow the deglobalization of the in-memory database.
    """
    app = FastAPI()

    @app.exception_handler(Exception)
    async def any_exception(e: Exception) -> Response:
        # We don't want to throw any exceptions in the mock server
        stacktrace = "".join(
            traceback.format_exception(type(e), value=e, tb=e.__traceback__, limit=None, chain=True)
        )
        print(stacktrace, flush=True)
        return Response(content=stacktrace, status_code=HTTPStatus.INTERNAL_SERVER_ERROR)

    # https://cloud.google.com/bigquery/docs/reference/rest/v2/jobs/insert
    @app.post("/bigquery/v2/projects/{project_id}/jobs")
    async def insert_job(project_id: str) -> JSONResponse:
        return JSONResponse(content=completed_job_response)

    # https://cloud.google.com/bigquery/docs/reference/rest/v2/jobs/getQueryResults
    @app.get("/bigquery/v2/projects/{project_id}/queries/{job_id}")
    async def get_query_results(project_id: str, job_id: str) -> JSONResponse:
        rows = [
            {
                "f": [
                    {"v": str(int(row["bucket"].timestamp() * 1000000))},
                    {"v": str(row["media_availability"])},
                    {"v": str(row["mops"])},
                    {"v": str(row["occupancy"])},
                    {"v": str(row["unscrubbed"])},
                    {"v": str(row["scrubbed"])},
                ]
            }
            for row in db.get_data()
        ]

        return JSONResponse(
            content={
                "jobReference": job_reference,
                "totalRows": len(rows),
                "schema": {
                    "fields": [
                        {"name": "bucket", "type": "TIMESTAMP", "mode": "NULLABLE"},
                        {"name": "media_availability", "type": "FLOAT", "mode": "NULLABLE"},
                        {"name": "occupancy", "type": "FLOAT", "mode": "NULLABLE"},
                        {"name": "unscrubbed", "type": "FLOAT", "mode": "NULLABLE"},
                        {"name": "scrubbed", "type": "FLOAT", "mode": "NULLABLE"},
                        {"name": "mops", "type": "FLOAT", "mode": "NULLABLE"},
                    ]
                },
                "rows": rows,
                "jobComplete": True,
            }
        )

    # https://cloud.google.com/bigquery/docs/reference/rest/v2/jobs/get
    @app.get("/bigquery/v2/projects/{project_id}/jobs/{job_id}")
    async def get_job(project_id: str, job_id: str) -> JSONResponse:
        return JSONResponse(content=completed_job_response)

    return app
