import asyncio
import datetime
import time
from typing import Final, Any

import requests
import schedule
from prometheus_client import Counter
import config
from apella_cloud_api import Client
from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_input_schema import GQLSiteLaunchUpsertInput
from apella_cloud_api.new_schema_generator.schema_generator_base_classes import Empty
from api_server import logging
from api_server.app_service_provider.notion_processor_container import notion_processor_container
from databases.secret_store import SecretStore


NOTION_PROCESSOR_JOB_RUN_COUNTER: Final = Counter(
    "notion_processor_job_runs",
    "Counts the number of times the notification processor has executed jobs",
)

NOTION_PROCESSOR_JOB_ERROR_COUNTER: Final = Counter(
    "notion_processor_job_errors",
    "Counts the number of errors encountered during job execution",
    ["error_type"],
)


class NotionProcessor:
    api_client: Client
    apella_schema: ApellaSchema
    notion_api_key: str
    notion_db_id: str

    def __init__(
        self,
    ) -> None:
        self.api_client = Client(environment=config.environment_name())
        self.apella_schema = ApellaSchema()
        secret_store = notion_processor_container.provide_app_scope_type(SecretStore)
        self.notion_api_key = secret_store.get_secret(config.get_notion_api_key_name())
        self.notion_db_id = config.get_notion_db_id()

    def start_processor(self) -> None:
        """
        Start the notification processor and schedule jobs.
        """
        try:
            logging.info("Starting Notion Processor...")
            schedule.every(config.notion_time_to_run()).seconds.do(self._job)
            while True:
                schedule.run_pending()
                time.sleep(1)
        except Exception as e:
            logging.error(f"Notion Processor encountered an error: {e}")
            NOTION_PROCESSOR_JOB_ERROR_COUNTER.labels(
                environment=config.environment_name(),
            ).inc()  # Increment error counter

    def _job(self) -> None:
        """
        Executes the job and updates Prometheus metrics.
        """
        try:
            logging.info("Running scheduled job...")
            asyncio.run(self._start_processor())
            NOTION_PROCESSOR_JOB_RUN_COUNTER.inc()  # Increment job run counter
        except Exception as e:
            logging.error(f"Error during job execution: {e}")
            NOTION_PROCESSOR_JOB_ERROR_COUNTER.labels(
                error_type="runtime_error",
            ).inc()  # Increment error counter

    async def _start_processor(self) -> None:
        sites_query = self.apella_schema.Query.sites.args().select(
            self.apella_schema.SiteConnection.edges.select(
                self.apella_schema.SiteEdge.node.select(self.apella_schema.Site.id)
            )
        )
        sites_results = self.api_client.query_graphql_from_schema(
            sites_query, label="notion_processor__get_sites"
        )
        site_ids = {edge.node.id for edge in sites_results.sites.edges}
        site_launches = self._get_site_launch_data(site_ids)

        mutation = self.apella_schema.Mutation.site_launches_upsert.args(
            input=site_launches,
        ).select(
            self.apella_schema.SiteLaunchesUpsert.success,
        )

        self.api_client.mutate_graphql_from_schema(
            mutation,
            label="notion_processor__site_launches_upsert",
        )

    def _get_site_launch_data(self, site_ids_to_check: set[str]) -> list[GQLSiteLaunchUpsertInput]:
        notion_url = f"https://api.notion.com/v1/databases/{self.notion_db_id}/query"
        headers = {
            "Notion-Version": "2022-06-28",
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.notion_api_key}",
        }
        response = requests.post(notion_url, headers=headers)
        response.raise_for_status()

        data: dict[str, Any] = response.json()
        results = data.get("results", [])

        site_launches = []
        for result in results:
            site_id = None
            site_obj = result["properties"]["Site ID"]["rich_text"]
            if site_obj:
                site_id = site_obj[0]["plain_text"].strip()

            anticipated_launch_date_obj = result["properties"]["Anticipated go-live date"]["date"]
            if anticipated_launch_date_obj:
                anticipated_launch_date_str = anticipated_launch_date_obj["start"]
            else:
                anticipated_launch_date_str = None

            actual_launch_date_obj = result["properties"]["Actual go-live date"]["date"]
            if actual_launch_date_obj:
                actual_launch_date_str = actual_launch_date_obj["start"]
            else:
                actual_launch_date_str = None

            if not (anticipated_launch_date_str or actual_launch_date_str):
                continue

            actual_launch_date = (
                datetime.date.fromisoformat(actual_launch_date_str)
                if actual_launch_date_str is not None
                else Empty()
            )
            anticipated_launch_date = (
                datetime.date.fromisoformat(anticipated_launch_date_str)
                if anticipated_launch_date_str is not None
                else Empty()
            )
            site_name_obj = result["properties"]["Site"]["title"][0]
            if not site_name_obj:
                continue
            site_name = site_name_obj["text"]["content"].strip()
            if not site_name:
                continue

            row_id = str(result["properties"]["ID"]["unique_id"]["number"])
            site_launches.append(
                GQLSiteLaunchUpsertInput(
                    site_id=site_id if site_id in site_ids_to_check else Empty(),
                    site_name=site_name,
                    actual_launch_date=actual_launch_date,
                    anticipated_launch_date=anticipated_launch_date,
                    notion_id=row_id,
                )
            )

        return site_launches
