[tool.poetry]
name = "cloud-api-server"
version = "0.1.0"
description = "Cloud API Server"
authors = ["Apella Technology"]
package-mode = false

[[tool.poetry.source]]
name = "PyPI"
priority = "primary"

[[tool.poetry.source]]
name = "prod-python-registry"
url = "https://us-central1-python.pkg.dev/prod-platform-29b5cb/prod-python-registry/simple/"
priority = "supplemental"

[tool.poetry.dependencies]
# TODO: Separate each library's dependencies to separate pyproject.toml files
python = "~3.10.0"
isodate = "^0.6.1"
google-cloud-secret-manager = "^2.2.0"
PyJWT = { version = "^2.4.0", extras = ["crypto"] }
pyyaml = "^6.0.1"
cachetools = "^4.2.0"
google-cloud-storage = "^1.36.0"
google-cloud-pubsub = "^2.12.0"
google-cloud-logging = "^3.0.0"
google-cloud-bigquery = "^3.29.0"
Jinja2 = "^3.1.6"
itsdangerous = "^2.0.0"
blinker = "^1.4"
python-jose = "^3.4.0"
graphene = "^3.2"
graphql-core = "^3.2.0"
inflection = "^0.5.1"
grpcio = "^1.46.0"
starlette-graphene3 = "^0.6.0"
vobject = "^0.9.7"

# SQL stuff
SQLAlchemy = {version = "2.0.28", extras = ["asyncio"]}
asyncpg = "^0.29.0"

# For api client. Remember to change in create_packages.py and setup_cli_tool.py
# TODO: create separate poetry pyproject.toml for each library
google_auth = "^2.17.0"
requests = "^2.32.2"
click = "^8.0.0"
tabulate = "^0.8.9"
marshmallow = "^3.19.0"
dataclasses_json = "^0.6.7"
sgqlc = "^16.0"

google = "^3.0.0"

# For Tracing
opentelemetry-api = "^1.22.0"
opentelemetry-sdk = "^1.22.0"
opentelemetry-exporter-otlp = "^1.22.0"
opentelemetry-instrumentation-fastapi = "^0.47b0"
opentelemetry-instrumentation-requests = "^0.47b0"
opentelemetry-instrumentation-sqlalchemy = "^0.47b0"
opentelemetry-instrumentation-aiohttp-client = "^0.47b0"
opentelemetry-propagator-b3 = "^1.22.0"
opentelemetry-instrumentation-asyncpg = "0.47b0"

# For logging
sentry-sdk = { version = "^1.14.0" }

# For caching
redis = "^4.5.3"
tenacity = "^8.0.1"
requests-cache = "^1.2.0"

# For Datadog prometheus metrics scraping
prometheus-client = "^0.16.0"


launchdarkly-server-sdk = "^9.0.0"
twilio = "^7.16.3"
twilio-stubs = "^0.2.0"
hypercorn = "^0.16.0"
fastapi = "^0.115.6"
uvloop = "^0.19.0"
asyncio = "^3.4.3"
a2wsgi = "^1.10.0"
di = "^0.79.2"
anyio = "^4.2.0"
contextvars = "^2.4"
aiodataloader = "^0.4.0"
pytz = "^2024.1"
aiohttp = "^3.10.0"
aioresponses = "^0.7.6"
asyncpg-stubs = "^0.29.1"
dacite = "^1.8.1"
lib-python-config = "^0.1.13"
schedule = "^1.2.2"
sendgrid = "^6.11.0"
holidays = "^0.62"
pybars3 = "^0.9.7"
annotation-task-optimizer = "^2.0.2"

opentelemetry-distro = "0.47b0"
[tool.poetry.group.test.dependencies]
docker = "^7.1.0"
httpx = "0.23.1"
requests-mock = "^1.10.0"
pytest = "^7.4.0"
pytest-asyncio = "0.21.1"
pytest-cov = "^3.0.0"
pytest-timeout = "^2.3.1"
pytest-xdist = "^3.3.1"


[tool.poetry.group.dev.dependencies]
alembic = "^1.12.0"
alembic-postgresql-enum = "^1.5.0"
fastapi-cli = "^0.0.5"
mypy = "^1.0.0"
ruff = "^0.11.0"
types-cachetools = "^5.0.0"
types-python-dateutil = "^2.8.19"
types-pyyaml = "^6.0.8"
types-redis = "^4.2.0"
types-requests = "^2.27.0"
types-setuptools = "^57.4.17"
types-tabulate = "^0.8.10"
types-vobject = "^0.9.8"

# For artifact registry
twine = "^5.1.1"
keyring = "^23.13.1"
keyrings-google-artifactregistry-auth = "^1.1.2"
locust = "^2.27.0"
har2locust = "^0.9.2"
types-pytz = "^2024.2.0.20241003"


[tool.ruff]
line-length = 100

# Assume Python 3.10
target-version = "py310"

[tool.ruff.lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`)  codes by default.
select = ["F", "W", "C9", "E"]
ignore = [
    "C901",
    "E501",
    "F541",
    "E721" # Do not compare types, use `isinstance()`
]

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.lint.mccabe]
# Unlike Flake8, default to a complexity level of 10.
max-complexity = 10

[tool.mypy]
mypy_path = "./stubs"
# For now, exclude a few directories as we gradually add mypy to these folders.
exclude = [
    'alembic', # auto-generated migrations don't need to be type-checked
    'api_server_schema\.py$', # auto-generated file does not need to be type-checked
    'build',
    'stubs',
    'venv' # no error on local runs
]

# Add strict mode settings that already pass. We'll add more as we make them pass.
warn_unused_configs = true
disallow_any_generics = true
# disallow_subclassing_any = true
# disallow_untyped_calls = true
disallow_untyped_defs = true
# disallow_incomplete_defs = true
# check_untyped_defs = true
disallow_untyped_decorators = true
warn_redundant_casts = true
warn_unused_ignores = true
# warn_return_any = true
# no_implicit_reexport = true
strict_equality = true
extra_checks = true

# This flag is always enabled when running mypy in daemon mode (like it does in VSCode). The
# documentation (https://mypy.readthedocs.io/en/stable/command_line.html#cmdoption-mypy-local-partial-types)
# states that it will become the default for the command line version at some point too, so we might
# as well turn it on.
local_partial_types = true

# As of 0.5.7, dataclasses_json is still using the implicit Optional argument annotation syntax
[[tool.mypy.overrides]]
module = "dataclasses_json.*"
implicit_optional = true

# The following 3rd party libraries have not exported stubs or types
# https://mypy.readthedocs.io/en/stable/running_mypy.html#missing-imports
[[tool.mypy.overrides]]
module = [
    "docker",
    "graphql_relay.*",
    "google.*",
    "graphene.*",
    "gunicorn.*",
    "isodate.*",
    "jose.*",
    "psycopg2.*",
    "setuptools.*",
    "sgqlc.*",
    "sqlalchemy.*",
    "pytz",
    "sendgrid",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
python_files = [
    "test_*.py",
    "tests_*.py",
    "*_test.py",
    "*_tests.py",
]
filterwarnings = [
    # Suppresses this warning:
    # pkg_resources/__init__.py:2349: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('google')`.
    "ignore::DeprecationWarning:pkg_resources.*",
    # Suppresses this warning:
    # graphene/relay/connection.py:2: DeprecationWarning: Using or importing the ABCs from 'collections' instead of from 'collections.abc' is deprecated since Python 3.3, and in 3.10 it will stop working
    "ignore::DeprecationWarning:graphene.*",
    # Suppresses this warning:
    # google/rpc/__init__.py:20: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('google.rpc')`.
    "ignore::DeprecationWarning:google.*",
    # Suppresses this warning:
    # dataclasses_json/mm.py:260: RemovedInMarshmallow4Warning: The 'default' argument to fields is deprecated. Use 'dump_default' instead.
    "ignore::DeprecationWarning:dataclasses_json*",
]
asyncio_mode = "auto"

[build-system]
# Should be the same as version in Dockerfile:
requires = ["poetry==2.1.1"]
build-backend = "poetry.masonry.api"

[tool.poetry.scripts]
apellaapp = "cloud-api-server.main"
