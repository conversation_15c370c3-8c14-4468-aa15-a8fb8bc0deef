from datetime import datetime
from unittest.mock import Async<PERSON>ock, MagicMock, call
import pytest
from api_server.services.apella_case.apella_case_service import ApellaCaseService
from api_server.services.apella_case.apella_case_store import (
    ApellaCase,
    ApellaCaseQuery,
    ApellaCaseStore,
)
from auth.auth import Auth
from auth.permissions import READ_ANY_EVENT

mock_auth = MagicMock(spec=Auth)
mock_apella_case_store = MagicMock(spec=ApellaCaseStore)
mock_auth.get_calling_org_id = MagicMock(return_value=None)
mock_launch_darkly_service = MagicMock()
mock_launch_darkly_service.get_feature_flag = MagicMock(return_value=False)
mock_case_forecast_store = MagicMock()
apella_case_service = ApellaCaseService(
    auth=mock_auth,
    apella_case_store=mock_apella_case_store,
    launch_darkly_service=mock_launch_darkly_service,
)


class TestApellaCaseService:
    @pytest.mark.asyncio
    async def test_query_cases(self) -> None:
        mock_auth.requires = MagicMock()
        test_case = ApellaCase(
            _actual=None, _case=None, _case_forecast=None, _apella_case_model=None
        )
        mock_apella_case_store.query_apella_cases = AsyncMock(return_value=[test_case])

        query = ApellaCaseQuery(
            min_end_time=datetime.now(),
            max_start_time=datetime.now(),
        )

        result = await apella_case_service.query_cases(query)

        mock_auth.requires.assert_called_with(READ_ANY_EVENT, enforce_universal_user=False)
        mock_apella_case_store.query_apella_cases.assert_called_once()
        mock_launch_darkly_service.get_feature_flag.assert_has_calls(
            [
                call("forecasted-case-variant", "stable"),
            ]
        )

        assert result == [test_case]
