from datetime import datetime, timedelta, timezone
from typing import Any

from api_server.services.apella_case.apella_case_store import (
    APELLA_CASE_STATUS_PRIORITY,
    APELLA_DEFAULT_CASE_STATUS_MAP,
    EVENT_TO_CASE_STATUS_MAP,
    OBSERVATION_TO_CASE_STATUS_MAP,
    POST_OP_OBSERVATIONS,
    PRE_OP_OBSERVATIONS,
    ApellaCase,
    ApellaCaseStatus,
    CaseSource,
    CaseStatusName,
    CaseType,
    LiveEvent,
)
from api_server.services.case_forecasts.case_forecast_store import CaseForecastModel
from api_server.services.case.case_store import Case
from api_server.services.phases.phase_store import PhaseModel
from tests.api_server.services.apella_case.apella_case_mock_helpers import (
    _mock_case,
    _mock_observation,
    _mock_event,
    _mock_phase,
    _mock_case_forecast,
)


class TestApellaCaseStore:
    def _generate_cases(self) -> dict[str, Any]:
        live_phase = _mock_phase(start_time=(datetime.now(timezone.utc) - timedelta(hours=4)))

        completed_phase = _mock_phase(
            start_time=(datetime.now(timezone.utc) - timedelta(hours=4)),
            end_time=(datetime.now(timezone.utc) - timedelta(hours=2)),
        )

        scheduled_case = _mock_case(
            start_time=(datetime.now(timezone.utc) - timedelta(hours=4)),
            end_time=(datetime.now(timezone.utc) - timedelta(hours=2)),
        )

        live_phase_with_case_id = _mock_phase(
            start_time=(datetime.now(timezone.utc) - timedelta(hours=4)),
            case_id=scheduled_case.case_id,
        )

        completed_phase_with_case_id = _mock_phase(
            start_time=(datetime.now(timezone.utc) - timedelta(hours=4)),
            end_time=(datetime.now(timezone.utc) - timedelta(hours=2)),
            case_id=scheduled_case.case_id,
        )

        live_case_with_no_scheduled_case = ApellaCase(
            _actual=live_phase,
            _case=None,
            _case_forecast=None,
            _apella_case_model=None,
        )

        completed_case_with_no_scheduled_case = ApellaCase(
            _actual=completed_phase,
            _case=None,
            _case_forecast=None,
            _apella_case_model=None,
        )

        future_case_forecast = _mock_case_forecast(
            start_time=(datetime.now(timezone.utc) + timedelta(hours=3, minutes=30)),
            end_time=(datetime.now(timezone.utc) + timedelta(hours=1, minutes=30)),
            case_id=scheduled_case.case_id,
            room_id=scheduled_case.room_id,
            forecast_variant="stable",
        )

        live_case_with_scheduled_case = ApellaCase(
            _actual=live_phase_with_case_id,
            _case=scheduled_case,
            _case_forecast=future_case_forecast,
            _apella_case_model=None,
        )

        future_scheduled_case = _mock_case(
            start_time=(datetime.now(timezone.utc) + timedelta(hours=2)),
            end_time=(datetime.now(timezone.utc) + timedelta(hours=4)),
        )

        future_case_with_scheduled_case = ApellaCase(
            _actual=None,
            _case=future_scheduled_case,
            _case_forecast=future_case_forecast,
            _apella_case_model=None,
        )

        future_case_with_no_case_forecast = ApellaCase(
            _actual=None,
            _case=future_scheduled_case,
            _case_forecast=None,
            _apella_case_model=None,
        )

        completed_case_with_scheduled_case = ApellaCase(
            _actual=completed_phase_with_case_id,
            _case=scheduled_case,
            _case_forecast=None,
            _apella_case_model=None,
        )

        past_scheduled_case = _mock_case(
            start_time=(datetime.now(timezone.utc) - timedelta(hours=4)),
            end_time=(datetime.now(timezone.utc) - timedelta(hours=2)),
        )

        case_with_scheduled_case_in_past = ApellaCase(
            _actual=None,
            _case=past_scheduled_case,
            _case_forecast=None,
            _apella_case_model=None,
        )

        case_with_both_phase_and_forecast = ApellaCase(
            _actual=None,
            _case=future_scheduled_case,
            _case_forecast=future_case_forecast,
            _apella_case_model=None,
        )

        return {
            "live_case_with_no_scheduled_case": live_case_with_no_scheduled_case,
            "completed_case_with_no_scheduled_case": completed_case_with_no_scheduled_case,
            "live_case_with_scheduled_case": live_case_with_scheduled_case,
            "completed_case_with_scheduled_case": completed_case_with_scheduled_case,
            "future_case_with_scheduled_case": future_case_with_scheduled_case,
            "future_case_with_no_case_forecast": future_case_with_no_case_forecast,
            "case_with_scheduled_case_in_past": case_with_scheduled_case_in_past,
            "live_phase": live_phase,
            "completed_phase": completed_phase,
            "scheduled_case": scheduled_case,
            "future_scheduled_case": future_scheduled_case,
            "past_scheduled_case": past_scheduled_case,
            "live_phase_with_case_id": live_phase_with_case_id,
            "completed_phase_with_case_id": completed_phase_with_case_id,
            "case_with_both_phase_and_forecast": case_with_both_phase_and_forecast,
            "future_case_forecast": future_case_forecast,
        }

    def test_id_determined(self) -> None:
        cases = self._generate_cases()

        live_phase: PhaseModel = cases["live_phase"]
        completed_phase: PhaseModel = cases["completed_phase"]
        scheduled_case: Case = cases["scheduled_case"]
        future_scheduled_case: Case = cases["future_scheduled_case"]
        past_scheduled_case: Case = cases["past_scheduled_case"]

        live_case_with_no_scheduled_case: ApellaCase = cases["live_case_with_no_scheduled_case"]
        completed_case_with_no_scheduled_case: ApellaCase = cases[
            "completed_case_with_no_scheduled_case"
        ]
        live_case_with_scheduled_case: ApellaCase = cases["live_case_with_scheduled_case"]
        completed_case_with_scheduled_case: ApellaCase = cases["completed_case_with_scheduled_case"]
        future_case_with_scheduled_case: ApellaCase = cases["future_case_with_scheduled_case"]
        future_case_with_no_case_forecast: ApellaCase = cases["future_case_with_no_case_forecast"]
        case_with_scheduled_case_in_past: ApellaCase = cases["case_with_scheduled_case_in_past"]
        case_with_both_phase_and_forecast: ApellaCase = cases["case_with_both_phase_and_forecast"]

        assert live_case_with_no_scheduled_case.id == f"phase:{live_phase.id}"
        assert completed_case_with_no_scheduled_case.id == f"phase:{completed_phase.id}"
        assert live_case_with_scheduled_case.id == f"case:{scheduled_case.case_id}"
        assert completed_case_with_scheduled_case.id == f"case:{scheduled_case.case_id}"
        assert future_case_with_scheduled_case.id == f"case:{future_scheduled_case.case_id}"
        assert future_case_with_no_case_forecast.id == f"case:{future_scheduled_case.case_id}"
        assert case_with_scheduled_case_in_past.id == f"case:{past_scheduled_case.case_id}"
        assert case_with_both_phase_and_forecast.id == f"case:{future_scheduled_case.case_id}"

    def test_case_type_determined(self) -> None:
        cases = self._generate_cases()

        live_case_with_no_scheduled_case: ApellaCase = cases["live_case_with_no_scheduled_case"]
        completed_case_with_no_scheduled_case: ApellaCase = cases[
            "completed_case_with_no_scheduled_case"
        ]
        live_case_with_scheduled_case: ApellaCase = cases["live_case_with_scheduled_case"]
        completed_case_with_scheduled_case: ApellaCase = cases["completed_case_with_scheduled_case"]
        future_case_with_scheduled_case: ApellaCase = cases["future_case_with_scheduled_case"]
        future_case_with_no_case_forecast: ApellaCase = cases["future_case_with_no_case_forecast"]
        case_with_scheduled_case_in_past: ApellaCase = cases["case_with_scheduled_case_in_past"]
        case_with_both_phase_and_forecast: ApellaCase = cases["case_with_both_phase_and_forecast"]

        assert live_case_with_no_scheduled_case.type == CaseType.LIVE
        assert completed_case_with_no_scheduled_case.type == CaseType.COMPLETE
        assert live_case_with_scheduled_case.type == CaseType.LIVE
        assert completed_case_with_scheduled_case.type == CaseType.COMPLETE
        assert future_case_with_scheduled_case.type == CaseType.FORECAST
        assert future_case_with_no_case_forecast.type == CaseType.FORECAST
        assert case_with_scheduled_case_in_past.type == CaseType.FORECAST
        assert case_with_both_phase_and_forecast.type == CaseType.FORECAST

    def test_case_source_determined(self) -> None:
        cases = self._generate_cases()

        live_case_with_no_scheduled_case: ApellaCase = cases["live_case_with_no_scheduled_case"]
        completed_case_with_no_scheduled_case: ApellaCase = cases[
            "completed_case_with_no_scheduled_case"
        ]
        live_case_with_scheduled_case: ApellaCase = cases["live_case_with_scheduled_case"]
        completed_case_with_scheduled_case: ApellaCase = cases["completed_case_with_scheduled_case"]
        future_case_with_scheduled_case: ApellaCase = cases["future_case_with_scheduled_case"]
        future_case_with_no_case_forecast: ApellaCase = cases["future_case_with_no_case_forecast"]
        case_with_scheduled_case_in_past: ApellaCase = cases["case_with_scheduled_case_in_past"]
        case_with_both_phase_and_forecast: ApellaCase = cases["case_with_both_phase_and_forecast"]

        assert live_case_with_no_scheduled_case.source == CaseSource.INTERNAL
        assert completed_case_with_no_scheduled_case.source == CaseSource.INTERNAL
        assert live_case_with_scheduled_case.source == CaseSource.INTERNAL
        assert completed_case_with_scheduled_case.source == CaseSource.INTERNAL
        assert future_case_with_scheduled_case.source == CaseSource.INTERNAL
        assert future_case_with_no_case_forecast.source == CaseSource.EXTERNAL
        assert case_with_scheduled_case_in_past.source == CaseSource.EXTERNAL
        assert case_with_both_phase_and_forecast.source == CaseSource.INTERNAL

    def test_room_id_determined(self) -> None:
        cases = self._generate_cases()

        live_phase: PhaseModel = cases["live_phase"]
        completed_phase: PhaseModel = cases["completed_phase"]
        scheduled_case: Case = cases["scheduled_case"]
        future_scheduled_case: Case = cases["future_scheduled_case"]
        past_scheduled_case: Case = cases["past_scheduled_case"]

        live_case_with_no_scheduled_case: ApellaCase = cases["live_case_with_no_scheduled_case"]
        completed_case_with_no_scheduled_case: ApellaCase = cases[
            "completed_case_with_no_scheduled_case"
        ]
        live_case_with_scheduled_case: ApellaCase = cases["live_case_with_scheduled_case"]
        completed_case_with_scheduled_case: ApellaCase = cases["completed_case_with_scheduled_case"]
        future_case_with_scheduled_case: ApellaCase = cases["future_case_with_scheduled_case"]
        future_case_with_no_case_forecast: ApellaCase = cases["future_case_with_no_case_forecast"]
        case_with_scheduled_case_in_past: ApellaCase = cases["case_with_scheduled_case_in_past"]
        case_with_both_phase_and_forecast: ApellaCase = cases["case_with_both_phase_and_forecast"]

        assert live_case_with_no_scheduled_case.room_id == live_phase.room_id
        assert completed_case_with_no_scheduled_case.room_id == completed_phase.room_id
        assert live_case_with_scheduled_case.room_id == scheduled_case.room_id
        assert completed_case_with_scheduled_case.room_id == scheduled_case.room_id
        assert future_case_with_scheduled_case.room_id == future_scheduled_case.room_id
        assert future_case_with_no_case_forecast.room_id == future_scheduled_case.room_id
        assert case_with_scheduled_case_in_past.room_id == past_scheduled_case.room_id
        assert (
            case_with_both_phase_and_forecast.room_id == case_with_both_phase_and_forecast.room_id
        )

    def test_start_time_determined(self) -> None:
        cases = self._generate_cases()

        live_phase: PhaseModel = cases["live_phase"]
        live_phase_with_case_id: PhaseModel = cases["live_phase_with_case_id"]
        completed_phase: PhaseModel = cases["completed_phase"]
        completed_phase_with_case_id: PhaseModel = cases["completed_phase_with_case_id"]
        future_scheduled_case: Case = cases["future_scheduled_case"]
        past_scheduled_case: Case = cases["past_scheduled_case"]

        live_case_with_no_scheduled_case: ApellaCase = cases["live_case_with_no_scheduled_case"]
        completed_case_with_no_scheduled_case: ApellaCase = cases[
            "completed_case_with_no_scheduled_case"
        ]
        live_case_with_scheduled_case: ApellaCase = cases["live_case_with_scheduled_case"]
        completed_case_with_scheduled_case: ApellaCase = cases["completed_case_with_scheduled_case"]
        future_case_with_no_case_forecast: ApellaCase = cases["future_case_with_no_case_forecast"]
        case_with_scheduled_case_in_past: ApellaCase = cases["case_with_scheduled_case_in_past"]
        case_with_both_phase_and_forecast: ApellaCase = cases["case_with_both_phase_and_forecast"]
        future_case_forecast: CaseForecastModel = cases["future_case_forecast"]

        assert live_case_with_no_scheduled_case.start_time == live_phase.start_time()
        assert completed_case_with_no_scheduled_case.start_time == completed_phase.start_time()
        assert live_case_with_scheduled_case.start_time == live_phase_with_case_id.start_time()
        assert (
            completed_case_with_scheduled_case.start_time
            == completed_phase_with_case_id.start_time()
        )
        assert (
            future_case_with_no_case_forecast.start_time
            == future_scheduled_case.scheduled_start_time
        )
        assert (
            case_with_scheduled_case_in_past.start_time == past_scheduled_case.scheduled_start_time
        )
        assert (
            case_with_both_phase_and_forecast.start_time == future_case_forecast.forecast_start_time
        )

    def test_end_time_determined(self) -> None:
        cases = self._generate_cases()

        live_phase: PhaseModel = cases["live_phase"]
        completed_phase: PhaseModel = cases["completed_phase"]
        completed_phase_with_case_id: PhaseModel = cases["completed_phase_with_case_id"]
        future_scheduled_case: Case = cases["future_scheduled_case"]
        past_scheduled_case: Case = cases["past_scheduled_case"]

        live_case_with_no_scheduled_case: ApellaCase = cases["live_case_with_no_scheduled_case"]
        completed_case_with_no_scheduled_case: ApellaCase = cases[
            "completed_case_with_no_scheduled_case"
        ]
        live_case_with_scheduled_case: ApellaCase = cases["live_case_with_scheduled_case"]
        completed_case_with_scheduled_case: ApellaCase = cases["completed_case_with_scheduled_case"]
        future_case_with_no_case_forecast: ApellaCase = cases["future_case_with_no_case_forecast"]
        case_with_scheduled_case_in_past: ApellaCase = cases["case_with_scheduled_case_in_past"]
        case_with_both_phase_and_forecast: ApellaCase = cases["case_with_both_phase_and_forecast"]
        future_case_forecast: CaseForecastModel = cases["future_case_forecast"]

        assert live_case_with_no_scheduled_case.end_time == live_phase.end_time()
        assert completed_case_with_no_scheduled_case.end_time == completed_phase.end_time()
        # This now has to be determined before live_case_with_scheduled_case.end_time, as that method takes the max between forecasted end and now
        now = datetime.now(timezone.utc)
        live_end_time = live_case_with_scheduled_case.end_time
        assert live_end_time is not None
        assert live_end_time >= now
        assert (
            completed_case_with_scheduled_case.end_time == completed_phase_with_case_id.end_time()
        )
        assert (
            future_case_with_no_case_forecast.end_time == future_scheduled_case.scheduled_end_time
        )
        assert case_with_scheduled_case_in_past.end_time == past_scheduled_case.scheduled_end_time
        assert case_with_both_phase_and_forecast.end_time == future_case_forecast.forecast_end_time

    def test_case_status_defaults_determined_when_no_events(self) -> None:
        cases = self._generate_cases()

        live_case_with_scheduled_case: ApellaCase = cases["live_case_with_scheduled_case"]
        completed_case_with_no_scheduled_case: ApellaCase = cases[
            "completed_case_with_no_scheduled_case"
        ]
        future_case_with_scheduled_case: ApellaCase = cases["future_case_with_scheduled_case"]

        assert live_case_with_scheduled_case.type == CaseType.LIVE
        assert live_case_with_scheduled_case.case_status(
            events=[], observations=[]
        ) == ApellaCaseStatus(
            name=APELLA_DEFAULT_CASE_STATUS_MAP[live_case_with_scheduled_case.type],
            source=CaseSource.INTERNAL,
            since=live_case_with_scheduled_case.start_time,
        )

        assert completed_case_with_no_scheduled_case.type == CaseType.COMPLETE
        assert completed_case_with_no_scheduled_case.case_status(
            events=[], observations=[]
        ) == ApellaCaseStatus(
            name=APELLA_DEFAULT_CASE_STATUS_MAP[completed_case_with_no_scheduled_case.type],
            source=CaseSource.INTERNAL,
            since=completed_case_with_no_scheduled_case.end_time,
        )

        assert future_case_with_scheduled_case.type == CaseType.FORECAST
        assert future_case_with_scheduled_case.case_status(
            events=[], observations=[]
        ) == ApellaCaseStatus(
            name=APELLA_DEFAULT_CASE_STATUS_MAP[future_case_with_scheduled_case.type],
            source=CaseSource.INTERNAL,
            since=(
                (
                    future_case_with_scheduled_case._case.updated_time
                    or future_case_with_scheduled_case._case.created_time
                )
                if future_case_with_scheduled_case._case is not None
                else None
            ),
        )

    def test_case_status_is_mapped_to_events_correctly(self) -> None:
        cases = self._generate_cases()
        live_case_with_scheduled_case: ApellaCase = cases["live_case_with_scheduled_case"]

        for key in EVENT_TO_CASE_STATUS_MAP.keys():
            event_time = datetime.now(timezone.utc)
            event = _mock_event(event_type_id=str(key.value), start_time=event_time)
            event_2 = _mock_event(
                event_type_id=str(key.value), start_time=(event_time - timedelta(minutes=30))
            )
            expected = ApellaCaseStatus(
                name=EVENT_TO_CASE_STATUS_MAP[key],
                source=CaseSource.INTERNAL,
                since=event.start_time,
            )
            actual = live_case_with_scheduled_case.case_status(
                events=[event_2, event], observations=[]
            )
            assert actual == expected

    def test_case_status_handles_multiple_drapings(self) -> None:
        cases = self._generate_cases()
        live_case_with_scheduled_case: ApellaCase = cases["live_case_with_scheduled_case"]

        event_time = datetime.now(timezone.utc) - timedelta(hours=1)
        first_surgery_event = _mock_event(
            event_type_id=LiveEvent.PATIENT_DRAPED.value, start_time=(event_time)
        )
        undraped_event = _mock_event(
            event_type_id=LiveEvent.PATIENT_UNDRAPED.value,
            start_time=(event_time + timedelta(minutes=1)),
        )
        second_surgery_event = _mock_event(
            event_type_id=LiveEvent.PATIENT_DRAPED.value,
            start_time=(event_time + timedelta(minutes=2)),
        )
        expected = ApellaCaseStatus(
            name=CaseStatusName.SURGERY,
            source=CaseSource.INTERNAL,
            since=second_surgery_event.start_time,
        )
        actual = live_case_with_scheduled_case.case_status(
            events=[first_surgery_event, undraped_event, second_surgery_event], observations=[]
        )
        assert actual == expected

    def test_observations_are_not_used_while_patient_in_room(self) -> None:
        cases = self._generate_cases()
        live_case_with_scheduled_case: ApellaCase = cases["live_case_with_scheduled_case"]

        prep_priority = APELLA_CASE_STATUS_PRIORITY[CaseStatusName.PREP]
        wrap_priority = APELLA_CASE_STATUS_PRIORITY[CaseStatusName.WRAP_UP]

        for key in EVENT_TO_CASE_STATUS_MAP.keys():
            event_status = EVENT_TO_CASE_STATUS_MAP[key]
            event_priority = APELLA_CASE_STATUS_PRIORITY[event_status] or -1
            is_in_room = prep_priority <= event_priority and event_priority <= wrap_priority

            if is_in_room is False:
                continue

            event_time = datetime.now(timezone.utc)
            event = _mock_event(event_type_id=str(key.value), start_time=event_time)

            if live_case_with_scheduled_case._case is None:
                assert False

            for observation_key in OBSERVATION_TO_CASE_STATUS_MAP.keys():
                observation_type = observation_key
                observation_time_now = datetime.now(timezone.utc)

                mock_observation = _mock_observation(
                    observation_type=observation_type,
                    case_id=live_case_with_scheduled_case._case.case_id,
                    observation_time=observation_time_now,
                )
                actual = live_case_with_scheduled_case.case_status(
                    events=[event], observations=[mock_observation]
                )

                expected = ApellaCaseStatus(
                    name=EVENT_TO_CASE_STATUS_MAP[key],
                    source=CaseSource.INTERNAL,
                    since=event.start_time,
                )

                assert actual == expected

    def test_case_status_is_mapped_to_post_op_observations_correctly(self) -> None:
        cases = self._generate_cases()
        completed_case_with_scheduled_case: ApellaCase = cases["completed_case_with_scheduled_case"]
        assert completed_case_with_scheduled_case.type == CaseType.COMPLETE

        default_status = completed_case_with_scheduled_case.case_status(events=[], observations=[])

        for key in OBSERVATION_TO_CASE_STATUS_MAP.keys():
            observation_type = key
            if completed_case_with_scheduled_case._case is None:
                assert False

            observation_time_now = datetime.now(timezone.utc)

            mock_observation = _mock_observation(
                observation_type=observation_type,
                case_id=completed_case_with_scheduled_case._case.case_id,
                observation_time=observation_time_now,
            )

            mock_observation_2 = _mock_observation(
                observation_type=observation_type,
                case_id=completed_case_with_scheduled_case._case.case_id,
                observation_time=(observation_time_now - timedelta(minutes=30)),
            )

            actual_status = completed_case_with_scheduled_case.case_status(
                events=[],
                observations=[mock_observation_2, mock_observation],
            )
            expected_status = (
                ApellaCaseStatus(
                    name=OBSERVATION_TO_CASE_STATUS_MAP[key],
                    source=CaseSource.EXTERNAL,
                    since=mock_observation_2.observation_time,
                )
                if key in POST_OP_OBSERVATIONS
                else default_status
            )

            assert actual_status == expected_status

    def test_case_status_is_mapped_to_pre_op_observations_correctly(self) -> None:
        cases = self._generate_cases()
        future_case_with_scheduled_case: ApellaCase = cases["future_case_with_scheduled_case"]
        assert future_case_with_scheduled_case.type == CaseType.FORECAST

        default_status = future_case_with_scheduled_case.case_status(events=[], observations=[])

        for key in OBSERVATION_TO_CASE_STATUS_MAP.keys():
            observation_type = key
            if future_case_with_scheduled_case._case is None:
                assert False

            observation_time_now = datetime.now(timezone.utc)

            mock_observation = _mock_observation(
                observation_type=observation_type,
                case_id=future_case_with_scheduled_case._case.case_id,
                observation_time=observation_time_now,
            )

            mock_observation_2 = _mock_observation(
                observation_type=observation_type,
                case_id=future_case_with_scheduled_case._case.case_id,
                observation_time=(observation_time_now - timedelta(minutes=30)),
            )

            actual_status = future_case_with_scheduled_case.case_status(
                events=[],
                observations=[mock_observation, mock_observation_2],
            )
            expected_status = (
                ApellaCaseStatus(
                    name=OBSERVATION_TO_CASE_STATUS_MAP[key],
                    source=CaseSource.EXTERNAL,
                    since=mock_observation_2.observation_time,
                )
                if key in PRE_OP_OBSERVATIONS
                else default_status
            )

            assert actual_status == expected_status

    def test_live_case_status_never_uses_observations(self) -> None:
        cases = self._generate_cases()
        live_case_with_scheduled_case: ApellaCase = cases["live_case_with_scheduled_case"]
        assert live_case_with_scheduled_case.type == CaseType.LIVE

        default_status = live_case_with_scheduled_case.case_status(events=[], observations=[])

        for key in OBSERVATION_TO_CASE_STATUS_MAP.keys():
            observation_type = key
            if live_case_with_scheduled_case._case is None:
                assert False

            actual_status = live_case_with_scheduled_case.case_status(
                events=[],
                observations=[
                    _mock_observation(
                        observation_type=observation_type,
                        case_id=live_case_with_scheduled_case._case.case_id,
                    )
                ],
            )
            mapped_observation_status = ApellaCaseStatus(
                name=OBSERVATION_TO_CASE_STATUS_MAP[key],
                source=CaseSource.EXTERNAL,
            )

            assert actual_status != mapped_observation_status
            assert actual_status == default_status

    def test_default_status_contains_defualt_state_keys(self) -> None:
        case = ApellaCase(
            _actual=None,
            _case=_mock_case(
                start_time=(datetime.now(timezone.utc) - timedelta(hours=4)),
                end_time=(datetime.now(timezone.utc) - timedelta(hours=2)),
            ),
            _case_forecast=None,
            _apella_case_model=None,
        )

        default_status_since = case.default_status_since()

        for case_status in APELLA_DEFAULT_CASE_STATUS_MAP.values():
            assert default_status_since.get(case_status, None) is not None
