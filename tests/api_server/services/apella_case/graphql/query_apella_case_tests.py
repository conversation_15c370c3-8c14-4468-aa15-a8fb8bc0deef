from datetime import datetime, timedelta, timezone
from unittest.mock import <PERSON><PERSON><PERSON>, AsyncMock

from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_client_schema import GQLCaseStatusName
from apella_cloud_api.new_input_schema import GQLApellaCaseQueryInput
from api_server.services.apella_case.apella_case_store import (
    ApellaCase,
    LiveEvent,
    CaseSource,
    CaseStatusName,
    ApellaCaseStatus,
)

from api_server.services.phases.phase_store import PhaseModel
import pytest

from tests.api_server.services.apella_case.apella_case_mock_helpers import (
    _mock_phase,
    _mock_event,
)
from tests.harness import harness

ROOM_1 = "room_1"
ROOM_2 = "room_2"


class TestQueryApellaCase:
    @pytest.fixture
    def live_phase_in_room_1(self) -> PhaseModel:
        case_start_time = datetime.now(timezone.utc) - timedelta(hours=4)
        return _mock_phase(start_time=case_start_time, room_id=ROOM_1)

    @pytest.fixture
    def live_phase_in_room_2(self) -> PhaseModel:
        case_start_time = datetime.now(timezone.utc) - timedelta(hours=4)
        return _mock_phase(start_time=case_start_time, room_id=ROOM_2)

    @pytest.mark.asyncio
    async def test_apella_case_query_returns_mocked_case_status(
        self, live_phase_in_room_1: PhaseModel
    ) -> None:
        client, context = harness()

        case_status = ApellaCaseStatus(name=CaseStatusName.PREP, source=CaseSource.INTERNAL)
        live_apella_case_in_room_1 = ApellaCase(
            _actual=live_phase_in_room_1,
            _case=None,
            _case_forecast=None,
            _apella_case_model=None,
        )
        context.apella_case_service.get_apella_case_status = MagicMock(return_value=case_status)
        context.apella_case_service.query_cases = AsyncMock(
            return_value=[live_apella_case_in_room_1]
        )

        # Make the GQL query
        apella_schema = ApellaSchema()
        apella_cases_gql_query = apella_schema.Query.apella_cases.args(
            query=GQLApellaCaseQueryInput(
                min_end_time=datetime.now() - timedelta(days=1),
                max_start_time=datetime.now() + timedelta(days=1),
                room_ids=[ROOM_1, ROOM_2],
            )
        ).select(
            apella_schema.ApellaCaseConnection.edges.select(
                apella_schema.ApellaCaseEdge.node.select(
                    apella_schema.ApellaCase.status.select(apella_schema.ApellaCaseStatus.name),
                )
            )
        )
        results = await client.execute_apella_query_async(
            apella_cases_gql_query,
            context_value=context,
        )
        cases = [result.node for result in results.apella_cases.edges]

        # Assertions
        for case in cases:
            assert GQLCaseStatusName[case_status.name.name] == case.status.name

    @pytest.mark.asyncio
    async def test_events_during_live_cases_passed_into_get_apella_case_status(
        self,
        live_phase_in_room_1: PhaseModel,
        live_phase_in_room_2: PhaseModel,
    ) -> None:
        client, context = harness()

        # Mock the events loader to return the events for each room
        events_in_room_1_before_case = [
            _mock_event(
                str(LiveEvent.PATIENT_WHEELS_OUT),
                live_phase_in_room_1.start_time() - timedelta(hours=2),
                room_id=ROOM_1,
            ),
        ]
        events_in_room_1_during_case = [
            live_phase_in_room_1.start_event,
            _mock_event(
                str(LiveEvent.PATIENT_DRAPED),
                live_phase_in_room_1.start_time() + timedelta(minutes=2),
                room_id=ROOM_1,
            ),
        ]

        events_in_room_2_before_case = [
            _mock_event(
                str(LiveEvent.PATIENT_WHEELS_OUT),
                live_phase_in_room_2.start_time() - timedelta(hours=2),
                room_id=ROOM_2,
            ),
        ]
        events_in_room_2_during_case = [live_phase_in_room_2.start_event]

        events_in_room_1_flattened = [*events_in_room_1_during_case, *events_in_room_1_before_case]
        events_in_room_2_flattened = [*events_in_room_2_during_case, *events_in_room_2_before_case]
        context.room_events_loader.load = AsyncMock(
            side_effect=lambda room_events_query: events_in_room_1_flattened
            if room_events_query.room_id == ROOM_1
            else events_in_room_2_flattened
        )
        live_apella_case_in_room_1 = ApellaCase(
            _actual=live_phase_in_room_1,
            _case=None,
            _case_forecast=None,
            _apella_case_model=None,
        )
        live_apella_case_in_room_2 = ApellaCase(
            _actual=live_phase_in_room_2,
            _case=None,
            _case_forecast=None,
            _apella_case_model=None,
        )
        context.apella_case_service.query_cases = AsyncMock(
            return_value=[live_apella_case_in_room_1, live_apella_case_in_room_2]
        )
        case_status = ApellaCaseStatus(name=CaseStatusName.PREP, source=CaseSource.INTERNAL)
        context.apella_case_service.get_apella_case_status = MagicMock(return_value=case_status)

        # Make the GQL query
        apella_schema = ApellaSchema()
        apella_cases_gql_query = apella_schema.Query.apella_cases.args(
            query=GQLApellaCaseQueryInput(
                min_end_time=datetime.now() - timedelta(days=1),
                max_start_time=datetime.now() + timedelta(days=1),
                room_ids=[ROOM_1, ROOM_2],
            )
        ).select(
            apella_schema.ApellaCaseConnection.edges.select(
                apella_schema.ApellaCaseEdge.node.select(
                    apella_schema.ApellaCase.status.select(apella_schema.ApellaCaseStatus.name),
                )
            )
        )
        results = await client.execute_apella_query_async(
            apella_cases_gql_query,
            context_value=context,
        )
        cases = [result.node for result in results.apella_cases.edges]

        # Assert only the events during the live cases are passed in
        assert len(cases) == 2
        context.apella_case_service.get_apella_case_status.assert_any_call(
            case=live_apella_case_in_room_1, observations=[], events=events_in_room_1_during_case
        )
        context.apella_case_service.get_apella_case_status.assert_any_call(
            case=live_apella_case_in_room_2, observations=[], events=events_in_room_2_during_case
        )
