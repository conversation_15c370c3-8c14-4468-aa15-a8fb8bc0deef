from dataclasses import dataclass

from api_server.app_service_provider.app_service_provider import AppServicesBuilder


class A:
    pass


@dataclass
class B:
    a: A
    val: int


class TestNotificationsSent:
    def test_app_level_provider(self) -> None:
        app_container = AppServicesBuilder([])

        first_a = app_container.provide_app_scope_type(A)
        second_a = app_container.provide_app_scope_type(A)
        assert first_a == second_a

    def test_request_level_provider(self) -> None:
        app_container = AppServicesBuilder([])

        first_a = app_container.run(A)
        second_a = app_container.run(A)
        assert first_a != second_a

    def test_instance_variables_properly_scoped(self) -> None:
        app_container = AppServicesBuilder([])
        base_a = A()

        app_container.add_simple_app_provider(base_a, A)
        first_b = app_container.run(B, values={int: 1})
        second_b = app_container.run(B)
        assert first_b != second_b
        assert first_b.a == second_b.a
