import j<PERSON>
import base64
from typing import Any, Generator, Optional
import pytest
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.testclient import TestClient
from starlette.datastructures import Headers
from apella_cloud_api.exceptions import ClientError

from unittest.mock import AsyncMock, MagicMock, patch
from api_server.services.block.rest.block_endpoints import (
    blocks_api_router,
    process_block_release_csv_upload,
)


@pytest.fixture
def client() -> Generator[TestClient, None, None]:
    app = FastAPI()
    app.include_router(blocks_api_router)

    with TestClient(app) as client:
        yield client


@pytest.fixture
def file_content() -> Generator[str, None, None]:
    yield """Release ID,Release Type,Location,Room,From Block Type,From Block,To Block,Release Date,Release Time,Release Length,Release Reason,Block Date,Release User,To Block Type,Release Comments,Days Prior,Release Start Time,Release End Time
87809,Manual Block Release,HMH OPC 19 OR,OPC18OR-17 (VOPC19OR),<PERSON><PERSON>,"<PERSON><PERSON>, Co<PERSON><PERSON>, MD",Orthopedics,4/9/2024,8:38,570,Other,4/22/2024,"SMITH, TAMIKA",Service,,13,4/22/24 7:30,4/22/24 17:00"""


@pytest.fixture
def request_body() -> Generator[dict[str, Any], None, None]:
    data = {
        "kind": "storage#object",
        "id": "dev-customer-block-release-data/apella/block_releases/HM_ORBLOCKRELEASE.csv/1713295307416664",
        "selfLink": "https://www.googleapis.com/storage/v1/b/dev-customer-block-release-data/o/apella%2F/block_releases/HM_ORBLOCKRELEASEs.csv",
        "name": "hmh/hmh/block_releases/HM_ORBLOCKRELEASE.csv",
        "bucket": "dev-customer-block-release-data",
        "generation": "1713295307416664",
        "metageneration": "1",
        "contentType": "text/csv",
        "timeCreated": "2024-04-16T19:21:47.431Z",
        "updated": "2024-04-16T19:21:47.431Z",
        "storageClass": "STANDARD",
        "timeStorageClassUpdated": "2024-04-16T19:21:47.431Z",
        "size": "9240",
        "md5Hash": "bkX+depEO6b4c5qYKVE+Cg==",
        "mediaLink": "https://storage.googleapis.com/download/storage/v1/b/dev-customer-block-release-data/o/apella%2FHM_OR_Block_Releases.csv?generation=1713295307416664&alt=media",
        "crc32c": "yjncHQ==",
        "etag": "CNjIxrG6x4UDEAE=",
    }
    # Convert the dictionary to a JSON string
    json_str = json.dumps(data)

    # Encode the JSON string to bytes
    bytes_to_encode = json_str.encode("utf-8")

    encoded_data_str = base64.b64encode(bytes_to_encode).decode("utf-8")

    yield {
        "message": {
            "data": encoded_data_str,
            "messageId": "2070443601311540",
            "message_id": "2070443601311540",
            "publishTime": "2021-02-26T19:13:55.749Z",
            "publish_time": "2021-02-26T19:13:55.749Z",
            "attributes": {
                "orgId": "houston_methodist",
            },
        },
        "subscription": "projects/myproject/subscriptions/mysubscription",
    }


@pytest.fixture
def bad_request_body() -> Generator[dict[str, Any], None, None]:
    data = {
        "kind": "storage#object",
        "id": "dev-customer-block-release-data/apella/HM_OR_Block_Releases.csv/1713295307416664",
        "iam": "missing md5 prop",
    }
    # Convert the dictionary to a JSON string
    json_str = json.dumps(data)

    # Encode the JSON string to bytes
    bytes_to_encode = json_str.encode("utf-8")

    encoded_data_str = base64.b64encode(bytes_to_encode).decode("utf-8")

    yield {
        "message": {
            "data": encoded_data_str,
            "messageId": "2070443601311540",
            "message_id": "2070443601311540",
            "publishTime": "2021-02-26T19:13:55.749Z",
            "publish_time": "2021-02-26T19:13:55.749Z",
        },
        "subscription": "projects/myproject/subscriptions/mysubscription",
    }


@pytest.fixture
def request_body_for_irrelevant_org() -> Generator[dict[str, Any], None, None]:
    data = {
        "kind": "storage#object",
        "id": "dev-customer-block-release-data/nyu/NYU_file.csv/1713295307416664",
        "selfLink": "https://www.googleapis.com/storage/v1/b/dev-customer-block-release-data/o/nyu%2FNYU_file.csv",
        "name": "nyu/nyu/NYU_file.csv",
        "bucket": "dev-customer-block-release-data",
        "generation": "1713295307416664",
        "metageneration": "1",
        "contentType": "text/csv",
        "timeCreated": "2024-04-16T19:21:47.431Z",
        "updated": "2024-04-16T19:21:47.431Z",
        "storageClass": "STANDARD",
        "timeStorageClassUpdated": "2024-04-16T19:21:47.431Z",
        "size": "9240",
        "md5Hash": "bkX+depEO6b4c5qYKVE+Cg==",
        "mediaLink": "https://storage.googleapis.com/download/storage/v1/b/dev-customer-block-release-data/o/nyu%2FNYU_file.csv?generation=1713295307416664&alt=media",
        "crc32c": "yjncHQ==",
        "etag": "CNjIxrG6x4UDEAE=",
    }
    # Convert the dictionary to a JSON string
    json_str = json.dumps(data)

    # Encode the JSON string to bytes
    bytes_to_encode = json_str.encode("utf-8")

    encoded_data_str = base64.b64encode(bytes_to_encode).decode("utf-8")

    yield {
        "message": {
            "data": encoded_data_str,
            "messageId": "2070443601311540",
            "message_id": "2070443601311540",
            "publishTime": "2021-02-26T19:13:55.749Z",
            "publish_time": "2021-02-26T19:13:55.749Z",
            "attributes": {
                "orgId": "nyu",
            },
        },
        "subscription": "projects/myproject/subscriptions/mysubscription",
    }


async def mock_receive(body: bytes) -> dict[str, Any]:
    """
    Asynchronous function to simulate receiving request body.
    """
    return {"type": "http.request", "body": body}


def create_mock_request(
    method: str, url: str, headers: Optional[dict[str, Any]] = None, body: Optional[bytes] = None
) -> Request:
    """
    Create a mock FastAPI Request object.

    :param method: HTTP method (e.g., 'GET', 'POST')
    :param url: URL for the request
    :param headers: Optional headers for the request
    :param body: Optional body for the request
    :return: Mock FastAPI Request object
    """
    scope = {
        "type": "http",
        "method": method,
        "path": url,
        "headers": Headers(headers or {}).raw,
    }
    request = Request(scope, receive=lambda: mock_receive(body or b""))
    return request


@pytest.mark.asyncio
@patch("api_server.services.block.rest.block_endpoints.BlockRouter")
async def test_process_block_release_csv_upload_success(
    mock_router: MagicMock,
    request_body: dict[str, Any],
) -> None:
    # Mock the BlockService functionality
    mock_block_release_processing_service = MagicMock()
    mock_block_release_processing_service.save_and_process_file = AsyncMock()

    mock_router.route.return_value = mock_block_release_processing_service

    request = create_mock_request(
        method="POST",
        url="/blocks/releases",
        headers={"Content-Type": "application/json"},
        body=json.dumps(request_body).encode("utf-8"),
    )

    response = await process_block_release_csv_upload(request, mock_router)

    assert response == {"message": "File processed"}
    mock_block_release_processing_service.save_and_process_file.assert_called_once()


@pytest.mark.asyncio
@patch("api_server.services.block.rest.block_endpoints.BlockRouter")
async def test_process_block_release_csv_upload_file_processed(
    mock_router: MagicMock,
    request_body: dict[str, Any],
) -> None:
    # Mock the BlockService functionality
    mock_block_release_processing_service = MagicMock()
    mock_block_release_processing_service.save_and_process_file = AsyncMock()

    mock_router.route.return_value = mock_block_release_processing_service

    request = create_mock_request(
        method="POST",
        url="/blocks/releases",
        headers={"Content-Type": "application/json"},
        body=json.dumps(request_body).encode("utf-8"),
    )

    response = await process_block_release_csv_upload(request, mock_router)

    assert response == {"message": "File processed"}
    mock_block_release_processing_service.save_and_process_file.assert_called_once()


@pytest.mark.asyncio
async def test_process_block_release_csv_upload_bad_request_missing_md5_hash(
    bad_request_body: dict[str, Any],
) -> None:
    mock_router = MagicMock()
    with pytest.raises(ClientError, match="md5 hash is required"):
        request = create_mock_request(
            method="POST",
            url="/blocks/releases",
            headers={"Content-Type": "application/json"},
            body=json.dumps(bad_request_body).encode("utf-8"),
        )

        await process_block_release_csv_upload(request, mock_router)


@pytest.mark.asyncio
async def test_process_block_release_csv_upload_bad_request_missing_request_body() -> None:
    mock_router = MagicMock()
    with pytest.raises(ClientError, match="Invalid request data"):
        request = create_mock_request(
            method="POST",
            url="/blocks/releases",
            headers={"Content-Type": "application/json"},
            body=json.dumps({}).encode("utf-8"),
        )

        await process_block_release_csv_upload(request, mock_router)


@pytest.mark.asyncio
@patch("api_server.services.block.rest.block_endpoints.BlockRouter")
async def test_process_csv_upload_for_irrelevant_orgs_results_in_no_op(
    mock_router: MagicMock,
    request_body_for_irrelevant_org: dict[str, Any],
) -> None:
    # Mock the BlockService functionality
    mock_block_release_processing_service = MagicMock()
    mock_block_release_processing_service.save_and_process_file = AsyncMock()

    mock_router.route.return_value = mock_block_release_processing_service

    request = create_mock_request(
        method="POST",
        url="/blocks/releases",
        headers={"Content-Type": "application/json"},
        body=json.dumps(request_body_for_irrelevant_org).encode("utf-8"),
    )

    response = await process_block_release_csv_upload(request, mock_router)

    assert response == {"message": "File processed"}
    mock_block_release_processing_service.save_and_process_file.assert_not_called()
