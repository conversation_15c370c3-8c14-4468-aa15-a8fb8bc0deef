# mypy: allow-untyped-defs
# Skip trailing whitespace check
# ruff: noqa: W291

import pytest

from unittest.mock import MagicMock, patch, AsyncMock

from api_server.services.block.block_models import BlockTypes, ReleaseTypes
from api_server.services.block.block_release_adapters.block_release_base_adapter import (
    DEFAULT_MANUAL_SOURCE,
    BATCH_RELEASE_DEFAULT_SOURCE,
)
from api_server.services.block.block_release_adapters.hf_adapter import BlockReleaseHFAdapter
from api_server.services.block.block_release_adapters.hmh_adapter import BlockReleaseHMHAdapter
from api_server.services.block.block_release_adapters.tgh_adapter import BlockReleaseTGHAdapter
from api_server.services.ehr_interfaces.mapping_store import RoomInfoDto
from api_server.services.site.site_store import Site as SiteModel


@pytest.fixture
def hmh_file_content():
    yield """Release ID,Release Type,Location,Room,From Block Type,From Block,To Block,Release Date,Release Time,Release Length,Release Reason,Block Date,Release User,To Block Type,Release Comments,Days Prior,Release Start Time,Release End Time
87809,Manual Block Release,HMH OPC 19 OR,OPC18OR-17 (VOPC19OR),surgeon,"<PERSON>fi, Comron, MD",Orthopedics,04/09/2024,838,570,Other,04/22/2024,"",Service,,13,4/22/2024 0730,4/22/2024 1700
87809,Manual Block Release,HMH OPC 19 OR,OPC18OR-18 (VOPC19OR),Surgeon,"Saifi, Comron, MD",Orthopedics,04/10/2024,2015,240,Released by batch,04/23/2024,"",Service,,13,4/23/2024 0700,4/23/2024 1100
87809,Manual Block Release,HMH HMW OR,HMWOR-01,testing,"Saifi, Comron, MD",Neurology,04/12/2024,2015,240,Other,04/23/2024,"SMITH, TAMIKA",Service,,13,4/23/2024 0700,4/23/2024 1100
87809,Manual Block Release,HMH OPC 19 OR,OPC18OR-18 (VOPC19OR),Surgeon,"Saifi, Comron, MD",Orthopedics,04/12/2024,2015,240,Other,04/23/2024,"SMITH, TAMIKA",Service,,13,4/23/2024 7:00,4/23/2024 1100
87809,Manual Block Release,HMH OPC 19 OR,OPC18OR-18 (VOPC19OR),Surgeon,"Saifi, Comron, MD",Orthopedics,04/12/2024,2015,240,Other,04/23/2024,"SMITH, TAMIKA",Service,,13,4/23/2024 7:00,4/23/2024 11:00"""


@pytest.fixture
def hmh_bad_file_content():
    yield """Release ID,Release Type,Location,Room,From Block Type,From Block,To Block,Release Date,Release Time,Release Length,Release Reason,Block Date,Release User,To Block Type,Release Comments,Days Prior,Release Start Time,Release End Time
87809,Manual Block Release,HMH OPC 19 OR,OPC18OR-17 (VOPC19OR),Surgeon,"Saifi, Comron, MD",Orthopedics,04/09/2024,838,570,Other,04/22/2024,"ab",Service,,13,4/22/2024 0730,4/22/2024 1700"""


@pytest.fixture
def hf_file_content():
    yield """,,,,,,,,,
Block Release Reason,,,,,,,,,
,,,,,,,,,
,,,,,,,,,
,,Selection Criteria:,,,,,,,
,,,,,,,,,
,,Block ID:,,<ALL>,,,,,
,,From Date:,,Sep-01-2023,,,,,
,,To Date:,,Mar-31-2024,,,,,
,,Total by Reason:,,NOT SELECTED,,,,,
,,Report Date:,,12/20/23,,,,,
,,,,,,,,,
,,,,,,,,,
Block Name,OR Room #,Release Reason,,Minutes Released,Block Date,Released By,Released On,DOW,Week of Month
Raup,,Provider Request,,570,Sep-01-2023,"Aquino, Sabrina",Aug-03-2023,Fri,1
Amani,3,Provider Request,,450,Sep-04-2023,"Aquino, Sabrina",Aug-14-2023,Mon,1
Stephens,1,Provider Request,,570,Sep-05-2023,"Aquino, Sabrina",Jul-26-2023,Tue,1
Stephens,3,Provider Request,,570,Sep-05-2023,"Aquino, Sabrina",Jul-26-2023,Tue,1"""


@pytest.fixture
def tgh_file_content():
    yield (
        """"record_id","template_modification_type","release_days_in_advance","room_id","or_location","modification_instant","modification_user_id","modification_user","from_block_type","from_block_id","from_block","from_block_record_name","to_block_type","to_block_id","to_block","to_block_record_name","block_start_instant","block_end_instant","template_date","release_reason","unavailable_reason","release_comments","request_range","request_declined","request_declined_comments","request_declined_by_user"
"238256","Automatic Block Release","7","2008348","TGH ENDOSCOPY CENTER OR 03","2024-01-01 07:30:03","","John, Doe","Group","90","199","USF GI OP","Group","","","","2024-01-08 17:00:00","2024-01-08 17:30:00","2024-01-08 00:00:00","","","Released by batch","","","",""
"238257","Automatic Block Release","7","2008349","TGH ENDOSCOPY CENTER OR 04","2024-01-01 07:30:03","","Jane, Doe","Group","87","198","USF BARIATRICS ENDO","Group","","","","2024-01-08 15:10:00","2024-01-08 17:30:00","2024-01-08 00:00:00","","","Released by batch","","","",""
"238264","Automatic Block Release","3","R000104","TGH CARDIAC OR 9","2024-01-01 07:30:03","","Block, Alex","Group","15","13","USF VASCULAR","Group","","","","2024-01-04 13:15:00","2024-01-04 17:30:00","2024-01-04 00:00:00","","Surgical Room Consolidation","Manually released as a room close","","","",""
"238263","Automatic Block Release","2","R000102","TGH CARDIAC OR 7","2024-01-01 07:30:03","","Apella Bot","Group","22","16","USF CT SURGERY","Group","","","","2024-01-03 14:50:00","2024-01-03 17:30:00","2024-01-03 00:00:00","","","Released by batch","","","",""
"""
    )


@patch("api_server.services.block.block_release_adapters.hf_adapter.SiteStore")
@patch("api_server.services.block.block_release_adapters.hf_adapter.MappingStore")
async def test_transform_health_first_csv(
    mock_mapping_store,
    mock_site_store,
    hf_file_content,
):
    populate_room_info_mapping = AsyncMock()
    populate_room_info_mapping.return_value = {
        "1": RoomInfoDto(
            room_id="HF-VH02-OR01",
            site_id="HF-VH02",
            timezone="America/New_York",
        )
    }
    mock_mapping_store_instance = MagicMock()
    mock_mapping_store_instance.populate_room_info_mapping = populate_room_info_mapping
    mock_mapping_store.return_value = mock_mapping_store_instance

    query_sites = AsyncMock()
    query_sites.return_value = [
        SiteModel(
            id="HF-VH02",
            name="VH02",
            timezone="America/New_York",
            org_id="health_first",
        ),
    ]
    mock_site_store_instance = MagicMock()
    mock_site_store_instance.query_sites = query_sites
    mock_site_store.return_value = mock_site_store_instance

    data = await BlockReleaseHFAdapter.transform(hf_file_content, "testing.csv")

    assert len(data.releases) == 4
    assert data.org_id == "health_first"

    assert data.releases[0].block_name == "Raup"
    assert data.releases[0].room_name == ""
    assert data.releases[0].room_id is None
    assert data.releases[0].rejected_reason is not None

    assert data.releases[1].block_name == "Amani"
    assert data.releases[1].room_name == "3"
    assert data.releases[1].room_id is None
    assert data.releases[1].rejected_reason is not None

    assert data.releases[2].block_name == "Stephens"
    assert data.releases[2].block_date.isoformat() == "2023-09-05T00:00:00-04:00"
    assert data.releases[2].room_name == "1"
    assert data.releases[2].room_id == "HF-VH02-OR01"
    assert data.releases[2].release_length == 570
    assert data.releases[2].released_by == "Aquino, Sabrina"
    assert data.releases[2].released_time.isoformat() == "2023-07-26T00:00:00-04:00"
    assert data.releases[2].rejected_reason is None

    assert data.releases[3].block_name == "Stephens"
    assert data.releases[3].room_name == "3"
    assert data.releases[1].room_id is None
    assert data.releases[1].rejected_reason is not None


@patch("api_server.services.block.block_release_adapters.hmh_adapter.SiteStore")
@patch("api_server.services.block.block_release_adapters.hmh_adapter.MappingStore")
async def test_transform_hmh_csv(
    mock_mapping_store,
    mock_site_store,
    hmh_file_content,
):
    populate_room_info_mapping = AsyncMock()
    populate_room_info_mapping.return_value = {
        "OPC18OR-17 (VOPC19OR)": RoomInfoDto(
            room_id="HMH-OPC18-OR17",
            site_id="HMH-OPC18",
            timezone="America/Chicago",
        ),
        "OPC18OR-18 (VOPC19OR)": RoomInfoDto(
            room_id="HMH-OPC18-OR18",
            site_id="HMH-OPC18",
            timezone="America/Chicago",
        ),
        "HMWOR-01": RoomInfoDto(
            room_id="HMH-HMW-OR01",
            site_id="HMH-HMW",
            timezone="America/Chicago",
        ),
    }
    mock_mapping_store_instance = MagicMock()
    mock_mapping_store_instance.populate_room_info_mapping = populate_room_info_mapping
    mock_mapping_store.return_value = mock_mapping_store_instance

    query_sites = AsyncMock()
    query_sites.return_value = [
        SiteModel(
            id="HMH-OPC18",
            name="OPC 18",
            timezone="America/Chicago",
            org_id="houston_methodist",
        ),
        SiteModel(
            id="HMH-OPC19",
            name="OPC 19",
            timezone="America/Chicago",
            org_id="houston_methodist",
        ),
        SiteModel(
            id="HMH-HMW",
            name="HMW",
            timezone="America/Chicago",
            org_id="houston_methodist",
        ),
    ]
    mock_site_store_instance = MagicMock()
    mock_site_store_instance.query_sites = query_sites
    mock_site_store.return_value = mock_site_store_instance

    data = await BlockReleaseHMHAdapter.transform(hmh_file_content, "testing.csv")

    assert len(data.releases) == 5
    assert data.org_id == "houston_methodist"

    assert data.releases[0].block_name == "Saifi, Comron, MD"
    assert data.releases[0].block_date.isoformat() == "2024-04-22T00:00:00-05:00"
    assert data.releases[0].room_name == "OPC18OR-17 (VOPC19OR)"
    assert data.releases[0].room_id == "HMH-OPC18-OR17"
    assert data.releases[0].release_length == 570
    assert data.releases[0].released_by == DEFAULT_MANUAL_SOURCE
    assert data.releases[0].released_time.isoformat() == "2024-04-09T08:38:00-05:00"
    assert data.releases[0].to_block == "Orthopedics"
    assert data.releases[0].rejected_reason is None
    assert data.releases[0].from_block_type == BlockTypes.SURGEON_BLOCK_TYPE
    assert data.releases[0].to_block_type == BlockTypes.SERVICE_BLOCK_TYPE
    assert data.releases[0].release_type == ReleaseTypes.MANUAL
    assert data.releases[0].days_prior == 13

    assert data.releases[1].block_name == "Saifi, Comron, MD"
    assert data.releases[1].block_date.isoformat() == "2024-04-23T00:00:00-05:00"
    assert data.releases[1].room_name == "OPC18OR-18 (VOPC19OR)"
    assert data.releases[1].room_id == "HMH-OPC18-OR18"
    assert data.releases[1].release_length == 240
    assert data.releases[1].released_by == BATCH_RELEASE_DEFAULT_SOURCE
    assert data.releases[1].released_time.isoformat() == "2024-04-10T20:15:00-05:00"
    assert data.releases[1].to_block == "Orthopedics"
    assert data.releases[1].rejected_reason is None
    assert data.releases[1].from_block_type == BlockTypes.SURGEON_BLOCK_TYPE
    assert data.releases[1].to_block_type == BlockTypes.SERVICE_BLOCK_TYPE
    assert data.releases[1].release_type == ReleaseTypes.MANUAL
    assert data.releases[1].days_prior == 13

    assert data.releases[2].block_name == "Saifi, Comron, MD"
    assert data.releases[2].room_name == "HMWOR-01"
    assert data.releases[2].room_id == "HMH-HMW-OR01"
    assert data.releases[2].rejected_reason is None
    assert data.releases[2].released_by == "SMITH, TAMIKA"
    assert data.releases[2].to_block == "Neurology"
    assert data.releases[2].from_block_type == BlockTypes.UNKNOWN
    assert data.releases[2].to_block_type == BlockTypes.SERVICE_BLOCK_TYPE
    assert data.releases[2].release_type == ReleaseTypes.MANUAL
    assert data.releases[2].days_prior == 13

    # Release End Time missing `:` sign
    assert (
        data.releases[3].rejected_reason
        == "time data '4/23/2024 7:00' does not match format '%m/%d/%Y %H%M'"
    )

    # Release Length has invalid value
    assert (
        data.releases[4].rejected_reason
        == "time data '4/23/2024 7:00' does not match format '%m/%d/%Y %H%M'"
    )


@patch("api_server.services.block.block_release_adapters.hmh_adapter.SiteStore")
@patch("api_server.services.block.block_release_adapters.hmh_adapter.MappingStore")
async def test_transform_hmh_csv_checks_when_source_len_is_less_than_three_chars(
    mock_mapping_store,
    mock_site_store,
    hmh_bad_file_content,
):
    populate_room_info_mapping = AsyncMock()
    populate_room_info_mapping.return_value = {
        "OPC18OR-17 (VOPC19OR)": RoomInfoDto(
            room_id="HMH-OPC18-OR17",
            site_id="HMH-OPC18",
            timezone="America/Chicago",
        ),
        "OPC18OR-18 (VOPC19OR)": RoomInfoDto(
            room_id="HMH-OPC18-OR18",
            site_id="HMH-OPC18",
            timezone="America/Chicago",
        ),
        "HMWOR-01": RoomInfoDto(
            room_id="HMH-HMW-OR01",
            site_id="HMH-HMW",
            timezone="America/Chicago",
        ),
    }
    mock_mapping_store_instance = MagicMock()
    mock_mapping_store_instance.populate_room_info_mapping = populate_room_info_mapping
    mock_mapping_store.return_value = mock_mapping_store_instance

    query_sites = AsyncMock()
    query_sites.return_value = [
        SiteModel(
            id="HMH-OPC18",
            name="OPC 18",
            timezone="America/Chicago",
            org_id="houston_methodist",
        ),
        SiteModel(
            id="HMH-OPC19",
            name="OPC 19",
            timezone="America/Chicago",
            org_id="houston_methodist",
        ),
        SiteModel(
            id="HMH-HMW",
            name="HMW",
            timezone="America/Chicago",
            org_id="houston_methodist",
        ),
    ]
    mock_site_store_instance = MagicMock()
    mock_site_store_instance.query_sites = query_sites
    mock_site_store.return_value = mock_site_store_instance

    data = await BlockReleaseHMHAdapter.transform(hmh_bad_file_content, "testing.csv")

    assert len(data.releases) == 1
    assert data.org_id == "houston_methodist"
    assert data.releases[0].rejected_reason == "Source less than 3 characters"


@patch("api_server.services.block.block_release_adapters.tgh_adapter.SiteStore")
@patch("api_server.services.block.block_release_adapters.tgh_adapter.MappingStore")
async def test_transform_tampa_general_csv(
    mock_mapping_store,
    mock_site_store,
    tgh_file_content,
):
    populate_room_info_mapping = AsyncMock()
    populate_room_info_mapping.return_value = {
        "TGH ENDOSCOPY CENTER OR 03": RoomInfoDto(
            room_id="TGH-MAIN02-OR03",
            site_id="TGH-MAIN02",
            timezone="America/New_York",
        ),
        "TGH ENDOSCOPY CENTER OR 04": RoomInfoDto(
            room_id="TGH-MAIN02-OR04",
            site_id="TGH-MAIN02",
            timezone="America/New_York",
        ),
        "TGH CARDIAC OR 7": RoomInfoDto(
            room_id="TGH-CVTOR03-OR07",
            site_id="TGH-CVTOR03",
            timezone="America/New_York",
        ),
        "TGH CARDIAC OR 9": RoomInfoDto(
            room_id="TGH-CVTOR03-OR09",
            site_id="TGH-CVTOR03",
            timezone="America/New_York",
        ),
    }
    mock_mapping_store_instance = MagicMock()
    mock_mapping_store_instance.populate_room_info_mapping = populate_room_info_mapping
    mock_mapping_store.return_value = mock_mapping_store_instance

    query_sites = AsyncMock()
    query_sites.return_value = [
        SiteModel(
            id="TGH-MAIN02",
            name="Main 02",
            timezone="America/New_York",
            org_id="tampa_general",
        ),
        SiteModel(
            id="TGH-CVTOR03",
            name="CVTOR 03",
            timezone="America/New_York",
            org_id="tampa_general",
        ),
    ]
    mock_site_store_instance = MagicMock()
    mock_site_store_instance.query_sites = query_sites
    mock_site_store.return_value = mock_site_store_instance

    tgh_transformer = BlockReleaseTGHAdapter

    data = await tgh_transformer.transform(tgh_file_content, "testing.csv")

    assert data.org_id == "tampa_general"
    assert len(data.releases) == 4

    assert data.releases[0].block_name == "USF GI OP"
    assert data.releases[0].block_date.isoformat() == "2024-01-08T17:00:00-05:00"
    assert data.releases[0].room_id == "TGH-MAIN02-OR03"
    assert data.releases[0].room_name == "TGH ENDOSCOPY CENTER OR 03"
    assert data.releases[0].release_length == 30
    assert data.releases[0].released_by == "John, Doe"
    assert data.releases[0].released_time.isoformat() == "2024-01-01T07:30:03-05:00"
    assert data.releases[0].release_reason == "Released by batch"
    assert data.releases[0].from_block_type == BlockTypes.GROUP_BLOCK_TYPE
    assert data.releases[0].to_block_type == BlockTypes.GROUP_BLOCK_TYPE
    assert data.releases[0].release_type == ReleaseTypes.AUTO
    assert data.releases[0].days_prior == 7

    assert data.releases[1].block_name == "USF BARIATRICS ENDO"
    assert data.releases[1].block_date.isoformat() == "2024-01-08T15:10:00-05:00"
    assert data.releases[1].room_id == "TGH-MAIN02-OR04"
    assert data.releases[1].room_name == "TGH ENDOSCOPY CENTER OR 04"
    assert data.releases[1].release_length == 140
    assert data.releases[1].released_by == "Jane, Doe"
    assert data.releases[1].released_time.isoformat() == "2024-01-01T07:30:03-05:00"
    assert data.releases[1].release_reason == "Released by batch"
    assert data.releases[1].from_block_type == BlockTypes.GROUP_BLOCK_TYPE
    assert data.releases[1].to_block_type == BlockTypes.GROUP_BLOCK_TYPE
    assert data.releases[1].release_type == ReleaseTypes.AUTO
    assert data.releases[1].days_prior == 7

    assert data.releases[2].block_name == "USF VASCULAR"
    assert data.releases[2].block_date.isoformat() == "2024-01-04T13:15:00-05:00"
    assert data.releases[2].room_id == "TGH-CVTOR03-OR09"
    assert data.releases[2].room_name == "TGH CARDIAC OR 9"
    assert data.releases[2].release_length == 255
    assert data.releases[2].released_by == "Block, Alex"
    assert data.releases[2].released_time.isoformat() == "2024-01-01T07:30:03-05:00"
    assert (
        data.releases[2].release_reason
        == "Surgical Room Consolidation | Manually released as a room close"
    )
    assert data.releases[2].from_block_type == BlockTypes.GROUP_BLOCK_TYPE
    assert data.releases[2].to_block_type == BlockTypes.GROUP_BLOCK_TYPE
    assert data.releases[2].release_type == ReleaseTypes.AUTO
    assert data.releases[2].days_prior == 3

    assert data.releases[3].block_name == "USF CT SURGERY"
    assert data.releases[3].block_date.isoformat() == "2024-01-03T14:50:00-05:00"
    assert data.releases[3].room_id == "TGH-CVTOR03-OR07"
    assert data.releases[3].room_name == "TGH CARDIAC OR 7"
    assert data.releases[3].release_length == 160
    assert data.releases[3].released_by == "Apella Bot"
    assert data.releases[3].released_time.isoformat() == "2024-01-01T07:30:03-05:00"
    assert data.releases[3].release_reason == "Released by batch"
    assert data.releases[3].from_block_type == BlockTypes.GROUP_BLOCK_TYPE
    assert data.releases[3].to_block_type == BlockTypes.GROUP_BLOCK_TYPE
    assert data.releases[3].release_type == ReleaseTypes.AUTO
    assert data.releases[3].days_prior == 2
