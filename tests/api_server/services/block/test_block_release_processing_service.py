# mypy: allow-untyped-defs

from datetime import date, datetime, timedelta, timezone
from unittest.mock import AsyncMock, MagicMock, call, patch
from uuid import uuid4

import pytest

from api_server.services.block.block_models import (
    BlockReleaseDataInput,
    BlockReleaseFileRowModel,
    BlockReleaseProcessedFileModel,
    BlockReleaseBulkCreateDataInput,
    BlockTypes,
    BlockProcessingStatus,
)
from api_server.services.block.block_release_adapters.hmh_adapter import BlockReleaseHMHAdapter
from api_server.services.block.block_release_processing_service import BlockReleaseProcessingService
from api_server.services.block.block_slack_notifier import BlockSlackNotifier
from api_server.services.closures.closure_service import ClosureService
from api_server.services.site.site_service import SiteService
from auth.auth import Auth


@pytest.fixture
def hmh_file_content():
    yield """Release ID,Release Type,Location,Room,From Block Type,From Block,To Block,Release Date,Release Time,Release Length,Release Reason,Block Date,Release User,To Block Type,Release Comments,Days Prior,Release Start Time,Release End Time
87809,Manual Block Release,HMH OPC 19 OR,Room 0,Unblocked,"Abc",Unavailable,04/09/2024,838,570,testing,04/22/2024,Ami,Unavailable,,13,4/22/2024 0730,4/22/2024 1700
"""


@pytest.fixture
def mock_auth() -> MagicMock:
    mock_auth = MagicMock(spec=Auth)
    mock_auth.get_calling_user_id = MagicMock(return_value="current_user_id")
    return mock_auth


@pytest.fixture
def closure_service(mock_auth: MagicMock) -> ClosureService:
    return ClosureService(
        auth=mock_auth,
        closure_store=MagicMock(),
        site_service=MagicMock(spec=SiteService),
    )


@pytest.fixture
def block_release_processor_service(
    mock_auth: MagicMock,
    closure_service: ClosureService,
) -> BlockReleaseProcessingService:
    return BlockReleaseProcessingService(
        auth=mock_auth,
        block_service=AsyncMock(),
        closure_service=closure_service,
        block_csv_asset_store=MagicMock(),
        block_release_file_store=AsyncMock(),
    )


@patch.object(BlockSlackNotifier, "log_error", new_callable=AsyncMock)
@patch.object(BlockSlackNotifier, "report", new_callable=AsyncMock)
@pytest.mark.asyncio
async def test_reprocessing_block_releases(
    mock_log_error, mock_report, hmh_file_content, block_release_processor_service
):
    released_time = datetime(2023, 7, 1, 9, 0, 0, tzinfo=timezone.utc)
    file_name = "apella/block_releases/Apella_OR_Block_20250312.csv"
    bucket = "testing-bucket"
    release_input = BlockReleaseDataInput(
        id=uuid4(),
        release_reason="testing",
        released_by="Ami",
        block_name="Unblocked",
        block_date=released_time,
        to_block="Unavailable",
        to_block_type=BlockTypes.UNAVAILABLE_BLOCK_TYPE,
        from_block_type=BlockTypes.UNBLOCKED_BLOCK_TYPE,
        room_id="room-id-0",
        room_name="Room 0",
        release_start_time=released_time,
        release_end_time=released_time,
        released_time=released_time,
        timezone="local",
        row_number=1,
    )
    file = BlockReleaseProcessedFileModel(
        id=uuid4(),
        file_name=file_name,
        bucket_name=bucket,
        generation="1741827603371226",
        size="1000",
        md5_hash="abc",
        media_link="gs://" + bucket + "/" + file_name,
        org_id="testing",
    )
    block_release_processor_service.block_release_file_store.get_processed_files = AsyncMock(
        return_value=[file]
    )
    block_release_processor_service.block_csv_asset_store.download_csv_file_as_text = AsyncMock(
        return_value=hmh_file_content
    )
    block_release_processor_service.save_transformed_block_release_rows = AsyncMock(
        return_value=[
            BlockReleaseFileRowModel(
                org_id=file.org_id,
                block_release_file_id=file.id,
                row_number=release_input.row_number,
                external_id=release_input.external_id,
                site_id=release_input.site_id,
                room_id=release_input.room_id,
                room_name=release_input.room_name,
                timezone=release_input.timezone,
                start_time=release_input.release_start_time,
                end_time=release_input.release_end_time,
                release_length=release_input.release_length,
                release_reason=release_input.release_reason,
                released_time=release_input.released_time,
                released_by=release_input.released_by,
                release_type=release_input.release_type,
                days_prior=release_input.days_prior,
                block_name=release_input.block_name,
                block_date=release_input.block_date,
                to_block_name=release_input.to_block,
                from_block_type=release_input.from_block_type,
                to_block_type=release_input.to_block_type,
                rejected_reason=release_input.rejected_reason,
                processing_status=BlockProcessingStatus.PENDING
                if release_input.rejected_reason is None or release_input.rejected_reason == ""
                else BlockProcessingStatus.REJECTED,
            )
        ]
    )
    hmh_adapter = BlockReleaseHMHAdapter
    hmh_adapter.transform = AsyncMock(
        return_value=BlockReleaseBulkCreateDataInput(
            releases=[release_input],
            org_id="apella",
            timezone="local",
        )
    )
    block_release_processor_service.get_block_release_adapter = MagicMock(return_value=hmh_adapter)

    block_release_processor_service.process_releases = AsyncMock()

    await block_release_processor_service.bulk_reprocess_block_releases(
        start_date=released_time.date(),
        end_date=released_time.date(),
        org_id="apella",
    )

    block_release_processor_service.process_releases.assert_called_once()
    hmh_adapter.transform.assert_called_once()
    mock_report.assert_called_once()
    mock_log_error.assert_not_called()


@pytest.mark.asyncio
async def test_transform_and_save_release_files(
    hmh_file_content, block_release_processor_service, mock_auth
):
    released_time = datetime(2023, 7, 1, 9, 0, 0, tzinfo=timezone.utc)
    file_name = "apella/block_releases/Apella_OR_Block_20250312.csv"
    bucket = "testing-bucket"
    block_release_processor_service.block_release_file_store.get_processed_files = AsyncMock(
        return_value=[
            BlockReleaseProcessedFileModel(
                id=uuid4(),
                file_name=file_name,
                bucket_name=bucket,
                generation="1741827603371226",
                size="1000",
                md5_hash="abc",
                media_link="gs://" + bucket + "/" + file_name,
            )
        ]
    )
    block_release_processor_service.block_csv_asset_store.download_csv_file_as_text = AsyncMock(
        return_value=hmh_file_content
    )
    hmh_adapter = BlockReleaseHMHAdapter
    hmh_adapter.transform = AsyncMock(
        return_value=BlockReleaseBulkCreateDataInput(
            releases=[
                BlockReleaseDataInput(
                    id=uuid4(),
                    release_reason="testing",
                    released_by="Ami",
                    block_name="Unblocked",
                    block_date=released_time,
                    to_block="Unavailable",
                    to_block_type=BlockTypes.UNAVAILABLE_BLOCK_TYPE,
                    from_block_type=BlockTypes.UNBLOCKED_BLOCK_TYPE,
                    room_id="room-id-0",
                    room_name="Room 0",
                    release_start_time=released_time,
                    release_end_time=released_time,
                    released_time=released_time,
                    timezone="local",
                ),
            ],
            org_id="apella",
            timezone="local",
        )
    )
    block_release_processor_service.get_block_release_adapter = MagicMock(return_value=hmh_adapter)

    await block_release_processor_service.transform_and_save_release_files(
        start_date=released_time.date(),
        end_date=released_time.date(),
        org_id="apella",
    )

    block_release_processor_service.block_csv_asset_store.download_csv_file_as_text.assert_called_once()
    block_release_processor_service.block_release_file_store.get_processed_files.assert_called_once()
    block_release_processor_service.get_block_release_adapter.assert_called_once()
    hmh_adapter.transform.assert_called_once()


# test that we can process a date range by querying for csv rows and then processing them
@pytest.mark.asyncio
async def test_process_block_releases_for_date_range(
    block_release_processor_service,
):
    released_time = datetime(2023, 7, 1, 9, 0, 0, tzinfo=timezone.utc)
    releases = [
        [MagicMock(spec=BlockReleaseFileRowModel)],
        [MagicMock(spec=BlockReleaseFileRowModel)],
        [MagicMock(spec=BlockReleaseFileRowModel)],
    ]
    release_dates = {
        "room-id-0": {
            date.fromisoformat("2023-07-01"): releases[0],
            date.fromisoformat("2023-07-02"): releases[1],
            date.fromisoformat("2023-07-04"): releases[2],
        }
    }
    block_release_processor_service.get_pending_block_release_on_dates_for_rooms = AsyncMock(
        return_value=release_dates
    )
    block_release_processor_service.process_releases = AsyncMock()
    await block_release_processor_service.process_block_releases_for_date_range(
        room_ids=["room-id-0"],
        start_date=released_time.date(),
        end_date=released_time.date() + timedelta(days=4),
    )
    # check that we called the get_block_release_on_date_for_room method for each date
    block_release_processor_service.get_pending_block_release_on_dates_for_rooms.assert_called_once_with(
        room_ids=["room-id-0"],
        start_date=released_time.date(),
        end_date=released_time.date() + timedelta(days=4),
    )
    # check that we called the process_releases method for each date that has releases
    block_release_processor_service.process_releases.assert_has_calls(
        [call(releases=release_list) for release_list in releases]
    )
