from unittest.mock import Mock, patch
import pytest

from api_server.services.block.block_router import BlockRouter


@pytest.mark.asyncio
async def test_block_router_with_mocked_services() -> None:
    # setup
    mock_block_release_service = Mock()
    mock_block_schedule_service = Mock()
    unknown_file = "apella/uploads/block_releases/.directory"

    router = BlockRouter(
        block_release_processing_service=mock_block_release_service,
        block_schedule_processing_service=mock_block_schedule_service,
    )

    # define test cases
    test_cases = [
        ("tgh/block_releases/TGH_OR_Block_20250421.csv", mock_block_release_service),
        (
            "hmh/block_releases/HM_ORBLOCKRELEASE_APELLA_20250421_0200.csv",
            mock_block_release_service,
        ),
        ("tgh/block_releases/TGH_OR_Room_Schedule_20250421.txt", mock_block_schedule_service),
        ("tgh/uploads/block_schedules/some_schedule_file.csv", mock_block_schedule_service),
        (unknown_file, None),
    ]

    # run test cases
    for file_path, expected_service in test_cases:
        result = router.route(file_path)
        assert result == expected_service, f"Failed for file_path: {file_path}"

    with patch("api_server.services.block.block_router.logger.info") as mock_logger_info:
        router.route(unknown_file)
        mock_logger_info.assert_called_once_with(
            f"No processor found for file, {unknown_file}", exc_info=True
        )
