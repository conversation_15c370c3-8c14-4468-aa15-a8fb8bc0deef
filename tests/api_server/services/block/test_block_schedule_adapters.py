import pytest
from unittest.mock import MagicMock, patch, AsyncMock
from datetime import datetime
from zoneinfo import ZoneInfo

from api_server.services.block.block_models import BlockTypes
from api_server.services.block.block_schedule_adapters.tgh_adapter import BlockScheduleTGHAdapter

# mypy: allow-untyped-defs
from api_server.services.ehr_interfaces.mapping_store import RoomInfoDto
from api_server.services.site.site_store import Site as SiteModel


@pytest.fixture
def tgh_file_content():
    yield """or_room|room_id|slot_type|start_time|end_time|surgeon|group_name|block_start_time|block_end_time|block_length_minutes
TGH ENDOSCOPY CENTER OR 03|TGH-MAIN02-OR03|Group|2024-01-08 17:00:00|2024-01-08 17:30:00||USF GI OP|||
TGH ENDOSCOPY CENTER OR 04|TGH-MAIN02-OR04|On Hold|2024-01-08 07:00:00|2024-01-08 11:30:00|||15:00:00.0000000|18:00:00.0000000|180
TGH ENDOSCOPY CENTER OR 04|TGH-MAIN02-OR04|Unblocked|2024-01-08 11:30:00|2024-01-08 15:00:00|||15:00:00.0000000|18:00:00.0000000|180
TGH ENDOSCOPY CENTER OR 04|TGH-MAIN02-OR04|Group|2024-01-08 15:00:00|2024-01-08 15:30:00||USF BARIATRICS ENDO|15:00:00.0000000|18:00:00.0000000|180
TGH ENDOSCOPY CENTER OR 04|TGH-MAIN02-OR04|Group|2024-01-08 15:40:00|2024-01-08 17:30:00||USF BARIATRICS ENDO|15:00:00.0000000|18:00:00.0000000|180
TGH CARDIAC OR 7|TGH-CVTOR03-OR07|Group|2024-01-03 14:50:00|2024-01-03 17:30:00||USF CT SURGERY|14:00:00.0000000|17:30:00.0000000|150
"""


@patch("api_server.services.block.block_schedule_adapters.tgh_adapter.SiteStore")
@patch("api_server.services.block.block_schedule_adapters.tgh_adapter.MappingStore")
async def test_transform_tampa_general_csv(
    mock_mapping_store,
    mock_site_store,
    tgh_file_content,
) -> None:
    populate_room_info_mapping = AsyncMock()
    populate_room_info_mapping.return_value = {
        "TGH ENDOSCOPY CENTER OR 03": RoomInfoDto(
            room_id="TGH-MAIN02-OR03",
            site_id="TGH-MAIN02",
            timezone="America/New_York",
        ),
        "TGH ENDOSCOPY CENTER OR 04": RoomInfoDto(
            room_id="TGH-MAIN02-OR04",
            site_id="TGH-MAIN02",
            timezone="America/New_York",
        ),
        "TGH CARDIAC OR 9": RoomInfoDto(
            room_id="TGH-CVTOR03-OR09",
            site_id="TGH-CVTOR03",
            timezone="America/New_York",
        ),
        "TGH CARDIAC OR 7": RoomInfoDto(
            room_id="TGH-CVTOR03-OR07",
            site_id="TGH-CVTOR03",
            timezone="America/New_York",
        ),
    }
    mock_mapping_store_instance = MagicMock()
    mock_mapping_store_instance.populate_room_info_mapping = populate_room_info_mapping
    mock_mapping_store.return_value = mock_mapping_store_instance

    query_sites = AsyncMock()
    query_sites.return_value = [
        SiteModel(
            id="TGH-MAIN02",
            name="Main 02",
            timezone="America/New_York",
            org_id="tampa_general",
        ),
        SiteModel(
            id="TGH-CVTOR03",
            name="CVTOR 03",
            timezone="America/New_York",
            org_id="tampa_general",
        ),
    ]
    mock_site_store_instance = MagicMock()
    mock_site_store_instance.query_sites = query_sites
    mock_site_store.return_value = mock_site_store_instance

    data = await BlockScheduleTGHAdapter.transform(tgh_file_content, "testing.csv")

    assert len(data) == 5

    assert data[0].room_name == "TGH ENDOSCOPY CENTER OR 03"
    assert data[0].room_id == "TGH-MAIN02-OR03"
    assert data[0].block_name == "USF GI OP"
    assert data[0].block_type == BlockTypes.GROUP_BLOCK_TYPE
    assert data[0].block_start_time == datetime(
        2024, 1, 8, 17, 0, 0, tzinfo=ZoneInfo("America/New_York")
    )
    assert data[0].block_end_time == datetime(
        2024, 1, 8, 17, 30, 0, tzinfo=ZoneInfo("America/New_York")
    )
    assert data[0].rejected_reason is None

    assert data[1].room_name == "TGH ENDOSCOPY CENTER OR 04"
    assert data[1].room_id == "TGH-MAIN02-OR04"
    assert data[1].block_name == ""
    assert data[1].block_type == BlockTypes.ON_HOLD_BLOCK_TYPE
    assert data[1].block_start_time == datetime(
        2024, 1, 8, 7, 00, 0, tzinfo=ZoneInfo("America/New_York")
    )
    assert data[1].block_end_time == datetime(
        2024, 1, 8, 11, 30, 0, tzinfo=ZoneInfo("America/New_York")
    )
    assert data[1].rejected_reason is None

    assert data[2].room_name == "TGH ENDOSCOPY CENTER OR 04"
    assert data[2].room_id == "TGH-MAIN02-OR04"
    assert data[2].block_name == ""
    assert data[2].block_type == BlockTypes.UNBLOCKED_BLOCK_TYPE
    assert data[2].block_start_time == datetime(
        2024, 1, 8, 11, 30, 0, tzinfo=ZoneInfo("America/New_York")
    )
    assert data[2].block_end_time == datetime(
        2024, 1, 8, 15, 00, 0, tzinfo=ZoneInfo("America/New_York")
    )
    assert data[2].rejected_reason is None

    assert data[3].room_name == "TGH ENDOSCOPY CENTER OR 04"
    assert data[3].room_id == "TGH-MAIN02-OR04"
    assert data[3].block_name == "USF BARIATRICS ENDO"
    assert data[3].block_type == BlockTypes.GROUP_BLOCK_TYPE
    assert data[3].block_start_time == datetime(
        2024, 1, 8, 15, 00, 0, tzinfo=ZoneInfo("America/New_York")
    )
    assert data[3].block_end_time == datetime(
        2024, 1, 8, 18, 00, 0, tzinfo=ZoneInfo("America/New_York")
    )
    assert data[3].rejected_reason is None

    assert data[4].room_name == "TGH CARDIAC OR 7"
    assert data[4].room_id == "TGH-CVTOR03-OR07"
    assert data[4].block_name == "USF CT SURGERY"
    assert data[4].block_type == BlockTypes.GROUP_BLOCK_TYPE
    assert data[4].block_start_time == datetime(
        2024, 1, 3, 14, 00, 0, tzinfo=ZoneInfo("America/New_York")
    )
    assert data[4].block_end_time == datetime(
        2024, 1, 3, 17, 30, 0, tzinfo=ZoneInfo("America/New_York")
    )
    assert data[4].rejected_reason is None
