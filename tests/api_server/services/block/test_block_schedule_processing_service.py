# mypy: allow-untyped-defs
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch
from uuid import uuid4

import pytest

from api_server.services.block.block_models import (
    BlockFile,
    BlockScheduleProcessedFileModel,
    BlockTimeDataInput,
)
from api_server.services.block.block_schedule_adapters.tgh_adapter import BlockScheduleTGHAdapter
from api_server.services.block.block_schedule_processing_service import (
    BlockScheduleProcessingService,
)
from api_server.services.block.block_csv_asset_store import BlockCsvAssetStore
from api_server.services.block.block_schedule_file_store import BlockScheduleFileStore
from auth.auth import Auth


@pytest.fixture
def tgh_file_content():
    yield """or_room|room_id|slot_type|start_time|end_time|surgeon|group_name|block_start_time|block_end_time|block_length_minutes
TGH ENDOSCOPY CENTER OR 03|TGH-MAIN02-OR03|Group|2024-01-08 17:00:00|2024-01-08 17:30:00|USF GI OP|USF GI OP|2024-01-08 17:00:00|2024-01-08 17:30:00|30
"""


@pytest.fixture
def block_csv_asset_store() -> MagicMock:
    return MagicMock(spec=BlockCsvAssetStore)


@pytest.fixture
def block_schedule_file_store() -> AsyncMock:
    return AsyncMock(spec=BlockScheduleFileStore)


@pytest.fixture
def auth() -> MagicMock:
    return MagicMock(spec=Auth)


@pytest.fixture
def block_schedule_processing_service(
    block_csv_asset_store: MagicMock, block_schedule_file_store: AsyncMock, auth: MagicMock
) -> BlockScheduleProcessingService:
    return BlockScheduleProcessingService(
        auth=auth,
        block_schedule_file_store=block_schedule_file_store,
        block_csv_asset_store=block_csv_asset_store,
    )


def test_get_block_schedule_adapter_success(
    block_schedule_processing_service: BlockScheduleProcessingService,
) -> None:
    adapter = block_schedule_processing_service.get_block_schedule_adapter("tampa_general")
    assert adapter is BlockScheduleTGHAdapter


def test_get_block_schedule_adapter_failure(
    block_schedule_processing_service: BlockScheduleProcessingService,
) -> None:
    with pytest.raises(Exception):
        block_schedule_processing_service.get_block_schedule_adapter("unknown_org")


@pytest.mark.asyncio
async def test_save_and_process_file_new_file(
    block_schedule_processing_service: BlockScheduleProcessingService,
    block_schedule_file_store: AsyncMock,
) -> None:
    file = MagicMock(spec=BlockFile)
    file.file_name = "test.csv"
    file.bucket_name = "bucket"
    file.generation = 1
    file.size = 100
    file.md5_hash = "md5"
    file.media_link = "media"
    file.org_id = "tampa_general"
    block_schedule_file_store.get_processed_files.return_value = []
    block_schedule_file_store.save_processed_file.return_value = MagicMock(
        spec=BlockScheduleProcessedFileModel
    )
    with patch.object(
        block_schedule_processing_service, "transform_and_save_release_file", new=AsyncMock()
    ):
        result = await block_schedule_processing_service.save_and_process_file(file)
        assert result is not None
        block_schedule_file_store.save_processed_file.assert_awaited_once()


@pytest.mark.asyncio
async def test_save_and_process_file_already_processed(
    block_schedule_processing_service: BlockScheduleProcessingService,
    block_schedule_file_store: AsyncMock,
) -> None:
    file = MagicMock(spec=BlockFile)
    file.file_name = "test.csv"
    file.bucket_name = "bucket"
    file.generation = 1
    file.size = 100
    file.md5_hash = "md5"
    file.media_link = "media"
    file.org_id = "tampa_general"
    processed_file = MagicMock(spec=BlockScheduleProcessedFileModel)
    block_schedule_file_store.get_processed_files.return_value = [processed_file]
    with patch.object(
        block_schedule_processing_service, "transform_and_save_release_file", new=AsyncMock()
    ):
        result = await block_schedule_processing_service.save_and_process_file(file)
        assert result is not None
        block_schedule_file_store.save_processed_file.assert_not_awaited()


@pytest.mark.asyncio
async def test_save_and_process_file_multiple_files_error(
    block_schedule_processing_service: BlockScheduleProcessingService,
    block_schedule_file_store: AsyncMock,
) -> None:
    file = MagicMock(spec=BlockFile)
    file.file_name = "test.csv"
    file.bucket_name = "bucket"
    file.generation = 1
    file.size = 100
    file.md5_hash = "md5"
    file.media_link = "media"
    file.org_id = "tampa_general"
    processed_file1 = MagicMock(spec=BlockScheduleProcessedFileModel)
    processed_file2 = MagicMock(spec=BlockScheduleProcessedFileModel)
    block_schedule_file_store.get_processed_files.return_value = [processed_file1, processed_file2]
    with patch.object(
        block_schedule_processing_service, "transform_and_save_release_file", new=AsyncMock()
    ):
        result = await block_schedule_processing_service.save_and_process_file(file)
        assert result is None


@pytest.mark.asyncio
async def test_save_transformed_block_schedule_rows(
    block_schedule_processing_service: BlockScheduleProcessingService,
    block_schedule_file_store: AsyncMock,
) -> None:
    block_schedule_file_id = uuid4()
    block_times = [MagicMock(spec=BlockTimeDataInput)]
    await block_schedule_processing_service.save_transformed_block_schedule_rows(
        block_schedule_file_id, block_times
    )
    block_schedule_file_store.save_transformed_block_schedule_rows.assert_awaited_once_with(
        block_schedule_file_id=block_schedule_file_id,
        block_times=block_times,
    )


@pytest.mark.asyncio
async def test_transform_and_save_release_file_error_handling(
    block_schedule_processing_service: BlockScheduleProcessingService,
    block_csv_asset_store: MagicMock,
    block_schedule_file_store: AsyncMock,
) -> None:
    # Arrange
    file = MagicMock(spec=BlockScheduleProcessedFileModel)
    file.media_link = "media_link"
    file.org_id = "tampa_general"
    file.file_name = "test.csv"
    block_csv_asset_store.download_csv_file_as_text.side_effect = Exception("Download failed")

    # Act & Assert
    with pytest.raises(Exception) as exc_info:
        await block_schedule_processing_service.transform_and_save_release_file(file)
    assert "Download failed" in str(exc_info.value)
    block_schedule_file_store.save_transformed_block_schedule_rows.assert_not_awaited()


@pytest.mark.asyncio
async def test_save_and_process_file_error_handling(
    block_schedule_processing_service: BlockScheduleProcessingService,
    block_schedule_file_store: AsyncMock,
) -> None:
    file = MagicMock(spec=BlockFile)
    file.file_name = "test.csv"
    file.bucket_name = "bucket"
    file.generation = 1
    file.size = 100
    file.md5_hash = "md5"
    file.media_link = "media"
    file.org_id = "tampa_general"
    block_schedule_file_store.get_processed_files.return_value = []
    block_schedule_file_store.save_processed_file.side_effect = Exception("Save failed")

    result = await block_schedule_processing_service.save_and_process_file(file)

    assert result is None
    block_schedule_file_store.save_processed_file.assert_awaited_once()


@pytest.mark.asyncio
async def test_save_transformed_block_schedule_rows_error_handling(
    block_schedule_processing_service: BlockScheduleProcessingService,
    block_schedule_file_store: AsyncMock,
) -> None:
    block_schedule_file_id = uuid4()
    block_times = [MagicMock(spec=BlockTimeDataInput)]
    block_schedule_file_store.save_transformed_block_schedule_rows.side_effect = Exception(
        "Save failed"
    )

    with pytest.raises(Exception) as exc_info:
        await block_schedule_processing_service.save_transformed_block_schedule_rows(
            block_schedule_file_id, block_times
        )
    assert "Save failed" in str(exc_info.value)
    block_schedule_file_store.save_transformed_block_schedule_rows.assert_awaited_once_with(
        block_schedule_file_id=block_schedule_file_id,
        block_times=block_times,
    )
