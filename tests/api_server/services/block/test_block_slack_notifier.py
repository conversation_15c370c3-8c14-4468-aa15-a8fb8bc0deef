# mypy: allow-untyped-defs


import pytest
from datetime import datetime
from api_server.services.block.block_models import BlockReleaseDataInput
from api_server.services.block.block_slack_notifier import BlockKitGenerator

from api_server.services.block.block_store import (
    ROOM_NOT_FOUND,
    BLOCK_NOT_FOUND,
    RELEASE_BULK_IMPORT_BLOCK_TIME_NOT_FOUND,
    RELEASE_BULK_IMPORT_RELEASE_ALREADY_EXISTS,
    RELEASE_BULK_IMPORT_RELEASE_CONFLICTS,
)


@pytest.fixture
def block_release_output():
    yield [
        BlockReleaseDataInput(
            release_reason="Block Release",
            released_by="Aquino, Sabrina",
            released_time=datetime.fromisoformat("2023-08-14T00:00:00-04:00"),
            id="f1b3b3b3-0b3b-4b3b-8b3b-0b3b3b3b3b3e",
            site_id="testing_site",
            to_block="Unavailable",
            block_name="Test Block",
            room_name="Test Room",
            timezone="America/New_York",
        ),
        BlockReleaseDataInput(
            release_reason="Block Release",
            released_by="<PERSON>, <PERSON>",
            released_time=datetime.fromisoformat("2023-08-14T00:00:00-04:00"),
            id="f1b3b3b3-0b3b-4b3b-8b3b-0b3b3b3b3b3e",
            site_id="testing_site",
            to_block="Ophthalmology",
            block_name="Test Block",
            room_name="Test Room",
            timezone="America/New_York",
        ),
        BlockReleaseDataInput(
            release_reason="Block Release",
            released_by="Aquino, Sabrina",
            released_time=datetime.fromisoformat("2023-08-03T00:00:00-04:00"),
            id="f1b3b3b3-0b3b-4b3b-8b3b-0b3b3b3b3b3a",
            site_id="testing_site",
            rejected_reason=RELEASE_BULK_IMPORT_BLOCK_TIME_NOT_FOUND,
            block_name="Test Block",
            room_name="Test Room",
            timezone="America/New_York",
        ),
        BlockReleaseDataInput(
            release_reason="Block Release",
            released_by="Aquino, Sabrina",
            released_time=datetime.fromisoformat("2023-08-03T00:00:00-04:00"),
            id="f1b3b3b3-0b3b-4b3b-8b3b-0b3b3b3b3b3a",
            site_id="APELLA-01",
            rejected_reason=RELEASE_BULK_IMPORT_BLOCK_TIME_NOT_FOUND,
            block_name="Test Block",
            room_name="Test Room",
            timezone="America/New_York",
        ),
        BlockReleaseDataInput(
            release_reason="Block Release",
            released_by="Aquino, Sabrina",
            released_time=datetime.fromisoformat("2023-08-14T00:00:00-04:00"),
            id="f1b3b3b3-0b3b-4b3b-8b3b-0b3b3b3b3b3e",
            site_id="testing_site",
            block_name="Test Block",
            room_name="Test Room",
            timezone="America/New_York",
        ),
        BlockReleaseDataInput(
            release_reason="Block Release",
            released_by="Aquino, Sabrina",
            released_time=datetime.fromisoformat("2023-08-14T00:00:00-04:00"),
            to_block="Unblocked",
            id="f1b3b3b3-0b3b-4b3b-8b3b-0b3b3b3b3b3e",
            site_id="testing_site",
            block_name="Test Block",
            room_name="Test Room",
            timezone="America/New_York",
        ),
        BlockReleaseDataInput(
            release_reason="Block Release",
            released_by="Aquino, Sabrina",
            released_time=datetime.fromisoformat("2023-08-14T00:00:00-04:00"),
            id="f1b3b3b3-0b3b-4b3b-8b3b-0b3b3b3b3b3b",
            site_id="testing_site",
            rejected_reason=BLOCK_NOT_FOUND,
            block_name="Test Block",
            room_name="Test Room",
            timezone="America/New_York",
        ),
        BlockReleaseDataInput(
            release_reason="Block Release",
            released_by="Jane, Doe",
            released_time=datetime.fromisoformat("2023-08-14T00:00:00-04:00"),
            id="f1b3b3b3-0b3b-4b3b-8b3b-0b3b3b3b3b3c",
            rejected_reason=ROOM_NOT_FOUND,
            block_name="Test Block",
            room_name="Test Room",
            timezone="America/New_York",
        ),
        BlockReleaseDataInput(
            release_reason="Block Release",
            released_by="Aquino, Sabrina",
            released_time=datetime.fromisoformat("2023-08-14T00:00:00-04:00"),
            id="f1b3b3b3-0b3b-4b3b-8b3b-0b3b3b3b3b3d",
            site_id="testing_site",
            rejected_reason="ValueError maybe",
            block_name="Test Block",
            room_name="Test Room",
            timezone="America/New_York",
        ),
        BlockReleaseDataInput(
            release_reason="Block Release",
            released_by="Aquino, Sabrina",
            released_time=datetime.fromisoformat("2023-08-03T00:00:00-04:00"),
            site_id="testing_site",
            id="f1b3b3b3-0b3b-4b3b-8b3b-0b3b3b3b3b3a",
            rejected_reason=RELEASE_BULK_IMPORT_BLOCK_TIME_NOT_FOUND,
            block_name="Test Block",
            room_name="Test Room",
            timezone="America/New_York",
        ),
        BlockReleaseDataInput(
            release_reason="Block Release",
            released_by="Aquino, Sabrina",
            released_time=datetime.fromisoformat("2023-08-03T00:00:00-04:00"),
            id="f1b3b3b3-0b3b-4b3b-8b3b-0b3b3b3b3b3a",
            site_id="APELLA-02",
            rejected_reason=RELEASE_BULK_IMPORT_BLOCK_TIME_NOT_FOUND,
            block_name="Test Block",
            room_name="Test Room",
            timezone="America/New_York",
        ),
        BlockReleaseDataInput(
            release_reason="Block Release",
            released_by="Aquino, Sabrina",
            released_time=datetime.fromisoformat("2023-08-14T00:00:00-04:00"),
            site_id="testing_site",
            id="f1b3b3b3-0b3b-4b3b-8b3b-0b3b3b3b3b3e",
            block_name="Test Block",
            room_name="Test Room",
            timezone="America/New_York",
        ),
        BlockReleaseDataInput(
            release_reason="Block Release",
            released_by="John, Doe",
            released_time=datetime.fromisoformat("2023-08-14T00:00:00-04:00"),
            id="f1b3b3b3-0b3b-4b3b-8b3b-0b3b3b3b3b3f",
            site_id="testing_site",
            rejected_reason="Some unknown issue",
            block_name="Test Block",
            room_name="Test Room",
            timezone="America/New_York",
        ),
        BlockReleaseDataInput(
            release_reason="Block Release",
            released_by="John, Doe",
            released_time=datetime.fromisoformat("2023-08-14T00:00:00-04:00"),
            id="f1b3b3b3-0b3b-4b3b-8b3b-0b3b3b3b3b3f",
            site_id="testing_site",
            rejected_reason=RELEASE_BULK_IMPORT_RELEASE_ALREADY_EXISTS,
            block_name="Test Block",
            room_name="Test Room",
            timezone="America/New_York",
        ),
        BlockReleaseDataInput(
            release_reason="Block Release",
            released_by="John, Doe",
            released_time=datetime.fromisoformat("2023-08-14T00:00:00-04:00"),
            id="f1b3b3b3-0b3b-4b3b-8b3b-0b3b3b3b3b3f",
            site_id="testing_site",
            rejected_reason=RELEASE_BULK_IMPORT_RELEASE_CONFLICTS,
            block_name="Test Block",
            room_name="Test Room",
            timezone="America/New_York",
        ),
    ]


@pytest.fixture
def block_release_one_processed_output():
    yield [
        BlockReleaseDataInput(
            release_reason="Block Release",
            released_by="Aquino, Sabrina",
            released_time=datetime.fromisoformat("2023-08-14T00:00:00-04:00"),
            site_id="testing_site",
            id="f1b3b3b3-0b3b-4b3b-8b3b-0b3b3b3b3b3e",
            to_block="Unavailable",
            block_name="Test Block",
            room_name="Test Room",
            timezone="America/New_York",
        ),
    ]


@pytest.fixture
def block_release_one_failed_output():
    yield [
        BlockReleaseDataInput(
            release_reason="Block Release",
            released_by="Aquino, Sabrina",
            released_time=datetime.fromisoformat("2023-08-14T00:00:00-04:00"),
            id="f1b3b3b3-0b3b-4b3b-8b3b-0b3b3b3b3b3e",
            site_id="testing_site",
            to_block="Unblocked",
            rejected_reason=RELEASE_BULK_IMPORT_RELEASE_CONFLICTS,
            block_name="Test Block",
            room_name="Test Room",
            timezone="America/New_York",
        ),
    ]


def test_build_slack_block_kit_full_output(
    block_release_output,
):
    time_uploaded = datetime.fromisoformat("2024-07-12T14:09:22-04:00")
    expected_output = [
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"[localhost] Block releases uploaded by SFTP at {time_uploaded.strftime('%Y-%m-%d %H:%M:%S')}",
            },
        },
        {"type": "divider"},
        {
            "type": "rich_text",
            "elements": [
                {
                    "type": "rich_text_section",
                    "elements": [{"type": "text", "text": "Total Records in the File: 15"}],
                },
                {
                    "type": "rich_text_list",
                    "style": "bullet",
                    "indent": 0,
                    "border": 0,
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [{"type": "text", "text": "Processed successfully: 5"}],
                        }
                    ],
                },
                {
                    "type": "rich_text_list",
                    "style": "bullet",
                    "indent": 1,
                    "border": 0,
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [{"type": "text", "text": "testing_site: 5"}],
                        }
                    ],
                },
                {
                    "type": "rich_text_list",
                    "style": "bullet",
                    "indent": 0,
                    "border": 0,
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [{"type": "text", "text": "Failed: 10"}],
                        }
                    ],
                },
                {
                    "border": 0,
                    "elements": [
                        {
                            "elements": [{"text": "APELLA-01: 1", "type": "text"}],
                            "type": "rich_text_section",
                        }
                    ],
                    "indent": 1,
                    "style": "bullet",
                    "type": "rich_text_list",
                },
                {
                    "border": 0,
                    "elements": [
                        {
                            "elements": [
                                {
                                    "text": "Block time not found: 1",
                                    "type": "text",
                                }
                            ],
                            "type": "rich_text_section",
                        }
                    ],
                    "indent": 2,
                    "style": "bullet",
                    "type": "rich_text_list",
                },
                {
                    "border": 0,
                    "elements": [
                        {
                            "elements": [{"text": "APELLA-02: 1", "type": "text"}],
                            "type": "rich_text_section",
                        }
                    ],
                    "indent": 1,
                    "style": "bullet",
                    "type": "rich_text_list",
                },
                {
                    "border": 0,
                    "elements": [
                        {
                            "elements": [
                                {
                                    "text": "Block time not found: 1",
                                    "type": "text",
                                }
                            ],
                            "type": "rich_text_section",
                        }
                    ],
                    "indent": 2,
                    "style": "bullet",
                    "type": "rich_text_list",
                },
                {
                    "border": 0,
                    "elements": [
                        {
                            "elements": [{"text": "testing_site: 7", "type": "text"}],
                            "type": "rich_text_section",
                        }
                    ],
                    "indent": 1,
                    "style": "bullet",
                    "type": "rich_text_list",
                },
                {
                    "border": 0,
                    "elements": [
                        {
                            "elements": [{"text": "Block not found: 1", "type": "text"}],
                            "type": "rich_text_section",
                        },
                        {
                            "elements": [{"text": "Block time not found: 2", "type": "text"}],
                            "type": "rich_text_section",
                        },
                        {
                            "elements": [{"text": "Release already exists: 1", "type": "text"}],
                            "type": "rich_text_section",
                        },
                        {
                            "elements": [
                                {
                                    "text": "Release time conflicts with another release: 1",
                                    "type": "text",
                                }
                            ],
                            "type": "rich_text_section",
                        },
                        {
                            "elements": [{"text": "Other Errors: 2", "type": "text"}],
                            "type": "rich_text_section",
                        },
                    ],
                    "indent": 2,
                    "style": "bullet",
                    "type": "rich_text_list",
                },
                {
                    "border": 0,
                    "elements": [
                        {
                            "elements": [{"text": "Room not found: 1", "type": "text"}],
                            "type": "rich_text_section",
                        }
                    ],
                    "indent": 1,
                    "style": "bullet",
                    "type": "rich_text_list",
                },
                {
                    "border": 0,
                    "elements": [
                        {
                            "elements": [{"type": "text", "text": "Test Room"}],
                            "type": "rich_text_section",
                        }
                    ],
                    "indent": 2,
                    "style": "bullet",
                    "type": "rich_text_list",
                },
            ],
        },
    ]

    slack_block_kit_object = BlockKitGenerator.generate_block_release_blocks(
        block_release_output, time_uploaded
    )
    assert slack_block_kit_object == expected_output


def test_build_slack_block_kit_empty_output():
    time_uploaded = datetime.fromisoformat("2024-07-12T14:09:22-04:00")
    expected_output = [
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"[localhost] Block releases uploaded by SFTP at {(time_uploaded).strftime('%Y-%m-%d %H:%M:%S')}",
            },
        },
        {"type": "divider"},
    ]

    slack_block_kit_object = BlockKitGenerator.generate_block_release_blocks([], time_uploaded)

    assert slack_block_kit_object == expected_output


def test_build_slack_block_kit_one_processed_output(block_release_one_processed_output):
    time_uploaded = datetime.fromisoformat("2024-07-12T14:09:22-04:00")
    expected_output = [
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": "[localhost] Block releases uploaded by SFTP at 2024-07-12 14:09:22",
            },
        },
        {"type": "divider"},
        {
            "type": "rich_text",
            "elements": [
                {
                    "type": "rich_text_section",
                    "elements": [{"type": "text", "text": "Total Records in the File: 1"}],
                },
                {
                    "type": "rich_text_list",
                    "style": "bullet",
                    "indent": 0,
                    "border": 0,
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [{"type": "text", "text": "Processed successfully: 1"}],
                        }
                    ],
                },
                {
                    "type": "rich_text_list",
                    "style": "bullet",
                    "indent": 1,
                    "border": 0,
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [{"type": "text", "text": "testing_site: 1"}],
                        }
                    ],
                },
            ],
        },
    ]

    slack_block_kit_object = BlockKitGenerator.generate_block_release_blocks(
        block_release_one_processed_output, time_uploaded
    )

    assert slack_block_kit_object == expected_output


def test_build_slack_block_kit_one_failed_output(block_release_one_failed_output):
    time_uploaded = datetime.fromisoformat("2024-07-12T14:09:22-04:00")
    expected_output = [
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": "[localhost] Block releases uploaded by SFTP at 2024-07-12 14:09:22",
            },
        },
        {"type": "divider"},
        {
            "type": "rich_text",
            "elements": [
                {
                    "type": "rich_text_section",
                    "elements": [{"type": "text", "text": "Total Records in the File: 1"}],
                },
                {
                    "type": "rich_text_list",
                    "style": "bullet",
                    "indent": 0,
                    "border": 0,
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [{"type": "text", "text": "Failed: 1"}],
                        }
                    ],
                },
                {
                    "border": 0,
                    "elements": [
                        {
                            "elements": [{"text": "testing_site: 1", "type": "text"}],
                            "type": "rich_text_section",
                        }
                    ],
                    "indent": 1,
                    "style": "bullet",
                    "type": "rich_text_list",
                },
                {
                    "border": 0,
                    "elements": [
                        {
                            "elements": [
                                {
                                    "text": "Release time conflicts with another release: 1",
                                    "type": "text",
                                }
                            ],
                            "type": "rich_text_section",
                        }
                    ],
                    "indent": 2,
                    "style": "bullet",
                    "type": "rich_text_list",
                },
            ],
        },
    ]

    slack_block_kit_object = BlockKitGenerator.generate_block_release_blocks(
        block_release_one_failed_output, time_uploaded
    )

    assert slack_block_kit_object == expected_output
