from unittest.mock import MagicMock, AsyncMock

import pytest

from api_server.services.camera.camera_store import CameraModel
from api_server.services.organization.organization_db import Organization
from api_server.services.room.room_store import RoomModel
from api_server.services.site.site_store import Site
from tests.harness import harness

test_camera: CameraModel = CameraModel()
test_camera.id = "test_camera_id"
test_camera.name = "Test Camera"
test_camera.org_id = "test_organization_id"
test_camera.site_id = "test_site_id"
test_camera.room_id = "test_room_id"
test_camera.family = "geovision"

test_organization: Organization = Organization()
test_organization.id = test_camera.org_id
test_organization.name = "Test Organization"

test_site: Site = Site()
test_site.id = test_camera.site_id
test_site.name = "Test Site"
test_site.org_id = test_organization.id

test_room: RoomModel = RoomModel()
test_room.id = test_camera.room_id
test_room.name = "Test Room"
test_room.site_id = test_site.id
test_room.org_id = test_organization.id


@pytest.mark.asyncio
async def test_camera_can_be_queried_by_id() -> None:
    client, context = harness()

    context.auth.get_calling_org_id = MagicMock(return_value=test_camera.org_id)
    context.camera_service.get_cameras = AsyncMock(return_value=[test_camera])

    result = await client.execute_async(
        """
    {
        camera(id: "test_camera_id") {
            name
        }
    }
    """,
        context_value=context,
    )

    context.camera_service.get_cameras.assert_called_with([test_camera.id])

    assert result == {"data": {"camera": {"name": test_camera.name}}}


@pytest.mark.asyncio
async def test_camera_org_can_be_queried() -> None:
    client, context = harness()

    context.auth.get_calling_org_id = MagicMock(return_value=test_camera.org_id)
    context.camera_service.get_cameras = AsyncMock(return_value=[test_camera])
    context.organization_service.get_organizations = AsyncMock(return_value=[test_organization])

    result = await client.execute_async(
        """
    {
        camera(id: "test_camera_id") {
            organization {
                name
            }
        }
    }
    """,
        context_value=context,
    )

    context.camera_service.get_cameras.assert_called_with([test_camera.id])
    context.organization_service.get_organizations.assert_called_with(org_ids=[test_camera.org_id])

    assert result == {"data": {"camera": {"organization": {"name": test_organization.name}}}}


@pytest.mark.asyncio
async def test_camera_site_can_be_queried() -> None:
    client, context = harness()

    context.auth.get_calling_org_id = MagicMock(return_value=test_camera.org_id)
    context.camera_service.get_cameras = AsyncMock(return_value=[test_camera])
    context.site_service.query_sites = AsyncMock(return_value=[test_site])

    result = await client.execute_async(
        """
    {
        camera(id: "test_camera_id") {
            site {
                name
            }
        }
    }
    """,
        context_value=context,
    )

    context.camera_service.get_cameras.assert_called_with([test_camera.id])

    called_args, _ = context.site_service.query_sites.call_args
    site_query = called_args[0]
    assert site_query.site_ids == [test_camera.site_id]

    assert result == {"data": {"camera": {"site": {"name": test_site.name}}}}


@pytest.mark.asyncio
async def test_camera_room_can_be_queried() -> None:
    client, context = harness()

    context.auth.get_calling_org_id = MagicMock(return_value=test_camera.org_id)
    context.camera_service.get_cameras = AsyncMock(return_value=[test_camera])
    context.room_service.get_rooms = AsyncMock(return_value=[test_room])

    result = await client.execute_async(
        """
    {
        camera(id: "test_camera_id") {
            room {
                name
            }
        }
    }
    """,
        context_value=context,
    )

    context.camera_service.get_cameras.assert_called_with([test_camera.id])
    context.room_service.get_rooms.assert_called_with(keys=[test_camera.room_id])

    assert result == {"data": {"camera": {"room": {"name": test_room.name}}}}
