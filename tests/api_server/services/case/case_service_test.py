from datetime import datetime
from typing import List
from unittest.mock import MagicMock, AsyncMock
from uuid import uuid4

import pytest

from apella_cloud_api.dtos import CaseSearchDto
from api_server.services.case.case_classification_type_store import (
    CaseClassificationTypeModel,
    CaseClassificationTypeStore,
)

from api_server.services.case.case_flag_store import CaseFlagStore
from api_server.services.case.case_procedure_store import (
    CaseProcedureModel,
    CaseProcedureStore,
)
from api_server.services.case.case_service import CaseService
from api_server.services.case.case_staff_store import CaseStaffModel, CaseStaffStore
from api_server.services.case.case_status import SCHEDULED
from api_server.services.case.case_store import (
    Case,
    CaseStore,
)
from api_server.services.case_derived_properties.case_derived_properties_service import (
    CaseDerivedPropertiesService,
)
from api_server.services.events.event_pubsub_store import EventPubSubStore
from api_server.services.phases.phase_service import PhaseService
from api_server.services.service_lines.service_line_store import ServiceLineStore
from api_server.services.site.site_service import SiteService
from auth.auth import Auth
from auth.permissions import READ_ANY_CASE

test_case = Case()
test_case.case_id = str(uuid4())
test_case.external_case_id = "test_case_id"
test_case.room_id = "test_room_id"
test_case.site_id = "test_site_id"
test_case.org_id = "test_org_id"
test_case.case_classification_types_id = "case_classification_types_id"
test_case.scheduled_start_time = datetime.fromisoformat("2021-08-01T01:00:00-07:00")
test_case.scheduled_end_time = datetime.fromisoformat("2021-08-01T02:00:00-07:00")
test_case.status = SCHEDULED
test_case.users = ["test_user_0"]  # type: ignore [attr-defined]

test_case_staff = CaseStaffModel()
test_case_staff.staff_id = uuid4()
test_case_staff.case_id = str(uuid4())

test_case_procedure = CaseProcedureModel()
test_case_procedure.procedure_id = uuid4()
test_case_procedure.case_id = str(uuid4())

mock_case_store = MagicMock(spec=CaseStore)
mock_case_procedure_store = MagicMock(spec=CaseProcedureStore)
mock_case_derived_properties_service = MagicMock(spec=CaseDerivedPropertiesService)
mock_auth = MagicMock(spec=Auth)
mock_case_staff_store = MagicMock(spec=CaseStaffStore)
mock_service_line_store = MagicMock(spec=ServiceLineStore)
mock_case_classification_type_store = MagicMock(spec=CaseClassificationTypeStore)
mock_case_flag_store = MagicMock(spec=CaseFlagStore)
mock_auth.get_calling_org_id = MagicMock(return_value=None)
mock_event_pub_sub_store = MagicMock(spec=EventPubSubStore)
mock_phase_service = MagicMock(spec=PhaseService)
mock_site_service = MagicMock(spec=SiteService)
case_service = CaseService(
    auth=mock_auth,
    case_store=mock_case_store,
    case_procedure_store=mock_case_procedure_store,
    case_staff_store=mock_case_staff_store,
    service_line_store=mock_service_line_store,
    case_classification_type_store=mock_case_classification_type_store,
    case_derived_properties_service=mock_case_derived_properties_service,
    case_flag_store=mock_case_flag_store,
    event_pub_sub_store=mock_event_pub_sub_store,
    phase_service=mock_phase_service,
    site_service=mock_site_service,
    case_matching_store=MagicMock(),
)


class TestCaseService:
    @pytest.mark.asyncio
    async def test_get_case_info(self) -> None:
        mock_auth.requires = MagicMock()
        mock_case_store.get_case = AsyncMock(return_value=test_case)

        result = await case_service.get_case(case_id=test_case.case_id)

        mock_auth.requires.assert_called_with(READ_ANY_CASE, enforce_universal_user=False)
        mock_case_store.get_case.assert_called_with(case_id=test_case.case_id)

        assert result == test_case

    @pytest.mark.asyncio
    async def test_query_cases(self) -> None:
        mock_auth.requires = MagicMock()
        mock_case_store.query_cases = AsyncMock(return_value=[test_case])

        query = CaseSearchDto()
        query.organization_id = test_case.org_id

        result = await case_service.query_cases(query)

        mock_auth.requires.assert_called_with(READ_ANY_CASE, enforce_universal_user=False)
        mock_case_store.query_cases.assert_called_once()

        assert result == [test_case]

    @pytest.mark.asyncio
    async def test_query_case_procedure(self) -> None:
        mock_auth.requires = MagicMock()
        mock_case_procedure_store.get_case_procedure_relationships_for_cases = AsyncMock(
            return_value=[test_case_procedure]
        )

        result = await case_service.get_case_procedure_relationships_for_cases(
            case_ids=["test_case"]
        )

        mock_auth.requires.assert_called_with(READ_ANY_CASE, enforce_universal_user=True)
        mock_case_procedure_store.get_case_procedure_relationships_for_cases.assert_called_once()

        assert result == [test_case_procedure]

    @pytest.mark.asyncio
    async def test_query_case_staff(self) -> None:
        mock_auth.requires = MagicMock()
        mock_case_staff_store.get_case_staff_relationships_for_cases = AsyncMock(
            return_value=[test_case_staff]
        )

        result = await case_service.get_case_staff_relationships_for_cases(case_ids=["test_case"])

        mock_auth.requires.assert_called_with(READ_ANY_CASE, enforce_universal_user=True)
        mock_case_staff_store.get_case_staff_relationships_for_cases.assert_called_once()

        assert result == [test_case_staff]

    def _get_test_case_classification_types(self) -> List[CaseClassificationTypeModel]:
        test_obj = CaseClassificationTypeModel()
        test_obj.id = "test_classification"
        test_obj.name = "Test Classification"

        test_obj_2 = CaseClassificationTypeModel()
        test_obj_2.id = "test_classification_2"
        test_obj_2.name = "Test Classification 2"

        return [test_obj, test_obj_2]

    @pytest.mark.asyncio
    async def test_query_case_classification_types_return_all(self) -> None:
        types = self._get_test_case_classification_types()

        mock_auth.requires = MagicMock()
        mock_case_classification_type_store.get_case_classification_types = AsyncMock(
            return_value=types
        )

        result = await case_service.get_case_classification_types()

        mock_auth.requires.assert_called_with(READ_ANY_CASE, enforce_universal_user=False)

        mock_case_classification_type_store.get_case_classification_types.assert_called_once()

        assert types == result

    @pytest.mark.asyncio
    async def test_query_case_classification_types_filter(self) -> None:
        types = self._get_test_case_classification_types()

        mock_auth.requires = MagicMock()
        mock_case_classification_type_store.get_case_classification_types = AsyncMock(
            return_value=types
        )

        result = await case_service.get_case_classification_types(["test_classification_2"])

        mock_auth.requires.assert_called_with(READ_ANY_CASE, enforce_universal_user=False)

        mock_case_classification_type_store.get_case_classification_types.assert_called_once()
        assert types == result
