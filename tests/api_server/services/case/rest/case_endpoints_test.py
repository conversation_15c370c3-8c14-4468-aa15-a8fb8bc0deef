from datetime import datetime
from unittest.mock import MagicMock, AsyncMock
from uuid import uuid4

import pytest

from apella_cloud_api.dtos import CaseSearchDto
from api_server.services.case.case_service import CaseService
from api_server.services.case.case_status import SCHEDULED
from api_server.services.case.case_store import Case
from api_server.services.case.rest import case_endpoints
from auth.auth import Auth

test_case = Case()
test_case.case_id = str(uuid4())
test_case.external_case_id = "test_case_id"
test_case.room_id = "test_room_id"
test_case.site_id = "test_site_id"
test_case.org_id = "test_org_id"
test_case.case_classification_types_id = "case_classification_types_id"
test_case.scheduled_start_time = datetime.fromisoformat("2021-08-01T01:00:00-07:00")
test_case.scheduled_end_time = datetime.fromisoformat("2021-08-01T02:00:00-07:00")
test_case.status = SCHEDULED

mock_case_service = MagicMock(spec=CaseService)
mock_auth = MagicMock(spec=Auth)


@pytest.mark.asyncio
async def test_get_case_info() -> None:
    mock_case_service.get_case = AsyncMock(return_value=test_case)
    mock_auth.get_calling_org_id = MagicMock(return_value=test_case.org_id)

    result = await case_endpoints.get_case(test_case.case_id, mock_case_service)

    mock_case_service.get_case.assert_called_with(case_id=test_case.case_id)

    assert result == test_case.to_dto()


@pytest.mark.asyncio
async def test_query_cases() -> None:
    mock_case_service.query_cases = AsyncMock(return_value=[test_case])

    query = CaseSearchDto()
    query.organization_id = test_case.org_id

    result = await case_endpoints.search_cases(query, mock_case_service)

    mock_case_service.query_cases.assert_called_once()

    assert result.cases == [test_case.to_dto()]
