from datetime import datetime, timezone
from unittest.mock import AsyncMock, MagicMock
import pytest
from api_server.services.case_activity.case_activity_service import CaseActivityService
from api_server.services.case_activity.case_activity_store import (
    CaseActivityStore,
    CaseActivityModel,
)


@pytest.mark.asyncio
class TestCaseActivityService:
    @pytest.fixture
    def mock_store(self) -> MagicMock:
        """Creates a mock CaseActivityStore."""
        return MagicMock(spec=CaseActivityStore)

    @pytest.fixture
    def service(self, mock_store: MagicMock) -> CaseActivityService:
        """Creates a CaseActivityService instance using the mocked store."""
        service = CaseActivityService()
        service.store = mock_store  # Inject mock store
        return service

    async def test_get_case_activities(
        self, service: CaseActivityService, mock_store: MagicMock
    ) -> None:
        """Tests if get_case_activities retrieves filtered case activities correctly."""
        mock_case_activity = CaseActivityModel(
            id="event_1",
            source_type="ehr",
            site_id="site_1",
            org_id="org_1",
            room_id="room_1",
            updated_time=datetime(2021, 1, 1, 12, 5, 0, tzinfo=timezone.utc),
            created_time=datetime(2021, 1, 1, 12, 0, 0, tzinfo=timezone.utc),
            deleted_time=None,
            event_type_id="event_type_1",
            source="test_source",
            case_id="case_1",
            start_time=datetime(2021, 1, 1, 12, 2, 0, tzinfo=timezone.utc),
            process_timestamp=datetime(2021, 1, 1, 12, 3, 0, tzinfo=timezone.utc),
        )

        # Mock store return
        mock_store.fetch_case_activities = AsyncMock(return_value=[mock_case_activity])

        result = await service.get_case_activities(
            site_id="site_1",
            min_time=datetime(2021, 1, 1, 12, 0, 0, tzinfo=timezone.utc),
            max_time=datetime(2021, 1, 1, 12, 5, 0, tzinfo=timezone.utc),
            include_source_type="ehr",
        )

        assert result == [mock_case_activity]
        assert len(result) == 1
        assert result[0].source_type == "ehr"
