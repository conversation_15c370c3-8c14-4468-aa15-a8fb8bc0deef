from datetime import datetime, timed<PERSON>ta
from unittest.mock import MagicMock, AsyncMock

import pytest

from api_server.services.case_derived_properties.case_derived_properties_service import (
    CaseDerivedPropertiesService,
)
from api_server.services.case_derived_properties.case_derived_properties_store import (
    CaseDerivedProperties,
    CaseDerivedPropertiesStore,
    ProcessCaseDerivedPropertiesDto,
)
from api_server.services.phases.phase_service import PhaseService

from auth.auth import Auth
from auth.permissions import READ_ANY_CASE
from tests.api_server.services.apella_case.apella_case_mock_helpers import _mock_phase
from tests.api_server.services.case_derived_properties.case_derived_properties_helper import (
    create_two_cases,
    create_case_staff,
    create_test_site,
    INITIAL_START_TIME,
    create_test_case,
)

MORNING_HOURS_START_TIME = datetime.fromisoformat("2021-08-01T07:00:00-05:00")

test_cp = CaseDerivedProperties()

mock_store = MagicMock(spec=CaseDerivedPropertiesStore)
mock_auth = MagicMock(spec=Auth)

mock_phase_service = MagicMock(spec=PhaseService)
service = CaseDerivedPropertiesService(
    auth=mock_auth,
    case_derived_properties_store=mock_store,
    phase_service=mock_phase_service,
)


@pytest.mark.asyncio
async def test_get_case_derived_properties_by_case_ids() -> None:
    mock_store.get_by_case_ids = AsyncMock(return_value=[test_cp])
    mock_auth.requires = MagicMock()

    result = await service.get_properties_by_case_ids([test_cp.case_id])
    assert result == [test_cp]

    mock_auth.requires_permission_prefix.assert_called_with(
        permission_prefix=READ_ANY_CASE, enforce_universal_user=False
    )


@pytest.mark.asyncio
async def test_two_rooms_with_first_cases() -> None:
    """
    Two separate rooms with first cases and different staff should return two first cases.
    """

    case_one, case_two = create_two_cases(
        first_case_scheduled_start_time=MORNING_HOURS_START_TIME,
        first_case_room="room_id_1",
        second_case_scheduled_start_time=MORNING_HOURS_START_TIME,
        second_case_room="room_id_2",
    )
    case_staff_one = create_case_staff()
    case_staff_two = create_case_staff()
    test_site = create_test_site()

    mock_get_cases_to_process = [
        (case_one, case_staff_one, test_site),
        (case_two, case_staff_two, test_site),
    ]
    mock_store.get_cases_to_process = AsyncMock(return_value=mock_get_cases_to_process)
    mock_store.upsert_case_derived_properties.reset_mock()
    await service.process_case_properties(
        case_properties_update_dto=[
            ProcessCaseDerivedPropertiesDto(site_id=test_site.id, date=INITIAL_START_TIME)
        ]
    )

    expected_cases_to_upsert = [
        CaseDerivedProperties(case_id=case_two.case_id, is_first_case=True, is_in_flip_room=False),
        CaseDerivedProperties(case_id=case_one.case_id, is_first_case=True, is_in_flip_room=False),
    ]
    expected_cases_to_upsert.sort(key=lambda case: case.case_id)
    mock_store.upsert_case_derived_properties.assert_called_once()
    mock_store.upsert_case_derived_properties.assert_called_with(expected_cases_to_upsert)


@pytest.mark.asyncio
async def test_one_case_multiple_staff() -> None:
    """
    One case with multiple staff should return one first case to upsert
    """

    case_one, _ = create_two_cases(
        first_case_scheduled_start_time=MORNING_HOURS_START_TIME,
        first_case_duration_minutes=60,
        first_case_room="test_room_id",
        time_gap_minutes=60,
    )

    case_staff_one = create_case_staff(case_id="case_id_1")
    case_staff_two = create_case_staff(case_id="case_id_1")
    test_site = create_test_site()

    mock_get_cases_to_process = [
        (case_one, case_staff_one, test_site),
        (case_one, case_staff_two, test_site),
    ]
    mock_store.get_cases_to_process = AsyncMock(return_value=mock_get_cases_to_process)

    mock_store.upsert_case_derived_properties.reset_mock()
    await service.process_case_properties(
        case_properties_update_dto=[
            ProcessCaseDerivedPropertiesDto(site_id=test_site.id, date=INITIAL_START_TIME)
        ]
    )

    expected_cases_to_upsert = [
        CaseDerivedProperties(case_id=case_one.case_id, is_first_case=True, is_in_flip_room=False),
    ]
    expected_cases_to_upsert.sort(key=lambda case: case.case_id)

    mock_store.upsert_case_derived_properties.assert_called_once()
    mock_store.upsert_case_derived_properties.assert_called_with(expected_cases_to_upsert)


@pytest.mark.asyncio
async def test_multiple_cases_same_room() -> None:
    """
    Multiple cases in the same room
    """
    case_one, case_two = create_two_cases(
        first_case_scheduled_start_time=MORNING_HOURS_START_TIME,
        first_case_duration_minutes=15,
        first_case_room="room_id_1",
        second_case_scheduled_start_time=MORNING_HOURS_START_TIME + timedelta(minutes=16),
        second_case_duration_minutes=10,
        second_case_room="room_id_1",
    )
    case_three = create_test_case(
        scheduled_start_time=MORNING_HOURS_START_TIME + timedelta(minutes=27),
        duration_minutes=10,
        room_id="room_id_1",
    )

    case_staff_one = create_case_staff()
    case_staff_two = create_case_staff()
    case_staff_three = create_case_staff()
    test_site = create_test_site()

    mock_get_cases_to_process = [
        (case_one, case_staff_one, test_site),
        (case_two, case_staff_two, test_site),
        (case_three, case_staff_three, test_site),
    ]
    mock_store.get_cases_to_process = AsyncMock(return_value=mock_get_cases_to_process)

    mock_store.upsert_case_derived_properties.reset_mock()
    await service.process_case_properties(
        case_properties_update_dto=[
            ProcessCaseDerivedPropertiesDto(site_id=test_site.id, date=INITIAL_START_TIME)
        ]
    )

    expected_cases_to_upsert = [
        CaseDerivedProperties(case_id=case_one.case_id, is_first_case=True, is_in_flip_room=False),
        CaseDerivedProperties(case_id=case_two.case_id, is_first_case=False, is_in_flip_room=False),
        CaseDerivedProperties(
            case_id=case_three.case_id, is_first_case=False, is_in_flip_room=False
        ),
    ]
    expected_cases_to_upsert.sort(key=lambda case: case.case_id)
    mock_store.upsert_case_derived_properties.assert_called_once()
    mock_store.upsert_case_derived_properties.assert_called_with(expected_cases_to_upsert)


@pytest.mark.asyncio
async def test_flip_room_common_case() -> None:
    """
    Two cases in the same room with the same staff, only one should be first case, the other (later one) is flip
    """

    case_one, case_two = create_two_cases(
        first_case_scheduled_start_time=MORNING_HOURS_START_TIME,
        first_case_duration_minutes=60,
        first_case_room="room_id_1",
        second_case_scheduled_start_time=MORNING_HOURS_START_TIME + timedelta(minutes=5),
        second_case_duration_minutes=60,
        second_case_room="room_id_2",
    )

    case_staff_one = create_case_staff()
    test_site = create_test_site()

    cases_to_process = [
        (case_one, case_staff_one, test_site),
        (case_two, case_staff_one, test_site),
    ]
    mock_store.get_cases_to_process = AsyncMock(return_value=cases_to_process)

    mock_store.upsert_case_derived_properties.reset_mock()
    await service.process_case_properties(
        case_properties_update_dto=[
            ProcessCaseDerivedPropertiesDto(site_id=test_site.id, date=INITIAL_START_TIME)
        ]
    )

    expected_cases_to_upsert = [
        CaseDerivedProperties(case_id=case_one.case_id, is_first_case=True, is_in_flip_room=False),
        CaseDerivedProperties(case_id=case_two.case_id, is_first_case=False, is_in_flip_room=True),
    ]
    expected_cases_to_upsert.sort(key=lambda case: case.case_id)
    mock_store.upsert_case_derived_properties.assert_called_with(expected_cases_to_upsert)


@pytest.mark.asyncio
async def test_flip_room_multiple_staff_first_case_earlier() -> None:
    """
    Both cases are first cases, case one is first case for staff one and case two is first case for staff two
    """
    case_one, case_two = create_two_cases(
        first_case_scheduled_start_time=MORNING_HOURS_START_TIME,
        first_case_duration_minutes=60,
        first_case_room="room_id_1",
        second_case_scheduled_start_time=MORNING_HOURS_START_TIME + timedelta(minutes=10),
        second_case_duration_minutes=60,
        second_case_room="room_id_2",
    )

    case_staff_one = create_case_staff()
    case_staff_two = create_case_staff()
    test_site = create_test_site()

    cases_to_process = [
        (case_one, case_staff_one, test_site),
        (case_two, case_staff_one, test_site),
        (case_two, case_staff_two, test_site),
    ]
    mock_store.get_cases_to_process = AsyncMock(return_value=cases_to_process)

    mock_store.upsert_case_derived_properties.reset_mock()
    await service.process_case_properties(
        case_properties_update_dto=[
            ProcessCaseDerivedPropertiesDto(site_id=test_site.id, date=INITIAL_START_TIME)
        ]
    )

    expected_cases_to_upsert = [
        CaseDerivedProperties(case_id=case_one.case_id, is_first_case=True, is_in_flip_room=False),
        CaseDerivedProperties(case_id=case_two.case_id, is_first_case=True, is_in_flip_room=False),
    ]
    expected_cases_to_upsert.sort(key=lambda case: case.case_id)
    mock_store.upsert_case_derived_properties.assert_called_with(expected_cases_to_upsert)


@pytest.mark.asyncio
async def test_flip_room_multiple_staff_first_case_earlier_swapped() -> None:
    """
    Both cases are first cases, case one is first case for staff one and case two is first case for staff two
    """
    case_one, case_two = create_two_cases(
        first_case_scheduled_start_time=MORNING_HOURS_START_TIME,
        first_case_duration_minutes=60,
        first_case_room="room_id_1",
        second_case_scheduled_start_time=MORNING_HOURS_START_TIME + timedelta(minutes=10),
        second_case_duration_minutes=60,
        second_case_room="room_id_2",
    )

    case_staff_one = create_case_staff()
    case_staff_two = create_case_staff()
    test_site = create_test_site()

    cases_to_process = [
        (case_one, case_staff_one, test_site),
        (case_two, case_staff_two, test_site),
        (case_two, case_staff_one, test_site),
    ]
    mock_store.get_cases_to_process = AsyncMock(return_value=cases_to_process)

    mock_store.upsert_case_derived_properties.reset_mock()
    await service.process_case_properties(
        case_properties_update_dto=[
            ProcessCaseDerivedPropertiesDto(site_id=test_site.id, date=INITIAL_START_TIME)
        ]
    )

    expected_cases_to_upsert = [
        CaseDerivedProperties(case_id=case_one.case_id, is_first_case=True, is_in_flip_room=False),
        CaseDerivedProperties(case_id=case_two.case_id, is_first_case=True, is_in_flip_room=False),
    ]
    expected_cases_to_upsert.sort(key=lambda case: case.case_id)
    mock_store.upsert_case_derived_properties.assert_called_with(expected_cases_to_upsert)


@pytest.mark.asyncio
async def test_flip_room_multiple_staff_first_case_earlier_swapped_2() -> None:
    """
    Both cases are first cases, case one is first case for staff one and staff two so case two is considered flip for staff two
    """

    case_one, case_two = create_two_cases(
        first_case_scheduled_start_time=MORNING_HOURS_START_TIME,
        first_case_duration_minutes=60,
        first_case_room="room_id_1",
        second_case_scheduled_start_time=MORNING_HOURS_START_TIME + timedelta(minutes=10),
        second_case_duration_minutes=60,
        second_case_room="room_id_2",
    )

    case_staff_one = create_case_staff()
    case_staff_two = create_case_staff()
    test_site = create_test_site()

    cases_to_process = [
        (case_one, case_staff_one, test_site),
        (case_one, case_staff_two, test_site),
        (case_two, case_staff_two, test_site),
    ]
    mock_store.get_cases_to_process = AsyncMock(return_value=cases_to_process)

    mock_store.upsert_case_derived_properties.reset_mock()
    await service.process_case_properties(
        case_properties_update_dto=[
            ProcessCaseDerivedPropertiesDto(site_id=test_site.id, date=INITIAL_START_TIME)
        ]
    )

    expected_cases_to_upsert = [
        CaseDerivedProperties(case_id=case_one.case_id, is_first_case=True, is_in_flip_room=False),
        CaseDerivedProperties(case_id=case_two.case_id, is_first_case=False, is_in_flip_room=True),
    ]
    expected_cases_to_upsert.sort(key=lambda case: case.case_id)
    mock_store.upsert_case_derived_properties.assert_called_with(expected_cases_to_upsert)


@pytest.mark.asyncio
async def test_flip_room_multiple_staff_second_case_earlier() -> None:
    """
    Case two is earlier and therefore considered first case for staff one and staff two
    Case one is then evaluated to be flip for staff one
    """

    case_one, case_two = create_two_cases(
        first_case_scheduled_start_time=MORNING_HOURS_START_TIME + timedelta(minutes=15),
        first_case_duration_minutes=60,
        first_case_room="room_id_1",
        second_case_scheduled_start_time=MORNING_HOURS_START_TIME,
        second_case_duration_minutes=60,
        second_case_room="room_id_2",
    )

    case_staff_one = create_case_staff()
    case_staff_two = create_case_staff()
    test_site = create_test_site()

    cases_to_process = [
        (case_one, case_staff_one, test_site),
        (case_two, case_staff_one, test_site),
        (case_two, case_staff_two, test_site),
    ]
    mock_store.get_cases_to_process = AsyncMock(return_value=cases_to_process)

    mock_store.upsert_case_derived_properties.reset_mock()
    await service.process_case_properties(
        case_properties_update_dto=[
            ProcessCaseDerivedPropertiesDto(site_id=test_site.id, date=INITIAL_START_TIME)
        ]
    )

    expected_cases_to_upsert = [
        CaseDerivedProperties(case_id=case_one.case_id, is_first_case=False, is_in_flip_room=True),
        CaseDerivedProperties(case_id=case_two.case_id, is_first_case=True, is_in_flip_room=False),
    ]
    expected_cases_to_upsert.sort(key=lambda case: case.case_id)
    mock_store.upsert_case_derived_properties.assert_called_with(expected_cases_to_upsert)


@pytest.mark.asyncio
async def test_flip_room_multiple_staff_second_case_earlier_swapped() -> None:
    """
    Case two is earlier and first case for staff two
    Case one is later and first case for staff one, even though staff two is on this case
    """

    case_one, case_two = create_two_cases(
        first_case_scheduled_start_time=MORNING_HOURS_START_TIME + timedelta(minutes=10),
        first_case_duration_minutes=60,
        first_case_room="room_id_1",
        second_case_scheduled_start_time=MORNING_HOURS_START_TIME,
        second_case_duration_minutes=60,
        second_case_room="room_id_2",
    )

    case_staff_one = create_case_staff()
    case_staff_two = create_case_staff()
    test_site = create_test_site()

    cases_to_process = [
        (case_one, case_staff_one, test_site),
        (case_one, case_staff_two, test_site),
        (case_two, case_staff_two, test_site),
    ]
    mock_store.get_cases_to_process = AsyncMock(return_value=cases_to_process)

    mock_store.upsert_case_derived_properties.reset_mock()
    await service.process_case_properties(
        case_properties_update_dto=[
            ProcessCaseDerivedPropertiesDto(site_id=test_site.id, date=INITIAL_START_TIME)
        ]
    )

    expected_cases_to_upsert = [
        CaseDerivedProperties(case_id=case_one.case_id, is_first_case=True, is_in_flip_room=False),
        CaseDerivedProperties(case_id=case_two.case_id, is_first_case=True, is_in_flip_room=False),
    ]
    expected_cases_to_upsert.sort(key=lambda case: case.case_id)
    mock_store.upsert_case_derived_properties.assert_called_with(expected_cases_to_upsert)


@pytest.mark.asyncio
async def test_three_first_case_flip_rooms_staggered_start() -> None:
    """
    For three rooms with staggered start times, each are first cases
    """
    case_one, case_two = create_two_cases(
        first_case_scheduled_start_time=MORNING_HOURS_START_TIME + timedelta(minutes=15),
        first_case_duration_minutes=60,
        first_case_room="room_id_1",
        time_gap_minutes=0,
        second_case_scheduled_start_time=MORNING_HOURS_START_TIME,
        second_case_duration_minutes=60,
        second_case_room="room_id_2",
    )

    case_three = create_test_case(
        scheduled_start_time=MORNING_HOURS_START_TIME + timedelta(minutes=30),
        duration_minutes=60,
        room_id="room_id_3",
    )

    case_staff_one = create_case_staff()
    case_staff_two = create_case_staff()
    case_staff_three = create_case_staff()
    test_site = create_test_site()

    cases_to_process = [
        (case_one, case_staff_one, test_site),
        (case_two, case_staff_two, test_site),
        (case_three, case_staff_three, test_site),
    ]
    mock_store.get_cases_to_process = AsyncMock(return_value=cases_to_process)

    mock_store.upsert_case_derived_properties.reset_mock()
    await service.process_case_properties(
        case_properties_update_dto=[
            ProcessCaseDerivedPropertiesDto(site_id=test_site.id, date=INITIAL_START_TIME)
        ]
    )

    expected_cases_to_upsert = [
        CaseDerivedProperties(case_id=case_one.case_id, is_first_case=True, is_in_flip_room=False),
        CaseDerivedProperties(case_id=case_two.case_id, is_first_case=True, is_in_flip_room=False),
        CaseDerivedProperties(
            case_id=case_three.case_id, is_first_case=True, is_in_flip_room=False
        ),
    ]
    expected_cases_to_upsert.sort(key=lambda case: case.case_id)
    mock_store.upsert_case_derived_properties.assert_called_with(expected_cases_to_upsert)


@pytest.mark.asyncio
async def test_first_case_flip_room_same_scheduled_start_time() -> None:
    """
    For two flip rooms with the same scheduled start time, both are first case since they are different staff
    """
    case_one, case_two = create_two_cases(
        first_case_scheduled_start_time=MORNING_HOURS_START_TIME,
        first_case_duration_minutes=60,
        first_case_room="room_id_1",
        time_gap_minutes=0,
        second_case_scheduled_start_time=MORNING_HOURS_START_TIME,
        second_case_duration_minutes=60,
        second_case_room="room_id_2",
    )

    case_staff_one = create_case_staff()
    case_staff_two = create_case_staff()
    test_site = create_test_site()

    cases_to_process = [
        (case_one, case_staff_one, test_site),
        (case_two, case_staff_two, test_site),
    ]

    mock_case_one_phase = _mock_phase(
        start_time=case_one.scheduled_start_time + timedelta(minutes=15), case_id=case_one.case_id
    )
    mock_case_two_phase = _mock_phase(
        start_time=case_two.scheduled_start_time, case_id=case_two.case_id
    )
    mock_phase_service.query_phases = AsyncMock(
        return_value=[mock_case_one_phase, mock_case_two_phase]
    )

    mock_store.get_cases_to_process = AsyncMock(return_value=cases_to_process)

    mock_store.upsert_case_derived_properties.reset_mock()
    await service.process_case_properties(
        case_properties_update_dto=[
            ProcessCaseDerivedPropertiesDto(site_id=test_site.id, date=INITIAL_START_TIME)
        ]
    )

    expected_cases_to_upsert = [
        CaseDerivedProperties(case_id=case_one.case_id, is_first_case=True, is_in_flip_room=False),
        CaseDerivedProperties(case_id=case_two.case_id, is_first_case=True, is_in_flip_room=False),
    ]
    expected_cases_to_upsert.sort(key=lambda case: case.case_id)
    mock_store.upsert_case_derived_properties.assert_called_with(expected_cases_to_upsert)


@pytest.mark.asyncio
async def test_flip_room_same_scheduled_start_time_swapped_phase_return_order() -> None:
    """
    For two flip rooms with the same scheduled start time, the first case is the one with the earlier actual start time.
    """
    case_one, case_two = create_two_cases(
        first_case_scheduled_start_time=MORNING_HOURS_START_TIME,
        first_case_duration_minutes=60,
        first_case_room="room_id_1",
        time_gap_minutes=0,
        second_case_scheduled_start_time=MORNING_HOURS_START_TIME,
        second_case_duration_minutes=60,
        second_case_room="room_id_2",
    )
    case_staff_one = create_case_staff()
    case_staff_two = create_case_staff()
    test_site = create_test_site()

    cases_to_process = [
        (case_one, case_staff_one, test_site),
        (case_two, case_staff_two, test_site),
    ]

    mock_case_one_phase = _mock_phase(
        start_time=case_one.scheduled_start_time + timedelta(minutes=15), case_id=case_one.case_id
    )
    mock_case_two_phase = _mock_phase(
        start_time=case_two.scheduled_start_time, case_id=case_two.case_id
    )
    mock_phase_service.query_phases = AsyncMock(
        return_value=[mock_case_two_phase, mock_case_one_phase]
    )

    mock_store.get_cases_to_process = AsyncMock(return_value=cases_to_process)

    mock_store.upsert_case_derived_properties.reset_mock()
    await service.process_case_properties(
        case_properties_update_dto=[
            ProcessCaseDerivedPropertiesDto(site_id=test_site.id, date=INITIAL_START_TIME)
        ]
    )

    expected_cases_to_upsert = [
        CaseDerivedProperties(case_id=case_one.case_id, is_first_case=True, is_in_flip_room=False),
        CaseDerivedProperties(case_id=case_two.case_id, is_first_case=True, is_in_flip_room=False),
    ]
    expected_cases_to_upsert.sort(key=lambda case: case.case_id)
    mock_store.upsert_case_derived_properties.assert_called_with(expected_cases_to_upsert)
