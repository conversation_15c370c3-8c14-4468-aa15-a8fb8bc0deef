from unittest.mock import MagicMock, AsyncMock, patch
from uuid import uuid4

from api_server.services.case.case_staff_store import CaseStaffStore
from api_server.services.case.case_staff_service import StaffSiteAppearanceCount
from api_server.services.staff.staff_service import StaffService
from api_server.services.staff.staff_store import StaffModel, StaffStore

from api_server.services.case_duration.case_duration_service import (
    CaseStaffService,
    CaseDurationService,
    DurationPredictions,
    TurnoverPrediction,
    PredictionMetadata,
)
from api_server.services.case_duration.case_duration_store import (
    CaseDurationStore,
    ProcedureOption,
    SurgeonOption,
)
from auth.auth import Auth
import config
import pytest

from databases.cache.persistent_cache import PersistentCache

test_table = "test_table"
config.bigquery_schedule_assistant_surgeon_procedures_table = MagicMock(return_value=test_table)
mock_auth = MagicMock(spec=Auth)

case_staff_service = CaseStaffService(
    auth=mock_auth,
    case_staff_store=MagicMock(spec=CaseStaffStore),
    persistent_cache=MagicMock(spec=PersistentCache),
)
staff_service = StaffService(auth=mock_auth, staff_store=MagicMock(spec=StaffStore))

mock_case_duration_store = MagicMock(spec=CaseDurationStore)
mock_staff_service = MagicMock(spec=staff_service)
mock_case_staff_service = MagicMock(spec=case_staff_service)

service = CaseDurationService(
    auth=mock_auth,
    case_duration_store=mock_case_duration_store,
    staff_service=mock_staff_service,
    case_staff_service=mock_case_staff_service,
    persistent_cache=MagicMock(spec=PersistentCache),
)
surgeon_id = uuid4()
surgeon_first_name = "GREGORY"
surgeon_last_name = "HOUSE"
surgeon_name = f"{surgeon_last_name}, {surgeon_first_name}"
surgeon_model = StaffModel(
    id=surgeon_id, first_name=surgeon_first_name, last_name=surgeon_last_name
)


@pytest.mark.asyncio
@patch("api_server.services.case_duration.case_duration_service.aiohttp.ClientSession")
async def test_get_turnover_prediction_by_id_ok(MockClientSession: AsyncMock) -> None:
    mock_session = MockClientSession.return_value
    mock_session.__aenter__.return_value = mock_session

    mock_response = AsyncMock()
    mock_model_response = {
        "prediction_turnover_before_case": 20,
        "prediction_turnover_after_case": 40,
    }
    mock_response.json.return_value = mock_model_response
    mock_session.post.return_value.__aenter__.return_value = mock_response

    mock_staff_service.get_all_staff = AsyncMock(return_value=[surgeon_model])

    result = await service.get_turnover_prediction(surgeon_id=surgeon_id, procedure="Surgery")

    mock_session.post.assert_called_with(
        config.case_duration_turnover_endpoint(),
        json={
            "inputs": {
                "org_id": mock_auth.get_calling_org_id.return_value,
                "first_primary_surgeon": surgeon_name,
                "first_primary_procedure": "Surgery",
            }
        },
    )
    assert result == TurnoverPrediction(
        PredictionMetadata(
            surgeon_name=surgeon_name, procedure_name="Surgery", additional_procedures=[]
        ),
        before_case=mock_model_response["prediction_turnover_before_case"],
        after_case=mock_model_response["prediction_turnover_after_case"],
        open_before_case=mock_model_response["prediction_turnover_before_case"],
        clean_after_case=mock_model_response["prediction_turnover_after_case"],
    )


@pytest.mark.asyncio
async def test_get_turnover_prediction_no_surgeon_info() -> None:
    with pytest.raises(ValueError):
        await service.get_turnover_prediction(surgeon_id=None, procedure="Surgery")


@pytest.mark.asyncio
@patch("api_server.services.case_duration.case_duration_service.aiohttp.ClientSession")
async def test_get_turnover_prediction_surgeon_not_found(MockClientSession: AsyncMock) -> None:
    mock_session = MockClientSession.return_value
    mock_session.__aenter__.return_value = mock_session

    mock_response = AsyncMock()
    mock_model_response = {
        "prediction_turnover_before_case": 20,
        "prediction_turnover_after_case": 40,
    }
    mock_response.json.return_value = mock_model_response
    mock_session.post.return_value.__aenter__.return_value = mock_response

    mock_staff_service.get_all_staff = AsyncMock(return_value=[])

    with pytest.raises(ValueError):
        await service.get_turnover_prediction(surgeon_id=uuid4(), procedure="Surgery")


@pytest.mark.asyncio
async def test_get_duration_predictions_no_surgeon_info() -> None:
    with pytest.raises(ValueError):
        await service.get_duration_predictions(surgeon_id=None, procedure="Surgery")


@pytest.mark.asyncio
@patch("api_server.services.case_duration.case_duration_service.aiohttp.ClientSession")
async def test_get_duration_predictions_bayesian_model(MockClientSession: AsyncMock) -> None:
    mock_session = MockClientSession.return_value
    mock_session.__aenter__.return_value = mock_session

    mock_response = AsyncMock()
    mock_model_response = {
        "prediction": [150, 250],
    }
    mock_response.json.return_value = mock_model_response
    mock_session.post.return_value.__aenter__.return_value = mock_response

    mock_staff_service.get_all_staff = AsyncMock(return_value=[surgeon_model])
    mock_case_staff_service.get_staff_most_frequent_site = AsyncMock(
        return_value=StaffSiteAppearanceCount(site_id="lab_1", appearance_count=744)
    )
    result = await service.get_duration_predictions(surgeon_id=surgeon_id, procedure="Surgery")

    mock_session.post.assert_called_with(
        config.case_duration_prediction_bayesian_endpoint(),
        json={
            "case_features": {
                "org_id": mock_auth.get_calling_org_id.return_value,
                "first_primary_surgeon": surgeon_name,
                "first_primary_procedure": "Surgery",
                "site_id": "lab_1",
                "additional_procedures": [],
            }
        },
    )
    assert result == DurationPredictions(
        PredictionMetadata(
            surgeon_name=surgeon_name, procedure_name="Surgery", additional_procedures=[]
        ),
        standard=200,
        complex=275,
        samples=[150, 250],
    )


@pytest.mark.asyncio
@patch("api_server.services.case_duration.case_duration_service.aiohttp.ClientSession")
async def test_get_duration_predictions_bayesian_model_none_most_frequent_site(
    MockClientSession: AsyncMock,
) -> None:
    mock_session = MockClientSession.return_value
    mock_session.__aenter__.return_value = mock_session

    mock_response = AsyncMock()
    mock_model_response = {
        "prediction": [150, 250],
    }
    mock_response.json.return_value = mock_model_response
    mock_session.post.return_value.__aenter__.return_value = mock_response

    mock_staff_service.get_all_staff = AsyncMock(return_value=[surgeon_model])
    mock_case_staff_service.get_staff_most_frequent_site = AsyncMock(
        return_value=None  # Simulate no most frequent site found
    )

    result = await service.get_duration_predictions(surgeon_id=surgeon_id, procedure="Surgery")

    mock_session.post.assert_called_with(
        config.case_duration_prediction_bayesian_endpoint(),
        json={
            "case_features": {
                "org_id": mock_auth.get_calling_org_id.return_value,
                "first_primary_surgeon": surgeon_name,
                "first_primary_procedure": "Surgery",
                "site_id": None,
                "additional_procedures": [],
            }
        },
    )
    assert result == DurationPredictions(
        PredictionMetadata(
            surgeon_name=surgeon_name, procedure_name="Surgery", additional_procedures=[]
        ),
        standard=200,
        complex=275,
        samples=[150, 250],
    )


@pytest.mark.asyncio
@patch("api_server.services.case_duration.case_duration_service.aiohttp.ClientSession")
async def test_get_duration_predictions_bayesian_model_additional_procedures(
    MockClientSession: AsyncMock,
) -> None:
    mock_session = MockClientSession.return_value
    mock_session.__aenter__.return_value = mock_session

    mock_response = AsyncMock()
    mock_model_response = {
        "prediction": [150, 250],
    }
    mock_response.json.return_value = mock_model_response
    mock_session.post.return_value.__aenter__.return_value = mock_response

    mock_staff_service.get_all_staff = AsyncMock(return_value=[surgeon_model])
    mock_case_staff_service.get_staff_most_frequent_site = AsyncMock(
        return_value=StaffSiteAppearanceCount(site_id="lab_1", appearance_count=744)
    )
    result = await service.get_duration_predictions(
        surgeon_id=surgeon_id,
        procedure="Surgery",
        additional_procedures=["Procedure A", "Procedure B"],
    )

    mock_session.post.assert_called_with(
        config.case_duration_prediction_bayesian_endpoint(),
        json={
            "case_features": {
                "org_id": mock_auth.get_calling_org_id.return_value,
                "first_primary_surgeon": surgeon_name,
                "first_primary_procedure": "Surgery",
                "site_id": "lab_1",
                "additional_procedures": ["Procedure A", "Procedure B"],
            }
        },
    )
    assert result == DurationPredictions(
        PredictionMetadata(
            surgeon_name=surgeon_name,
            procedure_name="Surgery",
            additional_procedures=["Procedure A", "Procedure B"],
        ),
        standard=200,
        complex=275,
        samples=[150, 250],
    )


@pytest.mark.asyncio
async def test_get_case_duration_procedures_no_org_id() -> None:
    mock_auth.get_calling_org_id.return_value = None
    result = await service.get_case_duration_procedures(procedure_term="Procedure", surgeon_id=None)
    assert result == []


@pytest.mark.asyncio
async def test_get_case_duration_procedures_ok() -> None:
    mock_auth.get_calling_org_id.return_value = "org_123"
    mock_case_duration_store.get_case_duration_procedures.return_value = [
        ProcedureOption(procedure_name="Procedure 1"),
        ProcedureOption(procedure_name="Procedure 2"),
    ]

    result = await service.get_case_duration_procedures(procedure_term="Procedure", surgeon_id=None)

    mock_case_duration_store.get_case_duration_procedures.assert_called_with(
        test_table, "org_123", None, "Procedure"
    )
    assert result == [
        ProcedureOption(procedure_name="Procedure 1"),
        ProcedureOption(procedure_name="Procedure 2"),
    ]


@pytest.mark.asyncio
async def test_get_case_duration_surgeons_no_org_id() -> None:
    mock_auth.get_calling_org_id.return_value = None
    result = await service.get_case_duration_surgeons(surgeon_term="Surgeon")
    assert result == []


@pytest.mark.asyncio
async def test_get_case_duration_surgeons_ok() -> None:
    mock_auth.get_calling_org_id.return_value = "org_123"
    mock_case_duration_store.get_case_duration_surgeons.return_value = [
        SurgeonOption(surgeon_id="surgeon_1", surgeon_name="Surgeon 1"),
        SurgeonOption(surgeon_id="surgeon_2", surgeon_name="Surgeon 2"),
    ]

    result = await service.get_case_duration_surgeons(surgeon_term="Surgeon")

    mock_case_duration_store.get_case_duration_surgeons.assert_called_with(
        "org_123", "Surgeon", test_table
    )
    assert result == [
        SurgeonOption(surgeon_id="surgeon_1", surgeon_name="Surgeon 1"),
        SurgeonOption(surgeon_id="surgeon_2", surgeon_name="Surgeon 2"),
    ]


@pytest.mark.asyncio
async def test_get_case_duration_surgeons_surgeon_id() -> None:
    mock_auth.get_calling_org_id.return_value = "org_123"
    mock_staff_service.get_all_staff = AsyncMock(return_value=[surgeon_model])

    result = await service.get_case_duration_surgeons(surgeon_id=surgeon_id)
    mock_staff_service.get_all_staff.assert_called_with(staff_ids=[str(surgeon_id)])
    assert result == [
        SurgeonOption(surgeon_id=str(surgeon_id), surgeon_name=surgeon_name),
    ]
