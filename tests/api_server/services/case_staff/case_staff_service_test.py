import json
from datetime import timed<PERSON>ta
from unittest.mock import MagicMock, AsyncMock

from api_server.services.case.case_staff_store import CaseStaffStore
from api_server.services.case.case_staff_service import StaffSiteAppearanceCount

from api_server.services.case.case_staff_service import CaseStaffService

from auth.auth import Auth
import config
import pytest

from databases.cache.persistent_cache import PersistentCache

test_table = "test_table"
config.bigquery_staff_site_frequency_table = MagicMock(return_value=test_table)
mock_auth = MagicMock(spec=Auth)


@pytest.mark.asyncio
async def test_get_staff_most_frequent_site_ok() -> None:
    mock_case_staff_store = MagicMock(spec=CaseStaffStore)
    mock_case_staff_store.get_staff_most_frequent_site = AsyncMock(
        return_value={"site_id": "site_123", "appearance_count": 100}
    )
    mock_persistent_cache = MagicMock(spec=PersistentCache)
    mock_persistent_cache.get = MagicMock(return_value=None)
    case_staff_service = CaseStaffService(
        auth=mock_auth,
        case_staff_store=mock_case_staff_store,
        persistent_cache=mock_persistent_cache,
    )

    mock_auth.get_calling_org_id.return_value = "org_123"
    result = await case_staff_service.get_staff_most_frequent_site(staff_id="staff_123")

    mock_case_staff_store.get_staff_most_frequent_site.assert_called_with("staff_123", test_table)
    mock_persistent_cache.set.assert_called_with(
        "org_123_staff_123__most_frequent_site",
        json.dumps({"site_id": "site_123", "appearance_count": 100}),
        expiration=timedelta(days=7),
    )
    assert result == StaffSiteAppearanceCount(site_id="site_123", appearance_count=100)


@pytest.mark.asyncio
async def test_get_staff_most_frequent_site_cached() -> None:
    mock_persistent_cache = MagicMock(spec=PersistentCache)
    mock_persistent_cache.get = MagicMock(
        return_value=json.dumps({"site_id": "site_123", "appearance_count": 100})
    )
    case_staff_service = CaseStaffService(
        auth=mock_auth,
        case_staff_store=MagicMock(spec=CaseStaffStore),
        persistent_cache=mock_persistent_cache,
    )

    mock_auth.get_calling_org_id.return_value = "org_123"
    result = await case_staff_service.get_staff_most_frequent_site(staff_id="staff_123")

    mock_persistent_cache.get.assert_called_with("org_123_staff_123__most_frequent_site")
    assert result == StaffSiteAppearanceCount(site_id="site_123", appearance_count=100)


@pytest.mark.asyncio
async def test_get_staff_most_frequent_site_no_org_id() -> None:
    case_staff_service = CaseStaffService(
        auth=mock_auth,
        case_staff_store=MagicMock(spec=CaseStaffStore),
        persistent_cache=MagicMock(spec=PersistentCache),
    )

    mock_auth.get_calling_org_id.return_value = None

    with pytest.raises(
        ValueError, match="Organization ID is required to query most frequent site."
    ):
        await case_staff_service.get_staff_most_frequent_site(staff_id="staff_123")


@pytest.mark.asyncio
async def test_get_staff_most_frequent_site_no_table() -> None:
    case_staff_service = CaseStaffService(
        auth=mock_auth,
        case_staff_store=MagicMock(spec=CaseStaffStore),
        persistent_cache=MagicMock(spec=PersistentCache),
    )

    mock_auth.get_calling_org_id.return_value = "org_123"
    config.bigquery_staff_site_frequency_table = MagicMock(return_value=None)

    with pytest.raises(
        ValueError, match="BigQuery table for staff site frequency is not configured."
    ):
        await case_staff_service.get_staff_most_frequent_site(staff_id="staff_123")


@pytest.mark.asyncio
async def test_get_staff_most_frequent_site_none_result() -> None:
    mock_case_staff_store = MagicMock(spec=CaseStaffStore)
    mock_case_staff_store.get_staff_most_frequent_site = AsyncMock(return_value=None)
    config.bigquery_staff_site_frequency_table = MagicMock(return_value=test_table)

    mock_persistent_cache = MagicMock(spec=PersistentCache)
    mock_persistent_cache.get = MagicMock(return_value=None)
    case_staff_service = CaseStaffService(
        auth=mock_auth,
        case_staff_store=mock_case_staff_store,
        persistent_cache=mock_persistent_cache,
    )

    mock_auth.get_calling_org_id.return_value = "org_123"
    result = await case_staff_service.get_staff_most_frequent_site(staff_id="staff_123")

    mock_case_staff_store.get_staff_most_frequent_site.assert_called_with("staff_123", test_table)
    mock_persistent_cache.set.assert_not_called()
    assert result is None
