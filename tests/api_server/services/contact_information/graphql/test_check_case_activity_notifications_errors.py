import datetime
import uuid
from typing import Any, cast, Union
from unittest.mock import MagicMock, patch, AsyncMock

from api_server.services.case_activity.case_activity_store import CaseActivityModel
import pytest

from apella_cloud_api import GQLMutation, ObjectField, results_to_dataclass
from apella_cloud_api.new_api_server_schema import ApellaGQLClient, ApellaSchema
from apella_cloud_api.new_input_schema import GQLStaffEventsNotificationsInput
from apella_cloud_api.new_schema_generator.schema_generator_base_classes import (
    GQLObject,
)
from api_server.services.case.case_staff_store import CaseStaffModel
from api_server.services.contact_information.contact_information_store import (
    ContactInformationModel,
    ContactInformationType,
)
from api_server.services.contact_information.staff_event_notification_contact_information_store import (
    StaffEventNotificationContactInformationModel,
    StaffEventNotificationModel,
    SentNotificationStatus,
)

from api_server.services.staff.staff_store import StaffModel
from tests.harness import harness
from utils.helpers import convert_str_to_uuid

FAKE_NOW = datetime.datetime(2021, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc)


def mock_case_activity(
    event_id: str,
    start_time: datetime.datetime,
    org_id: str,
    site_id: str,
    room_id: str,
    case_id: str,
    event_type_id: str = "patient_wheels_in",
    source_type: str = "prediction",
    created_time: datetime.datetime = FAKE_NOW,
) -> CaseActivityModel:
    event = CaseActivityModel()
    event.id = event_id
    event.created_time = created_time
    event.event_type_id = event_type_id
    event.start_time = start_time
    event.process_timestamp = start_time
    event.source = "test"
    event.source_type = source_type
    event.org_id = org_id
    event.site_id = site_id
    event.room_id = room_id
    event.case_id = case_id
    event.updated_time = start_time
    return event


def mock_contact_information(
    contact_information_value: str,
    contact_information_type: ContactInformationType = ContactInformationType.PHONE_NUMBER,
) -> ContactInformationModel:
    contact_info = ContactInformationModel()
    contact_info.id = uuid.uuid4()
    contact_info.contact_information_value = contact_information_value
    contact_info.type = contact_information_type
    contact_info.first_name = "Eve"
    contact_info.last_name = "Draper"
    return contact_info


def mock_staff_contact_information(
    contact_information_id: Any, staff_id: Any, event_type_id: str = "patient_wheels_in"
) -> StaffEventNotificationContactInformationModel:
    staff_contact_information = StaffEventNotificationContactInformationModel()
    staff_contact_information.contact_information_id = convert_str_to_uuid(contact_information_id)
    staff_contact_information.staff_id = convert_str_to_uuid(staff_id)
    staff_contact_information.event_type_id = event_type_id
    staff_contact_information.id = uuid.uuid4()
    return staff_contact_information


def mock_staff(external_staff_id: str, staff_id: str) -> StaffModel:
    staff = StaffModel()
    staff.first_name = "Johnny"
    staff.last_name = "Tsunami"
    staff.org_id = "org_id"
    staff.external_staff_id = external_staff_id
    staff.id = convert_str_to_uuid(staff_id)

    return staff


def mock_case_staff(
    staff_id: str,
    case_id: str,
) -> CaseStaffModel:
    case_staff = CaseStaffModel()
    case_staff.staff_id = convert_str_to_uuid(staff_id)
    case_staff.case_id = case_id
    case_staff.role = "Primary"
    return case_staff


def mock_staff_event_notification(
    staff_event_notification_contact_information_id: Union[uuid.UUID, str],
    event_id: str,
    event_time: datetime.datetime,
    case_id: str,
    attempts: int = 0,
    sent_status: SentNotificationStatus = SentNotificationStatus.SENT,
) -> StaffEventNotificationModel:
    staff_event_notification_contact_information_id_uuid = convert_str_to_uuid(
        staff_event_notification_contact_information_id
    )
    staff_event_notification = StaffEventNotificationModel()
    staff_event_notification.message_id = "aaa"
    staff_event_notification.staff_event_contact_information_id = (
        staff_event_notification_contact_information_id_uuid
    )
    staff_event_notification.event_id = event_id
    staff_event_notification.event_time = event_time
    staff_event_notification.case_id = case_id
    staff_event_notification.attempts = attempts
    staff_event_notification.sent_status = sent_status
    return staff_event_notification


class TestNotifyStaffForEvents:
    @pytest.fixture(autouse=True)
    def mock_datetime_now(self, monkeypatch: Any) -> None:
        class mydatetime(datetime.datetime):
            @classmethod
            def now(cls, tz: Any = None) -> Any:
                return FAKE_NOW

        monkeypatch.setattr(datetime, "datetime", mydatetime)

    @pytest.mark.asyncio
    async def test_check_errors_for_events_no_events(self) -> None:
        apella_gql_client = ApellaGQLClient()

        client, context = harness()
        site = MagicMock()
        site.timezone = "America/New_York"
        context.site_service.get_site = AsyncMock(return_value=site)

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="1234567890",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        context.contact_information_service.query_contact_information = AsyncMock(
            return_value=[mock_contact_information_1]
        )
        context.contact_information_service.query_staff_event_notifications_history = AsyncMock()
        context.contact_information_service.upsert_staff_event_notifications = AsyncMock()

        mock_case_staff_1 = mock_case_staff(staff_id="mock_staff_1_id", case_id="case_0")

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_case_staff_1.staff_id,
        )
        context.case_staff_service.query_case_staff_relationships = AsyncMock(
            return_value=[mock_case_staff_1]
        )
        context.case_staff_plan_service.query = AsyncMock(return_value=[])
        context.contact_information_service.query_many_staff_event_notification_contact_information_relationships = AsyncMock(
            return_value=[mock_staff_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1", staff_id="mock_staff_1_id")
        context.staff_service.query_staff = AsyncMock(return_value=[mock_staff_1])
        context.case_activity_service.get_case_activities = AsyncMock(return_value=[])

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.check_notifications_errors.args(
            input=GQLStaffEventsNotificationsInput(
                site_id="_test_site_id",
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.CheckNotificationsErrors.success,
        )

        missing_inc = MagicMock()
        mock_missing_notifications_counter = MagicMock()
        mock_missing_notifications_counter.labels = MagicMock(return_value=missing_inc)
        excess_inc = MagicMock()
        mock_excess_notifications_counter = MagicMock()
        mock_excess_notifications_counter.labels = MagicMock(return_value=excess_inc)

        with (
            patch(
                "api_server.services.contact_information.graphql.check_notifications_errors._MISSING_NOTIFICATIONS",
                mock_missing_notifications_counter,
            ),
            patch(
                "api_server.services.contact_information.graphql.check_notifications_errors._EXCESS_NOTIFICATIONS",
                mock_excess_notifications_counter,
            ),
        ):
            result_raw = await client.execute_async(
                f"""mutation {{ {mutation} }}""",
                context_value=context,
            )

            result: GQLMutation = cast(
                GQLMutation,
                results_to_dataclass(
                    result_raw["data"],
                    cast(ObjectField[GQLObject], apella_gql_client.mutation.select(mutation)),
                ),
            )

            assert result.check_notifications_errors.success is True
            assert context.case_activity_service.get_case_activities.call_count == 1
            assert missing_inc.inc.call_count == 0
            assert excess_inc.inc.call_count == 0

    @pytest.mark.asyncio
    async def test_check_errors_for_one_missing_event(self) -> None:
        apella_gql_client = ApellaGQLClient()

        client, context = harness()
        site = MagicMock()
        site.timezone = "America/New_York"
        context.site_service.get_site = AsyncMock(return_value=site)

        mock_case_activity_1 = mock_case_activity(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            org_id="case_0_org_id",
            site_id="_test_site_id",
            room_id="case_0_room_id",
            case_id="case_0",
        )
        context.case_activity_service.get_case_activities = AsyncMock(
            return_value=[mock_case_activity_1]
        )

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="1234567890",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        context.contact_information_service.query_contact_information = AsyncMock(
            return_value=[mock_contact_information_1]
        )
        context.contact_information_service.query_staff_event_notifications_history = AsyncMock()
        context.contact_information_service.upsert_staff_event_notifications = AsyncMock()

        mock_case_staff_1 = mock_case_staff(staff_id="mock_staff_1_id", case_id="case_0")

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_case_staff_1.staff_id,
        )
        context.case_staff_service.query_case_staff_relationships = AsyncMock(
            return_value=[mock_case_staff_1]
        )
        context.case_staff_plan_service.query = AsyncMock(return_value=[])
        context.contact_information_service.query_many_staff_event_notification_contact_information_relationships = AsyncMock(
            return_value=[mock_staff_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1", staff_id="mock_staff_1_id")
        context.staff_service.query_staff = AsyncMock(return_value=[mock_staff_1])

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.check_notifications_errors.args(
            input=GQLStaffEventsNotificationsInput(
                site_id="_test_site_id",
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.CheckNotificationsErrors.success,
        )

        missing_inc = MagicMock()
        mock_missing_notifications_counter = MagicMock()
        mock_missing_notifications_counter.labels = MagicMock(return_value=missing_inc)
        excess_inc = MagicMock()
        mock_excess_notifications_counter = MagicMock()
        mock_excess_notifications_counter.labels = MagicMock(return_value=excess_inc)

        with (
            patch(
                "api_server.services.contact_information.graphql.check_notifications_errors._MISSING_NOTIFICATIONS",
                mock_missing_notifications_counter,
            ),
            patch(
                "api_server.services.contact_information.graphql.check_notifications_errors._EXCESS_NOTIFICATIONS",
                mock_excess_notifications_counter,
            ),
        ):
            result_raw = await client.execute_async(
                f"""mutation {{ {mutation} }}""",
                context_value=context,
            )

            result: GQLMutation = cast(
                GQLMutation,
                results_to_dataclass(
                    result_raw["data"],
                    cast(ObjectField[GQLObject], apella_gql_client.mutation.select(mutation)),
                ),
            )
            assert result.check_notifications_errors.success is True
            assert missing_inc.inc.call_count == 1
            assert excess_inc.inc.call_count == 0

    @pytest.mark.asyncio
    async def test_check_errors_for_one_missing_event_now(self) -> None:
        apella_gql_client = ApellaGQLClient()

        client, context = harness()
        site = MagicMock()
        site.timezone = "America/New_York"
        context.site_service.get_site = AsyncMock(return_value=site)

        mock_case_activity_1 = mock_case_activity(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2020, 12, 31, 20, 0, 0, tzinfo=datetime.timezone.utc),
            org_id="case_0_org_id",
            site_id="_test_site_id",
            room_id="case_0_room_id",
            case_id="case_0",
        )
        context.case_activity_service.get_case_activities = AsyncMock(
            return_value=[mock_case_activity_1]
        )

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="1234567890",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        context.contact_information_service.query_contact_information = AsyncMock(
            return_value=[mock_contact_information_1]
        )
        context.contact_information_service.query_staff_event_notifications_history = AsyncMock()
        context.contact_information_service.upsert_staff_event_notifications = AsyncMock()

        mock_case_staff_1 = mock_case_staff(staff_id="mock_staff_1_id", case_id="case_0")

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_case_staff_1.staff_id,
        )
        context.case_staff_service.query_case_staff_relationships = AsyncMock(
            return_value=[mock_case_staff_1]
        )
        context.case_staff_plan_service.query = AsyncMock(return_value=[])
        context.contact_information_service.query_many_staff_event_notification_contact_information_relationships = AsyncMock(
            return_value=[mock_staff_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1", staff_id="mock_staff_1_id")
        context.staff_service.query_staff = AsyncMock(return_value=[mock_staff_1])

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.check_notifications_errors.args(
            input=GQLStaffEventsNotificationsInput(
                site_id="_test_site_id",
                time_window_to_search=datetime.timedelta(hours=24),
            )
        ).select(
            apella_schema.CheckNotificationsErrors.success,
        )

        missing_inc = MagicMock()
        mock_missing_notifications_counter = MagicMock()
        mock_missing_notifications_counter.labels = MagicMock(return_value=missing_inc)
        excess_inc = MagicMock()
        mock_excess_notifications_counter = MagicMock()
        mock_excess_notifications_counter.labels = MagicMock(return_value=excess_inc)

        with (
            patch(
                "api_server.services.contact_information.graphql.check_notifications_errors._MISSING_NOTIFICATIONS",
                mock_missing_notifications_counter,
            ),
            patch(
                "api_server.services.contact_information.graphql.check_notifications_errors._EXCESS_NOTIFICATIONS",
                mock_excess_notifications_counter,
            ),
        ):
            result_raw = await client.execute_async(
                f"""mutation {{ {mutation} }}""",
                context_value=context,
            )

            result: GQLMutation = cast(
                GQLMutation,
                results_to_dataclass(
                    result_raw["data"],
                    cast(ObjectField[GQLObject], apella_gql_client.mutation.select(mutation)),
                ),
            )
            assert result.check_notifications_errors.success is True
            assert missing_inc.inc.call_count == 1
            mock_missing_notifications_counter.labels.assert_called_with(
                "_test_site_id",
                "2020-12-31",
                str(mock_case_activity_1.id),
                str(mock_staff_contact_information_1.id),
                str(mock_case_activity_1.case_id),
                f"{mock_contact_information_1.first_name} {mock_contact_information_1.last_name}",
                "patient_wheels_in",
                f"{mock_staff_1.first_name} {mock_staff_1.last_name}",
            )
            assert excess_inc.inc.call_count == 0

    @pytest.mark.asyncio
    async def test_notify_staff_for_one_excess_event(self) -> None:
        apella_gql_client = ApellaGQLClient()

        client, context = harness()
        site = MagicMock()
        site.timezone = "America/New_York"
        context.site_service.get_site = AsyncMock(return_value=site)

        mock_case_activity_1 = mock_case_activity(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            org_id="case_0_org_id",
            site_id="_test_site_id",
            room_id="case_0_room_id",
            case_id="case_0",
        )
        context.case_activity_service.get_case_activities = AsyncMock(return_value=[])

        mock_case_staff_1 = mock_case_staff(staff_id="mock_staff_1_id", case_id="case_0")

        context.case_staff_service.query_case_staff_relationships = AsyncMock(
            return_value=[mock_case_staff_1]
        )
        context.case_staff_plan_service.query = AsyncMock(return_value=[])
        context.contact_information_service.query_many_staff_event_notification_contact_information_relationships = AsyncMock(
            return_value=[]
        )

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="1234567890",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        context.contact_information_service.query_contact_information = AsyncMock(
            return_value=[mock_contact_information_1]
        )
        context.contact_information_service.query_staff_event_notifications_history = AsyncMock()
        context.contact_information_service.upsert_staff_event_notifications = AsyncMock()

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_case_staff_1.staff_id,
        )
        context.case_staff_service.query_case_staff_relationships = AsyncMock(
            return_value=[mock_case_staff_1]
        )
        context.case_staff_plan_service.query = AsyncMock(return_value=[])
        context.contact_information_service.query_many_staff_event_notification_contact_information_relationships = AsyncMock(
            return_value=[mock_staff_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1", staff_id="mock_staff_1_id")
        context.staff_service.query_staff = AsyncMock(return_value=[mock_staff_1])

        excess_notification = mock_staff_event_notification(
            staff_event_notification_contact_information_id=mock_staff_contact_information_1.id,
            event_id=mock_case_activity_1.id,
            event_time=mock_case_activity_1.start_time,
            case_id="case_0",
            # sent_status=SentNotificationStatus.SENT,
        )
        context.contact_information_service.query_staff_event_notifications_history = AsyncMock(
            side_effect=[[excess_notification, excess_notification], []]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.check_notifications_errors.args(
            input=GQLStaffEventsNotificationsInput(
                site_id="_test_site_id",
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.CheckNotificationsErrors.success,
        )

        missing_inc = MagicMock()
        mock_missing_notifications_counter = MagicMock()
        mock_missing_notifications_counter.labels = MagicMock(return_value=missing_inc)
        excess_inc = MagicMock()
        mock_excess_notifications_counter = MagicMock()
        mock_excess_notifications_counter.labels = MagicMock(return_value=excess_inc)

        with (
            patch(
                "api_server.services.contact_information.graphql.check_notifications_errors._MISSING_NOTIFICATIONS",
                mock_missing_notifications_counter,
            ),
            patch(
                "api_server.services.contact_information.graphql.check_notifications_errors._EXCESS_NOTIFICATIONS",
                mock_excess_notifications_counter,
            ),
        ):
            result_raw = await client.execute_async(
                f"""mutation {{ {mutation} }}""",
                context_value=context,
            )

            result: GQLMutation = cast(
                GQLMutation,
                results_to_dataclass(
                    result_raw["data"],
                    cast(ObjectField[GQLObject], apella_gql_client.mutation.select(mutation)),
                ),
            )
            assert result.check_notifications_errors.success is True
            assert missing_inc.inc.call_count == 0
            assert excess_inc.inc.call_count == 1
            mock_excess_notifications_counter.labels.assert_called_once()
            mock_excess_notifications_counter.labels.assert_called_with(
                "_test_site_id",
                "2020-12-31",
                str(mock_case_activity_1.id),
                str(mock_staff_contact_information_1.id),
                excess_notification.case_id,
                f"{mock_contact_information_1.first_name} {mock_contact_information_1.last_name}",
                "patient_wheels_in",
                f"{mock_staff_1.first_name} {mock_staff_1.last_name}",
            )
