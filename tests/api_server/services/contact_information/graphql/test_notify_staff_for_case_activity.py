import datetime
import uuid
from typing import Any, cast, Union
from unittest.mock import MagicMock, AsyncMock

from api_server.services.case_activity.case_activity_store import CaseActivityModel
import pytest

from apella_cloud_api import GQLMutation, ObjectField, results_to_dataclass
from apella_cloud_api.new_api_server_schema import ApellaGQLClient, ApellaSchema
from apella_cloud_api.new_input_schema import GQLStaffEventsNotificationsInput
from apella_cloud_api.new_schema_generator.schema_generator_base_classes import (
    GQLObject,
)
from api_server.services.case.case_staff_store import CaseStaffModel
from api_server.services.contact_information.contact_information_store import (
    ContactInformationModel,
    ContactInformationType,
)
from api_server.services.contact_information.staff_event_notification_contact_information_store import (
    StaffEventNotificationContactInformationModel,
    StaffEventNotificationModel,
    SentNotificationStatus,
)

from api_server.services.plan.case_staff_plan_store import CaseStaffPlanModel
from api_server.services.staff.staff_store import StaffModel
from tests.harness import harness
from utils.helpers import convert_str_to_uuid

FAKE_NOW = datetime.datetime(2021, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc)


def mock_case_activity(
    event_id: str,
    start_time: datetime.datetime,
    org_id: str,
    site_id: str,
    room_id: str,
    case_id: str,
    event_type_id: str = "patient_wheels_in",
    source_type: str = "prediction",
    created_time: datetime.datetime = FAKE_NOW,
) -> CaseActivityModel:
    event = CaseActivityModel()
    event.id = event_id
    event.created_time = created_time
    event.event_type_id = event_type_id
    event.start_time = start_time
    event.process_timestamp = start_time
    event.source = "test"
    event.source_type = source_type
    event.org_id = org_id
    event.site_id = site_id
    event.room_id = room_id
    event.case_id = case_id
    event.updated_time = created_time
    return event


def mock_contact_information(
    contact_information_value: str,
    contact_information_type: ContactInformationType = ContactInformationType.PHONE_NUMBER,
) -> ContactInformationModel:
    contact_info = ContactInformationModel()
    contact_info.id = uuid.uuid4()
    contact_info.contact_information_value = contact_information_value
    contact_info.type = contact_information_type
    return contact_info


def mock_staff_contact_information(
    contact_information_id: Union[str, uuid.UUID],
    staff_id: Union[str, uuid.UUID],
    event_type_id: str = "patient_wheels_in",
) -> StaffEventNotificationContactInformationModel:
    staff_contact_information = StaffEventNotificationContactInformationModel()
    staff_contact_information.contact_information_id = convert_str_to_uuid(contact_information_id)
    staff_contact_information.staff_id = convert_str_to_uuid(staff_id)
    staff_contact_information.event_type_id = event_type_id
    staff_contact_information.id = uuid.uuid4()
    return staff_contact_information


def mock_staff(external_staff_id: str, staff_id: Union[str, uuid.UUID]) -> StaffModel:
    staff = StaffModel()
    staff.first_name = "Johnny"
    staff.last_name = "Tsunami"
    staff.org_id = "org_id"
    staff.external_staff_id = external_staff_id
    staff.id = convert_str_to_uuid(staff_id)

    return staff


def mock_case_staff(
    staff_id: Union[str, uuid.UUID],
    case_id: str,
) -> CaseStaffModel:
    case_staff = CaseStaffModel()
    case_staff.staff_id = convert_str_to_uuid(staff_id)
    case_staff.case_id = case_id
    case_staff.role = "Primary"
    return case_staff


def mock_case_staff_plan(
    staff_id: Union[str, uuid.UUID],
    case_id: str,
) -> CaseStaffPlanModel:
    case_staff = CaseStaffPlanModel()
    case_staff.staff_id = convert_str_to_uuid(staff_id)
    case_staff.case_id = case_id
    case_staff.role = "Primary"
    return case_staff


def mock_staff_event_notification(
    staff_event_notification_contact_information_id: Union[str, uuid.UUID],
    event_id: str,
    event_time: datetime.datetime,
    case_id: str,
    attempts: int = 0,
    sent_status: SentNotificationStatus = SentNotificationStatus.SENT,
) -> StaffEventNotificationModel:
    staff_event_notification = StaffEventNotificationModel()
    staff_event_notification.message_id = "aaa"
    staff_event_notification.staff_event_contact_information_id = convert_str_to_uuid(
        staff_event_notification_contact_information_id
    )
    staff_event_notification.event_id = event_id
    staff_event_notification.event_time = event_time
    staff_event_notification.case_id = case_id
    staff_event_notification.attempts = attempts
    staff_event_notification.sent_status = sent_status
    return staff_event_notification


mock_obx_type_1 = MagicMock()
mock_obx_type_1.id = "OBSERVED_IN_PRE_PROCEDURE"
mock_obx_type_1.name = "Observed In Pre-Procedure"

mock_obx_type_2 = MagicMock()
mock_obx_type_2.id = "OBSERVED_PRE_PROCEDURE_COMPLETE"
mock_obx_type_2.name = "Observed Pre-Procedure Complete"


# TODO: Add tests for when Twilio fails to send a message
class TestNotifyStaffForEvents:
    @pytest.fixture(autouse=True)
    def mock_datetime_now(self, monkeypatch: Any) -> None:
        class mydatetime(datetime.datetime):
            @classmethod
            def now(cls, tz: Any = None) -> Any:
                return FAKE_NOW

        monkeypatch.setattr(datetime, "datetime", mydatetime)

    @pytest.mark.asyncio
    async def test_notify_staff_for_events_no_events(self) -> None:
        apella_schema = ApellaSchema()
        apella_gql_client = ApellaGQLClient()

        client, context = harness()
        site = MagicMock()
        site.timezone = "America/New_York"
        context.site_service.get_site = AsyncMock(return_value=site)
        context.phase_service.query_phases = AsyncMock()
        context.launch_darkly_service.get_feature_flag = MagicMock(return_value=False)
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id="_test_site_id",
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            ),
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )
        mock_now = MagicMock(
            return_vale=datetime.datetime(2021, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc)
        )
        mock_datetime = MagicMock()
        mock_datetime.now = mock_now
        context.case_activity_service.get_case_activities = AsyncMock()
        result_raw = await client.execute_async(
            f"""mutation {{ {mutation} }}""",
            context_value=context,
        )

        result: GQLMutation = cast(
            GQLMutation,
            results_to_dataclass(
                result_raw["data"],
                cast(ObjectField[GQLObject], apella_gql_client.mutation.select(mutation)),
            ),
        )
        assert result.notify_staff_for_events.success is True
        assert context.case_activity_service.get_case_activities.call_count == 1
        context.contact_information_service.query_contact_information.assert_not_called()
        context.contact_information_service.query_many_staff_event_notification_contact_information_relationships.assert_not_called()

    @pytest.mark.asyncio
    async def test_notify_staff_for_one_event(self) -> None:
        apella_gql_client = ApellaGQLClient()

        client, context = harness()
        site = AsyncMock()
        site.timezone = "America/New_York"
        context.site_service.get_site = AsyncMock(return_value=site)
        context.phase_service.query_phases = AsyncMock()
        context.launch_darkly_service.get_feature_flag = MagicMock(return_value=False)
        mock_case_activity_1 = mock_case_activity(
            event_id="event_1",
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            org_id="case_0_org_id",
            site_id="case_0_site_id",
            room_id="case_0_room_id",
            case_id="case_0",
        )
        context.case_activity_service.get_case_activities = AsyncMock(
            return_value=[mock_case_activity_1]
        )

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        context.contact_information_service.query_contact_information = AsyncMock(
            return_value=[mock_contact_information_1]
        )
        context.contact_information_service.query_staff_event_notifications_history = AsyncMock(
            return_value=[]
        )
        context.contact_information_service.upsert_staff_event_notifications = AsyncMock()

        mock_staff_1 = mock_staff(external_staff_id="staff_1", staff_id="mock_staff_1_id")
        context.staff_service.query_staff = AsyncMock(return_value=[mock_staff_1])

        mock_case_staff_1 = mock_case_staff(staff_id="mock_staff_1_id", case_id="case_0")
        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_case_staff_1.staff_id,
        )
        context.case_staff_service.query_case_staff_relationships = AsyncMock(
            return_value=[mock_case_staff_1]
        )
        context.case_staff_plan_service.query = AsyncMock(return_value=[])

        context.contact_information_service.query_many_staff_event_notification_contact_information_relationships = AsyncMock(
            return_value=[mock_staff_contact_information_1]
        )

        context.observation_service.query_observation_types = AsyncMock(
            return_value=[mock_obx_type_1, mock_obx_type_2]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id="_test_site_id",
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                # use_streaming_table=True,
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )
        result_raw = await client.execute_async(
            f"""mutation {{ {mutation} }}""",
            context_value=context,
        )

        result: GQLMutation = cast(
            GQLMutation,
            results_to_dataclass(
                result_raw["data"],
                cast(ObjectField[GQLObject], apella_gql_client.mutation.select(mutation)),
            ),
        )
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 1

    @pytest.mark.asyncio
    async def test_notify_staff_for_one_event_health_first(self) -> None:
        apella_gql_client = ApellaGQLClient()

        client, context = harness()
        site = AsyncMock()
        site.timezone = "America/New_York"
        context.site_service.get_site = AsyncMock(return_value=site)
        context.launch_darkly_service.get_feature_flag = MagicMock(return_value=False)
        context.phase_service.query_phases = AsyncMock()
        mock_case_activity_1 = mock_case_activity(
            event_id="event_1",
            start_time=datetime.datetime(2021, 1, 2, 12, 0, 0, tzinfo=datetime.timezone.utc),
            org_id="case_0_org_id",
            site_id="case_0_site_id",
            room_id="case_0_room_id",
            case_id="case_0",
        )
        context.case_activity_service.get_case_activities = AsyncMock(
            return_value=[mock_case_activity_1]
        )

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        context.contact_information_service.query_contact_information = AsyncMock(
            return_value=[mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1", staff_id="mock_staff_1_id")
        context.staff_service.query_staff = AsyncMock(return_value=[mock_staff_1])

        mock_case_staff_1 = mock_case_staff(staff_id="mock_staff_1_id", case_id="case_0")
        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_case_staff_1.staff_id,
        )
        context.case_staff_service.query_case_staff_relationships = AsyncMock(
            return_value=[mock_case_staff_1]
        )
        context.case_staff_plan_service.query = AsyncMock(return_value=[])

        context.contact_information_service.query_many_staff_event_notification_contact_information_relationships = AsyncMock(
            return_value=[mock_staff_contact_information_1]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id="HF-VH02",
                time_to_check=datetime.datetime(2021, 1, 2, 12, 5, 0, tzinfo=datetime.timezone.utc),
                # use_streaming_table=True,
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )
        result_raw = await client.execute_async(
            f"""mutation {{ {mutation} }}""",
            context_value=context,
        )

        result: GQLMutation = cast(
            GQLMutation,
            results_to_dataclass(
                result_raw["data"],
                cast(ObjectField[GQLObject], apella_gql_client.mutation.select(mutation)),
            ),
        )
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 0

    @pytest.mark.asyncio
    async def test_notify_staff_for_one_event_fail(self) -> None:
        apella_gql_client = ApellaGQLClient()

        client, context = harness()
        site = MagicMock()
        site.timezone = "America/New_York"
        context.site_service.get_site = AsyncMock(return_value=site)
        context.launch_darkly_service.get_feature_flag = MagicMock(return_value=False)
        context.phase_service.query_phases = AsyncMock()
        mock_case_activity_1 = mock_case_activity(
            event_id="event_1",
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            org_id="case_0_org_id",
            site_id="case_0_site_id",
            room_id="case_0_room_id",
            case_id="case_0",
        )
        context.case_activity_service.get_case_activities = AsyncMock(
            return_value=[mock_case_activity_1]
        )

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        context.contact_information_service.query_contact_information = AsyncMock(
            return_value=[mock_contact_information_1]
        )
        context.contact_information_service.upsert_staff_event_notifications = AsyncMock()

        mock_staff_1 = mock_staff(external_staff_id="staff_1", staff_id="mock_staff_1_id")
        context.staff_service.query_staff = AsyncMock(return_value=[mock_staff_1])

        mock_case_staff_1 = mock_case_staff(staff_id="mock_staff_1_id", case_id="case_0")
        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_case_staff_1.staff_id,
        )
        context.case_staff_service.query_case_staff_relationships = AsyncMock(
            return_value=[mock_case_staff_1]
        )
        context.case_staff_plan_service.query = AsyncMock(return_value=[])

        context.contact_information_service.query_many_staff_event_notification_contact_information_relationships = AsyncMock(
            return_value=[mock_staff_contact_information_1]
        )
        context.contact_information_service.send_text_message = MagicMock(
            side_effect=Exception("Failed to send message")
        )

        context.contact_information_service.query_staff_event_notifications_history = AsyncMock(
            return_value=[
                mock_staff_event_notification(
                    staff_event_notification_contact_information_id=mock_staff_contact_information_1.id,
                    event_id=mock_case_activity_1.id,
                    event_time=mock_case_activity_1.start_time,
                    case_id="case_0",
                    attempts=1,
                    sent_status=SentNotificationStatus.FAILED,
                )
            ]
        )
        context.observation_service.query_observation_types = AsyncMock(
            return_value=[mock_obx_type_1, mock_obx_type_2]
        )
        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id="_test_site_id",
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                # use_streaming_table=True,
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result_raw = await client.execute_async(
            f"""mutation {{ {mutation} }}""",
            context_value=context,
        )

        result: GQLMutation = cast(
            GQLMutation,
            results_to_dataclass(
                result_raw["data"],
                cast(ObjectField[GQLObject], apella_gql_client.mutation.select(mutation)),
            ),
        )
        assert result.notify_staff_for_events.success is False
        assert result.notify_staff_for_events.sent_count == 0
        assert context.contact_information_service.upsert_staff_event_notifications.call_count == 1
        assert (
            context.contact_information_service.upsert_staff_event_notifications.call_args[0][0][
                0
            ].attempts
            == 2
        )

    @pytest.mark.asyncio
    async def test_notify_staff_for_one_event_from_plan(self) -> None:
        apella_gql_client = ApellaGQLClient()

        client, context = harness()
        site = MagicMock()
        site.timezone = "America/New_York"
        context.site_service.get_site = AsyncMock(return_value=site)
        context.launch_darkly_service.get_feature_flag = MagicMock(return_value=False)
        context.phase_service.query_phases = AsyncMock()
        mock_case_activity_1 = mock_case_activity(
            event_id="event_1",
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            org_id="case_0_org_id",
            site_id="case_0_site_id",
            room_id="case_0_room_id",
            case_id="case_0",
        )
        context.case_activity_service.get_case_activities = AsyncMock(
            return_value=[mock_case_activity_1]
        )

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        context.contact_information_service.query_contact_information = AsyncMock(
            return_value=[mock_contact_information_1]
        )
        context.contact_information_service.query_staff_event_notifications_history = AsyncMock(
            return_value=[]
        )
        context.contact_information_service.upsert_staff_event_notifications = AsyncMock()

        mock_staff_1 = mock_staff(external_staff_id="staff_1", staff_id="mock_staff_1_id")
        context.staff_service.query_staff = AsyncMock(return_value=[mock_staff_1])

        mock_case_staff_plan_1 = mock_case_staff_plan(staff_id="mock_staff_1_id", case_id="case_0")
        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_case_staff_plan_1.staff_id,
        )
        context.case_staff_service.query_case_staff_relationships = AsyncMock(return_value=[])
        context.case_staff_plan_service.query = AsyncMock(return_value=[mock_case_staff_plan_1])

        context.contact_information_service.query_many_staff_event_notification_contact_information_relationships = AsyncMock(
            return_value=[mock_staff_contact_information_1]
        )
        context.observation_service.query_observation_types = AsyncMock(
            return_value=[mock_obx_type_1, mock_obx_type_2]
        )
        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id="_test_site_id",
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                # use_streaming_table=True,
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result_raw = await client.execute_async(
            f"""mutation {{ {mutation} }}""",
            context_value=context,
        )

        result: GQLMutation = cast(
            GQLMutation,
            results_to_dataclass(
                result_raw["data"],
                cast(ObjectField[GQLObject], apella_gql_client.mutation.select(mutation)),
            ),
        )
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 1

    @pytest.mark.asyncio
    async def test_notify_staff_for_one_event_from_plan_and_non_plan(self) -> None:
        apella_gql_client = ApellaGQLClient()

        client, context = harness()
        site = MagicMock()
        site.timezone = "America/New_York"
        context.site_service.get_site = AsyncMock(return_value=site)
        context.launch_darkly_service.get_feature_flag = MagicMock(return_value=False)
        context.phase_service.query_phases = AsyncMock()
        mock_case_activity_1 = mock_case_activity(
            event_id="event_1",
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            org_id="case_0_org_id",
            site_id="case_0_site_id",
            room_id="case_0_room_id",
            case_id="case_0",
        )
        context.case_activity_service.get_case_activities = AsyncMock(
            return_value=[mock_case_activity_1]
        )

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        context.contact_information_service.query_contact_information = AsyncMock(
            return_value=[mock_contact_information_1]
        )
        context.contact_information_service.query_staff_event_notifications_history = AsyncMock(
            return_value=[]
        )
        context.contact_information_service.upsert_staff_event_notifications = AsyncMock()

        mock_staff_1 = mock_staff(external_staff_id="staff_1", staff_id="mock_staff_1_id")
        mock_staff_2 = mock_staff(external_staff_id="staff_1", staff_id="mock_staff_2_id")
        context.staff_service.query_staff = AsyncMock(return_value=[mock_staff_1, mock_staff_2])

        mock_case_staff_1 = mock_case_staff(staff_id=mock_staff_1.id, case_id="case_0")
        mock_case_staff_plan_1 = mock_case_staff_plan(staff_id=mock_staff_2.id, case_id="case_0")
        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_case_staff_1.staff_id,
        )
        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_case_staff_plan_1.staff_id,
        )
        context.case_staff_service.query_case_staff_relationships = AsyncMock(
            return_value=[mock_case_staff_1]
        )
        context.case_staff_plan_service.query = AsyncMock(return_value=[mock_case_staff_plan_1])

        context.contact_information_service.query_many_staff_event_notification_contact_information_relationships = AsyncMock(
            return_value=[mock_staff_contact_information_1, mock_staff_contact_information_2]
        )
        context.observation_service.query_observation_types = AsyncMock(
            return_value=[mock_obx_type_1, mock_obx_type_2]
        )
        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id="_test_site_id",
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                # use_streaming_table=True,
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result_raw = await client.execute_async(
            f"""mutation {{ {mutation} }}""",
            context_value=context,
        )

        result: GQLMutation = cast(
            GQLMutation,
            results_to_dataclass(
                result_raw["data"],
                cast(ObjectField[GQLObject], apella_gql_client.mutation.select(mutation)),
            ),
        )
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 1

    @pytest.mark.asyncio
    async def test_notify_multiple_contacts_from_multiple_staff(self) -> None:
        apella_gql_client = ApellaGQLClient()

        client, context = harness()
        site = MagicMock()
        site.timezone = "America/New_York"
        context.site_service.get_site = AsyncMock(return_value=site)
        context.launch_darkly_service.get_feature_flag = MagicMock(return_value=False)
        context.phase_service.query_phases = AsyncMock()
        mock_case_activity_1 = mock_case_activity(
            event_id="event_1",
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            org_id="case_0_org_id",
            site_id="case_0_site_id",
            room_id="case_0_room_id",
            case_id="case_0",
        )
        context.case_activity_service.get_case_activities = AsyncMock(
            return_value=[mock_case_activity_1]
        )

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )

        mock_contact_information_2 = mock_contact_information(
            contact_information_value="1234567891",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        context.contact_information_service.query_contact_information = AsyncMock(
            return_value=[mock_contact_information_1, mock_contact_information_2]
        )
        context.contact_information_service.query_staff_event_notifications_history = AsyncMock(
            return_value=[]
        )
        context.contact_information_service.upsert_staff_event_notifications = AsyncMock()

        mock_staff_1 = mock_staff(external_staff_id="staff_1", staff_id="mock_staff_1_id")
        mock_staff_2 = mock_staff(external_staff_id="staff_1", staff_id="mock_staff_2_id")
        context.staff_service.query_staff = AsyncMock(return_value=[mock_staff_1, mock_staff_2])

        mock_case_staff_1 = mock_case_staff(staff_id=mock_staff_1.id, case_id="case_0")
        mock_case_staff_plan_1 = mock_case_staff_plan(staff_id=mock_staff_2.id, case_id="case_0")
        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_case_staff_1.staff_id,
        )

        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_2.id,
            staff_id=mock_case_staff_plan_1.staff_id,
        )

        context.case_staff_service.query_case_staff_relationships = AsyncMock(
            return_value=[mock_case_staff_1]
        )
        context.case_staff_plan_service.query = AsyncMock(return_value=[mock_case_staff_plan_1])

        context.contact_information_service.query_many_staff_event_notification_contact_information_relationships = AsyncMock(
            return_value=[mock_staff_contact_information_1, mock_staff_contact_information_2]
        )
        context.observation_service.query_observation_types = AsyncMock(
            return_value=[mock_obx_type_1, mock_obx_type_2]
        )
        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id="_test_site_id",
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                # use_streaming_table=True,
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result_raw = await client.execute_async(
            f"""mutation {{ {mutation} }}""",
            context_value=context,
        )

        result: GQLMutation = cast(
            GQLMutation,
            results_to_dataclass(
                result_raw["data"],
                cast(ObjectField[GQLObject], apella_gql_client.mutation.select(mutation)),
            ),
        )
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 2

    @pytest.mark.parametrize(
        "source_type,expected_count",
        [
            ("prediction", 1),
            ("human_gt", 1),
            ("forecasting", 0),
        ],
    )
    @pytest.mark.asyncio
    async def test_notify_staff_for_one_no_time_to_check(
        self, source_type: str, expected_count: int
    ) -> None:
        apella_gql_client = ApellaGQLClient()

        client, context = harness()
        site = MagicMock()
        site.timezone = "America/New_York"
        context.site_service.get_site = AsyncMock(return_value=site)
        context.launch_darkly_service.get_feature_flag = MagicMock(return_value=False)
        context.phase_service.query_phases = AsyncMock()
        mock_case_activity_1 = mock_case_activity(
            event_id="event_1",
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            created_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            org_id="case_0_org_id",
            site_id="_test_site_id",
            room_id="case_0_room_id",
            source_type=source_type,
            case_id="case_0",
        )
        context.case_activity_service.get_case_activities = AsyncMock(
            return_value=[mock_case_activity_1]
        )

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        context.contact_information_service.query_contact_information = AsyncMock(
            return_value=[mock_contact_information_1]
        )
        context.contact_information_service.query_staff_event_notifications_history = AsyncMock(
            return_value=[]
        )
        context.contact_information_service.upsert_staff_event_notifications = AsyncMock()

        mock_staff_1 = mock_staff(external_staff_id="staff_1", staff_id="mock_staff_1_id")
        context.staff_service.query_staff = AsyncMock(return_value=[mock_staff_1])

        mock_case_staff_1 = mock_case_staff(staff_id="mock_staff_1_id", case_id="case_0")

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_case_staff_1.staff_id,
        )
        context.case_staff_service.query_case_staff_relationships = AsyncMock(
            return_value=[mock_case_staff_1]
        )
        context.case_staff_plan_service.query = AsyncMock(return_value=[])
        context.contact_information_service.query_many_staff_event_notification_contact_information_relationships = AsyncMock(
            return_value=[mock_staff_contact_information_1]
        )
        context.observation_service.query_observation_types = AsyncMock(
            return_value=[mock_obx_type_1, mock_obx_type_2]
        )
        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id="_test_site_id",
                time_window_to_search=datetime.timedelta(hours=2),
                # use_streaming_table=True,
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result_raw = await client.execute_async(
            f"""mutation {{ {mutation} }}""",
            context_value=context,
        )

        result: GQLMutation = cast(
            GQLMutation,
            results_to_dataclass(
                result_raw["data"],
                cast(ObjectField[GQLObject], apella_gql_client.mutation.select(mutation)),
            ),
        )
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == expected_count

    @pytest.mark.parametrize(
        "created_time,expected_count",
        [
            # This has to be in the future for testing because of inconsistent timezones on CI
            (datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc), 1),
        ],
    )
    @pytest.mark.asyncio
    async def test_notify_staff_for_one_no_time_to_check_no_confidence(
        self, created_time: datetime.datetime, expected_count: int
    ) -> None:
        apella_gql_client = ApellaGQLClient()

        client, context = harness()
        site = MagicMock()
        site.timezone = "America/New_York"
        context.site_service.get_site = AsyncMock(return_value=site)
        context.launch_darkly_service.get_feature_flag = MagicMock(return_value=False)
        context.phase_service.query_phases = AsyncMock()
        mock_case_activity_1 = mock_case_activity(
            event_id="event_1",
            start_time=created_time,
            created_time=created_time,
            org_id="case_0_org_id",
            site_id="_test_site_id",
            room_id="case_0_room_id",
            case_id="case_0",
        )
        context.case_activity_service.get_case_activities = AsyncMock(
            return_value=[mock_case_activity_1]
        )

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        context.contact_information_service.query_contact_information = AsyncMock(
            return_value=[mock_contact_information_1]
        )
        context.contact_information_service.query_staff_event_notifications_history = AsyncMock(
            return_value=[]
        )
        context.contact_information_service.upsert_staff_event_notifications = AsyncMock()

        mock_staff_1 = mock_staff(external_staff_id="staff_1", staff_id="mock_staff_1_id")
        context.staff_service.query_staff = AsyncMock(return_value=[mock_staff_1])

        mock_case_staff_1 = mock_case_staff(staff_id="mock_staff_1_id", case_id="case_0")
        # harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_case_staff_1.staff_id,
        )
        context.case_staff_service.query_case_staff_relationships = AsyncMock(
            return_value=[mock_case_staff_1]
        )
        context.case_staff_plan_service.query = AsyncMock(return_value=[])
        context.contact_information_service.query_many_staff_event_notification_contact_information_relationships = AsyncMock(
            return_value=[mock_staff_contact_information_1]
        )
        context.observation_service.query_observation_types = AsyncMock(
            return_value=[mock_obx_type_1, mock_obx_type_2]
        )
        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id="_test_site_id",
                time_window_to_search=datetime.timedelta(hours=2),
                # use_streaming_table=True,
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result_raw = await client.execute_async(
            f"""mutation {{ {mutation} }}""",
            context_value=context,
        )

        result: GQLMutation = cast(
            GQLMutation,
            results_to_dataclass(
                result_raw["data"],
                cast(ObjectField[GQLObject], apella_gql_client.mutation.select(mutation)),
            ),
        )
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == expected_count

    @pytest.mark.asyncio
    async def test_retry_failed_notifications(self) -> None:
        apella_schema = ApellaSchema()
        apella_gql_client = ApellaGQLClient()

        client, context = harness()
        site = MagicMock()
        site.timezone = "America/New_York"
        context.site_service.get_site = AsyncMock(return_value=site)
        context.case_activity_service.get_case_activities = AsyncMock()
        context.launch_darkly_service.get_feature_flag = MagicMock(return_value=False)
        context.phase_service.query_phases = AsyncMock()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id="_test_site_id",
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                # use_streaming_table=True,
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )
        mock_now = MagicMock(
            return_vale=datetime.datetime(2021, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc)
        )
        mock_datetime = MagicMock()
        mock_datetime.now = mock_now

        result_raw = await client.execute_async(
            f"""mutation {{ {mutation} }}""",
            context_value=context,
        )

        result: GQLMutation = cast(
            GQLMutation,
            results_to_dataclass(
                result_raw["data"],
                cast(ObjectField[GQLObject], apella_gql_client.mutation.select(mutation)),
            ),
        )
        assert result.notify_staff_for_events.success is True
        assert context.case_activity_service.get_case_activities.call_count == 1
        context.contact_information_service.query_contact_information.assert_not_called()
        context.contact_information_service.query_many_staff_event_notification_contact_information_relationships.assert_not_called()
