from api_server.services.custom_phase_config.custom_phase_config_service import (
    CustomPhaseConfigService,
    CustomPhaseConfigModel,
)
from unittest.mock import MagicMock, AsyncMock
import pytest
from auth.auth import Auth
from uuid import uuid4


class TestCustomPhaseConfigService:
    mock_custom_phase_config_store: MagicMock
    mock_auth: MagicMock
    custom_phase_config_service: CustomPhaseConfigService

    def setup_method(self) -> None:
        self.mock_custom_phase_config_store = MagicMock()
        self.mock_auth = MagicMock(spec=Auth)
        self.custom_phase_config_service = CustomPhaseConfigService(
            auth=self.mock_auth,
            custom_phase_config_store=self.mock_custom_phase_config_store,
        )

    def create_test_custom_phase_config(self) -> CustomPhaseConfigModel:
        custom_phase_config = CustomPhaseConfigModel()
        custom_phase_config.id = uuid4()
        custom_phase_config.org_id = "org_id"
        custom_phase_config.start_event_type = "start_event_type"
        custom_phase_config.end_event_type = "end_event_type"
        custom_phase_config.name = "name"
        custom_phase_config.description = "description"

        return custom_phase_config

    @pytest.mark.asyncio
    async def test_upsert_custom_phase_config(self) -> None:
        custom_phase_config = self.create_test_custom_phase_config()

        self.mock_auth.get_calling_org_id.return_value = "org_id"
        self.mock_custom_phase_config_store.upsert_custom_phase_config = AsyncMock(
            return_value=custom_phase_config
        )

        result = await self.custom_phase_config_service.upsert_custom_phase_config(
            custom_phase_id=None,
            start_event_type="start_event_type",
            end_event_type="end_event_type",
            name="name",
            description="description",
        )

        assert result == custom_phase_config

    @pytest.mark.asyncio
    async def test_delete_custom_phase_config(self) -> None:
        custom_phase_config = self.create_test_custom_phase_config()

        self.mock_auth.get_calling_org_id.return_value = "org_id"
        self.mock_custom_phase_config_store.upsert_custom_phase_config = AsyncMock(
            return_value=custom_phase_config
        )
        self.mock_custom_phase_config_store.delete_custom_phase_config = AsyncMock()

        # Insert the value.
        await self.custom_phase_config_service.upsert_custom_phase_config(
            custom_phase_id=None,
            start_event_type="start_event_type",
            end_event_type="end_event_type",
            name="name",
            description="description",
        )

        # Delete the value.
        await self.custom_phase_config_service.delete_custom_phase_config(
            custom_phase_id=str(custom_phase_config.id)
        )

        self.mock_custom_phase_config_store.delete_custom_phase_config.assert_called_once_with(
            str(custom_phase_config.id)
        )

    @pytest.mark.asyncio
    async def test_query_custom_phase_configs(self) -> None:
        custom_phase_config = self.create_test_custom_phase_config()

        self.mock_custom_phase_config_store.query_custom_phase_configs = AsyncMock(
            return_value=[custom_phase_config]
        )

        result = await self.custom_phase_config_service.query_custom_phase_configs()

        assert result == [custom_phase_config]
