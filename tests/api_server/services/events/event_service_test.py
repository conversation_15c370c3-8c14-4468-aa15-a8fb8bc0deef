# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

import uuid
from datetime import datetime, timezone
from unittest.mock import AsyncMock, MagicMock

import pytest

from apella_cloud_api.dtos import (
    EventChangelogAction,
    EventHistoryQueryDto,
    EventQueryDto,
)
from apella_cloud_api.exceptions import ClientError
from api_server.services.events.event_pubsub_store import EventPubSubStore
from api_server.services.events.event_service import EventService
from api_server.services.events.event_models import EventModel, EventWithPublish
from api_server.services.events.event_store import EventStore
from api_server.services.organization.organization_service import OrganizationService
from api_server.services.site.site_service import SiteService
from auth.auth import Auth
from auth.permissions import READ_ANY_EVENT, WRITE_ANY_EVENT


class TestEventService:
    mock_event_store: MagicMock
    mock_auth: MagicMock
    mock_site_service: MagicMock
    mock_event_pubsub_store: MagicMock
    mock_organization_service: AsyncMock
    event_service: EventService

    def setup_method(self) -> None:
        self.mock_event_store = AsyncMock(spec=EventStore)
        self.mock_auth = MagicMock(spec=Auth)
        self.mock_auth.get_calling_org_id = MagicMock(return_value=None)
        self.mock_site_service = MagicMock(spec=SiteService)
        self.mock_event_pubsub_store = MagicMock(spec=EventPubSubStore)
        self.mock_organization_service = AsyncMock(spec=OrganizationService)
        self.event_service = EventService(
            self.mock_auth,
            self.mock_event_store,
            self.mock_site_service,
            self.mock_event_pubsub_store,
            self.mock_organization_service,
        )

    @pytest.mark.asyncio
    async def test_get_event(self) -> None:
        test_id = "event_id"
        test_event = EventModel()

        self.mock_auth.requires = AsyncMock()
        self.mock_event_store.get_event = AsyncMock(return_value=test_event)

        result = await self.event_service.get_event(event_id=test_id)

        self.mock_auth.requires.assert_called_with(READ_ANY_EVENT, enforce_universal_user=False)
        self.mock_event_store.get_event.assert_called_with(event_id=test_id)

        assert result == test_event

    def create_test_event(self):
        event = EventModel()
        event.id = str(uuid.uuid4())
        event.event_type_id = "mop_in"
        event.start_time = datetime.fromisoformat("2020-02-02T20:02:20")
        event.process_timestamp = datetime.fromisoformat("2020-02-02T20:20:20")
        event.source_type = "human_gt"
        event.org_id = "test_org_0"
        event.site_id = "test_site_0"
        event.room_id = "test_room_0"
        event.camera_id = "test_camera_0"
        event.source = "test"
        event.model_version = "v1.test"
        event.confidence = 0.8
        event.labels = ["test_label"]
        event.notes = "Test note"
        return event

    @pytest.mark.asyncio
    async def assert_successful_create(self, event):
        self.mock_auth.requires = AsyncMock()
        self.mock_event_store.create_event = AsyncMock()

        result = await self.event_service.create_event(event)

        self.mock_auth.requires.assert_called_with(WRITE_ANY_EVENT, enforce_universal_user=False)
        self.mock_event_store.create_event.assert_called_with(event=event)

        return result

    def assert_published(self, event, action: EventChangelogAction):
        change_dto = event.to_change_dto(action)
        self.mock_event_pubsub_store.publish_event_changelog.assert_called_with(change_dto)

    @pytest.mark.asyncio
    async def assert_failed_validate(self, event, message):
        self.mock_auth.requires_permission_to_write_event = MagicMock()
        with pytest.raises(ClientError) as exc_info:
            await self.event_service.create_event(event)
        assert exc_info.value.message == message

    @pytest.mark.asyncio
    async def test_create_event_with_all_fields(self) -> None:
        event = self.create_test_event()
        result = await self.assert_successful_create(event)
        assert result == event

    @pytest.mark.asyncio
    async def test_create_event_publishes(self) -> None:
        event = self.create_test_event()
        result = await self.assert_successful_create(event)
        self.assert_published(event, EventChangelogAction.CREATE)
        assert result == event

    # TODO should remove this test as we're no longer creating events of type phase since 2022-01
    @pytest.mark.asyncio
    async def test_create_phase_doesnt_publish(self) -> None:
        event = self.create_test_event()
        event.event_type_id = "turn_over_idle"
        result = await self.assert_successful_create(event)
        self.mock_event_pubsub_store.publish_event_changelog.assert_not_called()
        assert result == event

    @pytest.mark.asyncio
    async def test_create_event_no_uuid_populates_it(self) -> None:
        event = self.create_test_event()
        event.id = None
        result = await self.assert_successful_create(event=event)
        assert result.id is not None

    @pytest.mark.asyncio
    async def test_create_event_with_bad_uuid_fails(self) -> None:
        event = self.create_test_event()
        event.id = "bad"
        await self.assert_failed_validate(event, "'id' must be a valid uuid")

    @pytest.mark.asyncio
    async def test_create_event_with_no_type_do_not_fails(self) -> None:
        event = self.create_test_event()
        event.event_type = None
        await self.assert_successful_create(event)

    @pytest.mark.asyncio
    async def test_create_event_with_uncategorized_succeeds(self) -> None:
        event = self.create_test_event()
        event.event_type = "uncategorized"
        await self.assert_successful_create(event)

    @pytest.mark.asyncio
    async def test_create_event_with_no_name_fails(self) -> None:
        event = self.create_test_event()
        event.event_type_id = None
        await self.assert_failed_validate(event, "'event_type_id' must be a string")

    @pytest.mark.asyncio
    async def test_create_event_with_no_org_fails(self) -> None:
        event = self.create_test_event()
        event.org_id = None
        await self.assert_failed_validate(event, "'organization_id' must be a string")

    @pytest.mark.asyncio
    async def test_create_event_with_bad_org_fails(self) -> None:
        event = self.create_test_event()
        event.org_id = 123
        await self.assert_failed_validate(event, "'organization_id' must be a string")

    @pytest.mark.asyncio
    async def test_create_event_with_no_site_fails(self) -> None:
        event = self.create_test_event()
        event.site_id = None
        await self.assert_failed_validate(event, "'site_id' must be a string")

    @pytest.mark.asyncio
    async def test_create_event_with_bad_site_fails(self) -> None:
        event = self.create_test_event()
        event.site_id = 123
        await self.assert_failed_validate(event, "'site_id' must be a string")

    @pytest.mark.asyncio
    async def test_create_event_with_no_room_fails(self) -> None:
        event = self.create_test_event()
        event.room_id = None
        await self.assert_failed_validate(event, "'room_id' must be a string")

    @pytest.mark.asyncio
    async def test_create_event_with_bad_room_fails(self) -> None:
        event = self.create_test_event()
        event.room_id = 123
        await self.assert_failed_validate(event, "'room_id' must be a string")

    @pytest.mark.asyncio
    async def test_create_event_with_no_start_time_fails(self) -> None:
        event = self.create_test_event()
        event.start_time = None
        await self.assert_failed_validate(event, "'start_time' must be a valid date time")

    @pytest.mark.asyncio
    async def test_create_event_with_bad_start_time_fails(self) -> None:
        event = self.create_test_event()
        event.start_time = "bad"
        await self.assert_failed_validate(event, "'start_time' must be a valid date time")

    @pytest.mark.asyncio
    async def test_create_event_with_no_process_time_fails(self) -> None:
        event = self.create_test_event()
        event.process_timestamp = None
        await self.assert_failed_validate(event, "'process_timestamp' must be a valid date time")

    @pytest.mark.asyncio
    async def test_create_event_with_bad_process_time_fails(self) -> None:
        event = self.create_test_event()
        event.process_timestamp = "bad"
        await self.assert_failed_validate(event, "'process_timestamp' must be a valid date time")

    @pytest.mark.asyncio
    async def test_create_event_with_no_source_fails(self) -> None:
        event = self.create_test_event()
        event.source = None
        await self.assert_failed_validate(event, "'source' must be a string")

    @pytest.mark.asyncio
    async def test_create_event_with_bad_source_fails(self) -> None:
        event = self.create_test_event()
        event.source = 123
        await self.assert_failed_validate(event, "'source' must be a string")

    @pytest.mark.asyncio
    async def test_create_event_with_no_source_type_fails(self) -> None:
        event = self.create_test_event()
        event.source_type = None
        await self.assert_failed_validate(
            event,
            "'source_type' must be one of ['human_gt', 'prediction', 'forecasting', 'experimental']",
        )

    @pytest.mark.asyncio
    async def test_create_event_with_bad_source_type_fails(self) -> None:
        event = self.create_test_event()
        event.source_type = "bad"
        await self.assert_failed_validate(
            event,
            "'source_type' must be one of ['human_gt', 'prediction', 'forecasting', 'experimental']",
        )

    @pytest.mark.asyncio
    async def test_create_event_with_no_model_and_prediction_type_fails(self) -> None:
        event = self.create_test_event()
        event.source_type = "prediction"
        event.model_version = None
        await self.assert_failed_validate(event, "'model_version' must be a string")

    @pytest.mark.asyncio
    async def test_create_event_with_no_model_and_human_gt_type_succeeds(self) -> None:
        event = self.create_test_event()
        event.source_type = "human_gt"
        event.model_version = None
        await self.assert_successful_create(event)

    @pytest.mark.asyncio
    async def test_create_event_with_no_confidence_succeeds(self) -> None:
        event = self.create_test_event()
        event.confidence = None
        await self.assert_successful_create(event)

    @pytest.mark.asyncio
    async def test_create_event_with_no_labels_succeeds(self) -> None:
        event = self.create_test_event()
        event.labels = None
        await self.assert_successful_create(event)

    @pytest.mark.asyncio
    async def test_create_event_with_bad_labels_fails(self) -> None:
        event = self.create_test_event()
        event.labels = 123
        await self.assert_failed_validate(event, "'labels' can only be an array of strings")

    @pytest.mark.asyncio
    async def test_create_event_with_bad_label_entries_fails(self) -> None:
        event = self.create_test_event()
        event.labels = ["test_label", 123]
        await self.assert_failed_validate(event, "a label must be a string")

    @pytest.mark.asyncio
    async def test_create_event_with_no_notes_succeeds(self) -> None:
        event = self.create_test_event()
        event.notes = None
        await self.assert_successful_create(event)

    @pytest.mark.asyncio
    async def test_create_event_with_bad_notes_fails(self) -> None:
        event = self.create_test_event()
        event.notes = 123
        await self.assert_failed_validate(event, "'notes' can only be a string")

    @pytest.mark.asyncio
    async def test_create_many_events_succeeds(self) -> None:
        events = [self.create_test_event() for i in range(0, 100)]

        self.mock_auth.requires = AsyncMock()
        self.mock_event_store.create_events = AsyncMock()

        await self.event_service.create_events(events)
        assert self.mock_event_pubsub_store.publish_event_changelog.call_count == 100

        self.mock_auth.requires.assert_called_with(WRITE_ANY_EVENT, enforce_universal_user=True)
        self.mock_event_store.create_events.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_many_events_succeeds_without_changelog_publish(self) -> None:
        events = [self.create_test_event() for i in range(0, 20)]

        self.mock_auth.requires = AsyncMock()
        self.mock_event_store.create_events = AsyncMock()

        await self.event_service.create_events(events, publish_changelog=False)

        self.mock_event_store.create_events.assert_called_once()
        self.mock_event_pubsub_store.publish_event_changelog.assert_not_called()

    @pytest.mark.asyncio
    async def test_create_many_events_with_an_invalid_event_fails(self) -> None:
        events = [self.create_test_event() for i in range(0, 100)]
        events[42].event_type_id = None

        self.mock_auth.requires = AsyncMock()

        with pytest.raises(ClientError, match="'event_type_id' must be a string"):
            await self.event_service.create_events(events)

        self.mock_auth.requires.assert_called_with(WRITE_ANY_EVENT, enforce_universal_user=True)

    @pytest.mark.asyncio
    async def test_replace_event(self) -> None:
        event = self.create_test_event()
        self.mock_auth.requires = AsyncMock()
        self.mock_event_store.replace_event = AsyncMock(return_value=event)

        result = await self.event_service.replace_event(event)

        self.assert_published(event, EventChangelogAction.UPDATE)
        self.mock_auth.requires.assert_called_with(WRITE_ANY_EVENT, enforce_universal_user=False)
        self.mock_event_store.replace_event.assert_called_once()

        assert result == event

    @pytest.mark.asyncio
    async def test_query_event_with_room_ids_and_room_id_fails(self) -> None:
        self.mock_auth.requires = AsyncMock()
        self.mock_event_store.query_events = AsyncMock(return_value=[])

        query = EventQueryDto().from_dict(
            {
                "room_ids": ["room_id_0", "room_id_1"],
                "room_id": "room_id_2",
                "min_time": "2021-01-01T00:00:00-00:00",
                "max_time": "2021-01-02T00:00:00-00:00",
            }
        )

        with pytest.raises(
            ClientError, match="Please specify either room_id or room_ids, not both"
        ):
            await self.event_service.query_events(query)

        self.mock_auth.requires.assert_called_with(READ_ANY_EVENT, enforce_universal_user=False)
        self.mock_event_store.query_events.assert_not_called()

    @pytest.mark.asyncio
    async def test_query_event_with_site_ids_and_site_id_fails(self) -> None:
        self.mock_auth.requires = AsyncMock()
        self.mock_event_store.query_events = AsyncMock(return_value=[])

        query = EventQueryDto().from_dict(
            {
                "site_ids": ["site_id_0", "site_id_1"],
                "site_id": "site_id_2",
                "min_time": "2021-01-01T00:00:00-00:00",
                "max_time": "2021-01-02T00:00:00-00:00",
            }
        )

        with pytest.raises(
            ClientError, match="Please specify either site_id or site_ids, not both"
        ):
            await self.event_service.query_events(query)

        self.mock_auth.requires.assert_called_with(READ_ANY_EVENT, enforce_universal_user=False)
        self.mock_event_store.query_events.assert_not_called()

    @pytest.mark.asyncio
    async def test_query_event_history_uses_validation(self) -> None:
        self.mock_auth.requires = AsyncMock()
        self.mock_event_store.query_event_history = AsyncMock(return_value=[])

        query = EventQueryDto().from_dict(
            {
                "site_ids": ["site_id_0", "site_id_1"],
                "site_id": "site_id_2",
                "min_time": "2021-01-01T00:00:00-00:00",
                "max_time": "2021-01-02T00:00:00-00:00",
            }
        )

        with pytest.raises(
            ClientError, match="Please specify either site_id or site_ids, not both"
        ):
            await self.event_service.query_events(query)

        self.mock_auth.requires.assert_called_with(READ_ANY_EVENT, enforce_universal_user=False)
        self.mock_event_store.query_event_history.assert_not_called()

    @pytest.mark.asyncio
    async def test_query_event(self) -> None:
        self.mock_auth.requires = AsyncMock()
        self.mock_event_store.query_events = AsyncMock(return_value=[])

        query = EventQueryDto().from_dict(
            {
                "organization_id": "test_org",
                "min_time": "2021-01-01T00:00:00-00:00",
                "max_time": "2021-01-02T00:00:00-00:00",
            }
        )

        assert query.min_time == datetime(2021, 1, 1, 0, 0, 0, 0, timezone.utc)
        assert query.max_time == datetime(2021, 1, 2, 0, 0, 0, 0, timezone.utc)

        await self.event_service.query_events(query)

        self.mock_auth.requires.assert_called_with(READ_ANY_EVENT, enforce_universal_user=False)
        self.mock_event_store.query_events.assert_called_with(query)

    @pytest.mark.asyncio
    async def test_query_event_history(self) -> None:
        self.mock_auth.requires = AsyncMock()
        self.mock_event_store.query_event_history = AsyncMock(return_value=[])

        query = EventHistoryQueryDto().from_dict(
            {
                "organization_id": "test_org",
                "min_time": "2021-01-01T00:00:00-00:00",
                "max_time": "2021-01-02T00:00:00-00:00",
            }
        )

        assert query.min_time == datetime(2021, 1, 1, 0, 0, 0, 0, timezone.utc)
        assert query.max_time == datetime(2021, 1, 2, 0, 0, 0, 0, timezone.utc)

        await self.event_service.query_event_history(query)

        self.mock_auth.requires.assert_called_with(READ_ANY_EVENT, enforce_universal_user=False)
        self.mock_event_store.query_event_history.assert_called_with(query)

    @pytest.mark.asyncio
    async def test_delete_event(self) -> None:
        event = self.create_test_event()
        self.mock_auth.requires = AsyncMock()
        self.mock_event_store.delete_event = AsyncMock()
        self.mock_event_store.get_event = AsyncMock(return_value=event)

        await self.event_service.delete_event(event.id, "test_source", "test_source_type")

        self.assert_published(event, EventChangelogAction.DELETE)
        self.mock_auth.requires.assert_called_with(WRITE_ANY_EVENT, enforce_universal_user=False)
        self.mock_event_store.delete_event.assert_called_with(
            event.id, "test_source", "test_source_type"
        )

    @pytest.mark.asyncio
    async def test_delete_event_without_publish(self) -> None:
        event = self.create_test_event()
        self.mock_auth.requires = AsyncMock()
        self.mock_event_store.delete_event = AsyncMock()
        self.mock_event_store.get_event = AsyncMock(return_value=event)

        await self.event_service.delete_event(
            event.id, "test_source", "test_source_type", publish_changelog=False
        )

        self.mock_event_pubsub_store.publish_event_changelog.assert_not_called()
        self.mock_auth.requires.assert_called_with(WRITE_ANY_EVENT, enforce_universal_user=False)
        self.mock_event_store.delete_event.assert_called_with(
            event.id, "test_source", "test_source_type"
        )

    @pytest.mark.asyncio
    async def test_publish_event_input(self) -> None:
        original_event_1 = self.create_test_event()
        original_event_2 = self.create_test_event()
        original_event_3 = self.create_test_event()

        original_events = [original_event_1, original_event_2, original_event_3]

        event_upsert_inputs = [
            EventWithPublish(
                event=original_event,
                publish_changelog=True,
            )
            for original_event in original_events
        ]

        event_upsert_inputs[1].publish_changelog = False
        await self.event_service.upsert_events(event_upsert_inputs)

        assert self.mock_event_pubsub_store.publish_event_changelog.call_count == 2
