from datetime import datetime
from unittest.mock import AsyncMock

import pytest

from api_server.services.events.event_service import EventModel
from api_server.services.events.event_store import EventTypeModel
from tests.harness import harness

turn_over_idle = "turn_over_idle"
camera_id = "test_camera_id"
room_id = "test_room_id"
site_id = "test_site_id"
organization_id = "test_organization_id"


@pytest.mark.asyncio
async def test_create_event_with_all_fields_and_return_event() -> None:
    client, context = harness()

    turn_over_idle_type = EventTypeModel(
        id=turn_over_idle,
        name="Turn Over Idle",
        type="phase",
        description="",
        color="#AABBCC",
    )

    created_event = EventModel(
        id="020a9e02-dceb-4a53-8f84-5b5ac0e8bd94",
        event_type_id=turn_over_idle,
        start_time=datetime.fromisoformat("2021-03-01T00:00:00-07:00"),
        process_timestamp=datetime.fromisoformat("2021-03-01T01:00:00-07:00"),
        source="test-runner",
        source_type="prediction",
        model_version="unit-test",
        camera_id=camera_id,
        room_id=room_id,
        site_id=site_id,
        org_id=organization_id,
        labels=["test label"],
        notes="test notes",
    )

    context.event_service.create_event = AsyncMock(return_value=created_event)
    context.event_attrs_loader.load = AsyncMock(return_value=turn_over_idle_type)

    result = await client.execute_async(
        f"""
        mutation {{
            eventCreate(input: {{
                            id: "020a9e02-dceb-4a53-8f84-5b5ac0e8bd94",
                            name: "{turn_over_idle}",
                            startTime: "2021-03-01T00:00:00-07:00",
                            processTime: "2021-03-01T01:00:00-07:00",
                            source: "test-runner",
                            sourceType: "prediction",
                            modelVersion: "unit-test",
                            cameraId: "{camera_id}",
                            roomId: "{room_id}",
                            siteId: "{site_id}",
                            organizationId: "{organization_id}",
                            labels: [ "test label" ],
                            notes: "test notes"
                            }}) {{
                success
                createdEvent {{
                    id
                    name
                    type
                    label
                    startTime
                    source
                    sourceType
                    modelVersion
                    labels
                    notes
                }}
            }}
        }}
        """,
        context_value=context,
    )

    assert result == {
        "data": {
            "eventCreate": {
                "success": True,
                "createdEvent": {
                    "id": "020a9e02-dceb-4a53-8f84-5b5ac0e8bd94",
                    "name": "turn_over_idle",
                    "type": "phase",
                    "label": "Turn Over Idle",
                    "startTime": "2021-03-01T00:00:00-07:00",
                    "source": "test-runner",
                    "sourceType": "prediction",
                    "modelVersion": "unit-test",
                    "labels": ["test label"],
                    "notes": "test notes",
                },
            }
        }
    }

    context.event_service.create_event.assert_called_once()
