from unittest.mock import AsyncMock

import pytest

from tests.harness import harness


@pytest.mark.asyncio
async def test_delete_succeeds() -> None:
    client, context = harness()

    context.auth.get_calling_org_id = AsyncMock(return_value="test_org")
    context.event_service.delete_event = AsyncMock()

    result = await client.execute_async(
        """
    mutation {
        eventDelete(input: {id: "123", source: "test", sourceType: "test"}) { success }
    }
    """,
        context_value=context,
    )

    assert result == {"data": {"eventDelete": {"success": True}}}

    context.event_service.delete_event.assert_called_with(
        event_id="123", source="test", source_type="test", publish_changelog=True
    )
