import datetime
from unittest.mock import AsyncMock

import pytest

from api_server.services.events.event_store import EventModel, EventTypeModel
from api_server.services.organization.organization_db import Organization
from api_server.services.room.room_store import RoomModel
from api_server.services.site.site_store import Site
from tests.harness import harness

# This comes with event_type_id in normal cases
test_phase = "patient_status"

test_event: EventModel = EventModel()
test_event.id = "test_event_id"
test_event.event_type_id = "patient_wheels_in"
test_event.org_id = "test_organization_id"
test_event.site_id = "test_site_id"
test_event.room_id = "test_room_id"
test_event.camera_id = "test_camera_id"
test_event.start_time = datetime.datetime.fromisoformat("2021-03-01T00:00:00-07:00")
test_event.labels = ["label_0"]
test_event.notes = "test notes"
test_event.source = "test-server"
test_event.source_type = "test-prediction"
test_event.model_version = "test-version"
test_event.confidence = 0.7

test_type = EventTypeModel(
    id=test_event.event_type_id,
    name="I'm a test event type labelled Patient Wheels In",
    type=test_phase,
    description="",
    color="#AABBCC",
    hidden=False,
)

test_organization: Organization = Organization()
test_organization.name = "Test Organization"

test_site: Site = Site()
test_site.name = "Test Site"

test_room: RoomModel = RoomModel()
test_room.name = "Test Room"


@pytest.mark.asyncio
async def test_event_can_be_queried_by_id() -> None:
    client, context = harness()

    context.auth.get_calling_org_id = AsyncMock(return_value="test_org")
    context.event_service.get_event = AsyncMock(return_value=test_event)
    context.event_attrs_loader.load = AsyncMock(return_value=test_type)

    result = await client.execute_async(
        """
    {
        event(id: "test_event_id") {
            id
            name
            type
            hidden
            startTime
            labels
            notes
            source
            sourceType
            modelVersion
            cameraId
        }
    }
    """,
        context_value=context,
    )

    context.event_service.get_event.assert_called_with(event_id=test_event.id)

    assert result == {
        "data": {
            "event": {
                "id": test_event.id,
                "name": test_event.event_type_id,
                "type": test_phase,
                "hidden": test_type.hidden,
                "startTime": test_event.start_time.isoformat(),
                "labels": test_event.labels,
                "notes": test_event.notes,
                "source": test_event.source,
                "sourceType": test_event.source_type,
                "modelVersion": test_event.model_version,
                "cameraId": test_event.camera_id,
            }
        }
    }


@pytest.mark.asyncio
async def test_event_org_can_be_queried() -> None:
    client, context = harness()

    context.auth.get_calling_org_id = AsyncMock(return_value=test_organization.name)
    context.event_service.get_event = AsyncMock(return_value=test_event)
    context.org_loader.load = AsyncMock(return_value=test_organization)

    result = await client.execute_async(
        """
    {
        event(id: "test_event_id") {
            organization {
                name
            }
        }
    }
    """,
        context_value=context,
    )

    context.event_service.get_event.assert_called_with(event_id=test_event.id)
    context.org_loader.load.assert_called_with(test_event.org_id)

    assert result == {"data": {"event": {"organization": {"name": test_organization.name}}}}


@pytest.mark.asyncio
async def test_event_site_can_be_queried() -> None:
    client, context = harness()

    context.auth.get_calling_org_id = AsyncMock(return_value=test_organization.id)
    context.event_service.get_event = AsyncMock(return_value=test_event)
    context.site_loader.load = AsyncMock(return_value=test_site)

    result = await client.execute_async(
        """
    {
        event(id: "test_event_id") {
            site {
                name
            }
        }
    }
    """,
        context_value=context,
    )

    context.event_service.get_event.assert_called_with(event_id=test_event.id)
    context.site_loader.load.assert_called_with(test_event.site_id)

    assert result == {"data": {"event": {"site": {"name": test_site.name}}}}


@pytest.mark.asyncio
async def test_event_room_can_be_queried() -> None:
    client, context = harness()

    context.auth.get_calling_org_id = AsyncMock(return_value=test_event.org_id)
    context.event_service.get_event = AsyncMock(return_value=test_event)
    context.room_loader.load = AsyncMock(return_value=test_room)

    result = await client.execute_async(
        """
    {
        event(id: "test_event_id") {
            room {
                name
            }
        }
    }
    """,
        context_value=context,
    )

    context.event_service.get_event.assert_called_with(event_id=test_event.id)
    context.room_loader.load.assert_called_with(test_event.room_id)

    assert result == {"data": {"event": {"room": {"name": test_room.name}}}}
