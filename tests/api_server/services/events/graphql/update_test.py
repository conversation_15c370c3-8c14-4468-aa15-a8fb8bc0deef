# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

import datetime
from unittest.mock import AsyncMock

import pytest

from api_server.services.events.event_store import EventModel
from tests.harness import harness


def create_test_event(event_id):
    result = EventModel()
    result.id = event_id
    result.event_type_id = "patient_wheels_in"
    result.start_time = datetime.datetime.fromisoformat("2021-03-01T00:00:00-07:00")
    result.process_timestamp = datetime.datetime.fromisoformat("2021-03-01T00:01:00-07:00")
    result.org_id = "test_org_id"
    result.site_id = "test_site_id"
    result.room_id = "test_room_id"
    result.source = "unit test"
    result.source_type = "human_gt"
    return result


@pytest.mark.asyncio
async def test_update_can_update_just_one_field_and_validates() -> None:
    client, context = harness()

    original_event = create_test_event("020a9e02-dceb-4a53-8f84-5b5ac0e8bd94")

    context.auth.get_calling_org_id = AsyncMock(return_value="test_org")
    context.event_service.patch_event = AsyncMock(return_value=original_event)

    result = await client.execute_async(
        """
        mutation {
            eventUpdate(input:
                {
                    id: "020a9e02-dceb-4a53-8f84-5b5ac0e8bd94"
                    name: "patient_wheels_out",
                    source: "test",
                    sourceType: "test",
                }) {
                success
            }
        }
        """,
        context_value=context,
    )

    assert result == {"data": {"eventUpdate": {"success": True}}}

    context.event_service.patch_event.assert_called_with(
        event_id="020a9e02-dceb-4a53-8f84-5b5ac0e8bd94",
        event_type_id="patient_wheels_out",
        start_time=None,
        labels=None,
        notes=None,
        source="test",
        source_type="test",
        camera_id=None,
        publish_changelog=True,
    )
