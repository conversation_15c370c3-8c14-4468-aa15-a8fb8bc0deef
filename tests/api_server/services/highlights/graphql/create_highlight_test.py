# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

from unittest.mock import AsyncMock

import pytest

from api_server.services.highlights.highlight_store import Highlight
from tests.harness import harness

camera_id = "test_camera_id"
room_id = "test_room_id"
site_id = "test_site_id"
organization_id = "test_organization_id"
user_id_1 = "test_user_id_1"
user_id_2 = "test_user_id_2"


async def assert_successful_mutation(statement):
    client, context = harness()

    context.highlight_service.create_highlight = AsyncMock()

    result = await client.execute_async(statement, context_value=context)

    assert result == {"data": {"highlightCreate": {"success": True}}}

    context.highlight_service.create_highlight.assert_called_once()


async def assert_failed_mutation(statement, error_message):
    client, context = harness()

    context.highlight_service.create_highlight = AsyncMock()

    result = await client.execute_async(statement, context_value=context)

    errors = result.get("errors")
    assert errors is not None
    assert errors[0].get("message") == error_message


@pytest.mark.asyncio
async def test_create_highlight_with_all_fields() -> None:
    await assert_successful_mutation(
        f"""
        mutation {{
            highlightCreate(input: {{
                            id: "020a9e02-dceb-4a53-8f84-5b5ac0e8bd94",
                            description: "test-description",
                            organizationId: "{organization_id}",
                            siteId: "{site_id}",
                            roomId: "{room_id}",
                            cameraId: "{camera_id}",
                            assignedUserIds: [ "{user_id_1}", "{user_id_2}" ],
                            startTime: "2021-03-01T00:00:00-07:00",
                            endTime: "2021-03-01T01:00:00-07:00",
                            }}) {{
                success
            }}
        }}
        """
    )


@pytest.mark.asyncio
async def test_create_highlight_with_all_fields_and_return_highlight() -> None:
    client, context = harness()

    created_highlight = Highlight(
        id="020a9e02-dceb-4a53-8f84-5b5ac0e8bd94",
        description="test-description",
    )

    context.highlight_service.create_highlight = AsyncMock(return_value=created_highlight)

    result = await client.execute_async(
        f"""
        mutation {{
            highlightCreate(input: {{
                            id: "020a9e02-dceb-4a53-8f84-5b5ac0e8bd94",
                            description: "test-description",
                            organizationId: "{organization_id}",
                            siteId: "{site_id}",
                            roomId: "{room_id}",
                            cameraId: "{camera_id}",
                            assignedUserIds: [ "{user_id_1}", "{user_id_2}" ],
                            startTime: "2021-03-01T00:00:00-07:00",
                            endTime: "2021-03-01T01:00:00-07:00",
                            }}) {{
                success
                createdHighlight {{
                    id
                    description
                }}
            }}
        }}
        """,
        context_value=context,
    )

    assert result == {
        "data": {
            "highlightCreate": {
                "success": True,
                "createdHighlight": {
                    "id": "020a9e02-dceb-4a53-8f84-5b5ac0e8bd94",
                    "description": "test-description",
                },
            }
        }
    }

    context.highlight_service.create_highlight.assert_called_once()


@pytest.mark.asyncio
async def test_create_highlight_with_bad_uuid_fails() -> None:
    await assert_failed_mutation(
        f"""
        mutation {{
            highlightCreate(input: {{
                            id: "bad",
                            description: "test-description",
                            organizationId: "{organization_id}",
                            siteId: "{site_id}",
                            roomId: "{room_id}",
                            cameraId: "{camera_id}",
                            assignedUserIds: [ "{user_id_1}", "{user_id_2}" ],
                            startTime: "2021-03-01T00:00:00-07:00",
                            endTime: "2021-03-01T01:00:00-07:00",
                            }}) {{
                success
            }}
        }}
        """,
        "'id' must be a valid uuid",
    )
