from datetime import datetime
from unittest.mock import AsyncMock

import pytest

from databases.sql.types import NonNullableDateRange
from api_server.services.measurement_periods.measurement_period_store import (
    DayOfWeek,
    MeasurementPeriodModel,
)
from tests.harness import harness
from utils.helpers import convert_str_to_uuid


@pytest.mark.asyncio
async def test_upsert_measurement_period_with_all_fields_and_return_success() -> None:
    client, context = harness()

    measurement_period_start = datetime(2023, 6, 22).date()
    measurement_period_end = datetime(2023, 6, 29).date()

    test_mp: MeasurementPeriodModel = MeasurementPeriodModel()
    test_mp.id = convert_str_to_uuid("test_mp_id")
    test_mp.name = "test_mp_name"
    test_mp.site_id = "test_site_id"
    test_mp.room_ids = ["test_room_id"]
    test_mp.days_of_week = [DayOfWeek.SATURDAY, DayOfWeek.SUNDAY]
    test_mp.measurement_period = NonNullableDateRange(
        lower=measurement_period_start, upper=measurement_period_end
    )
    test_mp.annotation_task_type_id = convert_str_to_uuid("test_annotation_task_type_id")

    context.measurement_period_service.upsert_measurement_period = AsyncMock(return_value=test_mp)

    result = await client.execute_async(
        f"""
        mutation {{
            measurementPeriodUpsert(measurementPeriod: {{
                            id: "{test_mp.id}",
                            name: "{test_mp.name}",
                            siteId: "{test_mp.site_id}",
                            roomIds: "{test_mp.room_ids}",
                            daysOfWeek: [SATURDAY, SUNDAY],
                            measurementPeriodStart: "{measurement_period_start}",
                            measurementPeriodEnd: "{measurement_period_end}",
                            annotationTaskTypeId: "{test_mp.annotation_task_type_id}"
                            }}) {{
                success
            }}
        }}
        """,
        context_value=context,
    )

    assert result == {
        "data": {
            "measurementPeriodUpsert": {
                "success": True,
            }
        }
    }

    context.measurement_period_service.upsert_measurement_period.assert_called_once()
