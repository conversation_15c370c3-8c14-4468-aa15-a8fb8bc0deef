from unittest.mock import AsyncMock

import pytest

from api_server.services.organization.organization_db import Organization
from api_server.services.site.site_store import Site
from tests.harness import harness

test_organization: Organization = Organization()
test_organization.id = "test_organization_id"
test_organization.name = "Test Organization"

test_site: Site = Site()
test_site.name = "test site"
test_site.id = "test_site_id"
test_site.org_id = test_organization.id


@pytest.mark.asyncio
async def test_organization_can_be_queried_by_id() -> None:
    client, context = harness()

    context.organization_service.get_organizations = AsyncMock(return_value=[test_organization])

    result = await client.execute_async(
        """
    {
        organization(id: "test_organization_id") {
            name
        }
    }
    """,
        context_value=context,
    )

    context.organization_service.get_organizations.assert_called_with(
        org_ids=[test_organization.id]
    )

    assert result == {"data": {"organization": {"name": test_organization.name}}}


@pytest.mark.asyncio
async def test_organization_sites_can_be_queried() -> None:
    client, context = harness()

    context.organization_service.get_organizations = AsyncMock(return_value=[test_organization])
    context.org_sites_loader.load = AsyncMock(return_value=[test_site])

    result = await client.execute_async(
        """
    {
        organization(id: "test_organization_id") {
            sites {
                edges {
                    node {
                        id
                        name
                    }
                }
            }
        }
    }
    """,
        context_value=context,
    )

    context.organization_service.get_organizations.assert_called_with(
        org_ids=[test_organization.id]
    )

    assert result == {
        "data": {
            "organization": {
                "sites": {"edges": [{"node": {"id": test_site.id, "name": test_site.name}}]}
            }
        }
    }
