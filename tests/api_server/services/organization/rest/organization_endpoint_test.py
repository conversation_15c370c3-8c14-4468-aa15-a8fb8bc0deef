from unittest.mock import MagicMock, AsyncMock

import pytest

import api_server.services.organization.rest.organization_functions
from api_server.services.organization.organization_db import Organization
from api_server.services.organization.organization_service import OrganizationService
from api_server.services.site.site_service import SiteService
from api_server.services.site.site_store import Site

test_organization = Organization()
test_organization.id = "test_organization_id_0"
test_organization.name = "Test Organization"

test_site = Site()
test_site.id = "test_site_id_0"
test_site.name = "Test Site"
test_site.org_id = test_organization.id
test_site.timezone = "America/Los_Angeles"

mock_organization_service = AsyncMock(spec=OrganizationService)

mock_site_service = MagicMock(spec=SiteService)


@pytest.mark.asyncio
async def test_get_organization_info() -> None:
    mock_organization_service.get_organization = AsyncMock(return_value=test_organization)
    mock_site_service.query_sites = AsyncMock(return_value=[test_site])

    result = await api_server.services.organization.rest.organization_functions.get_org_info(
        test_organization.id, mock_organization_service, mock_site_service
    )
    assert result["organization_id"] == test_organization.id
    assert result["organization_name"] == test_organization.name
    assert result["sites"][0] == test_site.id

    mock_organization_service.get_organization.assert_called_with(test_organization.id)
