from datetime import datetime
from unittest.mock import AsyncMock

import pytest

from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_input_schema import GQLRoomEventSearchInput
from api_server.services.camera.camera_store import CameraModel
from api_server.services.events.event_store import EventModel
from api_server.services.organization.organization_db import Organization
from api_server.services.room.room_store import RoomModel
from api_server.services.site.site_store import Site
from tests.harness import harness

test_organization: Organization = Organization()
test_organization.name = "Test Organization"
test_organization.id = "test_organization_id"

test_site: Site = Site()
test_site.name = "Test Site"
test_site.id = "test_site_id"
test_site.org_id = test_organization.id

test_room: RoomModel = RoomModel()
test_room.id = "test_room_id"
test_room.name = "Test Room"
test_room.org_id = test_organization.id
test_room.site_id = test_site.id

test_room_2: RoomModel = RoomModel()
test_room_2.id = "test_room_2_id"
test_room_2.name = "Test Room 2"
test_room_2.org_id = test_organization.id
test_room_2.site_id = test_site.id

test_camera_0: CameraModel = CameraModel()
test_camera_0.name = "Test Camera 0"
test_camera_0.org_id = "test_org_0"
test_camera_0.site_id = test_site.id
test_camera_0.room_id = test_room.id
test_camera_0.family = "geovision"
test_camera_1: CameraModel = CameraModel()
test_camera_1.name = "Test Camera 1"
test_camera_1.org_id = "test_org_1"
test_camera_1.site_id = test_site.id
test_camera_1.room_id = test_room.id
test_camera_1.family = "geovision"
test_cameras = [test_camera_0, test_camera_1]

test_event: EventModel = EventModel()
test_event.id = "test_event_id_0"
test_event.room_id = test_room.id
test_event.site_id = test_site.id

test_event_2: EventModel = EventModel()
test_event_2.id = "test_event_id_1"
test_event_2.room_id = test_room_2.id
test_event_2.site_id = test_site.id


@pytest.mark.asyncio
async def test_room_can_be_queried_by_id() -> None:
    client, context = harness()

    context.room_service.get_rooms = AsyncMock(return_value=[test_room])
    context.auth.get_calling_org_id = AsyncMock(return_value=test_room.org_id)

    result = await client.execute_async(
        """
    {
        room(id: "test_room_id") {
            name
        }
    }
    """,
        context_value=context,
    )

    context.room_service.get_rooms.assert_called_with(keys=[test_room.id])

    assert result == {
        "data": {
            "room": {
                "name": test_room.name,
            }
        }
    }


@pytest.mark.asyncio
async def test_room_org_can_be_queried() -> None:
    client, context = harness()

    context.auth.get_calling_org_id = AsyncMock(return_value=test_room.org_id)
    context.room_service.get_rooms = AsyncMock(return_value=[test_room])
    context.organization_service.get_organizations = AsyncMock(return_value=[test_organization])

    result = await client.execute_async(
        """
    {
        room(id: "test_room_id") {
            organization {
                name
            }
        }
    }
    """,
        context_value=context,
    )

    context.room_service.get_rooms.assert_called_with(keys=[test_room.id])
    context.organization_service.get_organizations.assert_called_with(
        org_ids=[test_organization.id]
    )

    assert result == {"data": {"room": {"organization": {"name": test_organization.name}}}}


@pytest.mark.asyncio
async def test_room_site_can_be_queried() -> None:
    client, context = harness()

    context.auth.get_calling_org_id = AsyncMock(return_value=test_site.org_id)
    context.room_service.get_rooms = AsyncMock(return_value=[test_room])
    context.site_service.query_sites = AsyncMock(return_value=[test_site])

    result = await client.execute_async(
        """
    {
        room(id: "test_room_id") {
            site {
                name
            }
        }
    }
    """,
        context_value=context,
    )

    context.room_service.get_rooms.assert_called_with(keys=[test_room.id])

    called_args, _ = context.site_service.query_sites.call_args
    site_query = called_args[0]

    assert site_query.site_ids == [test_site.id]
    assert result == {"data": {"room": {"site": {"name": test_site.name}}}}


@pytest.mark.asyncio
async def test_room_cameras_can_be_queried() -> None:
    client, context = harness()

    context.auth.get_calling_org_id = AsyncMock(return_value=test_room.org_id)
    context.room_service.get_rooms = AsyncMock(return_value=[test_room])
    context.room_cameras_loader.load = AsyncMock(return_value=test_cameras)

    result = await client.execute_async(
        """
    {
        room(id: "test_room_id") {
            cameras {
                edges {
                    node {
                        name
                    }
                }
            }
        }
    }
    """,
        context_value=context,
    )

    context.room_service.get_rooms.assert_called_with(keys=[test_room.id])

    assert result == {
        "data": {
            "room": {
                "cameras": {
                    "edges": [
                        {"node": {"name": test_camera_0.name}},
                        {"node": {"name": test_camera_1.name}},
                    ]
                }
            }
        }
    }


@pytest.mark.asyncio
async def test_room_events_can_be_queried() -> None:
    client, context = harness()

    context.auth.get_calling_org_id = AsyncMock(return_value=test_room.org_id)
    context.room_service.get_rooms = AsyncMock(return_value=[test_room])
    context.event_service.query_events = AsyncMock(return_value=[test_event])

    result = await client.execute_async(
        """
    {
        room(id: "test_room_id") {
            events(minTime: "2021-03-01T00:00:00-07:00", maxTime: "2021-04-01T00:00:00-07:00") {
                id
            }
        }
    }
    """,
        context_value=context,
    )

    assert result == {"data": {"room": {"events": [{"id": test_event.id}]}}}

    context.room_service.get_rooms.assert_called_with(keys=[test_room.id])
    context.event_service.query_events.assert_called_once()


@pytest.mark.asyncio
async def test_room_events_uses_loader_properly() -> None:
    apella_schema = ApellaSchema()
    query = (
        apella_schema.Query.rooms.args(site_id=test_site.id)
        .select(
            apella_schema.RoomConnection.edges.select(
                apella_schema.RoomEdge.node.select(
                    apella_schema.Room.room_events.args(
                        query=GQLRoomEventSearchInput(
                            min_time=datetime(2021, 3, 1, 0, 0, 0),
                            max_time=datetime(2021, 4, 1, 0, 0, 0),
                        )
                    ).select(
                        apella_schema.EventConnection.edges.select(
                            apella_schema.EventEdge.node.select(
                                apella_schema.Event.id,
                            )
                        )
                    ),
                )
            )
        )
        .to_gql()
    )
    client, context = harness()

    context.auth.get_calling_org_id = AsyncMock(return_value=test_room.org_id)
    context.room_service.query_rooms = AsyncMock(return_value=[test_room, test_room_2])

    context.event_service.query_events = AsyncMock(return_value=[test_event, test_event_2])

    result = await client.execute_async(
        f"""{{ {query} }}""",
        context_value=context,
    )

    context.event_service.query_events.assert_called_once()
    assert result == {
        "data": {
            "rooms": {
                "edges": [
                    {
                        "node": {
                            "roomEvents": {
                                "edges": [
                                    {
                                        "node": {
                                            "id": test_event.id,
                                        }
                                    }
                                ]
                            }
                        }
                    },
                    {
                        "node": {
                            "roomEvents": {
                                "edges": [
                                    {
                                        "node": {
                                            "id": test_event_2.id,
                                        }
                                    }
                                ]
                            }
                        }
                    },
                ]
            }
        }
    }


@pytest.mark.asyncio
async def test_room_events_uses_loader_properly_aliased() -> None:
    apella_schema = ApellaSchema()
    query = (
        apella_schema.Query.rooms.args(site_id=test_site.id)
        .select(
            apella_schema.RoomConnection.edges.select(
                apella_schema.RoomEdge.node.select(
                    apella_schema.Room.room_events.args(
                        query=GQLRoomEventSearchInput(
                            min_time=datetime(2021, 3, 1, 0, 0, 0),
                            max_time=datetime(2021, 4, 1, 0, 0, 0),
                        )
                    ).select(
                        apella_schema.EventConnection.edges.select(
                            apella_schema.EventEdge.node.select(
                                apella_schema.Event.id,
                            )
                        )
                    ),
                    apella_schema.Room.room_events.args(
                        query=GQLRoomEventSearchInput(
                            min_time=datetime(2021, 3, 1, 0, 0, 0),
                            max_time=datetime(2021, 5, 1, 0, 0, 0),
                        )
                    )
                    .select(
                        apella_schema.EventConnection.edges.select(
                            apella_schema.EventEdge.node.select(
                                apella_schema.Event.id,
                            )
                        )
                    )
                    .alias("other_events"),
                )
            )
        )
        .to_gql()
    )
    client, context = harness()

    context.auth.get_calling_org_id = AsyncMock(return_value=test_room.org_id)
    context.room_service.query_rooms = AsyncMock(return_value=[test_room, test_room_2])

    context.event_service.query_events = AsyncMock(return_value=[test_event, test_event_2])

    result = await client.execute_async(
        f"""{{ {query} }}""",
        context_value=context,
    )

    assert context.event_service.query_events.call_count == 2
    assert result == {
        "data": {
            "rooms": {
                "edges": [
                    {
                        "node": {
                            "roomEvents": {
                                "edges": [
                                    {
                                        "node": {
                                            "id": test_event.id,
                                        }
                                    }
                                ]
                            },
                            "other_events": {
                                "edges": [
                                    {
                                        "node": {
                                            "id": test_event.id,
                                        }
                                    }
                                ]
                            },
                        }
                    },
                    {
                        "node": {
                            "roomEvents": {
                                "edges": [
                                    {
                                        "node": {
                                            "id": test_event_2.id,
                                        }
                                    }
                                ]
                            },
                            "other_events": {
                                "edges": [
                                    {
                                        "node": {
                                            "id": test_event_2.id,
                                        }
                                    }
                                ]
                            },
                        }
                    },
                ]
            }
        }
    }


@pytest.mark.asyncio
async def test_rooms_can_be_queried_without_permissions() -> None:
    client, context = harness()

    context.room_service.query_rooms = AsyncMock(return_value=[test_room])

    result = await client.execute_async(
        """
    {
        rooms {
            edges {
                node {
                    id
                    name
                }
            }
        }
    }
    """,
        context_value=context,
    )

    context.room_service.query_rooms.assert_called_once()

    assert result == {
        "data": {
            "rooms": {
                "edges": [
                    {
                        "node": {
                            "name": test_room.name,
                            "id": test_room.id,
                        }
                    }
                ]
            }
        }
    }
