from datetime import datetime, timedelta, timezone
from typing import List
from unittest.mock import MagicMock, AsyncMock

from api_server.services.apella_case.apella_case_store import ApellaCase
from api_server.services.events.event_store import EventModel
from api_server.services.phases.phase_store import PhaseModel
from api_server.services.room.room_service import RoomService, RoomStatusName
from api_server.services.room.room_store import RoomModel, RoomStore
from api_server.services.turnover.turnover_service import TurnoverService
from auth.auth import Auth
from auth.permissions import READ_ANY_ROOM

test_room = RoomModel()
test_room.id = "test_room_id_0"
test_room.name = "Test Room"
test_room.site_id = "test_site_id_0"
test_room.org_id = "test_org_id_0"

mock_room_store = MagicMock(spec=RoomStore)
mock_auth = MagicMock(spec=Auth)
mock_turnover_service = AsyncMock(spec=TurnoverService)

room_service = RoomService(
    auth=mock_auth, room_store=mock_room_store, turnover_service=mock_turnover_service
)


async def test_get_room_info() -> None:
    mock_auth.requires = MagicMock()
    mock_room_store.get_room = AsyncMock(return_value=test_room)

    result = await room_service.get_room(room_id=test_room.id)

    mock_auth.requires.assert_called_with(READ_ANY_ROOM, enforce_universal_user=False)
    mock_room_store.get_room.assert_called_with(room_id=test_room.id)

    assert result == test_room


async def test_get_room_ids_in_site() -> None:
    mock_auth.requires = MagicMock()
    mock_room_store.get_rooms_in_site = AsyncMock(return_value=[test_room])

    result = await room_service.get_room_ids_in_site(site_id=test_room.site_id)

    mock_auth.requires.assert_called_with(READ_ANY_ROOM, enforce_universal_user=False)
    mock_room_store.get_rooms_in_site.assert_called_with(site_id=test_room.site_id)

    assert result == [test_room.id]


async def test_get_rooms_in_site() -> None:
    mock_auth.requires = MagicMock()
    mock_room_store.get_rooms_in_site = AsyncMock(return_value=[test_room])

    result = await room_service.get_rooms_in_site(site_id=test_room.site_id)

    mock_auth.requires.assert_called_with(READ_ANY_ROOM, enforce_universal_user=False)
    mock_room_store.get_rooms_in_site.assert_called_with(site_id=test_room.site_id)

    assert result == [test_room]


def mock_apella_case(start_time: datetime, end_time: datetime) -> ApellaCase:
    return ApellaCase(
        _case=None,
        _actual=PhaseModel(
            start_event=EventModel(start_time=start_time),
            end_event=EventModel(start_time=end_time),
            end_event_id="1",
        ),
        _case_forecast=None,
        _apella_case_model=None,
    )


async def test_get_room_status_in_case() -> None:
    now_utc = datetime.now(timezone.utc)
    case_start_time = now_utc - timedelta(hours=1)
    in_progress_case = mock_apella_case(case_start_time, now_utc + timedelta(hours=1))
    cases: List[ApellaCase] = [in_progress_case]

    result = await room_service.get_room_status(cases, now_utc)

    assert result.name == RoomStatusName.IN_CASE
    assert result.since == case_start_time
    assert result.in_progress_apella_case == in_progress_case
    assert result.next_case is None
    assert result.in_progress_turnover is None


async def test_get_room_status_next_case() -> None:
    now_utc = datetime.now(timezone.utc)
    case_start_time = now_utc - timedelta(hours=1)
    cases: List[ApellaCase] = [
        mock_apella_case(case_start_time, now_utc + timedelta(hours=1)),
        mock_apella_case(now_utc + timedelta(hours=2), now_utc + timedelta(hours=3)),
    ]

    result = await room_service.get_room_status(cases, now_utc)

    assert result.name == RoomStatusName.IN_CASE
    assert result.since == case_start_time
    assert result.in_progress_apella_case == cases[0]
    assert result.next_case == cases[1]
    assert result.in_progress_turnover is None


async def test_get_room_status_empty_closed() -> None:
    now_utc = datetime.now(timezone.utc)
    start_of_day = now_utc.replace(hour=0, minute=0, microsecond=0)
    cases: List[ApellaCase] = []

    result = await room_service.get_room_status(cases, now_utc)

    assert result.name == RoomStatusName.CLOSED
    assert result.since == start_of_day
    assert result.in_progress_apella_case is None
    assert result.next_case is None
    assert result.in_progress_turnover is None


async def test_get_room_status_closed() -> None:
    now_utc = datetime.now(timezone.utc)
    case_end_time = now_utc - timedelta(hours=1)
    cases: List[ApellaCase] = [mock_apella_case(now_utc - timedelta(hours=2), case_end_time)]

    result = await room_service.get_room_status(cases, now_utc)

    assert result.name == RoomStatusName.CLOSED
    assert result.since == case_end_time
    assert result.in_progress_apella_case is None
    assert result.next_case is None
    assert result.in_progress_turnover is None


async def test_get_room_status_turnover() -> None:
    now_utc = datetime.now(timezone.utc)
    case_end_time = now_utc - timedelta(hours=1)
    cases: List[ApellaCase] = [
        mock_apella_case(now_utc - timedelta(hours=2), case_end_time),
        mock_apella_case(now_utc + timedelta(minutes=10), now_utc + timedelta(hours=1)),
    ]

    mock_turnover_service.generate_turnover = AsyncMock(return_value=test_room)

    result = await room_service.get_room_status(cases, now_utc)

    assert result.name == RoomStatusName.TURNOVER
    assert result.since == case_end_time
    assert result.in_progress_apella_case is None
    mock_turnover_service.generate_turnover.assert_called_once_with(cases[0], cases[1])
    assert result.next_case == cases[1]
    assert result.in_progress_turnover is not None


async def test_get_room_status_idle_after_two_hours() -> None:
    now_utc = datetime.now(timezone.utc)
    case_end_time = now_utc - timedelta(hours=1)
    cases: List[ApellaCase] = [
        mock_apella_case(now_utc - timedelta(hours=2), case_end_time),
        mock_apella_case(now_utc + timedelta(hours=2), now_utc + timedelta(hours=3)),
    ]

    result = await room_service.get_room_status(cases, now_utc)

    assert result.name == RoomStatusName.IDLE
    assert result.since == case_end_time
    assert result.in_progress_apella_case is None
    assert result.next_case == cases[1]
    assert result.in_progress_turnover is not None


async def test_get_room_status_idle_before_first_case() -> None:
    now_utc = datetime.now(timezone.utc)
    start_of_day = now_utc.replace(hour=0, minute=0, microsecond=0)
    cases: List[ApellaCase] = [
        mock_apella_case(now_utc + timedelta(hours=2), now_utc + timedelta(hours=3)),
    ]

    result = await room_service.get_room_status(cases, now_utc)

    assert result.name == RoomStatusName.IDLE
    assert result.since == start_of_day
    assert result.in_progress_apella_case is None
    assert result.next_case == cases[0]
    assert result.in_progress_turnover is None


async def test_get_room_status_turnover_with_max() -> None:
    now_utc = datetime.now(timezone.utc)
    case_end_time = now_utc - timedelta(hours=1)
    cases: List[ApellaCase] = [
        mock_apella_case(now_utc - timedelta(hours=2), case_end_time),
        mock_apella_case(now_utc + timedelta(hours=2), now_utc + timedelta(hours=3)),
    ]
    result = await room_service.get_room_status(cases, now_utc, 4 * 60)

    assert result.name == RoomStatusName.TURNOVER
    assert result.since == case_end_time
    assert result.in_progress_apella_case is None
    assert result.next_case == cases[1]
    assert result.in_progress_turnover is not None


async def test_get_room_status_idle_with_max() -> None:
    now_utc = datetime.now(timezone.utc)
    case_end_time = now_utc - timedelta(hours=1)
    cases: List[ApellaCase] = [
        mock_apella_case(now_utc - timedelta(hours=2), case_end_time),
        mock_apella_case(now_utc + timedelta(hours=2), now_utc + timedelta(hours=3)),
    ]
    result = await room_service.get_room_status(cases, now_utc, 1 * 60)

    assert result.name == RoomStatusName.IDLE
    assert result.since == case_end_time
    assert result.in_progress_apella_case is None
    assert result.next_case == cases[1]
    assert result.in_progress_turnover is not None
