from datetime import datetime, timedelta, timezone
from unittest.mock import MagicMock, AsyncMock


from api_server.services.schedule_assistant_email_builder.models.schedule_assistant_email_builder_models import (
    SlotInput,
)
from api_server.services.schedule_assistant_email_builder.schedule_assistant_email_builder_service import (
    ScheduleAssistantEmailBuilderService,
)

from typing import Final
import pytest

from api_server.services.room.room_service import RoomService
from api_server.services.room.room_store import RoomModel, RoomTag
from api_server.services.site.site_service import SiteService
from api_server.services.site.site_store import Site
from auth.auth import Auth

org_id = "test-org-id"
test_site_0: Final = Site()
test_site_0.id = "test_site_id_0"
test_site_0.org_id = org_id
test_site_0.name = "Test Site 0"
test_site_0.timezone = "America/Los_Angeles"

test_site_1: Final = Site()
test_site_1.id = "test_site_id_1"
test_site_1.org_id = org_id
test_site_1.name = "Test Site 1"
test_site_1.timezone = "America/New_York"

test_room_0: Final = RoomModel()
test_room_0.id = "test_room_id_0"
test_room_0.name = "Test Room"
test_room_0.site_id = "test_site_id_0"
test_room_0.org_id = org_id

test_room_1: Final = RoomModel()
test_room_1.id = "test_room_id_1"
test_room_1.name = "Test Room 1"
test_room_1.site_id = "test_site_id_1"
test_room_1.org_id = org_id


test_rooms = (test_room_0, test_room_1)
test_sites = (test_site_0, test_site_1)


@pytest.fixture
async def schedule_assistant_email_builder_service() -> ScheduleAssistantEmailBuilderService:
    mock_auth = MagicMock(spec=Auth)
    mock_site_service = AsyncMock(spec=SiteService)
    mock_room_service = AsyncMock(spec=RoomService)

    schedule_assistant_email_builder_service = ScheduleAssistantEmailBuilderService(
        auth=mock_auth,
        site_service=mock_site_service,
        room_service=mock_room_service,
    )
    sites_map = {}
    for site in test_sites:
        sites_map[site.id] = site
    mock_site_service.get_site = AsyncMock(side_effect=lambda site_id: sites_map[site_id])

    rooms_map = {}
    for room in test_rooms:
        rooms_map[room.id] = room
    mock_room_service.get_room = AsyncMock(side_effect=lambda room_id: rooms_map[room_id])

    mock_room_service.query_room_tags = AsyncMock(
        return_value=[RoomTag(name="test-roomtag", color="#0000")]
    )

    return schedule_assistant_email_builder_service


@pytest.mark.asyncio
async def test_get_email_message_data(
    schedule_assistant_email_builder_service: ScheduleAssistantEmailBuilderService,
) -> None:
    result = await schedule_assistant_email_builder_service.get_email_message_data(
        start_date=datetime(2024, 10, 10),
        end_date=datetime(2024, 10, 12),
        slots=[
            SlotInput(
                room_id="test_room_id_0",
                start_time=datetime(2024, 10, 11, 8, tzinfo=timezone.utc),
                end_time=datetime(2024, 10, 11, 9, tzinfo=timezone.utc),
                max_available_duration=timedelta(minutes=90),
            ),
            SlotInput(
                room_id="test_room_id_1",
                start_time=datetime(2024, 10, 11, 11, tzinfo=timezone.utc),
                end_time=datetime(2024, 10, 11, 19, tzinfo=timezone.utc),
                max_available_duration=timedelta(minutes=40),
            ),
        ],
    )
    expected_available_slots = [
        {
            "site_name": "Test Site 0",
            "slots": [
                {
                    "date": "10/11",
                    "time": "01:00 - 02:00",
                    "duration": "60",
                    "room": "Test Room",
                    "roomtags": [{"name": "test-roomtag", "color": "#0000"}],
                },
            ],
        },
        {
            "site_name": "Test Site 1",
            "slots": [
                {
                    "date": "10/11",
                    "time": "07:00 - 15:00",
                    "duration": "480",
                    "room": "Test Room 1",
                    "roomtags": [{"name": "test-roomtag", "color": "#0000"}],
                },
            ],
        },
    ]
    assert result["start_date"] == "10/10/24"
    assert result["end_date"] == "10/12/24"
    assert result["available_slots"] == expected_available_slots
