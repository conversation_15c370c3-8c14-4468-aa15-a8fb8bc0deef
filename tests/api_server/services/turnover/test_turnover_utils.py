import dataclasses
from datetime import datetime, timedelta

import pytest

from api_server.services.apella_case.apella_case_store import ApellaCase
from api_server.services.events.event_models import EventModel
from api_server.services.turnover.turnover_utils import (
    extract_case_ids_from_turnover_id,
    generate_turnover_id,
    get_turnover_status,
)
from api_server.services.turnover.turnover_models import TurnoverStatusName, Turnover, TurnoverType


class TestGenerateAndExtractCaseIdsFromTurnoverId:
    def test_valid_turnover_id_with_case_and_phase(self) -> None:
        turnover_id = "turnover-phase:3964edf8-1cf0-416d-9e17-9ca820d086c4-case:ed4ab483-3d8d-427c-ad79-114c6eb1c52e"
        preceding, following = extract_case_ids_from_turnover_id(turnover_id)
        assert preceding == "phase:3964edf8-1cf0-416d-9e17-9ca820d086c4"
        assert following == "case:ed4ab483-3d8d-427c-ad79-114c6eb1c52e"

    def test_valid_turnover_id_with_mixed_prefixes(self) -> None:
        turnover_id = "turnover-case:3964edf8-1cf0-416d-9e17-9ca820d086c4-phase:ed4ab483-3d8d-427c-ad79-114c6eb1c52e"
        preceding, following = extract_case_ids_from_turnover_id(turnover_id)
        assert preceding == "case:3964edf8-1cf0-416d-9e17-9ca820d086c4"
        assert following == "phase:ed4ab483-3d8d-427c-ad79-114c6eb1c52e"

    def test_invalid_turnover_id_missing_turnover_prefix(self) -> None:
        turnover_id = (
            "phase:3964edf8-1cf0-416d-9e17-9ca820d086c4-case:ed4ab483-3d8d-427c-ad79-114c6eb1c52e"
        )
        with pytest.raises(ValueError):
            extract_case_ids_from_turnover_id(turnover_id)

    def test_invalid_turnover_id_missing_second_case_id(self) -> None:
        turnover_id = "turnover-case:3964edf8-1cf0-416d-9e17-9ca820d086c4"
        with pytest.raises(ValueError):
            extract_case_ids_from_turnover_id(turnover_id)

    def test_invalid_turnover_id_with_incorrect_uuid_format(self) -> None:
        turnover_id = "turnover-case:invaliduuid-case:anotherinvaliduuid"
        with pytest.raises(ValueError):
            extract_case_ids_from_turnover_id(turnover_id)

    def test_generate_turnover_id_with_valid_inputs(self) -> None:
        preceding_case_id = "case:123e4567-e89b-12d3-a456-************"
        following_case_id = "phase:223e4567-e89b-12d3-a456-************"
        turnover_id = generate_turnover_id(preceding_case_id, following_case_id)
        assert (
            turnover_id
            == "turnover-case:123e4567-e89b-12d3-a456-************-phase:223e4567-e89b-12d3-a456-************"
        )


class TestTurnoverStatus:
    anchor_date = datetime(2024, 1, 1, 9, 0, 0)
    turnover = Turnover(
        id="1",
        start_time=anchor_date,
        end_time=anchor_date + timedelta(hours=1),
        preceding_case=ApellaCase(
            _actual=None, _case=None, _case_forecast=None, _apella_case_model=None
        ),
        following_case=ApellaCase(
            _actual=None, _case=None, _case_forecast=None, _apella_case_model=None
        ),
        meets_inclusion_criteria=True,
        room_id="1",
        type=TurnoverType.COMPLETE,
    )

    def test_no_events(self) -> None:
        status = get_turnover_status(self.turnover, [])
        assert status is not None
        assert status.name == TurnoverStatusName.CLEANING
        assert status.since == self.anchor_date

    def test_cleaned(self) -> None:
        event_time = self.anchor_date + timedelta(minutes=15)
        status = get_turnover_status(
            self.turnover,
            [EventModel(id="1", event_type_id="mop_out_turnover", start_time=event_time)],
        )
        assert status is not None
        assert status.name == TurnoverStatusName.CLEANED
        assert status.since == event_time

    def test_junk_events(self) -> None:
        status = get_turnover_status(
            self.turnover, [EventModel(id="1", event_type_id="not_real_event")]
        )
        assert status is not None
        assert status.name == TurnoverStatusName.CLEANING
        assert status.since == self.anchor_date

    def test_backwards_events(self) -> None:
        event_1_time = self.anchor_date + timedelta(minutes=15)
        event_2_time = self.anchor_date + timedelta(minutes=30)
        status = get_turnover_status(
            self.turnover,
            [
                EventModel(id="2", event_type_id="mop_out_turnover", start_time=event_2_time),
                EventModel(id="1", event_type_id="back_table_open", start_time=event_1_time),
            ],
        )
        assert status is not None
        assert status.name == TurnoverStatusName.CLEANED
        assert status.since == event_2_time

    def test_open_back_table_open(self) -> None:
        event_time = self.anchor_date + timedelta(minutes=15)
        status = get_turnover_status(
            self.turnover,
            [EventModel(id="1", event_type_id="back_table_open", start_time=event_time)],
        )
        assert status is not None
        assert status.name == TurnoverStatusName.OPENING
        assert status.since == event_time

    def test_open_endo_table_open(self) -> None:
        event_time = self.anchor_date + timedelta(minutes=15)
        status = get_turnover_status(
            self.turnover,
            [EventModel(id="1", event_type_id="endo_pack_open", start_time=event_time)],
        )
        assert status is not None
        assert status.name == TurnoverStatusName.OPENING
        assert status.since == event_time

    def test_forecasted_no_status(self) -> None:
        forecasted_turnover = dataclasses.replace(self.turnover, type=TurnoverType.FORECAST)
        status = get_turnover_status(forecasted_turnover, [])
        assert status is None
