from datetime import datetime, timedelta, timezone
from unittest.mock import AsyncMock

from api_server.services.apella_case.apella_case_service import ApellaCaseService
from api_server.services.apella_case.apella_case_store import ApellaCase
from api_server.services.case.case_store import Case
from api_server.services.events.event_models import EventModel
from api_server.services.phases.phase_store import PhaseModel
from api_server.services.phases.phase_type import MAX_PHASE_LENGTH
from api_server.services.turnover.turnover_service import TurnoverService
from api_server.services.turnover.turnover_models import TurnoverType
from api_server.services.turnover.turnover_store import TurnoverStore
from auth.auth import Auth
import pytest


mock_case_service = AsyncMock(spec=ApellaCaseService)
mock_auth = AsyncMock(spec=Auth)
mock_turnover_store = AsyncMock(spec=TurnoverStore)
mock_turnover_note_service = AsyncMock(return_value="Test Note")


turnover_service = TurnoverService(
    auth=mock_auth,
    apella_case_service=mock_case_service,
    turnover_store=mock_turnover_store,
    turnover_note_service=mock_turnover_note_service,
)


def mock_case(
    case_id: str,
    start_time: datetime,
    end_time: datetime,
    room_id: str = "room_1",
) -> ApellaCase:
    return ApellaCase(
        _case=Case(case_id=case_id),
        _actual=PhaseModel(
            room_id=room_id,
            start_event_id="event_1",
            end_event_id="event_2",
            start_event=EventModel(start_time=start_time),
            end_event=EventModel(start_time=end_time),
        ),
        _case_forecast=None,
        _apella_case_model=None,
    )


anchor_date = datetime.now(timezone.utc)


@pytest.mark.asyncio
async def test_get_turnovers_empty() -> None:
    result = await turnover_service.calculate_turnovers_for_room([])

    assert len(result) == 0


@pytest.mark.asyncio
async def test_get_turnovers_types() -> None:
    mock_case_1 = mock_case(
        "case_1",
        anchor_date - timedelta(hours=2),
        anchor_date - timedelta(hours=1, minutes=30),
    )
    mock_case_2 = mock_case(
        "case_2",
        anchor_date - timedelta(minutes=50),
        anchor_date - timedelta(minutes=30),
    )
    mock_case_3 = mock_case(
        "case_3",
        anchor_date + timedelta(minutes=30),
        anchor_date + timedelta(minutes=50),
    )
    mock_case_4 = mock_case(
        "case_4",
        anchor_date + timedelta(hours=1),
        anchor_date + timedelta(hours=2),
    )

    result = await turnover_service.calculate_turnovers_for_room(
        [mock_case_2, mock_case_1, mock_case_3, mock_case_4]
    )
    sorted_results = sorted(result, key=lambda t: t.id)

    assert len(sorted_results) == 3
    assert sorted_results[0].type == TurnoverType.COMPLETE
    assert sorted_results[1].type == TurnoverType.LIVE
    assert sorted_results[2].type == TurnoverType.FORECAST


@pytest.mark.asyncio
async def test_get_turnovers_excludes_long_turnovers() -> None:
    mock_case_1 = mock_case(
        "case_1",
        anchor_date - timedelta(hours=2),
        anchor_date - timedelta(hours=1, minutes=30),
    )
    mock_case_2 = mock_case(
        "case_2",
        anchor_date - timedelta(minutes=50),
        anchor_date - timedelta(minutes=30),
    )
    mock_case_3 = mock_case(
        "case_3",
        anchor_date + timedelta(minutes=30),
        anchor_date + timedelta(minutes=50),
    )
    mock_case_4 = mock_case(
        "case_4",
        anchor_date + timedelta(hours=1),
        anchor_date + timedelta(hours=2),
    )

    mock_case_5 = mock_case(
        "case_5",
        anchor_date
        + timedelta(hours=2, milliseconds=1)
        + MAX_PHASE_LENGTH,  # This turnover is 1 millisecond too long
        anchor_date + timedelta(hours=3) + MAX_PHASE_LENGTH,
    )

    mock_case_6 = mock_case(
        "case_6",
        anchor_date + timedelta(hours=4) + MAX_PHASE_LENGTH,
        anchor_date + timedelta(hours=5) + MAX_PHASE_LENGTH,
    )

    result = await turnover_service.calculate_turnovers_for_room(
        [mock_case_2, mock_case_1, mock_case_3, mock_case_4, mock_case_5, mock_case_6]
    )
    sorted_results = sorted(result, key=lambda t: t.id)

    assert len(sorted_results) == 4


@pytest.mark.asyncio
async def test_get_turnovers_one_room() -> None:
    mock_case_1 = mock_case("case_1", anchor_date, anchor_date + timedelta(minutes=10))
    mock_case_2 = mock_case(
        "case_2",
        anchor_date + timedelta(minutes=30),
        anchor_date + timedelta(minutes=50),
    )
    mock_case_3 = mock_case(
        "case_3",
        anchor_date + timedelta(hours=4),
        anchor_date + timedelta(hours=5),
    )
    # This case overlaps the previous, so no turnover should be generated
    mock_case_4 = mock_case(
        "case_4",
        anchor_date + timedelta(hours=4, minutes=30),
        anchor_date + timedelta(hours=5),
    )

    result = await turnover_service.calculate_turnovers_for_room(
        [mock_case_2, mock_case_1, mock_case_3, mock_case_4]
    )
    sorted_results = sorted(result, key=lambda t: t.id)

    assert len(sorted_results) == 2
    assert sorted_results[0].id == "turnover-" + mock_case_1.id + "-" + mock_case_2.id
    assert sorted_results[0].start_time == mock_case_1.end_time
    assert sorted_results[0].end_time == mock_case_2.start_time
    assert sorted_results[0].meets_inclusion_criteria is True

    assert sorted_results[1].id == "turnover-" + mock_case_2.id + "-" + mock_case_3.id
    assert sorted_results[1].start_time == mock_case_2.end_time
    assert sorted_results[1].end_time == mock_case_3.start_time
    assert sorted_results[1].meets_inclusion_criteria is False
