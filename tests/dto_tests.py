from datetime import datetime, timezone
from unittest.mock import MagicMock

import pytest

from apella_cloud_api.dtos import (
    CaseRawDto,
    CaseUpdateDto,
    EventDto,
    UserDto,
    EventChangeDto,
    EventChangelogAction,
)
from apella_cloud_api.exceptions import ClientError


def test_email_format_is_validated_when_schema_is_used() -> None:
    dto = UserDto.from_json('{"email": "bad-format"}')
    assert dto.email == "bad-format"
    assert UserDto.schema().validate(dto.to_dict()) == {"email": ["Not a valid email address."]}


def test_user_schema_includes_all_fields() -> None:
    assert UserDto.schema().validate({"user_id": 123}) == {"user_id": ["Not a valid string."]}


def test_org_id_is_renamed() -> None:
    dto = EventDto().from_json('{"organization_id": "apella"}')
    assert dto.org_id == "apella"


def test_bad_datetime_gets_good_error() -> None:
    bad_datetime = "bad datetime"
    with pytest.raises(ClientError, match=f"Value is not an isoformat datetime: {bad_datetime}"):
        EventDto().from_json(f'{{"start_time": "{bad_datetime}"}}')


def test_datetime_with_bad_timezone_gets_good_error() -> None:
    bad_datetime = "2021-01-01T00:00:00-99:00"
    with pytest.raises(ClientError, match=f"Value is not an isoformat datetime: {bad_datetime}"):
        EventDto().from_json(f'{{"start_time": "{bad_datetime}"}}')


def test_external_message_id_is_not_none_when_initialized_case_update_dto() -> None:
    dto = CaseUpdateDto(case=MagicMock(), external_message_id=MagicMock())
    assert dto.external_message_id is not None


def test_external_message_id_is_not_none_when_initialized_case_raw_dto() -> None:
    raw = {"foo": "bar"}
    external_message_id = "sample_external_message_id"
    dto = CaseRawDto(raw, "organization_id", external_message_id=external_message_id)
    assert dto.external_message_id == external_message_id


def test_event_changelog_action_can_be_serialized_and_deserialized() -> None:
    dto = EventChangeDto(
        event_id="123",
        event_name="event_name",
        action=EventChangelogAction.CREATE,
        organization_id="org_id",
        site_id="site_id",
        room_id="room_id",
        camera_id="camera_id",
        start_time=datetime.now(tz=timezone.utc),
        source_type="source_type",
    )

    dto_json = dto.to_json()
    dto_deserialized = EventChangeDto.from_json(dto_json)

    assert dto.__dict__ == dto_deserialized.__dict__
    assert dto_deserialized.action == EventChangelogAction.CREATE
