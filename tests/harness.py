# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs
from unittest.mock import AsyncMock, MagicMock

from graphene.test import Client
from graphql import GraphQLError

from apella_cloud_api import (
    ApellaGQLClient,
    ObjectField,
    gql__Query,
    results_to_dataclass,
)
from api_server.graphql.context import Context
from api_server.graphql.graphene import schema
from api_server.services.block.graphql.block_loader import (
    <PERSON><PERSON>oa<PERSON>,
    BlockTimeLoader,
    BlockTimeForBlockLoader,
    BlockTimeReleaseForBlockTimeLoader,
    RoomBlockTimeLoader,
)
from api_server.services.camera.graphql.camera_loader import CameraLoader
from api_server.services.camera.graphql.default_camera_loader import DefaultCameraLoader
from api_server.services.case.graphql.case_loader import CaseLoader
from api_server.services.case.graphql.case_procedure_loader import CaseProcedureLoader
from api_server.services.case.graphql.case_staff_loader import CaseStaffLoader

from api_server.services.events.graphql.event_loaders import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    RoomEventsLoader,
)
from api_server.services.highlights.graphql.highlight_feedback_loader import (
    HighlightFeedbackLoader,
)
from api_server.services.highlights.graphql.highlight_loader import HighlightLoader
from api_server.services.measurement_periods.graphql.measurement_periods_loader import (
    MeasurementPeriodLoader,
)
from api_server.services.observations.graphql.observations_loader import (
    ObservationLoader,
)
from api_server.services.organization.graphql.organization_loader import (
    OrganizationLoader,
    OrganizationSitesLoader,
)
from api_server.services.plan.graphql.case_note_plan_loader import CaseNotePlanLoader
from api_server.services.plan.graphql.case_staff_plan_loader import CaseStaffPlanLoader
from api_server.services.procedures.graphql.procedure_loader import ProcedureLoader
from api_server.services.room.graphql.room_loader import RoomLoader, RoomCamerasLoader
from api_server.services.room.graphql.room_tag_loader import RoomTagLoader
from api_server.services.site.graphql.site_loader import SiteLoader, SiteRoomsLoader
from api_server.services.staff.graphql.staff_loader import StaffLoader
from api_server.services.staffing_needs.graphql.staffing_needs_roles_loader import (
    StaffingNeedsRolesLoader,
)
from api_server.services.turnover.graphql.turnover_goals_loader import (
    TurnoverGoalsLoader,
)
from api_server.services.users.graphql.user_loader import UserLoader


def harness():
    test_context = make_test_context()
    test_client = make_test_client()
    return test_client, test_context


def make_test_context():
    result = MagicMock(spec=Context)

    result.case_activity_service = MagicMock()
    result.event_service = AsyncMock()
    result.user_service = AsyncMock()
    result.organization_service = AsyncMock()
    result.site_service = AsyncMock()
    result.room_service = AsyncMock()
    result.highlight_service = MagicMock()
    result.camera_service = MagicMock()
    result.latest_image_loader = MagicMock()
    result.media_service = MagicMock()
    result.objects_service = MagicMock()
    result.observation_service = MagicMock()
    result.case_service = MagicMock()
    result.staffing_needs_service = MagicMock()
    result.staff_service = MagicMock()
    result.case_note_plan_service = MagicMock()
    result.case_staff_service = MagicMock()
    result.case_staff_plan_service = MagicMock()
    result.procedure_service = MagicMock()
    result.measurement_period_service = MagicMock()
    result.block_service = AsyncMock()
    result.phase_service = MagicMock()
    result.contact_information_service = MagicMock()
    result.launch_darkly_service = MagicMock()
    result.apella_case_service = MagicMock()
    result.turnover_service = MagicMock()
    result.case_forecast_service = AsyncMock()

    result.send_grid_service = MagicMock()
    result.schedule_assistant_email_builder_service = AsyncMock()
    result.schedule_assistant_email_service = AsyncMock()

    result.user_loader = UserLoader(user_service=result.user_service)
    result.org_loader = OrganizationLoader(organization_service=result.organization_service)
    result.staffing_needs_roles_loader = StaffingNeedsRolesLoader(
        staffing_needs_service=result.staffing_needs_service
    )
    result.turnover_goals_loader = TurnoverGoalsLoader(turnover_service=result.turnover_service)
    result.site_loader = SiteLoader(site_service=result.site_service)
    result.room_loader = RoomLoader(room_service=result.room_service)
    result.room_cameras_loader = RoomCamerasLoader(camera_service=result.camera_service)
    result.room_tag_loader = RoomTagLoader(room_service=result.room_service)
    result.case_loader = CaseLoader(case_service=result.case_service)
    result.staff_loader = StaffLoader(staff_service=result.staff_service)
    result.case_note_plan_loader = CaseNotePlanLoader(
        case_note_plan_service=result.case_note_plan_service
    )
    result.case_staff_loader = CaseStaffLoader(case_staff_service=result.case_staff_service)
    result.case_staff_plan_loader = CaseStaffPlanLoader(
        case_staff_plan_service=result.case_staff_plan_service
    )
    result.case_procedure_loader = CaseProcedureLoader(procedure_service=result.procedure_service)
    result.procedure_loader = ProcedureLoader(procedure_service=result.procedure_service)
    result.camera_loader = CameraLoader(camera_service=result.camera_service)
    result.default_camera_loader = DefaultCameraLoader(camera_service=result.camera_service)
    result.event_attrs_loader = EventAttrsLoader(event_service=result.event_service)
    result.event_loader = EventLoader(event_service=result.event_service)
    result.room_events_loader = RoomEventsLoader(event_service=result.event_service)
    result.highlight_loader = HighlightLoader(highlight_service=result.highlight_service)
    result.highlight_feedback_loader = HighlightFeedbackLoader(
        highlight_service=result.highlight_service
    )
    result.measurement_period_loader = MeasurementPeriodLoader(
        measurement_period_service=result.measurement_period_service
    )
    result.block_loader = BlockLoader(block_service=result.block_service)
    result.block_time_loader = BlockTimeLoader(block_service=result.block_service)
    result.block_time_for_block_loader = BlockTimeForBlockLoader(block_service=result.block_service)
    result.block_time_release_for_block_time_loader = BlockTimeReleaseForBlockTimeLoader(
        block_service=result.block_service
    )
    result.room_block_time_loader = RoomBlockTimeLoader(block_service=result.block_service)
    result.observation_loader = ObservationLoader(observation_service=result.observation_service)
    result.site_rooms_loader = SiteRoomsLoader(room_service=result.room_service)
    result.org_sites_loader = OrganizationSitesLoader(site_service=result.site_service)

    result.auth = MagicMock()

    message_1 = MagicMock()
    message_1.sid = "1234"

    TwilioClient = MagicMock()
    TwilioClient.messages = MagicMock()
    TwilioClient.messages.create = MagicMock(return_value=message_1)

    result.twilio_client = TwilioClient
    result.twilio_from_number = "+1234567890"

    SendGridClient = MagicMock()
    SendGridClient.send = MagicMock()
    result.sendgrid_client = SendGridClient

    return result


def make_test_client():
    return TestClient(schema())


class TestClient(Client):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.apella_gql_client = ApellaGQLClient()

    async def execute_apella_query_async(self, query: ObjectField[gql__Query], *args, **kwargs):
        """
        Execute a query using the Apella Schema Client for unit testing GraphQL layer.
        """
        query_obj = self.apella_gql_client.query.select(query)
        query_obj_gql = query_obj.to_gql()
        result_json = await self.execute_async(query_obj_gql, *args, **kwargs)
        if "errors" in result_json:
            raise GraphQLError(message=str(result_json["errors"]))
        return results_to_dataclass(result_json["data"], query_obj)
