# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

import logging
from http import HTTPStatus
from unittest.mock import patch

from apella_cloud_api import ServerError
from apella_cloud_api.exceptions import ClientError
from api_server.logging import report_internal_error, warning


@patch("config.use_gcloud_logging", return_value=False)
@patch("logging.log")
def test_report_internal_error_prints(mock_log, mock_gcloud_logging) -> None:
    error_message = "error_message"
    status_code = HTTPStatus.INTERNAL_SERVER_ERROR

    server_error = ServerError(message=error_message, status_code=status_code)

    # Raise the error to populate the stack trace
    try:
        raise server_error
    except Exception as e:
        error_id = report_internal_error(e=e)

    mock_log.assert_called_once()
    args, kwargs = mock_log.call_args
    assert kwargs["extra"]["json_fields"]["error_id"] == error_id
    assert kwargs["level"] == logging.ERROR
    assert kwargs["exc_info"] == server_error


@patch("config.use_gcloud_logging", return_value=False)
@patch("logging.log")
def test_warning_prints_correctly(mock_log, mock_gcloud_logging) -> None:
    error_message = "error_message"
    status_code = HTTPStatus.BAD_REQUEST

    client_error = ClientError(message=error_message, status_code=status_code)

    # Raise the error to populate the stack trace
    try:
        raise client_error
    except Exception as e:
        warning(exception=e)

    mock_log.assert_called_once()
    args, kwargs = mock_log.call_args
    assert kwargs["level"] == logging.WARNING
    assert kwargs["exc_info"] == client_error
