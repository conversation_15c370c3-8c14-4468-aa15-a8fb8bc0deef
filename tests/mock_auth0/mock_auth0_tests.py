import socket

import pytest

from mocks.mock_auth0.database import Database
from mocks.mock_auth0.rest_app import MockAuth0


@pytest.mark.asyncio
async def test_basic_server() -> None:
    """Show that we can connect to the mock auth0 server on a random available port"""
    db = Database()
    service = MockAuth0(db)
    async with service.serving() as port:
        with socket.socket() as sock:
            print("Attempting to connect\n")
            sock.connect(("127.0.0.1", port))
            print("Connected\n")


@pytest.mark.asyncio
async def test_provide_port() -> None:
    """Show that we can connect to the mock auth0 server on an assigned port"""

    # Acquire a port to use
    with socket.socket() as sock:
        sock.bind(("127.0.0.1", 0))
        _, port = sock.getsockname()

    db = Database()
    service = MockAuth0(db)
    async with service.serving(port) as actual_port:
        assert port == actual_port
        with socket.socket() as sock:
            sock.connect(("127.0.0.1", port))


@pytest.mark.asyncio
async def test_server_shuts_down() -> None:
    """Show that the context manager shuts down the server when it exits"""
    db = Database()
    service = MockAuth0(db)
    async with service.serving() as port:
        pass

    with pytest.raises(ConnectionRefusedError):
        with socket.socket() as sock:
            sock.connect(("127.0.0.1", port))
