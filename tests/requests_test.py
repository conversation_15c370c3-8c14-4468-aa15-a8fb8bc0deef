import json
from http import HTTPStatus
from unittest.mock import call, patch

import pytest
from requests import Response
from requests.exceptions import ConnectionError, Timeout

import apella_mock_api_server
from apella_cloud_api import Client
from apella_cloud_api.dtos import RoomInfoDto
from apella_cloud_api.exceptions import (
    ClientError,
    NotAuthorized,
    NotFound,
    ServerError,
)
from apella_cloud_api.apella_requests import assert_no_error_status_codes, Session

SERVER_URL = "http://localhost:8080"

room0 = RoomInfoDto()
room0.room_id = "test-room-id"
room0.organization_id = "test_org_id"
room0.site_id = "test_site_id"


def test_assert_no_error_status_codes_success() -> None:
    apella_mock_api_server.ensure_mock_server_is_up()
    apella_mock_api_server.reset()
    apella_mock_api_server.add_response(HTTPStatus.OK, room0.to_dict())

    client = Client(SERVER_URL, no_auth=True)

    response = client.make_request(
        "GET", f"{SERVER_URL}/v1/room/{room0.room_id}", headers=client.headers()
    )

    assert_no_error_status_codes(response)
    apella_mock_api_server.assert_all_responses_sent_successfully()


def test_assert_no_error_status_codes_NotAuthorized() -> None:
    apella_mock_api_server.ensure_mock_server_is_up()
    apella_mock_api_server.reset()
    apella_mock_api_server.add_response(HTTPStatus.UNAUTHORIZED, {"msg": "No Entry"})

    client = Client(SERVER_URL, no_auth=True)

    with pytest.raises(NotAuthorized):
        assert_no_error_status_codes(
            client.make_request(
                "GET", f"{SERVER_URL}/v1/room/{room0.room_id}", headers=client.headers()
            )
        )


def test_assert_no_error_status_codes_NotFound() -> None:
    apella_mock_api_server.ensure_mock_server_is_up()
    apella_mock_api_server.reset()
    apella_mock_api_server.add_response(HTTPStatus.NOT_FOUND, {})

    client = Client(SERVER_URL, no_auth=True)

    with pytest.raises(NotFound):
        assert_no_error_status_codes(
            client.make_request(
                "GET", f"{SERVER_URL}/v1/room/{room0.room_id}", headers=client.headers()
            )
        )


def test_assert_no_error_status_codes_client_error() -> None:
    apella_mock_api_server.ensure_mock_server_is_up()
    apella_mock_api_server.reset()
    apella_mock_api_server.add_response(HTTPStatus.IM_A_TEAPOT, {})

    client = Client(SERVER_URL, no_auth=True)

    with pytest.raises(ClientError):
        assert_no_error_status_codes(
            client.make_request(
                "GET", f"{SERVER_URL}/v1/room/{room0.room_id}", headers=client.headers()
            )
        )


def test_assert_no_error_status_codes_server_error() -> None:
    apella_mock_api_server.ensure_mock_server_is_up()
    apella_mock_api_server.reset()
    apella_mock_api_server.add_response(HTTPStatus.BAD_GATEWAY, {})

    client = Client(SERVER_URL, no_auth=True)

    with pytest.raises(ServerError):
        assert_no_error_status_codes(
            client.make_request(
                "GET", f"{SERVER_URL}/v1/room/{room0.room_id}", headers=client.headers()
            )
        )


def test_request_with_retryable_errors() -> None:
    apella_mock_api_server.ensure_mock_server_is_up()
    apella_mock_api_server.reset()
    apella_mock_api_server.add_response(HTTPStatus.BAD_GATEWAY, {})
    apella_mock_api_server.add_response(HTTPStatus.GATEWAY_TIMEOUT, {})
    apella_mock_api_server.add_response(HTTPStatus.OK, room0.to_dict())

    client = Client(SERVER_URL, no_auth=True)

    response = client.make_request_with_retry(
        "GET",
        f"{SERVER_URL}/v1/media/query",
        headers=client.headers(),
    )

    assert RoomInfoDto.from_dict(response.json()).to_dict() == room0.to_dict()


def test_request_with_non_retryable_errors() -> None:
    apella_mock_api_server.ensure_mock_server_is_up()
    apella_mock_api_server.reset()
    apella_mock_api_server.add_response(HTTPStatus.BAD_GATEWAY, {})
    apella_mock_api_server.add_response(HTTPStatus.UNAUTHORIZED, {})  # stop tryin'

    client = Client(SERVER_URL, no_auth=True)

    with pytest.raises(NotAuthorized):
        assert_no_error_status_codes(
            client.make_request_with_retry(
                "GET",
                f"{SERVER_URL}/v1/media/query",
                headers=client.headers(),
            )
        )


def test_request_with_connection_error() -> None:
    response = Response()
    response.status_code = HTTPStatus.OK
    response._content = json.dumps(room0.to_dict()).encode("utf-8")

    with patch.object(
        Session, "request", side_effect=[ConnectionError, Timeout, response]
    ) as mock_request:
        client = Client(SERVER_URL, no_auth=True, http_session=Session())
        result = client.make_request_with_retry("GET", "url")
        assert RoomInfoDto.from_dict(result.json()).to_dict() == room0.to_dict()
        assert mock_request.call_args_list == [
            call("GET", "url"),
            call("GET", "url"),
            call("GET", "url"),
        ]
