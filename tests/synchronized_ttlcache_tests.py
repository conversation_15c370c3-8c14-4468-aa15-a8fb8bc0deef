# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

import asyncio
import threading
import time

from cachetools.func import ttl_cache

from utils.synchronized_ttl_cache import synchronized_ttl_cache, async_synchronized_ttl_cache


def call_method_in_multiple_threads(method, num_threads):
    # Create the threads
    threads = []
    for i in range(num_threads):
        threads.append(threading.Thread(target=method))

    # Start the threads
    for thread in threads:
        thread.start()

    # Wait for all them to finish
    for thread in threads:
        thread.join()


async def call_method_multiple_times(method, num_calls):
    tasks = [method() for _ in range(num_calls)]
    await asyncio.gather(*tasks)


# We need to keep track of how many times a method is actually called
ttl_cached_method_executed_counter = 0
synchronized_ttl_cached_method_executed_counter = 0
synchronized_ttl_cached_async_method_executed_counter = 0


# This is the ttl cache from the cachetools package that has no synchronization on the arguments
@ttl_cache(ttl=60)
def ttl_cached_method(arg, delay):
    # Increment the counter that says we have been called
    global ttl_cached_method_executed_counter
    ttl_cached_method_executed_counter = ttl_cached_method_executed_counter + 1
    # Sleep to simulate IO
    time.sleep(delay)
    # Return the arg
    return arg


# This is the ttl cache that we have written that is synchronized
@synchronized_ttl_cache(ttl=60)
def synchronized_ttl_cached_method(arg: str, delay: int) -> str:
    # Increment the counter that says we have been called
    global synchronized_ttl_cached_method_executed_counter
    synchronized_ttl_cached_method_executed_counter = (
        synchronized_ttl_cached_method_executed_counter + 1
    )
    # Sleep to simulate IO
    time.sleep(delay)
    # Return the arg
    return arg


@async_synchronized_ttl_cache(ttl=60)
async def synchronized_ttl_cached_async_method(arg: str, delay: int) -> str:
    # Increment the counter that says we have been called
    global synchronized_ttl_cached_async_method_executed_counter
    synchronized_ttl_cached_async_method_executed_counter = (
        synchronized_ttl_cached_async_method_executed_counter + 1
    )
    # Sleep to simulate IO
    await asyncio.sleep(delay)
    # Return the arg
    return arg


def test_plain_ttl_cache_fails() -> None:
    # To test that this cache indeed does not synchronize on the args
    # We will call the same method in multiple threads many times
    global ttl_cached_method_executed_counter
    ttl_cached_method_executed_counter = 0
    num_threads = 10
    delay = 1  # second

    start = time.time()
    call_method_in_multiple_threads(lambda: ttl_cached_method("arg", delay), num_threads)
    end = time.time()

    # And we can see that it still got called for each thread
    assert num_threads == ttl_cached_method_executed_counter
    assert start - end < 2  # Less than 2 seconds for all 10 calls


def test_synchronized_ttl_cache_is_only_run_once() -> None:
    # To test that this cache indeed does not synchronize on the args
    # We will call the same method in multiple threads many times
    global synchronized_ttl_cached_method_executed_counter
    synchronized_ttl_cached_method_executed_counter = 0
    num_threads = 10
    delay = 1  # second

    start = time.time()
    call_method_in_multiple_threads(
        lambda: synchronized_ttl_cached_method("arg", delay), num_threads
    )
    end = time.time()

    # And we can assert that it only got called once
    assert 1 == synchronized_ttl_cached_method_executed_counter
    assert start - end < 2  # Less than 2 seconds for all 10 calls


async def test_async_synchronized_ttl_cache_is_only_run_once() -> None:
    # To test that this cache indeed does not synchronize on the args
    # We will call the same method in multiple threads many times
    global synchronized_ttl_cached_async_method_executed_counter
    synchronized_ttl_cached_async_method_executed_counter = 0
    num_times = 10
    delay = 1  # second

    start = time.time()
    await call_method_multiple_times(
        lambda: synchronized_ttl_cached_async_method("arg", delay), num_times
    )
    end = time.time()

    # And we can assert that it only got called once
    assert 1 == synchronized_ttl_cached_async_method_executed_counter
    assert start - end < 2  # Less than 2 seconds for all 10 calls
