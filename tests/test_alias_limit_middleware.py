import pytest
from typing import None as NoneType
from unittest.mock import Mock, AsyncMock
from graphql import parse
from graphql.language.ast import OperationDefinitionNode

from api_server.graphql.middleware.alias_limit_middleware import AliasLimitMiddleware


class TestAliasLimitMiddleware:
    """Test cases for the AliasLimitMiddleware."""

    def setup_method(self):
        """Set up test fixtures."""
        self.middleware = AliasLimitMiddleware()
        self.mock_next = Mock()
        self.mock_info = Mock()

    @pytest.mark.asyncio
    async def test_query_without_aliases_passes(self):
        """Test that queries without aliases pass through normally."""
        # Mock a query without aliases
        query = """
        query {
            user {
                name
                email
            }
        }
        """
        operation = parse(query).definitions[0]
        self.mock_info.operation = operation

        # Mock next function
        self.mock_next.return_value = "success"

        # Execute middleware
        result = await self.middleware.resolve(self.mock_next, None, self.mock_info)

        # Verify the query passed through
        assert result == "success"
        self.mock_next.assert_called_once()

    @pytest.mark.asyncio
    async def test_query_with_few_aliases_passes(self):
        """Test that queries with aliases under the limit pass through."""
        # Mock a query with 2 aliases (under limit of 3)
        query = """
        query {
            user1: user(id: "1") {
                name
            }
            user2: user(id: "2") {
                name
            }
        }
        """
        operation = parse(query).definitions[0]
        self.mock_info.operation = operation

        # Mock next function
        self.mock_next.return_value = "success"

        # Execute middleware
        result = await self.middleware.resolve(self.mock_next, None, self.mock_info)

        # Verify the query passed through
        assert result == "success"
        self.mock_next.assert_called_once()

    @pytest.mark.asyncio
    async def test_query_with_too_many_aliases_fails(self):
        """Test that queries with too many aliases are rejected."""
        # Mock a query with 4 aliases (over limit of 3)
        query = """
        query {
            user1: user(id: "1") {
                name
            }
            user2: user(id: "2") {
                name
            }
            user3: user(id: "3") {
                name
            }
            user4: user(id: "4") {
                name
            }
            user5: user(id: "5") {
                name
            }
            user6: user(id: "6") {
                name
            }
            user7: user(id: "7") {
                name
            }
            user8: user(id: "8") {
                name
            }
            user9: user(id: "9") {
                name
            }
            user10: user(id: "10") {
                name
            }
            user11: user(id: "11") {
                name
            }
            user12: user(id: "12") {
                name
            }
            user13: user(id: "13") {
                name
            }
            user14: user(id: "14") {
                name
            }
            user15: user(id: "15") {
                name
            }
            user16: user(id: "16") {
                name
            }
        }
        """
        operation = parse(query).definitions[0]
        self.mock_info.operation = operation

        # TODO: Enable failing requests if no logs exist
        # # Execute middleware and expect GraphQLError
        # with pytest.raises(GraphQLError) as exc_info:
        #     await self.middleware.resolve(self.mock_next, None, self.mock_info)
        #
        # # Verify error message
        # assert "16 aliases" in str(exc_info.value)
        # assert "exceeds the maximum allowed limit of 15" in str(exc_info.value)
        #
        # # Verify next was not called
        # self.mock_next.assert_not_called()

    @pytest.mark.asyncio
    async def test_nested_aliases_are_counted(self):
        """Test that aliases in nested fields are properly counted."""
        # Mock a query with nested aliases
        query = """
        query {
            user1: user(id: "1") {
                profile1: profile {
                    name
                }
                posts1: posts {
                    title
                }
            }
            user2: user(id: "2") {
                profile2: profile {
                    name
                }
            }
            user3: user(id: "3") {
                profile3: profile {
                    name
                }
                posts2: posts {
                    title
                }
                posts3: posts {
                    title
                }
                posts4: posts {
                    title
                }
                posts5: posts {
                    title
                }
                posts6: posts {
                    title
                }
            }
            user4: user(id: "4") {
                profile4: profile {
                    name
                }
                profile5: profile {
                    name
                }
                profile6: profile {
                    name
                }
            }
            user5: user(id: "5") {
                profile5: profile {
                    name
                }
            }
        }
        """
        operation = parse(query).definitions[0]
        self.mock_info.operation = operation

        # TODO: Enable failing requests when we enable failing
        # with pytest.raises(GraphQLError) as exc_info:
        #     await self.middleware.resolve(self.mock_next, None, self.mock_info)
        #
        # assert "18 aliases" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_middleware_only_checks_root_level(self):
        """Test that middleware only checks on root level (when root is None)."""
        # Mock a non-root call
        query = """
        query {
            user1: user(id: "1") {
                name
            }
            user2: user(id: "2") {
                name
            }
            user3: user(id: "3") {
                name
            }
            user4: user(id: "4") {
                name
            }
        }
        """
        operation = parse(query).definitions[0]
        self.mock_info.operation = operation

        # Mock next function
        self.mock_next.return_value = "success"

        # Execute middleware with non-None root (simulating nested resolver call)
        result = await self.middleware.resolve(self.mock_next, {"some": "root"}, self.mock_info)

        # Should pass through without checking aliases
        assert result == "success"
        self.mock_next.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_next_function_handling(self):
        """Test that async next functions are properly awaited."""
        # Mock an async next function
        async_mock = AsyncMock(return_value="async_success")

        query = """
        query {
            user {
                name
            }
        }
        """
        operation = parse(query).definitions[0]
        self.mock_info.operation = operation

        # Execute middleware
        result = await self.middleware.resolve(async_mock, None, self.mock_info)

        # Verify async function was awaited
        assert result == "async_success"
        async_mock.assert_called_once()

    def test_count_aliases_empty_operation(self):
        """Test alias counting with empty operation."""
        # Create a mock operation with no selection set
        mock_operation = Mock(spec=OperationDefinitionNode)
        mock_operation.selection_set = None

        count = self.middleware._count_aliases(mock_operation)
        assert count == 0

    def test_count_aliases_complex_query(self):
        """Test alias counting with a complex query structure."""
        query = """
        query {
            user1: user(id: "1") {
                name
                profile1: profile {
                    bio
                    avatar1: avatar {
                        url
                    }
                }
                posts1: posts {
                    title1: title
                    content1: content
                }
            }
            organization1: organization {
                name
                sites1: sites {
                    name
                }
            }
        }
        """
        operation = parse(query).definitions[0]

        # Count: user1, profile1, avatar1, posts1, title1, content1, organization1, sites1 = 8 aliases
        count = self.middleware._count_aliases(operation)
        assert count == 8

    @pytest.mark.asyncio
    async def test_mutation_with_aliases(self):
        """Test that mutations with aliases are also checked."""
        query = """
        mutation {
            create1: createUser(input: {name: "John"}) {
                id
            }
            create2: createUser(input: {name: "Jane"}) {
                id
            }
            create3: createUser(input: {name: "Bob"}) {
                id
            }
            create4: createUser(input: {name: "Alice"}) {
                id
            }
        }
        """
        operation = parse(query).definitions[0]
        self.mock_info.operation = operation
        count = self.middleware._count_aliases(operation)
        assert count == 4

    @pytest.mark.asyncio
    async def test_fragment_aliases_are_counted(self):
        """Test that aliases within fragments are counted."""
        query = """
        query {
            user1: user(id: "1") {
                ...UserFragment
            }
            user2: user(id: "2") {
                name
            }
        }
        fragment UserFragment on User {
            profile1: profile {
                bio
            }
            posts1: posts {
                title
            }
        }
        """
        # Note: This test simulates the structure, but in practice fragments
        # would be resolved differently. The middleware handles inline fragments.
        operation = parse(query).definitions[0]

        # For this test, we're checking that the middleware can handle
        # the basic structure without crashing
        count = self.middleware._count_aliases(operation)
        # Should count user1 and user2 from the main query
        assert count >= 2
