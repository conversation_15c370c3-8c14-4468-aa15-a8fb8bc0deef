import contextlib
import socket
import time
from datetime import timedelta
from threading import Thread
from typing import Iterator, <PERSON><PERSON>, Type, TypeVar, Union

import pytest

from utils.net import allocate_port, wait_for_port

E = TypeVar("E", bound=BaseException)


@contextlib.contextmanager
def timeout_expected(
    expected_exception: Union[Type[E], Tuple[Type[E], ...]], timeout: timedelta
) -> Iterator[None]:
    timeout_ns = timeout.total_seconds() * 1_000_000_000
    start = time.perf_counter_ns()

    with pytest.raises(expected_exception):
        yield

    duration = time.perf_counter_ns() - start

    # Ensure wait_for_port() waits at least as long as the specified timeout and no longer than a
    # second more.
    assert timeout_ns < duration < timeout_ns + 1_000_000_000


def test_wait_for_unbound_port_with_timeout() -> None:
    # Port 1023 is reserved by IANA and is extremely unlikely to have anything listening on it
    port = 1023

    timeout = timedelta(seconds=1)
    with timeout_expected(ConnectionRefusedError, timeout):
        wait_for_port("127.0.0.1", port, timeout)


@pytest.fixture
def bound_socket() -> Iterator[socket.socket]:
    with socket.socket() as sock:
        sock.bind(("127.0.0.1", 0))
        yield sock


def test_wait_for_already_bound_port(bound_socket: socket.socket) -> None:
    addr, port = bound_socket.getsockname()

    expected_exceptions = (
        socket.timeout,  # On MacOS
        ConnectionRefusedError,  # On Linux
    )
    timeout = timedelta(seconds=1)
    with timeout_expected(expected_exceptions, timeout):
        wait_for_port("127.0.0.1", port, timeout)


def test_wait_for_port_that_gets_closed(bound_socket: socket.socket) -> None:
    addr, port = bound_socket.getsockname()

    # Close the bound socket before the timeout
    def close_socket() -> None:
        # Sleep for a second to allow the main thread to block on the connection in wait_for_port
        time.sleep(1)

        bound_socket.close()

    thread = Thread(target=close_socket)
    thread.start()

    timeout = timedelta(seconds=2)
    with timeout_expected(ConnectionRefusedError, timeout):
        wait_for_port("127.0.0.1", port, timeout)

    thread.join()


def test_wait_for_already_bound_and_listening_port(bound_socket: socket.socket) -> None:
    addr, port = bound_socket.getsockname()

    bound_socket.listen()

    wait_for_port(addr, port, timeout=None)


def test_wait_for_bound_and_listening_port(bound_socket: socket.socket) -> None:
    addr, port = bound_socket.getsockname()

    def say_hi() -> None:
        # Sleep for a second to allow the main thread to block on the connection in wait_for_port
        time.sleep(1)

        # Now listen, which should unblock the connection in wait_for_port
        bound_socket.listen()

        # Accept the connection from wait_for_port and discard it
        bound_socket.accept()

        # Accept the connection from the test body and say hi!
        conn, _ = bound_socket.accept()
        with conn:
            conn.send(b"hi")

    thread = Thread(target=say_hi)
    thread.start()

    wait_for_port(addr, port, timeout=None)

    # Once wait_for_port has returned, we should be able to connect and read from the socket
    with socket.socket() as client_sock:
        client_sock.connect((addr, port))
        assert client_sock.recv(2) == b"hi"

    thread.join()


def test_allocate_port() -> None:
    port = allocate_port()
    with socket.socket() as sock:
        sock.bind(("127.0.0.1", port))


def test_allocate_multiple_ports_returns_different_values() -> None:
    """
    Spot-check that allocate_port() is returning different ports on each call. Note that this test
    may fail if the OS decides to recycle recently allocated ports, but that should be a (very) rare
    occasion.
    """
    ports = set(allocate_port() for _ in range(10))
    assert len(ports) == 10
