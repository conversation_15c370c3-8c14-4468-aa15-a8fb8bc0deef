import uuid


from utils.helpers import convert_str_to_uuid, convert_to_uuid


def test_convert_str_uuid_to_uuid() -> None:
    test_uuid = uuid.uuid4()
    assert test_uuid == convert_str_to_uuid(test_uuid)


def test_convert_str_to_uuid() -> None:
    converted_uuid = convert_str_to_uuid("random")
    assert isinstance(converted_uuid, uuid.UUID)


def test_uuid_to_uuid() -> None:
    test_uuid = uuid.uuid4()
    assert test_uuid == convert_to_uuid(test_uuid)


def test_int_to_uuid() -> None:
    test_uuid = uuid.uuid4()
    assert test_uuid == convert_to_uuid(test_uuid.int)


def test_str_to_uuid() -> None:
    test_uuid = uuid.uuid4()
    assert test_uuid == convert_to_uuid(test_uuid.hex)


def test_bytes_to_uuid() -> None:
    test_uuid = uuid.uuid4()
    assert test_uuid == convert_to_uuid(test_uuid.bytes)
