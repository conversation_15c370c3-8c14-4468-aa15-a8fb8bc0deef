from typing import List, AsyncGenerator

import pytest

from api_server.services.anesthesia.anesthesia_store import AnesthesiaModel
from tests_component import harness


@pytest.fixture
def org_id() -> str:
    return harness.org_0.id


@pytest.fixture
def name() -> str:
    return "General"


@pytest.fixture
def names() -> List[str]:
    return ["MAC", "Spinal"]


@pytest.fixture()
async def new_anesthesia(name: str, org_id: str) -> AsyncGenerator[AnesthesiaModel, None]:
    anesthesia = (
        await harness.anesthesia_store.upsert_anesthesias(
            [AnesthesiaModel(org_id=org_id, name=name)]
        )
    )[0]
    yield anesthesia
    await harness.anesthesia_store.delete_anesthesia(str(anesthesia.id))


@pytest.fixture()
async def existing_anesthesias(
    names: List[str], org_id: str
) -> AsyncGenerator[List[AnesthesiaModel], None]:
    anesthesia_list = []

    for anesthesia_name in names:
        anesthesia = (
            await harness.anesthesia_store.upsert_anesthesias(
                [AnesthesiaModel(org_id=org_id, name=anesthesia_name)]
            )
        )[0]
        anesthesia_list.append(anesthesia)

    await harness.anesthesia_store.upsert_anesthesias(anesthesia_list)

    yield anesthesia_list

    for anesthesia in anesthesia_list:
        await harness.anesthesia_store.delete_anesthesia(str(anesthesia.id))
