from datetime import datetime, timedelta, timezone
from typing import Optional
import uuid

from sqlalchemy.ext.asyncio import AsyncSession

from api_server.services.annotation_tasks.annotation_task_models import (
    TaskStatus,
)
from api_server.services.annotation_tasks.annotation_task_store import AnnotationTask
from api_server.services.room.room_store import RoomModel
from api_server.services.users.user_store import User
from tests_component import harness


async def create_task(
    async_session: AsyncSession,
    room: Optional[RoomModel] = None,
    start_time: datetime = datetime(2021, 10, 9, hour=13, tzinfo=timezone.utc),
    end_time: Optional[datetime] = None,
    status: TaskStatus = TaskStatus.IN_PROGRESS,
    reviewer: Optional[User] = None,
    annotator: Optional[User] = None,
    type_id: Optional[uuid.UUID] = None,
) -> AnnotationTask:
    task_room = room if room is not None else harness.room_0
    if type_id is not None:
        task_type = await harness.annotation_task_store.get_annotation_task_type(
            session=async_session,
            id=type_id,
        )
    else:
        task_type = harness.surgery_task_type

    return await harness.annotation_task_store.create_task(
        session=async_session,
        annotation_task=AnnotationTask(
            org_id=task_room.org_id,
            site_id=task_room.site_id,
            room_id=task_room.id,
            start_time=start_time,
            end_time=(start_time + timedelta(hours=1) if end_time is None else end_time),
            status=status,
            annotator_user_id=(annotator.user_id if annotator is not None else None),
            reviewer_user_id=(reviewer.user_id if reviewer is not None else None),
            type_id=task_type.id,
            type_version=task_type.version,
        ),
    )
