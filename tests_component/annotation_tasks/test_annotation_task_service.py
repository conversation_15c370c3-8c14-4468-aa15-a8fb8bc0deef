import uuid
from datetime import datetime, timedelta, timezone
from typing import Optional
from unittest.mock import MagicMock

from api_server.services.media.media_asset_service_client import MediaAssetService
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from api_server.services.annotation_tasks.annotation_task_models import (
    CancelledReason,
    ReviewReason,
    TaskQuery,
    TaskStatus,
    TaskTypeUpdate,
)
from api_server.services.annotation_tasks.annotation_task_service import (
    AnnotationTaskService,
    random as annotation_task_service_random,
)
from api_server.services.annotation_tasks.annotation_task_store import (
    AnnotationTask,
    AnnotationTaskTypeModel,
)
from unittest.mock import patch, AsyncMock
from annotation_task_optimizer.annotation_task_optimizer import AnnotationTaskOptimizer
from annotation_task_optimizer import Annotation<PERSON>eedsResult, ResultStatus
from api_server.services.events.event_pubsub_store import EventPubSubStore
from api_server.services.events.event_service import EventService
from api_server.services.events.event_store import EventModel
from api_server.services.events.source_type import HUMAN_GROUND_TRUTH, PREDICTION
from api_server.services.objects.objects_service import ObjectsService
from api_server.services.organization.organization_service import OrganizationService
from api_server.services.room.room_service import RoomService
from api_server.services.site.site_service import SiteService
from api_server.services.turnover.turnover_service import TurnoverService
from auth.auth import Auth
from tests_component import harness


@pytest.mark.asyncio
async def test_should_skip_review__task_type_doesnt_allow_skipping(
    annotation_task_service: AnnotationTaskService,
) -> None:
    assert await annotation_task_service.get_review_reasons(harness.annotation_task_0) == {
        ReviewReason.STANDARD,
    }


@pytest.mark.asyncio
async def test_should_skip_review__user_in_provisional_list(
    annotation_task_service: AnnotationTaskService,
    sample_task_type: AnnotationTaskTypeModel,
    sample_task: AnnotationTask,
    async_session: AsyncSession,
) -> None:
    await harness.annotation_task_store.update_annotation_task_type(
        session=async_session,
        task_type_update=TaskTypeUpdate(
            id=sample_task_type.id,
            provisional_annotator_ids=["current_user_id"],
        ),
    )

    task = await harness.annotation_task_store.get_task(
        session=async_session,
        annotation_task_id=sample_task.id,
    )  # refresh task

    assert await annotation_task_service.get_review_reasons(task) == {ReviewReason.PROVISIONAL}


@pytest.mark.asyncio
async def test_should_skip_review__event_needs_review(
    annotation_task_service: AnnotationTaskService,
    sample_task: AnnotationTask,
    async_session: AsyncSession,
) -> None:
    await create_verified_event(
        event_type_id="patient_wheels_in",
        event_time=sample_task.start_time + timedelta(hours=3),
        async_session=async_session,
        labels=["Needs Review"],
    )

    assert await annotation_task_service.get_review_reasons(sample_task) == {
        ReviewReason.REVIEW_REQUESTED,
    }


@pytest.mark.asyncio
async def test_should_skip_review(
    annotation_task_service: AnnotationTaskService,
    sample_task: AnnotationTask,
    random_seed_with_review: None,
    async_session: AsyncSession,
) -> None:
    await create_verified_event(
        event_type_id="patient_wheels_in",
        event_time=sample_task.start_time + timedelta(hours=3),
        async_session=async_session,
    )
    await async_session.commit()
    assert await annotation_task_service.get_review_reasons(sample_task) == {
        ReviewReason.RANDOM_SAMPLE,
    }

    event_with_insignificant_edit = await create_predicted_event(
        "patient_wheels_in",
        sample_task.start_time + timedelta(hours=3),
        async_session,
    )

    event_with_insignificant_edit.start_time = sample_task.start_time + timedelta(
        hours=3,
        minutes=2,  # newer prediction
    )

    event_with_insignificant_edit_list_original = await harness.event_store.upsert_events(
        events=[event_with_insignificant_edit],
        session=async_session,
    )
    event_with_insignificant_edit_list = [
        _copy_event(event) for event in event_with_insignificant_edit_list_original
    ]
    event_with_insignificant_edit = event_with_insignificant_edit_list[0]

    event_with_insignificant_edit.start_time = sample_task.start_time + timedelta(
        hours=3, minutes=1, seconds=30
    )

    await verify_event(event_with_insignificant_edit, async_session)
    # on dev random seed can return < sample rate, set the seed again to guarantee > then sample rate to remove random
    # chance -> 0.5714025946899135.
    annotation_task_service_random.seed(10)
    assert await annotation_task_service.get_review_reasons(sample_task) == set()


@pytest.mark.asyncio
@patch("api_server.services.annotation_tasks.annotation_task_service.OptimizerService")
async def test_bulk_generate__optimize_tasks_annotate(
    optimizer_service: MagicMock,
    annotation_task_service: AnnotationTaskService,
    mock_media_service: MagicMock,
    async_session: AsyncSession,
) -> None:
    mock_annotation_task_optimizer = MagicMock(spec=AnnotationTaskOptimizer)
    mock_annotation_task_optimizer.get_annotation_needs = MagicMock(
        return_value=AnnotationNeedsResult(status=ResultStatus.ANNOTATE, annotation_needs=[])
    )
    optimizer_service.get_instance.return_value = mock_annotation_task_optimizer

    start_time = datetime(2010, 10, day=9, hour=7, minute=0, tzinfo=timezone.utc)
    end_time = datetime(2010, 10, day=10, hour=7, minute=0, tzinfo=timezone.utc)
    mock_media_service.has_media_asset_hls_playlist_video_available = AsyncMock(return_value=True)

    await harness.annotation_task_store.update_annotation_task_type(
        session=async_session,
        task_type_update=TaskTypeUpdate(id=harness.surgery_task_type.id, optimize_tasks=True),
    )

    result = await annotation_task_service.bulk_generate_tasks(
        start_time=start_time, end_time=end_time, task_type_ids=[str(harness.surgery_task_type.id)]
    )

    assert result is True

    tasks = await harness.annotation_task_store.query_tasks(
        session=async_session,
        task_query=TaskQuery(start_time=start_time, end_time=end_time),
    )

    assert len(tasks) > 0
    for task in tasks:
        assert task.status == TaskStatus.NOT_STARTED


@pytest.mark.asyncio
@patch("api_server.services.annotation_tasks.annotation_task_service.OptimizerService")
async def test_bulk_generate__optimize_tasks_skip(
    optimizer_service: MagicMock,
    annotation_task_service: AnnotationTaskService,
    mock_media_service: MagicMock,
    async_session: AsyncSession,
) -> None:
    mock_annotation_task_optimizer = MagicMock(spec=AnnotationTaskOptimizer)
    mock_annotation_task_optimizer.get_annotation_needs = MagicMock(
        return_value=AnnotationNeedsResult(status=ResultStatus.SKIP, annotation_needs=[])
    )
    optimizer_service.get_instance.return_value = mock_annotation_task_optimizer

    start_time = datetime(2010, 10, day=9, hour=7, minute=0, tzinfo=timezone.utc)
    end_time = datetime(2010, 10, day=10, hour=7, minute=0, tzinfo=timezone.utc)
    mock_media_service.has_media_asset_hls_playlist_video_available = AsyncMock(return_value=True)

    await harness.annotation_task_store.update_annotation_task_type(
        session=async_session,
        task_type_update=TaskTypeUpdate(id=harness.surgery_task_type.id, optimize_tasks=True),
    )

    result = await annotation_task_service.bulk_generate_tasks(
        start_time=start_time, end_time=end_time, task_type_ids=[str(harness.surgery_task_type.id)]
    )

    assert result is True

    tasks = await harness.annotation_task_store.query_tasks(
        session=async_session,
        task_query=TaskQuery(start_time=start_time, end_time=end_time),
    )

    assert len(tasks) > 0
    for task in tasks:
        assert task.status == TaskStatus.CANCELLED
        assert task.cancelled_reason == CancelledReason.SKIP


@pytest.mark.asyncio
@patch("api_server.services.annotation_tasks.annotation_task_service.OptimizerService")
async def test_bulk_generate__optimize_tasks_idle(
    optimizer_service: MagicMock,
    annotation_task_service: AnnotationTaskService,
    mock_media_service: MagicMock,
    async_session: AsyncSession,
) -> None:
    mock_annotation_task_optimizer = MagicMock(spec=AnnotationTaskOptimizer)
    mock_annotation_task_optimizer.get_annotation_needs = MagicMock(
        return_value=AnnotationNeedsResult(status=ResultStatus.IDLE, annotation_needs=[])
    )
    optimizer_service.get_instance.return_value = mock_annotation_task_optimizer

    start_time = datetime(2010, 10, day=9, hour=7, minute=0, tzinfo=timezone.utc)
    end_time = datetime(2010, 10, day=10, hour=7, minute=0, tzinfo=timezone.utc)
    mock_media_service.has_media_asset_hls_playlist_video_available = AsyncMock(return_value=True)

    await harness.annotation_task_store.update_annotation_task_type(
        session=async_session,
        task_type_update=TaskTypeUpdate(id=harness.surgery_task_type.id, optimize_tasks=True),
    )

    result = await annotation_task_service.bulk_generate_tasks(
        start_time=start_time, end_time=end_time, task_type_ids=[str(harness.surgery_task_type.id)]
    )

    assert result is True

    tasks = await harness.annotation_task_store.query_tasks(
        session=async_session,
        task_query=TaskQuery(start_time=start_time, end_time=end_time),
    )

    assert len(tasks) > 0
    for task in tasks:
        assert task.status == TaskStatus.CANCELLED
        assert task.cancelled_reason == CancelledReason.IDLE


@pytest.fixture
def mock_auth() -> MagicMock:
    mock_auth = MagicMock(spec=Auth)
    mock_auth.get_calling_user_id = MagicMock(return_value="current_user_id")
    return mock_auth


@pytest.fixture
def site_service(mock_auth: MagicMock) -> SiteService:
    return SiteService(auth=mock_auth, site_store=harness.site_store)


@pytest.fixture
def room_service(mock_auth: MagicMock) -> RoomService:
    return RoomService(
        auth=mock_auth,
        room_store=harness.room_store,
        turnover_service=MagicMock(spec=TurnoverService),
    )


@pytest.fixture
def event_service(mock_auth: MagicMock, site_service: SiteService) -> EventService:
    return EventService(
        auth=mock_auth,
        event_store=harness.event_store,
        site_service=site_service,
        event_pub_sub_store=MagicMock(spec=EventPubSubStore),
        organization_service=MagicMock(spec=OrganizationService),
    )


@pytest.fixture
def mock_media_service() -> MagicMock:
    return MagicMock(spec=MediaAssetService)


@pytest.fixture
def random_seed_without_review() -> None:
    # assuming 15% sample rate
    # first value is 0.8444218515250481
    annotation_task_service_random.seed(0)


@pytest.fixture
def random_seed_with_review() -> None:
    # assuming 15% sample rate
    # first value is 0.01227824739797545
    annotation_task_service_random.seed(31)


@pytest.fixture
def annotation_task_service(
    mock_auth: MagicMock,
    site_service: SiteService,
    room_service: RoomService,
    event_service: EventService,
    mock_media_service: MagicMock,
) -> AnnotationTaskService:
    return AnnotationTaskService(
        auth=mock_auth,
        annotation_task_store=harness.annotation_task_store,
        site_service=site_service,
        room_service=room_service,
        media_service=mock_media_service,
        event_service=event_service,
        object_service=MagicMock(spec=ObjectsService),
    )


@pytest_asyncio.fixture
async def sample_task_type(async_session: AsyncSession) -> AnnotationTaskTypeModel:
    task_type = AnnotationTaskTypeModel()
    task_type.name = "Sample Task Type - patient_wheels_in and patient_wheels_out"
    task_type.description = "Task type used for testing skipping review"
    task_type.allow_skipping_review = True
    task_type.provisional_annotator_ids = []
    task_type.event_types = [
        "patient_wheels_in",
        "patient_wheels_out",
    ]

    return await harness.annotation_task_store.create_annotation_task_type(
        session=async_session,
        annotation_task_type=task_type,
    )


@pytest_asyncio.fixture
async def sample_task(
    sample_task_type: AnnotationTaskTypeModel, async_session: AsyncSession
) -> AnnotationTask:
    task_start_time = datetime(2021, 10, day=9, hour=13, minute=0, tzinfo=timezone.utc)
    await create_verified_event(
        "patient_wheels_in", task_start_time + timedelta(hours=1), async_session
    )
    await create_verified_event(
        "patient_wheels_out", task_start_time + timedelta(hours=2), async_session
    )

    return await harness.annotation_task_store.create_task(
        session=async_session,
        annotation_task=AnnotationTask(
            org_id=harness.org_0.id,
            site_id=harness.site_0.id,
            room_id=harness.room_0.id,
            start_time=task_start_time,
            end_time=task_start_time + timedelta(hours=12),
            status=TaskStatus.IN_PROGRESS,
            annotator_user_id="current_user_id",
            updated_time=datetime(2021, 10, day=11, hour=10, minute=10, tzinfo=timezone.utc),
            type_id=sample_task_type.id,
            type_version=sample_task_type.version,  # type: ignore [attr-defined]
        ),
    )


async def create_event(
    event_type_id: str,
    event_time: datetime,
    source_type: str,
    async_session: AsyncSession,
    confidence: Optional[float] = None,
    labels: list[str] = [],
) -> EventModel:
    event = EventModel()
    event.id = str(uuid.uuid4())
    event.event_type_id = event_type_id
    event.source_type = source_type
    event.source = "component-test"
    event.org_id = harness.org_0.id
    event.site_id = harness.site_0.id
    event.room_id = harness.room_0.id
    event.camera_id = harness.camera_0.id
    event.start_time = event_time
    event.process_timestamp = event.start_time
    event.labels = labels
    if confidence is not None:
        event.confidence = confidence

    return await harness.event_store.create_event(session=async_session, event=event)


def _copy_event(event: EventModel) -> EventModel:
    return EventModel(
        id=event.id,
        event_type_id=event.event_type_id,
        source_type=event.source_type,
        source=event.source,
        org_id=event.org_id,
        site_id=event.site_id,
        room_id=event.room_id,
        camera_id=event.camera_id,
        start_time=event.start_time,
        process_timestamp=event.process_timestamp,
        labels=event.labels,
        confidence=event.confidence,
    )


async def create_predicted_event(
    event_type_id: str,
    event_time: datetime,
    async_session: AsyncSession,
    confidence: Optional[float] = 0.95,
    labels: list[str] = [],
) -> EventModel:
    event = await create_event(
        event_type_id=event_type_id,
        event_time=event_time,
        async_session=async_session,
        source_type=PREDICTION,
        confidence=confidence,
        labels=labels,
    )
    return _copy_event(event)


async def verify_event(
    event: EventModel,
    async_session: AsyncSession,
) -> EventModel:
    event.source_type = HUMAN_GROUND_TRUTH

    result = await harness.event_store.upsert_events(
        session=async_session,
        events=[event],
    )
    return _copy_event(result[0])


async def create_verified_event(
    event_type_id: str,
    event_time: datetime,
    async_session: AsyncSession,
    labels: list[str] = [],
) -> EventModel:
    event = await create_predicted_event(
        event_type_id=event_type_id,
        event_time=event_time,
        async_session=async_session,
        labels=labels,
    )
    return await verify_event(event, async_session)
