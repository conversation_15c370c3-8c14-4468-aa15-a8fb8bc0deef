from datetime import datetime, time, timedelta, timezone
from typing import Any, List, Optional
from uuid import uuid4, UUID
from zoneinfo import ZoneInfo

import pytest
import pytest_asyncio
from sqlalchemy import StaticPool
from sqlalchemy.ext.asyncio import AsyncSession

from api_server.services.annotation_tasks.annotation_task_models import (
    CancelledReason,
    TaskQuery,
    TaskScheduleCreate,
    TaskStatus,
    TaskTypeUpdate,
)
from api_server.services.annotation_tasks.annotation_task_store import (
    AnnotationTask,
    AnnotationTaskSchedule,
    AnnotationTaskTypeModel,
)
from api_server.services.camera.camera_store import CameraModel
from api_server.services.events.event_store import EventModel
from databases.sql import Base, engine_provider
from mocks.mock_big_query.database import Database as MockBigQueryDatabase
from mocks.mock_media_asset_service.database import Database as MockMediaAssetServiceDatabase
from tests_component import harness
from tests_component.conftest import MockService


@pytest.mark.asyncio
async def test_create_annotation_task_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        f"""
    mutation {{
        annotationTaskBulkGenerate {{
            success
        }}
    }}
    """
    )

    assert result["data"] == {"annotationTaskBulkGenerate": None}
    assert result["errors"][0]["path"] == ["annotationTaskBulkGenerate"]
    assert result["errors"][0]["message"] == "Not Authorized: No authorization found"


@pytest_asyncio.fixture
async def delete_existing_tasks() -> None:
    engine = engine_provider(pool_class=StaticPool)
    async with engine.begin() as connection:
        await connection.execute(Base.metadata.tables["annotation_tasks"].delete())
        await connection.commit()


@pytest.mark.asyncio
@pytest.mark.usefixtures("create_media_assets_for_bulk")
async def test_bulk_generate_tasks_default_prev_48_hours(async_session: AsyncSession) -> None:
    # Delete existing tasks
    engine = engine_provider(pool_class=StaticPool)
    async with engine.begin() as connection:
        await connection.execute(Base.metadata.tables["annotation_tasks"].delete())
        await connection.commit()

    result = execute_bulk_generate_mutation()

    assert result == {"data": {"annotationTaskBulkGenerate": {"success": True}}}

    now = datetime.now(tz=timezone.utc)
    start_time = now - timedelta(days=3)
    end_time = now
    task_query = TaskQuery(start_time=start_time, end_time=end_time)

    tasks = await harness.annotation_task_store.query_tasks(
        session=async_session,
        task_query=task_query,
    )
    # we should always generate at least one task and no more than 2 days worth of tasks
    assert len(tasks) > 0
    assert len(tasks) <= 2 * 2  # tasks / day * days


@pytest.mark.asyncio
async def test_bulk_generate__offset_times(
    mock_media_asset_service: MockService[MockMediaAssetServiceDatabase],
    async_session: AsyncSession,
) -> None:
    start_time = datetime(2020, 10, day=9, hour=12, tzinfo=ZoneInfo(harness.site_0.timezone))
    end_time = datetime(2020, 10, day=10, hour=6, tzinfo=ZoneInfo(harness.site_0.timezone))

    # create media assets
    mock_media_asset_service.database.add_playlist(
        harness.org_0.id,
        harness.site_0.id,
        harness.room_0.id,
        harness.camera_0.id,
        start_time,
        end_time,
    )

    await create_annotation_task_schedule(
        session=async_session,
        task_type_id=harness.annotation_task_type_0.id,
        start_hour=6,
        interval=6,
    )

    result = execute_bulk_generate_mutation(start_time=start_time, end_time=end_time)

    assert result == {"data": {"annotationTaskBulkGenerate": {"success": True}}}

    tasks = await harness.annotation_task_store.query_tasks(
        session=async_session,
        task_query=TaskQuery(start_time=start_time, end_time=end_time),
    )
    assert len(tasks) == 4

    # Test idempotence
    result = execute_bulk_generate_mutation(start_time=start_time, end_time=end_time)

    tasks = await harness.annotation_task_store.query_tasks(
        session=async_session,
        task_query=TaskQuery(start_time=start_time, end_time=end_time),
    )
    assert len(tasks) == 4


@pytest.mark.asyncio
async def test_bulk_generate__rooms(
    mock_media_asset_service: MockService[MockMediaAssetServiceDatabase],
    async_session: AsyncSession,
) -> None:
    start_time = datetime(2021, 10, day=9, hour=6, tzinfo=ZoneInfo(harness.site_0.timezone))
    end_time = start_time + timedelta(hours=24)

    # create media assets
    mock_media_asset_service.database.add_playlist(
        harness.org_0.id,
        harness.site_0.id,
        harness.room_0.id,
        harness.camera_0.id,
        start_time,
        end_time,
    )

    await create_annotation_task_schedule(
        session=async_session,
        start_hour=6,
        interval=8,
        task_type_id=harness.annotation_task_type_0.id,
        site_ids=[],
        room_ids=[str(harness.room_0.id)],
    )

    result = execute_bulk_generate_mutation(start_time=start_time, end_time=end_time)

    assert result == {"data": {"annotationTaskBulkGenerate": {"success": True}}}

    tasks = await harness.annotation_task_store.query_tasks(
        session=async_session,
        task_query=TaskQuery(
            start_time=start_time,
            end_time=end_time,
            type_ids=[str(harness.annotation_task_type_0.id)],
        ),
    )
    assert len(tasks) == 3


@pytest.mark.asyncio
@pytest.mark.usefixtures(
    "create_media_assets_for_multitask", "start_time_multitask", "end_time_multitask"
)
async def test_bulk_generate_tasks_multi_task_types(
    start_time_multitask: datetime, end_time_multitask: datetime, async_session: AsyncSession
) -> None:
    await create_annotation_task_schedule(
        session=async_session,
        task_type_id=harness.annotation_task_type_0.id,
        start_hour=start_time_multitask.hour,
    )

    await create_event_generic(
        event_type="patient_wheels_in",
        start_time=start_time_multitask + timedelta(hours=12),
        camera=harness.camera_0,
        async_session=async_session,
    )

    await create_event_generic(
        event_type="patient_wheels_in",
        start_time=start_time_multitask + timedelta(hours=12),
        camera=harness.camera_2,
        async_session=async_session,
    )

    archived_task_type = AnnotationTaskTypeModel()
    archived_task_type.name = "Archived task type"
    archived_task_type.description = "Should not generate tasks for this test"
    archived_task_type.event_types = ["patient_wheels_in", "patient_wheels_out"]
    archived_task_type.archived_time = datetime.now(tz=timezone.utc)
    archived_task_type.priority = 3
    archived_task_type = await harness.annotation_task_store.create_annotation_task_type(
        session=async_session,
        annotation_task_type=archived_task_type,
    )
    await create_annotation_task_schedule(
        session=async_session,
        task_type_id=archived_task_type.id,
        start_hour=start_time_multitask.hour,
    )

    result = execute_bulk_generate_mutation(start_time_multitask, end_time_multitask)

    assert result == {"data": {"annotationTaskBulkGenerate": {"success": True}}}

    task_query = TaskQuery(start_time=start_time_multitask, end_time=end_time_multitask)
    tasks = await harness.annotation_task_store.query_tasks(
        session=async_session,
        task_query=task_query,
    )

    assert len(tasks) == 2

    task_type_0_tasks = [t for t in tasks if t.type_id == harness.annotation_task_type_0.id]
    other_tasks = [t for t in tasks if t.type_id != harness.annotation_task_type_0.id]
    expected_task_start_times = [time(6), time(start_time_multitask.hour)]
    assert_task_fields(other_tasks[0], expected_task_start_times, type_name="Surgery")
    assert_task_fields(
        task_type_0_tasks[0],
        expected_task_start_times,
        type_name=harness.annotation_task_type_0.name,
    )


@pytest.mark.asyncio
@pytest.mark.usefixtures(
    "create_media_assets_for_multitask", "start_time_multitask", "end_time_multitask"
)
async def test_bulk_generate_tasks__filter_task_type_ids(
    start_time_multitask: datetime,
    end_time_multitask: datetime,
    async_session: AsyncSession,
) -> None:
    await create_annotation_task_schedule(
        session=async_session,
        task_type_id=harness.annotation_task_type_0.id,
        start_hour=start_time_multitask.hour,
    )

    result = execute_bulk_generate_mutation(
        start_time=start_time_multitask,
        end_time=end_time_multitask,
        task_type_ids=f'["{harness.annotation_task_type_0.id}"]',
    )

    assert result == {"data": {"annotationTaskBulkGenerate": {"success": True}}}

    task_query = TaskQuery(start_time=start_time_multitask, end_time=end_time_multitask)
    tasks = await harness.annotation_task_store.query_tasks(
        session=async_session,
        task_query=task_query,
    )

    assert len(tasks) == 1

    task_type_0_tasks = [t for t in tasks if t.type_id == harness.annotation_task_type_0.id]
    expected_task_start_times = [time(6), time(start_time_multitask.hour)]
    assert_task_fields(
        task_type_0_tasks[0],
        expected_task_start_times,
        type_name=harness.annotation_task_type_0.name,
    )


@pytest.mark.asyncio
async def test_bulk_generate_with_no_media(async_session: AsyncSession) -> None:
    start_date = datetime(2000, 1, day=1)
    end_date = start_date + timedelta(days=1)

    result = execute_bulk_generate_mutation(start_date, end_date)

    assert result == {"data": {"annotationTaskBulkGenerate": {"success": True}}}
    task_query = TaskQuery(
        start_time=start_date - timedelta(days=1), end_time=end_date + timedelta(days=1)
    )
    tasks = await harness.annotation_task_store.query_tasks(
        session=async_session,
        task_query=task_query,
    )
    assert len(tasks) == 0


@pytest.mark.asyncio
async def test_bulk_generate_not_idle_task_type_doesnt_detect_idle(
    mock_big_query: MockService[MockBigQueryDatabase],
    mock_media_asset_service: MockService[MockMediaAssetServiceDatabase],
    async_session: AsyncSession,
) -> None:
    start_time = datetime(2010, 10, day=9, hour=7, minute=0, tzinfo=timezone.utc)
    end_time = datetime(2010, 10, day=10, hour=7, minute=0, tzinfo=timezone.utc)
    mock_big_query.database.set_data(
        [
            {
                "bucket": start_time,
                "mops": 0.0,
                "media_availability": 1.0,
                "occupancy": 0.0,
                "unscrubbed": 0.0,
                "scrubbed": 0.0,
            }
        ]
    )

    await harness.annotation_task_store.update_annotation_task_type(
        session=async_session,
        task_type_update=TaskTypeUpdate(id=harness.surgery_task_type.id, detect_idle=False),
    )

    mock_media_asset_service.database.add_playlist(
        harness.org_0.id,
        harness.site_0.id,
        harness.room_0.id,
        harness.camera_0.id,
        start_time,
        end_time,
    )

    result = execute_bulk_generate_mutation(start_time, end_time)

    assert result == {"data": {"annotationTaskBulkGenerate": {"success": True}}}

    tasks = await harness.annotation_task_store.query_tasks(
        session=async_session,
        task_query=TaskQuery(start_time=start_time, end_time=end_time),
    )

    assert (len(tasks)) > 0
    for task in tasks:
        assert task.status == TaskStatus.NOT_STARTED


@pytest.mark.asyncio
async def test_bulk_generate_not_idle_with_events(
    mock_big_query: MockService[MockBigQueryDatabase],
    mock_media_asset_service: MockService[MockMediaAssetServiceDatabase],
    async_session: AsyncSession,
) -> None:
    start_time = datetime(2010, 10, day=9, hour=7, minute=0, tzinfo=timezone.utc)
    end_time = datetime(2010, 10, day=10, hour=7, minute=0, tzinfo=timezone.utc)
    mock_big_query.database.set_data(
        [
            {
                "bucket": start_time,
                "mops": 0.0,
                "media_availability": 1.0,
                "occupancy": 0.0,
                "unscrubbed": 0.0,
                "scrubbed": 0.0,
            }
        ]
    )

    await create_event(start_time=start_time.replace(hour=15), async_session=async_session)

    mock_media_asset_service.database.add_playlist(
        harness.org_0.id,
        harness.site_0.id,
        harness.room_0.id,
        harness.camera_0.id,
        start_time,
        end_time,
    )

    result = execute_bulk_generate_mutation(start_time, end_time)

    assert result == {"data": {"annotationTaskBulkGenerate": {"success": True}}}

    tasks = await harness.annotation_task_store.query_tasks(
        session=async_session,
        task_query=TaskQuery(start_time=start_time, end_time=end_time),
    )

    assert (len(tasks)) > 0
    for task in tasks:
        assert task.status == TaskStatus.NOT_STARTED


@pytest.mark.asyncio
async def test_bulk_generate_not_idle_with_context_events(
    mock_big_query: MockService[MockBigQueryDatabase],
    mock_media_asset_service: MockService[MockMediaAssetServiceDatabase],
    async_session: AsyncSession,
) -> None:
    start_time = datetime(2010, 10, day=9, hour=7, minute=0, tzinfo=timezone.utc)
    end_time = start_time + timedelta(hours=24)
    mock_big_query.database.set_data(
        [
            {
                "bucket": start_time,
                "mops": 0.0,
                "media_availability": 1.0,
                "occupancy": 0.0,
                "unscrubbed": 0.0,
                "scrubbed": 0.0,
            }
        ]
    )

    await harness.annotation_task_store.update_annotation_task_type(
        session=async_session,
        task_type_update=TaskTypeUpdate(
            id=harness.surgery_task_type.id,
            context_event_types=["lights_on", "lights_off"],
        ),
    )

    await create_event_generic(
        event_type="lights_on", start_time=start_time.replace(hour=15), async_session=async_session
    )

    mock_media_asset_service.database.add_playlist(
        harness.org_0.id,
        harness.site_0.id,
        harness.room_0.id,
        harness.camera_0.id,
        start_time,
        end_time,
    )

    result = execute_bulk_generate_mutation(start_time, end_time)

    assert result == {"data": {"annotationTaskBulkGenerate": {"success": True}}}

    tasks = await harness.annotation_task_store.query_tasks(
        session=async_session,
        task_query=TaskQuery(start_time=start_time, end_time=end_time),
    )

    assert (len(tasks)) > 0
    for task in tasks:
        assert task.status == TaskStatus.NOT_STARTED


@pytest.mark.asyncio
async def test_bulk_generate_idle_with_no_task_events(
    mock_big_query: MockService[MockBigQueryDatabase],
    mock_media_asset_service: MockService[MockMediaAssetServiceDatabase],
    async_session: AsyncSession,
) -> None:
    start_time = datetime(2010, 10, day=9, hour=7, minute=0, tzinfo=timezone.utc)
    end_time = datetime(2010, 10, day=10, hour=7, minute=0, tzinfo=timezone.utc)
    mock_big_query.database.set_data(
        [
            {
                "bucket": start_time,
                "mops": 0.0,
                "media_availability": 1.0,
                "occupancy": 0.0,
                "unscrubbed": 0.0,
                "scrubbed": 0.0,
            }
        ]
    )

    await create_non_task_event(start_time=start_time.replace(hour=15), async_session=async_session)

    mock_media_asset_service.database.add_playlist(
        harness.org_0.id,
        harness.site_0.id,
        harness.room_0.id,
        harness.camera_0.id,
        start_time,
        end_time,
    )

    result = execute_bulk_generate_mutation(start_time, end_time)

    assert result == {"data": {"annotationTaskBulkGenerate": {"success": True}}}

    tasks = await harness.annotation_task_store.query_tasks(
        session=async_session,
        task_query=TaskQuery(start_time=start_time, end_time=end_time),
    )

    assert (len(tasks)) > 0
    for task in tasks:
        assert task.status == TaskStatus.CANCELLED
        assert task.cancelled_reason == CancelledReason.IDLE


@pytest.mark.asyncio
async def test_bulk_generate_not_idle_with_missing_media(
    mock_big_query: MockService[MockBigQueryDatabase],
    mock_media_asset_service: MockService[MockMediaAssetServiceDatabase],
    async_session: AsyncSession,
) -> None:
    start_time = datetime(2010, 10, day=9, hour=7, minute=0, tzinfo=timezone.utc)
    end_time = datetime(2010, 10, day=10, hour=7, minute=0, tzinfo=timezone.utc)
    mock_big_query.database.set_data(
        [
            {
                "bucket": start_time,
                "mops": 0.0,
                "media_availability": 0.4,
                "occupancy": 0.0,
                "unscrubbed": 0.0,
                "scrubbed": 0.0,
            }
        ]
    )

    mock_media_asset_service.database.add_playlist(
        harness.org_0.id,
        harness.site_0.id,
        harness.room_0.id,
        harness.camera_0.id,
        start_time,
        end_time,
    )

    result = execute_bulk_generate_mutation(start_time, end_time)

    assert result == {"data": {"annotationTaskBulkGenerate": {"success": True}}}

    tasks = await harness.annotation_task_store.query_tasks(
        session=async_session,
        task_query=TaskQuery(start_time=start_time, end_time=end_time),
    )

    assert (len(tasks)) > 0
    for task in tasks:
        assert task.status == TaskStatus.NOT_STARTED


def assert_task_fields(
    task: AnnotationTask, task_start_times: list[time], type_name: str = "Surgery"
) -> None:
    pst_timezone = ZoneInfo("America/Los_Angeles")
    start_hour = task.start_time.astimezone(pst_timezone).time()
    expected_end_time = (task.start_time.astimezone(pst_timezone) + timedelta(hours=12)).time()
    assert start_hour in task_start_times
    assert expected_end_time == task.end_time.astimezone(pst_timezone).time()
    assert task.end_time - task.start_time > timedelta()

    # Assert other fields
    assert task.annotator_user_id is None
    assert task.reviewer_user_id is None
    assert task.task_type.name == type_name
    assert task.status == TaskStatus.NOT_STARTED


def assert_tasks_fields(tasks: List[AnnotationTask]) -> None:
    for task in tasks:
        # Assert times
        pst_timezone = ZoneInfo("America/Los_Angeles")
        start_hour = task.start_time.astimezone(pst_timezone).time()
        task_start_times = [time(6), time(18)]
        assert start_hour in task_start_times
        assert (
            task.end_time.astimezone(pst_timezone).time()
            == task_start_times[(task_start_times.index(start_hour) + 1) % len(task_start_times)]
        )
        assert task.end_time - task.start_time > timedelta()

        # Assert other fields
        assert task.annotator_user_id is None
        assert task.reviewer_user_id is None
        assert task.task_type.name == "Surgery"
        assert task.status == TaskStatus.NOT_STARTED


@pytest.mark.asyncio
async def test_bulk_generate_tasks_exclude_org_ids(async_session: AsyncSession) -> None:
    result = execute_bulk_generate_mutation()

    assert result == {"data": {"annotationTaskBulkGenerate": {"success": True}}}

    start_time = datetime(2010, 10, day=9, hour=7, minute=0, tzinfo=timezone.utc)
    end_time = datetime(2010, 10, day=10, hour=7, minute=0, tzinfo=timezone.utc)
    task_query = TaskQuery(start_time=start_time, end_time=end_time)
    tasks = await harness.annotation_task_store.query_tasks(
        session=async_session,
        task_query=task_query,
    )

    # All annotation tasks are under the same org. Excluding the org should thus yield no tasks.
    assert len(tasks) == 0


async def create_annotation_task_schedule(
    session: AsyncSession,
    task_type_id: UUID,
    start_hour: int,
    interval: int = 12,
    site_ids: Optional[list[str]] = None,
    room_ids: list[str] = [],
) -> AnnotationTaskSchedule:
    if site_ids is None:
        site_ids = [str(harness.site_0.id)]

    return await harness.annotation_task_store.create_annotation_task_schedule(
        session=session,
        task_schedule_create=TaskScheduleCreate(
            annotation_task_type_id=task_type_id,
            start_time=time(hour=start_hour, minute=0),
            interval=interval,
            site_ids=site_ids,
            room_ids=room_ids,
        ),
    )


def build_schedule(
    task_type_id: UUID, start_hour: int, interval: int = 12
) -> AnnotationTaskSchedule:
    schedule = AnnotationTaskSchedule()
    schedule.start_time = time(hour=start_hour, minute=0)
    schedule.interval = interval
    schedule.sites = [harness.site_0]
    schedule.annotation_task_type_id = task_type_id
    return schedule


############################################################
# Created to support component testing
############################################################
@pytest.fixture()
def start_time_multitask() -> datetime:
    # The hardcoded schedule time is 6AM, and pacific time is -7.
    # Which causes issues with the logic seeking to 6AM tomorrow.
    # So in order to avoid time issues where the test generates random data based on when the test was run
    # we set the hour to be 1 less than 6.
    return (
        datetime(
            year=2010,
            month=10,
            day=9,
            hour=5,
            minute=0,
            second=0,
        )
        - timedelta(hours=48)
    ).astimezone(timezone.utc)


@pytest.fixture()
def end_time_multitask(start_time_multitask: datetime) -> datetime:
    return (start_time_multitask + timedelta(hours=24)).astimezone(timezone.utc)


@pytest.fixture()
def create_media_assets_for_multitask(
    mock_media_asset_service: MockService[MockMediaAssetServiceDatabase],
    start_time_multitask: datetime,
    end_time_multitask: datetime,
) -> None:
    mock_media_asset_service.database.add_playlist(
        harness.org_0.id,
        harness.site_0.id,
        harness.room_0.id,
        harness.camera_0.id,
        start_time_multitask,
        end_time_multitask,
    )


@pytest.fixture()
def create_media_assets_for_bulk(
    mock_media_asset_service: MockService[MockMediaAssetServiceDatabase],
) -> None:
    now = datetime.now(tz=ZoneInfo(harness.site_0.timezone)).replace(
        microsecond=0, second=0, minute=0
    )
    start_time = (now - timedelta(hours=48)).astimezone(timezone.utc)
    end_time = (now + timedelta(hours=1)).astimezone(timezone.utc)
    mock_media_asset_service.database.add_playlist(
        harness.org_0.id,
        harness.site_0.id,
        harness.room_0.id,
        harness.camera_0.id,
        start_time,
        end_time,
    )


def execute_bulk_generate_mutation(
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    task_type_ids: Optional[str] = None,
) -> Any:
    if start_time is None or end_time is None:
        return harness.service_account_client.execute_graphql(
            f"""
            mutation {{
                annotationTaskBulkGenerate {{
                    success
                }}
            }}
            """
        )

    return harness.service_account_client.execute_graphql(
        f"""
        mutation {{
        annotationTaskBulkGenerate(input: {{
            startTime: "{start_time.isoformat()}",
            endTime: "{end_time.isoformat()}",
            {"" if task_type_ids is None else f"taskTypeIds: {task_type_ids}"}
        }}) {{
            success
        }}
        }}
        """
    )


async def create_event_generic(
    event_type: str,
    start_time: datetime,
    async_session: AsyncSession,
    camera: Optional[CameraModel] = None,
) -> EventModel:
    if camera is None:
        camera = harness.camera_0

    event = EventModel()
    event.id = str(uuid4())
    event.event_type_id = event_type
    event.case_id = harness.case_0.case_id
    event.room_id = camera.room_id
    event.camera_id = camera.id
    event.site_id = camera.site_id
    event.org_id = camera.org_id
    event.start_time = start_time
    event.process_timestamp = datetime.now(tz=timezone.utc)
    event.source = "component-tests"
    event.source_type = "human_gt"
    event.labels = ["Test"]
    return await harness.event_store.create_event(session=async_session, event=event)


async def create_event(start_time: datetime, async_session: AsyncSession) -> EventModel:
    return await create_event_generic(
        "patient_wheels_in",
        start_time,
        async_session,
    )


async def create_non_task_event(start_time: datetime, async_session: AsyncSession) -> EventModel:
    return await create_event_generic("no_case_cart", start_time, async_session)
