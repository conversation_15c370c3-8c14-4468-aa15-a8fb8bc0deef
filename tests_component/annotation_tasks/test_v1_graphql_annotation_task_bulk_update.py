from typing import Any, Mapping, Optional

import pytest
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from api_server.services.annotation_tasks.annotation_task_models import (
    CancelledReason,
    TaskStatus,
    TaskUpdate,
)
from databases.sql import Base, engine_provider
from tests_component import harness
from tests_component.annotation_tasks.annotation_test_helper import create_task


@pytest.mark.asyncio
async def test_update_annotation_task_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        f"""
    mutation {{
      annotationTaskBulkUpdate(input: {{
                        taskIds: ["{harness.annotation_task_1.id}"],
                        updateInput: {{
                            status: IN_REVIEW,
                        }}
                        }}) {{
            success
        }}
    }}
    """
    )

    assert result == {
        "data": {"annotationTaskBulkUpdate": None},
        "errors": [
            {
                "path": ["annotationTaskBulkUpdate"],
                "locations": [{"line": 3, "column": 7}],
                "message": "Not Authorized: No authorization found",
            }
        ],
    }


@pytest.mark.asyncio
async def test_bulk_update__base() -> None:
    task_1 = harness.annotation_task_1
    task_2 = harness.annotation_task_2

    result = _execute_bulk_update_by_labeler(
        variables={
            "input": {
                "taskIds": [str(task_1.id), str(task_2.id)],
                "updateInput": {
                    "status": "CANCELLED",
                    "cancelledReason": "IDLE",
                    "annotatorUserId": str(harness.surgeon_user.user_id),
                    "reviewerUserId": str(harness.label_reviewer_user.user_id),
                },
            }
        }
    )

    assert result["data"]["annotationTaskBulkUpdate"]["success"]
    assert result["data"]["annotationTaskBulkUpdate"]["count"] == 2

    for task_id in [task_1.id, task_2.id]:
        task = await harness.annotation_task_store.get_task(annotation_task_id=task_id)
        assert task.status == TaskStatus.CANCELLED
        assert task.cancelled_reason == CancelledReason.IDLE
        assert task.annotator_user_id == harness.surgeon_user.user_id
        assert task.reviewer_user_id == harness.label_reviewer_user.user_id
        assert task.updated_by_user_id == harness.labeler_user.user_id


@pytest.mark.asyncio
async def test_bulk_update__omit_reviewer() -> None:
    task_1 = harness.annotation_task_1
    task_2 = harness.annotation_task_2

    result = _execute_bulk_update_by_labeler(
        variables={
            "input": {
                "taskIds": [str(task_1.id), str(task_2.id)],
                "updateInput": {
                    "status": "CANCELLED",
                },
            }
        }
    )

    assert result["data"]["annotationTaskBulkUpdate"]["success"]
    assert result["data"]["annotationTaskBulkUpdate"]["count"] == 2

    for task_id in [task_1.id, task_2.id]:
        task = await harness.annotation_task_store.get_task(annotation_task_id=task_id)
        assert task.status == TaskStatus.CANCELLED
        assert task.reviewer_user_id == harness.labeler_user.user_id  # unchanged values
        assert task.updated_by_user_id == harness.labeler_user.user_id


@pytest.mark.asyncio
async def test_bulk_update__unset_reviewer() -> None:
    task_1 = harness.annotation_task_1
    task_2 = harness.annotation_task_2

    result = _execute_bulk_update_by_labeler(
        variables={
            "input": {
                "taskIds": [str(task_1.id), str(task_2.id)],
                "updateInput": {
                    "status": "CANCELLED",
                    "reviewerUserId": None,
                },
            }
        }
    )

    assert result["data"]["annotationTaskBulkUpdate"]["success"]
    assert result["data"]["annotationTaskBulkUpdate"]["count"] == 2

    for task_id in [task_1.id, task_2.id]:
        task = await harness.annotation_task_store.get_task(annotation_task_id=task_id)
        assert task.status == TaskStatus.CANCELLED
        assert task.reviewer_user_id is None
        assert task.updated_by_user_id == harness.labeler_user.user_id


@pytest.mark.asyncio
async def test_unset_cancelled_reason() -> None:
    task_1 = await harness.annotation_task_store.update_task(
        task_update=TaskUpdate(
            id=harness.annotation_task_1.id,
            status=TaskStatus.CANCELLED,
            cancelled_reason=CancelledReason.IDLE,
        )
    )
    task_2 = harness.annotation_task_2

    result = _execute_bulk_update_by_labeler(
        variables={
            "input": {
                "taskIds": [str(task_1.id), str(task_2.id)],
                "updateInput": {
                    "status": "CANCELLED",
                    "cancelledReason": None,
                },
            }
        }
    )

    assert result["data"]["annotationTaskBulkUpdate"]["success"]
    assert result["data"]["annotationTaskBulkUpdate"]["count"] == 2

    for task_id in [task_1.id, task_2.id]:
        task = await harness.annotation_task_store.get_task(annotation_task_id=task_id)
        assert task.status == TaskStatus.CANCELLED
        assert task.cancelled_reason is None
        assert task.updated_by_user_id == harness.labeler_user.user_id


@pytest.mark.asyncio
async def test_update_status_clears_cancelled_reason() -> None:
    task_1 = await harness.annotation_task_store.update_task(
        task_update=TaskUpdate(
            id=harness.annotation_task_1.id,
            status=TaskStatus.CANCELLED,
            cancelled_reason=CancelledReason.IDLE,
        )
    )
    task_2 = harness.annotation_task_2

    result = _execute_bulk_update_by_labeler(
        variables={
            "input": {
                "taskIds": [str(task_1.id), str(task_2.id)],
                "updateInput": {
                    "status": "NOT_STARTED",
                },
            }
        }
    )
    assert result["data"]["annotationTaskBulkUpdate"]["success"]
    assert result["data"]["annotationTaskBulkUpdate"]["count"] == 2

    for task_id in [task_1.id, task_2.id]:
        task = await harness.annotation_task_store.get_task(annotation_task_id=task_id)
        assert task.status == TaskStatus.NOT_STARTED
        assert task.cancelled_reason is None
        assert task.updated_by_user_id == harness.labeler_user.user_id


@pytest.mark.asyncio
async def test_update_status_cancelled_reason_non_cancelled_status() -> None:
    task_1 = harness.annotation_task_1
    task_2 = harness.annotation_task_2

    result = _execute_bulk_update_by_labeler(
        variables={
            "input": {
                "taskIds": [str(task_1.id), str(task_2.id)],
                "updateInput": {
                    "cancelledReason": "IDLE",
                },
            }
        }
    )

    assert (
        result["errors"][0]["message"] == "Cannot set a cancelled reason if status is not cancelled"
    )


@pytest.mark.asyncio
async def test_bulk_update_query(async_session: AsyncSession) -> None:
    await delete_existing_tasks()
    site_0_task_1 = await create_task(
        async_session=async_session,
        room=harness.room_0,
        status=TaskStatus.IN_PROGRESS,
        annotator=harness.labeler_user,
    )
    site_0_task_2 = await create_task(
        async_session=async_session,
        room=harness.room_1,
        status=TaskStatus.IN_PROGRESS,
        annotator=harness.labeler_user,
    )
    unaffected_task = await create_task(
        async_session=async_session,
        room=harness.room_2,
        status=TaskStatus.IN_PROGRESS,
        annotator=harness.labeler_user,
    )
    result = _execute_bulk_update_by_labeler(
        variables={
            "input": {
                "queryInput": {"siteId": str(harness.site_0.id)},
                "updateInput": {"status": "NOT_STARTED"},
            }
        }
    )
    assert result["data"]["annotationTaskBulkUpdate"]["success"]
    assert result["data"]["annotationTaskBulkUpdate"]["count"] == 2
    annotation_status = await harness.annotation_task_store.get_task(
        session=async_session,
        annotation_task_id=unaffected_task.id,
    )
    assert annotation_status.status == TaskStatus.IN_PROGRESS
    tasks = [
        await harness.annotation_task_store.get_task(
            session=async_session,
            annotation_task_id=task_id,
        )
        for task_id in [site_0_task_1.id, site_0_task_2.id]
    ]
    for task in tasks:
        assert task.status == TaskStatus.NOT_STARTED
        assert task.cancelled_reason is None
        assert task.updated_by_user_id == harness.labeler_user.user_id


def _execute_bulk_update_by_labeler(variables: Optional[Mapping[str, Any]]) -> Any:
    return harness.labeler_client.execute_graphql(
        _get_bulk_update_graphql(),
        variables=variables,
    )


def _get_bulk_update_graphql() -> str:
    return """
    mutation UpdateTask($input: AnnotationTaskBulkUpdateInput!) {
      annotationTaskBulkUpdate(input: $input) {
            success
            count
        }
    }
    """


async def delete_existing_tasks() -> None:
    engine = engine_provider(pool_class=StaticPool)
    async with engine.begin() as connection:
        await connection.execute(Base.metadata.tables["annotation_tasks"].delete())
        await connection.commit()
