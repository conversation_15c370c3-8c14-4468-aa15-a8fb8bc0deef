from datetime import datetime, timedelta, timezone
from typing import Any, Optional
import uuid

import pytest
import pytest_asyncio
from sqlalchemy import <PERSON><PERSON><PERSON>ool
from sqlalchemy.ext.asyncio import AsyncSession

from api_server.services.annotation_tasks.annotation_task_models import (
    TaskStatus,
    TaskTypeUpdate,
)
from databases.sql import Base, engine_provider
from tests_component import harness
from tests_component.annotation_tasks.annotation_test_helper import create_task


def test_next_annotate_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(next_task_graphql(harness.annotation_task_0.id))

    assert result["data"]["annotationTaskNextAnnotate"] is None
    assert result["errors"][0]["message"] == "Not Authorized: No authorization found"


@pytest.mark.asyncio
async def test_next_annotate__no_next_task_available(async_session: AsyncSession) -> None:
    await delete_existing_tasks()
    current_task = await create_task(
        async_session=async_session,
        room=harness.room_0,
        annotator=harness.label_reviewer_user,
        status=TaskStatus.IN_PROGRESS,
    )

    result = harness.label_reviewer_client.execute_graphql(next_task_graphql(current_task.id))
    assert "data" in result.keys()
    assert result["data"].get("annotationTaskNextAnnotate").get("success")

    assert result["data"]["annotationTaskNextAnnotate"]["nextTask"] is None

    current_task_data = result["data"]["annotationTaskNextAnnotate"]["currentTask"]
    assert current_task_data["id"] == str(current_task.id)
    assert current_task_data["status"] == "READY_FOR_REVIEW"


@pytest.mark.asyncio
async def test_next_annotate__skip_review(async_session: AsyncSession) -> None:
    await delete_existing_tasks()
    await harness.annotation_task_store.update_annotation_task_type(
        session=async_session,
        task_type_update=TaskTypeUpdate(
            id=harness.annotation_task_type_0.id, allow_skipping_review=True
        ),
    )

    current_task = await create_task(
        async_session=async_session,
        type_id=harness.annotation_task_type_0.id,
        room=harness.room_0,
        annotator=harness.label_reviewer_user,
        status=TaskStatus.IN_PROGRESS,
    )

    result = harness.label_reviewer_client.execute_graphql(next_task_graphql(current_task.id))

    assert result["data"]["annotationTaskNextAnnotate"]["success"]

    assert result["data"]["annotationTaskNextAnnotate"]["nextTask"] is None

    current_task_data = result["data"]["annotationTaskNextAnnotate"]["currentTask"]
    assert current_task_data["id"] == str(current_task.id)
    assert current_task_data["status"] == "DONE" or (
        current_task_data["status"] == "READY_FOR_REVIEW"
        and (
            await harness.annotation_task_store.get_task(annotation_task_id=current_task.id)
        ).review_reasons
        == ["ReviewReason.RANDOM_SAMPLE"]
    )


@pytest.mark.asyncio
async def test_next_annotate__dont_change_status_dont_return_current_task(
    async_session: AsyncSession,
) -> None:
    await delete_existing_tasks()
    current_task = await create_task(
        async_session=async_session,
        room=harness.room_0,
        annotator=harness.label_reviewer_user,
        status=TaskStatus.IN_PROGRESS,
    )

    result = harness.label_reviewer_client.execute_graphql(
        next_task_graphql(task_id=current_task.id, status=None)
    )

    assert result["data"]["annotationTaskNextAnnotate"]["success"]

    assert result["data"]["annotationTaskNextAnnotate"]["nextTask"] is None

    current_task_data = result["data"]["annotationTaskNextAnnotate"]["currentTask"]
    assert current_task_data["id"] == str(current_task.id)
    assert current_task_data["status"] == "IN_PROGRESS"


@pytest.mark.asyncio
async def test_next_annotate__no_current_task(async_session: AsyncSession) -> None:
    await delete_existing_tasks()
    next_task = await create_task(
        async_session=async_session,
        room=harness.room_1,
        status=TaskStatus.NOT_STARTED,
    )

    result = harness.label_reviewer_client.execute_graphql(next_task_graphql())

    assert result["data"]["annotationTaskNextAnnotate"]["success"]

    assert result["data"]["annotationTaskNextAnnotate"]["currentTask"] is None

    next_task_data = result["data"]["annotationTaskNextAnnotate"]["nextTask"]
    assert next_task_data["id"] == str(next_task.id)
    assert next_task_data["status"] == "IN_PROGRESS"


@pytest.mark.asyncio
async def test_next_annotate__picks_oldest_assigned_annotate_task(
    async_session: AsyncSession,
) -> None:
    await delete_existing_tasks()
    current_task = await create_task(
        async_session=async_session,
        room=harness.room_0,
        annotator=harness.label_reviewer_user,
        status=TaskStatus.IN_PROGRESS,
    )

    next_task = await create_task(
        async_session=async_session,
        room=harness.room_1,
        annotator=harness.label_reviewer_user,
        status=TaskStatus.IN_PROGRESS,
    )

    await create_task(  # blocked task, ignore
        async_session=async_session,
        start_time=current_task.start_time - timedelta(hours=1),
        room=harness.room_1,
        annotator=harness.label_reviewer_user,
        status=TaskStatus.BLOCKED,
    )

    await create_task(  # newer assigned annotate task, lower priority
        async_session=async_session,
        start_time=current_task.start_time + timedelta(hours=1),
        room=harness.room_1,
        annotator=harness.label_reviewer_user,
        status=TaskStatus.IN_PROGRESS,
    )

    await create_task(  # unassigned annotate task, lower priority
        async_session=async_session,
        start_time=current_task.start_time - timedelta(hours=1),
        room=harness.room_2,
        status=TaskStatus.NOT_STARTED,
    )

    result = harness.label_reviewer_client.execute_graphql(next_task_graphql(current_task.id))

    assert_annotate_tasks(str(next_task.id), str(current_task.id), result)


@pytest.mark.asyncio
async def test_next_annotate__picks_highest_priority_unassgined_task(
    async_session: AsyncSession,
) -> None:
    await delete_existing_tasks()
    current_task = await create_task(
        async_session=async_session,
        room=harness.room_0,
        annotator=harness.label_reviewer_user,
        status=TaskStatus.IN_PROGRESS,
    )

    next_task = await create_task(
        async_session=async_session,
        room=harness.room_1,
        status=TaskStatus.NOT_STARTED,
        type_id=harness.terminal_clean_task_type.id,
    )

    await create_task(  # older task w/ lower priority type, lower priority
        async_session=async_session,
        start_time=datetime(2021, 10, 9, hour=12, tzinfo=timezone.utc),
        room=harness.room_2,
        status=TaskStatus.NOT_STARTED,
    )

    result = harness.label_reviewer_client.execute_graphql(next_task_graphql(current_task.id))

    assert_annotate_tasks(str(next_task.id), str(current_task.id), result)


@pytest.mark.asyncio
@pytest.mark.usefixtures("remove_users_from_terminal_cleans")
async def test_next_annotate__picks_oldest_unassigned_annotate_task(
    async_session: AsyncSession,
) -> None:
    await delete_existing_tasks()
    current_task = await create_task(
        async_session=async_session,
        room=harness.room_0,
        annotator=harness.label_reviewer_user,
        status=TaskStatus.IN_PROGRESS,
    )

    next_task = await create_task(
        async_session=async_session, room=harness.room_1, status=TaskStatus.NOT_STARTED
    )

    await create_task(  # newer unassigned annotate task, lower priority
        async_session=async_session,
        start_time=datetime(2021, 10, 9, hour=14, tzinfo=timezone.utc),
        room=harness.room_2,
        status=TaskStatus.NOT_STARTED,
    )

    await create_task(  # older but user isn't assigned to this task type, skip
        async_session=async_session,
        start_time=datetime(2021, 10, 9, hour=11, tzinfo=timezone.utc),
        room=harness.room_1,
        status=TaskStatus.NOT_STARTED,
        type_id=harness.terminal_clean_task_type.id,
    )

    result = harness.label_reviewer_client.execute_graphql(next_task_graphql(current_task.id))

    assert_annotate_tasks(str(next_task.id), str(current_task.id), result)


def assert_annotate_tasks(next_task_id: str, current_task_id: str, result: dict[str, Any]) -> None:
    data_annotation_task_next_annotate = result.get("data", {}).get("annotationTaskNextAnnotate")
    assert data_annotation_task_next_annotate is not None
    assert data_annotation_task_next_annotate.get("success") is True

    next_task_data = data_annotation_task_next_annotate.get("nextTask", {})
    assert next_task_data["id"] == str(next_task_id)
    assert next_task_data["status"] == "IN_PROGRESS"

    current_task_data = data_annotation_task_next_annotate.get("currentTask", {})
    assert current_task_data["id"] == str(current_task_id)
    assert current_task_data["status"] == "READY_FOR_REVIEW"


def next_task_graphql(
    task_id: Optional[uuid.UUID] = None, status: Optional[str] = "READY_FOR_REVIEW"
) -> str:
    return f"""
    mutation {{
      annotationTaskNextAnnotate(input: {{
            {"" if task_id is None else f'currentTaskId: "{task_id}"'}
            {"" if status is None else f", status: {status}"}
        }}) {{
            success
            currentTask {{
                id
                status
                cancelledReason
            }}
            nextTask {{
                id
                status
            }}
        }}
    }}
    """


@pytest_asyncio.fixture
async def remove_users_from_terminal_cleans(async_session: AsyncSession) -> None:
    await harness.annotation_task_store.update_annotation_task_type(
        session=async_session,
        task_type_update=TaskTypeUpdate(id=harness.terminal_clean_task_type.id, annotator_ids=[]),
    )


async def delete_existing_tasks() -> None:
    engine = engine_provider(pool_class=StaticPool)
    async with engine.begin() as connection:
        await connection.execute(Base.metadata.tables["annotation_tasks"].delete())
        await connection.commit()
