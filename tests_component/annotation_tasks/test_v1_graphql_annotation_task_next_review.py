from datetime import datetime, timedelta, timezone
from typing import Any, Optional
import uuid

import pytest
import pytest_asyncio
from sqlalchemy import <PERSON><PERSON><PERSON>ool
from sqlalchemy.ext.asyncio import AsyncSession

from api_server.services.annotation_tasks.annotation_task_models import (
    TaskStatus,
    TaskTypeUpdate,
)
from databases.sql import Base, engine_provider
from tests_component import harness
from tests_component.annotation_tasks.annotation_test_helper import create_task


def test_next_review_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(next_task_graphql(harness.annotation_task_0.id))

    assert result["data"]["annotationTaskNextReview"] is None
    assert result["errors"][0]["message"] == "Not Authorized: No authorization found"


@pytest.mark.asyncio
async def test_next_review__no_next_task_available(async_session: AsyncSession) -> None:
    await delete_existing_tasks()
    current_task = await create_task(
        async_session=async_session,
        room=harness.room_0,
        reviewer=harness.label_reviewer_user,
        status=TaskStatus.IN_REVIEW,
    )

    result = harness.label_reviewer_client.execute_graphql(next_task_graphql(current_task.id))

    assert result["data"]["annotationTaskNextReview"]["success"]

    assert result["data"]["annotationTaskNextReview"]["nextTask"] is None

    current_task_data = result["data"]["annotationTaskNextReview"]["currentTask"]
    assert current_task_data["id"] == str(current_task.id)
    assert current_task_data["status"] == "DONE"


@pytest.mark.asyncio
async def test_next_review__dont_change_status_dont_return_current_task(
    async_session: AsyncSession,
) -> None:
    await delete_existing_tasks()
    current_task = await create_task(
        async_session=async_session,
        room=harness.room_0,
        reviewer=harness.label_reviewer_user,
        status=TaskStatus.IN_REVIEW,
    )

    result = harness.label_reviewer_client.execute_graphql(
        next_task_graphql(task_id=current_task.id, status=None)
    )

    assert result["data"]["annotationTaskNextReview"]["success"]

    assert result["data"]["annotationTaskNextReview"]["nextTask"] is None

    current_task_data = result["data"]["annotationTaskNextReview"]["currentTask"]
    assert current_task_data["id"] == str(current_task.id)
    assert current_task_data["status"] == "IN_REVIEW"


@pytest.mark.asyncio
async def test_next_review__no_current_task(async_session: AsyncSession) -> None:
    await delete_existing_tasks()
    next_task = await create_task(
        async_session=async_session,
        room=harness.room_1,
        annotator=harness.labeler_user,
        status=TaskStatus.READY_FOR_REVIEW,
    )

    result = harness.label_reviewer_client.execute_graphql(next_task_graphql())

    assert result["data"]["annotationTaskNextReview"]["success"]

    assert result["data"]["annotationTaskNextReview"]["currentTask"] is None

    next_task_data = result["data"]["annotationTaskNextReview"]["nextTask"]
    assert next_task_data["id"] == str(next_task.id)
    assert next_task_data["status"] == "IN_REVIEW"


@pytest.mark.asyncio
async def test_next_review__picks_oldest_assigned_review_task(async_session: AsyncSession) -> None:
    await delete_existing_tasks()
    current_task = await create_task(
        async_session=async_session,
        room=harness.room_0,
        reviewer=harness.label_reviewer_user,
        status=TaskStatus.IN_REVIEW,
    )

    next_task = await create_task(
        async_session=async_session,
        room=harness.room_1,
        reviewer=harness.label_reviewer_user,
        status=TaskStatus.IN_REVIEW,
    )

    await create_task(  # blocked task, ignore
        async_session=async_session,
        start_time=current_task.start_time - timedelta(hours=2),
        room=harness.room_2,
        reviewer=harness.label_reviewer_user,
        status=TaskStatus.BLOCKED,
    )

    await create_task(  # current user shouldn't review a task they annotated themselves, skip
        async_session=async_session,
        start_time=current_task.start_time - timedelta(hours=2),
        room=harness.room_1,
        annotator=harness.label_reviewer_user,
        status=TaskStatus.READY_FOR_REVIEW,
    )

    await create_task(  # newer assigned review task, lower priority
        async_session=async_session,
        start_time=current_task.start_time + timedelta(hours=1),
        room=harness.room_1,
        reviewer=harness.label_reviewer_user,
        status=TaskStatus.READY_FOR_REVIEW,
    )

    await create_task(  # older unassigned review task, lower priority
        async_session=async_session,
        start_time=current_task.start_time - timedelta(hours=1),
        room=harness.room_1,
        status=TaskStatus.READY_FOR_REVIEW,
    )

    result = harness.label_reviewer_client.execute_graphql(next_task_graphql(current_task.id))

    assert_review_tasks(str(next_task.id), str(current_task.id), result)


@pytest.mark.asyncio
@pytest.mark.usefixtures("remove_users_from_terminal_cleans")
async def test_next_review__picks_oldest_unassigned_review_task(
    async_session: AsyncSession,
) -> None:
    await delete_existing_tasks()
    current_task = await create_task(
        async_session=async_session,
        room=harness.room_0,
        annotator=harness.labeler_user,
        reviewer=harness.label_reviewer_user,
        status=TaskStatus.IN_REVIEW,
    )

    next_task = await create_task(
        async_session=async_session,
        room=harness.room_1,
        annotator=harness.labeler_user,
        status=TaskStatus.READY_FOR_REVIEW,
    )

    await create_task(  # older but user isn't assigned to this task type, skip
        async_session=async_session,
        start_time=datetime(2021, 10, 9, hour=11, tzinfo=timezone.utc),
        room=harness.room_2,
        status=TaskStatus.READY_FOR_REVIEW,
        type_id=harness.terminal_clean_task_type.id,
    )

    await create_task(  # newer unassigned review task, lower priority
        async_session=async_session,
        start_time=datetime(2021, 10, 9, hour=14, tzinfo=timezone.utc),
        room=harness.room_1,
        status=TaskStatus.READY_FOR_REVIEW,
        annotator=harness.labeler_user,
    )

    result = harness.label_reviewer_client.execute_graphql(next_task_graphql(current_task.id))

    assert_review_tasks(str(next_task.id), str(current_task.id), result)


@pytest.mark.asyncio
async def test_next_review__picks_oldest_unassigned_review_task__skip_self_annotated(
    async_session: AsyncSession,
) -> None:
    await delete_existing_tasks()
    current_task = await create_task(
        async_session=async_session,
        room=harness.room_0,
        annotator=harness.labeler_user,
        reviewer=harness.label_reviewer_user,
        status=TaskStatus.IN_REVIEW,
    )

    next_task = await create_task(
        async_session=async_session,
        room=harness.room_1,
        annotator=harness.labeler_user,
        status=TaskStatus.READY_FOR_REVIEW,
    )

    await create_task(  # current user shouldn't review a task they annotated themselves, skip
        async_session=async_session,
        start_time=datetime(2021, 10, 9, hour=11, tzinfo=timezone.utc),
        room=harness.room_1,
        status=TaskStatus.READY_FOR_REVIEW,
        annotator=harness.label_reviewer_user,
    )

    await create_task(  # newer unassigned review task, lower priority
        async_session=async_session,
        start_time=datetime(2021, 10, 9, hour=14, tzinfo=timezone.utc),
        room=harness.room_1,
        status=TaskStatus.READY_FOR_REVIEW,
        annotator=harness.labeler_user,
    )

    result = harness.label_reviewer_client.execute_graphql(next_task_graphql(current_task.id))

    assert_review_tasks(str(next_task.id), str(current_task.id), result)


def assert_review_tasks(next_task_id: str, current_task_id: str, result: dict[str, Any]) -> None:
    assert result["data"]["annotationTaskNextReview"]["success"]

    next_task_data = result["data"]["annotationTaskNextReview"]["nextTask"]
    assert next_task_data["id"] == str(next_task_id)
    assert next_task_data["status"] == "IN_REVIEW"

    current_task_data = result["data"]["annotationTaskNextReview"]["currentTask"]
    assert current_task_data["id"] == str(current_task_id)
    assert current_task_data["status"] == "DONE"


def next_task_graphql(task_id: Optional[uuid.UUID] = None, status: Optional[str] = "DONE") -> str:
    return f"""
    mutation {{
      annotationTaskNextReview(input: {{
            {"" if task_id is None else f'currentTaskId: "{task_id}"'}
            {"" if status is None else f", status: {status}"}
        }}) {{
            success
            currentTask {{
                id
                status
                cancelledReason
            }}
            nextTask {{
                id
                status
            }}
        }}
    }}
    """


@pytest_asyncio.fixture
async def remove_users_from_terminal_cleans(async_session: AsyncSession) -> None:
    await harness.annotation_task_store.update_annotation_task_type(
        session=async_session,
        task_type_update=TaskTypeUpdate(id=harness.terminal_clean_task_type.id, reviewer_ids=[]),
    )


async def delete_existing_tasks() -> None:
    engine = engine_provider(pool_class=StaticPool)
    async with engine.begin() as connection:
        await connection.execute(Base.metadata.tables["annotation_tasks"].delete())
        await connection.commit()
