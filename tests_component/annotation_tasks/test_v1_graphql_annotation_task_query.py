from datetime import datetime, timedelta, timezone
from uuid import uuid4

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from apella_cloud_api import AnnotationTaskQueryInput
from api_server.services.annotation_tasks.annotation_task_models import (
    TaskStatus,
    TaskTypeUpdate,
)
from api_server.services.annotation_tasks.annotation_task_store import AnnotationTask
from api_server.services.camera.camera_store import CameraModel
from api_server.services.events.event_store import EventModel
from auth.permissions import READ_ANY_ANNOTATION_TASK
from tests_component import harness

# midnight in PST to UTC
start_time = datetime(2021, 10, day=9, hour=7, minute=0, tzinfo=timezone.utc)
end_time = datetime(2021, 10, day=10, hour=14, minute=0, tzinfo=timezone.utc)


def test_query_annotation_task_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        f"""
    query {{
        annotationTasks(
            query: {{ startTime: "{start_time.isoformat()}", endTime: "{end_time.isoformat()}" }}
        ) {{
            edges {{
                node {{
                    id
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": None,
        "errors": [
            {
                "path": ["annotationTasks"],
                "locations": [{"line": 3, "column": 9}],
                "message": "Not Authorized: No authorization found",
            }
        ],
    }


def test_query_annotation_task_requires_permissions() -> None:
    result = harness.user_without_permissions_client.execute_graphql(
        f"""
    query {{
        annotationTasks(
            query: {{ startTime: "{start_time.isoformat()}", endTime: "{end_time.isoformat()}" }}
        ) {{
            edges {{
                node {{
                    id
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": None,
        "errors": [
            {
                "path": ["annotationTasks"],
                "locations": [{"line": 3, "column": 9}],
                "message": f"User does not have permission '{READ_ANY_ANNOTATION_TASK}'",
            }
        ],
    }


def test_get_annotation_task_returns_all_fields() -> None:
    result = harness.labeler_client.get_annotation_task(str(harness.annotation_task_0.id))

    assert result.id == str(harness.annotation_task_0.id)
    assert result.start_time == harness.annotation_task_0.start_time
    assert result.end_time == harness.annotation_task_0.end_time
    assert result.status == harness.annotation_task_0.status.name
    assert result.org_id == harness.annotation_task_0.org_id
    assert result.site_id == harness.annotation_task_0.site_id
    assert result.room_id == harness.annotation_task_0.room_id
    assert result.annotator_user_id == harness.annotation_task_0.annotator_user_id
    assert result.reviewer_user_id == harness.annotation_task_0.reviewer_user_id
    assert result.updated_time == harness.annotation_task_0.updated_time


@pytest.mark.asyncio
async def test_get_annotation_task_returns_events(async_session: AsyncSession) -> None:
    start_time = datetime(2022, 10, day=9, hour=13, minute=0, tzinfo=timezone.utc)
    end_time = start_time + timedelta(days=1)

    harness.surgery_task_type = await harness.annotation_task_store.update_annotation_task_type(
        session=async_session,
        task_type_update=TaskTypeUpdate(
            id=harness.surgery_task_type.id, context_event_types=["lights_on"]
        ),
    )
    event = await _create_event(
        start_time=start_time + timedelta(minutes=2),
        event_type_id="patient_wheels_in",
        camera=harness.camera_0,
        async_session=async_session,
    )

    context_event = await _create_event(
        start_time=start_time + timedelta(minutes=2),
        event_type_id="lights_on",
        camera=harness.camera_0,
        async_session=async_session,
    )

    await _create_event(  # excluded event - before task start
        start_time=start_time - timedelta(minutes=2),
        event_type_id="patient_wheels_in",
        camera=harness.camera_0,
        async_session=async_session,
    )

    await _create_event(  # excluded event - after task end
        start_time=end_time + timedelta(minutes=2),
        event_type_id="patient_wheels_in",
        camera=harness.camera_0,
        async_session=async_session,
    )

    await _create_event(  # excluded context event - before task start
        start_time=start_time - timedelta(minutes=2),
        event_type_id="lights_on",
        camera=harness.camera_0,
        async_session=async_session,
    )

    await _create_event(  # excluded context event - after task end
        start_time=end_time + timedelta(minutes=2),
        event_type_id="lights_on",
        camera=harness.camera_0,
        async_session=async_session,
    )

    await _create_event(  # excluded - irrelevant event type
        start_time=start_time + timedelta(minutes=2),
        event_type_id="mop_in",
        camera=harness.camera_0,
        async_session=async_session,
    )

    await _create_event(  # excluded - different room
        start_time=start_time + timedelta(minutes=2),
        event_type_id="patient_wheels_in",
        camera=harness.camera_2,
        async_session=async_session,
    )

    await _create_event(  # excluded context event - different room
        start_time=start_time + timedelta(minutes=2),
        event_type_id="lights_on",
        camera=harness.camera_2,
        async_session=async_session,
    )

    annotation_task = AnnotationTask(
        org_id=harness.org_0.id,
        site_id=harness.site_0.id,
        room_id=harness.room_0.id,
        start_time=start_time,
        end_time=end_time,
        status=TaskStatus.DONE,
        annotator_user_id=harness.labeler_user.user_id,
        reviewer_user_id=harness.labeler_user.user_id,
        updated_time=end_time + timedelta(days=3),
        type_id=harness.surgery_task_type.id,
        type_version=harness.surgery_task_type.version,  # type: ignore [attr-defined]
    )

    await harness.annotation_task_store.create_task(
        session=async_session,
        annotation_task=annotation_task,
    )

    result = harness.labeler_client.get_annotation_task(str(annotation_task.id))

    assert result.id == str(annotation_task.id)

    assert len(result.events) == 1
    assert result.events[0].id == event.id

    assert len(result.context_events) == 1
    assert result.context_events[0].id == context_event.id


def test_query_annotation_tasks_returns_all_fields() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        annotationTasks(
            query: {{ startTime: "{start_time.isoformat()}", endTime: "{end_time.isoformat()}" }}
        ) {{
            edges {{
                node {{
                    id
                    startTime
                    endTime
                    status
                    orgId
                    siteId
                    roomId
                    annotatorUserId
                    reviewerUserId
                    updatedTime
                }}
            }}
        }}
    }}
    """
    )

    assert sorted(
        result["data"]["annotationTasks"]["edges"], key=lambda x: str(x["node"]["id"])
    ) == sorted(
        [
            {
                "node": {
                    "id": str(harness.annotation_task_0.id),
                    "startTime": harness.annotation_task_0.start_time.isoformat(),
                    "endTime": harness.annotation_task_0.end_time.isoformat(),
                    "status": harness.annotation_task_0.status.name,
                    "orgId": harness.annotation_task_0.org_id,
                    "siteId": harness.annotation_task_0.site_id,
                    "roomId": harness.annotation_task_0.room_id,
                    "annotatorUserId": harness.annotation_task_0.annotator_user_id,
                    "reviewerUserId": harness.annotation_task_0.reviewer_user_id,
                    "updatedTime": harness.annotation_task_0.updated_time.isoformat(),
                }
            },
            {
                "node": {
                    "id": str(harness.annotation_task_1.id),
                    "startTime": harness.annotation_task_1.start_time.isoformat(),
                    "endTime": harness.annotation_task_1.end_time.isoformat(),
                    "status": harness.annotation_task_1.status.name,
                    "orgId": harness.annotation_task_1.org_id,
                    "siteId": harness.annotation_task_1.site_id,
                    "roomId": harness.annotation_task_1.room_id,
                    "annotatorUserId": harness.annotation_task_1.annotator_user_id,
                    "reviewerUserId": harness.annotation_task_1.reviewer_user_id,
                    "updatedTime": harness.annotation_task_1.updated_time.isoformat(),
                }
            },
            {
                "node": {
                    "id": str(harness.annotation_task_2.id),
                    "startTime": harness.annotation_task_2.start_time.isoformat(),
                    "endTime": harness.annotation_task_2.end_time.isoformat(),
                    "status": harness.annotation_task_2.status.name,
                    "orgId": harness.annotation_task_2.org_id,
                    "siteId": harness.annotation_task_2.site_id,
                    "roomId": harness.annotation_task_2.room_id,
                    "annotatorUserId": harness.annotation_task_2.annotator_user_id,
                    "reviewerUserId": harness.annotation_task_2.reviewer_user_id,
                    "updatedTime": harness.annotation_task_2.updated_time.isoformat(),
                }
            },
        ],
        key=lambda x: str(x["node"]["id"]),
    )


def test_query_annotation_tasks_by_status() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        annotationTasks(
            query: {{
                startTime: "{start_time.isoformat()}",
                endTime: "{end_time.isoformat()}",
                statuses: [{TaskStatus.DONE.name}, {TaskStatus.IN_PROGRESS.name}]
            }}
        ) {{
            edges {{
                node {{
                    id
                    status
                }}
            }}
        }}
    }}
    """
    )

    assert sorted(
        result["data"]["annotationTasks"]["edges"], key=lambda x: x["node"]["id"]
    ) == sorted(
        [
            {
                "node": {
                    "id": str(harness.annotation_task_0.id),
                    "status": harness.annotation_task_0.status.name,
                },
            },
            {
                "node": {
                    "id": str(harness.annotation_task_1.id),
                    "status": harness.annotation_task_1.status.name,
                },
            },
        ],
        key=lambda x: x["node"]["id"],
    )


def test_query_annotation_tasks_by_annotator() -> None:
    by_user_result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        annotationTasks(
            query: {{
                startTime: "{start_time.isoformat()}",
                endTime: "{end_time.isoformat()}",
                annotatorUserIds: ["{harness.annotation_task_0.annotator_user_id}"]
            }}
            orderBy: {{ sort: "startTime", direction: ASC }}
        ) {{
            edges {{
                node {{
                    id
                }}
            }}
        }}
    }}
    """
    )

    assert sorted(
        by_user_result["data"]["annotationTasks"]["edges"],
        key=lambda x: x["node"]["id"],
    ) == sorted(
        [
            {
                "node": {
                    "id": str(harness.annotation_task_0.id),
                },
            },
            {
                "node": {
                    "id": str(harness.annotation_task_1.id),
                },
            },
        ],
        key=lambda x: x["node"]["id"],
    )

    by_unassigned_result = harness.labeler_client.execute_graphql(
        f"""
    query UnassignedQuery($unassignedQueryInput: AnnotationTaskQueryInput!){{
        annotationTasks(query: $unassignedQueryInput) {{
            edges {{
                node {{
                    id
                }}
            }}
        }}
    }}
    """,
        variables={
            "unassignedQueryInput": {
                "startTime": start_time.isoformat(),
                "endTime": end_time.isoformat(),
                "annotatorUserIds": [None],
            }
        },
    )

    assert by_unassigned_result == {
        "data": {
            "annotationTasks": {
                "edges": [
                    {
                        "node": {
                            "id": str(harness.annotation_task_2.id),
                        },
                    },
                ]
            }
        }
    }

    by_unassigned_and_assigned_result = harness.labeler_client.execute_graphql(
        f"""
    query UnassignedQuery($unassignedQueryInput: AnnotationTaskQueryInput!){{
        annotationTasks(query: $unassignedQueryInput) {{
            edges {{
                node {{
                    id
                }}
            }}
        }}
    }}
    """,
        variables={
            "unassignedQueryInput": {
                "startTime": start_time.isoformat(),
                "endTime": end_time.isoformat(),
                "annotatorUserIds": [harness.annotation_task_0.annotator_user_id, None],
            }
        },
    )

    assert sorted(
        by_unassigned_and_assigned_result["data"]["annotationTasks"]["edges"],
        key=lambda x: x["node"]["id"],
    ) == sorted(
        [
            {
                "node": {
                    "id": str(harness.annotation_task_0.id),
                },
            },
            {
                "node": {
                    "id": str(harness.annotation_task_1.id),
                },
            },
            {
                "node": {
                    "id": str(harness.annotation_task_2.id),
                },
            },
        ],
        key=lambda x: x["node"]["id"],
    )


def test_query_annotation_tasks_by_updated_time() -> None:
    min_updated_time = (harness.annotation_task_0.updated_time - timedelta(hours=1)).isoformat()
    max_updated_time = (harness.annotation_task_0.updated_time + timedelta(hours=1)).isoformat()

    query = AnnotationTaskQueryInput(
        min_updated_time=min_updated_time, max_updated_time=max_updated_time
    )
    result = harness.labeler_client.query_annotation_tasks(query)

    assert len(result) == 1
    assert result[0].id == str(harness.annotation_task_0.id)


def test_query_annotation_tasks_by_organization_id() -> None:
    organization_id = harness.org_0.id
    query = AnnotationTaskQueryInput(organization_id=organization_id)
    result = harness.labeler_client.query_annotation_tasks(query)
    assert len(result) == 11


def test_query_annotation_tasks_by_room_id() -> None:
    room_id = harness.room_0.id
    query = AnnotationTaskQueryInput(room_id=room_id)
    result = harness.labeler_client.query_annotation_tasks(query)
    assert len(result) == 2


def test_query_annotation_tasks_by_site_id() -> None:
    site_id = harness.site_0.id
    query = AnnotationTaskQueryInput(site_id=site_id)
    result = harness.labeler_client.query_annotation_tasks(query)
    assert len(result) == 11


def test_query_annotation_tasks_start_time_before_tasks() -> None:
    min_time = (harness.annotation_task_0.start_time - timedelta(hours=1)).isoformat()
    query = AnnotationTaskQueryInput(start_time=min_time)
    result = harness.labeler_client.query_annotation_tasks(query)

    assert len(result) == 11


def test_query_annotation_tasks_start_time_during_tasks() -> None:
    min_time = (harness.annotation_task_0.end_time - timedelta(hours=1)).isoformat()
    query = AnnotationTaskQueryInput(start_time=min_time)
    result = harness.labeler_client.query_annotation_tasks(query)

    assert len(result) == 11


def test_query_annotation_tasks_end_time_after_tasks() -> None:
    max_time = (harness.annotation_task_0.end_time + timedelta(hours=1)).isoformat()
    query = AnnotationTaskQueryInput(end_time=max_time)
    result = harness.labeler_client.query_annotation_tasks(query)

    assert len(result) == 3


def test_query_annotation_tasks_end_time_during_tasks() -> None:
    max_time = (harness.annotation_task_0.end_time - timedelta(hours=1)).isoformat()
    query = AnnotationTaskQueryInput(end_time=max_time)
    result = harness.labeler_client.query_annotation_tasks(query)

    assert len(result) == 3


def test_query_annotation_tasks_start_end_time_during_tasks() -> None:
    min_time = (harness.annotation_task_0.start_time + timedelta(hours=1)).isoformat()
    max_time = (harness.annotation_task_0.end_time - timedelta(hours=1)).isoformat()
    query = AnnotationTaskQueryInput(start_time=min_time, end_time=max_time)
    result = harness.labeler_client.query_annotation_tasks(query)

    assert len(result) == 3


def test_query_annotation_tasks_task_during_start_end_time() -> None:
    min_time = (harness.annotation_task_0.start_time - timedelta(hours=1)).isoformat()
    max_time = (harness.annotation_task_0.end_time + timedelta(hours=1)).isoformat()
    query = AnnotationTaskQueryInput(start_time=min_time, end_time=max_time)
    result = harness.labeler_client.query_annotation_tasks(query)

    assert len(result) == 3


def test_query_annotation_tasks_task_ends_during_query() -> None:
    min_time = (harness.annotation_task_0.start_time + timedelta(hours=1)).isoformat()
    max_time = (harness.annotation_task_0.end_time + timedelta(hours=1)).isoformat()
    query = AnnotationTaskQueryInput(start_time=min_time, end_time=max_time)
    result = harness.labeler_client.query_annotation_tasks(query)

    assert len(result) == 3


def test_query_annotation_tasks_task_starts_during_query() -> None:
    min_time = (harness.annotation_task_0.start_time - timedelta(hours=1)).isoformat()
    max_time = (harness.annotation_task_0.end_time - timedelta(hours=1)).isoformat()
    query = AnnotationTaskQueryInput(start_time=min_time, end_time=max_time)
    result = harness.labeler_client.query_annotation_tasks(query)

    assert len(result) == 3


def test_query_annotation_tasks_task_starts_at_query_end_returns_none() -> None:
    min_time = (harness.annotation_task_0.start_time - timedelta(hours=1)).isoformat()
    max_time = (harness.annotation_task_0.start_time).isoformat()
    query = AnnotationTaskQueryInput(start_time=min_time, end_time=max_time)
    result = harness.labeler_client.query_annotation_tasks(query)

    assert len(result) == 0


def test_query_annotation_tasks_task_ends_at_query_start_returns_none() -> None:
    min_time = (harness.annotation_task_0.end_time).isoformat()
    max_time = (harness.annotation_task_0.end_time + timedelta(hours=1)).isoformat()
    query = AnnotationTaskQueryInput(start_time=min_time, end_time=max_time)
    result = harness.labeler_client.query_annotation_tasks(query)

    assert len(result) == 0


async def _create_event(
    event_type_id: str, camera: CameraModel, start_time: datetime, async_session: AsyncSession
) -> EventModel:
    event = EventModel()
    event.id = str(uuid4())
    event.event_type_id = event_type_id
    event.source_type = "prediction"
    event.source = "component-test"
    event.org_id = camera.org_id
    event.site_id = camera.site_id
    event.room_id = camera.room_id
    event.camera_id = camera.id
    event.start_time = start_time
    event.process_timestamp = datetime.now()

    return await harness.event_store.create_event(
        session=async_session,
        event=event,
    )
