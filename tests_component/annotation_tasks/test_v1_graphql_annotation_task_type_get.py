import pytest

from tests_component import harness


GET_ANNOTATION_TASK_TYPE = """
  query GetTaskType($id: ID!) {
    annotationTaskType(id: $id) {
      name
      id
      description
      priority
      detectIdle
      allowSkippingReview
      annotatorIds
      provisionalAnnotatorIds
      reviewerIds
      eventTypes
      contextEventTypes
      archivedTime
      schedules {
        id
        startTime
        interval
        rooms {
          id
          name
        }
        sites {
          id
          name
        }
      }
    }
  }
"""


@pytest.mark.asyncio
async def test_get() -> None:
    result = harness.label_reviewer_client.execute_graphql(
        GET_ANNOTATION_TASK_TYPE, {"id": str(harness.surgery_task_type.id)}
    )

    task_type = result["data"]["annotationTaskType"]

    assert task_type["id"] == str(harness.surgery_task_type.id)
    assert task_type["name"] == "Surgery"

    assert len(task_type["schedules"]) == 1
    schedule = task_type["schedules"][0]
    assert schedule["startTime"] == "06:00:00"
    assert schedule["interval"] == 12

    assert len(schedule["sites"]) == 1
    assert schedule["sites"][0]["id"] == str(harness.site_0.id)
