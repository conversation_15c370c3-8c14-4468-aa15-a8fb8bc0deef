import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from api_server.services.annotation_tasks.annotation_task_models import TaskTypeUpdate
from tests_component import harness


ANNOTATION_TASK_TYPE_UPDATE_MUTATION = """
    mutation UpdateTaskType($input: AnnnotationTaskTypeUpdateInput!) {
        annotationTaskTypeUpdate(input: $input) {
            success
            updatedAnnotationTaskType {
                id
                name
                archivedTime
                description
                eventTypes
                contextEventTypes
                priority
                annotatorIds
                provisionalAnnotatorIds
                reviewerIds
                detectIdle
                allowSkippingReview
                optimizeTasks
                schedules {
                    sites { id }
                    rooms { id }
                    startTime
                    interval
                }
            }
        }
    }
"""


@pytest.mark.asyncio
async def test_update_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        ANNOTATION_TASK_TYPE_UPDATE_MUTATION,
        variables={"input": {"id": f"{harness.annotation_task_type_0.id}"}},
    )

    assert result["data"]["annotationTaskTypeUpdate"] is None
    assert len(result["errors"]) == 1

    error = result["errors"][0]
    assert error["path"] == ["annotationTaskTypeUpdate"]
    assert error["message"] == "Not Authorized: No authorization found"


@pytest.mark.asyncio
async def test_update__invalid_name(async_session: AsyncSession) -> None:
    task_type_id = str(harness.annotation_task_type_0.id)

    task_type_before = await harness.annotation_task_store.get_annotation_task_type(
        session=async_session, id=task_type_id
    )
    result = harness.labeler_client.execute_graphql(
        ANNOTATION_TASK_TYPE_UPDATE_MUTATION,
        variables={
            "input": {
                "id": f"{harness.annotation_task_type_0.id}",
                "name": "",
            }
        },
    )

    assert len(result["errors"]) > 0
    task_type_after = await harness.annotation_task_store.get_annotation_task_type(
        session=async_session, id=task_type_id
    )
    assert task_type_before.name == task_type_after.name


@pytest.mark.asyncio
async def test_update(async_session: AsyncSession) -> None:
    result = harness.label_reviewer_client.execute_graphql(
        ANNOTATION_TASK_TYPE_UPDATE_MUTATION,
        variables={
            "input": {
                "id": f"{harness.annotation_task_type_0.id}",
                "name": "Newer, better name",
                "description": "Newer, more helpful description",
                "eventTypes": ["terminal_clean_start", "terminal_clean_end"],
                "contextEventTypes": ["lights_on", "lights_off"],
                "annotatorIds": [f"{harness.labeler_user.user_id}"],
                "provisionalAnnotatorIds": [f"{harness.labeler_user.user_id}"],
                "reviewerIds": [
                    f"{harness.labeler_user.user_id}",
                    f"{harness.label_reviewer_user.user_id}",
                ],
                "detectIdle": True,
                "allowSkippingReview": True,
                "optimizeTasks": True,
                "priority": 2,
            }
        },
    )

    annotationTaskTypeUpdate = result["data"]["annotationTaskTypeUpdate"]
    assert annotationTaskTypeUpdate["success"]

    updatedAnnotationTaskType = annotationTaskTypeUpdate["updatedAnnotationTaskType"]
    assert updatedAnnotationTaskType["id"] == str(harness.annotation_task_type_0.id)
    assert updatedAnnotationTaskType["name"] == "Newer, better name"
    assert updatedAnnotationTaskType["description"] == "Newer, more helpful description"
    assert updatedAnnotationTaskType["priority"] == 2
    assert updatedAnnotationTaskType["detectIdle"] is True
    assert updatedAnnotationTaskType["allowSkippingReview"] is True
    assert updatedAnnotationTaskType["optimizeTasks"] is True
    assert updatedAnnotationTaskType["annotatorIds"] == [harness.labeler_user.user_id]
    assert updatedAnnotationTaskType["provisionalAnnotatorIds"] == [harness.labeler_user.user_id]
    assert updatedAnnotationTaskType["reviewerIds"] == [
        harness.labeler_user.user_id,
        harness.label_reviewer_user.user_id,
    ]
    assert updatedAnnotationTaskType["eventTypes"] == [
        "terminal_clean_start",
        "terminal_clean_end",
    ]
    assert updatedAnnotationTaskType["contextEventTypes"] == [
        "lights_on",
        "lights_off",
    ]

    task_types = await harness.annotation_task_store.get_annotation_task_types(
        session=async_session,
        keys=[harness.annotation_task_type_0.id],
    )

    assert len(task_types) == 1
    assert task_types[0].name == "Newer, better name"


@pytest.mark.asyncio
async def test_update__remove_annotators(async_session: AsyncSession) -> None:
    await harness.annotation_task_store.update_annotation_task_type(
        session=async_session,
        task_type_update=TaskTypeUpdate(
            id=harness.annotation_task_type_0.id,
            annotator_ids=[harness.labeler_user.user_id, harness.label_reviewer_user.user_id],
            provisional_annotator_ids=[
                harness.labeler_user.user_id,
                harness.label_reviewer_user.user_id,
            ],
            reviewer_ids=[harness.labeler_user.user_id, harness.label_reviewer_user.user_id],
        ),
    )

    result = harness.label_reviewer_client.execute_graphql(
        ANNOTATION_TASK_TYPE_UPDATE_MUTATION,
        variables={
            "input": {
                "id": f"{harness.annotation_task_type_0.id}",
                "annotatorIds": [],
                "provisionalAnnotatorIds": [],
                "reviewerIds": [],
            }
        },
    )

    annotationTaskTypeUpdate = result["data"]["annotationTaskTypeUpdate"]
    assert annotationTaskTypeUpdate["success"]

    updatedAnnotationTaskType = annotationTaskTypeUpdate["updatedAnnotationTaskType"]
    assert updatedAnnotationTaskType["id"] == str(harness.annotation_task_type_0.id)
    assert updatedAnnotationTaskType["annotatorIds"] == []
    assert updatedAnnotationTaskType["provisionalAnnotatorIds"] == []
    assert updatedAnnotationTaskType["reviewerIds"] == []


@pytest.mark.asyncio
async def test_update_archive() -> None:
    result = harness.label_reviewer_client.execute_graphql(
        ANNOTATION_TASK_TYPE_UPDATE_MUTATION,
        variables={"input": {"id": f"{harness.annotation_task_type_0.id}", "archived": True}},
    )

    annotationTaskTypeUpdate = result["data"]["annotationTaskTypeUpdate"]
    assert annotationTaskTypeUpdate["success"]
    updatedAnnotationTaskType = annotationTaskTypeUpdate["updatedAnnotationTaskType"]
    assert updatedAnnotationTaskType["archivedTime"] is not None


@pytest.mark.asyncio
async def test_update__unarchive(async_session: AsyncSession) -> None:
    await harness.annotation_task_store.update_annotation_task_type(
        session=async_session,
        task_type_update=TaskTypeUpdate(id=harness.annotation_task_type_0.id, archived=True),
    )

    result = harness.label_reviewer_client.execute_graphql(
        ANNOTATION_TASK_TYPE_UPDATE_MUTATION,
        variables={"input": {"id": f"{harness.annotation_task_type_0.id}", "archived": False}},
    )

    annotationTaskTypeUpdate = result["data"]["annotationTaskTypeUpdate"]
    assert annotationTaskTypeUpdate["success"]
    updatedAnnotationTaskType = annotationTaskTypeUpdate["updatedAnnotationTaskType"]
    assert updatedAnnotationTaskType["archivedTime"] is None


@pytest.mark.asyncio
async def test_update__ignores_omitted_fields(async_session: AsyncSession) -> None:
    await harness.annotation_task_store.update_annotation_task_type(
        session=async_session,
        task_type_update=TaskTypeUpdate(
            id=harness.annotation_task_type_0.id,
            annotator_ids=[harness.labeler_user.user_id],
            provisional_annotator_ids=[harness.labeler_user.user_id],
            reviewer_ids=[harness.labeler_user.user_id],
        ),
    )

    result = harness.label_reviewer_client.execute_graphql(
        ANNOTATION_TASK_TYPE_UPDATE_MUTATION,
        variables={
            "input": {
                "id": f"{harness.annotation_task_type_0.id}",
                "description": "Newer, more helpful description",
            }
        },
    )

    annotationTaskTypeUpdate = result["data"]["annotationTaskTypeUpdate"]
    assert annotationTaskTypeUpdate["success"]

    updatedAnnotationTaskType = annotationTaskTypeUpdate["updatedAnnotationTaskType"]

    assert updatedAnnotationTaskType["id"] == str(harness.annotation_task_type_0.id)
    assert updatedAnnotationTaskType["name"] == "User created task type 0"
    assert updatedAnnotationTaskType["description"] == "Newer, more helpful description"
    assert updatedAnnotationTaskType["eventTypes"] == ["patient_wheels_in", "patient_wheels_out"]
    assert updatedAnnotationTaskType["annotatorIds"] == [harness.labeler_user.user_id]
    assert updatedAnnotationTaskType["provisionalAnnotatorIds"] == [harness.labeler_user.user_id]
    assert updatedAnnotationTaskType["reviewerIds"] == [harness.labeler_user.user_id]
    assert updatedAnnotationTaskType["priority"] == 3
    task_types = await harness.annotation_task_store.get_annotation_task_types(
        session=async_session,
        keys=[harness.annotation_task_type_0.id],
    )
    assert len(task_types) == 1
    assert task_types[0].name == "User created task type 0"
