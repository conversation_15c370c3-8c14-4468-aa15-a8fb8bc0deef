import pytest
from api_server.services.annotation_tasks.annotation_task_models import (
    CancelledReason,
    TaskStatus,
    TaskUpdate,
)
from tests_component import harness


@pytest.mark.asyncio
async def test_update_annotation_task_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        f"""
    mutation {{
      annotationTaskUpdate(input: {{
                        id: "{harness.annotation_task_1.id}",
                        status: IN_REVIEW,
                        }}) {{
            success
        }}
    }}
    """
    )

    assert result == {
        "data": {"annotationTaskUpdate": None},
        "errors": [
            {
                "path": ["annotationTaskUpdate"],
                "locations": [{"line": 3, "column": 7}],
                "message": "Not Authorized: No authorization found",
            }
        ],
    }


@pytest.mark.asyncio
async def test_update_status_annotator_annotation_task() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    mutation {{
      annotationTaskUpdate(input: {{
                        id: "{harness.annotation_task_1.id}",
                        status: CANCELLED,
                        cancelledReason: IDLE,
                        annotatorUserId: "{harness.surgeon_user.user_id}"
                        }}) {{
            success
            updatedAnnotationTask {{
                id
                annotatorUserId
                reviewerUserId
                updatedByUserId
                status
                cancelledReason
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "annotationTaskUpdate": {
                "success": True,
                "updatedAnnotationTask": {
                    "id": str(harness.annotation_task_1.id),
                    "status": "CANCELLED",
                    "cancelledReason": "IDLE",
                    "annotatorUserId": harness.surgeon_user.user_id,
                    "reviewerUserId": harness.labeler_user.user_id,
                    "updatedByUserId": harness.labeler_user.user_id,
                },
            }
        }
    }


@pytest.mark.asyncio
async def test_update_reviewer_annotation_task() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    mutation {{
      annotationTaskUpdate(input: {{
                        id: "{harness.annotation_task_1.id}",
                        reviewerUserId: "{harness.surgeon_user.user_id}"
                        }}) {{
            success
            updatedAnnotationTask {{
                id
                annotatorUserId
                reviewerUserId
                status
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "annotationTaskUpdate": {
                "success": True,
                "updatedAnnotationTask": {
                    "id": str(harness.annotation_task_1.id),
                    "status": harness.annotation_task_1.status.name,
                    "annotatorUserId": harness.labeler_user.user_id,
                    "reviewerUserId": harness.surgeon_user.user_id,
                },
            }
        }
    }


@pytest.mark.asyncio
async def test_unset_reviewer_annotation_task() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    mutation UpdateTask($updateInput: AnnotationTaskUpdateInput!) {{
      annotationTaskUpdate(input: $updateInput) {{
            success
            updatedAnnotationTask {{
                id
                annotatorUserId
                reviewerUserId
                status
            }}
        }}
    }}
    """,
        variables={
            "updateInput": {
                "id": str(harness.annotation_task_1.id),
                "reviewerUserId": None,
            }
        },
    )

    assert result == {
        "data": {
            "annotationTaskUpdate": {
                "success": True,
                "updatedAnnotationTask": {
                    "id": str(harness.annotation_task_1.id),
                    "status": harness.annotation_task_1.status.name,
                    "annotatorUserId": harness.labeler_user.user_id,
                    "reviewerUserId": None,
                },
            }
        }
    }


@pytest.mark.asyncio
async def test_unset_cancelled_reason() -> None:
    await harness.annotation_task_store.update_task(
        task_update=TaskUpdate(
            id=harness.annotation_task_1.id,
            status=TaskStatus.CANCELLED,
            cancelled_reason=CancelledReason.IDLE,
        )
    )

    result = harness.labeler_client.execute_graphql(
        f"""
    mutation UpdateTask($updateInput: AnnotationTaskUpdateInput!) {{
      annotationTaskUpdate(input: $updateInput) {{
            success
            updatedAnnotationTask {{
                id
                status
                cancelledReason
            }}
        }}
    }}
    """,
        variables={
            "updateInput": {
                "id": str(harness.annotation_task_1.id),
                "cancelledReason": None,
            }
        },
    )

    assert result == {
        "data": {
            "annotationTaskUpdate": {
                "success": True,
                "updatedAnnotationTask": {
                    "id": str(harness.annotation_task_1.id),
                    "status": "CANCELLED",
                    "cancelledReason": None,
                },
            }
        }
    }


@pytest.mark.asyncio
async def test_update_status_clears_cancelled_reason() -> None:
    harness.annotation_task_store.update_task(
        TaskUpdate(
            id=harness.annotation_task_1.id,
            status=TaskStatus.CANCELLED,
            cancelled_reason=CancelledReason.IDLE,
        )
    )

    result = harness.labeler_client.execute_graphql(
        f"""
    mutation UpdateTask($updateInput: AnnotationTaskUpdateInput!) {{
      annotationTaskUpdate(input: $updateInput) {{
            success
            updatedAnnotationTask {{
                id
                status
                cancelledReason
            }}
        }}
    }}
    """,
        variables={
            "updateInput": {
                "id": str(harness.annotation_task_1.id),
                "status": "NOT_STARTED",
            }
        },
    )

    assert result == {
        "data": {
            "annotationTaskUpdate": {
                "success": True,
                "updatedAnnotationTask": {
                    "id": str(harness.annotation_task_1.id),
                    "status": "NOT_STARTED",
                    "cancelledReason": None,
                },
            }
        }
    }


@pytest.mark.asyncio
async def test_update_status_cancelled_reason_non_cancelled_status() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    mutation UpdateTask($updateInput: AnnotationTaskUpdateInput!) {{
      annotationTaskUpdate(input: $updateInput) {{
            success
            updatedAnnotationTask {{
                id
                status
                cancelledReason
            }}
        }}
    }}
    """,
        variables={
            "updateInput": {
                "id": str(harness.annotation_task_1.id),
                "cancelledReason": "IDLE",
            }
        },
    )

    assert (
        result["errors"][0]["message"] == "Cannot set a cancelled reason if status is not cancelled"
    )
