import datetime
import uuid
from datetime import timed<PERSON><PERSON>

import pytest

from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_client_schema import GQLCaseStatusName
from apella_cloud_api.new_input_schema import GQLRoom<PERSON>ventSearchInput, GQLApellaCaseQueryInput
from api_server.services.apella_case.apella_case_store import LiveEvent
from api_server.services.events.event_store import EventModel
from api_server.services.events.source_type import HUMAN_GROUND_TRUTH
from api_server.services.phases.phase_store import PhaseModel, PhaseStatus
from tests_component import harness

DEFAULT_TIME = datetime.datetime(
    year=2022, month=1, day=1, hour=0, minute=0, second=0, microsecond=0
)


class TestApellaLivePage:
    def _mock_event(
        self, event_type_id: str, start_time: datetime.datetime, room_id: str
    ) -> EventModel:
        return EventModel(
            id=str(uuid.uuid4()),
            event_type_id=event_type_id,
            start_time=start_time,
            source_type=HUMAN_GROUND_TRUTH,
            source="test",
            site_id=harness.site_0.id,
            room_id=room_id,
            org_id=harness.org_0.id,
            process_timestamp=start_time,
        )

    def _mock_phase(self, room_id: str, start_event_id: str) -> PhaseModel:
        return PhaseModel(
            id=uuid.uuid4(),
            site_id=harness.site_0.id,
            room_id=room_id,
            org_id=harness.org_0.id,
            type_id="CASE",
            start_event_id=start_event_id,
            end_event_id=None,
            case_id=None,
            source_type="unified",
            status=PhaseStatus.VALID,
        )

    # This primarily serves as a test for loaders,
    # if it fails, it's likely that the loaders are not working correctly
    @pytest.mark.asyncio
    async def test_new_event_in_case(self) -> None:
        await harness.event_store.create_event(
            self._mock_event("patient_wheels_in", DEFAULT_TIME, harness.room_0.id)
        )

        apella_schema = ApellaSchema()

        query = apella_schema.Query.sites.args(
            site_ids=[harness.site_0.id],
        ).select(
            apella_schema.SiteConnection.edges.select(
                apella_schema.SiteEdge.node.select(
                    apella_schema.Site.id,
                    apella_schema.Site.rooms.select(
                        apella_schema.RoomConnection.edges.select(
                            apella_schema.RoomEdge.node.select(
                                apella_schema.Room.id,
                                apella_schema.Room.room_events.args(
                                    query=GQLRoomEventSearchInput(
                                        min_start_time=DEFAULT_TIME - timedelta(days=1),
                                        max_start_time=DEFAULT_TIME + timedelta(days=1),
                                        source_types=[HUMAN_GROUND_TRUTH],
                                        event_names=["patient_wheels_in"],
                                    )
                                ).select(
                                    apella_schema.EventConnection.edges.select(
                                        apella_schema.EventEdge.node.select(
                                            apella_schema.Event.id,
                                        )
                                    )
                                ),
                            )
                        )
                    ),
                )
            )
        )
        results_1 = harness.service_account_client.query_graphql_from_schema(query)
        events_1 = [
            edge.node
            for edge in results_1.sites.edges[0].node.rooms.edges[1].node.room_events.edges
        ]
        assert len(events_1) == 1

        await harness.event_store.create_event(
            self._mock_event(
                "patient_wheels_in", DEFAULT_TIME + timedelta(minutes=1), harness.room_0.id
            )
        )

        results_2 = harness.service_account_client.query_graphql_from_schema(query)
        events_2 = [
            edge.node
            for edge in results_2.sites.edges[0].node.rooms.edges[1].node.room_events.edges
        ]
        assert len(events_2) == 2

    @pytest.mark.asyncio
    async def test_apella_case_status_for_live_cases(self) -> None:
        time_now = datetime.datetime.now(tz=datetime.timezone.utc)

        # Case 1 in Room 1 in Prep
        case_1_wheels_in = self._mock_event(
            LiveEvent.PATIENT_WHEELS_IN.value, time_now - timedelta(hours=1), harness.room_1.id
        )
        await harness.event_store.create_event(case_1_wheels_in)
        await harness.phase_store.insert_phase(
            self._mock_phase(harness.room_1.id, case_1_wheels_in.id)
        )

        # Case 2 in Room 2 in Surgery
        case_2_wheels_in = self._mock_event(
            LiveEvent.PATIENT_WHEELS_IN.value, time_now - timedelta(hours=2), harness.room_2.id
        )
        await harness.event_store.create_event(case_2_wheels_in)
        await harness.event_store.create_event(
            self._mock_event(
                LiveEvent.PATIENT_DRAPED.value, time_now - timedelta(hours=1), harness.room_2.id
            )
        )
        await harness.phase_store.insert_phase(
            self._mock_phase(harness.room_2.id, case_2_wheels_in.id)
        )

        # Query for case status
        apella_schema = ApellaSchema()
        query = apella_schema.Query.apella_cases.args(
            query=GQLApellaCaseQueryInput(
                min_end_time=time_now - timedelta(days=1),
                max_start_time=time_now + timedelta(days=1),
                room_ids=[harness.room_1.id, harness.room_2.id],
            )
        ).select(
            apella_schema.ApellaCaseConnection.edges.select(
                apella_schema.ApellaCaseEdge.node.select(
                    apella_schema.ApellaCase.room.select(
                        apella_schema.Room.id,
                    ),
                    apella_schema.ApellaCase.status.select(apella_schema.ApellaCaseStatus.name),
                )
            )
        )
        results = harness.service_account_client.query_graphql_from_schema(query)
        cases = [result.node for result in results.apella_cases.edges]

        # Assert case status
        apella_case_1 = next(filter(lambda case: case.room.id == harness.room_1.id, cases))
        assert apella_case_1.status.name == GQLCaseStatusName.PREP
        apella_case_2 = next(filter(lambda case: case.room.id == harness.room_2.id, cases))
        assert apella_case_2.status.name == GQLCaseStatusName.SURGERY
