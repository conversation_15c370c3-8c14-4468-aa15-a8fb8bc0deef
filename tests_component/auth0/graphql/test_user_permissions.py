from apella_cloud_api.new_api_server_schema import ApellaSchema
from tests_component import harness


class TestUserPermissions:
    def test_read_big_board_permissions(self) -> None:
        apella_schema = ApellaSchema()
        query = apella_schema.Query.user.args(id=harness.labeler_user.user_id).select(
            apella_schema.User.ui_permissions.select(
                apella_schema.UserUiPermissions.big_board_enabled
            )
        )
        result = harness.labeler_client.query_graphql_from_schema(query)
        assert result.user.ui_permissions.big_board_enabled

    def test_read_case_duration_permissions(self) -> None:
        apella_schema = ApellaSchema()
        query = apella_schema.Query.user.args(id=harness.labeler_user.user_id).select(
            apella_schema.User.ui_permissions.select(
                apella_schema.UserUiPermissions.case_duration_enabled
            )
        )
        result = harness.labeler_client.query_graphql_from_schema(query)
        # Labeler user should not have case duration permissions
        assert not result.user.ui_permissions.case_duration_enabled

    def test_write_room_permissions(self) -> None:
        apella_schema = ApellaSchema()
        query = apella_schema.Query.user.args(id=harness.labeler_user.user_id).select(
            apella_schema.User.ui_permissions.select(
                apella_schema.UserUiPermissions.room_write_enabled
            )
        )
        result = harness.labeler_client.query_graphql_from_schema(query)
        # Labeler user should not have case duration permissions
        assert not result.user.ui_permissions.room_write_enabled

    def test_edit_dashboard_schedule_permissions(self) -> None:
        apella_schema = ApellaSchema()
        query = apella_schema.Query.user.args(id=harness.label_reviewer_user.user_id).select(
            apella_schema.User.ui_permissions.select(
                apella_schema.UserUiPermissions.dashboard_schedule_edit_enabled,
            )
        )
        result = harness.label_reviewer_client.query_graphql_from_schema(query)
        # Labeler user should not have case duration permissions
        assert result.user.ui_permissions.dashboard_schedule_edit_enabled

    def test_write_room_configuration_permissions(self) -> None:
        apella_schema = ApellaSchema()
        query = apella_schema.Query.user.args(id=harness.labeler_user.user_id).select(
            apella_schema.User.ui_permissions.select(
                apella_schema.UserUiPermissions.room_write_configuration_enabled
            )
        )
        result = harness.labeler_client.query_graphql_from_schema(query)
        assert not result.user.ui_permissions.room_write_configuration_enabled
