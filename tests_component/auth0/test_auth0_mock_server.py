from typing import Iterator

import pytest
import requests
from unittest.mock import patch, MagicMock
from http import HTTPStatus
from auth import AuthOrganization, AuthUser
from auth import permissions as auth_permissions
from auth.auth0 import Auth0
from mocks.mock_auth0.database import Database as MockAuth0Database
from tests_component import harness
from tests_component.conftest import MockService


@pytest.fixture
def auth0_client(
    api_server: int, mock_auth0_server: MockService[MockAuth0Database]
) -> Iterator[Auth0]:
    yield Auth0(
        auth0_url=mock_auth0_server.url,
        auth_audience=f"http://127.0.0.1:{api_server}",
        auth0_client_id="fake_client_id",
        auth0_client_secret="fake_client_secret",
        auth0_default_domain=mock_auth0_server.url,
    )


def test_get_user_permissions(auth0_client: Auth0) -> None:
    permissions = auth0_client.get_permissions_for_subject(sub=harness.labeler_user.user_id)
    assert permissions == harness.apella_labeler_role.permissions


def test_get_organizations_for_user(auth0_client: Auth0) -> None:
    organizations = auth0_client.get_organizations_for_user(
        auth0_user_id=harness.hospital_administrator_user.user_id
    )
    assert organizations == [
        AuthOrganization(
            id=harness.org_0.auth0_org_id,
            name=harness.org_0.name,
            display_name=harness.org_0.name,
        ),
        AuthOrganization(
            id=harness.org_1.auth0_org_id,
            name=harness.org_1.name,
            display_name=harness.org_1.name,
        ),
    ]


def test_get_users_roles_in_org(auth0_client: Auth0) -> None:
    roles_in_org = auth0_client.get_users_roles_in_org(
        sub=harness.hospital_administrator_user.user_id,
        auth0_org_id=harness.org_0.auth0_org_id,
    )
    assert roles_in_org == [harness.customer_dashboard_user_role.id]


def test_big_board_read_any(auth0_client: Auth0) -> None:
    permissions = auth0_client.get_permissions_for_subject(harness.labeler_user.user_id)
    assert auth_permissions.READ_ANY_BIG_BOARD in permissions


def test_get_permissions_granted_by_role(auth0_client: Auth0) -> None:
    permissions = auth0_client.get_permissions_granted_by_role(
        harness.customer_dashboard_user_role.id
    )
    assert permissions == harness.customer_dashboard_user_role.permissions


def test_get_user_info_for_email(auth0_client: Auth0) -> None:
    user_info: AuthUser = auth0_client.get_user_info_for_email(
        email_address=harness.surgeon_user.email
    )
    assert user_info.email == harness.surgeon_user.email
    assert user_info.display_name == harness.surgeon_user.display_name
    assert user_info.user_id == harness.surgeon_user.user_id


def test_assign_roles_to_user(auth0_client: Auth0) -> None:
    auth0_client.assign_roles_to_user(
        harness.user_without_permissions.user_id,
        [harness.customer_dashboard_user_role.id],
    )
    permissions = auth0_client.get_permissions_for_subject(harness.user_without_permissions.user_id)
    assert permissions == harness.customer_dashboard_user_role.permissions


def test_get_user_info_for_email_retries_on_connection_error(auth0_client: Auth0) -> None:
    with patch(
        "jose.jwt.get_unverified_claims",
        return_value={"email": harness.surgeon_user.email, "exp": 9999999999},
    ):
        with patch("requests.request") as mock_request:
            # Mock the /oauth/token call
            mock_token_response = MagicMock()
            mock_token_response.status_code = HTTPStatus.OK
            mock_token_response.json.return_value = {
                "access_token": "fake_access_token",
            }

            # Mock the /users-by-email call:
            mock_user_response = MagicMock()
            mock_user_response.status_code = HTTPStatus.OK
            mock_user_response.json.return_value = [
                {
                    "user_id": harness.surgeon_user.user_id,
                    "name": harness.surgeon_user.display_name,
                    "email": harness.surgeon_user.email,
                }
            ]

            # First /users-by-email call fails, second succeeds
            mock_request.side_effect = [
                mock_token_response,  # 1st call: /oauth/token
                requests.exceptions.ConnectionError(
                    "connection dropped"
                ),  # 2nd: /users-by-email fail
                mock_user_response,  # 3rd: /users-by-email retry success
            ]

            user_info: AuthUser = auth0_client.get_user_info_for_email(
                email_address=harness.surgeon_user.email
            )
            assert user_info.email == harness.surgeon_user.email
            assert user_info.display_name == harness.surgeon_user.display_name
            assert user_info.user_id == harness.surgeon_user.user_id
            assert mock_request.call_count == 3  # 3 calls made: 1 for token, 2 for user info


def test_get_user_info_for_email_raises_after_max_retries(auth0_client: Auth0) -> None:
    with patch("requests.request") as mock_request:
        mock_request.side_effect = requests.exceptions.ConnectionError("persistent failure")

        with pytest.raises(requests.exceptions.ConnectionError):
            auth0_client.get_user_info_for_email(email_address=harness.surgeon_user.email)

        assert mock_request.call_count >= 2  # retried then failed
