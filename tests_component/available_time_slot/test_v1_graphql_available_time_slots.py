from typing import Any, Optional, TypedDict
from uuid import UUID, uuid4
from zoneinfo import ZoneInfo
from api_server.services.block.block_store import calculate_unblocked_time_id
from api_server.services.case.case_staff_store import PRIMARY_SURGEON_ROLES, CaseStaffModel
from api_server.services.case_forecasts.case_forecast_store import CaseForecastModel
from api_server.services.closures.closure_store import RoomClosure, SiteClosure
from api_server.services.case.case_status import SCHEDULED
from api_server.services.case.case_store import Case
from api_server.services.room.room_store import RoomModel
from api_server.services.site.site_store import Site
from sqlalchemy.ext.asyncio import AsyncSession
from api_server.services.prime_time.dtos import (
    CapacityConstraint,
    DayOfWeekConfig,
    SitePrimeTimeConfigUpdateDto,
)
from api_server.services.prime_time.prime_time_store import (
    RoomPrimeTimeConfig,
    SitePrimeTimeConfig,
)
import isodate
import pytest

from datetime import date, datetime, time, timedelta
from tests_component import harness
from api_server.services.block.block_models import BlockDataInput, BlockTimeModel

GET_AVAILABLE_TIME_SLOTS = """
  query GetAvailableTimeSlots(
    $minAvailableDuration: Duration!
    $startDate: Date!
    $endDate: Date!
    $siteIds: [String!]!
    $surgeonId: String
  ) {
    availableTimeSlots(
      query: {
        minAvailableDuration: $minAvailableDuration
        startDate: $startDate
        endDate: $endDate
        siteIds: $siteIds
        surgeonId: $surgeonId
      }
    ) {
      roomId
      startTime
      endTime
      maxAvailableDuration
      blockTimeIds
    }
  }
"""


@pytest.mark.asyncio
async def test_get_available_slots_requires_auth() -> None:
    site = harness.site_0
    start_date = _get_next_tuesday()
    end_date = start_date + timedelta(days=2)

    result = harness.no_auth_client.execute_graphql(
        GET_AVAILABLE_TIME_SLOTS,
        variables={
            "minAvailableDuration": isodate.duration_isoformat(timedelta(minutes=90)),
            "siteIds": [site.id],
            "startDate": start_date.isoformat(),
            "endDate": end_date.isoformat(),
        },
    )

    assert result["data"]["availableTimeSlots"] is None
    assert result["errors"][0]["message"] == "Not Authorized: No authorization found"
    assert result["errors"][0]["path"] == ["availableTimeSlots"]


@pytest.mark.asyncio
async def test_get_available_slots_single_site() -> None:
    site = harness.site_0
    start_date = _get_next_tuesday()
    end_date = start_date + timedelta(days=2)

    result = harness.site_0_scoped_client.execute_graphql(
        GET_AVAILABLE_TIME_SLOTS,
        variables={
            "minAvailableDuration": isodate.duration_isoformat(timedelta(minutes=90)),
            "siteIds": [site.id],
            "startDate": start_date.isoformat(),
            "endDate": end_date.isoformat(),
        },
    )

    site_timezone = ZoneInfo(site.timezone)
    for room in [harness.room_0, harness.room_1]:
        room_slots = _get_slots_for_room(result, room.id)

        for i in range(2):
            slot = room_slots[i]

            slot_start = start_date + timedelta(days=i)
            slot_start_time = datetime.combine(slot_start, time(7, 30), tzinfo=site_timezone)
            slot_end_time = datetime.combine(slot_start, time(17, 0), tzinfo=site_timezone)

            assert slot["maxAvailableDuration"] == isodate.duration_isoformat(
                timedelta(hours=9, minutes=30)
            )

            assert datetime.fromisoformat(slot["startTime"]) == slot_start_time
            assert datetime.fromisoformat(slot["endTime"]) == slot_end_time
            assert slot["blockTimeIds"] == [
                calculate_unblocked_time_id(
                    room_id=room.id, start_time=slot_start_time, end_time=slot_end_time
                )
            ]


@pytest.mark.asyncio
async def test_get_available_slots_single_site_query_multiple_auth_error() -> None:
    site = harness.site_0
    site2 = harness.site_1
    start_date = _get_next_tuesday()
    end_date = start_date + timedelta(days=2)

    result = harness.site_0_scoped_client.execute_graphql(
        GET_AVAILABLE_TIME_SLOTS,
        variables={
            "minAvailableDuration": isodate.duration_isoformat(timedelta(minutes=90)),
            "siteIds": [site.id, site2.id],
            "startDate": start_date.isoformat(),
            "endDate": end_date.isoformat(),
        },
    )

    assert result["data"]["availableTimeSlots"] is None
    assert result["errors"][0]["message"] == "No site found with id: site_id_1"
    assert result["errors"][0]["path"] == ["availableTimeSlots"]


@pytest.mark.asyncio
async def test_get_available_slots__default() -> None:
    """
    No scheduled, no cases, everything is wide open. Prime time default is 07:30-17:00
    """
    site = harness.site_0
    start_date = _get_next_tuesday()
    end_date = start_date + timedelta(days=2)

    result = harness.hospital_administrator_client.execute_graphql(
        GET_AVAILABLE_TIME_SLOTS,
        variables={
            "minAvailableDuration": isodate.duration_isoformat(timedelta(minutes=90)),
            "siteIds": [site.id],
            "startDate": start_date.isoformat(),
            "endDate": end_date.isoformat(),
        },
    )

    site_timezone = ZoneInfo(site.timezone)
    for room in [harness.room_0, harness.room_1]:
        room_slots = _get_slots_for_room(result, room.id)

        for i in range(2):
            slot = room_slots[i]

            slot_start = start_date + timedelta(days=i)

            assert slot["maxAvailableDuration"] == isodate.duration_isoformat(
                timedelta(hours=9, minutes=30)
            )

            start_time = datetime.combine(slot_start, time(7, 30), tzinfo=site_timezone)
            end_time = datetime.combine(slot_start, time(17, 0), tzinfo=site_timezone)

            assert datetime.fromisoformat(slot["startTime"]) == start_time
            assert datetime.fromisoformat(slot["endTime"]) == end_time
            assert slot["blockTimeIds"] == [
                calculate_unblocked_time_id(
                    room_id=room.id, start_time=start_time, end_time=end_time
                )
            ]


@pytest.mark.asyncio
async def test_get_zero_available_slots_for_holidays() -> None:
    """
    No scheduled, no cases, everything is wide open. Prime time default is 07:30-17:00
    """
    christmas = date(2024, 12, 25)
    site = harness.site_0
    start_date = christmas
    end_date = christmas

    await harness.closure_store.create_site_closure(
        SiteClosure(
            closure_date=christmas,
            reason="christmas",
            site_id=site.id,
        )
    )

    result = harness.hospital_administrator_client.execute_graphql(
        GET_AVAILABLE_TIME_SLOTS,
        variables={
            "minAvailableDuration": isodate.duration_isoformat(timedelta(minutes=90)),
            "siteIds": [site.id],
            "startDate": start_date.isoformat(),
            "endDate": end_date.isoformat(),
        },
    )

    all_slots = []

    for room in [harness.room_0, harness.room_1]:
        all_slots.extend(_get_slots_for_room(result, room.id))

    assert len(all_slots) == 0


@pytest.mark.asyncio
async def test_get_available_slots__prime_time_set() -> None:
    """
    No scheduled, no cases, everything is wide open.
    Prime time is set at site and room level.
    """
    site = harness.site_0
    start_date = _get_next_tuesday()
    end_date = start_date + timedelta(days=2)

    site_prime_time_start = time(hour=6)
    site_prime_time_end = time(hour=18)
    await _upsert_site_weekday_prime_time(
        harness.site_0.id, site_prime_time_start, site_prime_time_end
    )

    room_prime_time_start = time(hour=8)
    room_prime_time_end = time(hour=19)
    await _upsert_room_weekday_prime_time(
        harness.room_0.id, room_prime_time_start, room_prime_time_end
    )

    result = harness.hospital_administrator_client.execute_graphql(
        GET_AVAILABLE_TIME_SLOTS,
        variables={
            "minAvailableDuration": isodate.duration_isoformat(timedelta(minutes=90)),
            "siteIds": [site.id],
            "startDate": start_date.isoformat(),
            "endDate": end_date.isoformat(),
        },
    )

    room_0_slots = _get_slots_for_room(result, harness.room_0.id)
    site_timezone = ZoneInfo(site.timezone)

    for i in range(2):
        slot = room_0_slots[i]

        slot_start = start_date + timedelta(days=i)
        slot_start_time = datetime.combine(slot_start, room_prime_time_start, tzinfo=site_timezone)
        slot_end_time = datetime.combine(slot_start, room_prime_time_end, tzinfo=site_timezone)

        # Room level prime time - 08:00-19:00 PT
        assert datetime.fromisoformat(slot["startTime"]) == slot_start_time
        assert datetime.fromisoformat(slot["endTime"]) == slot_end_time
        assert slot["maxAvailableDuration"] == isodate.duration_isoformat(timedelta(hours=11))
        assert slot["blockTimeIds"] == [
            calculate_unblocked_time_id(
                room_id=harness.room_0.id, start_time=slot_start_time, end_time=slot_end_time
            )
        ]

    room_1_slots = _get_slots_for_room(result, harness.room_1.id)

    for i in range(2):
        slot = room_1_slots[i]

        slot_start = start_date + timedelta(days=i)
        slot_start_time = datetime.combine(slot_start, site_prime_time_start, tzinfo=site_timezone)
        slot_end_time = datetime.combine(slot_start, site_prime_time_end, tzinfo=site_timezone)

        # Site level prime time - 06:00-18:00 PT
        assert datetime.fromisoformat(slot["startTime"]) == slot_start_time
        assert datetime.fromisoformat(slot["endTime"]) == slot_end_time
        assert slot["maxAvailableDuration"] == isodate.duration_isoformat(timedelta(hours=12))

        assert slot["blockTimeIds"] == [
            calculate_unblocked_time_id(
                room_id=harness.room_1.id, start_time=slot_start_time, end_time=slot_end_time
            )
        ]


@pytest.mark.asyncio
async def test_get_available_slots__block_past_prime_time() -> None:
    """
    No scheduled, no cases, everything is wide open.
    Prime time is set at room level
    Block time runs before/past prime time.
    """
    site = harness.site_0
    start_date = _get_next_tuesday()
    end_date = start_date + timedelta(days=1)
    block_name = "Block 1"
    prime_start_hour = 6
    prime_time_start = time(hour=prime_start_hour)
    # 1 hour before prime time
    block_start_hour = prime_start_hour - 1
    block_time_start_time = time(hour=block_start_hour)
    block_time_start = datetime.combine(
        start_date, block_time_start_time, tzinfo=ZoneInfo(site.timezone)
    )
    prime_end_hour = 18
    prime_time_end = time(hour=prime_end_hour)
    # 2 hours after prime time
    block_time_end_time = time(hour=prime_end_hour + 2)
    block_time_end = datetime.combine(
        start_date, block_time_end_time, tzinfo=ZoneInfo(site.timezone)
    )
    block_id = uuid4()
    block_time_id = uuid4()
    await harness.block_store.create_block(
        BlockDataInput(
            id=block_id,
            name=block_name,
            org_id=harness.org_0.id,
            color="#ABCDEF",
            surgeon_ids=[str(harness.staff_0.id)],
            block_times=[
                BlockTimeModel(
                    id=block_time_id,
                    block_id=block_id,
                    room_id=harness.room_0.id,
                    start_time=block_time_start,
                    end_time=block_time_end,
                )
            ],
        )
    )

    await _upsert_room_weekday_prime_time(harness.room_0.id, prime_time_start, prime_time_end)

    result = harness.hospital_administrator_client.execute_graphql(
        GET_AVAILABLE_TIME_SLOTS,
        variables={
            "minAvailableDuration": isodate.duration_isoformat(timedelta(minutes=90)),
            "surgeonId": str(harness.staff_0.id),
            "siteIds": [site.id],
            "startDate": start_date.isoformat(),
            "endDate": end_date.isoformat(),
        },
    )

    room_0_slots = _get_slots_for_room(result, harness.room_0.id)
    no_block_start_time = datetime.combine(
        start_date + timedelta(days=1), prime_time_start, tzinfo=ZoneInfo(site.timezone)
    )
    no_block_end_time = datetime.combine(
        start_date + timedelta(days=1), prime_time_end, tzinfo=ZoneInfo(site.timezone)
    )
    assert_slots_equal(
        room_0_slots,
        [
            # Block time exists on start date - 05:00-20:00 PT
            {
                "startTime": datetime.combine(
                    start_date, block_time_start_time, tzinfo=ZoneInfo(site.timezone)
                ),
                "endTime": datetime.combine(
                    start_date, block_time_end_time, tzinfo=ZoneInfo(site.timezone)
                ),
                "maxAvailableDuration": timedelta(hours=15),
                "blockTimeIds": [block_time_id],
            },
            # No Block time, so expect room level prime time - 06:00-18:00 PT
            {
                "startTime": no_block_start_time,
                "endTime": no_block_end_time,
                "maxAvailableDuration": timedelta(hours=12),
                "blockTimeIds": [
                    calculate_unblocked_time_id(
                        room_id=harness.room_0.id,
                        start_time=no_block_start_time,
                        end_time=no_block_end_time,
                    )
                ],
            },
        ],
    )


@pytest.mark.asyncio
async def test_get_available_time_slots__with_surgeon_id(
    async_session: AsyncSession,
) -> None:
    """
    Prime Time: 07:30-17:00
    1 case scheduled by surgeon 0 in room 2
    85 minute min case duration - querying for available time slots for surgeon 0

    Diagram:

    room_0 :  7.30 ---------------------------------------------------------------------- 17
    <USER> <GROUP>

    room_1 :  7.30 ---------------------------------------------------------------------- 17
    <USER> <GROUP>, CLOSED

    room_2 :  7.30 ---------------------------------------------------------------------- 17
    case   :                    9 -------- 10

    For a 85 minute min case duration, we should suggest:
    room_0 :  7.30 ------------ 9          <USER> <GROUP> 17
    room_1 :  (nothing)
    room_2 :  7.30 ------------ 9          <USER> <GROUP> 17
    """
    timezone_str = "UTC"
    site_id = "HMH-WT03"
    room_id_0 = "HMH-WT03-OR08"
    room_id_1 = "HMH-WT03-OR06"
    room_id_2 = "HMH-WT03-OR07"
    staff_id = harness.staff_0.id
    org_id = harness.org_0.id

    site = await harness.site_store.create_site(
        Site(
            id=site_id,
            name="Test Site",
            timezone=timezone_str,
            org_id=org_id,
        )
    )
    await harness.room_store.create_rooms(
        [
            RoomModel(
                id=room_id_0,
                name="OR 08",
                site_id=site_id,
                org_id=org_id,
            ),
            RoomModel(
                id=room_id_1,
                name="OR 06",
                site_id=site_id,
                org_id=org_id,
            ),
            RoomModel(
                id=room_id_2,
                name="OR 07",
                site_id=site_id,
                org_id=org_id,
            ),
        ]
    )

    room_2 = await harness.room_store.get_room(room_id_2)

    case_date = _get_next_tuesday()

    def get_dt(t: time) -> datetime:
        return datetime.combine(case_date, t, tzinfo=site.timezone_info)

    block_id = uuid4()
    block_time_id_0 = uuid4()
    block_time_id_1 = uuid4()
    await harness.block_store.create_block(
        BlockDataInput(
            id=block_id,
            name="Test Block 0",
            color="#FFFFFF",
            org_id=org_id,
            surgeon_ids=[str(staff_id)],
            block_times=[
                BlockTimeModel(
                    id=block_time_id_0,
                    block_id=block_id,
                    room_id=room_id_0,
                    start_time=get_dt(time(8)),
                    end_time=get_dt(time(12)),
                ),
                BlockTimeModel(
                    id=block_time_id_1,
                    block_id=block_id,
                    room_id=room_id_1,
                    start_time=get_dt(time(9)),
                    end_time=get_dt(time(14)),
                ),
            ],
        )
    )

    await _create_mock_case(
        room=room_2,
        start_time=get_dt(time(9)),
        end_time=get_dt(time(10)),
        surgeon_id=staff_id,
        session=async_session,
    )

    await harness.closure_store.create_room_closure(
        RoomClosure(room_id=room_id_1, start_time=get_dt(time(0)), end_time=get_dt(time(23, 59)))
    )

    min_available_duration = timedelta(minutes=85)

    result = harness.hospital_administrator_client.execute_graphql(
        GET_AVAILABLE_TIME_SLOTS,
        variables={
            "minAvailableDuration": isodate.duration_isoformat(min_available_duration),
            "surgeonId": str(staff_id),
            "siteIds": [site_id],
            "startDate": case_date.isoformat(),
            "endDate": case_date.isoformat(),
        },
    )

    room_0_slots = _get_slots_for_room(result, room_id_0)
    room_1_slots = _get_slots_for_room(result, room_id_1)
    room_2_slots = _get_slots_for_room(result, room_id_2)

    # room 0 has a open time from 7:30AM until scheduled case at 9:00am and from
    # scheduled case end at 10:am till 5:00pm
    # this is only because the surgeon is the primary surgeon of the case in room 2
    assert_slots_equal(
        room_0_slots,
        [
            {
                "startTime": get_dt(time(7, 30)),
                "endTime": get_dt(time(9)),
                "maxAvailableDuration": timedelta(minutes=90),
                "blockTimeIds": [
                    calculate_unblocked_time_id(
                        room_id=room_id_0, start_time=get_dt(time(7, 30)), end_time=get_dt(time(8))
                    ),
                    block_time_id_0,
                ],
            },
            {
                "startTime": get_dt(time(7, 30)),
                "endTime": get_dt(time(8)),
                "maxAvailableDuration": timedelta(minutes=30),
                "blockTimeIds": [
                    calculate_unblocked_time_id(
                        room_id=room_id_0, start_time=get_dt(time(7, 30)), end_time=get_dt(time(8))
                    ),
                ],
            },
            {
                "startTime": get_dt(time(8)),
                "endTime": get_dt(time(9)),
                "maxAvailableDuration": timedelta(hours=1),
                "blockTimeIds": [
                    block_time_id_0,
                ],
            },
            {
                "startTime": get_dt(time(10, 0)),
                "endTime": get_dt(time(17, 0)),
                "maxAvailableDuration": timedelta(hours=7),
                "blockTimeIds": [
                    block_time_id_0,
                    calculate_unblocked_time_id(
                        room_id=room_id_0, start_time=get_dt(time(12)), end_time=get_dt(time(17))
                    ),
                ],
            },
            {
                "startTime": get_dt(time(10, 0)),
                "endTime": get_dt(time(12, 0)),
                "maxAvailableDuration": timedelta(hours=2),
                "blockTimeIds": [
                    block_time_id_0,
                ],
            },
            {
                "startTime": get_dt(time(12, 0)),
                "endTime": get_dt(time(17, 0)),
                "maxAvailableDuration": timedelta(hours=5),
                "blockTimeIds": [
                    calculate_unblocked_time_id(
                        room_id=room_id_0, start_time=get_dt(time(12)), end_time=get_dt(time(17))
                    ),
                ],
            },
        ],
    )

    # room 1 has no cases, but has a closure from 12:00AM to 11:59PM
    assert len(room_1_slots) == 0

    # room 2 has a case scheduled from 9-10 as mentioned above
    assert_slots_equal(
        room_2_slots,
        [
            {
                "startTime": get_dt(time(7, 30)),
                "endTime": get_dt(time(9, 0)),
                "maxAvailableDuration": timedelta(minutes=90),
                "blockTimeIds": [
                    calculate_unblocked_time_id(
                        room_id=room_id_2, start_time=get_dt(time(7, 30)), end_time=get_dt(time(9))
                    ),
                ],
            },
            {
                "startTime": get_dt(time(10, 0)),
                "endTime": get_dt(time(17, 0)),
                "maxAvailableDuration": timedelta(hours=7),
                "blockTimeIds": [
                    calculate_unblocked_time_id(
                        room_id=room_id_2, start_time=get_dt(time(10)), end_time=get_dt(time(17))
                    ),
                ],
            },
        ],
    )

    # No other rooms have availability
    assert len(result["data"]["availableTimeSlots"]) == len(room_0_slots) + len(room_2_slots)


@pytest.mark.asyncio
async def test_get_available_slots__capacity_constraints_configured(
    async_session: AsyncSession,
) -> None:
    """
    1 capacity constraint configured for the site
    Prime Time: 07:00-17:00
    1 constraint at 15:00 - 1 case allowed
    60 minute min case duration

    Diagram:

    room_0:   7 ---------------------------------------------------------------------- 17
    case_1:                                               14 -------------- 16

    room_1:   7 ---------------------------------------------------------------------- 17
    <USER> <GROUP>

    For a 60 minute min case duration, we should suggest:
    room_0:   7 ----------------------------------------- 14                <USER> <GROUP> 17
    room_1:   7 -------------------------------------------------- 15       <USER> <GROUP> 17
    """
    site = harness.site_0
    start_date = _get_next_tuesday()
    end_date = start_date + timedelta(days=1)
    room_0, room_1 = harness.room_0, harness.room_1

    await _upsert_site_weekday_prime_time(
        site_id=site.id,
        start_time=time(7, 30),
        end_time=time(17),
        constraints=[CapacityConstraint(count=1, start_time=time(15))],
    )

    await _create_mock_case(
        room=room_0,
        start_time=datetime.combine(start_date, time(14), tzinfo=site.timezone_info),
        end_time=datetime.combine(start_date, time(16), tzinfo=site.timezone_info),
        session=async_session,
    )

    result = harness.hospital_administrator_client.execute_graphql(
        GET_AVAILABLE_TIME_SLOTS,
        variables={
            "minAvailableDuration": isodate.duration_isoformat(timedelta(minutes=60)),
            "siteIds": [site.id],
            "startDate": start_date.isoformat(),
            "endDate": end_date.isoformat(),
        },
    )

    room_0_slots = _get_slots_for_room(result, room_0.id)
    room_1_slots = _get_slots_for_room(result, room_1.id)

    # Day 1
    day_1 = start_date
    day_2 = start_date + timedelta(days=1)

    # times
    day_1_time_7_30 = datetime.combine(day_1, time(7, 30), tzinfo=site.timezone_info)
    day_1_time_14 = datetime.combine(day_1, time(14), tzinfo=site.timezone_info)
    day_1_time_15 = datetime.combine(day_1, time(15), tzinfo=site.timezone_info)
    day_1_time_16 = datetime.combine(day_1, time(16), tzinfo=site.timezone_info)
    day_1_time_17 = datetime.combine(day_1, time(17), tzinfo=site.timezone_info)

    day_2_time_7_30 = datetime.combine(day_2, time(7, 30), tzinfo=site.timezone_info)
    day_2_time_17 = datetime.combine(day_2, time(17), tzinfo=site.timezone_info)

    assert_slots_equal(
        room_0_slots,
        [
            # Day 1 - Affected by constraints
            {
                "startTime": day_1_time_7_30,
                "endTime": day_1_time_14,
                "maxAvailableDuration": timedelta(hours=6, minutes=30),
                "blockTimeIds": [
                    calculate_unblocked_time_id(
                        room_id=room_0.id, start_time=day_1_time_7_30, end_time=day_1_time_14
                    )
                ],
            },
            {
                "startTime": day_1_time_16,
                "endTime": day_1_time_17,
                "maxAvailableDuration": timedelta(hours=1),
                "blockTimeIds": [
                    calculate_unblocked_time_id(
                        room_id=room_0.id, start_time=day_1_time_16, end_time=day_1_time_17
                    )
                ],
            },
            # Day 2 - Everything open
            {
                "startTime": day_2_time_7_30,
                "endTime": day_2_time_17,
                "maxAvailableDuration": timedelta(hours=9, minutes=30),
                "blockTimeIds": [
                    calculate_unblocked_time_id(
                        room_id=room_0.id, start_time=day_2_time_7_30, end_time=day_2_time_17
                    )
                ],
            },
        ],
    )

    assert_slots_equal(
        room_1_slots,
        [
            # Day 1 - Affected by constraints
            {
                "startTime": day_1_time_7_30,
                "endTime": day_1_time_15,
                "maxAvailableDuration": timedelta(hours=7, minutes=30),
                "blockTimeIds": [
                    calculate_unblocked_time_id(
                        room_id=room_1.id, start_time=day_1_time_7_30, end_time=day_1_time_15
                    )
                ],
            },
            {
                "startTime": day_1_time_16,
                "endTime": day_1_time_17,
                "maxAvailableDuration": timedelta(hours=1),
                "blockTimeIds": [
                    calculate_unblocked_time_id(
                        room_id=room_1.id, start_time=day_1_time_16, end_time=day_1_time_17
                    )
                ],
            },
            # Day 2 - Everything open
            {
                "startTime": day_2_time_7_30,
                "endTime": day_2_time_17,
                "maxAvailableDuration": timedelta(hours=9, minutes=30),
                "blockTimeIds": [
                    calculate_unblocked_time_id(
                        room_id=room_1.id, start_time=day_2_time_7_30, end_time=day_2_time_17
                    )
                ],
            },
        ],
    )


def _get_slots_for_room(result: dict[str, Any], room_id: str) -> list[dict[str, Any]]:
    return sorted(
        [slot for slot in result["data"]["availableTimeSlots"] if slot["roomId"] == room_id],
        key=lambda x: x["startTime"],
    )


def _get_next_tuesday() -> date:
    today = date.today()
    days_until_tuesday = (1 - today.weekday() + 7) % 7  # 1 is Tuesday
    days_until_tuesday = (
        days_until_tuesday or 7
    )  # Ensure it moves to next Tuesday if today is Tuesday
    next_tuesday = today + timedelta(days=days_until_tuesday)
    return next_tuesday


async def _upsert_room_weekday_prime_time(
    room_id: str, start_time: time, end_time: time
) -> RoomPrimeTimeConfig:
    return await harness.prime_time_store.upsert_room_prime_time_config(
        RoomPrimeTimeConfig(
            room_id=room_id,
            monday_start_time=start_time,
            monday_end_time=end_time,
            tuesday_start_time=start_time,
            tuesday_end_time=end_time,
            wednesday_start_time=start_time,
            wednesday_end_time=end_time,
            thursday_start_time=start_time,
            thursday_end_time=end_time,
            friday_start_time=start_time,
            friday_end_time=end_time,
        )
    )


async def _upsert_site_weekday_prime_time(
    site_id: str,
    start_time: time,
    end_time: time,
    constraints: list[CapacityConstraint] = [],
) -> SitePrimeTimeConfig:
    day_of_week_config = DayOfWeekConfig(
        start_time=start_time, end_time=end_time, constraints=constraints
    )

    return await harness.prime_time_store.upsert_site_prime_time_and_capacity_constraints(
        SitePrimeTimeConfigUpdateDto(
            site_id=site_id,
            monday=day_of_week_config,
            tuesday=day_of_week_config,
            wednesday=day_of_week_config,
            thursday=day_of_week_config,
            friday=day_of_week_config,
        )
    )


async def _create_mock_case(
    room: RoomModel,
    start_time: datetime,
    end_time: datetime,
    session: AsyncSession,
    surgeon_id: Optional[UUID] = None,
) -> Case:
    case_id = str(uuid4())
    org_id = room.org_id
    site_id = room.site_id
    room_id = room.id
    case = await harness.case_store.create_case(
        Case(
            external_case_id=f"ext_case_id_{case_id}",
            case_id=case_id,
            org_id=org_id,
            site_id=site_id,
            room_id=room_id,
            scheduled_start_time=start_time,
            scheduled_end_time=end_time,
            is_add_on=False,
            status=SCHEDULED,
        ),
        session=session,
    )
    case_id = case.case_id
    await harness.case_forecast_store.upsert_case_forecasts(
        [
            CaseForecastModel(
                id=str(uuid4()),
                org_id=org_id,
                site_id=site_id,
                room_id=room_id,
                case_id=case_id,
                forecast_start_time=start_time,
                forecast_end_time=end_time,
            )
        ]
    )

    if surgeon_id is not None:
        await harness.case_staff_store.upsert_case_staff(
            [
                CaseStaffModel(
                    staff_id=surgeon_id, case_id=case.case_id, role=PRIMARY_SURGEON_ROLES[0]
                )
            ]
        )
    return case


AvailableTimeSlot = TypedDict(
    "AvailableTimeSlot",
    {
        "startTime": datetime,
        "endTime": datetime,
        "maxAvailableDuration": timedelta,
        "blockTimeIds": list[Any],
    },
)


def assert_slots_equal(actual: list[dict[str, Any]], expected: list[AvailableTimeSlot]) -> None:
    expected.sort(key=lambda x: (x["startTime"], x["endTime"]))
    assert len(actual) == len(expected)

    for i in range(len(actual)):
        actual_slot = actual[i]
        expected_slot = expected[i]
        assert datetime.fromisoformat(actual_slot["startTime"]) == expected_slot["startTime"]
        assert datetime.fromisoformat(actual_slot["endTime"]) == expected_slot["endTime"]
        assert (
            isodate.parse_duration(actual_slot["maxAvailableDuration"])
            == expected_slot["maxAvailableDuration"]
        )
        assert actual_slot["blockTimeIds"] == [
            str(blockId) for blockId in expected_slot["blockTimeIds"]
        ]
