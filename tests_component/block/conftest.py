from datetime import datetime, timedelta, timezone
from typing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from unittest.mock import MagicMock
from uuid import UUI<PERSON>, uuid4

import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession

from apella_cloud_api import Client
from api_server.services.block.block_models import BlockDataInput
from api_server.services.block.block_csv_asset_store import BlockCsvAssetStore
from api_server.services.block.block_release_file_store import BlockReleaseFileStore
from api_server.services.block.block_release_processing_service import BlockReleaseProcessingService
from api_server.services.block.block_schedule_file_store import BlockScheduleFileStore
from api_server.services.block.block_schedule_processing_service import (
    BlockScheduleProcessingService,
)
from api_server.services.block.block_service import BlockService

from api_server.services.block.block_store import (
    BlockModel,
    BlockTimeModel,
)
from api_server.services.closures.closure_service import ClosureService
from api_server.services.site.site_service import SiteService
from api_server.services.staff.staff_store import StaffMode<PERSON>
from auth.auth import Auth
from tests_component import harness


@pytest.fixture
def mock_auth() -> MagicMock:
    mock_auth = MagicMock(spec=Auth)
    mock_auth.get_calling_user_id = MagicMock(return_value="current_user_id")
    return mock_auth


@pytest.fixture
def closure_service(mock_auth: MagicMock) -> ClosureService:
    return ClosureService(
        auth=mock_auth,
        closure_store=harness.closure_store,
        site_service=MagicMock(spec=SiteService),
    )


@pytest.fixture
def block_csv_asset_store(mock_auth: MagicMock) -> BlockCsvAssetStore:
    block_csv_asset_store = MagicMock()
    block_csv_asset_store.storage_client = MagicMock(spec=Client)
    return block_csv_asset_store


@pytest.fixture
def block_release_file_store(mock_auth: MagicMock) -> BlockReleaseFileStore:
    return BlockReleaseFileStore()


@pytest.fixture
def block_schedule_file_store(mock_auth: MagicMock) -> BlockScheduleFileStore:
    return BlockScheduleFileStore()


@pytest.fixture
def block_service(
    mock_auth: MagicMock,
) -> BlockService:
    return BlockService(
        auth=mock_auth,
        block_store=harness.block_store,
    )


@pytest.fixture
def block_release_processing_service(
    mock_auth: MagicMock,
    block_service: BlockService,
    block_csv_asset_store: BlockCsvAssetStore,
    block_release_file_store: BlockReleaseFileStore,
    closure_service: ClosureService,
) -> BlockReleaseProcessingService:
    return BlockReleaseProcessingService(
        auth=mock_auth,
        closure_service=closure_service,
        block_csv_asset_store=block_csv_asset_store,
        block_service=block_service,
        block_release_file_store=block_release_file_store,
    )


@pytest.fixture
def block_schedule_processing_service(
    mock_auth: MagicMock,
    block_schedule_file_store: BlockScheduleFileStore,
    block_csv_asset_store: BlockCsvAssetStore,
) -> BlockScheduleProcessingService:
    return BlockScheduleProcessingService(
        auth=mock_auth,
        block_schedule_file_store=block_schedule_file_store,
        block_csv_asset_store=block_csv_asset_store,
    )


@pytest.fixture
def block_id() -> UUID:
    return uuid4()


@pytest.fixture
def on_hold_block_id() -> UUID:
    return uuid4()


@pytest.fixture
def general_block_id() -> UUID:
    return uuid4()


@pytest.fixture
def block_time_id_0() -> UUID:
    return uuid4()


@pytest.fixture
def block_time_id_1() -> UUID:
    return uuid4()


@pytest.fixture
def org_id() -> str:
    return harness.org_0.id


@pytest.fixture
def room_id_0() -> str:
    return harness.room_0.id


@pytest.fixture
def room_id_1() -> str:
    return harness.room_1.id


@pytest.fixture
def block_name() -> str:
    return "i_am_a_new_block"


@pytest.fixture
def block_color() -> str:
    return "#FF0000"


@pytest.fixture
def start_time() -> datetime:
    return datetime(2023, 7, 1, 9, 0, 0, tzinfo=timezone.utc)


@pytest.fixture
def end_time(start_time: datetime) -> datetime:
    return start_time + timedelta(hours=8)


@pytest_asyncio.fixture
async def staff_0(async_session: AsyncSession) -> AsyncGenerator[StaffModel, None]:
    staff = StaffModel()
    staff.last_name = "LastStaff 0"
    staff.first_name = "FirstStaff 0"
    staff.external_staff_id = "ext_staff_id_0"
    staff.org_id = harness.org_0.id

    [upserted_staff] = await harness.staff_store.upsert_staff(staff=[staff], session=async_session)
    yield upserted_staff


@pytest_asyncio.fixture
async def new_block(
    block_id: UUID,
    org_id: str,
    room_id_0: str,
    room_id_1: str,
    block_name: str,
    block_color: str,
    block_time_id_0: UUID,
    block_time_id_1: UUID,
    staff_0: StaffModel,
    start_time: datetime,
    end_time: datetime,
) -> AsyncGenerator[BlockModel, None]:
    block = await harness.block_store.create_block(
        block=BlockDataInput(
            id=block_id,
            name=block_name,
            color=block_color,
            org_id=org_id,
            site_ids=[str(harness.site_0.id)],
            block_times=[
                BlockTimeModel(
                    id=block_time_id_0,
                    block_id=block_id,
                    room_id=room_id_0,
                    start_time=start_time,
                    end_time=end_time,
                    releases=[],
                ),
                BlockTimeModel(
                    id=block_time_id_1,
                    block_id=block_id,
                    room_id=room_id_1,
                    start_time=start_time,
                    end_time=end_time,
                    releases=[],
                ),
            ],
            surgeon_ids=[str(staff_0.id)],
        ),
    )
    yield block
    # When working with sessions we cannot rely on the session to be updated as a result of another remote session
    # Ideally we should create db resources through the api, alternatively we must refresh the session.
    # await async_session.refresh(block)
    await harness.block_store.delete_block(id=block_id)


@pytest_asyncio.fixture
async def on_hold_block(
    on_hold_block_id: UUID,
    org_id: str,
    block_color: str,
    block_time_id_0: UUID,
    room_id_0: str,
    staff_0: StaffModel,
    start_time: datetime,
    end_time: datetime,
) -> AsyncGenerator[BlockModel, None]:
    block = await harness.block_store.create_block(
        block=BlockDataInput(
            id=on_hold_block_id,
            name="On Hold",
            color=block_color,
            org_id=org_id,
            site_ids=[str(harness.site_0.id)],
            block_times=[
                BlockTimeModel(
                    id=block_time_id_0,
                    block_id=on_hold_block_id,
                    room_id=room_id_0,
                    start_time=start_time,
                    end_time=end_time,
                    releases=[],
                ),
            ],
            surgeon_ids=[],
        ),
    )
    yield block
    await harness.block_store.delete_block(id=on_hold_block_id)


@pytest_asyncio.fixture
async def general_block(
    general_block_id: UUID,
    org_id: str,
    block_color: str,
    block_time_id_0: UUID,
    room_id_0: str,
    staff_0: StaffModel,
    start_time: datetime,
    end_time: datetime,
) -> AsyncGenerator[BlockModel, None]:
    block = await harness.block_store.create_block(
        BlockDataInput(
            id=general_block_id,
            name="General",
            org_id=org_id,
            color="#ABCDEF",
            surgeon_ids=[str(harness.staff_0.id)],
            site_ids=[str(harness.site_0.id)],
            block_times=[],
        )
    )
    yield block
    await harness.block_store.delete_block(id=general_block_id)
