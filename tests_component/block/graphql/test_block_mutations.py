from datetime import datetime
from unittest.mock import MagicMock
from uuid import UUID, uuid4

import pytest

from apella_cloud_api.dtos import BlockQueryDto
from api_server.services.block.block_models import BlockDataInput, BlockTimeModel
from api_server.services.block.block_service import BlockService
from api_server.services.block.block_store import (
    BlockModel,
)
from api_server.services.staff.staff_store import StaffModel
from tests_component import harness


@pytest.mark.asyncio
async def test_create_block(
    block_id: UUID,
    org_id: str,
    room_id: str,
    block_name: str,
    block_color: str,
    staff_0: StaffModel,
    staff_1: StaffModel,
) -> None:
    new_block = harness.service_account_client.execute_graphql(
        f"""
    mutation {{
      blockCreate(input: {{
        id: "{block_id}",
        name: "{block_name}",
        color: "{block_color}",
        orgId: "{org_id}",
        siteIds: ["{harness.site_0.id}"],
        blockTimes: [{{
          startTime: "2021-01-01T09:00:00+0000",
          endTime: "2021-01-01T17:00:00+0000",
          roomId: "{room_id}"
        }}]
        surgeonIds: ["{staff_0.id}", "{staff_1.id}"]
      }}) {{
        block {{
          id
          name
          color
          orgId
          siteIds
          surgeonIds
          blockTimes {{
            startTime
            endTime
            roomId
            releases {{
              startTime
              endTime
              reason
              source
            }}
          }}
        }}
      }}
    }}
    """
    )["data"]["blockCreate"]["block"]

    assert new_block == {
        "id": str(block_id),
        "name": block_name,
        "color": block_color,
        "orgId": org_id,
        "siteIds": [str(harness.site_0.id)],
        "surgeonIds": [str(staff_0.id), str(staff_1.id)],
        "blockTimes": [
            {
                "startTime": "2021-01-01T09:00:00+00:00",
                "endTime": "2021-01-01T17:00:00+00:00",
                "roomId": room_id,
                "releases": [],
            }
        ],
    }


@pytest.mark.asyncio
async def test_create_block_release_time_out_of_bound(
    block_id: UUID,
    org_id: str,
    room_id: str,
    block_name: str,
    block_color: str,
    staff_0: StaffModel,
    staff_1: StaffModel,
) -> None:
    error = harness.service_account_client.execute_graphql(
        f"""
    mutation {{
      blockCreate(input: {{
        id: "{block_id}",
        name: "{block_name}",
        color: "{block_color}",
        orgId: "{org_id}",
        siteIds: ["{harness.site_0.id}"],
        blockTimes: [{{
          startTime: "2021-01-01T09:00:00+0000",
          endTime: "2021-01-01T17:00:00+0000",
          roomId: "{room_id}",
          releases: [{{
              startTime: "2021-01-01T08:00:00+0000",
              endTime: "2021-01-01T17:00:00+0000",
              source: "component test update",
              reason: "I gotta go home before lunch"
            }}]
        }}]
        surgeonIds: ["{staff_0.id}", "{staff_1.id}"]
      }}) {{
        block {{
          id
        }}
      }}
    }}
    """
    )

    assert error == {
        "errors": [
            {
                "message": "Block time release start and end times must be within the block time.",
                "locations": [{"line": 3, "column": 7}],
                "path": ["blockCreate"],
            }
        ],
        "data": {"blockCreate": None},
    }


@pytest.mark.asyncio
async def test_create_block_release_time_start_eq_end(
    block_id: UUID,
    org_id: str,
    room_id: str,
    block_name: str,
    block_color: str,
    staff_0: StaffModel,
    staff_1: StaffModel,
) -> None:
    error = harness.service_account_client.execute_graphql(
        f"""
    mutation {{
      blockCreate(input: {{
        id: "{block_id}",
        name: "{block_name}",
        color: "{block_color}",
        orgId: "{org_id}"
        blockTimes: [{{
          startTime: "2021-01-01T09:00:00+0000",
          endTime: "2021-01-01T17:00:00+0000",
          roomId: "{room_id}",
          releases: [{{
              startTime: "2021-01-01T10:00:00+0000",
              endTime: "2021-01-01T10:00:00+0000",
              source: "component test update",
              reason: "This won't work"
            }}]
        }}]
        surgeonIds: ["{staff_0.id}", "{staff_1.id}"]
      }}) {{
        block {{
          id
        }}
      }}
    }}
    """
    )

    assert (
        error["errors"][0]["message"] == "The start time must be strictly less than the end time."
    )
    assert error["data"]["blockCreate"]["block"] is None


@pytest.mark.asyncio
async def test_create_block_without_id(block_name: str) -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    mutation {{
      blockCreate(input: {{
        name: "{block_name}",
        orgId: "{harness.org_0.id}",
        siteIds: ["{harness.site_0.id}"],
        blockTimes: []
      }}) {{
        block {{
          id
          name
          orgId
          siteIds
        }}
      }}
    }}
    """
    )
    block = result["data"]["blockCreate"]["block"]
    assert block["id"] is not None
    assert block["name"] == block_name
    assert block["orgId"] == harness.org_0.id
    assert block["siteIds"] == [harness.site_0.id]


@pytest.mark.asyncio
async def test_block_create_without_org_id(block_name: str) -> None:
    response = harness.service_account_client.execute_graphql(
        f"""
  mutation {{
    blockCreate(input: {{
      name: "{block_name}",
      blockTimes: []
    }}) {{
      block {{
        id
        name
        orgId
      }}
    }}
  }}
  """
    )

    assert response["data"] is None
    assert len(response["errors"]) == 1
    assert (
        response["errors"][0]["message"]
        == "Field 'BlockCreateInput.orgId' of required type 'ID!' was not provided."
    )


@pytest.mark.asyncio
async def test_block_create_with_conflicting_block_times_in_db(
    org_id: str, room_id: str, new_block: BlockModel
) -> None:
    response = harness.service_account_client.execute_graphql(
        f"""
        mutation {{
          blockCreate(input: {{
            id: "{uuid4()}",
            name: "another_block_name",
            color: "#000000",
            orgId: "{org_id}"
            blockTimes: [{{
              startTime: "2021-01-01T12:00:00+0000",
              endTime: "2021-01-01T17:00:00+0000",
              roomId: "{room_id}"
            }}]
          }}) {{
            block {{
              id
              name
              color
              orgId
              blockTimes {{
                startTime
                endTime
                roomId
              }}
            }}
          }}
        }}
        """
    )

    assert response == {
        "errors": [
            {
                "message": "Block release start and end times are overlapping with existing block times.",
                "locations": [{"line": 3, "column": 11}],
                "path": ["blockCreate"],
            }
        ],
        "data": {"blockCreate": None},
    }

    dto = BlockQueryDto(org_ids=[org_id])
    blocks = await harness.block_store.query_blocks(dto=dto)
    assert len(blocks) == 1  # only the new_block
    assert blocks[0].id == new_block.id


@pytest.mark.asyncio
async def test_block_create_with_conflicting_block_times_in_input(
    block_id: UUID,
    org_id: str,
    room_id: str,
    block_name: str,
    block_color: str,
) -> None:
    response = harness.service_account_client.execute_graphql(
        f"""
        mutation {{
          blockCreate(input: {{
            id: "{block_id}",
            name: "{block_name}",
            color: "{block_color}",
            orgId: "{org_id}"
            blockTimes: [{{
              startTime: "2021-01-01T09:00:00+0000",
              endTime: "2021-01-01T17:00:00+0000",
              roomId: "{room_id}"
            }}, {{
              startTime: "2021-01-01T09:00:00+0000",
              endTime: "2021-01-01T17:00:00+0000",
              roomId: "{room_id}"
            }}]
          }}) {{
            block {{
              id
              name
              color
              orgId
            }}
          }}
        }}
        """
    )

    assert response == {
        "errors": [
            {
                "message": "Block time release start and end times are overlapping with existing block times.",
                "locations": [{"line": 3, "column": 11}],
                "path": ["blockCreate"],
            }
        ],
        "data": {"blockCreate": None},
    }

    dto = BlockQueryDto(org_ids=[org_id])
    blocks = await harness.block_store.query_blocks(dto=dto)
    assert len(blocks) == 0


@pytest.mark.asyncio
async def test_block_update(
    new_block: BlockModel, staff_0: StaffModel, staff_1: StaffModel
) -> None:
    assert new_block.surgeon_ids == [staff_0.id, staff_1.id]

    edited_block_name = "i_am_an_edited_block_name"
    edited_block_color = "#00FF00"
    result = harness.service_account_client.execute_graphql(
        f"""
    mutation {{
      blockUpdate(
        input: {{
          id: "{new_block.id}",
          name: "{edited_block_name}",
          color: "{edited_block_color}",
          blockTimes: [],
          siteIds: ["{harness.site_0.id}", "{harness.site_1.id}"],
          surgeonIds: ["{staff_0.id}", "{staff_1.id}"]
        }}
      ) {{
        block {{
          id
          name
          color
          surgeonIds
          siteIds
        }}
      }}
    }}
    """
    )

    assert result == {
        "data": {
            "blockUpdate": {
                "block": {
                    "id": str(new_block.id),
                    "name": edited_block_name,
                    "color": edited_block_color,
                    "siteIds": [str(harness.site_0.id), str(harness.site_1.id)],
                    "surgeonIds": [str(staff_0.id), str(staff_1.id)],
                }
            }
        }
    }


@pytest.mark.asyncio
async def test_block_update_with_block_times(
    new_block: BlockModel, room_id: str, block_name: str, block_color: str
) -> None:
    block_with_block_times = harness.service_account_client.execute_graphql(
        f"""
    mutation {{
      blockUpdate(
        input: {{
          id: "{new_block.id}",
          name: "{block_name}"
          color: "{block_color}"
          blockTimes: [{{
            startTime: "2021-01-01T09:00:00+0000",
            endTime: "2021-01-01T17:00:00+0000",
            roomId: "{room_id}"
            releases: [{{
              startTime: "2021-01-01T12:00:00+0000",
              endTime: "2021-01-01T17:00:00+0000",
              source: "component test update",
              reason: "I gotta go home before lunch",
              releasedTime: "2021-01-01T11:00:00+0000",
              unreleasedTime: "2021-01-01T11:00:00+0000",
              unreleasedSource: "component test update",
            }}]
          }}]
        }}
      ) {{
        block {{
          id
          name
          color
          blockTimes {{
            startTime
            endTime
            roomId
            releases(includeUnreleased: true) {{
              startTime
              endTime
              reason
              releasedTime
              unreleasedTime
              unreleasedSource
            }}
          }}
        }}
      }}
    }}
    """
    )["data"]["blockUpdate"]["block"]

    assert block_with_block_times == {
        "id": str(new_block.id),
        "name": block_name,
        "color": block_color,
        "blockTimes": [
            {
                "startTime": "2021-01-01T09:00:00+00:00",
                "endTime": "2021-01-01T17:00:00+00:00",
                "roomId": room_id,
                "releases": [
                    {
                        "startTime": "2021-01-01T12:00:00+00:00",
                        "endTime": "2021-01-01T17:00:00+00:00",
                        "reason": "I gotta go home before lunch",
                        "releasedTime": "2021-01-01T11:00:00+00:00",
                        "unreleasedTime": "2021-01-01T11:00:00+00:00",
                        "unreleasedSource": "component test update",
                    }
                ],
            }
        ],
    }


@pytest.mark.asyncio
async def test_block_update_with_block_time_releases_to_other_block(
    block_service: BlockService,
    new_block: BlockModel,
    room_id: str,
    block_name: str,
    block_color: str,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    mock_auth = MagicMock()
    mock_auth.requires = MagicMock()
    block_id = uuid4()
    general_block = await harness.block_store.create_block(
        BlockDataInput(
            id=block_id,
            name="General",
            org_id=new_block.org_id,
            color="#ABCDEF",
            surgeon_ids=[str(harness.staff_0.id)],
            block_times=[
                BlockTimeModel(
                    id=uuid4(),
                    block_id=block_id,
                    room_id=room_id_0,
                    start_time=start_time,
                    end_time=end_time,
                    releases=[],
                )
            ],
        )
    )

    block_with_block_times = harness.service_account_client.execute_graphql(
        f"""
    mutation {{
      blockUpdate(
        input: {{
          id: "{new_block.id}",
          name: "{block_name}"
          color: "{block_color}"
          blockTimes: [{{
            startTime: "2021-01-05T09:00:00+0000",
            endTime: "2021-01-05T17:00:00+0000",
            roomId: "{room_id}"
            releases: [{{
              startTime: "2021-01-05T12:00:00+0000",
              endTime: "2021-01-05T17:00:00+0000",
              source: "component test update",
              toBlock: "General",
              reason: "I gotta go home before lunch",
              releasedTime: "2021-01-05T11:00:00+0000",
              unreleasedTime: null,
              unreleasedSource: null,
            }}]
          }}]
        }}
      ) {{
        block {{
          id
          name
          color
          blockTimes {{
            id
            startTime
            endTime
            roomId
            releases(includeUnreleased: true) {{
              startTime
              endTime
              reason
              releasedTime
              unreleasedTime
              unreleasedSource
            }}
          }}
        }}
      }}
    }}
    """
    )["data"]["blockUpdate"]["block"]

    new_block_time = await block_service.get_block_times_for_block(block_id=general_block.id)
    assert len(new_block_time) == 2
    assert str(new_block_time[1].released_from) == block_with_block_times["blockTimes"][0]["id"]

    block_time_released = await harness.block_store.get_block_times_for_block(
        block_id=new_block.id, eager_load_releases=True
    )
    releases = block_time_released[0].releases
    assert len(releases) == 1
    assert releases[0].to_block_id == general_block.id


@pytest.mark.asyncio
async def test_archive_block(block_id: UUID, new_block: BlockModel) -> None:
    assert new_block.archived_time is None

    archived_block = harness.service_account_client.execute_graphql(
        f"""
    mutation {{
      blockArchive(
        id: "{block_id}"
      ) {{ block {{
        id
        archivedTime
      }} }}
    }}
    """
    )["data"]["blockArchive"]["block"]

    assert archived_block["archivedTime"] is not None


@pytest.mark.asyncio
async def test_unarchive_block(block_id: UUID, new_block: BlockModel) -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    mutation {{
      blockArchive(
        id: "{block_id}"
      ) {{ block {{
        id
        archivedTime
      }} }}
    }}
    """
    )
    archived_block = result["data"]["blockArchive"]["block"]
    assert archived_block["archivedTime"] is not None

    unarchived_block = harness.service_account_client.execute_graphql(
        f"""
    mutation {{
      blockUnarchive(
        id: "{block_id}"
      ) {{ block {{
        id
        archivedTime
      }} }}
    }}
    """
    )["data"]["blockUnarchive"]["block"]

    assert unarchived_block["archivedTime"] is None


@pytest.mark.asyncio
async def test_block_time_bulk_delete_duplicate(
    block_service: BlockService,
    org_id: str,
    room_id: str,
    block_name: str,
    block_color: str,
) -> None:
    block_id = uuid4()
    block_time_id1 = uuid4()
    block_time_id2 = uuid4()
    block_time_id3 = uuid4()

    await harness.block_store.create_block(
        BlockDataInput(
            id=block_id,
            name=block_name,
            org_id=org_id,
            color=block_color,
            surgeon_ids=[],
            block_times=[
                BlockTimeModel(
                    id=block_time_id1,
                    block_id=block_id,
                    room_id=room_id,
                    start_time=datetime(2021, 1, 1, 9, 0, 0),
                    end_time=datetime(2021, 1, 1, 17, 0, 0),
                    releases=[],
                ),
                BlockTimeModel(
                    id=block_time_id2,
                    block_id=block_id,
                    room_id=room_id,
                    start_time=datetime(2021, 1, 1, 9, 0, 0),
                    end_time=datetime(2021, 1, 1, 17, 0, 0),
                    releases=[],
                ),
                BlockTimeModel(
                    id=block_time_id3,
                    block_id=block_id,
                    room_id=room_id,
                    start_time=datetime(2021, 1, 2, 9, 0, 0),
                    end_time=datetime(2021, 1, 2, 17, 0, 0),
                    releases=[],
                ),
            ],
        )
    )

    block_times_before = await block_service.get_block_times_for_block(block_id=block_id)
    assert len(block_times_before) == 3

    result = harness.service_account_client.execute_graphql(
        f"""
        mutation {{
          blockTimeBulkDeleteDuplicate(
            input: {{
              orgId: "{org_id}"
            }}
          ) {{
            success
            deletedCount
          }}
        }}
        """
    )

    assert result["data"]["blockTimeBulkDeleteDuplicate"]["success"] is True
    assert result["data"]["blockTimeBulkDeleteDuplicate"]["deletedCount"] == 1

    block_times_after = await block_service.get_block_times_for_block(block_id=block_id)
    assert len(block_times_after) == 2

    block_time_ids_after = [str(bt.id) for bt in block_times_after]
    assert str(block_time_id1) in block_time_ids_after
    assert str(block_time_id3) in block_time_ids_after
    assert str(block_time_id2) not in block_time_ids_after


@pytest.mark.asyncio
async def test_block_time_bulk_delete_duplicate_does_not_delete_non_duplicates(
    block_service: BlockService,
    org_id: str,
    room_id: str,
    block_name: str,
    block_color: str,
) -> None:
    block_id = uuid4()
    block_time_id1 = uuid4()
    block_time_id2 = uuid4()

    await harness.block_store.create_block(
        BlockDataInput(
            id=block_id,
            name=block_name,
            org_id=org_id,
            color=block_color,
            surgeon_ids=[],
            block_times=[
                BlockTimeModel(
                    id=block_time_id1,
                    block_id=block_id,
                    room_id=room_id,
                    start_time=datetime(2021, 1, 1, 9, 0, 0),
                    end_time=datetime(2021, 1, 1, 17, 0, 0),
                    releases=[],
                ),
                BlockTimeModel(
                    id=block_time_id2,
                    block_id=block_id,
                    room_id=room_id,
                    start_time=datetime(2021, 1, 2, 9, 0, 0),
                    end_time=datetime(2021, 1, 2, 17, 0, 0),
                    releases=[],
                ),
            ],
        )
    )

    block_times_before = await block_service.get_block_times_for_block(block_id=block_id)
    assert len(block_times_before) == 2

    result = harness.service_account_client.execute_graphql(
        f"""
        mutation {{
          blockTimeBulkDeleteDuplicate(
            input: {{
              orgId: "{org_id}"
            }}
          ) {{
            success
            deletedCount
          }}
        }}
        """
    )

    assert result["data"]["blockTimeBulkDeleteDuplicate"]["success"] is True
    assert result["data"]["blockTimeBulkDeleteDuplicate"]["deletedCount"] == 0

    block_times_after = await block_service.get_block_times_for_block(block_id=block_id)
    assert len(block_times_after) == 2

    block_time_ids_after = [str(bt.id) for bt in block_times_after]
    assert str(block_time_id1) in block_time_ids_after
    assert str(block_time_id2) in block_time_ids_after
