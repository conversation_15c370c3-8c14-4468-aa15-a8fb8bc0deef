from datetime import datetime, timedelta, timezone
from uuid import uuid4
import pytest

from unittest.mock import <PERSON>M<PERSON>

from apella_cloud_api.dtos import RoomClosureQueryDto
from api_server.services.block.block_release_file_store import BlockReleaseFileStore
from api_server.services.block.block_service import BlockService
from api_server.services.closures.closure_service import ClosureService
from api_server.services.closures.closure_store import RoomClosure
from tests_component import harness
from api_server.services.block.block_models import (
    BlockProcessingStatus,
    BlockReleaseDataInput,
    BlockReleaseBulkCreateDataInput,
    BlockDataInput,
    BlockFile,
    BlockReleaseProcessedFileModel,
    BlockTypes,
    BlockTimeModel,
)
from api_server.services.block.block_release_processing_service import BlockReleaseProcessingService
from api_server.services.block.block_store import (
    RELEASE_BULK_IMPORT_BLOCK_TIME_NOT_FOUND,
    BlockModel,
)


@pytest.mark.asyncio
async def test_bulk_add_releases_match_correct_block_time_ids(
    block_release_processing_service: BlockReleaseProcessingService,
    new_block: BlockModel,
    block_name: str,
    room_id_0: str,
    room_id_1: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    releases = [
        BlockReleaseDataInput(
            id=uuid4(),
            release_reason="I'm a release reason",
            released_by="Alex",
            block_name=block_name,
            block_date=start_time,
            room_id=room_id_0,
            site_id=harness.site_0.id,
            room_name="Room 0",
            release_start_time=start_time,
            release_end_time=end_time,
            released_time=start_time,
            from_block_type=BlockTypes.SURGEON_BLOCK_TYPE,
            to_block_type=BlockTypes.UNKNOWN,
            timezone=harness.site_0.timezone,
        ),
        BlockReleaseDataInput(
            id=uuid4(),
            release_reason="I'm a release reason",
            released_by="Alex",
            block_name=block_name,
            block_date=start_time,
            room_id=room_id_1,
            site_id=harness.site_0.id,
            room_name="Room 1",
            release_start_time=start_time,
            release_end_time=end_time,
            released_time=start_time,
            from_block_type=BlockTypes.SURGEON_BLOCK_TYPE,
            to_block_type=BlockTypes.UNKNOWN,
            timezone=harness.site_0.timezone,
        ),
    ]

    input = BlockReleaseBulkCreateDataInput(
        releases=releases,
        org_id=new_block.org_id,
        timezone=harness.site_0.timezone,
    )

    mock_auth = MagicMock()
    mock_auth.requires = MagicMock()
    await block_release_processing_service.process_releases(
        releases=input.releases,
    )
    assert len(input.releases) == 2
    assert input.releases[0].block_time_id != input.releases[1].block_time_id
    assert input.releases[0].rejected_reason is None
    assert input.releases[1].rejected_reason is None
    assert input.releases[0].from_block_type == input.releases[1].from_block_type
    assert input.releases[0].from_block_type == BlockTypes.SURGEON_BLOCK_TYPE
    assert input.releases[0].to_block_type == BlockTypes.UNKNOWN
    assert input.releases[1].to_block_type == BlockTypes.UNKNOWN


@pytest.mark.asyncio
async def test_bulk_add_releases_matches_correct_block_times(
    block_release_processing_service: BlockReleaseProcessingService,
    new_block: BlockModel,
    block_name: str,
    room_id_0: str,
    room_id_1: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    released_start_time = datetime(2023, 7, 1, 7, 0, 0, tzinfo=timezone.utc)
    releases = [
        BlockReleaseDataInput(
            id=uuid4(),
            release_reason="Testing",
            released_by="Ami",
            block_name=block_name,
            block_date=start_time,
            room_id=room_id_0,
            site_id=harness.site_0.id,
            room_name="Room 0",
            release_start_time=released_start_time,
            release_end_time=end_time,
            released_time=released_start_time,
            timezone=harness.site_0.timezone,
        ),
    ]

    input = BlockReleaseBulkCreateDataInput(
        releases=releases,
        org_id=new_block.org_id,
        timezone=harness.site_0.timezone,
    )

    mock_auth = MagicMock()
    mock_auth.requires = MagicMock()
    await block_release_processing_service.process_releases(
        releases=input.releases,
    )
    assert len(input.releases) == 1
    # since released start time < block start time, exact release times wouldn't match any existing block times
    assert input.releases[0].rejected_reason is RELEASE_BULK_IMPORT_BLOCK_TIME_NOT_FOUND


@pytest.mark.asyncio
async def test_bulk_add_releases_match_correct_block_time_ids__case_insensitive(
    block_release_processing_service: BlockReleaseProcessingService,
    new_block: BlockModel,
    block_name: str,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    releases = [
        BlockReleaseDataInput(
            id=uuid4(),
            release_reason="I'm a release reason",
            released_by="Alex",
            block_name=block_name.upper(),
            block_date=start_time,
            room_id=room_id_0,
            site_id=harness.site_0.id,
            room_name="Room 0",
            release_start_time=start_time,
            release_end_time=end_time,
            released_time=start_time,
            timezone=harness.site_0.timezone,
        ),
    ]

    input = BlockReleaseBulkCreateDataInput(
        releases=releases,
        org_id=new_block.org_id,
        timezone=harness.site_0.timezone,
    )

    mock_auth = MagicMock()
    mock_auth.requires = MagicMock()
    await block_release_processing_service.process_releases(
        releases=input.releases,
    )
    assert len(input.releases) == 1
    assert input.releases[0].rejected_reason is None


@pytest.mark.asyncio
async def test_overlapping_block_times_are_valid_post_release(
    block_release_processing_service: BlockReleaseProcessingService,
    block_service: BlockService,
    new_block: BlockModel,
    general_block: BlockModel,
    block_name: str,
    block_time_id_0: str,
    room_id_0: str,
    room_id_1: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    await harness.block_store.create_block_time(
        BlockTimeModel(
            id=uuid4(),
            block_id=general_block.id,
            room_id=room_id_0,
            start_time=datetime(2023, 6, 1, 8, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2023, 6, 1, 9, 0, 0, tzinfo=timezone.utc),
            releases=[],
        )
    )

    releases = [
        BlockReleaseDataInput(
            id=uuid4(),
            release_reason="testing",
            released_by="Ami",
            block_name=new_block.name,
            block_date=start_time,
            to_block="General",
            to_block_type=BlockTypes.SURGEON_BLOCK_TYPE,
            from_block_type=BlockTypes.SURGEON_BLOCK_TYPE,
            room_id=room_id_0,
            site_id=harness.site_0.id,
            room_name="Room 0",
            release_start_time=start_time,
            release_end_time=end_time,
            released_time=start_time,
            timezone=harness.site_0.timezone,
        ),
    ]

    mock_auth = MagicMock()
    mock_auth.requires = MagicMock()

    await block_release_processing_service.process_releases(
        releases=releases,
    )
    assert len(releases) == 1
    assert releases[0].rejected_reason is None
    assert releases[0].to_block_id == general_block.id

    result = await block_service.get_block_times_for_block(block_id=general_block.id)

    assert len(result) == 2


@pytest.mark.asyncio
async def test_block_releases_from_valid_block_to_unavailable(
    block_release_processing_service: BlockReleaseProcessingService,
    closure_service: ClosureService,
    new_block: BlockModel,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    releases = [
        BlockReleaseDataInput(
            id=uuid4(),
            release_reason="testing",
            released_by="Ami",
            block_name=new_block.name,
            block_date=start_time,
            to_block="Unavailable",
            to_block_type=BlockTypes.UNAVAILABLE_BLOCK_TYPE,
            from_block_type=BlockTypes.SURGEON_BLOCK_TYPE,
            room_id=room_id_0,
            site_id=harness.site_0.id,
            room_name="Room 0",
            release_start_time=start_time,
            release_end_time=end_time,
            released_time=start_time,
            timezone=harness.site_0.timezone,
        ),
    ]

    mock_auth = MagicMock()
    mock_auth.requires = MagicMock()

    await block_release_processing_service.process_releases(
        releases=releases,
    )

    assert len(releases) == 1
    assert releases[0].rejected_reason is None
    assert releases[0].to_block_id is None

    closures = await closure_service.query_room_closures(
        query=RoomClosureQueryDto(room_id=room_id_0)
    )

    assert len(closures) == 1
    assert closures[0].room_id == room_id_0
    assert closures[0].start_time == start_time
    assert closures[0].end_time == end_time


@pytest.mark.asyncio
async def test_block_releases_from_unblocked_block_to_valid_block(
    block_release_processing_service: BlockReleaseProcessingService,
    block_service: BlockService,
    general_block: BlockModel,
    org_id: str,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    await harness.block_store.create_block_time(
        BlockTimeModel(
            id=uuid4(),
            block_id=general_block.id,
            room_id=room_id_0,
            start_time=datetime(2023, 6, 1, 8, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2023, 6, 1, 9, 0, 0, tzinfo=timezone.utc),
            releases=[],
        )
    )
    releases = [
        BlockReleaseDataInput(
            id=uuid4(),
            release_reason="testing",
            released_by="Ami",
            block_name="Unblocked",
            block_date=start_time,
            to_block="General",
            to_block_type=BlockTypes.GROUP_BLOCK_TYPE,
            from_block_type=BlockTypes.UNBLOCKED_BLOCK_TYPE,
            room_id=room_id_0,
            site_id=harness.site_0.id,
            room_name="Room 0",
            release_start_time=datetime(2025, 8, 1, 8, 0, 0, tzinfo=timezone.utc),
            release_end_time=datetime(2025, 8, 1, 9, 0, 0, tzinfo=timezone.utc),
            released_time=start_time,
            timezone=harness.site_0.timezone,
        ),
    ]

    mock_auth = MagicMock()
    mock_auth.requires = MagicMock()

    await block_release_processing_service.process_releases(
        releases=releases,
    )

    assert len(releases) == 1
    assert releases[0].rejected_reason is None
    assert releases[0].to_block_id == general_block.id

    result = await block_service.get_block_times_for_block(block_id=general_block.id)

    assert len(result) == 2


@pytest.mark.asyncio
async def test_block_releases_from_unavailable_block_to_valid_block(
    block_release_processing_service: BlockReleaseProcessingService,
    block_service: BlockService,
    closure_service: ClosureService,
    general_block: BlockModel,
    org_id: str,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    await harness.closure_store.create_room_closure(
        RoomClosure(room_id=room_id_0, start_time=start_time, end_time=end_time)
    )

    closures = await closure_service.query_room_closures(
        query=RoomClosureQueryDto(room_id=room_id_0)
    )

    assert len(closures) == 1

    await harness.block_store.create_block_time(
        BlockTimeModel(
            id=uuid4(),
            block_id=general_block.id,
            room_id=room_id_0,
            start_time=datetime(2023, 6, 1, 8, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2023, 6, 1, 9, 0, 0, tzinfo=timezone.utc),
            releases=[],
        )
    )

    releases = [
        BlockReleaseDataInput(
            id=uuid4(),
            release_reason="testing",
            released_by="Ami",
            block_name="Unavailable",
            block_date=start_time,
            to_block="General",
            to_block_type=BlockTypes.GROUP_BLOCK_TYPE,
            from_block_type=BlockTypes.UNAVAILABLE_BLOCK_TYPE,
            room_id=room_id_0,
            site_id=harness.site_0.id,
            room_name="Room 0",
            release_start_time=start_time,
            release_end_time=end_time,
            released_time=start_time,
            timezone=harness.site_0.timezone,
        ),
    ]

    mock_auth = MagicMock()
    mock_auth.requires = MagicMock()

    await block_release_processing_service.process_releases(
        releases=releases,
    )
    result = await block_service.get_block_times_for_block(block_id=general_block.id)
    closures = await closure_service.query_room_closures(
        query=RoomClosureQueryDto(room_id=room_id_0)
    )

    assert len(releases) == 1
    assert releases[0].rejected_reason is None
    assert releases[0].to_block_id == general_block.id
    assert len(closures) == 0
    assert len(result) == 2


@pytest.mark.asyncio
async def test_block_releases_from_unavailable_block_to_unblocked_block(
    block_release_processing_service: BlockReleaseProcessingService,
    closure_service: ClosureService,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    await harness.closure_store.create_room_closure(
        RoomClosure(room_id=room_id_0, start_time=start_time, end_time=end_time)
    )
    closures = await harness.closure_store.query_room_closures(
        room_id=room_id_0,
        site_id=None,
        min_end_time=start_time,
        max_start_time=end_time,
    )

    assert len(closures) == 1

    releases = [
        BlockReleaseDataInput(
            id=uuid4(),
            release_reason="testing",
            released_by="Ami",
            block_name="Unavailable",
            block_date=start_time,
            to_block="Unblocked",
            to_block_type=BlockTypes.UNBLOCKED_BLOCK_TYPE,
            from_block_type=BlockTypes.UNAVAILABLE_BLOCK_TYPE,
            room_id=room_id_0,
            room_name="Room 0",
            site_id=harness.site_0.id,
            release_start_time=start_time,
            release_end_time=end_time,
            released_time=start_time,
            timezone=harness.site_0.timezone,
        ),
    ]

    mock_auth = MagicMock()
    mock_auth.requires = MagicMock()

    await block_release_processing_service.process_releases(
        releases=releases,
    )
    closures = await closure_service.query_room_closures(
        query=RoomClosureQueryDto(room_id=room_id_0)
    )

    assert len(releases) == 1
    assert releases[0].rejected_reason is None
    assert releases[0].to_block_id is None
    assert len(closures) == 0


@pytest.mark.asyncio
async def test_block_releases_from_unblocked_block_to_unavailable_block(
    block_release_processing_service: BlockReleaseProcessingService,
    block_service: BlockService,
    closure_service: ClosureService,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    releases = [
        BlockReleaseDataInput(
            id=uuid4(),
            release_reason="testing",
            released_by="Ami",
            block_name="Unblocked",
            block_date=start_time,
            to_block="Unavailable",
            to_block_type=BlockTypes.UNAVAILABLE_BLOCK_TYPE,
            from_block_type=BlockTypes.UNBLOCKED_BLOCK_TYPE,
            room_id=room_id_0,
            room_name="Room 0",
            site_id=harness.site_0.id,
            release_start_time=start_time,
            release_end_time=end_time,
            released_time=start_time,
            timezone=harness.site_0.timezone,
        ),
    ]

    mock_auth = MagicMock()
    mock_auth.requires = MagicMock()

    await block_release_processing_service.process_releases(
        releases=releases,
    )
    closures = await closure_service.query_room_closures(
        query=RoomClosureQueryDto(room_id=room_id_0)
    )

    assert len(releases) == 1
    assert releases[0].rejected_reason is None
    assert releases[0].to_block_id is None
    assert len(closures) == 1
    assert closures[0].room_id == room_id_0
    assert closures[0].start_time == start_time
    assert closures[0].end_time == end_time


@pytest.mark.asyncio
async def test_releases_get_updated_when_reprocessed(
    block_release_processing_service: BlockReleaseProcessingService,
    closure_service: ClosureService,
    new_block: BlockModel,
    block_name: str,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    mock_auth = MagicMock()
    mock_auth.requires = MagicMock()

    input1 = BlockReleaseBulkCreateDataInput(
        releases=[
            BlockReleaseDataInput(
                id=uuid4(),
                release_reason="I'm a release reason",
                released_by="Ami",
                block_name=block_name.upper(),
                block_date=start_time,
                room_id=room_id_0,
                room_name="Room 0",
                release_start_time=start_time,
                release_end_time=end_time,
                released_time=start_time,
                timezone=harness.site_0.timezone,
            ),
        ],
        org_id=new_block.org_id,
        timezone=harness.site_0.timezone,
    )

    await block_release_processing_service.process_releases(
        releases=input1.releases,
    )

    input2 = BlockReleaseBulkCreateDataInput(
        releases=[
            BlockReleaseDataInput(
                id=uuid4(),
                release_reason="testing",
                released_by="Ami",
                block_name=block_name.upper(),
                block_date=start_time,
                to_block="Unavailable",
                to_block_type=BlockTypes.UNAVAILABLE_BLOCK_TYPE,
                from_block_type=BlockTypes.SURGEON_BLOCK_TYPE,
                room_id=room_id_0,
                site_id=harness.site_0.id,
                room_name="Room 0",
                timezone=harness.site_0.timezone,
                release_start_time=start_time,
                release_end_time=end_time,
                released_time=start_time,
            ),
        ],
        org_id=new_block.org_id,
        timezone=harness.site_0.timezone,
    )

    await block_release_processing_service.process_releases(
        releases=input2.releases,
    )

    assert len(input2.releases) == 1
    assert input2.releases[0].rejected_reason is None

    closures = await closure_service.query_room_closures(
        query=RoomClosureQueryDto(room_id=room_id_0)
    )

    assert len(closures) == 1
    assert closures[0].room_id == room_id_0
    assert closures[0].start_time == start_time
    assert closures[0].end_time == end_time


@pytest.mark.asyncio
async def test_block_times_are_only_generated_when_no_block_time_exists_for_that_room(
    block_release_processing_service: BlockReleaseProcessingService,
    block_service: BlockService,
    new_block: BlockModel,
    general_block: BlockModel,
    block_name: str,
    block_time_id_0: str,
    room_id_0: str,
    room_id_1: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    await harness.block_store.create_block_time(
        BlockTimeModel(
            id=uuid4(),
            block_id=general_block.id,
            room_id=room_id_0,
            start_time=start_time,
            end_time=end_time,
            releases=[],
        )
    )

    releases = [
        BlockReleaseDataInput(
            id=uuid4(),
            release_reason="testing",
            released_by="Ami",
            block_name=new_block.name,
            block_date=start_time,
            to_block="General",
            to_block_type=BlockTypes.SURGEON_BLOCK_TYPE,
            from_block_type=BlockTypes.SURGEON_BLOCK_TYPE,
            room_id=room_id_0,
            site_id=harness.site_0.id,
            room_name="Room 0",
            release_start_time=start_time,
            release_end_time=end_time,
            released_time=start_time,
            timezone=harness.site_0.timezone,
        ),
    ]

    mock_auth = MagicMock()
    mock_auth.requires = MagicMock()

    await block_release_processing_service.process_releases(
        releases=releases,
    )

    assert len(releases) == 1
    assert releases[0].rejected_reason is None
    assert releases[0].to_block_id == general_block.id

    result = await block_service.get_block_times_for_block(block_id=general_block.id)

    assert len(result) == 1


@pytest.mark.asyncio
async def setup_block_release_test(
    block_release_processor_service: BlockReleaseProcessingService,
    new_block: BlockModel,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
    use_invalid_room: bool = False,
    add_sequential_releases_from_start: bool = False,
    add_sequential_releases_from_end: bool = False,
) -> tuple[BlockReleaseProcessedFileModel, BlockReleaseBulkCreateDataInput]:
    """Setup common test infrastructure for block release tests."""
    block_id = uuid4()
    await harness.block_store.create_block(
        BlockDataInput(
            id=block_id,
            name="General",
            org_id=new_block.org_id,
            site_ids=[str(harness.site_0.id)],
            color="#ABCDEF",
            surgeon_ids=[str(harness.staff_0.id)],
            block_times=[
                BlockTimeModel(
                    id=uuid4(),
                    block_id=block_id,
                    room_id=room_id_0,
                    start_time=start_time - timedelta(hours=1),
                    end_time=start_time + timedelta(hours=1),
                    releases=[],
                )
            ],
        )
    )

    room_id = "room 2" if use_invalid_room else room_id_0
    room_name = "Room 2" if use_invalid_room else "Room 0"
    rejected_reason = "room not found" if use_invalid_room else None

    releases = [
        BlockReleaseDataInput(
            id=uuid4(),
            release_reason="first release",
            released_by="Ami",
            block_name=new_block.name,
            block_date=start_time,
            to_block="General",
            to_block_type=BlockTypes.SURGEON_BLOCK_TYPE,
            from_block_type=BlockTypes.SURGEON_BLOCK_TYPE,
            room_id=room_id,
            site_id=harness.site_0.id,
            room_name=room_name,
            release_start_time=start_time,
            release_end_time=end_time,
            released_time=start_time,
            row_number=1,
            external_id=str(uuid4()),
            rejected_reason=rejected_reason,
            timezone=harness.site_0.timezone,
        ),
    ]

    if add_sequential_releases_from_end or add_sequential_releases_from_start:
        releases.append(
            BlockReleaseDataInput(
                id=uuid4(),
                release_reason="second release",
                released_by="Ami",
                block_name="General",
                block_date=start_time,
                to_block=new_block.name,
                to_block_type=BlockTypes.SURGEON_BLOCK_TYPE,
                from_block_type=BlockTypes.SURGEON_BLOCK_TYPE,
                room_id=room_id,
                site_id=harness.site_0.id,
                room_name=room_name,
                release_start_time=start_time
                if add_sequential_releases_from_start
                else start_time + timedelta(hours=3),
                release_end_time=end_time
                if add_sequential_releases_from_end
                else end_time - timedelta(hours=3),
                released_time=start_time,
                row_number=2,
                external_id=str(uuid4()),
                rejected_reason=None,
                timezone=harness.site_0.timezone,
            )
        )
    input = BlockReleaseBulkCreateDataInput(
        releases=releases,
        org_id=new_block.org_id,
        timezone=harness.site_0.timezone,
    )

    file_id = await block_release_processor_service.save_and_process_file(
        BlockFile(
            file_name="file.csv",
            bucket_name="test-bucket",
            generation="1234",
            size="1000",
            md5_hash="1234",
            media_link="http://fake.test/file.csv",
            org_id=new_block.org_id,
        )
    )

    return file_id, input


@pytest.mark.asyncio
async def test_processed_block_release_rows_are_saved_and_updated(
    block_release_processing_service: BlockReleaseProcessingService,
    new_block: BlockModel,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    file_id, input = await setup_block_release_test(
        block_release_processor_service=block_release_processing_service,
        new_block=new_block,
        room_id_0=room_id_0,
        start_time=start_time,
        end_time=end_time,
    )
    await block_release_processing_service.save_transformed_block_release_rows(file_id.id, input)

    rows = await block_release_processing_service.get_block_release_file_rows(file_id.id)

    assert len(rows) == 1
    assert rows[0].room_id == room_id_0
    assert rows[0].room_name == "Room 0"
    assert rows[0].block_name == new_block.name
    assert rows[0].release_reason == "first release"
    assert rows[0].released_by == "Ami"
    assert rows[0].block_date == start_time
    assert rows[0].to_block_name == "General"
    assert rows[0].to_block_type == BlockTypes.SURGEON_BLOCK_TYPE
    assert rows[0].from_block_type == BlockTypes.SURGEON_BLOCK_TYPE
    assert rows[0].start_time == start_time
    assert rows[0].end_time == end_time
    assert rows[0].released_time == start_time
    assert rows[0].processing_status == BlockProcessingStatus.PENDING
    assert rows[0].rejected_reason is None
    assert rows[0].row_number == 1

    await block_release_processing_service.process_releases(
        releases=input.releases,
    )

    await block_release_processing_service.update_block_release_file_row_statuses(
        file_id.id,
        [
            BlockReleaseFileStore.BlockReleaseFileRowStatus(
                row_number=release.row_number,
                rejected_reason=release.rejected_reason,
            )
            for release in input.releases
            if release.row_number is not None
        ],
    )

    rows = await block_release_processing_service.get_block_release_file_rows(file_id.id)

    assert len(rows) == 1
    assert rows[0].processing_status == BlockProcessingStatus.PROCESSED
    assert rows[0].rejected_reason == ""


@pytest.mark.asyncio
async def test_rejected_during_transform_block_release_rows_are_saved_and_updated(
    block_release_processing_service: BlockReleaseProcessingService,
    new_block: BlockModel,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    file_id, input = await setup_block_release_test(
        block_release_processor_service=block_release_processing_service,
        new_block=new_block,
        room_id_0=room_id_0,
        start_time=start_time,
        end_time=end_time,
        use_invalid_room=True,
    )

    await block_release_processing_service.save_transformed_block_release_rows(file_id.id, input)

    rows = await block_release_processing_service.get_block_release_file_rows(file_id.id)

    assert len(rows) == 1
    assert rows[0].room_id == "room 2"  # invalid room
    assert rows[0].processing_status == BlockProcessingStatus.REJECTED
    assert rows[0].rejected_reason == "room not found"

    await block_release_processing_service.process_releases(
        releases=input.releases,
    )

    await block_release_processing_service.update_block_release_file_row_statuses(
        file_id.id,
        [
            BlockReleaseFileStore.BlockReleaseFileRowStatus(
                row_number=release.row_number,
                rejected_reason=release.rejected_reason,
            )
            for release in input.releases
            if release.row_number is not None
        ],
    )

    rows = await block_release_processing_service.get_block_release_file_rows(file_id.id)

    assert len(rows) == 1
    assert rows[0].processing_status == BlockProcessingStatus.REJECTED
    assert rows[0].rejected_reason == "room not found"


@pytest.mark.asyncio
async def test_rejected_during_save_block_release_rows_are_saved_and_updated(
    block_release_processing_service: BlockReleaseProcessingService,
    new_block: BlockModel,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    file_id, input = await setup_block_release_test(
        block_release_processor_service=block_release_processing_service,
        new_block=new_block,
        room_id_0=room_id_0,
        start_time=start_time,
        end_time=end_time,
    )
    await block_release_processing_service.save_transformed_block_release_rows(file_id.id, input)

    rows = await block_release_processing_service.get_block_release_file_rows(file_id.id)

    assert len(rows) == 1
    assert rows[0].processing_status == BlockProcessingStatus.PENDING

    await block_release_processing_service.process_releases(
        releases=input.releases,
    )

    # simulate a failure to save
    input.releases[0].rejected_reason = "failed to save"

    await block_release_processing_service.update_block_release_file_row_statuses(
        file_id.id,
        [
            BlockReleaseFileStore.BlockReleaseFileRowStatus(
                row_number=release.row_number,
                rejected_reason=release.rejected_reason,
            )
            for release in input.releases
            if release.row_number is not None
        ],
    )

    rows = await block_release_processing_service.get_block_release_file_rows(file_id.id)

    assert len(rows) == 1
    assert rows[0].processing_status == BlockProcessingStatus.REJECTED
    assert rows[0].rejected_reason == "failed to save"


@pytest.mark.asyncio
async def test_process_block_releases_for_date_range(
    block_release_processing_service: BlockReleaseProcessingService,
    block_service: BlockService,
    new_block: BlockModel,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    # create a new block with a different room
    file_id, release_input = await setup_block_release_test(
        block_release_processor_service=block_release_processing_service,
        new_block=new_block,
        room_id_0=room_id_0,
        start_time=start_time,
        end_time=end_time,
        add_sequential_releases_from_end=True,
    )
    await block_release_processing_service.save_transformed_block_release_rows(
        file_id.id, release_input
    )

    await block_release_processing_service.process_block_releases_for_date_range(
        room_ids=[room_id_0],
        start_date=start_time.date(),
        end_date=end_time.date(),
    )

    rows = await block_release_processing_service.get_block_release_file_rows(file_id.id)

    assert len(rows) == 2
    assert rows[0].processing_status == BlockProcessingStatus.PROCESSED
    assert rows[0].rejected_reason == ""
    assert rows[0].row_number == 1
    assert rows[1].processing_status == BlockProcessingStatus.PROCESSED
    assert rows[1].rejected_reason == ""
    assert rows[1].row_number == 2

    block_times = await block_service.query_block_times(
        room_ids=[room_id_0],
        min_end_time=start_time,
        max_start_time=end_time,
    )

    assert len(block_times) == 4

    releases = [
        release
        for release in await block_service.get_block_time_releases_for_block_times(
            block_time_ids=[str(block_time.id) for block_time in block_times],
            active_only=True,
        )
    ]

    assert len(releases) == 2


@pytest.mark.asyncio
async def test_existing_block_release(
    block_release_processing_service: BlockReleaseProcessingService,
    new_block: BlockModel,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    """Test that the system correctly handles an existing block release in the database."""
    # Create initial block release
    file_id, release_input = await setup_block_release_test(
        block_release_processor_service=block_release_processing_service,
        new_block=new_block,
        room_id_0=room_id_0,
        start_time=start_time,
        end_time=end_time,
    )

    # Save and process the initial release
    await block_release_processing_service.save_transformed_block_release_rows(
        file_id.id, release_input
    )

    await block_release_processing_service.process_block_releases_for_date_range(
        room_ids=[room_id_0],
        start_date=start_time.date(),
        end_date=end_time.date(),
    )

    # Verify initial release was processed
    initial_rows = await block_release_processing_service.get_block_release_file_rows(file_id.id)
    assert len(initial_rows) == len(release_input.releases)
    assert all(row.processing_status == BlockProcessingStatus.PROCESSED for row in initial_rows)

    # Save and process the releases again, marking the rows "pending"
    await block_release_processing_service.save_transformed_block_release_rows(
        file_id.id, release_input
    )

    # verify the rows were marked "pending"
    rows = await block_release_processing_service.get_block_release_file_rows(file_id.id)
    assert len(rows) == len(release_input.releases)
    assert all(row.processing_status == BlockProcessingStatus.PENDING for row in rows)

    # process the releases again, this time with the releases already in the database
    await block_release_processing_service.process_block_releases_for_date_range(
        room_ids=[room_id_0],
        start_date=start_time.date(),
        end_date=end_time.date(),
    )

    # Verify the release was processed the second time
    rows = await block_release_processing_service.get_block_release_file_rows(file_id.id)
    assert len(rows) > 0
    assert all(row.processing_status == BlockProcessingStatus.PROCESSED for row in rows)


@pytest.mark.asyncio
async def test_process_block_releases_with_missing_block_times(
    block_release_processing_service: BlockReleaseProcessingService,
    new_block: BlockModel,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    """Test processing block releases when there are missing block times."""

    # create a block without any block times
    block_id = uuid4()
    await harness.block_store.create_block(
        BlockDataInput(
            id=block_id,
            name="Test Block with Missing Times",
            org_id=new_block.org_id,
            site_ids=[str(harness.site_0.id)],
            color="#ABCDEF",
            surgeon_ids=[],
            block_times=[],
        )
    )

    # create a file with the release
    file = await block_release_processing_service.save_and_process_file(
        BlockFile(
            file_name="test.csv",
            bucket_name="test-bucket",
            generation="1234",
            size="1000",
            md5_hash="1234",
            media_link="http://fake.test/file.csv",
            org_id=new_block.org_id,
        )
    )

    # create a release for the block
    release = BlockReleaseDataInput(
        id=uuid4(),
        block_release_file_id=file.id,
        row_number=1,
        release_reason="test release",
        released_by="test user",
        block_name="Test Block with Missing Times",
        room_name="Room 0",
        room_id=room_id_0,
        site_id=harness.site_0.id,
        block_date=start_time,
        release_start_time=start_time,
        release_end_time=end_time,
        released_time=start_time - timedelta(days=10),
        timezone=harness.site_0.timezone,
    )

    # save the release
    await block_release_processing_service.save_transformed_block_release_rows(
        file.id,
        BlockReleaseBulkCreateDataInput(
            releases=[release],
            org_id=new_block.org_id,
            timezone=harness.site_0.timezone,
        ),
    )

    # process the release
    await block_release_processing_service.process_releases([release])

    # verify the release was rejected with the right error message
    assert release.rejected_reason == str(RELEASE_BULK_IMPORT_BLOCK_TIME_NOT_FOUND)

    # verify the status was updated to rejected
    rows = await block_release_processing_service.get_block_release_file_rows(file.id)
    assert len(rows) == 1
    assert rows[0].processing_status == BlockProcessingStatus.REJECTED
    assert rows[0].rejected_reason == str(RELEASE_BULK_IMPORT_BLOCK_TIME_NOT_FOUND)


@pytest.mark.asyncio
async def test_block_releases_from_on_hold_to_valid_block(
    block_release_processing_service: BlockReleaseProcessingService,
    block_service: BlockService,
    on_hold_block: BlockModel,
    general_block: BlockModel,
    block_time_id_0: str,
    org_id: str,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
) -> None:
    releases = [
        BlockReleaseDataInput(
            id=uuid4(),
            release_reason="testing",
            released_by="Ami",
            block_name="On Hold",
            block_date=start_time,
            to_block="General",
            to_block_type=BlockTypes.GROUP_BLOCK_TYPE,
            from_block_type=BlockTypes.ON_HOLD_BLOCK_TYPE,
            room_id=room_id_0,
            site_id=harness.site_0.id,
            room_name="Room 0",
            release_start_time=start_time,
            release_end_time=end_time,
            released_time=start_time,
            timezone=harness.site_0.timezone,
        ),
    ]

    await block_release_processing_service.process_releases(
        releases=releases,
    )

    assert len(releases) == 1
    assert releases[0].rejected_reason is None
    assert releases[0].to_block_id == general_block.id
    assert releases[0].block_time_id == block_time_id_0

    result = await block_service.get_block_times_for_block(block_id=general_block.id)
    assert len(result) == 1
    assert result[0].room_id == room_id_0
    assert result[0].start_time == start_time
    assert result[0].end_time == end_time


@pytest.mark.asyncio
async def test_block_releases_from_valid_to_on_hold(
    block_release_processing_service: BlockReleaseProcessingService,
    block_service: BlockService,
    org_id: str,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
    on_hold_block: BlockModel,
) -> None:
    block_id = uuid4()
    await harness.block_store.create_block(
        BlockDataInput(
            id=block_id,
            name="General",
            org_id=org_id,
            color="#ABCDEF",
            surgeon_ids=[str(harness.staff_0.id)],
            site_ids=[str(harness.site_0.id)],
            block_times=[
                BlockTimeModel(
                    id=uuid4(),
                    block_id=block_id,
                    room_id=room_id_0,
                    start_time=start_time,
                    end_time=end_time,
                    releases=[],
                )
            ],
        )
    )
    releases = [
        BlockReleaseDataInput(
            id=uuid4(),
            release_reason="testing",
            released_by="Ami",
            block_name="General",
            block_date=start_time,
            to_block="On Hold",
            to_block_type=BlockTypes.ON_HOLD_BLOCK_TYPE,
            from_block_type=BlockTypes.GROUP_BLOCK_TYPE,
            room_id=room_id_0,
            site_id=harness.site_0.id,
            room_name="Room 0",
            release_start_time=start_time,
            release_end_time=end_time,
            released_time=start_time,
            timezone=harness.site_0.timezone,
        ),
    ]

    await block_release_processing_service.process_releases(
        releases=releases,
    )

    assert len(releases) == 1
    assert releases[0].rejected_reason is None
    assert releases[0].to_block_id == on_hold_block.id

    result = await block_service.get_block_times_for_block(block_id=on_hold_block.id)
    assert len(result) == 1
    assert result[0].room_id == room_id_0
    assert result[0].start_time == start_time
    assert result[0].end_time == end_time


@pytest.mark.asyncio
async def test_block_releases_from_unblocked_to_on_hold(
    block_release_processing_service: BlockReleaseProcessingService,
    block_service: BlockService,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
    on_hold_block: BlockModel,
) -> None:
    releases = [
        BlockReleaseDataInput(
            id=uuid4(),
            release_reason="testing",
            released_by="Ami",
            block_name="Unblocked",
            block_date=start_time,
            to_block="On Hold",
            to_block_type=BlockTypes.ON_HOLD_BLOCK_TYPE,
            from_block_type=BlockTypes.UNBLOCKED_BLOCK_TYPE,
            room_id=room_id_0,
            site_id=harness.site_0.id,
            room_name="Room 0",
            release_start_time=start_time,
            release_end_time=end_time,
            released_time=start_time,
            timezone=harness.site_0.timezone,
        ),
    ]

    await block_release_processing_service.process_releases(
        releases=releases,
    )

    assert len(releases) == 1
    assert releases[0].rejected_reason is None
    assert releases[0].to_block_id == on_hold_block.id

    result = await block_service.get_block_times_for_block(block_id=on_hold_block.id)
    assert len(result) == 1
    assert result[0].room_id == room_id_0
    assert result[0].start_time == start_time
    assert result[0].end_time == end_time


@pytest.mark.asyncio
async def test_block_releases_from_unavailable_to_on_hold(
    block_release_processing_service: BlockReleaseProcessingService,
    block_service: BlockService,
    closure_service: ClosureService,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
    on_hold_block: BlockModel,
) -> None:
    # First create a room closure to represent unavailable state
    await harness.closure_store.create_room_closure(
        RoomClosure(
            id=uuid4(),
            room_id=room_id_0,
            start_time=start_time,
            end_time=end_time,
        )
    )

    releases = [
        BlockReleaseDataInput(
            id=uuid4(),
            release_reason="testing",
            released_by="Ami",
            block_name="Unavailable",
            block_date=start_time,
            to_block="On Hold",
            to_block_type=BlockTypes.ON_HOLD_BLOCK_TYPE,
            from_block_type=BlockTypes.UNAVAILABLE_BLOCK_TYPE,
            room_id=room_id_0,
            site_id=harness.site_0.id,
            room_name="Room 0",
            release_start_time=start_time,
            release_end_time=end_time,
            released_time=start_time,
            timezone=harness.site_0.timezone,
        ),
    ]

    await block_release_processing_service.process_releases(
        releases=releases,
    )

    assert len(releases) == 1
    assert releases[0].rejected_reason is None
    assert releases[0].to_block_id == on_hold_block.id

    result = await block_service.get_block_times_for_block(block_id=on_hold_block.id)
    assert len(result) == 1
    assert result[0].room_id == room_id_0
    assert result[0].start_time == start_time
    assert result[0].end_time == end_time

    closures = await closure_service.query_room_closures(
        query=RoomClosureQueryDto(room_id=room_id_0)
    )
    assert len(closures) == 0


@pytest.mark.asyncio
async def test_block_releases_from_on_hold_to_unavailable(
    block_release_processing_service: BlockReleaseProcessingService,
    block_service: BlockService,
    closure_service: ClosureService,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
    on_hold_block: BlockModel,
) -> None:
    releases = [
        BlockReleaseDataInput(
            id=uuid4(),
            release_reason="testing",
            released_by="Ami",
            block_name="On Hold",
            block_date=start_time,
            to_block="Unavailable",
            to_block_type=BlockTypes.UNAVAILABLE_BLOCK_TYPE,
            from_block_type=BlockTypes.ON_HOLD_BLOCK_TYPE,
            room_id=room_id_0,
            site_id=harness.site_0.id,
            room_name="Room 0",
            release_start_time=start_time,
            release_end_time=end_time,
            released_time=start_time,
            timezone=harness.site_0.timezone,
        ),
    ]

    await block_release_processing_service.process_releases(
        releases=releases,
    )

    assert len(releases) == 1
    assert releases[0].rejected_reason is None
    assert releases[0].to_block_id is None

    closures = await closure_service.query_room_closures(
        query=RoomClosureQueryDto(room_id=room_id_0)
    )
    assert len(closures) == 1
    assert closures[0].room_id == room_id_0
    assert closures[0].start_time == start_time
    assert closures[0].end_time == end_time


@pytest.mark.asyncio
async def test_block_releases_from_on_hold_to_unblocked(
    block_release_processing_service: BlockReleaseProcessingService,
    block_service: BlockService,
    room_id_0: str,
    start_time: datetime,
    end_time: datetime,
    on_hold_block: BlockModel,
) -> None:
    releases = [
        BlockReleaseDataInput(
            id=uuid4(),
            release_reason="testing",
            released_by="Ami",
            block_name="On Hold",
            block_date=start_time,
            to_block="Unblocked",
            to_block_type=BlockTypes.UNBLOCKED_BLOCK_TYPE,
            from_block_type=BlockTypes.ON_HOLD_BLOCK_TYPE,
            room_id=room_id_0,
            site_id=harness.site_0.id,
            room_name="Room 0",
            release_start_time=start_time,
            release_end_time=end_time,
            released_time=start_time,
            timezone=harness.site_0.timezone,
        ),
    ]

    await block_release_processing_service.process_releases(
        releases=releases,
    )

    assert len(releases) == 1
    assert releases[0].rejected_reason is None
    assert releases[0].to_block_id is None
