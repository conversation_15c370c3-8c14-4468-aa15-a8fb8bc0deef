from uuid import uuid4
from api_server.services.block.block_models import BlockDataInput, BlockTimeModel
from api_server.services.block_utilization.block_utilization_store import BlockTimeOverrideModel
import pytest

from datetime import datetime, time, timezone
from tests_component import harness


@pytest.mark.asyncio
async def test_get_block_time_override_history() -> None:
    user_id = harness.site_0_scoped_user.user_id
    block_date = datetime(2025, 2, 21)
    site = harness.site_0
    room_0 = harness.room_0
    org = harness.org_0
    block_id = uuid4()
    block_time_id = uuid4()
    await harness.block_store.create_block(
        BlockDataInput(
            id=block_id,
            name="Test Block",
            color="#FFFFFF",
            org_id=org.id,
            site_ids=[str(site.id)],
            surgeon_ids=[str(harness.staff_0.id)],
            block_times=[
                BlockTimeModel(
                    id=block_time_id,
                    block_id=block_id,
                    room_id=room_0.id,
                    start_time=datetime.combine(block_date, time(12), tzinfo=timezone.utc),
                    end_time=datetime.combine(block_date, time(15), tzinfo=timezone.utc),
                ),
            ],
        )
    )

    await harness.block_utilization_store.upsert_block_time_overrides(
        block_time_overrides=[
            BlockTimeOverrideModel(
                block_time_id=block_time_id, block_time_minutes=10, user_id=user_id
            )
        ]
    )

    await harness.block_utilization_store.upsert_block_time_overrides(
        block_time_overrides=[
            BlockTimeOverrideModel(
                block_time_id=block_time_id, block_time_minutes=100, user_id=user_id
            )
        ]
    )

    results = await harness.block_utilization_store.get_block_time_override_history(
        block_time_id=block_time_id,
    )
    history_block_time_minutes = {result.block_time_minutes for result in results}
    expected_block_time_minutes = {10, 100}

    assert history_block_time_minutes == expected_block_time_minutes
