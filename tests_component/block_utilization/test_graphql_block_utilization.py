from uuid import UUID, uuid4
import uuid
from api_server.services.block_utilization.block_utilization_store import BlockTimeOverrideModel
from api_server.services.case.case_staff_store import PRIMARY_SURGEON_ROLES, CaseStaffModel
from api_server.services.case.case_status import SCHEDULED
from api_server.services.case.case_store import Case
from api_server.services.case_to_block.case_to_block_store import CaseToBlockOverrideModel
from api_server.services.events.event_models import EventModel
from api_server.services.events.source_type import PREDICTION
from api_server.services.phases.phase_store import PhaseModel, PhaseStatus
from api_server.services.phases.source_type import UNIFIED
from api_server.services.room.room_store import RoomModel
from sqlalchemy.ext.asyncio import AsyncSession

import pytest

from datetime import datetime, time, timedelta, timezone
from tests_component import harness
from api_server.services.block.block_models import (
    BlockDataInput,
    BlockReleaseDataInput,
    BlockTimeModel,
)

GET_BLOCK_UTILIZATIONS_FOR_SITE = """
  query BlockUtilization(
    $minDate: DateTime!
    $maxDate: DateTime!
    $siteId: String!
  ) {
    blockUtilizations(
      query: {
        minDate: $minDate
        maxDate: $maxDate
        siteId: $siteId
      }
    ) {
        utilizedSeconds
        totalActualCaseSeconds
        utilizedScheduledSeconds
        totalScheduledCaseSeconds
        availableSeconds
        totalBlockSeconds
        blockId
        date
        availableSecondsByBlockTimeId {
            blockTimeId
            availableSeconds
            overridden
        }
        casesForBlockDay {
            caseId
        }
    }
  }
"""


@pytest.mark.asyncio
async def test_get_block_utilization_requires_auth() -> None:
    site = harness.site_0
    result = harness.no_auth_client.execute_graphql(
        GET_BLOCK_UTILIZATIONS_FOR_SITE,
        variables={
            "minDate": datetime(2025, 2, 21).isoformat(),
            "maxDate": datetime(2025, 2, 22).isoformat(),
            "siteId": site.id,
        },
    )

    assert result["data"]["blockUtilizations"] is None
    assert result["errors"][0]["message"] == "Not Authorized: No authorization found"
    assert result["errors"][0]["path"] == ["blockUtilizations"]


@pytest.mark.asyncio
async def test_get_block_utilizations_for_site_with_valid_release(
    async_session: AsyncSession,
) -> None:
    site = harness.site_0
    room_1 = harness.room_1
    org = harness.org_0
    today = datetime.now(timezone.utc)

    block_id = uuid4()
    block_time_id_1 = uuid4()
    await harness.block_store.create_block(
        BlockDataInput(
            id=block_id,
            name="Test Block",
            color="#FFFFFF",
            org_id=org.id,
            site_ids=[str(site.id)],
            surgeon_ids=[str(harness.staff_0.id)],
            block_times=[
                BlockTimeModel(
                    id=block_time_id_1,
                    block_id=block_id,
                    room_id=room_1.id,
                    start_time=datetime.combine(today, time(12), tzinfo=timezone.utc),
                    end_time=datetime.combine(today, time(15), tzinfo=timezone.utc),
                ),
            ],
        )
    )
    release_just_in_time = BlockReleaseDataInput(
        id=str(uuid4()),
        block_time_id=block_time_id_1,
        release_reason="I was released just in time",
        released_by="Oracle",
        block_name="Test Block",
        block_date=datetime.combine(today, time(12), tzinfo=timezone.utc),
        timezone="utc",
        room_id=room_1.id,
        room_name=room_1.name,
        release_start_time=datetime.combine(today, time(12), tzinfo=timezone.utc),
        release_end_time=datetime.combine(today, time(15), tzinfo=timezone.utc),
        released_time=datetime.combine(today + timedelta(days=-10), time(12), tzinfo=timezone.utc),
    )

    await harness.block_store.bulk_add_block_releases([release_just_in_time])

    result_after_release = harness.site_0_scoped_client.execute_graphql(
        GET_BLOCK_UTILIZATIONS_FOR_SITE,
        variables={
            "minDate": today.isoformat(),
            "maxDate": today.isoformat(),
            "siteId": site.id,
        },
    )

    assert len(result_after_release["data"]["blockUtilizations"]) == 1
    assert result_after_release["data"]["blockUtilizations"] == [
        {
            "totalActualCaseSeconds": 0,
            "utilizedSeconds": 0,
            "totalScheduledCaseSeconds": 0,
            "utilizedScheduledSeconds": 0,
            # 3 hours * 60 min/hr (release happened and it counted towards the totalBlockSeconds and not availableSeconds)
            "totalBlockSeconds": 10800,
            "availableSeconds": 0,
            "blockId": str(block_id),
            "date": today.strftime("%Y-%m-%d"),
            "casesForBlockDay": [],
            "availableSecondsByBlockTimeId": [],
        },
    ]


@pytest.mark.asyncio
async def test_get_block_utilizations_for_site_with_invalid_release(
    async_session: AsyncSession,
) -> None:
    site = harness.site_0
    room_0 = harness.room_0
    org = harness.org_0
    today = datetime.now(timezone.utc)

    block_id = uuid4()
    block_time_id_0 = uuid4()
    await harness.block_store.create_block(
        BlockDataInput(
            id=block_id,
            name="Test Block",
            color="#FFFFFF",
            org_id=org.id,
            site_ids=[str(site.id)],
            surgeon_ids=[str(harness.staff_0.id)],
            block_times=[
                BlockTimeModel(
                    id=block_time_id_0,
                    block_id=block_id,
                    room_id=room_0.id,
                    start_time=datetime.combine(today, time(12), tzinfo=timezone.utc),
                    end_time=datetime.combine(today, time(15), tzinfo=timezone.utc),
                )
            ],
        )
    )

    release_too_late = BlockReleaseDataInput(
        id=str(uuid4()),
        release_reason="I was released too late to count",
        released_by="Oracle",
        block_name="Test Block",
        block_time_id=block_time_id_0,
        block_date=datetime.combine(today, time(12), tzinfo=timezone.utc),
        timezone="utc",
        room_id=room_0.id,
        room_name=room_0.name,
        release_start_time=datetime.combine(today, time(12), tzinfo=timezone.utc),
        release_end_time=datetime.combine(today, time(15), tzinfo=timezone.utc),
        released_time=datetime.combine(today + timedelta(days=-2), time(12), tzinfo=timezone.utc),
    )

    await harness.block_store.bulk_add_block_releases([release_too_late])

    result_after_release = harness.site_0_scoped_client.execute_graphql(
        GET_BLOCK_UTILIZATIONS_FOR_SITE,
        variables={
            "minDate": today.isoformat(),
            "maxDate": today.isoformat(),
            "siteId": site.id,
        },
    )

    assert len(result_after_release["data"]["blockUtilizations"]) == 1
    assert result_after_release["data"]["blockUtilizations"] == [
        {
            "totalScheduledCaseSeconds": 0,
            "totalActualCaseSeconds": 0,
            "utilizedSeconds": 0,
            "utilizedScheduledSeconds": 0,
            # 3 hours * 60 min/hr * 60 sec/min (no releases so totalBlockSeconds=availableSeconds)
            "totalBlockSeconds": 10800,
            "availableSeconds": 10800,
            "blockId": str(block_id),
            "date": today.strftime("%Y-%m-%d"),
            "casesForBlockDay": [],
            "availableSecondsByBlockTimeId": [
                {
                    "blockTimeId": str(block_time_id_0),
                    "availableSeconds": 10800,
                    "overridden": False,
                }
            ],
        }
    ]


@pytest.mark.asyncio
async def test_get_block_utilizations_for_site_ignore_cancelled_case(
    async_session: AsyncSession,
) -> None:
    site = harness.site_0
    room_0 = harness.room_0
    org = harness.org_0
    today = datetime.now(timezone.utc)

    # overlapping case with block time 0 but case is cancelled
    await _create_mock_case(
        room=room_0,
        start_time=datetime.combine(today, time(14), tzinfo=timezone.utc),
        end_time=datetime.combine(today, time(16), tzinfo=timezone.utc),
        session=async_session,
        surgeon_id=harness.staff_0.id,
        status="cancelled",
    )

    block_id = uuid4()
    block_time_id_0 = uuid4()
    await harness.block_store.create_block(
        BlockDataInput(
            id=block_id,
            name="Test Block",
            color="#FFFFFF",
            org_id=org.id,
            site_ids=[str(site.id)],
            surgeon_ids=[str(harness.staff_0.id)],
            block_times=[
                BlockTimeModel(
                    id=block_time_id_0,
                    block_id=block_id,
                    room_id=room_0.id,
                    start_time=datetime.combine(today, time(12), tzinfo=timezone.utc),
                    end_time=datetime.combine(today, time(15), tzinfo=timezone.utc),
                ),
            ],
        )
    )

    expected = [
        {
            # all 0 becaue case was cancelled
            "totalScheduledCaseSeconds": 0,
            "totalActualCaseSeconds": 0,
            "utilizedSeconds": 0,
            "utilizedScheduledSeconds": 0,
            # 3 hours * 60 min/hr * 60 sec/min (no releases so totalBlockSeconds=availableSeconds)
            "totalBlockSeconds": 10800,
            "availableSeconds": 10800,
            "blockId": str(block_id),
            "date": today.strftime("%Y-%m-%d"),
            "casesForBlockDay": [],
            "availableSecondsByBlockTimeId": [
                {
                    "blockTimeId": str(block_time_id_0),
                    "availableSeconds": 10800,
                    "overridden": False,
                }
            ],
        }
    ]

    result = harness.site_0_scoped_client.execute_graphql(
        GET_BLOCK_UTILIZATIONS_FOR_SITE,
        variables={
            "minDate": today.isoformat(),
            "maxDate": today.isoformat(),
            "siteId": site.id,
        },
    )

    assert len(result["data"]["blockUtilizations"]) == 1
    assert result["data"]["blockUtilizations"] == expected


@pytest.mark.asyncio
async def test_get_block_utilizations_for_site_overridden_cancelled_case(
    async_session: AsyncSession,
) -> None:
    site = harness.site_0
    room_0 = harness.room_0
    org = harness.org_0
    today = datetime.now(timezone.utc)

    # overlapping case with block time 0 but case is cancelled
    cancelled_case_id = await _create_mock_case(
        room=room_0,
        start_time=datetime.combine(today, time(14), tzinfo=timezone.utc),
        end_time=datetime.combine(today, time(16), tzinfo=timezone.utc),
        session=async_session,
        surgeon_id=harness.staff_0.id,
        status="canceled",
    )

    block_id = uuid4()
    block_time_id_0 = uuid4()
    await harness.block_store.create_block(
        BlockDataInput(
            id=block_id,
            name="Test Block",
            color="#FFFFFF",
            org_id=org.id,
            site_ids=[str(site.id)],
            surgeon_ids=[str(harness.staff_0.id)],
            block_times=[
                BlockTimeModel(
                    id=block_time_id_0,
                    block_id=block_id,
                    room_id=room_0.id,
                    start_time=datetime.combine(today, time(12), tzinfo=timezone.utc),
                    end_time=datetime.combine(today, time(15), tzinfo=timezone.utc),
                ),
            ],
        )
    )

    # create override for cancelled case
    await harness.case_to_block_store.upsert_case_to_block_overrides(
        case_to_block_overrides=[
            CaseToBlockOverrideModel(
                block_date=today.date(),
                case_id=cancelled_case_id,
                block_id=str(block_id),
                utilized_procedure_minutes=120,  # use scheduled time
                utilized_turnover_minutes=0,
                user_id=harness.site_0_scoped_user.user_id,
            )
        ]
    )

    result = harness.site_0_scoped_client.execute_graphql(
        GET_BLOCK_UTILIZATIONS_FOR_SITE,
        variables={
            "minDate": today.isoformat(),
            "maxDate": today.isoformat(),
            "siteId": site.id,
        },
    )

    assert len(result["data"]["blockUtilizations"]) == 1
    assert result["data"]["blockUtilizations"] == [
        {
            "totalScheduledCaseSeconds": 120 * 60,
            "totalActualCaseSeconds": 0,  # cancelled case has 0 actual minutes
            "utilizedSeconds": 120 * 60,
            "utilizedScheduledSeconds": 120 * 60,
            # 3 hours * 60 min/hr * 60 sec/min (no releases so totalBlockSeconds=availableSeconds)
            "totalBlockSeconds": 10800,
            "availableSeconds": 10800,
            "blockId": str(block_id),
            "date": today.strftime("%Y-%m-%d"),
            "casesForBlockDay": [{"caseId": cancelled_case_id}],
            "availableSecondsByBlockTimeId": [
                {
                    "blockTimeId": str(block_time_id_0),
                    "availableSeconds": 10800,
                    "overridden": False,
                }
            ],
        }
    ]


@pytest.mark.asyncio
async def test_get_block_utilizations_for_site_with_no_blocks(async_session: AsyncSession) -> None:
    site = harness.site_0
    room_0 = harness.room_0
    today = datetime.now(timezone.utc)

    await _create_mock_case_and_actual_case(
        room=room_0,
        start_time=datetime.combine(today, time(14), tzinfo=timezone.utc),
        end_time=datetime.combine(today, time(16), tzinfo=timezone.utc),
        session=async_session,
        surgeon_id=harness.staff_0.id,
        status=SCHEDULED,
    )
    result = harness.site_0_scoped_client.execute_graphql(
        GET_BLOCK_UTILIZATIONS_FOR_SITE,
        variables={
            "minDate": today.isoformat(),
            "maxDate": today.isoformat(),
            "siteId": site.id,
        },
    )

    assert result["data"]["blockUtilizations"] == []


@pytest.mark.asyncio
async def test_get_block_utilizations_for_site_overridden_block_time(
    async_session: AsyncSession,
) -> None:
    today = datetime.now(timezone.utc)

    block_id = uuid4()
    block_time_id_0 = uuid4()
    await harness.block_store.create_block(
        BlockDataInput(
            id=block_id,
            name="Test Block",
            color="#FFFFFF",
            org_id=harness.org_0.id,
            site_ids=[str(harness.site_0.id)],
            surgeon_ids=[str(harness.staff_0.id)],
            block_times=[
                BlockTimeModel(
                    id=block_time_id_0,
                    block_id=block_id,
                    room_id=harness.room_0.id,
                    start_time=datetime.combine(today, time(12), tzinfo=timezone.utc),
                    end_time=datetime.combine(today, time(15), tzinfo=timezone.utc),
                ),
            ],
        )
    )

    # create override for cancelled case
    await harness.block_utilization_store.upsert_block_time_overrides(
        block_time_overrides=[
            BlockTimeOverrideModel(
                block_time_id=block_time_id_0,
                block_time_minutes=10,
                user_id=harness.site_0_scoped_user.user_id,
            )
        ]
    )

    expected = [
        {
            "totalScheduledCaseSeconds": 0,
            "totalActualCaseSeconds": 0,
            "utilizedSeconds": 0,
            "utilizedScheduledSeconds": 0,
            # 3 hours * 60 min/hr * 60 sec/min (no releases so totalBlockSeconds=availableSeconds)
            "totalBlockSeconds": 10800,
            # overridden 10 minutes here for availableSeconds
            "availableSeconds": 10 * 60,
            "blockId": str(block_id),
            "date": today.strftime("%Y-%m-%d"),
            "casesForBlockDay": [],
            "availableSecondsByBlockTimeId": [
                {
                    "blockTimeId": str(block_time_id_0),
                    "availableSeconds": 10 * 60,
                    "overridden": True,
                }
            ],
        }
    ]

    result = harness.site_0_scoped_client.execute_graphql(
        GET_BLOCK_UTILIZATIONS_FOR_SITE,
        variables={
            "minDate": today.isoformat(),
            "maxDate": today.isoformat(),
            "siteId": harness.site_0.id,
        },
    )

    assert len(result["data"]["blockUtilizations"]) == 1
    assert result["data"]["blockUtilizations"] == expected


async def _create_mock_case(
    room: RoomModel,
    start_time: datetime,
    end_time: datetime,
    status: str,
    surgeon_id: UUID,
    session: AsyncSession,
) -> str:
    case_id = str(uuid4())
    org_id = room.org_id
    site_id = room.site_id
    room_id = room.id
    case = await harness.case_store.create_case(
        Case(
            external_case_id=f"ext_case_id_{case_id}",
            case_id=case_id,
            org_id=org_id,
            site_id=site_id,
            room_id=room_id,
            scheduled_start_time=start_time,
            scheduled_end_time=end_time,
            is_add_on=False,
            status=status,
        ),
        session=session,
    )
    await harness.case_staff_store.upsert_case_staff(
        [CaseStaffModel(staff_id=surgeon_id, case_id=case.case_id, role=PRIMARY_SURGEON_ROLES[0])]
    )
    return case_id


async def _create_mock_case_and_actual_case(
    room: RoomModel,
    start_time: datetime,
    end_time: datetime,
    session: AsyncSession,
    surgeon_id: UUID,
    status: str,
) -> str:
    org_id = room.org_id
    site_id = room.site_id
    room_id = room.id
    case_id = await _create_mock_case(
        room=room,
        start_time=start_time,
        end_time=end_time,
        status=status,
        surgeon_id=surgeon_id,
        session=session,
    )

    # started 5 minutes late
    start_event = _mock_event(
        start_time=start_time + timedelta(minutes=5),
        event_type_id="patient_wheels_in",
        site_id=site_id,
        org_id=org_id,
        room_id=room_id,
    )
    # ended 5 minutes early
    end_event = _mock_event(
        start_time=end_time + timedelta(minutes=-5),
        event_type_id="patient_wheels_out",
        site_id=site_id,
        org_id=org_id,
        room_id=room_id,
    )

    await harness.event_store.upsert_events([start_event, end_event])

    phase = PhaseModel(
        id=uuid.uuid4(),
        org_id=org_id,
        site_id=site_id,
        room_id=room_id,
        start_event_id=start_event.id,
        end_event_id=end_event.id,
        type_id="CASE",
        case_id=case_id,
        source_type=UNIFIED,
        status=PhaseStatus.VALID,
        start_event=start_event,
        end_event=end_event,
    )

    await harness.phase_store.upsert_phases([phase])
    return case_id


def _mock_event(
    event_type_id: str,
    start_time: datetime,
    room_id: str,
    site_id: str,
    org_id: str,
) -> EventModel:
    return EventModel(
        id=str(uuid.uuid4()),
        event_type_id=event_type_id,
        start_time=start_time,
        process_timestamp=start_time,
        org_id=org_id,
        site_id=site_id,
        room_id=room_id,
        source=UNIFIED,
        source_type=PREDICTION,
        labels=[],
    )
