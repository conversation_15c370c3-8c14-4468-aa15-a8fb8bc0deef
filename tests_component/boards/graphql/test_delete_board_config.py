import pytest

from apella_cloud_api.dtos import BoardConfigQueryDto
from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_input_schema import (
    GQLBoardConfigDeleteInput,
)
from api_server.services.boards.board_store import BoardConfigModel, BoardViewType
from tests_component import harness


class TestUpdateBoardConfig:
    @pytest.mark.asyncio
    async def test_update_board_config(self) -> None:
        board_config = BoardConfigModel(
            name="initial",
            page_size=10,
            page_duration=10,
            blur_video=False,
            updated_by_user_id=harness.user_without_permissions.user_id,
            site_id=harness.site_0.id,
            org_id=harness.site_0.org_id,
            board_view_type=BoardViewType.TIMELINE,
            enable_video=True,
            show_closed_rooms=False,
        )
        board_config = await harness.board_store.create_board_config(
            board_config, room_ids=[harness.room_0.id]
        )
        board_config_input = GQLBoardConfigDeleteInput(
            board_config_id=str(board_config.id),
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.board_config_delete.args(
            input=board_config_input,
        ).select(apella_schema.BoardConfigDelete.success)
        board_configs = await harness.board_store.query_board_configs(
            BoardConfigQueryDto(
                org_ids=[harness.site_0.org_id],
            )
        )
        assert len(board_configs) == 1
        assert board_configs[0].name == "initial"

        harness.service_account_client.mutate_graphql_from_schema(mutation)

        board_configs = await harness.board_store.query_board_configs(
            BoardConfigQueryDto(
                org_ids=[harness.site_0.org_id],
            )
        )
        assert len(board_configs) == 0
