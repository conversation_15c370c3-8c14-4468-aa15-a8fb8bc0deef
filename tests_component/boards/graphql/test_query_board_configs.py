from typing import Callable

import pytest

from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_client_schema import GQLDirection
from apella_cloud_api.new_input_schema import GQLBoardConfigQueryInput, GQLOrderBy
from apella_cloud_api.new_schema_generator.schema_generator_base_classes import NotFound
from api_server.services.boards.board_store import BoardConfigModel, BoardViewType
from tests_component import harness


class TestQueryBoardConfigs:
    @pytest.mark.asyncio
    async def test_query_by_nonexistent_updated_user(self) -> None:
        nonexistent_user_board_config = BoardConfigModel(
            name="test_nonexistent",
            page_size=10,
            page_duration=10,
            blur_video=False,
            updated_by_user_id="bad user",
            site_id=harness.site_0.id,
            org_id=harness.site_0.org_id,
            board_view_type=BoardViewType.TIMELINE,
            enable_video=True,
            show_closed_rooms=True,
        )
        board_config = BoardConfigModel(
            name="test",
            page_size=10,
            page_duration=10,
            blur_video=False,
            updated_by_user_id=harness.user_without_permissions.user_id,
            site_id=harness.site_0.id,
            org_id=harness.site_0.org_id,
            board_view_type=BoardViewType.TIMELINE,
            enable_video=True,
            show_closed_rooms=True,
        )
        await harness.board_store.create_board_config(nonexistent_user_board_config, room_ids=[])
        await harness.board_store.create_board_config(board_config, room_ids=[])
        site_id = harness.site_0.id
        apella_schema = ApellaSchema()
        query = apella_schema.Query.board_configs.args(
            query=GQLBoardConfigQueryInput(
                site_ids=[site_id],
            )
        ).select(
            apella_schema.BoardConfigConnection.edges.select(
                apella_schema.BoardConfigEdge.node.select(
                    apella_schema.BoardConfig.name,
                    apella_schema.BoardConfig.updated_by_user.select(apella_schema.User.id),
                )
            )
        )
        results = harness.service_account_client.query_graphql_from_schema(query)
        assert len(results.board_configs.edges) == 2

        nonexistent_user_result = next(
            (
                e
                for e in results.board_configs.edges
                if e.node.name == nonexistent_user_board_config.name
            )
        )
        assert isinstance(nonexistent_user_result.node.updated_by_user, NotFound)

        existing_user_result = next(
            (e for e in results.board_configs.edges if e.node.name == board_config.name)
        )
        assert existing_user_result.node.updated_by_user.id == board_config.updated_by_user_id

    @pytest.mark.parametrize(
        "site_id_fn,expected_count",
        [(lambda: harness.site_0.id, 1), (lambda: harness.site_1.id, 0)],
    )
    @pytest.mark.asyncio
    async def test_query_by_site_id(
        self, site_id_fn: Callable[[], str], expected_count: int
    ) -> None:
        board_config = BoardConfigModel(
            name="test",
            page_size=10,
            page_duration=10,
            blur_video=False,
            updated_by_user_id=harness.user_without_permissions.user_id,
            site_id=harness.site_0.id,
            org_id=harness.site_0.org_id,
            board_view_type=BoardViewType.TIMELINE,
            enable_video=True,
            show_closed_rooms=True,
        )
        await harness.board_store.create_board_config(board_config, room_ids=[])
        site_id = site_id_fn()
        apella_schema = ApellaSchema()
        query = apella_schema.Query.board_configs.args(
            query=GQLBoardConfigQueryInput(
                site_ids=[site_id],
            )
        ).select(
            apella_schema.BoardConfigConnection.edges.select(
                apella_schema.BoardConfigEdge.node.select(
                    apella_schema.BoardConfig.name,
                )
            )
        )
        results = harness.service_account_client.query_graphql_from_schema(query)
        assert len(results.board_configs.edges) == expected_count

    @pytest.mark.parametrize(
        "room_id_fn,expected_count",
        [(lambda: harness.room_0.id, 1), (lambda: harness.room_1.id, 0)],
    )
    @pytest.mark.asyncio
    async def test_query_by_room_id(
        self, room_id_fn: Callable[[], str], expected_count: int
    ) -> None:
        board_config = BoardConfigModel(
            name="test",
            page_size=10,
            page_duration=10,
            blur_video=False,
            updated_by_user_id=harness.user_without_permissions.user_id,
            site_id=harness.site_0.id,
            org_id=harness.site_0.org_id,
            board_view_type=BoardViewType.TIMELINE,
            enable_video=True,
            show_closed_rooms=True,
        )
        room_ids = [harness.room_0.id]
        room_id = room_id_fn()
        await harness.board_store.create_board_config(board_config, room_ids=room_ids)

        apella_schema = ApellaSchema()
        query = apella_schema.Query.board_configs.args(
            query=GQLBoardConfigQueryInput(
                room_ids=[room_id],
            )
        ).select(
            apella_schema.BoardConfigConnection.edges.select(
                apella_schema.BoardConfigEdge.node.select(
                    apella_schema.BoardConfig.name,
                )
            )
        )
        results = harness.service_account_client.query_graphql_from_schema(query)
        assert len(results.board_configs.edges) == expected_count

    @pytest.mark.asyncio
    async def test_query_by_site_id_all_rooms(self) -> None:
        board_config_1 = BoardConfigModel(
            name="test",
            page_size=10,
            page_duration=10,
            blur_video=False,
            updated_by_user_id=harness.user_without_permissions.user_id,
            site_id=harness.site_0.id,
            org_id=harness.site_0.org_id,
            board_view_type=BoardViewType.TIMELINE,
            enable_video=True,
            show_closed_rooms=True,
        )
        await harness.board_store.create_board_config(board_config_1, room_ids=[harness.room_0.id])
        board_config_2 = BoardConfigModel(
            name="test_2",
            page_size=10,
            page_duration=10,
            blur_video=False,
            updated_by_user_id=harness.user_without_permissions.user_id,
            site_id=harness.site_0.id,
            org_id=harness.site_0.org_id,
            board_view_type=BoardViewType.TIMELINE,
            enable_video=True,
            show_closed_rooms=True,
        )
        await harness.board_store.create_board_config(board_config_2, room_ids=[])
        site_id = board_config_1.site_id
        apella_schema = ApellaSchema()
        query = apella_schema.Query.board_configs.args(
            query=GQLBoardConfigQueryInput(
                site_ids=[site_id],
            ),
            order_by=[
                GQLOrderBy(sort=apella_schema.BoardConfig.name.name, direction=GQLDirection.ASC)
            ],
        ).select(
            apella_schema.BoardConfigConnection.edges.select(
                apella_schema.BoardConfigEdge.node.select(
                    apella_schema.BoardConfig.name,
                    apella_schema.BoardConfig.rooms.select(
                        apella_schema.Room.name,
                    ),
                )
            )
        )
        results = harness.service_account_client.query_graphql_from_schema(query)
        assert len(results.board_configs.edges) == 2
        assert results.board_configs.edges[0].node.name == board_config_1.name
        assert len(results.board_configs.edges[0].node.rooms) == 1
        assert results.board_configs.edges[1].node.name == board_config_2.name
        assert isinstance(results.board_configs.edges[1].node.rooms, NotFound)
