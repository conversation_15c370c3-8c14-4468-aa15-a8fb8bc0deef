from auth.permissions import READ_CAMERA_PREFIX
from tests_component import harness


def test_get_cameras_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        f"""
    query {{
        camera(id: "{harness.camera_0.id}") {{
            id
            name
        }}
    }}
    """
    )
    assert result == {
        "data": {"camera": None},
        "errors": [
            {
                "locations": [{"column": 9, "line": 3}],
                "message": "Not Authorized: No authorization found",
                "path": ["camera"],
            }
        ],
    }


def test_get_cameras_no_permissions() -> None:
    result = harness.user_without_permissions_client.execute_graphql(
        f"""
        query {{
          camera(id: "{harness.camera_0.id}") {{
              id
              name
          }}
        }}
        """
    )
    assert result == {
        "data": {"camera": None},
        "errors": [
            {
                "locations": [{"column": 11, "line": 3}],
                "message": f"User does not have permission '{READ_CAMERA_PREFIX}'",
                "path": ["camera"],
            }
        ],
    }


def test_get_cameras() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        camera(id: "{harness.camera_0.id}") {{
            id
            name
            organizationId
            organization {{
                id
            }}
            siteId
            site {{
                id
            }}
            roomId
            room {{
                id
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": {
            "camera": {
                "id": harness.camera_0.id,
                "name": harness.camera_0.name,
                "organizationId": harness.camera_0.org_id,
                "organization": {"id": harness.camera_0.org_id},
                "siteId": harness.camera_0.site_id,
                "site": {"id": harness.camera_0.site_id},
                "roomId": harness.camera_0.room_id,
                "room": {"id": harness.camera_0.room_id},
            }
        }
    }


def test_get_camera_as_org_user_requires_same_org() -> None:
    result = harness.surgeon_client.execute_graphql(
        f"""
        query {{
            camera(id: "{harness.camera_5.id}") {{
                name
            }}
        }}
        """
    )
    assert result == {"data": {"camera": None}}


def test_get_camera_as_site_user_fails_for_different_sites() -> None:
    result = harness.site_0_scoped_client.execute_graphql(
        f"""
        query {{
            camera(id: "{harness.camera_5.id}") {{
                name
            }}
        }}
        """
    )
    assert result == {"data": {"camera": None}}


def test_get_camera_as_site_user_succeeds_for_same_site() -> None:
    result = harness.site_0_scoped_client.execute_graphql(
        f"""
        query {{
            camera(id: "{harness.camera_0.id}") {{
                name
            }}
        }}
        """
    )
    assert result == {
        "data": {
            "camera": {
                "name": harness.camera_0.name,
            }
        }
    }


def test_query_camera() -> None:
    result = harness.surgeon_client.execute_graphql(
        f"""
        query {{
            camera(id: "{harness.camera_0.id}") {{
                id
            }}
        }}
        """
    )

    assert result == {"data": {"camera": {"id": harness.camera_0.id}}}


def test_query_organization_ids() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    query {{
        cameras(organizationId: "{harness.org_0.id}") {{
            edges {{
                node {{
                    name
                    id
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "cameras": {
                "edges": [
                    {"node": {"id": harness.camera_0.id, "name": harness.camera_0.name}},
                    {"node": {"id": harness.camera_1.id, "name": harness.camera_1.name}},
                    {"node": {"id": harness.camera_2.id, "name": harness.camera_2.name}},
                    {"node": {"id": harness.camera_3.id, "name": harness.camera_3.name}},
                    {"node": {"id": harness.camera_4.id, "name": harness.camera_4.name}},
                    {"node": {"id": harness.camera_ca.id, "name": harness.camera_ca.name}},
                ]
            }
        }
    }


def test_query_site_ids() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    query {{
        cameras(siteIds: ["{harness.site_0.id}", "{harness.site_1.id}"]) {{
            edges {{
                node {{
                    name
                    id
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "cameras": {
                "edges": [
                    {"node": {"id": harness.camera_0.id, "name": harness.camera_0.name}},
                    {"node": {"id": harness.camera_1.id, "name": harness.camera_1.name}},
                    {"node": {"id": harness.camera_2.id, "name": harness.camera_2.name}},
                    {"node": {"id": harness.camera_3.id, "name": harness.camera_3.name}},
                    {"node": {"id": harness.camera_4.id, "name": harness.camera_4.name}},
                ]
            }
        }
    }


def test_query_empty_site_ids() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    query {{
        cameras(siteIds: []) {{
            edges {{
                node {{
                    name
                    id
                }}
            }}
        }}
    }}
    """
    )

    assert result == {"data": {"cameras": {"edges": []}}}


def test_query_room_ids() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    query {{
        cameras(roomIds: ["{harness.room_0.id}", "{harness.room_1.id}", "{harness.room_2.id}"]) {{
            edges {{
                node {{
                    name
                    id
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "cameras": {
                "edges": [
                    {"node": {"id": harness.camera_0.id, "name": harness.camera_0.name}},
                    {"node": {"id": harness.camera_1.id, "name": harness.camera_1.name}},
                    {"node": {"id": harness.camera_2.id, "name": harness.camera_2.name}},
                    {"node": {"id": harness.camera_3.id, "name": harness.camera_3.name}},
                    {"node": {"id": harness.camera_4.id, "name": harness.camera_4.name}},
                ]
            }
        }
    }


def test_query_empty_room_ids() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    query {{
        cameras(roomIds: []) {{
            edges {{
                node {{
                    name
                    id
                }}
            }}
        }}
    }}
    """
    )

    assert result == {"data": {"cameras": {"edges": []}}}


def test_query_site_and_room_ids() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    query {{
        cameras(
            siteIds: ["{harness.site_0.id}", "{harness.site_1.id}"],
            roomIds: ["{harness.room_0.id}", "{harness.room_1.id}", "{harness.room_2.id}", "{harness.room_3.id}"]
        ) {{
            edges {{
                node {{
                    name
                    id
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "cameras": {
                "edges": [
                    {"node": {"id": harness.camera_0.id, "name": harness.camera_0.name}},
                    {"node": {"id": harness.camera_1.id, "name": harness.camera_1.name}},
                    {"node": {"id": harness.camera_2.id, "name": harness.camera_2.name}},
                    {"node": {"id": harness.camera_3.id, "name": harness.camera_3.name}},
                    {"node": {"id": harness.camera_4.id, "name": harness.camera_4.name}},
                ]
            }
        }
    }


def test_query_site_by_nonexistent_organization_id() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    query {{
        cameras(organizationId: "Non Existent Organization") {{
            edges {{
                node {{
                    name
                    id
                }}
            }}
        }}
    }}
    """
    )

    assert result == {"data": {"cameras": {"edges": []}}}


def test_query_mismatched_site_and_room_ids() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    query {{
        cameras(
            siteIds: ["{harness.site_0.id}"],
            roomIds: ["{harness.room_2.id}"]
        ) {{
            edges {{
                node {{
                    name
                    id
                }}
            }}
        }}
    }}
    """
    )

    assert result == {"data": {"cameras": {"edges": []}}}


def test_query_families() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    query {{
        cameras(
            families: ["geovision"]
        ) {{
            edges {{
                node {{
                    name
                    id
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "cameras": {
                "edges": [
                    {"node": {"id": harness.camera_0.id, "name": harness.camera_0.name}},
                    {"node": {"id": harness.camera_1.id, "name": harness.camera_1.name}},
                    {"node": {"id": harness.camera_2.id, "name": harness.camera_2.name}},
                    {"node": {"id": harness.camera_3.id, "name": harness.camera_3.name}},
                    {"node": {"id": harness.camera_4.id, "name": harness.camera_4.name}},
                ]
            }
        }
    }
