from auth.permissions import WRITE_ANY_CAMERA
from tests_component import harness

UPDATED_CAMERA_NAME = "updated camera name"
UPDATED_FAMILY_NAME = "ubiquiti"


def mutation_camera_input() -> str:
    return f"""
    mutation {{
      cameraUpdate(input: {{
        id: "{harness.camera_0.id}",
        name: "{UPDATED_CAMERA_NAME}",
        family: "{UPDATED_FAMILY_NAME}",
       }}
      ) {{
        success
        updatedCamera {{
          id
          name
          family
        }}
      }}
    }}
    """


def test_update_camera_requires_auth() -> None:
    result = harness.user_without_permissions_client.execute_graphql(
        f"""
    {mutation_camera_input()}
    """
    )
    assert result == {
        "data": {"cameraUpdate": None},
        "errors": [
            {
                "path": ["cameraUpdate"],
                "locations": [{"line": 4, "column": 7}],
                "message": f"User does not have permission '{WRITE_ANY_CAMERA}'",
            }
        ],
    }


def test_update_camera_return() -> None:
    result = harness.field_engineer_client.execute_graphql(mutation_camera_input())

    assert result == {
        "data": {
            "cameraUpdate": {
                "success": True,
                "updatedCamera": {
                    "id": str(harness.camera_0.id),
                    "name": UPDATED_CAMERA_NAME,
                    "family": UPDATED_FAMILY_NAME,
                },
            }
        }
    }


def get_camera_labels_mutation(label_gql: str) -> str:
    return f"""
    mutation {{
        cameraUpdate(input: {{
            id: "{harness.camera_0.id}",
            rtspUrl: "https://rtps-url.tld"
            labels: {{
              {label_gql}
            }}
         }}
        ) {{
          success
            updatedCamera {{
              id
              rtspUrl
              labels
          }}
        }}
    }}
    """


def test_update_camera_labels_return() -> None:
    result = harness.field_engineer_client.execute_graphql(
        get_camera_labels_mutation(
            """
            imageAddress: "*************/media/video1"
            hlsAddress: "*************/media/video2"
            captureModes: "hls,image,audio"
            """
        )
    )

    assert result == {
        "data": {
            "cameraUpdate": {
                "success": True,
                "updatedCamera": {
                    "id": str(harness.camera_0.id),
                    "rtspUrl": "https://rtps-url.tld",
                    "labels": {
                        "captureModes": "hls,image,audio",
                        "hlsAddress": "*************/media/video2",
                        "imageAddress": "*************/media/video1",
                    },
                },
            }
        }
    }
    result = harness.field_engineer_client.execute_graphql(
        get_camera_labels_mutation(
            """
            imageAddress: "*************/media/video1"
            captureModes: "hls"
            """
        )
    )

    assert result == {
        "data": {
            "cameraUpdate": {
                "success": True,
                "updatedCamera": {
                    "id": str(harness.camera_0.id),
                    "rtspUrl": "https://rtps-url.tld",
                    "labels": {
                        "captureModes": "hls",
                        "imageAddress": "*************/media/video1",
                    },
                },
            }
        }
    }
