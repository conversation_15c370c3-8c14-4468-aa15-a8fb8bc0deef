import uuid
from typing import Optional

import pytest
from graphql import GraphQLError

from apella_cloud_api.api_server_schema import PhaseStatus
from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_client_schema import GQLCase<PERSON>atchingStatus
from apella_cloud_api.new_input_schema import GQLMatchCaseInput
from apella_cloud_api.new_schema_generator.schema_generator_base_classes import NotFound
from api_server.services.phases.phase_store import PhaseModel, PhaseRelationshipModel
from api_server.services.phases.phases_models import PhaseType
from tests_component import harness


class TestManualCaseMatch:
    def _create_mock_phase(
        self,
        start_event_id: str,
        end_event_id: str,
        org_id: Optional[str] = None,
        site_id: Optional[str] = None,
        room_id: Optional[str] = None,
        case_id: Optional[str] = None,
        source_type: str = "unified",
        phase_type: PhaseType = PhaseType.TURNOVER,
    ) -> PhaseModel:
        phase = PhaseModel()
        phase.id = uuid.uuid4()
        phase.org_id = org_id if org_id is not None else harness.org_0.id
        phase.site_id = site_id if site_id is not None else harness.site_0.id
        phase.room_id = room_id if room_id is not None else harness.room_0.id
        phase.type_id = phase_type.name
        phase.start_event_id = start_event_id
        phase.end_event_id = end_event_id
        if case_id is not None:
            phase.case_id = case_id
        phase.source_type = source_type
        phase.status = PhaseStatus.VALID
        return phase

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "case_matching_type", [GQLCaseMatchingStatus.OVERRIDE, GQLCaseMatchingStatus.AUTOMATIC]
    )
    async def test_manual_case_match(self, case_matching_type: GQLCaseMatchingStatus) -> None:
        # Arrange
        phase = self._create_mock_phase(
            start_event_id=harness.event_0.id,
            end_event_id=harness.event_1.id,
            room_id=harness.case_0.room_id,
        )
        phase = await harness.phase_store.insert_phase(phase)
        assert phase.event_matching_status.name == "AUTOMATIC"
        assert phase.case_id is None
        apella_schema = ApellaSchema()
        case_match = GQLMatchCaseInput(
            case_id=harness.case_0.case_id,
            phase_id=phase.id,
            case_match_type=case_matching_type,
            explanation_for_change="explanation",
        )

        mutation = apella_schema.Mutation.match_cases.args(input=[case_match]).select(
            apella_schema.MatchCases.phases.select(
                apella_schema.Phase.id,
                apella_schema.Phase.event_matching_status,
                apella_schema.Phase.case.select(
                    apella_schema.ScheduledCase.id,
                    apella_schema.ScheduledCase.case_matching_status,
                ),
            )
        )

        # Act
        results = harness.service_account_client.mutate_graphql_from_schema(mutation)

        # Assert
        phases = results.match_cases.phases
        assert len(phases) == 1
        returned_phase = phases[0]
        assert returned_phase.id == str(phase.id)
        assert returned_phase.event_matching_status.name == case_matching_type.name
        case = returned_phase.case
        assert case.id == harness.case_0.case_id
        assert case.case_matching_status.name == case_matching_type.name

    @pytest.mark.asyncio
    async def test_manual_case_match_child_phases(self) -> None:
        # Arrange
        phase = self._create_mock_phase(
            start_event_id=harness.event_0.id,
            end_event_id=harness.event_1.id,
            room_id=harness.case_0.room_id,
        )
        child_phase = self._create_mock_phase(
            start_event_id=harness.event_2.id,
            end_event_id=harness.event_3.id,
            room_id=harness.case_0.room_id,
        )

        phase = await harness.phase_store.insert_phase(phase)
        child_phase = await harness.phase_store.insert_phase(child_phase)
        await harness.phase_store.insert_relationship(
            PhaseRelationshipModel(
                parent_phase_id=phase.id,
                child_phase_id=child_phase.id,
                org_id=harness.org_0.id,
            )
        )
        assert phase.event_matching_status.name == "AUTOMATIC"
        assert phase.case_id is None
        apella_schema = ApellaSchema()
        case_match = GQLMatchCaseInput(
            case_id=harness.case_0.case_id,
            phase_id=phase.id,
            case_match_type=GQLCaseMatchingStatus.OVERRIDE,
            explanation_for_change="explanation",
        )

        mutation = apella_schema.Mutation.match_cases.args(input=[case_match]).select(
            apella_schema.MatchCases.phases.select(
                apella_schema.Phase.id,
                apella_schema.Phase.event_matching_status,
                apella_schema.Phase.case.select(
                    apella_schema.ScheduledCase.id,
                    apella_schema.ScheduledCase.case_matching_status,
                ),
            )
        )

        # Act
        results = harness.service_account_client.mutate_graphql_from_schema(mutation)

        # Assert
        phases = results.match_cases.phases
        assert len(phases) == 2

        # Create a mapping by phase ID to avoid relying on ordering
        phase_id = str(phase.id)
        child_phase_id = str(child_phase.id)
        phases_map = {phase.id: phase for phase in phases}

        # Assert parent phase
        assert phase_id in phases_map
        returned_phase = phases_map[phase_id]
        assert returned_phase.event_matching_status.name == GQLCaseMatchingStatus.OVERRIDE.name
        case = returned_phase.case
        assert case.id == harness.case_0.case_id
        assert case.case_matching_status.name == GQLCaseMatchingStatus.OVERRIDE.name

        # Assert child phase
        assert child_phase_id in phases_map
        returned_child_phase = phases_map[child_phase_id]
        assert (
            returned_child_phase.event_matching_status.name == GQLCaseMatchingStatus.OVERRIDE.name
        )
        child_phase_case = returned_child_phase.case
        assert child_phase_case.id == str(harness.case_0.case_id)
        assert child_phase_case.case_matching_status.name == GQLCaseMatchingStatus.OVERRIDE.name

    @pytest.mark.asyncio
    async def test_manual_case_match_duplicates_case_id_fail(self) -> None:
        # Arrange
        phase = self._create_mock_phase(
            start_event_id=harness.event_0.id,
            end_event_id=harness.event_1.id,
        )
        phase = await harness.phase_store.insert_phase(phase)
        assert phase.event_matching_status.name == "AUTOMATIC"
        assert phase.case_id is None
        apella_schema = ApellaSchema()
        case_match_1 = GQLMatchCaseInput(
            case_id=harness.case_0.case_id,
            phase_id=phase.id,
            explanation_for_change="explanation",
        )
        case_match_2 = GQLMatchCaseInput(
            case_id=harness.case_0.case_id,
            explanation_for_change="explanation",
        )

        mutation = apella_schema.Mutation.match_cases.args(
            input=[case_match_1, case_match_2]
        ).select(
            apella_schema.MatchCases.phases.select(
                apella_schema.Phase.id,
                apella_schema.Phase.event_matching_status,
                apella_schema.Phase.case.select(
                    apella_schema.ScheduledCase.id,
                    apella_schema.ScheduledCase.case_matching_status,
                ),
            )
        )

        # Act
        with pytest.raises(GraphQLError) as exc_info:
            harness.service_account_client.mutate_graphql_from_schema(mutation)

        # Assert
        assert f"Duplicate case ids: {{'{harness.case_0.case_id}'}}" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_manual_case_match_duplicates_phase_id_fail(self) -> None:
        # Arrange
        phase = self._create_mock_phase(
            start_event_id=harness.event_0.id,
            end_event_id=harness.event_1.id,
        )
        phase = await harness.phase_store.insert_phase(phase)
        assert phase.event_matching_status.name == "AUTOMATIC"
        assert phase.case_id is None
        apella_schema = ApellaSchema()
        case_match_1 = GQLMatchCaseInput(
            case_id=harness.case_0.case_id,
            phase_id=phase.id,
            explanation_for_change="explanation",
        )
        case_match_2 = GQLMatchCaseInput(
            case_id=harness.case_1.case_id,
            phase_id=phase.id,
            explanation_for_change="explanation",
        )

        mutation = apella_schema.Mutation.match_cases.args(
            input=[case_match_1, case_match_2]
        ).select(
            apella_schema.MatchCases.phases.select(
                apella_schema.Phase.id,
                apella_schema.Phase.event_matching_status,
                apella_schema.Phase.case.select(
                    apella_schema.ScheduledCase.id,
                    apella_schema.ScheduledCase.case_matching_status,
                ),
            )
        )

        # Act
        with pytest.raises(GraphQLError) as exc_info:
            harness.service_account_client.mutate_graphql_from_schema(mutation)

        # Assert
        assert f"duplicate phase ids: {{'{phase.id}'}}" in str(exc_info.value)

    def test_manual_case_match_phase_not_found_fail(self) -> None:
        # Arrange
        apella_schema = ApellaSchema()
        test_id = uuid.uuid4()
        case_match = GQLMatchCaseInput(
            case_id=harness.case_0.case_id,
            phase_id=test_id,
            explanation_for_change="explanation",
        )

        mutation = apella_schema.Mutation.match_cases.args(input=[case_match]).select(
            apella_schema.MatchCases.phases.select(
                apella_schema.Phase.id,
                apella_schema.Phase.event_matching_status,
                apella_schema.Phase.case.select(
                    apella_schema.ScheduledCase.id,
                    apella_schema.ScheduledCase.case_matching_status,
                ),
            )
        )

        # Act
        with pytest.raises(GraphQLError) as exc_info:
            harness.service_account_client.mutate_graphql_from_schema(mutation)

        # Assert
        assert f"Phase ids {{'{str(test_id)}'}}" in str(exc_info.value)

    def test_manual_case_match_case_not_found_fail(self) -> None:
        # Arrange
        apella_schema = ApellaSchema()
        test_id = str(uuid.uuid4())
        case_match = GQLMatchCaseInput(
            case_id=test_id,
            explanation_for_change="explanation",
        )

        mutation = apella_schema.Mutation.match_cases.args(input=[case_match]).select(
            apella_schema.MatchCases.phases.select(
                apella_schema.Phase.id,
                apella_schema.Phase.event_matching_status,
                apella_schema.Phase.case.select(
                    apella_schema.ScheduledCase.id,
                    apella_schema.ScheduledCase.case_matching_status,
                ),
            )
        )

        # Act
        with pytest.raises(GraphQLError) as exc_info:
            harness.service_account_client.mutate_graphql_from_schema(mutation)

        # Assert
        assert f"case ids {{'{str(test_id)}'}}" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_manual_case_match_unmatches_other_phases(self) -> None:
        # Arrange

        phase_0 = self._create_mock_phase(
            start_event_id=harness.event_0.id,
            end_event_id=harness.event_1.id,
            case_id=harness.case_0.case_id,
        )
        phase_0 = await harness.phase_store.insert_phase(phase_0)
        assert phase_0.event_matching_status.name == "AUTOMATIC"

        phase_1 = self._create_mock_phase(
            start_event_id=harness.event_0.id,
            end_event_id=harness.event_1.id,
        )
        phase_1 = await harness.phase_store.insert_phase(phase_1)
        assert phase_1.event_matching_status.name == "AUTOMATIC"

        apella_schema = ApellaSchema()
        case_match = GQLMatchCaseInput(
            case_id=harness.case_0.case_id,
            phase_id=phase_1.id,
            explanation_for_change="explanation_2",
        )

        mutation = apella_schema.Mutation.match_cases.args(input=[case_match]).select(
            apella_schema.MatchCases.phases.select(
                apella_schema.Phase.id,
                apella_schema.Phase.event_matching_status,
                apella_schema.Phase.case.select(
                    apella_schema.ScheduledCase.id,
                    apella_schema.ScheduledCase.case_matching_status,
                    apella_schema.ScheduledCase.matching_status_reason.select(
                        apella_schema.MatchingStatusReason.explanation_for_change
                    ),
                ),
            )
        )

        # Act
        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        phase_0_id = str(phase_0.id)
        phase_1_id = str(phase_1.id)
        phases = result.match_cases.phases
        assert len(phases) == 2
        phases_map = {phase.id: phase for phase in phases}
        assert phases_map[phase_0_id].event_matching_status.name == "AUTOMATIC"
        assert phases_map[phase_0_id].case == NotFound()

        assert phases_map[phase_1_id].event_matching_status.name == "OVERRIDE"
        assert phases_map[phase_1_id].case.case_matching_status.name == "OVERRIDE"
        assert (
            phases_map[phase_1_id].case.matching_status_reason.explanation_for_change
            == "explanation_2"
        )

    @pytest.mark.asyncio
    async def test_manual_case_match_unmatches_phase(self) -> None:
        # Arrange
        phase = self._create_mock_phase(
            start_event_id=harness.event_0.id,
            end_event_id=harness.event_1.id,
            case_id=harness.case_0.case_id,
        )
        phase = await harness.phase_store.insert_phase(phase)
        assert phase.event_matching_status.name == "AUTOMATIC"

        apella_schema = ApellaSchema()
        case_match = GQLMatchCaseInput(
            case_id=harness.case_0.case_id,
            explanation_for_change="explanation",
        )

        mutation = apella_schema.Mutation.match_cases.args(input=[case_match]).select(
            apella_schema.MatchCases.phases.select(
                apella_schema.Phase.id,
                apella_schema.Phase.event_matching_status,
                apella_schema.Phase.case.select(
                    apella_schema.ScheduledCase.id,
                    apella_schema.ScheduledCase.case_matching_status,
                ),
            )
        )

        # Act
        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        phase_id = str(phase.id)
        phases = result.match_cases.phases
        assert len(phases) == 1
        assert phases[0].id == phase_id
        assert phases[0].event_matching_status.name == "AUTOMATIC"
        assert phases[0].case == NotFound()

    @pytest.mark.asyncio
    async def test_manual_case_match_ignores_forecast(self) -> None:
        # Arrange
        phase = self._create_mock_phase(
            start_event_id=harness.event_0.id,
            end_event_id=harness.event_1.id,
            case_id=harness.case_0.case_id,
            source_type="forecasting",
        )
        phase = await harness.phase_store.insert_phase(phase)
        assert phase.event_matching_status.name == "AUTOMATIC"

        apella_schema = ApellaSchema()
        case_match = GQLMatchCaseInput(
            case_id=harness.case_0.case_id,
            explanation_for_change="explanation",
        )

        mutation = apella_schema.Mutation.match_cases.args(input=[case_match]).select(
            apella_schema.MatchCases.phases.select(
                apella_schema.Phase.id,
                apella_schema.Phase.event_matching_status,
                apella_schema.Phase.case.select(
                    apella_schema.ScheduledCase.id,
                    apella_schema.ScheduledCase.case_matching_status,
                ),
            )
        )

        # Act
        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        phases = result.match_cases.phases
        assert len(phases) == 0
        updated_phases = await harness.phase_store.get_phases_by_ids([str(phase.id)])
        assert len(updated_phases) == 1
        assert updated_phases[0].event_matching_status.name == "AUTOMATIC"
        assert updated_phases[0].case_id == harness.case_0.case_id

    @pytest.mark.asyncio
    async def test_manual_case_match_forecast_fails(self) -> None:
        # Arrange
        phase = self._create_mock_phase(
            start_event_id=harness.event_0.id,
            end_event_id=harness.event_1.id,
            source_type="forecasting",
        )
        phase = await harness.phase_store.insert_phase(phase)
        assert phase.event_matching_status.name == "AUTOMATIC"

        apella_schema = ApellaSchema()
        case_match = GQLMatchCaseInput(
            case_id=harness.case_0.case_id,
            phase_id=phase.id,
            explanation_for_change="explanation",
        )

        mutation = apella_schema.Mutation.match_cases.args(input=[case_match]).select(
            apella_schema.MatchCases.phases.select(
                apella_schema.Phase.id,
                apella_schema.Phase.event_matching_status,
                apella_schema.Phase.case.select(
                    apella_schema.ScheduledCase.id,
                    apella_schema.ScheduledCase.case_matching_status,
                ),
            )
        )
        with pytest.raises(GraphQLError) as exc_info:
            harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert f"Cannot match cases to forecasting phases, phase ids {{'{phase.id}'}}" in str(
            exc_info.value
        )

    @pytest.mark.asyncio
    async def test_manual_case_match_different_rooms(self) -> None:
        # Arrange
        phase = self._create_mock_phase(
            start_event_id=harness.event_0.id,
            end_event_id=harness.event_1.id,
            room_id=harness.room_1.id,
        )
        phase = await harness.phase_store.insert_phase(phase)
        assert phase.event_matching_status.name == "AUTOMATIC"

        apella_schema = ApellaSchema()
        case_match = GQLMatchCaseInput(
            case_id=harness.case_0.case_id,
            phase_id=phase.id,
            explanation_for_change="explanation",
        )

        mutation = apella_schema.Mutation.match_cases.args(input=[case_match]).select(
            apella_schema.MatchCases.phases.select(
                apella_schema.Phase.id,
                apella_schema.Phase.event_matching_status,
                apella_schema.Phase.case.select(
                    apella_schema.ScheduledCase.id,
                    apella_schema.ScheduledCase.case_matching_status,
                ),
            )
        )

        # Act
        with pytest.raises(GraphQLError) as exc_info:
            harness.service_account_client.mutate_graphql_from_schema(mutation)

        assert (
            f"Case {harness.case_0.case_id} and Phase {phase.id} are not in the same room"
            in str(exc_info.value)
        )
