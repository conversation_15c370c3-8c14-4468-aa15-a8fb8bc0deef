# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs
from datetime import datetime, timedelta
from typing import Optional, Union
import uuid

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from apella_cloud_api import CaseProceduresUpsertInput, ScheduledCaseQueryInput
from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_input_schema import (
    GQLCaseProceduresUpsertInput,
    GQLScheduledCaseQueryInput,
)
from apella_cloud_api.new_schema_generator.schema_generator_base_classes import Empty
from api_server.services.case.case_staff_store import PRIMARY_SURGEON_ROLES, CaseStaffModel
from api_server.services.case.case_store import Case
from api_server.services.case_derived_properties.case_derived_properties_store import (
    CaseDerivedProperties,
)
from api_server.services.observations.observation_store import ObservationModel
from api_server.services.procedures.procedure_store import ProcedureModel
from tests_component import harness
from tests_component.case.graphql.utils import mock_case, mock_staff

apella_schema = ApellaSchema()

START_OF_DAY = datetime(2020, 10, 10, 12, 0, 0)


def mock_procedure(name: str) -> ProcedureModel:
    procedure = ProcedureModel(
        name=name,
        org_id=harness.org_0.id,
    )

    return procedure


def mock_case_staff_input(
    case_id: str,
    staff_id: uuid.UUID,
    role: Optional[str] = PRIMARY_SURGEON_ROLES[1],
    archived_time: Optional[datetime] = None,
) -> CaseStaffModel:
    case_staff = CaseStaffModel()
    case_staff.case_id = case_id
    case_staff.staff_id = staff_id
    case_staff.role = role
    if archived_time is not None:
        case_staff.archived_time = archived_time

    return case_staff


def mock_case_derived_properties(
    case: Case, preceding_case_id: Optional[str] = None
) -> CaseDerivedProperties:
    cdp = CaseDerivedProperties()
    cdp.case_id = case.case_id
    cdp.is_in_flip_room = True
    cdp.preceding_case_id = preceding_case_id
    return cdp


def mock_observation(case_id: str) -> ObservationModel:
    return ObservationModel(
        case_id=case_id,
        org_id=harness.org_0.id,
        recorded_time=START_OF_DAY + timedelta(hours=3),
        type_id="OBSERVED_IN_ROOM",
        observation_time=START_OF_DAY + timedelta(hours=1),
    )


class TestScheduledCaseQuery:
    @pytest.mark.asyncio
    async def test_query_cases_returns_all_fields(self, async_session: AsyncSession) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))
        case_2 = mock_case(
            start_time=START_OF_DAY + timedelta(hours=2),
            end_time=START_OF_DAY + timedelta(hours=3),
            status="cancelled",
        )
        case_3 = mock_case(
            start_time=START_OF_DAY + timedelta(hours=4),
            end_time=START_OF_DAY + timedelta(hours=5),
        )

        await harness.case_store.create_case(case=case_1, session=async_session)
        await harness.case_store.create_case(case=case_2, session=async_session)
        await harness.case_store.create_case(case=case_3, session=async_session)

        await harness.case_derived_properties_store.upsert_case_derived_properties(
            case_derived_properties=[
                mock_case_derived_properties(case_1, str(case_2.case_id)),
                mock_case_derived_properties(case_3, str(case_2.case_id)),
            ]
        )

        query = ScheduledCaseQueryInput(
            min_time=START_OF_DAY, max_time=START_OF_DAY + timedelta(hours=4)
        )
        scheduled_cases = harness.service_account_client.query_cases(query=query)

        # Assert the case
        assert len(scheduled_cases) == 3
        assert scheduled_cases[0].id == case_1.case_id
        assert scheduled_cases[0].is_in_flip_room is True
        assert scheduled_cases[0].preceding_case.id == str(case_2.case_id)
        assert scheduled_cases[0].preceding_case.scheduled_start_time == case_2.scheduled_start_time
        assert scheduled_cases[0].patient is not None
        assert scheduled_cases[1].id == case_2.case_id
        assert scheduled_cases[2].id == case_3.case_id
        assert scheduled_cases[2].preceding_case.id == str(case_2.case_id)

    @pytest.mark.asyncio
    async def test_query_scheduled_cases_returns_all_fields(
        self, async_session: AsyncSession
    ) -> None:
        case_1 = mock_case(
            start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1), is_add_on=True
        )
        case_2 = mock_case(
            start_time=START_OF_DAY + timedelta(hours=2),
            end_time=START_OF_DAY + timedelta(hours=3),
            status="cancelled",
        )

        await harness.case_store.create_case(case=case_1, session=async_session)
        await harness.case_store.create_case(case=case_2, session=async_session)

        await harness.case_derived_properties_store.upsert_case_derived_properties(
            case_derived_properties=[mock_case_derived_properties(case_1)]
        )

        query = ScheduledCaseQueryInput(
            min_start_time=START_OF_DAY, max_start_time=START_OF_DAY + timedelta(hours=4)
        )
        scheduled_cases = harness.service_account_client.query_scheduled_cases(query=query)
        # Assert the case
        assert len(scheduled_cases) == 1
        assert scheduled_cases[0].id == case_1.case_id
        assert scheduled_cases[0].status == case_1.status
        assert scheduled_cases[0].scheduled_start_time == case_1.scheduled_start_time
        assert scheduled_cases[0].scheduled_end_time == case_1.scheduled_end_time
        assert scheduled_cases[0].site.id == case_1.site_id
        assert scheduled_cases[0].room.id == case_1.room_id
        assert scheduled_cases[0].patient_class == case_1.patient_class.name
        assert scheduled_cases[0].is_add_on == case_1.is_add_on
        assert scheduled_cases[0].is_in_flip_room is True
        assert scheduled_cases[0].preceding_case is not None
        assert scheduled_cases[0].patient is not None
        assert hasattr(scheduled_cases[0].preceding_case, "id") is False

    @pytest.mark.asyncio
    async def test_query_scheduled_cases_is_inclusive_of_bounds(
        self, async_session: AsyncSession
    ) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=2))
        case_2 = mock_case(
            start_time=START_OF_DAY + timedelta(hours=2),
            end_time=START_OF_DAY + timedelta(hours=3),
        )
        case_3 = mock_case(
            start_time=START_OF_DAY + timedelta(hours=3),
            end_time=START_OF_DAY + timedelta(hours=5),
        )

        await harness.case_store.create_case(case=case_1, session=async_session)
        await harness.case_store.create_case(case=case_2, session=async_session)
        await harness.case_store.create_case(case=case_3, session=async_session)

        query = ScheduledCaseQueryInput(
            min_time=START_OF_DAY + timedelta(hours=1), max_time=START_OF_DAY + timedelta(hours=4)
        )
        scheduled_cases = harness.service_account_client.query_scheduled_cases(query=query)

        # Assert the case
        assert len(scheduled_cases) == 3
        assert scheduled_cases[0].id == case_1.case_id
        assert scheduled_cases[1].id == case_2.case_id
        assert scheduled_cases[2].id == case_3.case_id

    @pytest.mark.asyncio
    async def test_query_scheduled_cases_returns_staff(self, async_session: AsyncSession) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))

        await harness.case_store.create_case(case=case_1, session=async_session)

        staff_1 = mock_staff(external_staff_id="1234")
        [db_staff_1] = await harness.staff_store.upsert_staff(
            staff=[staff_1], session=async_session
        )

        case_staff_role = "role"
        case_staff = [
            mock_case_staff_input(
                case_id=case_1.case_id, staff_id=db_staff_1.id, role=case_staff_role
            )
        ]
        await harness.case_staff_store.upsert_case_staff(case_staff=case_staff)

        query = ScheduledCaseQueryInput(
            min_time=START_OF_DAY, max_time=START_OF_DAY + timedelta(hours=4)
        )
        scheduled_cases = harness.service_account_client.query_scheduled_cases(query=query)

        # Assert the staff
        assert len(scheduled_cases[0].staff.edges) == 1
        assert scheduled_cases[0].staff.edges[0].node.first_name == staff_1.first_name
        assert scheduled_cases[0].staff.edges[0].node.last_name == staff_1.last_name

        # Test new case_staff nodes
        assert len(scheduled_cases[0].case_staff) == 1
        assert scheduled_cases[0].case_staff[0].role == case_staff_role
        assert scheduled_cases[0].case_staff[0].staff.first_name == staff_1.first_name
        assert scheduled_cases[0].case_staff[0].staff.last_name == staff_1.last_name

    @pytest.mark.asyncio
    async def test_query_scheduled_cases_by_staff(self, async_session: AsyncSession) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))

        await harness.case_store.create_case(case=case_1, session=async_session)

        staff_1 = mock_staff(external_staff_id="1234")
        [db_staff_1] = await harness.staff_store.upsert_staff(
            staff=[staff_1], session=async_session
        )

        case_staff = [mock_case_staff_input(case_id=case_1.case_id, staff_id=db_staff_1.id)]
        await harness.case_staff_store.upsert_case_staff(case_staff=case_staff)

        query = ScheduledCaseQueryInput(
            min_time=START_OF_DAY,
            max_time=START_OF_DAY + timedelta(hours=4),
            staff_ids=[db_staff_1.id],
        )
        scheduled_cases = harness.service_account_client.query_scheduled_cases(query=query)

        # Assert the staff
        assert len(scheduled_cases[0].staff.edges) == 1
        assert scheduled_cases[0].staff.edges[0].node.first_name == staff_1.first_name
        assert scheduled_cases[0].staff.edges[0].node.last_name == staff_1.last_name

    @pytest.mark.asyncio
    async def test_query_scheduled_cases_by_staff_discerns(
        self, async_session: AsyncSession
    ) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))

        await harness.case_store.create_case(case=case_1, session=async_session)

        staff_1 = mock_staff(external_staff_id="1234")
        [db_staff_1] = await harness.staff_store.upsert_staff(
            staff=[staff_1], session=async_session
        )

        staff_2 = mock_staff(external_staff_id="5678")
        [db_staff_2] = await harness.staff_store.upsert_staff(
            staff=[staff_2], session=async_session
        )

        case_staff = [mock_case_staff_input(case_id=case_1.case_id, staff_id=db_staff_1.id)]
        await harness.case_staff_store.upsert_case_staff(case_staff=case_staff)

        query = ScheduledCaseQueryInput(
            min_time=START_OF_DAY,
            max_time=START_OF_DAY + timedelta(hours=4),
            staff_ids=[db_staff_2.id],
        )
        scheduled_cases = harness.service_account_client.query_scheduled_cases(query=query)
        assert len(scheduled_cases) == 0

    @pytest.mark.asyncio
    async def test_query_scheduled_cases_returns_primary_surgeon(
        self, async_session: AsyncSession
    ) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))

        await harness.case_store.create_case(case=case_1, session=async_session)

        staff_1 = mock_staff(external_staff_id="1234")
        staff_2 = mock_staff(external_staff_id="5678")
        db_staff = await harness.staff_store.upsert_staff(
            staff=[staff_1, staff_2], session=async_session
        )

        case_staff = [
            mock_case_staff_input(case_id=case_1.case_id, staff_id=db_staff[0].id),
            mock_case_staff_input(case_id=case_1.case_id, staff_id=db_staff[1].id, role="CRNA"),
        ]
        await harness.case_staff_store.upsert_case_staff(case_staff=case_staff)

        query = ScheduledCaseQueryInput(
            min_time=START_OF_DAY, max_time=START_OF_DAY + timedelta(hours=4)
        )
        scheduled_cases = harness.service_account_client.query_cases(
            query=query, only_primary_surgeons=True
        )

        # Assert the staff
        assert len(scheduled_cases[0].staff.edges) == 1

        staff_ids = [staff.node.external_staff_id for staff in scheduled_cases[0].staff.edges]
        case_staff_ids = [cs.staff.external_staff_id for cs in scheduled_cases[0].case_staff]

        assert staff_1.external_staff_id in staff_ids
        assert staff_2.external_staff_id not in staff_ids
        assert staff_1.external_staff_id in case_staff_ids
        assert staff_2.external_staff_id not in case_staff_ids

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "has_hierachy, expected_procedure_count_1, expected_procedure_count_2, expected_anesthesia_count_1, expected_anesthesia_count_2",
        [
            (True, 1, 1, 0, 1),
            (False, 3, 2, 2, 2),
        ],
    )
    async def test_query_case_primary_procedures(
        self,
        has_hierachy,
        expected_procedure_count_1,
        expected_procedure_count_2,
        expected_anesthesia_count_1,
        expected_anesthesia_count_2,
        mock_anesthesias,
        async_session: AsyncSession,
    ) -> None:
        # Arrange Procedures, Cases, Case Procedures
        procedure_1 = mock_procedure(name="heart")
        procedure_2 = mock_procedure(name="endo")
        procedure_3 = mock_procedure(name="knee")
        upsert_procedure_results = await harness.procedure_store.upsert_procedures(
            procedures=[procedure_1, procedure_2, procedure_3],
            session=async_session,
        )

        created_procedure_heart = next(x for x in upsert_procedure_results if x.name == "heart")
        created_procedure_endo = next(x for x in upsert_procedure_results if x.name == "endo")
        created_procedure_knee = next(x for x in upsert_procedure_results if x.name == "knee")

        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))
        case_2 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))
        await harness.case_store.create_case(case=case_1, session=async_session)
        await harness.case_store.create_case(case=case_2, session=async_session)

        upsert_case_procedures_inputs = [
            CaseProceduresUpsertInput(
                case_id=case_1.case_id,
                procedure_id=created_procedure_heart.id,
            ),
            CaseProceduresUpsertInput(
                case_id=case_1.case_id,
                procedure_id=created_procedure_endo.id,
                anesthesia_id=str(mock_anesthesias[0].id),
            ),
            CaseProceduresUpsertInput(
                case_id=case_1.case_id,
                procedure_id=created_procedure_knee.id,
                anesthesia_id=str(mock_anesthesias[1].id),
            ),
            CaseProceduresUpsertInput(
                case_id=case_2.case_id,
                procedure_id=created_procedure_heart.id,
                anesthesia_id=str(mock_anesthesias[0].id),
            ),
            CaseProceduresUpsertInput(
                case_id=case_2.case_id,
                procedure_id=created_procedure_endo.id,
                anesthesia_id=str(mock_anesthesias[1].id),
            ),
        ]
        if has_hierachy:
            upsert_case_procedures_inputs[0].hierarchy = 1
            upsert_case_procedures_inputs[1].hierarchy = 2
            upsert_case_procedures_inputs[3].hierarchy = 1

        harness.service_account_client.upsert_case_procedures(
            case_procedures=upsert_case_procedures_inputs
        )

        apella_schema = ApellaSchema()
        query = apella_schema.Query.scheduled_cases.args(
            query=GQLScheduledCaseQueryInput(
                min_time=case_1.scheduled_start_time - timedelta(minutes=1),
                max_time=case_1.scheduled_end_time + timedelta(minutes=1),
            )
        ).select(
            apella_schema.ScheduledCaseConnection.edges.select(
                apella_schema.ScheduledCaseEdge.node.select(
                    apella_schema.ScheduledCase.primary_case_procedures.select(
                        apella_schema.CaseProcedure.hierarchy,
                        apella_schema.CaseProcedure.procedure.select(
                            apella_schema.Procedure.name,
                        ),
                        apella_schema.CaseProcedure.anesthesia.select(
                            apella_schema.Anesthesia.name,
                        ),
                    ),
                    apella_schema.ScheduledCase.case_matching_status,
                    apella_schema.ScheduledCase.id,
                )
            )
        )
        scheduled_case_results = harness.service_account_client.query_graphql_from_schema(query)
        cases = {case.node.id: case.node for case in scheduled_case_results.scheduled_cases.edges}

        # Assert Case 1
        case_1_result = cases.get(case_1.case_id)
        assert case_1_result is not None
        result_procedures_1 = case_1_result.primary_case_procedures
        # Assert procedure
        result_procedures_1_names = [
            case_procedure.procedure.name for case_procedure in result_procedures_1
        ]
        assert len(result_procedures_1) == expected_procedure_count_1
        assert "heart" in result_procedures_1_names
        # Assert anesthesia
        result_anesthesia_1_names = {
            case_procedure.anesthesia.name
            for case_procedure in result_procedures_1
            if case_procedure.anesthesia
        }
        assert len(result_anesthesia_1_names) == expected_anesthesia_count_1
        assert case_1_result.case_matching_status.name == "AUTOMATIC"
        if not has_hierachy:
            assert "General" in result_anesthesia_1_names
            assert "Spinal" in result_anesthesia_1_names

        # Assert Case 2
        case_2_result = cases.get(case_2.case_id)
        assert case_2_result is not None
        # Assert procedure
        result_procedures_2 = case_2_result.primary_case_procedures
        result_procedures_2_names = [procedure.procedure.name for procedure in result_procedures_2]
        assert len(result_procedures_2) == expected_procedure_count_2
        assert "heart" in result_procedures_2_names
        # Assert anesthesia
        result_anesthesia_2_names = {
            case_procedure.anesthesia.name
            for case_procedure in result_procedures_2
            if case_procedure.anesthesia
        }
        assert len(result_anesthesia_2_names) == expected_anesthesia_count_2
        if not has_hierachy:
            assert "General" in result_anesthesia_2_names

    @pytest.mark.asyncio
    async def test_query_case_by_procedures(self, async_session: AsyncSession) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))
        procedure_1 = mock_procedure(name="heart")

        await harness.case_store.create_case(case=case_1, session=async_session)

        result_procedures = await harness.procedure_store.upsert_procedures(
            procedures=[procedure_1], session=async_session
        )

        case_procedures = [
            CaseProceduresUpsertInput(
                case_id=case_1.case_id, procedure_id=result_procedures[0].id, hierarchy=1
            ),
        ]
        harness.service_account_client.upsert_case_procedures(case_procedures=case_procedures)

        query = ScheduledCaseQueryInput(
            min_time=START_OF_DAY,
            max_time=START_OF_DAY + timedelta(hours=4),
            procedure_ids=[result_procedures[0].id],
        )
        result_cases = harness.service_account_client.query_scheduled_cases(query)

        # Assert the procedure
        assert len(result_cases) == 1

    @pytest.mark.asyncio
    async def test_query_case_by_procedures_discerns(self, async_session: AsyncSession) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))
        procedure_1 = mock_procedure(name="heart")
        procedure_2 = mock_procedure(name="endo")

        await harness.case_store.create_case(case=case_1, session=async_session)

        result_procedures = await harness.procedure_store.upsert_procedures(
            procedures=[procedure_1, procedure_2], session=async_session
        )

        case_procedures = [
            CaseProceduresUpsertInput(
                case_id=case_1.case_id, procedure_id=result_procedures[0].id, hierarchy=1
            ),
        ]
        harness.service_account_client.upsert_case_procedures(case_procedures=case_procedures)

        query = ScheduledCaseQueryInput(
            min_time=START_OF_DAY,
            max_time=START_OF_DAY + timedelta(hours=4),
            procedure_ids=[result_procedures[1].id],
        )
        result_cases = harness.service_account_client.query_scheduled_cases(query)

        # Assert the procedure
        assert len(result_cases) == 0

    @pytest.mark.asyncio
    async def test_query_case_by_procedures_and_staff(self, async_session: AsyncSession) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))
        procedure_1 = mock_procedure(name="heart")

        staff_1 = mock_staff(external_staff_id="1234")
        [db_staff_1] = await harness.staff_store.upsert_staff(
            staff=[staff_1], session=async_session
        )

        case_staff = [mock_case_staff_input(case_id=case_1.case_id, staff_id=db_staff_1.id)]

        await harness.case_store.create_case(case=case_1, session=async_session)

        result_procedures = await harness.procedure_store.upsert_procedures(
            procedures=[procedure_1], session=async_session
        )

        case_procedures = [
            CaseProceduresUpsertInput(
                case_id=case_1.case_id, procedure_id=result_procedures[0].id, hierarchy=1
            ),
        ]
        harness.service_account_client.upsert_case_procedures(case_procedures=case_procedures)
        await harness.case_staff_store.upsert_case_staff(case_staff=case_staff)

        query = ScheduledCaseQueryInput(
            min_time=START_OF_DAY,
            max_time=START_OF_DAY + timedelta(hours=4),
            procedure_ids=[result_procedures[0].id],
            staff_ids=[db_staff_1.id],
        )
        result_cases = harness.service_account_client.query_scheduled_cases(query)

        # Assert the procedure
        assert len(result_cases) == 1

    @pytest.mark.asyncio
    async def test_query_case_updated_time(self, async_session: AsyncSession) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))
        procedure_1 = mock_procedure(name="heart")

        await harness.case_store.create_case(case=case_1, session=async_session)

        result_procedures = await harness.procedure_store.upsert_procedures(
            procedures=[procedure_1], session=async_session
        )

        case_procedures = [
            CaseProceduresUpsertInput(
                case_id=case_1.case_id, procedure_id=result_procedures[0].id, hierarchy=1
            ),
        ]
        harness.service_account_client.upsert_case_procedures(case_procedures=case_procedures)

        query = ScheduledCaseQueryInput(
            min_updated_time=START_OF_DAY - timedelta(days=2),
            max_updated_time=START_OF_DAY,
            min_time=START_OF_DAY - timedelta(hours=1),
            max_time=START_OF_DAY + timedelta(hours=1),
        )
        result_cases = harness.service_account_client.query_cases(query)

        # Assert the procedure
        assert len(result_cases) == 1

    @pytest.mark.asyncio
    async def test_query_case_updated_time_discerns(self, async_session: AsyncSession) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))
        procedure_1 = mock_procedure(name="heart")

        await harness.case_store.create_case(case=case_1, session=async_session)

        result_procedures = await harness.procedure_store.upsert_procedures(
            procedures=[procedure_1], session=async_session
        )

        case_procedures = [
            CaseProceduresUpsertInput(
                case_id=case_1.case_id, procedure_id=result_procedures[0].id, hierarchy=1
            ),
        ]
        harness.service_account_client.upsert_case_procedures(case_procedures=case_procedures)

        query = ScheduledCaseQueryInput(
            min_updated_time=START_OF_DAY - timedelta(hours=2),
            max_updated_time=START_OF_DAY - timedelta(hours=1),
            min_time=START_OF_DAY - timedelta(hours=1),
            max_time=START_OF_DAY + timedelta(hours=1),
        )
        result_cases = harness.service_account_client.query_cases(query)

        # Assert the procedure
        assert len(result_cases) == 0

    @pytest.mark.asyncio
    async def test_query_case_created_time(self, async_session: AsyncSession) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))
        procedure_1 = mock_procedure(name="heart")

        await harness.case_store.create_case(case=case_1, session=async_session)

        result_procedures = await harness.procedure_store.upsert_procedures(
            procedures=[procedure_1], session=async_session
        )

        case_procedures = [
            CaseProceduresUpsertInput(
                case_id=case_1.case_id, procedure_id=result_procedures[0].id, hierarchy=1
            ),
        ]
        harness.service_account_client.upsert_case_procedures(case_procedures=case_procedures)

        query = ScheduledCaseQueryInput(
            min_created_time=START_OF_DAY - timedelta(days=2),
            max_created_time=START_OF_DAY,
            min_time=START_OF_DAY - timedelta(hours=1),
            max_time=START_OF_DAY + timedelta(hours=1),
        )
        result_cases = harness.service_account_client.query_cases(query)

        # Assert the procedure
        assert len(result_cases) == 1

    @pytest.mark.asyncio
    async def test_query_case_created_time_discerns(self, async_session: AsyncSession) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))
        procedure_1 = mock_procedure(name="heart")

        await harness.case_store.create_case(case=case_1, session=async_session)

        result_procedures = await harness.procedure_store.upsert_procedures(
            procedures=[procedure_1], session=async_session
        )

        case_procedures = [
            CaseProceduresUpsertInput(
                case_id=case_1.case_id, procedure_id=result_procedures[0].id, hierarchy=1
            ),
        ]
        harness.service_account_client.upsert_case_procedures(case_procedures=case_procedures)

        query = ScheduledCaseQueryInput(
            min_created_time=START_OF_DAY - timedelta(hours=2),
            max_created_time=START_OF_DAY - timedelta(hours=1),
            min_time=START_OF_DAY - timedelta(hours=1),
            max_time=START_OF_DAY + timedelta(hours=1),
        )
        result_cases = harness.service_account_client.query_cases(query)

        # Assert the procedure
        assert len(result_cases) == 0

    @pytest.mark.asyncio
    async def test_query_scheduled_cases_filter_case_classification_type(
        self, async_session: AsyncSession
    ) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))
        case_2 = mock_case(
            start_time=START_OF_DAY + timedelta(hours=2),
            end_time=START_OF_DAY + timedelta(hours=3),
            status="cancelled",
            case_classification_types_id="CASE_CLASSIFICATION_URGENT",
        )

        await harness.case_store.create_case(case=case_1, session=async_session)
        await harness.case_store.create_case(case=case_2, session=async_session)

        query = ScheduledCaseQueryInput(
            min_time=START_OF_DAY,
            max_time=START_OF_DAY + timedelta(hours=4),
            case_classification_types_ids=["CASE_CLASSIFICATION_URGENT"],
        )
        scheduled_cases = harness.service_account_client.query_cases(query=query)

        # Assert the case
        assert len(scheduled_cases) == 1
        assert scheduled_cases[0].id == case_2.case_id
        assert scheduled_cases[0].status == case_2.status
        assert scheduled_cases[0].scheduled_start_time == case_2.scheduled_start_time
        assert scheduled_cases[0].scheduled_end_time == case_2.scheduled_end_time
        assert scheduled_cases[0].site.id == case_2.site_id
        assert scheduled_cases[0].room.id == case_2.room_id

    @pytest.mark.asyncio
    async def test_query_cases_returns_staff(self, async_session: AsyncSession) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))

        await harness.case_store.create_case(case=case_1, session=async_session)

        staff_1 = mock_staff(external_staff_id="1234")
        staff_2 = mock_staff(external_staff_id="5678")
        [db_staff_1, db_staff_2] = await harness.staff_store.upsert_staff(
            staff=[staff_1, staff_2], session=async_session
        )

        case_staff_role_1 = "CRNA"
        case_staff_role_2 = PRIMARY_SURGEON_ROLES[0]
        case_staff = [
            mock_case_staff_input(
                case_id=case_1.case_id, staff_id=db_staff_1.id, role=case_staff_role_1
            ),
            mock_case_staff_input(
                case_id=case_1.case_id, staff_id=db_staff_2.id, role=case_staff_role_2
            ),
        ]
        await harness.case_staff_store.upsert_case_staff(case_staff=case_staff)

        query = ScheduledCaseQueryInput(
            min_time=START_OF_DAY, max_time=START_OF_DAY + timedelta(hours=4)
        )
        scheduled_cases = harness.service_account_client.query_cases(query=query)

        # Assert the staff
        assert len(scheduled_cases[0].staff.edges) == 2

        # Test new case_staff nodes
        assert len(scheduled_cases[0].case_staff) == 2

        query = ScheduledCaseQueryInput(
            min_time=START_OF_DAY, max_time=START_OF_DAY + timedelta(hours=4)
        )
        scheduled_cases = harness.service_account_client.query_cases(
            query=query, only_primary_surgeons=True
        )

        assert len(scheduled_cases[0].case_staff) == 1
        assert scheduled_cases[0].case_staff[0].role == case_staff_role_2
        assert scheduled_cases[0].case_staff[0].staff.first_name == staff_2.first_name
        assert scheduled_cases[0].case_staff[0].staff.last_name == staff_2.last_name

    @pytest.mark.asyncio
    async def test_query_cases_returns_staff_gql(self, async_session: AsyncSession) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))

        await harness.case_store.create_case(case=case_1, session=async_session)

        staff_1 = mock_staff(external_staff_id="1234")
        staff_2 = mock_staff(external_staff_id="5678")
        [db_staff_1, db_staff_2] = await harness.staff_store.upsert_staff(
            staff=[staff_1, staff_2], session=async_session
        )

        case_staff_role_1 = "CRNA"
        case_staff_role_2 = PRIMARY_SURGEON_ROLES[0]
        case_staff = [
            mock_case_staff_input(
                case_id=case_1.case_id, staff_id=db_staff_1.id, role=case_staff_role_1
            ),
            mock_case_staff_input(
                case_id=case_1.case_id, staff_id=db_staff_2.id, role=case_staff_role_2
            ),
        ]
        await harness.case_staff_store.upsert_case_staff(case_staff=case_staff)

        scheduled_case_query = apella_schema.Query.scheduled_cases.args(
            query=GQLScheduledCaseQueryInput(
                min_time=START_OF_DAY, max_time=START_OF_DAY + timedelta(hours=4)
            )
        ).select(
            apella_schema.ScheduledCaseConnection.edges.select(
                apella_schema.ScheduledCaseEdge.node.select(
                    apella_schema.ScheduledCase.case_staff.args(only_primary_surgeons=True).select(
                        apella_schema.CaseStaff.staff.select(
                            apella_schema.Staff.first_name,
                            apella_schema.Staff.last_name,
                        ),
                        apella_schema.CaseStaff.role,
                    ),
                    apella_schema.ScheduledCase.external_case_id,
                )
            )
        )
        scheduled_cases = harness.service_account_client.query_graphql_from_schema(
            scheduled_case_query
        ).scheduled_cases.edges

        assert len(scheduled_cases[0].node.case_staff) == 1
        assert scheduled_cases[0].node.case_staff[0].role == case_staff_role_2
        assert scheduled_cases[0].node.case_staff[0].staff.first_name == staff_2.first_name
        assert scheduled_cases[0].node.case_staff[0].staff.last_name == staff_2.last_name
        assert scheduled_cases[0].node.external_case_id == case_1.external_case_id

    @pytest.mark.asyncio
    async def test_query_cases_returns_staff_gql_all_staff(
        self, async_session: AsyncSession
    ) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))

        await harness.case_store.create_case(case=case_1, session=async_session)

        staff_1 = mock_staff(external_staff_id="1234")
        staff_2 = mock_staff(external_staff_id="5678")
        [db_staff_1, db_staff_2] = await harness.staff_store.upsert_staff(
            staff=[staff_1, staff_2], session=async_session
        )

        case_staff_role_1 = "CRNA"
        case_staff_role_2 = PRIMARY_SURGEON_ROLES[0]
        case_staff = [
            mock_case_staff_input(
                case_id=case_1.case_id, staff_id=db_staff_1.id, role=case_staff_role_1
            ),
            mock_case_staff_input(
                case_id=case_1.case_id, staff_id=db_staff_2.id, role=case_staff_role_2
            ),
        ]
        await harness.case_staff_store.upsert_case_staff(case_staff=case_staff)

        scheduled_case_query = apella_schema.Query.scheduled_cases.args(
            query=GQLScheduledCaseQueryInput(
                min_time=START_OF_DAY, max_time=START_OF_DAY + timedelta(hours=4)
            )
        ).select(
            apella_schema.ScheduledCaseConnection.edges.select(
                apella_schema.ScheduledCaseEdge.node.select(
                    apella_schema.ScheduledCase.case_staff.args(only_primary_surgeons=True).select(
                        apella_schema.CaseStaff.staff.select(
                            apella_schema.Staff.first_name,
                            apella_schema.Staff.last_name,
                        ),
                        apella_schema.CaseStaff.role,
                    ),
                    apella_schema.ScheduledCase.case_staff.args(only_primary_surgeons=False)
                    .select(
                        apella_schema.CaseStaff.staff.select(
                            apella_schema.Staff.first_name,
                            apella_schema.Staff.last_name,
                        ),
                        apella_schema.CaseStaff.role,
                    )
                    .alias("onlyPrimarySurgeonsFalse"),
                    apella_schema.ScheduledCase.external_case_id,
                )
            )
        )
        scheduled_cases = harness.service_account_client.query_graphql_from_schema(
            scheduled_case_query
        ).scheduled_cases.edges

        assert len(scheduled_cases[0].node.case_staff) == 1
        assert len(scheduled_cases[0].node.get_aliased_field("onlyPrimarySurgeonsFalse")) == 2
        assert scheduled_cases[0].node.case_staff[0].role == case_staff_role_2
        assert scheduled_cases[0].node.case_staff[0].staff.first_name == staff_2.first_name
        assert scheduled_cases[0].node.case_staff[0].staff.last_name == staff_2.last_name
        assert scheduled_cases[0].node.external_case_id == case_1.external_case_id

    @pytest.mark.asyncio
    async def test_patient_class_returns_as_enum(self, async_session: AsyncSession):
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))

        await harness.case_store.create_case(case=case_1, session=async_session)

        scheduled_case_query = apella_schema.Query.scheduled_cases.args(
            query=GQLScheduledCaseQueryInput(
                min_time=START_OF_DAY, max_time=START_OF_DAY + timedelta(hours=4)
            )
        ).select(
            apella_schema.ScheduledCaseConnection.edges.select(
                apella_schema.ScheduledCaseEdge.node.select(
                    apella_schema.ScheduledCase.patient_class,
                )
            )
        )
        scheduled_cases = harness.service_account_client.query_graphql_from_schema(
            scheduled_case_query
        ).scheduled_cases.edges

        assert scheduled_cases[0].node.patient_class.name == case_1.patient_class.name

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "types_to_search,expected",
        [(["OBSERVED_IN_ROOM"], 1), (["OBSERVED_OUT_OF_ROOM"], 0), (Empty(), 1)],
    )
    async def test_query_cases_observations(
        self, types_to_search: Union[list[str], Empty], expected: int, async_session: AsyncSession
    ) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))

        await harness.case_store.create_case(case=case_1, session=async_session)

        observation_1 = mock_observation(case_id=case_1.case_id)

        observation_1 = (
            await harness.observation_store.upsert_observations(observations=[observation_1])
        )[0]

        query = apella_schema.Query.scheduled_cases.args(
            query=GQLScheduledCaseQueryInput(
                min_time=START_OF_DAY, max_time=START_OF_DAY + timedelta(hours=4)
            ),
        ).select(
            apella_schema.ScheduledCaseConnection.edges.select(
                apella_schema.ScheduledCaseEdge.node.select(
                    apella_schema.ScheduledCase.id,
                    apella_schema.ScheduledCase.observations.args(types=types_to_search).select(
                        apella_schema.ObservationConnection.edges.select(
                            apella_schema.ObservationEdge.node.select(
                                apella_schema.Observation.id,
                                apella_schema.Observation.observation_type.select(
                                    apella_schema.ObservationType.name
                                ),
                            )
                        )
                    ),
                ),
            )
        )

        results = harness.service_account_client.query_graphql_from_schema(query)
        scheduled_cases = results.scheduled_cases.edges

        assert len(scheduled_cases) == 1
        assert len(scheduled_cases[0].node.observations.edges) == expected
        if scheduled_cases[0].node.observations.edges:
            assert scheduled_cases[0].node.observations.edges[0].node.id == str(observation_1.id)

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "types_to_search,expected",
        [(["OBSERVED_IN_ROOM"], 1), (["OBSERVED_OUT_OF_ROOM"], 0), (Empty(), 1)],
    )
    async def test_query_cases_observations_aliased(
        self, types_to_search: Union[list[str], Empty], expected: int, async_session: AsyncSession
    ) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))

        await harness.case_store.create_case(case=case_1, session=async_session)

        observation_1 = mock_observation(case_id=case_1.case_id)

        observation_1 = (
            await harness.observation_store.upsert_observations(observations=[observation_1])
        )[0]

        query = apella_schema.Query.scheduled_cases.args(
            query=GQLScheduledCaseQueryInput(
                min_time=START_OF_DAY, max_time=START_OF_DAY + timedelta(hours=4)
            ),
        ).select(
            apella_schema.ScheduledCaseConnection.edges.select(
                apella_schema.ScheduledCaseEdge.node.select(
                    apella_schema.ScheduledCase.id,
                    apella_schema.ScheduledCase.observations.args(types=types_to_search).select(
                        apella_schema.ObservationConnection.edges.select(
                            apella_schema.ObservationEdge.node.select(
                                apella_schema.Observation.id,
                                apella_schema.Observation.observation_type.select(
                                    apella_schema.ObservationType.name
                                ),
                            )
                        )
                    ),
                    apella_schema.ScheduledCase.observations.args()
                    .select(
                        apella_schema.ObservationConnection.edges.select(
                            apella_schema.ObservationEdge.node.select(
                                apella_schema.Observation.id,
                                apella_schema.Observation.observation_type.select(
                                    apella_schema.ObservationType.name
                                ),
                            )
                        )
                    )
                    .alias("all_observations"),
                ),
            )
        )

        results = harness.service_account_client.query_graphql_from_schema(query)
        scheduled_cases = results.scheduled_cases.edges

        assert len(scheduled_cases) == 1
        assert len(scheduled_cases[0].node.observations.edges) == expected
        if scheduled_cases[0].node.observations.edges:
            assert scheduled_cases[0].node.observations.edges[0].node.id == str(observation_1.id)
        assert len(scheduled_cases[0].node.get_aliased_field("all_observations").edges) == 1

    @pytest.mark.asyncio
    async def test_query_case_procedures_and_anesthesia_gql(
        self, mock_anesthesias, mock_procedures, async_session: AsyncSession
    ) -> None:
        case_1 = mock_case(start_time=START_OF_DAY, end_time=START_OF_DAY + timedelta(hours=1))
        procedure_1 = mock_procedures[0]
        anesthesia_1 = mock_anesthesias[0]

        await harness.case_store.create_case(case=case_1, session=async_session)

        apella_schema = ApellaSchema()
        case_procedure_upserts = apella_schema.Mutation.case_procedures_upsert.args(
            input=[
                GQLCaseProceduresUpsertInput(
                    case_id=case_1.case_id,
                    procedure_id=procedure_1.id,
                    anesthesia_id=anesthesia_1.id,
                )
            ]
        ).select(apella_schema.CaseProceduresUpsert.success)
        result = harness.service_account_client.mutate_graphql_from_schema(case_procedure_upserts)
        assert result.case_procedures_upsert is not None
        assert result.case_procedures_upsert.success is True

        cases_edges = harness.service_account_client.execute_graphql(
            f"""
        query {{
            scheduledCases(query: {{
                minTime: "{START_OF_DAY.isoformat()}"
                maxTime: "{(START_OF_DAY + timedelta(hours=1)).isoformat()}"

            }}) {{
                edges{{
                    node{{
                        id
                        primaryCaseProcedures {{
                            procedure {{
                                 name
                            }}
                            anesthesia {{
                                name
                            }}
                        }}
                        caseProcedures {{
                            procedure {{
                                name
                            }}
                            anesthesia {{
                                name
                            }}
                        }}
                    }}
                }}
            }}
        }}
        """
        )["data"]["scheduledCases"]

        assert cases_edges
        case_node = cases_edges["edges"][0]["node"]
        assert case_node["id"] == case_1.case_id
        assert case_node["caseProcedures"][0]["procedure"]["name"] == procedure_1.name
        assert case_node["caseProcedures"][0]["anesthesia"]["name"] == anesthesia_1.name
        assert case_node["primaryCaseProcedures"][0]["procedure"]["name"] == procedure_1.name
        assert case_node["primaryCaseProcedures"][0]["anesthesia"]["name"] == anesthesia_1.name
