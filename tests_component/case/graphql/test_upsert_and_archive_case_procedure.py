from datetime import datetime, timedelta

from typing import List

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from apella_cloud_api import (
    CaseProceduresUpsertAndArchiveInput,
    ScheduledCaseQueryInput,
)
from api_server.services.anesthesia.anesthesia_store import AnesthesiaModel
from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_client_schema import GQLDirection
from apella_cloud_api.new_input_schema import (
    GQLCaseProceduresUpsertAndArchiveInput,
    GQLOrderBy,
    GQLScheduledCaseQueryInput,
)
from apella_cloud_api.new_schema_generator.schema_generator_base_classes import NotFound
from api_server.services.procedures.procedure_store import ProcedureModel
from tests_component import harness
from tests_component.case.graphql.test_query_scheduled_cases import mock_case


def mock_procedure(name: str) -> ProcedureModel:
    procedure = ProcedureModel(
        name=name,
        org_id=harness.org_0.id,
    )

    return procedure


@pytest.mark.asyncio
async def test_upsert_and_archive_case_procedures(
    mock_anesthesias: List[AnesthesiaModel], async_session: AsyncSession
) -> None:
    start_of_day = datetime(2020, 10, 10, 12, 0, 0)
    case_1 = mock_case(start_time=start_of_day, end_time=start_of_day + timedelta(hours=1))
    procedure_1 = mock_procedure(name="heart")
    procedure_2 = mock_procedure(name="endo")
    procedure_3 = mock_procedure(name="knee")
    procedure_4 = mock_procedure(name="elbow")

    await harness.case_store.create_case(case=case_1, session=async_session)

    result_procedures = await harness.procedure_store.upsert_procedures(
        procedures=[procedure_1, procedure_2, procedure_3, procedure_4],
        session=async_session,
    )

    first_set_of_case_procedures = [
        CaseProceduresUpsertAndArchiveInput(
            case_id=case_1.case_id,
            procedure_id=str(procedure.id),
            anesthesia_id=str(mock_anesthesias[0].id),
        )
        for procedure in result_procedures
    ]
    harness.service_account_client.upsert_and_archive_case_procedures(
        case_procedures=first_set_of_case_procedures
    )

    first_query = ScheduledCaseQueryInput(
        min_time=start_of_day, max_time=start_of_day + timedelta(hours=4)
    )
    scheduled_cases = harness.service_account_client.query_scheduled_cases(query=first_query)

    # Assert the procedure
    first_result_procedures = [edge.node for edge in scheduled_cases[0].procedures.edges]
    assert len(first_result_procedures) == 4
    assert first_result_procedures[0].id == str(result_procedures[0].id)
    assert first_result_procedures[1].id == str(result_procedures[1].id)
    assert first_result_procedures[2].id == str(result_procedures[2].id)
    assert first_result_procedures[3].id == str(result_procedures[3].id)

    first_set_of_case_procedures = [
        CaseProceduresUpsertAndArchiveInput(
            case_id=case_1.case_id,
            procedure_id=str(procedure.id),
            anesthesia_id=str(mock_anesthesias[0].id),
        )
        for procedure in result_procedures[0:3]
    ]
    harness.service_account_client.upsert_and_archive_case_procedures(
        case_procedures=first_set_of_case_procedures
    )
    second_query = ScheduledCaseQueryInput(
        min_time=start_of_day, max_time=start_of_day + timedelta(hours=4)
    )
    scheduled_cases = harness.service_account_client.query_scheduled_cases(query=second_query)
    # Assert the procedure
    second_result_procedures = [edge.node for edge in scheduled_cases[0].procedures.edges]
    assert len(second_result_procedures) == 3
    assert second_result_procedures[0].id == str(result_procedures[0].id)
    assert second_result_procedures[1].id == str(result_procedures[1].id)
    assert second_result_procedures[2].id == str(result_procedures[2].id)


@pytest.mark.asyncio
async def test_upsert_and_archive_case_procedures_only_archives_for_given_case(
    async_session: AsyncSession,
) -> None:
    start_of_day = datetime(2020, 10, 10, 12, 0, 0)
    case_1 = mock_case(start_time=start_of_day, end_time=start_of_day + timedelta(hours=1))
    case_2 = mock_case(start_time=start_of_day, end_time=start_of_day + timedelta(hours=1))
    procedure_1 = mock_procedure(name="heart")
    procedure_2 = mock_procedure(name="endo")
    procedure_3 = mock_procedure(name="knee")
    procedure_4 = mock_procedure(name="elbow")
    procedure_5 = mock_procedure(name="wrist")

    await harness.case_store.create_case(case=case_1, session=async_session)
    await harness.case_store.create_case(case=case_2, session=async_session)

    result_procedures = await harness.procedure_store.upsert_procedures(
        procedures=[procedure_1, procedure_2, procedure_3, procedure_4, procedure_5],
        session=async_session,
    )

    first_set_of_case_procedures = [
        CaseProceduresUpsertAndArchiveInput(case_id=case_1.case_id, procedure_id=str(procedure.id))
        for procedure in result_procedures[0:4]
    ]
    assert len(first_set_of_case_procedures) == 4
    first_set_of_case_procedures += [
        CaseProceduresUpsertAndArchiveInput(
            case_id=case_2.case_id, procedure_id=str(result_procedures[4].id)
        )
    ]
    harness.service_account_client.upsert_and_archive_case_procedures(
        case_procedures=first_set_of_case_procedures
    )

    first_query = ScheduledCaseQueryInput(
        min_time=start_of_day, max_time=start_of_day + timedelta(hours=4)
    )
    scheduled_cases = harness.service_account_client.query_scheduled_cases(query=first_query)

    # Assert the procedure
    first_result_procedures = [edge.node for edge in scheduled_cases[0].procedures.edges]
    first_result_procedures += [edge.node for edge in scheduled_cases[1].procedures.edges]
    assert len(first_result_procedures) == 5
    assert first_result_procedures[0].id == str(result_procedures[0].id)
    assert first_result_procedures[1].id == str(result_procedures[1].id)
    assert first_result_procedures[2].id == str(result_procedures[2].id)
    assert first_result_procedures[3].id == str(result_procedures[3].id)
    assert first_result_procedures[4].id == str(result_procedures[4].id)

    first_set_of_case_procedures = [
        CaseProceduresUpsertAndArchiveInput(case_id=case_1.case_id, procedure_id=str(procedure.id))
        for procedure in result_procedures[0:3]
    ]
    harness.service_account_client.upsert_and_archive_case_procedures(
        case_procedures=first_set_of_case_procedures
    )
    second_query = ScheduledCaseQueryInput(
        min_time=start_of_day, max_time=start_of_day + timedelta(hours=4)
    )
    scheduled_cases = harness.service_account_client.query_scheduled_cases(query=second_query)
    # Assert the procedure
    second_result_procedures = [edge.node for edge in scheduled_cases[0].procedures.edges]
    second_result_procedures += [edge.node for edge in scheduled_cases[1].procedures.edges]

    # result_procedures[3] is not present
    assert len(second_result_procedures) == 4
    assert second_result_procedures[0].id == str(result_procedures[0].id)
    assert second_result_procedures[1].id == str(result_procedures[1].id)
    assert second_result_procedures[2].id == str(result_procedures[2].id)
    assert second_result_procedures[3].id == str(result_procedures[4].id)


@pytest.mark.asyncio
async def test_upsert_and_archive_case_procedures_only_archives_for_given_case_with_hierarchy(
    async_session: AsyncSession,
) -> None:
    start_of_day = datetime(2020, 10, 10, 12, 0, 0)
    case_1 = mock_case(start_time=start_of_day, end_time=start_of_day + timedelta(hours=1))
    case_2 = mock_case(start_time=start_of_day, end_time=start_of_day + timedelta(hours=1))
    procedure_1 = mock_procedure(name="heart")
    procedure_2 = mock_procedure(name="endo")
    procedure_3 = mock_procedure(name="knee")
    procedure_4 = mock_procedure(name="elbow")
    procedure_5 = mock_procedure(name="wrist")

    await harness.case_store.create_case(case=case_1, session=async_session)
    await harness.case_store.create_case(case=case_2, session=async_session)

    result_procedures = await harness.procedure_store.upsert_procedures(
        procedures=[procedure_1, procedure_2, procedure_3, procedure_4, procedure_5],
        session=async_session,
    )

    first_set_of_case_procedures = [
        CaseProceduresUpsertAndArchiveInput(
            case_id=case_1.case_id, procedure_id=str(procedure.id), hierarchy=1
        )
        for procedure in result_procedures[0:4]
    ]
    assert len(first_set_of_case_procedures) == 4
    first_set_of_case_procedures += [
        CaseProceduresUpsertAndArchiveInput(
            case_id=case_2.case_id, procedure_id=str(result_procedures[4].id)
        )
    ]
    harness.service_account_client.upsert_and_archive_case_procedures(
        case_procedures=first_set_of_case_procedures
    )

    first_query = ScheduledCaseQueryInput(
        min_time=start_of_day, max_time=start_of_day + timedelta(hours=4)
    )
    scheduled_cases = harness.service_account_client.query_scheduled_cases(query=first_query)

    # Assert the procedure
    first_result_procedures = [edge.node for edge in scheduled_cases[0].procedures.edges]
    first_result_procedures += [edge.node for edge in scheduled_cases[1].procedures.edges]
    assert len(first_result_procedures) == 5
    assert first_result_procedures[0].id == str(result_procedures[0].id)
    assert first_result_procedures[1].id == str(result_procedures[1].id)
    assert first_result_procedures[2].id == str(result_procedures[2].id)
    assert first_result_procedures[3].id == str(result_procedures[3].id)
    assert first_result_procedures[4].id == str(result_procedures[4].id)
    assert first_result_procedures[4].hierarchy is None

    second_set_of_case_procedures = [
        CaseProceduresUpsertAndArchiveInput(
            case_id=case_1.case_id, procedure_id=str(procedure.id), hierarchy=1
        )
        for procedure in result_procedures[0:3]
    ]
    harness.service_account_client.upsert_and_archive_case_procedures(
        case_procedures=second_set_of_case_procedures
    )
    second_query = ScheduledCaseQueryInput(
        min_time=start_of_day, max_time=start_of_day + timedelta(hours=4)
    )
    scheduled_cases = harness.service_account_client.query_scheduled_cases(query=second_query)
    # Assert the procedure
    second_result_procedures = [edge.node for edge in scheduled_cases[0].procedures.edges]
    second_result_procedures += [edge.node for edge in scheduled_cases[1].procedures.edges]

    # result_procedures[3] is not present
    assert len(second_result_procedures) == 4
    assert {p.id for p in second_result_procedures} == {
        str(p.id)
        for p in [
            result_procedures[0],
            result_procedures[1],
            result_procedures[2],
            result_procedures[4],
        ]
    }


@pytest.mark.asyncio
async def test_upsert_and_archive_case_procedures_only_archives_for_given_case_with_hierarchy_gql(
    async_session: AsyncSession,
) -> None:
    start_of_day = datetime(2020, 10, 10, 12, 0, 0)
    case_1 = mock_case(start_time=start_of_day, end_time=start_of_day + timedelta(hours=1))
    case_2 = mock_case(
        start_time=start_of_day + timedelta(hours=2), end_time=start_of_day + timedelta(hours=4)
    )
    procedure_1 = mock_procedure(name="heart")
    procedure_2 = mock_procedure(name="endo")
    procedure_3 = mock_procedure(name="knee")
    procedure_4 = mock_procedure(name="elbow")
    procedure_5 = mock_procedure(name="wrist")

    await harness.case_store.create_case(case=case_1, session=async_session)
    await harness.case_store.create_case(case=case_2, session=async_session)

    procedures = await harness.procedure_store.upsert_procedures(
        procedures=[procedure_1, procedure_2, procedure_3, procedure_4, procedure_5],
        session=async_session,
    )

    created_procedure_heart = next(p for p in procedures if p.name == "heart")
    created_procedure_endo = next(p for p in procedures if p.name == "endo")
    created_procedure_knee = next(p for p in procedures if p.name == "knee")
    created_procedure_elbow = next(p for p in procedures if p.name == "elbow")
    created_procedure_wrist = next(p for p in procedures if p.name == "wrist")

    first_set_of_case_procedures = [
        GQLCaseProceduresUpsertAndArchiveInput(
            case_id=case_1.case_id, procedure_id=procedure.id, hierarchy=1
        )
        for procedure in [
            created_procedure_heart,
            created_procedure_endo,
            created_procedure_knee,
            created_procedure_elbow,
        ]
    ]

    first_set_of_case_procedures += [
        GQLCaseProceduresUpsertAndArchiveInput(
            case_id=case_2.case_id, procedure_id=created_procedure_wrist.id
        )
    ]
    apella_schema = ApellaSchema()
    first_mutation = apella_schema.Mutation.case_procedures_upsert_and_archive.args(
        input=first_set_of_case_procedures
    ).select(apella_schema.CaseProceduresUpsertAndArchive.success)
    harness.service_account_client.mutate_graphql_from_schema(first_mutation)

    query = apella_schema.Query.cases.args(
        query=GQLScheduledCaseQueryInput(
            min_time=start_of_day, max_time=start_of_day + timedelta(hours=4)
        ),
        order_by=[GQLOrderBy(sort="scheduled_start_time", direction=GQLDirection.ASC)],
    ).select(
        apella_schema.ScheduledCaseConnection.edges.select(
            apella_schema.ScheduledCaseEdge.node.select(
                apella_schema.ScheduledCase.case_procedures.select(
                    apella_schema.CaseProcedure.procedure.select(
                        apella_schema.Procedure.id,
                        apella_schema.Procedure.name,
                    ),
                    apella_schema.CaseProcedure.hierarchy,
                )
            )
        )
    )

    scheduled_cases = [
        edge.node
        for edge in harness.service_account_client.query_graphql_from_schema(query).cases.edges
    ]
    # Assert the procedure
    first_case_procedures = sorted(
        scheduled_cases[0].case_procedures,
        key=lambda x: x.procedure.name,
    )
    second_case_procedures = scheduled_cases[1].case_procedures
    assert isinstance(first_case_procedures, list)
    assert isinstance(second_case_procedures, list)

    # assert case #1
    expected_first_case_procedures = [
        created_procedure_elbow,
        created_procedure_endo,
        created_procedure_heart,
        created_procedure_knee,
    ]
    for first_case_procedure in first_case_procedures:
        assert any(
            first_case_procedure.procedure.id == str(expected_first_case_procedure.id)
            for expected_first_case_procedure in expected_first_case_procedures
        )
        assert first_case_procedure.hierarchy == 1

    # assert case #2
    assert second_case_procedures[0].procedure.id == str(created_procedure_wrist.id)
    assert second_case_procedures[0].hierarchy == NotFound()


@pytest.mark.asyncio
async def test_upsert_and_archive_case_procedures_only_archives_for_given_case_with_null_hierarchy(
    async_session: AsyncSession,
) -> None:
    start_of_day = datetime(2020, 10, 10, 12, 0, 0)
    case_1 = mock_case(start_time=start_of_day, end_time=start_of_day + timedelta(hours=1))
    case_2 = mock_case(start_time=start_of_day, end_time=start_of_day + timedelta(hours=1))
    procedure_1 = mock_procedure(name="heart")
    procedure_2 = mock_procedure(name="endo")
    procedure_3 = mock_procedure(name="knee")
    procedure_4 = mock_procedure(name="elbow")
    procedure_5 = mock_procedure(name="wrist")

    await harness.case_store.create_case(case=case_1, session=async_session)
    await harness.case_store.create_case(case=case_2, session=async_session)

    result_procedures = await harness.procedure_store.upsert_procedures(
        procedures=[procedure_1, procedure_2, procedure_3, procedure_4, procedure_5],
        session=async_session,
    )

    first_set_of_case_procedures = [
        CaseProceduresUpsertAndArchiveInput(case_id=case_1.case_id, procedure_id=str(procedure.id))
        for procedure in result_procedures[0:4]
    ]
    assert len(first_set_of_case_procedures) == 4
    first_set_of_case_procedures += [
        CaseProceduresUpsertAndArchiveInput(
            case_id=case_2.case_id, procedure_id=str(result_procedures[4].id)
        )
    ]
    harness.service_account_client.upsert_and_archive_case_procedures(
        case_procedures=first_set_of_case_procedures
    )

    first_query = ScheduledCaseQueryInput(
        min_time=start_of_day, max_time=start_of_day + timedelta(hours=4)
    )
    scheduled_cases = harness.service_account_client.query_scheduled_cases(query=first_query)

    # Assert the procedure
    first_result_procedures = [edge.node for edge in scheduled_cases[0].procedures.edges]
    first_result_procedures += [edge.node for edge in scheduled_cases[1].procedures.edges]
    assert len(first_result_procedures) == 5

    assert {p.id for p in first_result_procedures[0:5]} == {
        str(p.id) for p in result_procedures[0:5]
    }

    second_set_of_case_procedures = [
        CaseProceduresUpsertAndArchiveInput(case_id=case_1.case_id, procedure_id=str(procedure.id))
        for procedure in result_procedures[0:3]
    ]
    harness.service_account_client.upsert_and_archive_case_procedures(
        case_procedures=second_set_of_case_procedures
    )
    second_query = ScheduledCaseQueryInput(
        min_time=start_of_day, max_time=start_of_day + timedelta(hours=4)
    )
    scheduled_cases = harness.service_account_client.query_scheduled_cases(query=second_query)
    # Assert the procedure
    second_result_procedures = [edge.node for edge in scheduled_cases[0].procedures.edges]
    second_result_procedures += [edge.node for edge in scheduled_cases[1].procedures.edges]

    # result_procedures[3] is not present
    assert len(second_result_procedures) == 4
    assert {p.id for p in second_result_procedures[0:4]} == {
        str(p.id)
        for p in [
            result_procedures[0],
            result_procedures[1],
            result_procedures[2],
            result_procedures[4],
        ]
    }
