import uuid
from copy import copy
from datetime import datetime, timedelta
from typing import Optional

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from api_server.services.case.case_status import SCHEDULED
from api_server.services.case.case_store import Case, PatientClass
from api_server.services.case_derived_properties.case_derived_properties_store import (
    CaseDerivedProperties,
)
from api_server.services.procedures.procedure_store import ProcedureModel
from tests_component import harness


def mock_procedure(name: str) -> ProcedureModel:
    procedure = ProcedureModel(
        name=name,
        org_id=harness.org_0.id,
    )

    return procedure


def mock_case(
    start_time: datetime,
    end_time: datetime,
    status: str = SCHEDULED,
    case_classification_types_id: str = "CASE_CLASSIFICATION_ELECTIVE",
    is_add_on: Optional[bool] = None,
) -> Case:
    case = Case()
    case.external_case_id = str(uuid.uuid4())
    case.case_id = str(uuid.uuid4())
    case.org_id = harness.org_0.id
    case.site_id = harness.site_0.id
    case.room_id = harness.room_0.id
    case.case_classification_types_id = case_classification_types_id
    case.scheduled_start_time = start_time
    case.scheduled_end_time = end_time
    case.status = status
    case.updated_time = start_time - timedelta(days=1)
    case.created_time = start_time - timedelta(days=2)
    case.patient_class = PatientClass.HOSPITAL_OUTPATIENT_SURGERY
    case.is_add_on = is_add_on

    return case


def mock_case_derived_properties(
    case: Case, preceding_case_id: Optional[str] = None
) -> CaseDerivedProperties:
    cdp = CaseDerivedProperties()
    cdp.case_id = case.case_id
    cdp.is_in_flip_room = True
    cdp.preceding_case_id = preceding_case_id
    return cdp


class TestUpsertCaseDerivedProperties:
    @pytest.mark.asyncio
    async def test_upsert_case_derived_properties(self, async_session: AsyncSession) -> None:
        start_of_day = datetime(2020, 10, 10, 12, 0, 0)
        case_1 = mock_case(start_time=start_of_day, end_time=start_of_day + timedelta(hours=1))
        case_2 = mock_case(
            start_time=start_of_day + timedelta(hours=2),
            end_time=start_of_day + timedelta(hours=3),
            status="cancelled",
        )
        case_3 = mock_case(
            start_time=start_of_day + timedelta(hours=4),
            end_time=start_of_day + timedelta(hours=5),
        )

        await harness.case_store.create_case(case=case_1, session=async_session)
        await harness.case_store.create_case(case=case_2, session=async_session)
        await harness.case_store.create_case(case=case_3, session=async_session)

        case_1_cdp = mock_case_derived_properties(case_1, str(case_2.case_id))
        case_3_cdp = mock_case_derived_properties(case_3, str(case_2.case_id))
        case_ids = await harness.case_derived_properties_store.upsert_case_derived_properties(
            case_derived_properties=[
                case_1_cdp,
                case_3_cdp,
            ]
        )

        case_3_cdp_copy = copy(case_3_cdp)
        case_3_cdp_copy.is_in_flip_room = not case_3_cdp_copy.is_in_flip_room

        case_ids_updated = (
            await harness.case_derived_properties_store.upsert_case_derived_properties(
                case_derived_properties=[
                    case_3_cdp_copy,
                ]
            )
        )

        items = await harness.case_derived_properties_store.get_by_case_ids(
            [case_1.case_id, case_3.case_id]
        )

        map_ids = {cdp.case_id: cdp for cdp in items}

        # Assert the case
        assert len(case_ids) == 2
        assert len(case_ids_updated) == 1
        assert case_3_cdp.case_id in case_ids
        assert case_1_cdp.case_id in case_ids
        assert case_3_cdp_copy.case_id in case_ids_updated
        assert len(items) == 2
        assert case_1.case_id in map_ids
        assert map_ids[case_1.case_id].case_id == case_1_cdp.case_id
        assert map_ids[case_1.case_id].is_in_flip_room == case_1_cdp.is_in_flip_room
        assert map_ids[case_1.case_id].preceding_case_id == case_1_cdp.preceding_case_id
        assert case_3.case_id in map_ids
        assert map_ids[case_3.case_id].case_id == case_3_cdp.case_id
        assert map_ids[case_3.case_id].is_in_flip_room == case_3_cdp_copy.is_in_flip_room
        assert map_ids[case_3.case_id].preceding_case_id == case_3_cdp_copy.preceding_case_id
