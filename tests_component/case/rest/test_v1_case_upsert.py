from typing import Optional

import pytest

from apella_cloud_api.dtos import CaseInfoDto, CaseUpdateDto
from apella_cloud_api.exceptions import NotAuthorized
from api_server.services.case.case_store import PatientClass
from tests_component import harness


def sample_update(patient_class: Optional[PatientClass] = None) -> CaseUpdateDto:
    case = CaseInfoDto()
    case.external_case_id = harness.case_0.external_case_id
    case.organization_id = harness.case_0.org_id
    case.site_id = harness.case_0.site_id
    case.room_id = harness.case_0.room_id
    case.case_classification_types_id = harness.case_0.case_classification_types_id
    case.scheduled_start_time = harness.case_0.scheduled_start_time
    case.scheduled_end_time = harness.case_0.scheduled_end_time
    case.status = "canceled"
    case.external_service_line_id = harness.service_line_0.external_service_line_id
    if patient_class is not None:
        case.patient_class = patient_class.name
    case.cancellation_reason = ["reason line 1", "reason line 2", "reason line 3"]

    update = CaseUpdateDto(case=case, external_message_id=harness.case_0_raw.external_message_id)

    return update


def test_upsert_case_requires_auth() -> None:
    with pytest.raises(NotAuthorized):
        harness.no_auth_client.upsert_case(sample_update())


def test_upsert_case_with_no_permissions() -> None:
    with pytest.raises(NotAuthorized):
        harness.surgeon_client.upsert_case(sample_update())


@pytest.mark.asyncio
async def test_upsert_case_as_service_account() -> None:
    patient_class = PatientClass.HOSPITAL_OUTPATIENT_SURGERY
    update = sample_update(patient_class)
    case_response = harness.service_account_client.upsert_case(update)

    assert case_response.external_case_id == update.case.external_case_id
    assert case_response.status == update.case.status
    assert case_response.case_id == harness.case_0.case_id
    # For now, we don't populate external_service_line_id - resolving in RT-350
    assert case_response.external_service_line_id is None

    # Confirm it's in the DB
    case_in_db = await harness.case_store.get_case(case_id=case_response.case_id)
    assert case_in_db.status == update.case.status
    assert case_in_db.scheduled_start_time == harness.case_0.scheduled_start_time
    assert case_in_db.service_line_id == harness.service_line_0.id
    assert case_in_db.patient_class == patient_class
    assert case_in_db.cancellation_reason == update.case.cancellation_reason

    # Confirm case raw was updated with Case Id
    case_raw_in_db = await harness.case_store.get_case_raw(update.external_message_id)
    assert case_raw_in_db.case_id is not None
    assert case_raw_in_db.case_id == case_in_db.case_id


@pytest.mark.asyncio
async def test_upsert_case_classification_null_allowed() -> None:
    update = sample_update()

    case_response = harness.service_account_client.upsert_case(update)
    assert case_response.external_case_id == update.case.external_case_id
    assert case_response.status == update.case.status
    assert case_response.case_id == harness.case_0.case_id
    # For now, we don't populate external_service_line_id - resolving in RT-350
    assert case_response.external_service_line_id is None

    # Confirm its in the DB
    case_in_db = await harness.case_store.get_case(case_id=case_response.case_id)
    assert case_in_db.status == update.case.status
    assert case_in_db.scheduled_start_time == harness.case_0.scheduled_start_time
    assert case_in_db.case_classification_types_id is None
    assert case_in_db.service_line_id == harness.service_line_0.id
    assert case_in_db.cancellation_reason == update.case.cancellation_reason


def test_upsert_case_service_line_does_not_exist() -> None:
    update = sample_update()
    update.case.external_service_line_id = "New service line"

    try:
        case_response = harness.service_account_client.upsert_case(update)
    except Exception as exc:
        assert False, f"'New service line should not throw {exc}"

    assert case_response.external_case_id == update.case.external_case_id

    # For now, we don't populate external_service_line_id - resolivng in RT-350
    # assert case_response.external_service_line_id == update.case.external_service_line_id


def test_upsert_case_service_line_different_case() -> None:
    update = sample_update()
    update.case.external_service_line_id = "Ort"

    try:
        case_response = harness.service_account_client.upsert_case(update)
    except Exception as exc:
        assert False, f"'different cased service line should not throw {exc}"

    assert case_response.external_case_id == update.case.external_case_id

    # For now, we don't populate external_service_line_id - resolivng in RT-350
    # assert case_response.external_service_line_id == update.case.external_service_line_id


@pytest.mark.asyncio
async def test_upsert_case_toggle_is_add_on() -> None:
    test_case = await harness.case_store.get_case(case_id=harness.case_0.case_id)
    assert type(test_case.is_add_on) is bool

    is_original_add_on = test_case.is_add_on

    case_info_dto = test_case.to_dto()
    case_info_dto.is_add_on = not is_original_add_on

    update = CaseUpdateDto(
        case=case_info_dto, external_message_id=harness.case_0_raw.external_message_id
    )

    case_response = harness.service_account_client.upsert_case(update)
    assert case_response.is_add_on is not is_original_add_on

    updated_db_case = await harness.case_store.get_case(case_id=harness.case_0.case_id)
    assert updated_db_case.is_add_on is not is_original_add_on


@pytest.mark.asyncio
async def test_upsert_case_unset_is_add_on() -> None:
    test_case = await harness.case_store.get_case(case_id=harness.case_0.case_id)
    assert type(test_case.is_add_on) is bool

    update = sample_update()
    assert update.case.is_add_on is None

    case_response = harness.service_account_client.upsert_case(update)
    assert case_response.is_add_on is None

    updated_db_case = await harness.case_store.get_case(case_id=harness.case_0.case_id)
    assert updated_db_case.is_add_on is None
