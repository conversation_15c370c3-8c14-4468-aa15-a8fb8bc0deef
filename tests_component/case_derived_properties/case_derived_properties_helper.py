from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional
from uuid import uuid4
import uuid
from api_server.services.events.event_models import EventModel
from api_server.services.events.source_type import PREDICTION
from api_server.services.phases.phase_store import PhaseModel, PhaseStatus
from api_server.services.phases.phases_models import PhaseType
from api_server.services.phases.source_type import UNIFIED
from api_server.services.room.room_store import RoomModel
from api_server.services.staff.staff_store import StaffModel
from sqlalchemy.ext.asyncio import AsyncSession

from api_server.services.case.case_staff_store import (
    PRIMARY_SURGEON_ROLES,
    CaseStaffModel,
)
from api_server.services.case.case_status import SCHEDULED
from api_server.services.case.case_store import Case
from tests_component import harness


async def create_test_case_with_staff(
    session: AsyncSession,
    scheduled_start_time: datetime,
    duration_minutes: int = 60,
    room: Optional[RoomModel] = None,
    case_classification_types_id: Optional[str] = None,
    status: str = SCHEDULED,
) -> Case:
    case = await create_test_case(
        session=session,
        scheduled_start_time=scheduled_start_time,
        duration_minutes=duration_minutes,
        room=room,
        case_classification_types_id=case_classification_types_id,
        status=status,
    )

    await create_case_staff(case_id=case.case_id)

    return case


async def create_test_case(
    session: AsyncSession,
    scheduled_start_time: datetime,
    duration_minutes: int = 60,
    room: Optional[RoomModel] = None,
    case_classification_types_id: Optional[str] = None,
    status: str = SCHEDULED,
) -> Case:
    case = Case()
    case.case_id = str(uuid4())
    case.external_case_id = f"test_case_{case.case_id[:8]}"
    room = room or harness.room_0
    case.room_id = room.id
    case.site_id = room.site_id
    case.org_id = room.org_id
    case.case_classification_types_id = case_classification_types_id
    case.scheduled_start_time = scheduled_start_time
    case.scheduled_end_time = scheduled_start_time + timedelta(minutes=duration_minutes)
    case.status = status
    await harness.case_store.create_case(case=case, session=session)
    return case


async def create_case_staff(case_id: str, staff_id: Optional[uuid.UUID] = None) -> CaseStaffModel:
    """Creates a test case staff member associated with a given case ID."""
    case_staff = CaseStaffModel()
    if staff_id is None:
        [staff] = await harness.staff_store.upsert_staff(
            [
                StaffModel(
                    external_staff_id=str(uuid4()),
                    first_name="Gregory",
                    last_name="House",
                    org_id=harness.org_0.id,
                )
            ],
        )

        case_staff.staff_id = staff.id
    else:
        case_staff.staff_id = staff_id
    case_staff.case_id = case_id
    case_staff.role = PRIMARY_SURGEON_ROLES[0]
    await harness.case_staff_store.upsert_case_staff([case_staff])

    return case_staff


async def create_event(
    event_type_id: str,
    start_time: datetime,
    room: RoomModel,
) -> EventModel:
    event = EventModel(
        id=str(uuid.uuid4()),
        event_type_id=event_type_id,
        start_time=start_time,
        process_timestamp=start_time,
        org_id=room.org_id,
        site_id=room.site_id,
        room_id=room.id,
        source=UNIFIED,
        source_type=PREDICTION,
        labels=[],
    )
    await harness.event_store.upsert_events([event])
    return event


async def create_phase(
    start_time: datetime,
    room: RoomModel,
    case_id: Optional[str] = None,
    source_type: str = UNIFIED,
    phase_type: PhaseType = PhaseType.CASE,
    end_time: Optional[datetime] = None,
) -> PhaseModel:
    start_event = await create_event(
        room=room, start_time=start_time, event_type_id="patient_wheels_in"
    )
    end_event = (
        await create_event(room=room, start_time=end_time, event_type_id="patient_wheels_out")
        if end_time is not None
        else None
    )
    phase = PhaseModel()
    phase.id = uuid4()
    phase.room_id = room.id
    phase.site_id = room.site_id
    phase.org_id = room.org_id
    phase.type_id = phase_type.name
    phase.start_event_id = start_event.id
    if end_event is not None:
        phase.end_event_id = str(end_event.id)
    phase.case_id = case_id
    phase.source_type = source_type
    phase.status = PhaseStatus.VALID
    phase.start_event = start_event
    phase.end_event = end_event
    await harness.phase_store.upsert_phases([phase])
    return phase


async def create_cases_with_same_room_and_surgeon(
    session: AsyncSession,
    scheduled_start_times: list[datetime],
    room: Optional[RoomModel] = None,
    staff_id: Optional[uuid.UUID] = None,
) -> list[Case]:
    if room is None:
        room = harness.room_0

    cases = [
        await create_test_case(
            session=session,
            scheduled_start_time=start_time,
            duration_minutes=60,
            room=room,
        )
        for start_time in scheduled_start_times
    ]

    for case in cases:
        await create_case_staff(case_id=case.case_id, staff_id=staff_id)

    return cases
