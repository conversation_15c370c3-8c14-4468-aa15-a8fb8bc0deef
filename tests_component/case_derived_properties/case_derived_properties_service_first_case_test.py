from datetime import datetime, timedelta, time
from zoneinfo import ZoneInfo
from apella_cloud_api.api_server_schema import ProcessCaseDerivedPropertiesInput
from api_server.services.site.site_store import Site
from sqlalchemy import StaticPool
from sqlalchemy.ext.asyncio import AsyncSession

from api_server.services.case.case_store import Case

from api_server.services.first_case_config.first_case_config_store import (
    RoomFirstCaseConfig,
    SiteFirstCaseConfig,
)
from databases.sql import Base, engine_provider
import pytest

import pytest_asyncio


from tests_component.case_derived_properties.case_derived_properties_helper import (
    create_cases_with_same_room_and_surgeon,
    create_phase,
    create_test_case_with_staff,
    create_case_staff,
    create_test_case,
)
from tests_component import harness

TEST_DAY_7AM = datetime(2021, 8, 1, 7, 0, 0, tzinfo=ZoneInfo("America/Los_Angeles"))


async def assert_first_cases(
    site_and_dates: list[tuple[Site, datetime]],
    first_cases: list[Case] = [],
    not_first_cases: list[Case] = [],
) -> None:
    harness.service_account_client.process_case_derived_properties(
        input=[
            ProcessCaseDerivedPropertiesInput(site_id=site.id, date=date.date())
            for site, date in site_and_dates
        ]
    )

    first_case_cdps = await harness.case_derived_properties_store.get_by_case_ids(
        [c.case_id for c in first_cases]
    )
    assert len(first_case_cdps) == len(first_cases), (
        "did not retrieve same number of first_cases with CaseDerivedProperties records"
    )

    not_first_case_cdps = await harness.case_derived_properties_store.get_by_case_ids(
        [c.case_id for c in not_first_cases]
    )
    assert len(not_first_case_cdps) == len(not_first_cases), (
        "did not retrieve same number of not_first_cases with CaseDerivedProperties records"
    )

    for cdp in first_case_cdps:
        assert cdp.is_first_case is True

    for cdp in not_first_case_cdps:
        assert cdp.is_first_case is False


@pytest_asyncio.fixture(autouse=True)
async def delete_all_cases() -> None:
    engine = engine_provider(pool_class=StaticPool)
    async with engine.begin() as connection:
        await connection.execute(Base.metadata.tables["observations"].delete())
        await connection.execute(Base.metadata.tables["case_raw"].delete())
        await connection.execute(Base.metadata.tables["cases"].delete())
        await connection.commit()


@pytest.mark.asyncio
async def test_determine_first_case_case_before_morning_hours(async_session: AsyncSession) -> None:
    case_before_morning_hours = await create_test_case_with_staff(
        session=async_session, scheduled_start_time=TEST_DAY_7AM - timedelta(minutes=5)
    )

    case_during_morning_hours = await create_test_case_with_staff(
        session=async_session, scheduled_start_time=TEST_DAY_7AM + timedelta(minutes=5)
    )

    await assert_first_cases(
        site_and_dates=[(harness.site_0, TEST_DAY_7AM)],
        first_cases=[case_during_morning_hours],
        not_first_cases=[case_before_morning_hours],
    )


@pytest.mark.asyncio
async def test_determine_first_case_tiebreak_actual_time(async_session: AsyncSession) -> None:
    case_one, case_two = await create_cases_with_same_room_and_surgeon(
        session=async_session, scheduled_start_times=[TEST_DAY_7AM, TEST_DAY_7AM]
    )

    await create_phase(
        room=harness.room_0,
        start_time=TEST_DAY_7AM,
        end_time=TEST_DAY_7AM + timedelta(minutes=4),
        case_id=case_one.case_id,
    )
    await create_phase(
        room=harness.room_0,
        start_time=TEST_DAY_7AM + timedelta(minutes=5),
        end_time=TEST_DAY_7AM + timedelta(minutes=10),
        case_id=case_two.case_id,
    )

    await assert_first_cases(
        site_and_dates=[(harness.site_0, TEST_DAY_7AM)],
        first_cases=[case_one],
        not_first_cases=[case_two],
    )


@pytest.mark.skip(reason="flaky test")
@pytest.mark.asyncio
async def test_determine_first_case_tiebreak_room_number(async_session: AsyncSession) -> None:
    case_larger_room_id = await create_test_case(
        session=async_session, scheduled_start_time=TEST_DAY_7AM, room=harness.room_1
    )
    case_staff_one = await create_case_staff(case_id=case_larger_room_id.case_id)
    case_smaller_room_id = await create_test_case(
        session=async_session, scheduled_start_time=TEST_DAY_7AM, room=harness.room_0
    )

    await create_case_staff(case_id=case_smaller_room_id.case_id, staff_id=case_staff_one.staff_id)

    await assert_first_cases(
        site_and_dates=[(harness.site_0, TEST_DAY_7AM)],
        first_cases=[case_larger_room_id],
        not_first_cases=[case_smaller_room_id],
    )


@pytest.mark.asyncio
async def test_evaluate_and_update_first_case_standard(async_session: AsyncSession) -> None:
    case_one = await create_test_case_with_staff(
        session=async_session, scheduled_start_time=TEST_DAY_7AM
    )

    await assert_first_cases(
        site_and_dates=[(harness.site_0, TEST_DAY_7AM)],
        first_cases=[case_one],
    )


@pytest.mark.asyncio
async def test_first_case_hours_null(async_session: AsyncSession) -> None:
    case_one = await create_test_case(session=async_session, scheduled_start_time=TEST_DAY_7AM)
    await create_case_staff(case_id=case_one.case_id)
    await harness.first_case_config_store.upsert_site_first_case_config(
        SiteFirstCaseConfig(site_id=harness.site_0.id)
    )

    await assert_first_cases(
        site_and_dates=[(harness.site_0, TEST_DAY_7AM)],
        first_cases=[],
        not_first_cases=[case_one],
    )


@pytest.mark.asyncio
async def test_custom_first_case_hours(async_session: AsyncSession) -> None:
    # site_0 first case hours: 07:00 - 09:00
    await harness.first_case_config_store.upsert_site_first_case_config(
        SiteFirstCaseConfig.build_instance(
            site_id=harness.site_0.id, start_time=time(7), end_time=time(9)
        )
    )

    # room_1 first case hours: 08:00 - 10:00
    await harness.first_case_config_store.upsert_room_first_case_config(
        RoomFirstCaseConfig.build_instance(
            room_id=harness.room_1.id, start_time=time(8), end_time=time(10)
        )
    )

    room_0_first_case = await create_test_case_with_staff(
        session=async_session, room=harness.room_0, scheduled_start_time=TEST_DAY_7AM
    )

    room_0_second_case = await create_test_case_with_staff(
        session=async_session,
        room=harness.room_0,
        scheduled_start_time=TEST_DAY_7AM + timedelta(minutes=60),
    )

    room_1_early_case = await create_test_case_with_staff(
        session=async_session, room=harness.room_1, scheduled_start_time=TEST_DAY_7AM
    )

    room_1_first_case = await create_test_case_with_staff(
        session=async_session,
        room=harness.room_1,
        scheduled_start_time=TEST_DAY_7AM + timedelta(minutes=60),
    )

    await assert_first_cases(
        site_and_dates=[(harness.site_0, TEST_DAY_7AM)],
        first_cases=[room_0_first_case, room_1_first_case],
        not_first_cases=[room_0_second_case, room_1_early_case],
    )


@pytest.mark.asyncio
async def test_cases_outside_morning_hours_returns_no_first_cases(
    async_session: AsyncSession,
) -> None:
    case_one, case_two = await create_cases_with_same_room_and_surgeon(
        session=async_session,
        scheduled_start_times=[
            TEST_DAY_7AM - timedelta(minutes=20),
            TEST_DAY_7AM + timedelta(hours=3),
        ],
    )

    await assert_first_cases(
        site_and_dates=[(harness.site_0, TEST_DAY_7AM)],
        first_cases=[],
        not_first_cases=[case_one, case_two],
    )


@pytest.mark.asyncio
async def test_two_rooms_with_first_cases_same_surgeon(async_session: AsyncSession) -> None:
    case_one, case_two = await create_cases_with_same_room_and_surgeon(
        session=async_session,
        scheduled_start_times=[TEST_DAY_7AM, TEST_DAY_7AM + timedelta(minutes=5)],
    )
    await assert_first_cases(
        site_and_dates=[(harness.site_0, TEST_DAY_7AM)],
        first_cases=[case_one],
        not_first_cases=[case_two],
    )


@pytest.mark.asyncio
async def test_non_elective_cases_not_counted_first_case(async_session: AsyncSession) -> None:
    case_one = await create_test_case(
        session=async_session,
        scheduled_start_time=TEST_DAY_7AM,
        duration_minutes=60,
        room=harness.room_0,
        case_classification_types_id="CASE_CLASSIFICATION_URGENT",
    )
    case_two = await create_test_case(
        session=async_session,
        scheduled_start_time=TEST_DAY_7AM,
        duration_minutes=60,
        room=harness.room_1,
        case_classification_types_id="CASE_CLASSIFICATION_ELECTIVE",
    )

    for case in [case_one, case_two]:
        await create_case_staff(case_id=case.case_id)

    await assert_first_cases(
        site_and_dates=[(harness.site_0, TEST_DAY_7AM)],
        first_cases=[case_two],
        not_first_cases=[case_one],
    )


@pytest.mark.asyncio
async def test_multiple_sites(async_session: AsyncSession) -> None:
    case_one = await create_test_case(
        session=async_session,
        scheduled_start_time=TEST_DAY_7AM,
        room=harness.room_0,
    )
    case_two = await create_test_case(
        session=async_session,
        scheduled_start_time=TEST_DAY_7AM,
        room=harness.room_2,
    )

    for case in [case_one, case_two]:
        await create_case_staff(case_id=case.case_id)

    await assert_first_cases(
        site_and_dates=[(harness.site_0, TEST_DAY_7AM), (harness.site_1, TEST_DAY_7AM)],
        first_cases=[case_one, case_two],
        not_first_cases=[],
    )


@pytest.mark.asyncio
async def test_multiple_cases_same_room_returns_multiple_days(async_session: AsyncSession) -> None:
    # First Day
    case_one = await create_test_case_with_staff(
        session=async_session,
        scheduled_start_time=TEST_DAY_7AM,
        duration_minutes=15,
        room=harness.room_0,
    )
    case_two = await create_test_case_with_staff(
        session=async_session,
        scheduled_start_time=TEST_DAY_7AM + timedelta(minutes=16),
        duration_minutes=10,
        room=harness.room_0,
    )

    # Second Day
    case_three = await create_test_case_with_staff(
        session=async_session,
        scheduled_start_time=TEST_DAY_7AM + timedelta(days=1),
        duration_minutes=15,
        room=harness.room_0,
    )
    case_four = await create_test_case_with_staff(
        session=async_session,
        scheduled_start_time=TEST_DAY_7AM + timedelta(days=1, minutes=16),
        duration_minutes=10,
        room=harness.room_0,
    )

    # Third Day
    case_five = await create_test_case_with_staff(
        session=async_session,
        scheduled_start_time=TEST_DAY_7AM + timedelta(days=2),
        duration_minutes=15,
        room=harness.room_0,
    )
    case_six = await create_test_case_with_staff(
        session=async_session,
        scheduled_start_time=TEST_DAY_7AM + timedelta(days=2, minutes=16),
        duration_minutes=10,
        room=harness.room_0,
    )

    await assert_first_cases(
        site_and_dates=[
            (harness.site_0, TEST_DAY_7AM),
            (harness.site_0, TEST_DAY_7AM + timedelta(days=1)),
            (harness.site_0, TEST_DAY_7AM + timedelta(days=2)),
        ],
        first_cases=[case_one, case_three, case_five],
        not_first_cases=[case_two, case_four, case_six],
    )
