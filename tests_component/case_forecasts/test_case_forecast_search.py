import datetime
import uuid
from datetime import timed<PERSON><PERSON>
from typing import Optional

import pytest
from api_server.services.case_forecasts.case_forecast_store import (
    CaseForecastModel,
    CaseForecastStatus,
    ForecastQuery,
)

from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_client_schema import GQLCaseForecastStatus
from apella_cloud_api.new_input_schema import (
    GQLCaseForecastQueryInput,
    GQLCaseForecastUpsertInput,
    GQLCaseForecastForCaseInput,
)
from api_server.services.phases.phase_store import PhaseModel, PhaseStatus
from api_server.services.phases.phases_models import PhaseType
from tests_component import harness


class TestCaseForecastQuery:
    def _create_mock_phase(
        self,
        start_event_id: str,
        end_event_id: str,
        org_id: Optional[str] = None,
        site_id: Optional[str] = None,
        room_id: Optional[str] = None,
        case_id: Optional[str] = None,
        source_type: str = "unified",
        phase_type: PhaseType = PhaseType.CASE,
    ) -> PhaseModel:
        phase = PhaseModel()
        phase.id = uuid.uuid4()
        phase.org_id = org_id if org_id is not None else harness.org_0.id
        phase.site_id = site_id if site_id is not None else harness.site_0.id
        phase.room_id = room_id if room_id is not None else harness.room_0.id
        phase.type_id = phase_type.name
        phase.start_event_id = start_event_id
        phase.end_event_id = end_event_id
        phase.case_id = case_id
        phase.source_type = source_type
        phase.status = PhaseStatus.VALID
        return phase

    def _create_mock_case_forecast(
        self,
        start_time: datetime.datetime,
        end_time: datetime.datetime,
        forecast_variant: str = "stable",
        org_id: Optional[str] = None,
        site_id: Optional[str] = None,
        room_id: Optional[str] = None,
        case_id: Optional[str] = None,
        status: Optional[CaseForecastStatus] = CaseForecastStatus.VALID,
        static_duration_minutes: Optional[float] = None,
        transformer_end_time: Optional[datetime.datetime] = None,
        pythia_end_time: Optional[datetime.datetime] = None,
        is_auto_follow: Optional[bool] = None,
        turnover_duration_minutes: Optional[float] = None,
        static_start_offset_minutes: Optional[float] = None,
        is_overtime: Optional[bool] = None,
        pythia_duration_minutes: Optional[float] = None,
        static_duration_end_time: Optional[datetime.datetime] = None,
        pythia_prediction_tag: Optional[str] = None,
        bayesian_duration_minutes: Optional[float] = None,
        bayesian_end_time: Optional[datetime.datetime] = None,
        case_start_source: Optional[str] = None,
    ) -> CaseForecastModel:
        return CaseForecastModel(
            id=uuid.uuid4(),
            org_id=org_id if org_id is not None else harness.org_0.id,
            site_id=site_id if site_id is not None else harness.site_0.id,
            room_id=room_id if room_id is not None else harness.room_0.id,
            case_id=case_id,
            forecast_start_time=start_time,
            forecast_end_time=end_time,
            forecast_variant=forecast_variant,
            forecast_status=status,
            static_duration_minutes=static_duration_minutes,
            transformer_end_time=transformer_end_time,
            pythia_end_time=pythia_end_time,
            is_auto_follow=is_auto_follow,
            turnover_duration_minutes=turnover_duration_minutes,
            static_start_offset_minutes=static_start_offset_minutes,
            is_overtime=is_overtime,
            pythia_duration_minutes=pythia_duration_minutes,
            static_duration_end_time=static_duration_end_time,
            pythia_prediction_tag=pythia_prediction_tag,
            bayesian_duration_minutes=bayesian_duration_minutes,
            bayesian_end_time=bayesian_end_time,
            case_start_source=case_start_source,
        )

    @pytest.mark.asyncio
    async def test_get_forecasted_case(self) -> None:
        case_forecast = self._create_mock_case_forecast(
            start_time=harness.case_0.scheduled_end_time + timedelta(days=3),
            end_time=harness.case_0.scheduled_end_time + timedelta(days=4),
            case_id=harness.case_0.case_id,
        )
        case_forecast = (await harness.case_forecast_store.upsert_case_forecasts([case_forecast]))[
            0
        ]
        apella_schema = ApellaSchema()

        query = apella_schema.Query.case_forecasts.args(
            query=GQLCaseForecastQueryInput(
                min_start_time=harness.case_0.scheduled_end_time + timedelta(days=2),
                max_start_time=harness.case_0.scheduled_end_time + timedelta(days=6),
                site_ids=[harness.case_0.site_id],
            )
        ).select(
            apella_schema.CaseForecastConnection.edges.select(
                apella_schema.CaseForecastEdge.node.select(
                    apella_schema.CaseForecast.case.select(
                        apella_schema.ScheduledCase.id,
                    ),
                    apella_schema.CaseForecast.forecast_start_time,
                    apella_schema.CaseForecast.forecast_end_time,
                )
            )
        )
        results = harness.service_account_client.query_graphql_from_schema(query)
        cases = {result.node.case.id: result.node for result in results.case_forecasts.edges}
        assert case_forecast.case_id in cases
        assert cases[case_forecast.case_id].forecast_start_time == case_forecast.forecast_start_time
        assert cases[case_forecast.case_id].forecast_end_time == case_forecast.forecast_end_time

    @pytest.mark.asyncio
    async def test_get_forecasted_case_invalid(self) -> None:
        case_forecast = self._create_mock_case_forecast(
            start_time=harness.case_0.scheduled_end_time + timedelta(days=3),
            end_time=harness.case_0.scheduled_end_time + timedelta(days=4),
            case_id=harness.case_0.case_id,
            status=CaseForecastStatus.INVALID,
        )
        apella_schema = ApellaSchema()
        (await harness.case_forecast_store.upsert_case_forecasts([case_forecast]))
        query = apella_schema.Query.case_forecasts.args(
            query=GQLCaseForecastQueryInput(
                min_start_time=harness.case_0.scheduled_end_time + timedelta(days=2),
                max_start_time=harness.case_0.scheduled_end_time + timedelta(days=6),
                site_ids=[harness.case_0.site_id],
                forecast_statuses=[GQLCaseForecastStatus.VALID],
            )
        ).select(
            apella_schema.CaseForecastConnection.edges.select(
                apella_schema.CaseForecastEdge.node.select(
                    apella_schema.CaseForecast.case.select(
                        apella_schema.ScheduledCase.id,
                    ),
                    apella_schema.CaseForecast.forecast_start_time,
                    apella_schema.CaseForecast.forecast_end_time,
                )
            )
        )
        results = harness.service_account_client.query_graphql_from_schema(query)
        cases = {result.node.case.id: result.node for result in results.case_forecasts.edges}
        assert len(cases) == 0

    @pytest.mark.asyncio
    async def test_get_forecasted_case_invalid_search(self) -> None:
        case_forecast = self._create_mock_case_forecast(
            start_time=harness.case_0.scheduled_end_time + timedelta(days=3),
            end_time=harness.case_0.scheduled_end_time + timedelta(days=4),
            case_id=harness.case_0.case_id,
            status=CaseForecastStatus.INVALID,
        )
        apella_schema = ApellaSchema()
        (await harness.case_forecast_store.upsert_case_forecasts([case_forecast]))
        query = apella_schema.Query.case_forecasts.args(
            query=GQLCaseForecastQueryInput(
                min_start_time=harness.case_0.scheduled_end_time + timedelta(days=2),
                max_start_time=harness.case_0.scheduled_end_time + timedelta(days=6),
                site_ids=[harness.case_0.site_id],
                forecast_statuses=[GQLCaseForecastStatus.INVALID],
            )
        ).select(
            apella_schema.CaseForecastConnection.edges.select(
                apella_schema.CaseForecastEdge.node.select(
                    apella_schema.CaseForecast.case.select(
                        apella_schema.ScheduledCase.id,
                    ),
                    apella_schema.CaseForecast.forecast_start_time,
                    apella_schema.CaseForecast.forecast_end_time,
                )
            )
        )
        results = harness.service_account_client.query_graphql_from_schema(query)
        cases = {result.node.case.id: result.node for result in results.case_forecasts.edges}
        assert case_forecast.case_id in cases
        assert cases[case_forecast.case_id].forecast_start_time == case_forecast.forecast_start_time
        assert cases[case_forecast.case_id].forecast_end_time == case_forecast.forecast_end_time

    @pytest.mark.asyncio
    async def test_case_forecast_upsert(self) -> None:
        case_forecast = self._create_mock_case_forecast(
            start_time=harness.case_0.scheduled_end_time + timedelta(days=3),
            end_time=harness.case_0.scheduled_end_time + timedelta(days=4),
            case_id=harness.case_0.case_id,
        )
        apella_schema = ApellaSchema()

        mutation = apella_schema.Mutation.case_forecast_upsert.args(
            input=[
                GQLCaseForecastUpsertInput(
                    case_id=harness.case_0.case_id,
                    room_id=harness.room_0.id,
                    site_id=harness.site_0.id,
                    organization_id=harness.org_0.id,
                    forecast_start_time=harness.case_0.scheduled_end_time + timedelta(days=3),
                    forecast_end_time=harness.case_0.scheduled_end_time + timedelta(days=4),
                    forecast_variant="stable",
                    forecast_status=GQLCaseForecastStatus.VALID,
                )
            ]
        ).select(apella_schema.CaseForecastUpsert.success)

        harness.service_account_client.mutate_graphql_from_schema(mutation)

        query = apella_schema.Query.case_forecasts.args(
            query=GQLCaseForecastQueryInput(
                min_start_time=harness.case_0.scheduled_end_time + timedelta(days=2),
                max_start_time=harness.case_0.scheduled_end_time + timedelta(days=6),
                site_ids=[harness.case_0.site_id],
            )
        ).select(
            apella_schema.CaseForecastConnection.edges.select(
                apella_schema.CaseForecastEdge.node.select(
                    apella_schema.CaseForecast.case.select(
                        apella_schema.ScheduledCase.id,
                    ),
                    apella_schema.CaseForecast.forecast_start_time,
                    apella_schema.CaseForecast.forecast_end_time,
                )
            )
        )
        results = harness.service_account_client.query_graphql_from_schema(query)
        cases = {result.node.case.id: result.node for result in results.case_forecasts.edges}
        assert case_forecast.case_id in cases
        assert cases[case_forecast.case_id].forecast_start_time == case_forecast.forecast_start_time
        assert cases[case_forecast.case_id].forecast_end_time == case_forecast.forecast_end_time

    @pytest.mark.asyncio
    async def test_upsert_case_forecast_with_debug_fields(self) -> None:
        static_duration = 30.5
        transformer_end = harness.case_0.scheduled_end_time + timedelta(minutes=30)
        pythia_end = harness.case_0.scheduled_end_time + timedelta(minutes=45)
        auto_follow = True
        turnover_duration = 45.0
        static_offset = 10.0
        is_overtime = True
        pythia_duration = 20.0
        static_duration_end = harness.case_0.scheduled_end_time + timedelta(days=3, minutes=60)
        pythia_prediction_tag = "result_tag_1"

        case_forecast = self._create_mock_case_forecast(
            start_time=harness.case_0.scheduled_end_time + timedelta(days=3),
            end_time=harness.case_0.scheduled_end_time + timedelta(days=4),
            case_id=harness.case_0.case_id,
            static_duration_minutes=static_duration,
            transformer_end_time=transformer_end,
            pythia_end_time=pythia_end,
            is_auto_follow=auto_follow,
            turnover_duration_minutes=turnover_duration,
            static_start_offset_minutes=static_offset,
            is_overtime=is_overtime,
            pythia_duration_minutes=pythia_duration,
            static_duration_end_time=static_duration_end,
            pythia_prediction_tag=pythia_prediction_tag,
            bayesian_duration_minutes=45.0,
            bayesian_end_time=harness.case_0.scheduled_end_time + timedelta(days=3, hours=1),
            case_start_source="test_source",
        )
        case_forecast = (await harness.case_forecast_store.upsert_case_forecasts([case_forecast]))[
            0
        ]

        # Retrieve the forecast using case_forecast_store
        retrieved_forecast = await harness.case_forecast_store.get_forecast_by_id(
            str(case_forecast.id)
        )

        assert retrieved_forecast is not None
        assert retrieved_forecast.static_duration_minutes == static_duration
        assert retrieved_forecast.transformer_end_time == transformer_end
        assert retrieved_forecast.pythia_end_time == pythia_end
        assert retrieved_forecast.is_auto_follow == auto_follow
        assert retrieved_forecast.turnover_duration_minutes == turnover_duration
        assert retrieved_forecast.static_start_offset_minutes == static_offset
        assert retrieved_forecast.is_overtime == is_overtime
        assert retrieved_forecast.pythia_duration_minutes == pythia_duration
        assert retrieved_forecast.static_duration_end_time == static_duration_end
        assert retrieved_forecast.pythia_prediction_tag == pythia_prediction_tag
        assert retrieved_forecast.bayesian_duration_minutes == 45.0
        assert (
            retrieved_forecast.bayesian_end_time
            == harness.case_0.scheduled_end_time + timedelta(days=3, hours=1)
        )
        assert retrieved_forecast.case_start_source == "test_source"

    @pytest.mark.asyncio
    async def test_upsert_forecasts_for_cases_with_debug_fields(self) -> None:
        static_duration = 25.0
        transformer_end = harness.case_0.scheduled_end_time + timedelta(minutes=30)
        pythia_end = harness.case_0.scheduled_end_time + timedelta(minutes=45)
        auto_follow = True
        turnover_duration = 40.0
        static_offset = 5.0
        is_overtime = False
        pythia_duration_minutes = 12.5
        static_duration_end_time = harness.case_0.scheduled_end_time + timedelta(days=3, minutes=45)
        pythia_prediction_tag = "result_tag_2"

        case_forecast = self._create_mock_case_forecast(
            start_time=harness.case_0.scheduled_end_time + timedelta(days=3),
            end_time=harness.case_0.scheduled_end_time + timedelta(days=4),
            case_id=harness.case_0.case_id,
            static_duration_minutes=static_duration,
            transformer_end_time=transformer_end,
            pythia_end_time=pythia_end,
            is_auto_follow=auto_follow,
            turnover_duration_minutes=turnover_duration,
            static_start_offset_minutes=static_offset,
            is_overtime=is_overtime,
            pythia_duration_minutes=pythia_duration_minutes,
            static_duration_end_time=static_duration_end_time,
            pythia_prediction_tag=pythia_prediction_tag,
            bayesian_duration_minutes=30.0,
            bayesian_end_time=harness.case_0.scheduled_end_time + timedelta(days=3, hours=3),
            case_start_source="test_source",
        )
        case_forecast = (await harness.case_forecast_store.upsert_case_forecasts([case_forecast]))[
            0
        ]

        # Retrieve the forecasts using case_forecast_store
        forecasts = await harness.case_forecast_store.upsert_case_forecasts([case_forecast])

        # Find the forecast for the specific case
        case_forecast = next(
            (forecast for forecast in forecasts if forecast.case_id == harness.case_0.case_id),
            None,  # type: ignore
        )

        assert case_forecast is not None
        assert case_forecast.static_duration_minutes == static_duration
        assert case_forecast.transformer_end_time == transformer_end
        assert case_forecast.pythia_end_time == pythia_end
        assert case_forecast.is_auto_follow == auto_follow
        assert case_forecast.turnover_duration_minutes == turnover_duration
        assert case_forecast.static_start_offset_minutes == static_offset
        assert case_forecast.is_overtime == is_overtime
        assert case_forecast.case_start_source == "test_source"

    @pytest.mark.asyncio
    async def test_upsert_forecasts_for_cases_with_debug_fields_store_check(self) -> None:
        # Define debug fields
        static_duration = 25.0
        transformer_end = harness.case_0.scheduled_end_time + timedelta(minutes=30)
        pythia_end = harness.case_0.scheduled_end_time + timedelta(minutes=45)
        auto_follow = True
        turnover_duration = 40.0
        static_offset = 5.0
        is_overtime = False
        pythia_duration_minutes = 12.5
        static_duration_end_time = harness.case_0.scheduled_end_time + timedelta(days=3, minutes=45)
        pythia_prediction_tag = "result_tag_2"

        # Prepare the mutation input with debug fields
        mutation = (
            ApellaSchema()
            .Mutation.upsert_forecasts_for_cases.args(
                input=[
                    GQLCaseForecastForCaseInput(
                        case_id=harness.case_0.case_id,
                        room_id=harness.room_0.id,
                        site_id=harness.site_0.id,
                        organization_id=harness.org_0.id,
                        forecast_start_time=harness.case_0.scheduled_end_time + timedelta(days=3),
                        forecast_end_time=harness.case_0.scheduled_end_time + timedelta(days=4),
                        forecast_variant="stable",
                        forecast_status=GQLCaseForecastStatus.VALID,
                        static_duration_minutes=static_duration,
                        transformer_end_time=transformer_end,
                        pythia_end_time=pythia_end,
                        is_auto_follow=auto_follow,
                        turnover_duration_minutes=turnover_duration,
                        static_start_offset_minutes=static_offset,
                        is_overtime=is_overtime,
                        pythia_duration_minutes=pythia_duration_minutes,
                        static_duration_end_time=static_duration_end_time,
                        pythia_prediction_tag=pythia_prediction_tag,
                        bayesian_duration_minutes=30.0,
                        bayesian_end_time=harness.case_0.scheduled_end_time
                        + timedelta(days=3, hours=3),
                        case_start_source="test_source",
                    )
                ]
            )
            .select(ApellaSchema().UpsertForecastsForCases.success)
        )

        # Execute the mutation
        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.upsert_forecasts_for_cases.success is True

        # Retrieve the forecasts using case_forecast_store
        forecast_query = ForecastQuery(
            case_ids=[harness.case_0.case_id],
            forecast_variants=["stable"],
            min_start_time=harness.case_0.scheduled_end_time + timedelta(days=2),
            max_start_time=harness.case_0.scheduled_end_time + timedelta(days=6),
            site_ids=[harness.case_0.site_id],
        )
        forecasts = await harness.case_forecast_store.query_forecasts(forecast_query)

        # There should be one forecast for the case_id and variant
        assert len(forecasts) == 1
        forecast = forecasts[0]

        # Assert the debug fields are correctly set
        assert forecast.static_duration_minutes == static_duration
        assert forecast.transformer_end_time == transformer_end
        assert forecast.pythia_end_time == pythia_end
        assert forecast.is_auto_follow == auto_follow
        assert forecast.turnover_duration_minutes == turnover_duration
        assert forecast.static_start_offset_minutes == static_offset
        assert forecast.is_overtime == is_overtime
        assert forecast.pythia_duration_minutes == pythia_duration_minutes
        assert forecast.static_duration_end_time == static_duration_end_time
        assert forecast.pythia_prediction_tag == pythia_prediction_tag
        assert forecast.bayesian_duration_minutes == 30.0
        assert forecast.bayesian_end_time == harness.case_0.scheduled_end_time + timedelta(
            days=3, hours=3
        )
        assert forecast.bayesian_duration_minutes == 30.0
        assert forecast.bayesian_end_time == harness.case_0.scheduled_end_time + timedelta(
            days=3, hours=3
        )
        assert forecast.case_start_source == "test_source"

    async def test_upsert_forecast_for_case(self) -> None:
        case_forecast = self._create_mock_case_forecast(
            start_time=harness.case_0.scheduled_end_time + timedelta(days=3),
            end_time=harness.case_0.scheduled_end_time + timedelta(days=4),
            case_id=harness.case_0.case_id,
        )
        (await harness.case_forecast_store.upsert_case_forecasts([case_forecast]))

        new_forecast_start_time = harness.case_0.scheduled_end_time + timedelta(days=5)
        new_forecast_end_time = harness.case_0.scheduled_end_time + timedelta(days=6)

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.upsert_forecasts_for_cases.args(
            input=[
                GQLCaseForecastForCaseInput(
                    case_id=harness.case_0.case_id,
                    room_id=harness.room_0.id,
                    site_id=harness.site_0.id,
                    organization_id=harness.org_0.id,
                    forecast_start_time=new_forecast_start_time,
                    forecast_end_time=new_forecast_end_time,
                    forecast_variant="stable",
                    forecast_status=GQLCaseForecastStatus.VALID,
                )
            ]
        ).select(apella_schema.UpsertForecastsForCases.success)

        harness.service_account_client.mutate_graphql_from_schema(mutation)
        query = apella_schema.Query.case_forecasts.args(
            query=GQLCaseForecastQueryInput(
                min_start_time=harness.case_0.scheduled_end_time + timedelta(days=2),
                max_start_time=harness.case_0.scheduled_end_time + timedelta(days=6),
                site_ids=[harness.case_0.site_id],
            )
        ).select(
            apella_schema.CaseForecastConnection.edges.select(
                apella_schema.CaseForecastEdge.node.select(
                    apella_schema.CaseForecast.case.select(
                        apella_schema.ScheduledCase.id,
                    ),
                    apella_schema.CaseForecast.forecast_start_time,
                    apella_schema.CaseForecast.forecast_end_time,
                )
            )
        )
        results = harness.service_account_client.query_graphql_from_schema(query)
        cases = {result.node.case.id: result.node for result in results.case_forecasts.edges}
        assert case_forecast.case_id in cases
        assert cases[case_forecast.case_id].forecast_start_time == new_forecast_start_time
        assert cases[case_forecast.case_id].forecast_end_time == new_forecast_end_time
