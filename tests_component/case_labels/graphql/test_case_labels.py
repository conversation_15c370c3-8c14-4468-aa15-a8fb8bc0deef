import uuid
from datetime import datetime, timezone

import pytest

from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_client_schema import GQLCaseLabelFieldType
from apella_cloud_api.new_input_schema import GQLCaseLabelUpsertInput, GQLScheduledCaseQueryInput
from api_server.services.case_labels.case_label_store import (
    CaseLabelStore,
    CaseLabelCategoryModel,
    CaseLabelFieldModel,
    CaseLabelFieldType,
    CaseLabelFieldOptionModel,
    CaseLabelBooleanValues,
    CaseLabelAssocModel,
)
from tests_component import harness


@pytest.fixture
async def store() -> CaseLabelStore:
    return CaseLabelStore()


@pytest.fixture
async def category(store: CaseLabelStore) -> CaseLabelCategoryModel:
    case_label_store = CaseLabelStore()

    return await case_label_store.upsert_case_label_category(
        CaseLabelCategoryModel(
            id=uuid.uuid4(), name="Category", site_id=harness.site_0.id, org_id=harness.org_0.id
        )
    )


@pytest.fixture
async def field_boolean(
    store: CaseLabelStore, category: CaseLabelCategoryModel
) -> CaseLabelFieldModel:
    field_model = CaseLabelFieldModel(
        id=uuid.uuid4(),
        name="Field 1",
        type=CaseLabelFieldType.BOOLEAN,
        category_id=category.id,
        site_id=harness.site_0.id,
        org_id=harness.org_0.id,
    )
    field = (await store.upsert_case_label_fields([field_model]))[0]
    option_models = [
        CaseLabelFieldOptionModel(
            id=uuid.uuid4(),
            field_id=field.id,
            color="red",
            abbreviation="t",
            value=CaseLabelBooleanValues.TRUE.value,
        ),
        CaseLabelFieldOptionModel(
            id=uuid.uuid4(),
            field_id=field.id,
            color="red",
            abbreviation="f",
            value=CaseLabelBooleanValues.FALSE.value,
        ),
    ]
    await store.upsert_case_label_field_options(option_models)
    return await store.query_case_field(str(field.id))


@pytest.fixture
async def field_single_select(
    store: CaseLabelStore, category: CaseLabelCategoryModel
) -> CaseLabelFieldModel:
    field_model = CaseLabelFieldModel(
        id=uuid.uuid4(),
        name="Field 2",
        type=CaseLabelFieldType.SINGLE_SELECT,
        category_id=category.id,
        site_id=harness.site_0.id,
        org_id=harness.org_0.id,
    )
    field = (await store.upsert_case_label_fields([field_model]))[0]
    option_models = [
        CaseLabelFieldOptionModel(
            id=uuid.uuid4(), field_id=field.id, color="red", abbreviation="1", value="1"
        ),
        CaseLabelFieldOptionModel(
            id=uuid.uuid4(), field_id=field.id, color="red", abbreviation="2", value="2"
        ),
        CaseLabelFieldOptionModel(
            id=uuid.uuid4(), field_id=field.id, color="red", abbreviation="3", value="3"
        ),
    ]
    await store.upsert_case_label_field_options(option_models)
    return await store.query_case_field(str(field.id))


@pytest.mark.asyncio
async def test_query_case_label_form(
    category: CaseLabelCategoryModel,
    field_boolean: CaseLabelFieldModel,
    field_single_select: CaseLabelFieldModel,
) -> None:
    apella_schema = ApellaSchema()
    query = apella_schema.Query.site.args(
        id=harness.site_0.id,
    ).select(
        apella_schema.Site.case_label_form.select(
            apella_schema.CaseLabelCategory.id,
            apella_schema.CaseLabelCategory.fields.select(
                apella_schema.CaseLabelField.id,
                apella_schema.CaseLabelField.type,
                apella_schema.CaseLabelField.options.select(apella_schema.CaseLabelFieldOption.id),
            ),
        )
    )
    query_result = harness.service_account_client.query_graphql_from_schema(query)
    categories_result = query_result.site.case_label_form
    assert len(categories_result) == 1
    assert categories_result[0].id == str(category.id)

    fields_result = categories_result[0].fields
    assert len(fields_result) == 2
    field_0_result = next(f for f in fields_result if f.id == str(field_boolean.id))
    field_1_result = next(f for f in fields_result if f.id == str(field_single_select.id))
    assert field_0_result.type == GQLCaseLabelFieldType.BOOLEAN
    assert field_1_result.type == GQLCaseLabelFieldType.SINGLE_SELECT

    options_0_result = field_0_result.options
    options_1_result = field_1_result.options
    assert len(options_0_result) == 2
    assert str(options_0_result[0].id) == str(field_boolean.options[0].id)
    assert len(options_1_result) == 3


@pytest.mark.asyncio
async def test_query_case_label_form_miss() -> None:
    apella_schema = ApellaSchema()
    query = apella_schema.Query.site.args(
        id=harness.site_1.id,
    ).select(
        apella_schema.Site.case_label_form.select(
            apella_schema.CaseLabelCategory.id,
        )
    )
    query_result = harness.service_account_client.query_graphql_from_schema(query)
    assert len(query_result.site.case_label_form) == 0


@pytest.mark.asyncio
async def test_case_label_upsert_mutation(
    category: CaseLabelCategoryModel, field_boolean: CaseLabelFieldModel
) -> None:
    case_id = harness.case_0.case_id
    assoc_id = uuid.uuid4()

    apella_schema = ApellaSchema()

    # Insert association
    insert_mutation = apella_schema.Mutation.case_label_upsert.args(
        input=[
            GQLCaseLabelUpsertInput(
                id=str(assoc_id),
                case_id=case_id,
                option_id=str(field_boolean.options[0].id),
            )
        ]
    ).select(
        apella_schema.CaseLabelUpsert.success,
        apella_schema.CaseLabelUpsert.case_labels.select(
            apella_schema.CaseLabel.id,
            apella_schema.CaseLabel.case_id,
            apella_schema.CaseLabel.option_id,
            apella_schema.CaseLabel.value,
            apella_schema.CaseLabel.boolean_value,
        ),
    )

    insert_result = harness.service_account_client.mutate_graphql_from_schema(insert_mutation)

    assert insert_result.case_label_upsert.success is True
    assert insert_result.case_label_upsert.case_labels[0].case_id == case_id
    assert insert_result.case_label_upsert.case_labels[0].option_id == str(
        field_boolean.options[0].id
    )

    # Query for association
    query = apella_schema.Query.cases.args(
        query=GQLScheduledCaseQueryInput(case_ids=[case_id])
    ).select(
        apella_schema.ScheduledCaseConnection.edges.select(
            apella_schema.ScheduledCaseEdge.node.select(
                apella_schema.ScheduledCase.case_labels.select(apella_schema.CaseLabel.id)
            )
        )
    )

    query_result = harness.service_account_client.query_graphql_from_schema(query)
    assert len(query_result.cases.edges) == 1
    assert len(query_result.cases.edges[0].node.case_labels) == 1
    assert query_result.cases.edges[0].node.case_labels[0].id == str(assoc_id)

    # Update association
    update_mutation = apella_schema.Mutation.case_label_upsert.args(
        input=[
            GQLCaseLabelUpsertInput(
                id=str(assoc_id),
                case_id=case_id,
                option_id=str(field_boolean.options[1].id),
            )
        ]
    ).select(
        apella_schema.CaseLabelUpsert.success,
        apella_schema.CaseLabelUpsert.case_labels.select(
            apella_schema.CaseLabel.id,
            apella_schema.CaseLabel.case_id,
            apella_schema.CaseLabel.option_id,
        ),
    )

    update_result = harness.service_account_client.mutate_graphql_from_schema(update_mutation)
    assert update_result.case_label_upsert.success is True
    assert update_result.case_label_upsert.case_labels[0].case_id == case_id
    assert update_result.case_label_upsert.case_labels[0].option_id == str(
        field_boolean.options[1].id
    )
    assert (
        update_result.case_label_upsert.case_labels[0].id
        == insert_result.case_label_upsert.case_labels[0].id
    )

    # Delete association
    archived_time = datetime.now(tz=timezone.utc)
    delete_mutation = apella_schema.Mutation.case_label_upsert.args(
        input=[
            GQLCaseLabelUpsertInput(
                id=str(assoc_id),
                case_id=case_id,
                option_id=str(field_boolean.options[1].id),
                archived_time=archived_time,
            )
        ]
    ).select(
        apella_schema.CaseLabelUpsert.success,
        apella_schema.CaseLabelUpsert.case_labels.select(
            apella_schema.CaseLabel.id,
            apella_schema.CaseLabel.case_id,
            apella_schema.CaseLabel.option_id,
            apella_schema.CaseLabel.archived_time,
        ),
    )

    delete_result = harness.service_account_client.mutate_graphql_from_schema(delete_mutation)
    assert delete_result.case_label_upsert.success is True
    assert delete_result.case_label_upsert.case_labels[0].id == str(assoc_id)
    assert (
        delete_result.case_label_upsert.case_labels[0].archived_time.isoformat()
        == archived_time.isoformat()
    )

    # Confirm that archived result is no longer returned
    second_query_result = harness.service_account_client.query_graphql_from_schema(query)
    assert len(second_query_result.cases.edges) == 1
    assert len(second_query_result.cases.edges[0].node.case_labels) == 0


@pytest.mark.asyncio
async def test_case_label_boolean(
    store: CaseLabelStore, field_boolean: CaseLabelFieldModel
) -> None:
    case_id = harness.case_0.case_id
    true_label = CaseLabelAssocModel(
        id=uuid.uuid4(),
        option_id=field_boolean.options[0].id,
        case_id=case_id,
        updated_by_user_id=harness.service_account_writer_role.id,
    )
    await store.upsert_case_label_assocs([true_label])

    apella_schema = ApellaSchema()
    query = apella_schema.Query.cases.args(
        query=GQLScheduledCaseQueryInput(case_ids=[case_id])
    ).select(
        apella_schema.ScheduledCaseConnection.edges.select(
            apella_schema.ScheduledCaseEdge.node.select(
                apella_schema.ScheduledCase.case_labels.select(
                    apella_schema.CaseLabel.id,
                    apella_schema.CaseLabel.field_id,
                    apella_schema.CaseLabel.color,
                    apella_schema.CaseLabel.abbreviation,
                    apella_schema.CaseLabel.value,
                    apella_schema.CaseLabel.boolean_value,
                    apella_schema.CaseLabel.case_id,
                    apella_schema.CaseLabel.option_id,
                    apella_schema.CaseLabel.updated_by_user_id,
                    apella_schema.CaseLabel.archived_time,
                )
            )
        )
    )

    result = harness.service_account_client.query_graphql_from_schema(query)
    assert len(result.cases.edges) == 1
    assert len(result.cases.edges[0].node.case_labels) == 1

    labels_result = result.cases.edges[0].node.case_labels
    assert labels_result[0].id == str(true_label.id)
    assert labels_result[0].case_id == true_label.case_id
    assert labels_result[0].option_id == str(true_label.option_id)
    assert labels_result[0].updated_by_user_id == true_label.updated_by_user_id
    assert labels_result[0].archived_time == true_label.archived_time
    assert labels_result[0].field_id == str(field_boolean.options[0].field_id)
    assert labels_result[0].color == field_boolean.options[0].color
    assert labels_result[0].abbreviation == field_boolean.options[0].abbreviation
    assert labels_result[0].value == CaseLabelBooleanValues.TRUE.value
    assert labels_result[0].boolean_value is True

    false_label = CaseLabelAssocModel(
        id=true_label.id,
        option_id=field_boolean.options[1].id,
        case_id=case_id,
        updated_by_user_id=harness.service_account_writer_role.id,
    )
    await store.upsert_case_label_assocs([false_label])

    apella_schema = ApellaSchema()
    query = apella_schema.Query.cases.args(
        query=GQLScheduledCaseQueryInput(case_ids=[case_id])
    ).select(
        apella_schema.ScheduledCaseConnection.edges.select(
            apella_schema.ScheduledCaseEdge.node.select(
                apella_schema.ScheduledCase.case_labels.select(
                    apella_schema.CaseLabel.id,
                )
            )
        )
    )

    result = harness.service_account_client.query_graphql_from_schema(query)
    assert len(result.cases.edges) == 1
    # false labels should not be returned
    assert len(result.cases.edges[0].node.case_labels) == 0


@pytest.mark.asyncio
async def test_case_label_single_select(
    store: CaseLabelStore, field_single_select: CaseLabelFieldModel
) -> None:
    case_id = harness.case_0.case_id
    single_select_label = CaseLabelAssocModel(
        id=uuid.uuid4(),
        option_id=field_single_select.options[0].id,
        case_id=case_id,
        updated_by_user_id=harness.service_account_writer_role.id,
    )
    await store.upsert_case_label_assocs([single_select_label])

    apella_schema = ApellaSchema()
    query = apella_schema.Query.cases.args(
        query=GQLScheduledCaseQueryInput(case_ids=[case_id])
    ).select(
        apella_schema.ScheduledCaseConnection.edges.select(
            apella_schema.ScheduledCaseEdge.node.select(
                apella_schema.ScheduledCase.case_labels.select(
                    apella_schema.CaseLabel.id,
                    apella_schema.CaseLabel.field_id,
                    apella_schema.CaseLabel.color,
                    apella_schema.CaseLabel.abbreviation,
                    apella_schema.CaseLabel.value,
                    apella_schema.CaseLabel.boolean_value,
                    apella_schema.CaseLabel.case_id,
                    apella_schema.CaseLabel.option_id,
                    apella_schema.CaseLabel.updated_by_user_id,
                    apella_schema.CaseLabel.archived_time,
                )
            )
        )
    )

    result = harness.service_account_client.query_graphql_from_schema(query)
    assert len(result.cases.edges) == 1
    assert len(result.cases.edges[0].node.case_labels) == 1

    labels_result = result.cases.edges[0].node.case_labels
    assert labels_result[0].id == str(single_select_label.id)
    assert labels_result[0].case_id == single_select_label.case_id
    assert labels_result[0].option_id == str(single_select_label.option_id)
    assert labels_result[0].updated_by_user_id == single_select_label.updated_by_user_id
    assert labels_result[0].archived_time == single_select_label.archived_time
    assert labels_result[0].field_id == str(field_single_select.options[0].field_id)
    assert labels_result[0].color == field_single_select.options[0].color
    assert labels_result[0].abbreviation == field_single_select.options[0].abbreviation
    assert labels_result[0].value == field_single_select.options[0].value
