from sqlalchemy.ext.asyncio import AsyncSession

import pytest

from datetime import datetime, timezone
from tests_component import harness

GET_CASE_TO_BLOCK = """
  query CasesToBlocks(
    $minDate: DateTime!
    $maxDate: DateTime!
    $siteId: String!
  ) {
    casesToBlocks(
      query: {
        minDate: $minDate
        maxDate: $maxDate
        siteId: $siteId
      }
    ) {
        edges {
            node {
                caseId
                blockId
            }
        }
    }
}
"""


@pytest.mark.asyncio
async def test_get_cases_to_blocks_requires_auth() -> None:
    site = harness.site_0
    result = harness.no_auth_client.execute_graphql(
        GET_CASE_TO_BLOCK,
        variables={
            "minDate": datetime(2025, 2, 21).isoformat(),
            "maxDate": datetime(2025, 2, 22).isoformat(),
            "siteId": site.id,
        },
    )

    assert result["data"] is None
    assert result["errors"][0]["message"] == "Not Authorized: No authorization found"
    assert result["errors"][0]["path"] == ["casesToBlocks"]


@pytest.mark.asyncio
async def test_get_cases_to_blocks_for_no_cases_and_no_blocks(async_session: AsyncSession) -> None:
    today = datetime.now(timezone.utc)
    result = harness.service_account_client.execute_graphql(
        GET_CASE_TO_BLOCK,
        variables={
            "minDate": today.isoformat(),
            "maxDate": today.isoformat(),
            "siteId": harness.site_1.id,
        },
    )
    assert result["data"]["casesToBlocks"]["edges"] == []
