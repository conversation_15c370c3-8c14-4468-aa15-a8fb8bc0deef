import asyncio
import os
from datetime import datetime, timedelta, timezone
from typing import Callable, Iterable, Optional, cast
from uuid import UUID, uuid4

import requests

from api_server.services.block_utilization.block_utilization_store import BlockUtilizationStore
from api_server.services.case_activity.case_activity_store import (
    CaseActivityModel,
    CaseActivityStore,
)
from api_server.services.case_forecasts.case_forecast_store import CaseForecastStore
from api_server.services.case_to_block.case_to_block_store import CaseToBlockStore
from api_server.services.first_case_config.first_case_config_store import FirstCaseConfigStore
from api_server.services.prime_time.prime_time_store import PrimeTimeStore
from api_server.services.terminal_cleans.terminal_cleans_store import TerminalCleansStore
from sqlalchemy import StaticPool, delete, select, text
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession
from sqlalchemy.orm import Session
from sqlalchemy.schema import Table
from sqlalchemy.types import JSO<PERSON>

from api_server.services.turnover.turnover_label_store import TurnoverLabelStore
import auth.permissions
import config
from apella_cloud_api import Client as ApiServerClient
from api_server.services.anesthesia.anesthesia_store import AnesthesiaStore
from api_server.services.annotation_tasks.annotation_task_models import (
    TaskScheduleCreate,
    TaskStatus,
    TaskTypeUpdate,
)
from api_server.services.annotation_tasks.annotation_task_store import (
    AnnotationTask,
    AnnotationTaskSchedule,
    AnnotationTaskStore,
    AnnotationTaskTypeAnnotator,
    AnnotationTaskTypeModel,
)
from api_server.services.block.block_store import BlockStore
from api_server.services.boards.board_store import BoardStore
from api_server.services.camera.camera_store import CameraModel, CameraStore
from api_server.services.case.case_classification_type_store import (
    CaseClassificationTypeModel,
    CaseClassificationTypeStore,
)
from api_server.services.case.case_flag_store import CaseFlagStore
from api_server.services.case.case_matching_store import CaseMatchingStore
from api_server.services.case.case_procedure_store import CaseProcedureStore
from api_server.services.case.case_staff_store import CaseStaffStore
from api_server.services.case.case_status import SCHEDULED
from api_server.services.case.case_store import Case, CaseRaw, CaseStore
from api_server.services.case_derived_properties.case_derived_properties_store import (
    CaseDerivedPropertiesStore,
)
from api_server.services.cluster.cluster_store import ClusterStore
from api_server.services.contact_information.contact_information_store import (
    ContactInformationStore,
)
from api_server.services.contact_information.staff_event_notification_contact_information_store import (
    StaffEventNotificationContactInformationStore,
)
from api_server.services.custom_phase_config.custom_phase_config_store import (
    CustomPhaseConfigStore,
)
from api_server.services.ehr_interfaces.mapping_store import MappingStore
from api_server.services.events.event_store import (
    EventModel,
    EventStore,
    EventTypeModel,
)
from api_server.services.highlights.highlight_feedback_store import (
    HighlightFeedback,
    HighlightFeedbackStore,
)
from api_server.services.highlights.highlight_store import (
    Highlight,
    HighlightAsset,
    HighlightStore,
    HighlightUser,
)
from api_server.services.measurement_periods.measurement_period_store import (
    MeasurementPeriodStore,
)
from api_server.services.observations.observation_store import (
    ObservationModel,
    ObservationStore,
    ObservationTypeNameStore,
)
from api_server.services.organization.organization_db import Organization
from api_server.services.organization.organization_store import OrganizationStore
from api_server.services.phases.phase_store import PhaseStore
from api_server.services.plan.case_note_plan_store import CaseNotePlanStore
from api_server.services.plan.case_staff_plan_store import CaseStaffPlanStore
from api_server.services.procedures.procedure_store import ProcedureStore
from api_server.services.closures.closure_store import ClosureStore
from api_server.services.room.room_store import RoomModel, RoomStore
from api_server.services.service_lines.service_line_store import (
    ServiceLineModel,
    ServiceLineStore,
)
from api_server.services.site.site_store import Site, SiteStore
from api_server.services.staff.staff_store import StaffModel, StaffStore
from api_server.services.staffing_needs.staffing_needs_store import StaffingNeedsStore
from api_server.services.user_filter_views.user_filter_view_store import (
    UserFilterViewStore,
)
from api_server.services.users.user_store import User, UserStore
from auth import AuthOrganization, AuthRole, permissions
from databases.sql import (
    Base,
    EngineSingleton,
    engine_provider,
    get_session_maker,
    DECODABLE_SCHEMA,
)
from databases.sql.sync_as_async_session import component_test_db_async_session_helper
from mocks.mock_auth0.database import Database as MockAuth0Database
from mocks.mock_auth0.jwt_tokens import make_jwt
from mocks.mock_big_query.database import Database as MockBigQueryDatabase
from tests_component.test_case_activity_utils import CaseActivityTestHelper

surgery_task_type_id = UUID("831027c5-942b-477a-b490-93ab731ebba6")
mop_task_type_id = UUID("4d426c27-fd3c-4b88-8cb4-fdf2338b97d8")
light_task_type_id = UUID("f775f20b-0aff-48b2-9808-35622ad554ed")


class ComponentTestHarness:
    server_url: str
    auth0_url: str

    org_store: OrganizationStore
    site_store: SiteStore
    room_store: RoomStore
    closure_store: ClosureStore
    camera_store: CameraStore
    user_store: UserStore
    observation_store: ObservationStore
    observation_type_name_store: ObservationTypeNameStore
    measurement_period_store: MeasurementPeriodStore
    case_store: CaseStore
    case_classification_type_store: CaseClassificationTypeStore
    case_derived_properties_store: CaseDerivedPropertiesStore
    event_store: EventStore
    highlight_store: HighlightStore
    highlight_feedback_store: HighlightFeedbackStore
    mapping_store: MappingStore
    annotation_task_store: AnnotationTaskStore
    procedure_store: ProcedureStore
    staff_store: StaffStore
    service_line_store: ServiceLineStore
    case_procedure_store: CaseProcedureStore
    case_matching_store: CaseMatchingStore
    case_note_plan_store: CaseNotePlanStore
    case_staff_store: CaseStaffStore
    case_staff_plan_store: CaseStaffPlanStore
    phase_store: PhaseStore
    contact_information_store: ContactInformationStore
    staffing_needs_store: StaffingNeedsStore
    block_store: BlockStore
    anesthesia_store: AnesthesiaStore
    cluster_store: ClusterStore
    staff_event_notification_contact_information_store: (
        StaffEventNotificationContactInformationStore
    )
    case_flag_store: CaseFlagStore
    user_filter_view_store: UserFilterViewStore
    custom_phase_config_store: CustomPhaseConfigStore
    terminal_cleans_store: TerminalCleansStore

    turnover_label_store: TurnoverLabelStore
    org_0: Organization
    org_1: Organization

    site_0: Site
    site_1: Site
    site_2: Site
    site_ca: (
        Site  # for case_activity table specifically, please not to use this site in other tests
    )

    room_0: RoomModel
    room_1: RoomModel
    room_2: RoomModel
    room_3: RoomModel
    room_ca: RoomModel  # for case_activity table specifically, please not to use this room in other tests

    camera_0: CameraModel
    camera_1: CameraModel
    camera_2: CameraModel
    camera_3: CameraModel
    camera_4: CameraModel
    camera_5: CameraModel
    camera_ca: CameraModel  # for case_activity table specifically, please not to use this camera in other tests

    service_line_0: ServiceLineModel
    service_line_1: ServiceLineModel

    customer_dashboard_user_role: AuthRole
    site_0_scoped_dashboard_role: AuthRole
    apella_labeler_role: AuthRole
    apella_label_reviewer_role: AuthRole
    service_account_writer_role: AuthRole
    field_engineer_role: AuthRole

    surgeon_user: User
    site_0_scoped_user: User

    user_without_permissions: User
    non_registered_user: User
    internal_service_account_user: User
    labeler_user: User
    label_reviewer_user: User
    hospital_administrator_user: User
    field_engineer_user: User

    case_0: Case
    case_1: Case
    case_2: Case
    case_3: Case
    case_4: Case
    elapsed_case: Case
    future_case: Case
    case_ca: (
        Case  # for case_activity table specifically, please not to use this case in other tests
    )

    staff_0: StaffModel

    case_0_raw: CaseRaw

    event_0: EventModel
    event_1: EventModel
    event_2: EventModel
    event_3: EventModel
    event_4: EventModel
    event_5: EventModel
    event_6: EventModel
    event_7: EventModel
    elapsed_event: EventModel
    future_event: EventModel
    event_ca: EventModel  # for case_activity table specifically, please not to use this event in other tests

    event_type_0: EventTypeModel

    highlight_1: Highlight
    highlight_2: Highlight

    highlight_asset_1: HighlightAsset
    highlight_asset_2: HighlightAsset

    highlight_user_1: HighlightUser
    highlight_user_2: HighlightUser
    highlight_user_3: HighlightUser
    highlight_user_4: HighlightUser

    highlight_feedback_1: HighlightFeedback
    highlight_feedback_2: HighlightFeedback

    annotation_task_schedule_0: AnnotationTaskSchedule

    surgery_task_type: AnnotationTaskTypeModel
    light_task_type: AnnotationTaskTypeModel
    mop_task_type: AnnotationTaskTypeModel
    annotation_task_type_0: AnnotationTaskTypeModel
    terminal_clean_task_type: AnnotationTaskTypeModel

    annotation_task_0: AnnotationTask
    annotation_task_1: AnnotationTask
    annotation_task_2: AnnotationTask

    observation_0: ObservationModel
    observation_1: ObservationModel
    observation_2: ObservationModel
    observation_3: ObservationModel

    no_auth_client: ApiServerClient
    site_0_scoped_client: ApiServerClient
    surgeon_client: ApiServerClient
    user_without_permissions_client: ApiServerClient
    labeler_client: ApiServerClient
    label_reviewer_client: ApiServerClient
    service_account_client: ApiServerClient
    hospital_administrator_client: ApiServerClient
    field_engineer_client: ApiServerClient

    def __init__(self) -> None:
        self.engine_singleton: Optional[EngineSingleton] = None
        self.async_session_maker: Optional[Callable[[], AsyncSession]] = None
        self.db_engine: Optional[AsyncEngine] = None

    def init_stores(self, sql_port: int) -> None:
        # Initialize DB connections

        os.environ["SQL_USER"] = "postgres"
        os.environ["SQL_PASSWORD"] = "postgres"
        os.environ["SQL_DATABASE"] = "postgres"
        os.environ["SQL_HOST"] = "localhost"
        os.environ["SQL_PORT"] = str(sql_port)

        # Initialize our stores

        self.case_flag_store = CaseFlagStore()
        self.org_store = OrganizationStore()
        self.prime_time_store = PrimeTimeStore()
        self.first_case_config_store = FirstCaseConfigStore()
        self.terminal_cleans_store = TerminalCleansStore()
        self.site_store = SiteStore()
        self.room_store = RoomStore()
        self.closure_store = ClosureStore()
        self.camera_store = CameraStore()
        self.event_store = EventStore()
        self.highlight_store = HighlightStore()
        self.highlight_feedback_store = HighlightFeedbackStore()
        self.case_store = CaseStore()
        self.case_classification_store = CaseClassificationTypeStore()
        self.mapping_store = MappingStore()
        self.measurement_period_store = MeasurementPeriodStore()
        self.annotation_task_store = AnnotationTaskStore()
        self.procedure_store = ProcedureStore()
        self.case_procedure_store = CaseProcedureStore()
        self.staff_store = StaffStore()
        self.service_line_store = ServiceLineStore()
        self.case_matching_store = CaseMatchingStore()
        self.case_note_plan_store = CaseNotePlanStore()
        self.case_staff_store = CaseStaffStore()
        self.case_staff_plan_store = CaseStaffPlanStore()
        self.phase_store = PhaseStore()
        self.observation_store = ObservationStore()
        self.observation_type_name_store = ObservationTypeNameStore()
        self.contact_information_store = ContactInformationStore()
        self.staffing_needs_store = StaffingNeedsStore()
        self.case_derived_properties_store = CaseDerivedPropertiesStore()
        self.block_store = BlockStore()
        self.staff_event_notification_contact_information_store = (
            StaffEventNotificationContactInformationStore()
        )
        self.board_store = BoardStore()
        self.anesthesia_store = AnesthesiaStore()
        self.user_filter_view_store = UserFilterViewStore()
        self.cluster_store = ClusterStore()
        self.engine_singleton = EngineSingleton()
        self.db_engine = self.engine_singleton()
        self.async_session_maker = get_session_maker(self.db_engine)
        self.case_forecast_store = CaseForecastStore()
        self.turnover_label_store = TurnoverLabelStore()
        self.case_to_block_store = CaseToBlockStore()
        self.block_utilization_store = BlockUtilizationStore()
        self.custom_phase_config_store = CustomPhaseConfigStore()

        asyncio.run(self._try_create_case_activity_table())
        self.case_activity_store = CaseActivityStore()
        self.case_activity_test_helper = CaseActivityTestHelper()

    async def setup(
        self,
        api_server_port: int,
        auth0_url: str,
        mock_auth0_database: MockAuth0Database,
        mock_big_query_database: MockBigQueryDatabase,
    ) -> None:
        if self.async_session_maker is None:
            raise TypeError("Async Session Maker MUST be set")
        async with self.async_session_maker() as session:
            await self._setup(
                api_server_port,
                auth0_url,
                mock_auth0_database,
                mock_big_query_database,
                session,
            )

    async def _setup(
        self,
        api_server_port: int,
        auth0_url: str,
        mock_auth0_database: MockAuth0Database,
        mock_big_query_database: MockBigQueryDatabase,
        session: AsyncSession,
    ) -> None:
        """
        Initialize our various databases (postgres, auth0, bigquery) with stock test data.

        Note: Not every test needs all this data and we may want to look at culling it to improve
        test start/teardown time.
        """
        self.server_url = f"http://127.0.0.1:{api_server_port}"
        self.auth0_url = auth0_url
        mock_big_query_database.set_data(
            [
                {
                    "bucket": datetime.fromisoformat("2022-12-08T06:00:00.000-08:00"),
                    "mops": 0.0,
                    "occupancy": 1.0,
                    "unscrubbed": 0.0,
                    "scrubbed": 1.0,
                    "media_availability": 1.0,
                }
            ]
        )
        self.org_0 = Organization()
        self.org_0.id = "org_id_0"
        self.org_0.name = "Org 0"
        self.org_0.auth0_org_id = "org_auth0_org_id_0"
        await self.org_store.create_organization(
            session=session,
            org=self.org_0,
        )
        mock_auth0_database.resource_server_identifier = f"http://127.0.0.1:{api_server_port}"
        mock_auth0_database.add_user(User("auth0|test-admin", "Test Admin", "<EMAIL>"))
        mock_auth0_database.add_role(AuthRole("admin", "Admin", [auth.permissions.READ_ANY_SITE]))
        mock_auth0_database.add_organization(AuthOrganization("test-org", "Test Org", "Test Org"))
        mock_auth0_database.grant_user_role_in_org("auth0|test-admin", "test-org", "admin")

        mock_auth0_database.add_organization(
            organization=AuthOrganization(
                id=self.org_0.auth0_org_id,
                name=self.org_0.name,
                display_name=self.org_0.name,
            )
        )

        self.service_line_0 = ServiceLineModel()
        self.service_line_0.external_service_line_id = "ORT"
        self.service_line_0.org_id = self.org_0.id
        self.service_line_0.name = "Orthopedic"
        await self.service_line_store.create_or_update_service_line(
            service_line=self.service_line_0, session=session
        )

        self.service_line_1 = ServiceLineModel()
        self.service_line_1.external_service_line_id = "ENT"
        self.service_line_1.org_id = self.org_0.id
        self.service_line_1.name = "Ear, Nose, Throat"
        await self.service_line_store.create_or_update_service_line(
            service_line=self.service_line_1, session=session
        )

        if True:  # For arbitrary nesting
            self.site_0 = Site()
            self.site_0.org_id = self.org_0.id
            self.site_0.id = "site_id_0"
            self.site_0.name = "Site 0"
            self.site_0.timezone = "America/Los_Angeles"
            await self.site_store.create_site(
                session=session,
                site=self.site_0,
            )

            if True:  # For arbitrary nesting
                self.room_0 = RoomModel()
                self.room_0.site_id = self.site_0.id
                self.room_0.name = "Room 0"
                self.room_0.id = "room-id-0"
                self.room_0.org_id = self.org_0.id
                self.room_0.sort_key = "z-room-id-0"
                await self.room_store.create_room(room=self.room_0, session=session)
                if True:  # For arbitrary nesting
                    self.camera_0 = CameraModel()
                    self.camera_0.site_id = self.site_0.id
                    self.camera_0.room_id = self.room_0.id
                    self.camera_0.name = "Camera 0"
                    self.camera_0.id = "camera_id_0"
                    self.camera_0.org_id = self.org_0.id
                    self.camera_0.rtsp_url = "rtsp://nvr:7447/0"
                    self.camera_0.family = "geovision"
                    await self.camera_store.create_camera(self.camera_0, session)

                    self.camera_1 = CameraModel()
                    self.camera_1.site_id = self.site_0.id
                    self.camera_1.room_id = self.room_0.id
                    self.camera_1.name = "Camera 1"
                    self.camera_1.id = "camera_id_1"
                    self.camera_1.org_id = self.org_0.id
                    self.camera_1.rtsp_url = "rtsp://nvr:7447/1"
                    self.camera_1.family = "geovision"
                    await self.camera_store.create_camera(self.camera_1, session)

                    await self.room_0.set_default_camera(
                        camera_id=self.camera_1.id, session=session
                    )

                self.room_1 = RoomModel()
                self.room_1.site_id = self.site_0.id
                self.room_1.name = "Room 1"
                self.room_1.id = "room_id_1"
                self.room_1.org_id = self.org_0.id
                await self.room_store.create_room(room=self.room_1, session=session)

                if True:  # For arbitrary nesting
                    self.camera_2 = CameraModel()
                    self.camera_2.site_id = self.site_0.id
                    self.camera_2.room_id = self.room_1.id
                    self.camera_2.name = "Camera 2"
                    self.camera_2.id = "camera_id_2"
                    self.camera_2.org_id = self.org_0.id
                    self.camera_2.rtsp_url = "rtsp://nvr:7447/2"
                    self.camera_2.family = "geovision"
                    await self.camera_store.create_camera(self.camera_2, session)

                    self.camera_3 = CameraModel()
                    self.camera_3.site_id = self.site_0.id
                    self.camera_3.room_id = self.room_1.id
                    self.camera_3.name = "Camera 3"
                    self.camera_3.id = "camera_id_3"
                    self.camera_3.org_id = self.org_0.id
                    self.camera_3.rtsp_url = "rtsp://nvr:7447/3"
                    self.camera_3.family = "geovision"
                    await self.camera_store.create_camera(self.camera_3, session)

            self.site_1 = Site()
            self.site_1.org_id = self.org_0.id
            self.site_1.id = "site_id_1"
            self.site_1.name = "Site 1"
            self.site_1.timezone = "America/Los_Angeles"
            await self.site_store.create_site(
                session=session,
                site=self.site_1,
            )

            if True:  # For arbitrary nesting
                self.room_2 = RoomModel()
                self.room_2.site_id = self.site_1.id
                self.room_2.name = "Room 2"
                self.room_2.id = "room_id_2"
                self.room_2.org_id = self.org_0.id
                await self.room_store.create_room(room=self.room_2, session=session)

                if True:  # For arbitrary nesting
                    self.camera_4 = CameraModel()
                    self.camera_4.site_id = self.site_1.id
                    self.camera_4.room_id = self.room_2.id
                    self.camera_4.name = "Camera 4"
                    self.camera_4.id = "camera_id_4"
                    self.camera_4.org_id = self.org_0.id
                    self.camera_4.rtsp_url = "rtsp://nvr:7447/4"
                    self.camera_4.family = "geovision"
                    await self.camera_store.create_camera(self.camera_4, session)

            self.site_ca = (
                Site()
            )  # for case_activity table specifically, please not to use this site in other tests
            self.site_ca.org_id = self.org_0.id
            self.site_ca.id = "_site_id_ca_"
            self.site_ca.name = "Site CA"
            self.site_ca.timezone = "America/Los_Angeles"
            await self.site_store.create_site(
                session=session,
                site=self.site_ca,
            )

            if True:  # For arbitrary nesting
                self.room_ca = RoomModel()  # for case_activity table specifically, please not to use this room in other tests
                self.room_ca.site_id = self.site_ca.id
                self.room_ca.name = "Room CA"
                self.room_ca.id = "room_id_ca"
                self.room_ca.org_id = self.org_0.id
                await self.room_store.create_room(room=self.room_ca, session=session)

                if True:  # For arbitrary nesting
                    self.camera_ca = CameraModel()  # for case_activity table specifically, please not to use this camera in other tests
                    self.camera_ca.site_id = self.site_ca.id
                    self.camera_ca.room_id = self.room_ca.id
                    self.camera_ca.name = "Camera CA"
                    self.camera_ca.id = "camera_id_ca"
                    self.camera_ca.org_id = self.org_0.id
                    self.camera_ca.rtsp_url = "rtsp://nvr:7447/ca"
                    self.camera_ca.family = "ubiquitica"
                    await self.camera_store.create_camera(self.camera_ca, session)

        self.org_1 = Organization()
        self.org_1.id = "org_id_1"
        self.org_1.name = "Org 1"
        self.org_1.auth0_org_id = "org_auth0_org_id_1"
        await self.org_store.create_organization(
            session=session,
            org=self.org_1,
        )

        mock_auth0_database.add_organization(
            organization=AuthOrganization(
                id=self.org_1.auth0_org_id,
                name=self.org_1.name,
                display_name=self.org_1.name,
            )
        )

        self.service_line_2 = ServiceLineModel()
        self.service_line_2.external_service_line_id = "GEN"
        self.service_line_2.org_id = self.org_1.id
        self.service_line_2.name = "General Surgery"
        await self.service_line_store.create_or_update_service_line(
            service_line=self.service_line_2, session=session
        )

        if True:  # For arbitrary nesting
            self.site_2 = Site()
            self.site_2.org_id = self.org_1.id
            self.site_2.id = "site_id_2"
            self.site_2.name = "Site 2"
            self.site_2.timezone = "America/Los_Angeles"
            await self.site_store.create_site(
                session=session,
                site=self.site_2,
            )

            if True:  # For arbitrary nesting
                self.room_3 = RoomModel()
                self.room_3.site_id = self.site_2.id
                self.room_3.name = "Room 3"
                self.room_3.id = "room_id_3"
                self.room_3.org_id = self.org_1.id
                await self.room_store.create_room(room=self.room_3, session=session)

                if True:  # For arbitrary nesting
                    self.camera_5 = CameraModel()
                    self.camera_5.site_id = self.site_2.id
                    self.camera_5.room_id = self.room_3.id
                    self.camera_5.name = "Camera 5"
                    self.camera_5.id = "camera_id_5"
                    self.camera_5.org_id = self.org_1.id
                    self.camera_5.rtsp_url = "rtsp://nvr:7447/5"
                    self.camera_5.family = "ubiquiti"
                    await self.camera_store.create_camera(self.camera_5, session)

        self.customer_dashboard_user_role = AuthRole(
            id="customer_dashboard_user_role",
            name="Customer Dashboard User",
            permissions=[  # on par with prod
                permissions.READ_DASHBOARD_INSIGHTS,
                permissions.READ_ANY_ANNOTATION_TASK,
                permissions.READ_ANY_ORG,
                permissions.READ_ANY_SITE,
                permissions.READ_ANY_ROOM,
                permissions.READ_ANY_CAMERA,
                permissions.READ_ANY_CASE,
                permissions.READ_ANY_CASE_DURATION,
                permissions.READ_ANY_EVENT,
                permissions.WRITE_ANY_EVENT,
                permissions.READ_HIGHLIGHT_IF_ASSIGNED,
                permissions.WRITE_FEEDBACK_IF_ASSIGNED,
                permissions.READ_MEDIA_ASSET_IF_ASSIGNED,
                permissions.READ_ANY_LIVE_STREAM,
                permissions.READ_ANY_STAFFING_NEEDS,
                permissions.WRITE_ANY_STAFFING_NEEDS,
                permissions.READ_ANY_OBJECT,
                permissions.READ_ANY_BLOCK,
                permissions.READ_ANY_MEASUREMENT_PERIOD,
            ],
        )
        mock_auth0_database.add_role(role=self.customer_dashboard_user_role)

        self.site_0_scoped_dashboard_role = AuthRole(
            id="site_0_scoped_dashboard_role",
            name="Site Scoped Dashboard Role",
            permissions=[
                permissions.READ_ANY_ORG,
                f"{permissions.READ_SITE_PREFIX}{self.site_0.id}",
                permissions.READ_ANY_ROOM,
                permissions.READ_ANY_CAMERA,
                permissions.READ_ANY_CASE,
                permissions.READ_ANY_EVENT,
                permissions.WRITE_ANY_EVENT,
                permissions.READ_ANY_LIVE_STREAM,
                permissions.READ_DASHBOARD_LIVE,
                permissions.READ_DASHBOARD_LIVE_FROM_SCHEDULE,
                permissions.READ_DASHBOARD_SCHEDULE,
                permissions.READ_ANY_OBJECT,
                permissions.READ_ANY_CONTACT_INFORMATION,
                permissions.READ_ANY_BLOCK,
                permissions.READ_ANY_MEASUREMENT_PERIOD,
                permissions.EMAIL_ANY_AVAILABLE_TIME,
            ],
        )
        mock_auth0_database.add_role(role=self.site_0_scoped_dashboard_role)

        self.apella_labeler_role = AuthRole(
            id="apella_labeler_role",
            name="Apella Labeler",
            permissions=[
                permissions.READ_ANY_ORG,
                permissions.READ_ANY_SITE,
                permissions.READ_ANY_ROOM,
                permissions.READ_ANY_CAMERA,
                permissions.READ_ANY_CASE,
                permissions.READ_ANY_EVENT,
                permissions.WRITE_ANY_EVENT,
                permissions.READ_ANY_USER,
                permissions.READ_ANY_HIGHLIGHT,
                permissions.READ_ANY_FEEDBACK,
                permissions.WRITE_ANY_HIGHLIGHT,
                permissions.READ_ANY_MEDIA_ASSET,
                permissions.READ_ANY_ANNOTATION_TASK,
                permissions.WRITE_ANY_ANNOTATION_TASK,
                permissions.READ_DASHBOARD_INSIGHTS,
                permissions.READ_ANY_OBJECT,
                permissions.READ_TERMINAL_CLEANS,
                permissions.READ_ANY_BIG_BOARD,
                permissions.READ_ANY_BLOCK,
                permissions.READ_ANY_MEASUREMENT_PERIOD,
            ],
        )
        mock_auth0_database.add_role(role=self.apella_labeler_role)

        self.apella_label_reviewer_role = AuthRole(
            id="apella_label_reviewer_role",
            name="Apella Label Reviewer",
            permissions=[
                permissions.WRITE_ANY_ANNOTATION_TASK_TYPE,
                permissions.READ_ANY_ORG,
                permissions.READ_ANY_SITE,
                permissions.READ_ANY_ROOM,
                permissions.READ_ANY_CAMERA,
                permissions.READ_ANY_CASE,
                permissions.READ_ANY_EVENT,
                permissions.WRITE_ANY_EVENT,
                permissions.WRITE_ANY_EVENT_TYPE,
                permissions.READ_ANY_USER,
                permissions.READ_ANY_HIGHLIGHT,
                permissions.READ_ANY_FEEDBACK,
                permissions.WRITE_ANY_HIGHLIGHT,
                permissions.READ_ANY_MEDIA_ASSET,
                permissions.READ_ANY_ANNOTATION_TASK,
                permissions.WRITE_ANY_ANNOTATION_TASK,
                permissions.READ_DASHBOARD_INSIGHTS,
                permissions.READ_ANY_OBJECT,
                permissions.READ_ANY_BIG_BOARD,
                permissions.EDIT_DASHBOARD_SCHEDULE,
                permissions.READ_ANY_BLOCK,
                permissions.READ_ANY_MEASUREMENT_PERIOD,
            ],
        )
        mock_auth0_database.add_role(role=self.apella_label_reviewer_role)

        self.service_account_writer_role = AuthRole(
            id="service_account_write_role",
            name="Service Account Writer",
            permissions=[
                permissions.WRITE_ANY_ANNOTATION_TASK_TYPE,
                permissions.READ_ANY_ORG,
                permissions.READ_ANY_SITE,
                permissions.READ_ANY_ROOM,
                permissions.WRITE_ANY_ROOM,
                permissions.WRITE_CONFIGURATION_ROOM,
                permissions.READ_ANY_CAMERA,
                permissions.READ_ANY_CASE,
                permissions.WRITE_ANY_CASE,
                permissions.READ_ANY_EVENT,
                permissions.WRITE_ANY_EVENT,
                permissions.WRITE_ANY_EVENT_TYPE,
                permissions.READ_ANY_USER,
                permissions.READ_ANY_MEDIA_ASSET,
                permissions.WRITE_ANY_MEDIA_ASSET,
                permissions.WRITE_ANY_HIGHLIGHT,
                permissions.READ_ANY_HIGHLIGHT,
                permissions.WRITE_MAPPINGS,
                permissions.READ_MAPPINGS,
                permissions.READ_ANY_ANNOTATION_TASK,
                permissions.WRITE_ANY_ANNOTATION_TASK,
                permissions.READ_ANY_OBJECT,
                permissions.WRITE_ANY_MEASUREMENT_PERIOD,
                permissions.READ_ANY_BLOCK,
                permissions.WRITE_ANY_BLOCK,
                permissions.READ_ANY_BIG_BOARD,
                permissions.WRITE_ANY_BIG_BOARD,
                permissions.READ_ANY_CONTACT_INFORMATION,
                permissions.WRITE_ANY_CONTACT_INFORMATION,
                permissions.READ_ANY_NOTIFICATION,
                permissions.WRITE_ANY_NOTIFICATION,
                permissions.READ_ANY_CASE_STAFF_PLAN,
                permissions.WRITE_ANY_CASE_STAFF_PLAN,
                permissions.READ_ANY_CASE_NOTE_PLAN,
                permissions.WRITE_ANY_CASE_NOTE_PLAN,
                permissions.EDIT_DASHBOARD_SCHEDULE,
                permissions.READ_ALL_PATIENT,
                permissions.READ_ANY_MEASUREMENT_PERIOD,
                permissions.WRITE_SITE_LAUNCH,
            ],
        )
        mock_auth0_database.add_role(role=self.service_account_writer_role)

        self.field_engineer_role = AuthRole(
            id="field_engineer_role",
            name="Field Engineer Role",
            permissions=[
                permissions.READ_ANY_ORG,
                permissions.WRITE_ANY_ORG,
                permissions.READ_ANY_SITE,
                permissions.WRITE_ANY_SITE,
                permissions.READ_ANY_ROOM,
                permissions.WRITE_ANY_ROOM,
                permissions.READ_ANY_CAMERA,
                permissions.WRITE_ANY_CAMERA,
                permissions.READ_ANY_CLUSTER,
                permissions.WRITE_ANY_CLUSTER,
            ],
        )
        mock_auth0_database.add_role(role=self.field_engineer_role)

        self.surgeon_user = User("auth0|test_auth_id", "Test User 0", "<EMAIL>")
        mock_auth0_database.add_user(user=self.surgeon_user)
        mock_auth0_database.grant_user_role_in_org(
            user_id=self.surgeon_user.user_id,
            role_id=self.customer_dashboard_user_role.id,
            org_id=self.org_0.auth0_org_id,
        )
        mock_auth0_database.grant_user_role_in_org(
            user_id=self.surgeon_user.user_id,
            role_id=self.customer_dashboard_user_role.id,
            org_id=self.org_1.auth0_org_id,
        )

        self.site_0_scoped_user = User(
            "auth0|test_org_site_user_id",
            "Test Org Site User 0",
            "<EMAIL>",
        )
        mock_auth0_database.add_user(user=self.site_0_scoped_user)
        mock_auth0_database.grant_user_role_in_org(
            user_id=self.site_0_scoped_user.user_id,
            role_id=self.site_0_scoped_dashboard_role.id,
            org_id=self.org_0.auth0_org_id,
        )

        self.user_without_permissions = User(
            "auth0|test_auth_id_1", "Test User 1", "<EMAIL>"
        )
        mock_auth0_database.add_user(user=self.user_without_permissions)

        self.non_registered_user = User(
            "auth0|test_not_registered",
            "Test Not Registered",
            "<EMAIL>",
        )

        self.labeler_user = User("auth0|labeler_0", "Apella labeler User", "<EMAIL>")
        mock_auth0_database.add_user(user=self.labeler_user, role_ids=[self.apella_labeler_role.id])

        self.label_reviewer_user = User(
            "auth0|label_reviewer_0", "Apella label reviewer User", "<EMAIL>"
        )
        mock_auth0_database.add_user(
            user=self.label_reviewer_user, role_ids=[self.apella_label_reviewer_role.id]
        )

        self.internal_service_account_user = User(
            "auth0|deploy_admin_auth_id", "Deploy Admin", "<EMAIL>"
        )
        mock_auth0_database.add_user(
            user=self.internal_service_account_user,
            role_ids=[self.service_account_writer_role.id],
        )

        self.hospital_administrator_user = User(
            "auth0|hospital_administrator",
            "Hospital Administrator",
            "<EMAIL>",
        )
        mock_auth0_database.add_user(user=self.hospital_administrator_user)
        mock_auth0_database.grant_user_role_in_org(
            user_id=self.hospital_administrator_user.user_id,
            org_id=self.org_0.auth0_org_id,
            role_id=self.customer_dashboard_user_role.id,
        )
        mock_auth0_database.grant_user_role_in_org(
            user_id=self.hospital_administrator_user.user_id,
            org_id=self.org_1.auth0_org_id,
            role_id=self.customer_dashboard_user_role.id,
        )

        self.field_engineer_user = User(
            "auth0|field_engineer", "Field Engineer", "<EMAIL>"
        )
        mock_auth0_database.add_user(
            user=self.field_engineer_user, role_ids=[self.field_engineer_role.id]
        )

        self.case_0 = Case()
        self.case_0.external_case_id = "ext_case_id_0"
        self.case_0.case_id = "3dabfefa-d471-4945-b05a-961d17c194c4"
        self.case_0.org_id = self.org_0.id
        self.case_0.site_id = self.site_0.id
        self.case_0.room_id = self.room_0.id
        self.case_0.scheduled_start_time = datetime.now(tz=timezone.utc) - timedelta(minutes=100)
        self.case_0.scheduled_end_time = self.case_0.scheduled_start_time + timedelta(minutes=100)
        self.case_0.is_add_on = False
        self.case_0.status = SCHEDULED

        self.case_0_raw = CaseRaw()
        self.case_0_raw.raw = cast(JSON, {"raw_for": "case 0"})
        self.case_0_raw.raw_id = "case_0_raw_id"
        self.case_0_raw.org_id = self.case_0.org_id
        self.case_0_raw.external_message_id = "case_0_external_message_id"
        await self.case_store.upsert_case_raw(self.case_0_raw, session)

        await self.case_store.create_case(self.case_0, session)
        await self.case_store.add_case_id_to_raw(
            case_external_message_id=self.case_0_raw.external_message_id,
            case_id=self.case_0.case_id,
            session=session,
        )

        if True:  # For arbitrary nesting
            self.event_0 = EventModel()
            self.event_0.id = "89d4d3b3-a70a-4bbe-88a4-f895c0d1b860"
            self.event_0.event_type_id = "patient_wheels_in"
            self.event_0.case_id = self.case_0.case_id
            self.event_0.room_id = self.case_0.room_id
            self.event_0.camera_id = self.camera_0.id
            self.event_0.site_id = self.case_0.site_id
            self.event_0.org_id = self.case_0.org_id
            self.event_0.start_time = self.case_0.scheduled_start_time
            self.event_0.process_timestamp = datetime.now(tz=timezone.utc)
            self.event_0.source = "component-tests"
            self.event_0.source_type = "human_gt"
            self.event_0.labels = ["Test"]
            self.event_0.camera_id = self.camera_0.id
            await self.event_store.create_event(
                session=session,
                event=self.event_0,
            )

            self.event_1 = EventModel()
            self.event_1.id = "9a111ba4-2bca-4e40-8e6b-14b5a40c2f85"
            self.event_1.event_type_id = "patient_draped"
            self.event_1.case_id = self.case_0.case_id
            self.event_1.room_id = self.case_0.room_id
            self.event_1.camera_id = self.camera_0.id
            self.event_1.site_id = self.case_0.site_id
            self.event_1.org_id = self.case_0.org_id
            self.event_1.start_time = self.event_0.start_time + timedelta(minutes=10)
            self.event_1.process_timestamp = datetime.now(tz=timezone.utc)
            self.event_1.source = "component-tests"
            self.event_1.source_type = "human_gt"
            self.event_1.labels = ["Test"]
            await self.event_store.create_event(
                session=session,
                event=self.event_1,
            )

        def create_case_clf_type(session: Session) -> None:
            # the following data will be added to the `cases` table, so it must first be added to the `case_classification_types` table b/c of the foreign key constraint
            orgs_data = {
                "org_id_0": {
                    "Elective": "CASE_CLASSIFICATION_ELECTIVE",
                    "Emergent": "CASE_CLASSIFICATION_EMERGENT",
                    "Unknown": "CASE_CLASSIFICATION_UNKNOWN",
                    "Urgent": "CASE_CLASSIFICATION_URGENT",
                },
                "org_id_1": {
                    "Elective": "CASE_CLASSIFICATION_ELECTIVE",
                    "Emergent": "CASE_CLASSIFICATION_EMERGENT",
                    "Unknown": "CASE_CLASSIFICATION_UNKNOWN",
                    "Urgent": "CASE_CLASSIFICATION_URGENT",
                },
            }

            # Convert the data to a list of dictionaries for insertion
            insert_data = []
            for org_id, classifications in orgs_data.items():
                for name, classification in classifications.items():
                    insert_data.append({"org_id": org_id, "name": name, "id": classification})
            session.execute(
                insert(CaseClassificationTypeModel).values(insert_data).on_conflict_do_nothing()
            )

        await component_test_db_async_session_helper(create_case_clf_type, async_session=session)

        self.case_1 = Case()
        self.case_1.external_case_id = "ext_case_id_1"
        self.case_1.case_id = "f4514182-f14b-4c4e-86fb-19dd25125773"
        self.case_1.org_id = self.org_0.id
        self.case_1.site_id = self.site_0.id
        self.case_1.room_id = self.room_0.id
        self.case_1.case_classification_types_id = "CASE_CLASSIFICATION_URGENT"
        self.case_1.scheduled_start_time = self.case_0.scheduled_end_time + timedelta(minutes=1)
        self.case_1.scheduled_end_time = self.case_1.scheduled_start_time + timedelta(minutes=100)
        self.case_1.status = SCHEDULED
        self.case_1.is_add_on = True
        await self.case_store.create_case(self.case_1, session)

        if True:  # For arbitrary nesting
            self.event_2 = EventModel()
            self.event_2.id = "6e7a7257-941f-49a9-a33d-73af9c96d9b4"
            self.event_2.event_type_id = "patient_undraped"
            self.event_2.case_id = self.case_1.case_id
            self.event_2.room_id = self.case_1.room_id
            self.event_2.camera_id = self.camera_0.id
            self.event_2.site_id = self.case_1.site_id
            self.event_2.org_id = self.case_1.org_id
            self.event_2.start_time = self.case_1.scheduled_start_time
            self.event_2.process_timestamp = datetime.now(tz=timezone.utc)
            self.event_2.source = "component-tests"
            self.event_2.source_type = "human_gt"
            self.event_2.labels = ["Test"]
            await self.event_store.create_event(
                session=session,
                event=self.event_2,
            )

        self.case_ca = Case()
        self.case_ca.external_case_id = "ext_case_id_ca"
        self.case_ca.case_id = "3babfefa-d471-4945-b05a-961d17c194c5"
        self.case_ca.org_id = self.org_0.id
        self.case_ca.site_id = self.site_ca.id
        self.case_ca.room_id = self.room_ca.id
        self.case_ca.case_classification_types_id = "CASE_CLASSIFICATION_ELECTIVE"
        self.case_ca.scheduled_start_time = datetime.fromisoformat("2021-03-03T12:01:02.345+00:00")
        self.case_ca.scheduled_end_time = self.case_ca.scheduled_start_time + timedelta(minutes=100)
        self.case_ca.is_add_on = False
        self.case_ca.status = SCHEDULED
        await self.case_store.create_case(self.case_ca, session)

        self.case_ca_2 = Case()
        self.case_ca_2.external_case_id = "ext_case_id_ca_2"
        self.case_ca_2.case_id = "3babgefa-d471-4945-b05a-961d17c194c5"
        self.case_ca_2.org_id = self.org_0.id
        self.case_ca_2.site_id = self.site_ca.id
        self.case_ca_2.room_id = self.room_ca.id
        self.case_ca_2.case_classification_types_id = "CASE_CLASSIFICATION_ELECTIVE"
        self.case_ca_2.scheduled_start_time = datetime.fromisoformat(
            "2021-03-03T12:00:02.345+00:00"
        )
        self.case_ca_2.scheduled_end_time = self.case_ca_2.scheduled_start_time + timedelta(
            minutes=100
        )
        self.case_ca_2.is_add_on = False
        self.case_ca_2.status = SCHEDULED
        await self.case_store.create_case(self.case_ca_2, session)

        self.case_2 = Case()
        self.case_2.external_case_id = "ext_case_id_2"
        self.case_2.case_id = "4a81eacc-9701-4457-a5e8-3a8a1d2cd7d2"
        self.case_2.org_id = self.org_0.id
        self.case_2.site_id = self.site_0.id
        self.case_2.room_id = self.room_2.id
        self.case_2.case_classification_types_id = "CASE_CLASSIFICATION_EMERGENT"
        self.case_2.scheduled_start_time = self.case_1.scheduled_end_time
        self.case_2.scheduled_end_time = self.case_2.scheduled_start_time + timedelta(minutes=100)
        self.case_2.status = SCHEDULED
        self.case_2.is_add_on = False
        await self.case_store.create_case(self.case_2, session)

        self.case_3 = Case()
        self.case_3.external_case_id = "ext_case_id_3"
        self.case_3.case_id = "a59af4b2-e292-4079-9924-9c968b954244"
        self.case_3.org_id = self.org_0.id
        self.case_3.site_id = self.site_0.id
        self.case_3.room_id = self.room_0.id
        self.case_3.case_classification_types_id = "CASE_CLASSIFICATION_ELECTIVE"
        self.case_3.users = [self.surgeon_user.user_id]  # type: ignore [attr-defined]
        self.case_3.scheduled_start_time = datetime.fromisoformat("2021-03-01T12:01:02.345+00:00")
        self.case_3.scheduled_end_time = self.case_3.scheduled_start_time + timedelta(minutes=100)
        self.case_3.status = SCHEDULED
        self.case_3.cancellation_reason = [
            "Test cancellation reason line 1",
            "Test cancellation reason line 2",
        ]
        await self.case_store.create_case(self.case_3, session)

        self.case_4 = Case()
        self.case_4.external_case_id = "ext_case_id_4"
        self.case_4.case_id = "a59af4b2-e292-4079-9924-9c968b954255"
        self.case_4.org_id = self.org_1.id
        self.case_4.site_id = self.site_2.id
        self.case_4.room_id = self.room_3.id
        self.case_4.case_classification_types_id = "CASE_CLASSIFICATION_URGENT"
        self.case_4.scheduled_start_time = datetime.fromisoformat("2021-03-01T12:01:02.345+00:00")
        self.case_4.scheduled_end_time = self.case_4.scheduled_start_time + timedelta(minutes=100)
        self.case_4.status = SCHEDULED
        self.case_4.is_add_on = True

        await self.case_store.create_case(self.case_4, session)

        self.event_3 = EventModel()
        self.event_3.id = "62b798bd-bc10-4df9-bee1-9b2fda2bc0f0"
        self.event_3.event_type_id = "patient_wheels_out"
        self.event_3.source_type = "human_gt"
        self.event_3.source = "component-test"
        self.event_3.org_id = self.org_0.id
        self.event_3.site_id = self.site_0.id
        self.event_3.room_id = self.room_0.id
        self.event_3.camera_id = self.camera_0.id
        self.event_3.start_time = datetime.fromisoformat("2021-03-01T12:01:02.345+00:00")
        self.event_3.process_timestamp = self.event_3.start_time
        self.event_3.updated_time = datetime.fromisoformat("2021-03-01T12:01:03+00:00")
        self.event_3.labels = ["Needs Review", "Test"]
        self.event_3.confidence = 0.7
        await self.event_store.create_event(
            session=session,
            event=self.event_3,
        )

        self.event_ca = EventModel()
        self.event_ca.id = "d61d3c2a-a6d6-4c9b-9d63-ed1175952144"
        self.event_ca.event_type_id = "patient_wheels_in"
        self.event_ca.source_type = "human_gt"
        self.event_ca.source = "component-test"
        self.event_ca.org_id = self.org_0.id
        self.event_ca.site_id = self.site_ca.id
        self.event_ca.room_id = self.room_ca.id
        self.event_ca.camera_id = self.camera_ca.id
        self.event_ca.start_time = datetime.fromisoformat("2021-03-03T12:01:02.345+00:00")
        self.event_ca.process_timestamp = self.event_ca.start_time
        self.event_ca.labels = ["Test"]
        self.event_ca.confidence = 0.7
        await self.event_store.create_event(
            session=session,
            event=self.event_ca,
        )
        self.event_ca_2 = EventModel()
        self.event_ca_2.id = "w61d3c2a-a6d6-4c9b-9d63-ed1175952145"
        self.event_ca_2.event_type_id = "patient_wheels_out"
        self.event_ca_2.source_type = "human_gt"
        self.event_ca_2.source = "component-test"
        self.event_ca_2.org_id = self.org_0.id
        self.event_ca_2.site_id = self.site_ca.id
        self.event_ca_2.room_id = self.room_ca.id
        self.event_ca_2.camera_id = self.camera_ca.id
        self.event_ca_2.start_time = datetime.fromisoformat("2021-03-03T12:31:02.345+00:00")
        self.event_ca_2.process_timestamp = self.event_ca_2.start_time
        self.event_ca_2.labels = ["Test"]
        self.event_ca_2.confidence = 0.7
        await self.event_store.create_event(
            session=session,
            event=self.event_ca_2,
        )

        self.event_4 = EventModel()
        self.event_4.id = "d61d3c2a-a6d6-4c9b-9d63-ed1175952145"
        self.event_4.event_type_id = "patient_xfer_to_bed"
        self.event_4.source_type = "human_gt"
        self.event_4.source = "component-test"
        self.event_4.org_id = self.org_0.id
        self.event_4.site_id = self.site_0.id
        self.event_4.room_id = self.room_0.id
        self.event_4.camera_id = self.camera_0.id
        self.event_4.start_time = datetime.fromisoformat("2021-04-01T13:01:02.345+00:00")
        self.event_4.process_timestamp = self.event_4.start_time
        self.event_4.labels = ["Needs Review", "Test"]
        await self.event_store.create_event(
            session=session,
            event=self.event_4,
        )

        self.event_5 = EventModel()
        self.event_5.id = "d61d3c2a-a6d6-4c9b-9d63-ed1175952146"
        self.event_5.event_type_id = "patient_wheels_in"
        self.event_5.source_type = "prediction"
        self.event_5.model_version = "jellyslug"
        self.event_5.source = "component-test"
        self.event_5.org_id = self.org_1.id
        self.event_5.site_id = self.site_2.id
        self.event_5.room_id = self.room_3.id
        self.event_5.camera_id = self.camera_5.id
        self.event_5.start_time = datetime.fromisoformat("2021-04-01T13:01:02.345+00:00")
        self.event_5.process_timestamp = self.event_5.start_time
        self.event_5.labels = []
        self.event_5.event_type_id = "patient_wheels_in"
        await self.event_store.create_event(
            session=session,
            event=self.event_5,
        )

        [self.staff_0] = await self.staff_store.upsert_staff(
            [
                StaffModel(
                    external_staff_id="external_staff_id_0",
                    first_name="Gregory",
                    last_name="House",
                    org_id=self.org_0.id,
                )
            ],
            session=session,
        )

        self.media_asset_0_id = str(uuid4())
        self.media_asset_1_id = str(uuid4())

        self.highlight_1 = Highlight()
        self.highlight_1.id = UUID("7cf42ded-cd91-4b8d-b597-3ddcccca4eae")
        self.highlight_1.description = "test highlight 1 description"
        self.highlight_1.org_id = self.org_0.id
        self.highlight_1.site_id = self.site_0.id
        self.highlight_1.room_id = self.room_0.id
        self.highlight_1.start_time = datetime.fromisoformat("2021-03-01T12:01:02.345+00:00")
        self.highlight_1.end_time = datetime.fromisoformat("2021-03-01T13:01:02.345+00:00")
        self.highlight_1.category = "timeout"

        self.highlight_2 = Highlight()
        self.highlight_2.id = UUID("705a5d82-2f74-4f40-bd5b-1bbfd8672093")
        self.highlight_2.org_id = self.org_0.id
        self.highlight_2.site_id = self.site_0.id
        self.highlight_2.room_id = self.room_0.id
        self.highlight_2.start_time = datetime.fromisoformat("2021-03-05T12:01:02.345+00:00")
        self.highlight_2.end_time = datetime.fromisoformat("2021-03-05T14:01:02.345+00:00")
        self.highlight_2.description = "test highlight 2 description"
        self.highlight_2.category = "turnover"

        self.highlight_asset_1 = HighlightAsset()
        self.highlight_asset_1.highlight_id = self.highlight_1.id
        self.highlight_asset_1.asset_id = self.media_asset_0_id

        self.highlight_asset_2 = HighlightAsset()
        self.highlight_asset_2.highlight_id = self.highlight_2.id
        self.highlight_asset_2.asset_id = self.media_asset_1_id

        self.highlight_user_1 = HighlightUser()
        self.highlight_user_1.highlight_id = self.highlight_1.id
        self.highlight_user_1.user_id = self.hospital_administrator_user.user_id

        self.highlight_user_2 = HighlightUser()
        self.highlight_user_2.highlight_id = self.highlight_2.id
        self.highlight_user_2.user_id = self.hospital_administrator_user.user_id

        self.highlight_user_3 = HighlightUser()
        self.highlight_user_3.highlight_id = self.highlight_1.id
        self.highlight_user_3.user_id = self.surgeon_user.user_id

        self.highlight_user_4 = HighlightUser()
        self.highlight_user_4.highlight_id = self.highlight_1.id
        self.highlight_user_4.user_id = self.user_without_permissions.user_id

        await self.highlight_store.create_highlight(
            highlight=self.highlight_1,
            highlight_users=[self.highlight_user_1, self.highlight_user_3, self.highlight_user_4],
            session=session,
        )

        await self.highlight_store.create_highlight(
            highlight=self.highlight_2, highlight_users=[self.highlight_user_2], session=session
        )

        await self.highlight_store.create_highlight_assets(
            highlight_assets=[self.highlight_asset_1, self.highlight_asset_2], session=session
        )

        self.highlight_feedback_1 = HighlightFeedback()
        self.highlight_feedback_1.id = UUID("983cc9dd-5a8f-4478-80fe-13710736a8e2")
        self.highlight_feedback_1.comment = "test comments"
        self.highlight_feedback_1.rating = 5
        self.highlight_feedback_1.highlight_id = self.highlight_1.id
        self.highlight_feedback_1.user_id = self.hospital_administrator_user.user_id
        self.highlight_feedback_1.org_id = self.highlight_1.org_id

        await self.highlight_feedback_store.create_feedback(
            feedback=self.highlight_feedback_1,
            session=session,
        )

        self.highlight_feedback_2 = HighlightFeedback()
        self.highlight_feedback_2.id = UUID("983cc9dd-5a8f-4478-80fe-13710736a8e4")
        self.highlight_feedback_2.comment = "test comments"
        self.highlight_feedback_2.rating = 5
        self.highlight_feedback_2.highlight_id = self.highlight_1.id
        self.highlight_feedback_2.user_id = self.surgeon_user.user_id
        self.highlight_feedback_2.org_id = self.highlight_1.org_id

        await self.highlight_feedback_store.create_feedback(
            feedback=self.highlight_feedback_2,
            session=session,
        )

        self.surgery_task_type = await self.annotation_task_store.update_annotation_task_type(
            session=session,
            task_type_update=TaskTypeUpdate(
                id=surgery_task_type_id,
                name="Surgery",
                description="The default task type containing all events",
                event_types=[
                    "patient_briefing_end",
                    "patient_briefing_start",
                    "patient_draped",
                    "patient_imaging_end",
                    "patient_imaging_start",
                    "patient_on_hospital_bed",
                    "patient_on_or_table",
                    "patient_skin_prep_end",
                    "patient_skin_prep_start",
                    "patient_undraped",
                    "patient_wheels_in",
                    "patient_wheels_out",
                    "patient_xfer_to_bed",
                    "patient_xfer_to_or_table",
                    "post_operative",
                    "pre_operative",
                    "sensitive_content_end",
                    "sensitive_content_start",
                    "sterile_pack_on_back_table",
                    "surgery",
                    "terminal_clean_end",
                    "terminal_clean_start",
                    "timeout_end",
                    "timeout_start",
                    "turn_over_clean",
                    "turn_over_idle",
                    "turn_over_open",
                    "back_table_cleared",
                ],
                context_event_types=[],
                priority=2,
                archived=False,
                detect_idle=True,
                allow_skipping_review=False,
                optimize_tasks=False,
                annotator_ids=[
                    self.labeler_user.user_id,
                    self.label_reviewer_user.user_id,
                ],
                provisional_annotator_ids=[],
                reviewer_ids=[
                    self.labeler_user.user_id,
                    self.label_reviewer_user.user_id,
                ],
            ),
        )

        self.mop_task_type = await self.annotation_task_store.update_annotation_task_type(
            session=session,
            task_type_update=TaskTypeUpdate(
                id=mop_task_type_id,
                name="Mop",
                description="The mop type concerns all mop-related events",
                event_types=["mop", "mop_in", "mop_out"],
                context_event_types=[],
                priority=5,
                archived=False,
                detect_idle=True,
                allow_skipping_review=False,
                optimize_tasks=False,
                annotator_ids=[],
                provisional_annotator_ids=[],
                reviewer_ids=[],
            ),
        )

        self.light_task_type = await self.annotation_task_store.update_annotation_task_type(
            session=session,
            task_type_update=TaskTypeUpdate(
                id=light_task_type_id,
                name="Light",
                description="Is it on or off? ¿Porque no los dos?",
                event_types=["lights_off", "lights_on"],
                context_event_types=[],
                priority=5,
                archived=False,
                detect_idle=True,
                allow_skipping_review=False,
                optimize_tasks=False,
                annotator_ids=[],
                provisional_annotator_ids=[],
                reviewer_ids=[],
            ),
        )

        # Annotation Tasks
        self.annotation_task_0 = AnnotationTask(
            org_id=self.org_0.id,
            site_id=self.site_0.id,
            room_id=self.room_0.id,
            # 6am PST to UTC
            start_time=datetime(2021, 10, day=9, hour=13, minute=0, tzinfo=timezone.utc),
            end_time=datetime(2021, 10, day=10, hour=13, minute=0, tzinfo=timezone.utc),
            status=TaskStatus.DONE,
            annotator_user_id=self.labeler_user.user_id,
            reviewer_user_id=self.labeler_user.user_id,
            updated_time=datetime(2021, 10, day=11, hour=10, minute=10, tzinfo=timezone.utc),
            type_id=self.surgery_task_type.id,
            type_version=self.surgery_task_type.version,  # type: ignore [attr-defined]
        )
        self.annotation_task_1 = AnnotationTask(
            org_id=self.org_0.id,
            site_id=self.site_0.id,
            room_id=self.room_1.id,
            # 6am PST to UTC
            start_time=datetime(2021, 10, day=9, hour=13, minute=0, tzinfo=timezone.utc),
            end_time=datetime(2021, 10, day=10, hour=13, minute=0, tzinfo=timezone.utc),
            status=TaskStatus.IN_PROGRESS,
            annotator_user_id=self.labeler_user.user_id,
            reviewer_user_id=self.labeler_user.user_id,
            type_id=self.mop_task_type.id,
            type_version=self.mop_task_type.version,  # type: ignore [attr-defined]
        )
        self.annotation_task_2 = AnnotationTask(
            org_id=self.org_0.id,
            site_id=self.site_0.id,
            room_id=self.room_2.id,
            # 6am PST to UTC
            start_time=datetime(2021, 10, day=9, hour=13, minute=0, tzinfo=timezone.utc),
            end_time=datetime(2021, 10, day=10, hour=13, minute=0, tzinfo=timezone.utc),
            status=TaskStatus.NOT_STARTED,
            annotator_user_id=None,
            reviewer_user_id=self.labeler_user.user_id,
            type_id=self.light_task_type.id,
            type_version=self.light_task_type.version,  # type: ignore [attr-defined]
        )

        self.annotation_task_3 = AnnotationTask(
            org_id=self.org_0.id,
            site_id=self.site_0.id,
            room_id=self.room_0.id,
            # 6am PST to UTC
            start_time=self.event_0.start_time - timedelta(minutes=200),
            end_time=self.event_0.start_time + timedelta(minutes=200),
            status=TaskStatus.IN_PROGRESS,
            annotator_user_id=self.labeler_user.user_id,
            reviewer_user_id=self.labeler_user.user_id,
            type_id=self.surgery_task_type.id,
            type_version=self.surgery_task_type.version,  # type: ignore [attr-defined]
        )

        self.annotation_task_4 = AnnotationTask(
            org_id=self.org_0.id,
            site_id=self.site_0.id,
            room_id=self.room_1.id,
            # 6am PST to UTC
            start_time=self.event_0.start_time - timedelta(minutes=200),
            end_time=self.event_0.start_time + timedelta(minutes=1),
            status=TaskStatus.DONE,
            annotator_user_id=self.labeler_user.user_id,
            reviewer_user_id=self.labeler_user.user_id,
            type_id=self.surgery_task_type.id,
            type_version=self.surgery_task_type.version,  # type: ignore [attr-defined]
        )
        self.annotation_task_5 = AnnotationTask(
            org_id=self.org_0.id,
            site_id=self.site_0.id,
            room_id=self.room_1.id,
            # 6am PST to UTC
            start_time=self.event_0.start_time + timedelta(minutes=1),
            end_time=self.event_0.start_time + timedelta(minutes=200),
            status=TaskStatus.IN_PROGRESS,
            annotator_user_id=self.labeler_user.user_id,
            reviewer_user_id=self.labeler_user.user_id,
            type_id=self.surgery_task_type.id,
            type_version=self.surgery_task_type.version,  # type: ignore [attr-defined]
        )

        self.annotation_task_6 = AnnotationTask(
            org_id=self.org_0.id,
            site_id=self.site_0.id,
            room_id=self.room_2.id,
            # 6am PST to UTC
            start_time=self.event_0.start_time - timedelta(minutes=200),
            end_time=self.event_0.start_time - timedelta(minutes=50),
            status=TaskStatus.DONE,
            annotator_user_id=self.labeler_user.user_id,
            reviewer_user_id=self.labeler_user.user_id,
            type_id=self.surgery_task_type.id,
            type_version=self.surgery_task_type.version,  # type: ignore [attr-defined]
        )
        self.annotation_task_7 = AnnotationTask(
            org_id=self.org_0.id,
            site_id=self.site_0.id,
            room_id=self.room_2.id,
            # 6am PST to UTC
            start_time=self.event_0.start_time - timedelta(minutes=50),
            end_time=self.event_0.start_time + timedelta(minutes=50),
            status=TaskStatus.IN_PROGRESS,
            annotator_user_id=self.labeler_user.user_id,
            reviewer_user_id=self.labeler_user.user_id,
            type_id=self.surgery_task_type.id,
            type_version=self.surgery_task_type.version,  # type: ignore [attr-defined]
        )

        self.annotation_task_8 = AnnotationTask(
            org_id=self.org_0.id,
            site_id=self.site_0.id,
            room_id=self.room_3.id,
            # 6am PST to UTC
            start_time=self.event_0.start_time - timedelta(minutes=200),
            end_time=self.event_0.start_time + timedelta(seconds=1),
            status=TaskStatus.DONE,
            annotator_user_id=self.labeler_user.user_id,
            reviewer_user_id=self.labeler_user.user_id,
            type_id=self.surgery_task_type.id,
            type_version=self.surgery_task_type.version,  # type: ignore [attr-defined]
        )
        self.annotation_task_9 = AnnotationTask(
            org_id=self.org_0.id,
            site_id=self.site_0.id,
            room_id=self.room_3.id,
            # 6am PST to UTC
            start_time=self.event_0.start_time + timedelta(seconds=1),
            end_time=self.event_0.start_time + timedelta(minutes=11),
            status=TaskStatus.DONE,
            annotator_user_id=self.labeler_user.user_id,
            reviewer_user_id=self.labeler_user.user_id,
            type_id=self.surgery_task_type.id,
            type_version=self.surgery_task_type.version,  # type: ignore [attr-defined]
        )
        self.annotation_task_10 = AnnotationTask(
            org_id=self.org_0.id,
            site_id=self.site_0.id,
            room_id=self.room_3.id,
            # 6am PST to UTC
            start_time=self.event_0.start_time + timedelta(minutes=2),
            end_time=self.event_0.start_time + timedelta(minutes=8),
            status=TaskStatus.DONE,
            annotator_user_id=self.labeler_user.user_id,
            reviewer_user_id=self.labeler_user.user_id,
            type_id=self.surgery_task_type.id,
            type_version=self.surgery_task_type.version,  # type: ignore [attr-defined]
        )

        self.annotation_task_schedule_0 = (
            await self.annotation_task_store.create_annotation_task_schedule(
                session=session,
                task_schedule_create=TaskScheduleCreate(
                    site_ids=[self.site_0.id],
                    annotation_task_type_id=self.surgery_task_type.id,
                    start_time=datetime.strptime("06:00", "%H:%M").time(),
                    interval=12,
                ),
            )
        )

        self.annotation_task_type_0: AnnotationTaskTypeModel = AnnotationTaskTypeModel(
            name="User created task type 0",
            description="Helpful description of interesting task",
            event_types=[
                "patient_wheels_in",
                "patient_wheels_out",
            ],
            priority=3,
        )
        await self.annotation_task_store.create_annotation_task_type(
            session=session,
            annotation_task_type=self.annotation_task_type_0,
        )

        self.terminal_clean_task_type: AnnotationTaskTypeModel = AnnotationTaskTypeModel()
        self.terminal_clean_task_type.name = "Terminal Cleans"
        self.terminal_clean_task_type.description = "Terminal cleaning - higher priority"
        self.terminal_clean_task_type.event_types = [
            "terminal_clean_start",
            "terminal_clean_end",
        ]
        self.terminal_clean_task_type.priority = 1
        self.terminal_clean_task_type._annotators = [  # type: ignore [attr-defined]
            AnnotationTaskTypeAnnotator(user_id=self.label_reviewer_user.user_id)
        ]
        await self.annotation_task_store.create_annotation_task_type(
            session=session,
            annotation_task_type=self.terminal_clean_task_type,
        )

        await self.annotation_task_store.create_task(
            session=session,
            annotation_task=self.annotation_task_0,
        )
        await self.annotation_task_store.create_task(
            session=session,
            annotation_task=self.annotation_task_1,
        )
        await self.annotation_task_store.create_task(
            session=session,
            annotation_task=self.annotation_task_2,
        )
        await self.annotation_task_store.create_task(
            session=session,
            annotation_task=self.annotation_task_3,
        )
        await self.annotation_task_store.create_task(
            session=session,
            annotation_task=self.annotation_task_4,
        )
        await self.annotation_task_store.create_task(
            session=session,
            annotation_task=self.annotation_task_5,
        )
        await self.annotation_task_store.create_task(
            session=session,
            annotation_task=self.annotation_task_6,
        )
        await self.annotation_task_store.create_task(
            session=session,
            annotation_task=self.annotation_task_7,
        )
        await self.annotation_task_store.create_task(
            session=session,
            annotation_task=self.annotation_task_8,
        )
        await self.annotation_task_store.create_task(
            session=session,
            annotation_task=self.annotation_task_9,
        )
        await self.annotation_task_store.create_task(
            session=session,
            annotation_task=self.annotation_task_10,
        )

        self.event_type_0: EventTypeModel = EventTypeModel()
        self.event_type_0.id = str(uuid4())
        self.event_type_0.name = "User created event type 0"
        self.event_type_0.description = "Helpful description of interesting event type"
        self.event_type_0.type = "uncategorized"
        self.event_type_0.color = "#001122"

        self.event_type_0 = await self.event_store.create_event_type(
            session=session,
            event_type=self.event_type_0,
        )

        # Two events at the same time, one patient_wheels_in and one the above event_type_0
        self.event_6 = EventModel()
        self.event_6.id = str(uuid4())
        self.event_6.event_type_id = "patient_wheels_in"
        self.event_6.source_type = "human_gt"
        self.event_6.source = "component-test"
        self.event_6.org_id = self.org_0.id
        self.event_6.site_id = self.site_0.id
        self.event_6.room_id = self.room_0.id
        self.event_6.camera_id = self.camera_0.id
        self.event_6.start_time = datetime.fromisoformat("2021-05-01T12:01:02.345+00:00")
        self.event_6.process_timestamp = self.event_6.start_time
        self.event_6.updated_time = self.event_6.start_time
        await self.event_store.create_event(
            session=session,
            event=self.event_6,
        )
        self.event_7 = EventModel()
        self.event_7.id = str(uuid4())
        self.event_7.event_type_id = self.event_type_0.id
        self.event_7.source_type = "human_gt"
        self.event_7.source = "component-test"
        self.event_7.org_id = self.org_0.id
        self.event_7.site_id = self.site_0.id
        self.event_7.room_id = self.room_0.id
        self.event_7.camera_id = self.camera_0.id
        self.event_7.start_time = datetime.fromisoformat("2021-05-01T12:01:02.345+00:00")
        self.event_7.process_timestamp = self.event_7.start_time
        self.event_7.updated_time = self.event_7.start_time
        await self.event_store.create_event(
            session=session,
            event=self.event_7,
        )

        self.observation_0 = ObservationModel()
        self.observation_0.id = "eaae9871-56d9-499a-baab-b91f2d8d7580"
        self.observation_0.type_id = "OBSERVED_PREP_START"
        self.observation_0.org_id = self.org_0.id
        self.observation_0.case_id = self.case_0.case_id
        self.observation_0.recorded_time = datetime.fromisoformat("2021-03-01T12:01:01.345+00:00")
        self.observation_0.observation_time = datetime.fromisoformat(
            "2021-03-01T12:01:01.345+00:00"
        )
        await self.observation_store.create_observation(self.observation_0, session)

        self.observation_1 = ObservationModel()
        self.observation_1.id = "6f943e2b-4b98-4915-a413-64cccc09192e"
        self.observation_1.type_id = "OBSERVED_PREP_COMPLETE"
        self.observation_1.org_id = self.org_0.id
        self.observation_1.case_id = self.case_1.case_id
        self.observation_1.recorded_time = datetime.fromisoformat("2021-03-01T12:01:02.345+00:00")
        self.observation_1.observation_time = datetime.fromisoformat(
            "2021-03-01T12:01:02.345+00:00"
        )
        await self.observation_store.create_observation(self.observation_1, session)

        self.observation_2 = ObservationModel()
        self.observation_2.id = "8429f8f1-04bc-4bd8-a2df-8742a9e18291"
        self.observation_2.type_id = "OBSERVED_PREP_START"
        self.observation_2.org_id = self.org_1.id
        self.observation_2.case_id = self.case_4.case_id
        self.observation_2.recorded_time = datetime.fromisoformat("2021-03-03T12:01:01.345+00:00")
        self.observation_2.observation_time = datetime.fromisoformat(
            "2021-03-02T12:01:01.345+00:00"
        )
        await self.observation_store.create_observation(self.observation_2, session)

        self.observation_3 = ObservationModel()
        self.observation_3.id = "2d1a7f62-b99a-4752-b92f-2dfa0e6f4202"
        self.observation_3.type_id = "OBSERVED_PREP_COMPLETE"
        self.observation_3.org_id = self.org_1.id
        self.observation_3.case_id = self.case_4.case_id
        self.observation_3.recorded_time = datetime.fromisoformat("2021-03-03T12:01:02.345+00:00")
        self.observation_3.observation_time = datetime.fromisoformat(
            "2021-03-02T12:02:02.345+00:00"
        )
        await self.observation_store.create_observation(self.observation_3, session)

        self.observation_4 = ObservationModel()
        self.observation_4.id = "2d1a7f62-b99a-4752-b92f-2dfa0e6f4203"
        self.observation_4.type_id = "OBSERVED_ANESTHESIA_READY"
        self.observation_4.org_id = self.org_1.id
        self.observation_4.case_id = self.case_4.case_id
        self.observation_4.recorded_time = datetime.fromisoformat("2021-05-03T12:01:02.345+00:00")
        self.observation_4.observation_time = datetime.fromisoformat(
            "2021-05-31T12:02:02.345+00:00"
        )
        await self.observation_store.create_observation(self.observation_4, session)

        valid_events = [
            "back_table_open",
            "patient_draped",
            "patient_undraped",
            "patient_wheels_in",
            "patient_wheels_out",
            "patient_xfer_to_bed",
            "patient_xfer_to_or_table",
            "endo_pack_open",
            "sensitive_content_end",
            "sensitive_content_start",
            "terminal_clean_end",
            "terminal_clean_start",
        ]

        # Generate a bunch of events for roll ups
        num_events = 100
        start = datetime.fromisoformat("2021-01-01T12:00:00-00:00")
        time_delta = timedelta(minutes=10)
        current = start
        for i in range(num_events):
            event = EventModel()
            event.id = str(uuid4())
            event.event_type_id = valid_events[i % len(valid_events)]
            event.source_type = "prediction"
            event.source = "component-test"
            event.model_version = "component-test"
            event.confidence = 0.8
            event.org_id = self.org_0.id
            event.site_id = self.site_0.id
            event.room_id = self.room_0.id
            event.camera_id = self.camera_0.id
            event.start_time = current
            event.process_timestamp = datetime.now()
            await self.event_store.create_event(
                session=session,
                event=event,
            )
            current = event.start_time + time_delta

        self.no_auth_client = ApiServerClient(server_url=self.server_url, no_auth=True)
        self.surgeon_client = ApiServerClient(
            server_url=self.server_url,
            jwt=make_jwt(self.server_url, self.auth0_url, user=self.surgeon_user, org=self.org_0),
        )
        self.site_0_scoped_client = ApiServerClient(
            server_url=self.server_url,
            jwt=make_jwt(
                self.server_url, self.auth0_url, user=self.site_0_scoped_user, org=self.org_0
            ),
        )
        self.user_without_permissions_client = ApiServerClient(
            server_url=self.server_url,
            jwt=make_jwt(self.server_url, self.auth0_url, self.user_without_permissions),
        )
        self.labeler_client = ApiServerClient(
            server_url=self.server_url,
            jwt=make_jwt(self.server_url, self.auth0_url, self.labeler_user),
            timeout_seconds=5,
        )
        self.label_reviewer_client = ApiServerClient(
            server_url=self.server_url,
            jwt=make_jwt(self.server_url, self.auth0_url, self.label_reviewer_user),
            timeout_seconds=5,
        )
        self.service_account_client = ApiServerClient(
            server_url=self.server_url,
            jwt=make_jwt(self.server_url, self.auth0_url, self.internal_service_account_user),
            timeout_seconds=5,
        )
        self.hospital_administrator_client = ApiServerClient(
            server_url=self.server_url,
            jwt=make_jwt(
                self.server_url, self.auth0_url, self.hospital_administrator_user, org=self.org_0
            ),
        )

        self.field_engineer_client = ApiServerClient(
            server_url=self.server_url,
            jwt=make_jwt(self.server_url, self.auth0_url, self.field_engineer_user),
        )

    async def teardown(self) -> None:
        print("deleting all sql data...")
        assert config.sql_database() == "postgres"
        assert config.sql_user() == "postgres"

        # We have a cycle in our schema. rooms.default_camera_id has an fk to cameras, and
        # cameras.room_id has an fk to rooms. The former has ON DELETE SET NULL, so we can delete
        # the cameras first (which consequently sets rooms.default_camera_id = NULL on all rows).
        # Sites and orgs are also not getting sorted properly, possibly because rooms and cameras
        # have fks to them that cause there to be a larger cycle. We can explicitly sort these as
        # well.
        explicitly_sorted_tables = ["cameras", "rooms", "sites", "organizations"]
        # Any tables to not delete data for.
        excluded_tables = [
            "event_dashboard_visibility",
            "event_types",
            "phase_types",
            "observation_types",
            "annotation_task_types",
            "case_classification_types",
            "staff_role",
        ]

        sorted_tables = list(reversed(Base.metadata.sorted_tables))

        def get_table_idx(tables: Iterable[Table], name: object) -> int:
            return next(i for i, v in enumerate(tables) if v.name == name)

        for excluded_table in excluded_tables:
            idx = get_table_idx(sorted_tables, excluded_table)
            sorted_tables.pop(idx)

        explicitly_sorted_table_objs = []
        for explicitly_sorted_table in explicitly_sorted_tables:
            idx = get_table_idx(sorted_tables, explicitly_sorted_table)
            table_obj = sorted_tables.pop(idx)
            explicitly_sorted_table_objs.append(table_obj)

        # Currently (as of 12/19/24) decodable manages table schemas for us, so we're not writing migrations for
        # these tables as part of Cloud API. However, because we're reading from them and tables can extend off the
        # `Base` class, we need to explicitly exclude these tables from being torn down as part our setup process for
        # component tests. For now, we're just blanket excluding the entire schema, however we could choose to be
        # more targeted in the future if we wanted to actually write migrations and/or write tests for some of these
        # tables in our component tests
        sorted_tables = list(
            filter(
                lambda table: (table.schema != DECODABLE_SCHEMA or table.name == "case_activity"),
                # Exclude decodable schema tables except case_activity,
                sorted_tables + explicitly_sorted_table_objs,
            )
        )
        engine = engine_provider(pool_class=StaticPool)
        async with engine.begin() as connection:
            query = select(Organization)
            await connection.execute(query)

            for table in sorted_tables:
                await connection.execute(delete(table))

            # Delete all non-excluded rows from annotation_task_types
            table = Base.metadata.tables["annotation_task_types"]
            await connection.execute(
                table.delete().where(
                    table.c.id.not_in([surgery_task_type_id, mop_task_type_id, light_task_type_id])
                )
            )
            await connection.commit()
        assert self.db_engine is not None
        await self.db_engine.dispose(close=False)

    def make_request_no_auth(
        self, method: str, url: str, data: Optional[dict[str, object]] = None
    ) -> requests.Response:
        return requests.request(method, f"{self.server_url}{url}", json=data)

    def make_request_as_user(
        self,
        method: str,
        url: str,
        user: User,
        org: Optional[Organization] = None,
        data: Optional[dict[str, object]] = None,
    ) -> requests.Response:
        jwt = make_jwt(self.server_url, self.auth0_url, user=user, org=org)
        return self.make_request_with_jwt(method, url, jwt, data)

    def make_request_with_jwt(
        self, method: str, url: str, jwt: str, data: Optional[dict[str, object]] = None
    ) -> requests.Response:
        headers = {"X-Forwarded-Authorization": f"Bearer {jwt}"}
        return requests.request(method, f"{self.server_url}{url}", headers=headers, json=data)

    async def _try_create_case_activity_table(self) -> None:
        engine = engine_provider(pool_class=StaticPool)
        async with engine.begin() as conn:
            # Create schema if not exists
            await conn.execute(text("CREATE SCHEMA IF NOT EXISTS decodable"))

            # Create just the case_activity table
            await conn.run_sync(
                lambda sync_conn: cast(Table, CaseActivityModel.__table__).create(
                    bind=sync_conn, checkfirst=True
                )
            )
