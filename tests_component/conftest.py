import os
import shutil
import subprocess
import tempfile
from asyncio import AbstractEvent<PERSON>oop
from dataclasses import dataclass
from datetime import timedel<PERSON>
from pathlib import Path
from typing import Generic, Iterator, TypeVar, Generator, AsyncGenerator

import asyncio
import asyncpg
import docker
import pytest
import pytest_asyncio
import uvloop
from _pytest.tmpdir import TempPathFactory
from asyncpg import CannotConnectNowError
from sqlalchemy import StaticPool
from sqlalchemy.ext.asyncio import AsyncSession

import mocks.mock_auth0.database
import mocks.mock_auth0.rest_app
import mocks.mock_big_query.rest_app as mock_big_query_app
import mocks.mock_media_asset_service.rest_app as media_asset_service_app
import mocks.mock_ehr_scribe_service.rest_app as patient_service_app
import mocks.mock_simple_service.rest_app as mock_simple_service_app
from databases.sql import engine_provider, get_session_maker
from mocks.mock_auth0.rest_app import MockAuth0
from mocks.mock_big_query.database import Database as MockBigQueryDatabase
from mocks.mock_media_asset_service.database import Database as MockMediaAssetServiceDatabase
from mocks.mock_ehr_scribe_service.database import Database as MockEhrScribeServiceDatabase
from mocks.mock_simple_service.database import Database as MockSimpleServiceDatabase
from tests_component import ComponentTestHarness, harness
from utils.net import allocate_port, wait_for_port


def find_project_root() -> Path:
    current = Path(os.getcwd())

    while current != Path("/"):
        if (current / ".git").is_dir():
            return current

        current = current.parent

    raise FileNotFoundError


@pytest.fixture(scope="session")
def postgres_container() -> Iterator[int]:
    client = docker.from_env()

    port = allocate_port()
    container = client.containers.run(
        "postgres@sha256:c83014a2b46834ef6d17f64c8e4a70089901a8c0dee158f1ca5ccae032ea32e5",
        ["-c", "wal_level=logical", "-c", "max-connections=200"],
        detach=True,
        remove=True,
        ports={"5432": port},
        environment={
            "POSTGRES_DB": "postgres",
            "POSTGRES_PASSWORD": "postgres",
            "POSTGRES_USER": "postgres",
        },
        volumes={
            f"{find_project_root()}/databases/sql/schema": {
                "bind": "/docker-entrypoint-initdb.d",
                "mode": "ro",
            }
        },
    )
    asyncio.run(wait_for_postgres(port))
    yield port

    container.kill()


async def wait_for_postgres(port: int) -> None:
    while True:
        try:
            connection = await asyncpg.connect(
                host="127.0.0.1",
                port=port,
                database="postgres",
                user="postgres",
                password="postgres",
                timeout=1,
            )
            await connection.close()
        except (ConnectionError, CannotConnectNowError, asyncio.TimeoutError):
            # This error is raised when the server closes the connection unexpectedly, which happens
            # on postgres container startup in between the database initialization phase and
            # bringing up the server
            pass
        except Exception as e:
            # Re-raise any other exception encountered.
            raise e
        else:
            # If no exception was encountered, the database is up!
            break


@pytest.fixture(scope="session")
def gcs_emulator_container(
    tmp_path_factory: TempPathFactory,
) -> Iterator[docker.models.containers.Container]:
    client = docker.from_env()

    # Copy some static data into the data directory for the emulator. Ideally, this would be its own
    # fixture that could be under the control of individual tests, but doing it this way preserves
    # existing behavior.
    data_dir = tmp_path_factory.mktemp("fake-gcs")
    shutil.copytree("tests_component/mock_gcs", data_dir, dirs_exist_ok=True)

    port = allocate_port()
    container = client.containers.run(
        "fsouza/fake-gcs-server:latest",
        ["-scheme", "http"],
        detach=True,
        remove=True,
        ports={"4443": port},
        volumes={data_dir: {"bind": "/data", "mode": "rw"}},
    )

    yield port

    container.kill()


@pytest.fixture(scope="session")
def redis_container() -> Iterator[int]:
    client = docker.from_env()
    port = allocate_port()
    container = client.containers.run("redis", detach=True, remove=True, ports={"6379": port})

    yield port

    container.kill()


T = TypeVar("T")


@dataclass
class MockService(Generic[T]):
    database: T
    port: int

    @property
    def url(self) -> str:
        return f"http://127.0.0.1:{self.port}"


@pytest.fixture(scope="session")
async def mock_auth0_server() -> AsyncGenerator[
    MockService[mocks.mock_auth0.database.Database], None
]:
    database = mocks.mock_auth0.database.Database()
    mock_auth0_service = MockAuth0(database)
    async with mock_auth0_service.serving() as port:
        yield MockService(database, port)


@pytest.fixture(scope="session")
async def mock_media_asset_service() -> AsyncGenerator[
    MockService[MockMediaAssetServiceDatabase], None
]:
    database = MockMediaAssetServiceDatabase()
    media_asset_service = media_asset_service_app.MockMediaAssetService(database)
    async with media_asset_service.serving() as port:
        yield MockService(database, port)


@pytest.fixture(scope="session")
async def mock_big_query() -> AsyncGenerator[MockService[MockBigQueryDatabase], None]:
    database = MockBigQueryDatabase([])
    service = mock_big_query_app.MockBigQuery(database)
    async with service.serving() as port:
        yield MockService(database, port)


@pytest.fixture(scope="session")
async def mock_ehr_scribe_service() -> AsyncGenerator[
    MockService[MockEhrScribeServiceDatabase], None
]:
    database = MockEhrScribeServiceDatabase()
    service = patient_service_app.MockEhrScribeService(database)
    async with service.serving() as port:
        yield MockService(database, port)


@pytest.fixture(scope="session")
async def mock_turnover_model_service() -> AsyncGenerator[
    MockService[MockSimpleServiceDatabase], None
]:
    database = MockSimpleServiceDatabase()
    service = mock_simple_service_app.MockSimpleService(database)
    async with service.serving() as port:
        yield MockService(database, port)


@pytest.fixture(scope="session")
def prometheus_port() -> int:
    return allocate_port()


@pytest.fixture(scope="session")
def api_server_log() -> Iterator[str]:
    with tempfile.NamedTemporaryFile(prefix="api-server-", suffix=".log") as logfile:
        yield logfile.name


@pytest.fixture(scope="session")
def api_server(
    api_server_log: str,
    mock_auth0_server: MockService[mocks.mock_auth0.database.Database],
    mock_media_asset_service: MockService[MockMediaAssetServiceDatabase],
    mock_big_query: MockService[MockBigQueryDatabase],
    mock_ehr_scribe_service: MockService[MockEhrScribeServiceDatabase],
    mock_turnover_model_service: MockService[MockSimpleServiceDatabase],
    tmp_path_factory: TempPathFactory,
    gcs_emulator_container: int,
    redis_container: int,
    postgres_container: int,
    prometheus_port: int,
) -> Iterator[int]:
    """Creates an instance of the API server running under hypercorn and returns its port"""
    for port in [gcs_emulator_container, redis_container]:
        wait_for_port("127.0.0.1", port)
    project_root = find_project_root()

    port = allocate_port()

    # Run in a clean environment. The only thing we inherit is PATH so that rest app runs in the
    # correct Python virtualenv.
    inherited_environment = {"PATH": os.environ["PATH"]}
    environment = inherited_environment | {
        "API_GATEWAY_URL": f"http://127.0.0.1:{port}",
        "AUTH0_DEFAULT_DOMAIN": mock_auth0_server.url,
        "AUTH0_URL": mock_auth0_server.url,
        "CASE_DURATION_TURNOVER_ENDPOINT": mock_turnover_model_service.url,
        "EHR_SCRIBE_ENDPOINT": mock_ehr_scribe_service.url,
        "USE_EHR_SCRIBE_PATIENT_DATA": "True",
        "FLUSH_LOGS_IMMEDIATELY": "True",
        "GCP_PROJECT": "component-test",
        "GOOGLE_APPLICATION_CREDENTIALS": "component-test-sa-key.json",
        "MEDIA_ASSET_SERVICE_ENDPOINT": mock_media_asset_service.url,
        "MOCK_BIG_QUERY_ENDPOINT": mock_big_query.url,
        "MOCK_PUB_SUB": "True",
        "MOCK_SECRET_STORE": "True",
        "OBJC_DISABLE_INITIALIZE_FORK_SAFETY": "YES",
        "PORT": str(port),
        "PROMETHEUS_PORT": str(prometheus_port),
        "PROMETHEUS_MULTIPROC_DIR": str(tmp_path_factory.mktemp("metrics_dir")),
        "REDIS_HOST": "localhost",
        "REDIS_PORT": str(redis_container),
        "SQL_DATABASE": "postgres",
        "SQL_HOST": "localhost",
        "SQL_PASSWORD": "postgres",
        "SQL_PORT": str(postgres_container),
        "SQL_USER": "postgres",
        "STORAGE_EMULATOR_HOST": f"http://localhost:{gcs_emulator_container}",
        "WEB_SERVER_ERROR_LOG_FILE": api_server_log,
        "WEB_SERVER_WORKER_COUNT": "4",
    }

    with subprocess.Popen(
        ["python", "main.py"],
        cwd=project_root,
        env=environment,
    ) as proc:
        try:
            wait_for_port("127.0.0.1", port, timedelta(seconds=40))
            yield port
        finally:
            proc.terminate()


@pytest.fixture(autouse=True)
def component_test_harness(
    api_server: int,
    api_server_log: str,
    mock_auth0_server: MockService[mocks.mock_auth0.database.Database],
    mock_big_query: MockService[MockBigQueryDatabase],
    mock_media_asset_service: MockService[MockMediaAssetServiceDatabase],
    postgres_container: int,
) -> Iterator[ComponentTestHarness]:
    """Fixture to execute before and after a test is run"""
    harness.init_stores(postgres_container)

    # Delete all the old data
    mock_auth0_server.database.clear()
    mock_media_asset_service.database.clear()
    """
    All function calls below should be marked await and the fixture would need to be marked async.
    However, that creates a lot of cascading changes. In order to limit this, all harness calls are
    encapsulated by `asyncio.run`.
    """
    # TODO: Migrate component_test_harness to async
    if harness.db_engine is None:
        raise Exception("Harness MUST have a db_engine")
    asyncio.run(harness.teardown())
    # Populate the databases
    asyncio.run(
        harness.setup(
            api_server_port=api_server,
            auth0_url=mock_auth0_server.url,
            mock_auth0_database=mock_auth0_server.database,
            mock_big_query_database=mock_big_query.database,
        )
    )
    # Clear the API server log by opening it for write
    print(f"API Server Logs: {api_server_log}")
    with open(api_server_log, "w"):
        pass

    yield harness  # this is where the testing and fun begins

    # Print the logs
    print("===== Start API Server Logs ====")
    with open(api_server_log) as f:
        print(f.read())
    print("===== End API Server Logs ====")

    # Delete the data from this test
    asyncio.run(harness.teardown())


@pytest_asyncio.fixture(scope="session")
def event_loop() -> Generator[AbstractEventLoop, None, None]:
    # We set nesting of this async thread here because we do nested direct session calls
    # in our component tests to service/store objects instead of using clients.
    # TODO: Remove once we migrate tests to using clients instead of service objects directly.
    uvloop.install()
    asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture()
async def async_session() -> AsyncGenerator[AsyncSession, None]:
    engine = engine_provider(pool_class=StaticPool)
    session_maker = get_session_maker(engine)
    async with session_maker() as async_session:
        yield async_session
        await engine.dispose()
