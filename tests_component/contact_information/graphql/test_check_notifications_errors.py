import datetime
import uuid
from typing import Optional

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_input_schema import GQLStaffEventsNotificationsInput
from api_server.services.case.case_staff_store import CaseStaffModel
from api_server.services.contact_information.contact_information_store import (
    ContactInformationModel,
    ContactInformationType,
)
from api_server.services.contact_information.staff_event_notification_contact_information_store import (
    StaffEventNotificationContactInformationModel,
    StaffEventNotificationModel,
    SentNotificationStatus,
    EventNotificationQueryDto,
)
from api_server.services.events.event_store import EventModel
from api_server.services.phases.phase_store import PhaseModel, PhaseStatus
from tests_component import harness
from tests_component.contact_information.graphql.mock_helpers import mock_staff


def mock_event(
    event_id: str,
    start_time: datetime.datetime,
    org_id: str,
    site_id: str,
    room_id: str,
    event_type_id: str = "patient_wheels_in",
    source_type: str = "prediction",
    created_time: datetime.datetime = datetime.datetime.now(),
    confidence: float = 0.5,
    deleted_at: Optional[datetime.datetime] = None,
) -> EventModel:
    event = EventModel()
    event.id = event_id
    event.created_time = created_time
    event.event_type_id = event_type_id
    event.start_time = start_time
    event.process_timestamp = start_time
    event.source = "test"
    event.source_type = source_type
    event.confidence = confidence
    event.org_id = org_id
    event.site_id = site_id
    event.room_id = room_id
    event.deleted_at = deleted_at
    return event


def mock_phase(
    start_event: EventModel,
    case_id: Optional[str] = None,
    type_id: str = "CASE",
    end_event: Optional[EventModel] = None,
    source_type: str = "unified",
) -> PhaseModel:
    phase = PhaseModel()
    phase.start_event_id = start_event.id
    phase.type_id = type_id
    if end_event:
        phase.end_event_id = end_event.id
    phase.case_id = case_id
    phase.source_type = source_type
    phase.org_id = start_event.org_id
    phase.site_id = start_event.site_id
    phase.room_id = start_event.room_id
    phase.status = PhaseStatus.VALID
    return phase


def mock_contact_information(
    contact_information_value: str,
    contact_information_type: ContactInformationType = ContactInformationType.PHONE_NUMBER,
) -> ContactInformationModel:
    contact_info = ContactInformationModel()
    contact_info.id = uuid.uuid4()
    contact_info.contact_information_value = contact_information_value
    contact_info.type = contact_information_type
    return contact_info


def mock_staff_contact_information(
    contact_information_id: uuid.UUID,
    staff_id: uuid.UUID,
    event_type_id: str = "patient_wheels_in",
    archived_time: Optional[datetime.datetime] = None,
) -> StaffEventNotificationContactInformationModel:
    staff_contact_information = StaffEventNotificationContactInformationModel()
    staff_contact_information.contact_information_id = contact_information_id
    staff_contact_information.staff_id = staff_id
    staff_contact_information.event_type_id = event_type_id
    staff_contact_information.id = uuid.uuid4()
    if archived_time is not None:
        staff_contact_information.archived_time = archived_time
    return staff_contact_information


def mock_case_staff(
    staff_id: uuid.UUID,
    case_id: str,
) -> CaseStaffModel:
    case_staff = CaseStaffModel()
    case_staff.staff_id = staff_id
    case_staff.case_id = case_id
    case_staff.role = "Primary"
    return case_staff


def mock_staff_event_notification(
    staff_event_notification_contact_information_id: uuid.UUID,
    event_id: str,
    event_time: datetime.datetime,
    case_id: str,
    sent_status: SentNotificationStatus = SentNotificationStatus.SENT,
) -> StaffEventNotificationModel:
    staff_event_notification = StaffEventNotificationModel()
    staff_event_notification.message_id = "aaa"
    staff_event_notification.staff_event_contact_information_id = (
        staff_event_notification_contact_information_id
    )
    staff_event_notification.event_id = event_id
    staff_event_notification.event_time = event_time
    staff_event_notification.case_id = case_id
    staff_event_notification.sent_status = sent_status
    return staff_event_notification


# TODO: Check that the errors are persisted to the database
class TestCheckNotificationsErrors:
    @pytest.mark.asyncio
    async def test_check_for_events_no_events_component(self) -> None:
        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.check_notifications_errors.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_1.id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.CheckNotificationsErrors.success,
            apella_schema.CheckNotificationsErrors.missing_notifications.select(
                apella_schema.MissingNotification.event.select(apella_schema.Event.id),
            ),
            apella_schema.CheckNotificationsErrors.excess_notifications.select(
                apella_schema.EventNotification.event.select(apella_schema.Event.id),
            ),
            apella_schema.CheckNotificationsErrors.duplicate_notifications.select(
                apella_schema.EventNotification.event.select(apella_schema.Event.id),
            ),
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.check_notifications_errors.success is True
        assert len(result.check_notifications_errors.missing_notifications) == 0
        assert len(result.check_notifications_errors.excess_notifications) == 0
        assert len(result.check_notifications_errors.duplicate_notifications) == 0

    @pytest.mark.asyncio
    async def test_check_for_one_event_missing(self, async_session: AsyncSession) -> None:
        """Test that when we are missing an event, we don't send the corresponding notification
        and later on, when the event is found, we do trigger the notificaton
        """
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 6, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events(
            events=[mock_event_1],
            session=async_session,
        )
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_0.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="1234567890",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.check_notifications_errors.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 2, 0, 0, 0, tzinfo=datetime.timezone.utc),
                time_window_to_search=datetime.timedelta(days=1),
            )
        ).select(
            apella_schema.CheckNotificationsErrors.success,
            apella_schema.CheckNotificationsErrors.missing_notifications.select(
                apella_schema.MissingNotification.event.select(apella_schema.Event.id),
            ),
            apella_schema.CheckNotificationsErrors.excess_notifications.select(
                apella_schema.EventNotification.event.select(apella_schema.Event.id),
            ),
            apella_schema.CheckNotificationsErrors.duplicate_notifications.select(
                apella_schema.EventNotification.event.select(apella_schema.Event.id),
            ),
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.check_notifications_errors.success is True
        assert len(result.check_notifications_errors.missing_notifications) == 1
        assert len(result.check_notifications_errors.excess_notifications) == 0
        assert len(result.check_notifications_errors.duplicate_notifications) == 0

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.check_notifications_errors.success is True
        assert len(result.check_notifications_errors.missing_notifications) == 1
        assert len(result.check_notifications_errors.excess_notifications) == 0
        assert len(result.check_notifications_errors.duplicate_notifications) == 0

        # now we check the results in the db directly (in contrast to checking the result
        # coming from mutation)
        results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            request=EventNotificationQueryDto(
                case_ids=[mock_case_staff_1.case_id],
                event_ids=[mock_event_1.id],
                staff_ids=[str(mock_staff_1.id)],
            ),
        )

        assert len(results) == 1
        staff_event_result = results[0]
        assert staff_event_result.duplicated_id is None
        assert staff_event_result.is_excess is False
        assert staff_event_result.attempts == 0
        assert staff_event_result.sent_status == SentNotificationStatus.MISSED
        assert staff_event_result.version == 1

        notify_mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 1, 6, 10, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )
        notify_results = harness.service_account_client.mutate_graphql_from_schema(notify_mutation)
        assert notify_results.notify_staff_for_events.success is True
        assert notify_results.notify_staff_for_events.sent_count == 1

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.check_notifications_errors.success is True
        assert len(result.check_notifications_errors.missing_notifications) == 0
        assert len(result.check_notifications_errors.excess_notifications) == 0
        assert len(result.check_notifications_errors.duplicate_notifications) == 0

    @pytest.mark.asyncio
    async def test_check_for_one_event_missing_doesn_t_mark_excess(
        self, async_session: AsyncSession
    ) -> None:
        """Test that when we have a missed notification, we don't mark it as excess"""
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 6, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events(
            events=[mock_event_1],
            session=async_session,
        )
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_1.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="1234567890",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            contact_infos=[mock_contact_information_1],
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff(
            case_staff=[mock_case_staff_1],
        )

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            staff_event_notification_contact_information=[mock_staff_contact_information_1],
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            staff_event_notifications=[
                StaffEventNotificationModel(
                    staff_event_contact_information_id=mock_staff_contact_information_1.id,
                    event_id=mock_event_1.id,
                    event_time=mock_event_1.start_time,
                    case_id=harness.case_0.case_id,
                    message_id="missed",
                    created_time=datetime.datetime(
                        2021, 1, 1, 6, 10, 0, tzinfo=datetime.timezone.utc
                    ),
                    attempts=0,
                    sent_status=SentNotificationStatus.MISSED.name,
                )
            ],
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.check_notifications_errors.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 2, 0, 0, 0, tzinfo=datetime.timezone.utc),
                time_window_to_search=datetime.timedelta(days=1),
            )
        ).select(
            apella_schema.CheckNotificationsErrors.success,
            apella_schema.CheckNotificationsErrors.missing_notifications.select(
                apella_schema.MissingNotification.event.select(apella_schema.Event.id),
            ),
            apella_schema.CheckNotificationsErrors.excess_notifications.select(
                apella_schema.EventNotification.event.select(apella_schema.Event.id),
            ),
            apella_schema.CheckNotificationsErrors.duplicate_notifications.select(
                apella_schema.EventNotification.event.select(apella_schema.Event.id),
            ),
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.check_notifications_errors.success is True
        assert len(result.check_notifications_errors.missing_notifications) == 0
        assert len(result.check_notifications_errors.excess_notifications) == 0
        assert len(result.check_notifications_errors.duplicate_notifications) == 0

        # now we check the results in the db directly (in contrast to checking the result
        # coming from mutation)
        results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            request=EventNotificationQueryDto(
                case_ids=[mock_case_staff_1.case_id],
                event_ids=[mock_event_1.id],
                staff_ids=[str(mock_staff_1.id)],
            ),
        )

        staff_event_result = results[0]
        assert len(results) == 1
        assert staff_event_result.duplicated_id is None
        assert staff_event_result.is_excess is False
        assert staff_event_result.attempts == 0
        assert staff_event_result.sent_status == SentNotificationStatus.MISSED

    @pytest.mark.asyncio
    async def test_check_for_one_event_missing_doesn_t_mark_missed(
        self, async_session: AsyncSession
    ) -> None:
        """Test that when we have a missed notification, we don't mark it as excess"""
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 6, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events(
            events=[mock_event_1],
            session=async_session,
        )
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_0.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="1234567890",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            contact_infos=[mock_contact_information_1],
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff(
            case_staff=[mock_case_staff_1],
        )

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            [
                StaffEventNotificationModel(
                    staff_event_contact_information_id=mock_staff_contact_information_1.id,
                    event_id=mock_event_1.id,
                    event_time=mock_event_1.start_time,
                    case_id=harness.case_0.case_id,
                    message_id="failed",
                    created_time=datetime.datetime(
                        2021, 1, 1, 6, 10, 0, tzinfo=datetime.timezone.utc
                    ),
                    attempts=1,
                    sent_status=SentNotificationStatus.FAILED.name,
                )
            ]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.check_notifications_errors.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 2, 0, 0, 0, tzinfo=datetime.timezone.utc),
                time_window_to_search=datetime.timedelta(days=1),
            )
        ).select(
            apella_schema.CheckNotificationsErrors.success,
            apella_schema.CheckNotificationsErrors.missing_notifications.select(
                apella_schema.MissingNotification.event.select(apella_schema.Event.id),
            ),
            apella_schema.CheckNotificationsErrors.excess_notifications.select(
                apella_schema.EventNotification.event.select(apella_schema.Event.id),
            ),
            apella_schema.CheckNotificationsErrors.duplicate_notifications.select(
                apella_schema.EventNotification.event.select(apella_schema.Event.id),
            ),
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.check_notifications_errors.success is True
        assert len(result.check_notifications_errors.missing_notifications) == 1
        assert len(result.check_notifications_errors.excess_notifications) == 0
        assert len(result.check_notifications_errors.duplicate_notifications) == 0

        # now we check the results in the db directly (in contrast to checking the result
        # coming from mutation)
        results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                case_ids=[mock_case_staff_1.case_id],
                event_ids=[mock_event_1.id],
                staff_ids=[str(mock_staff_1.id)],
            )
        )
        assert len(results) == 1
        event_result = results[0]
        assert event_result.duplicated_id is None
        assert event_result.is_excess is False
        assert event_result.attempts == 1
        assert event_result.sent_status == SentNotificationStatus.FAILED

    @pytest.mark.asyncio
    async def test_check_for_excess_events_one_per(self, async_session: AsyncSession) -> None:
        """Test that when we have a missed notification, we don't mark it as excess"""
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 6, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events(
            events=[mock_event_1],
            session=async_session,
        )
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_1.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="1234567890",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            contact_infos=[mock_contact_information_1],
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff(
            case_staff=[mock_case_staff_1],
        )

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            staff_event_notification_contact_information=[mock_staff_contact_information_1],
        )
        notification_id = uuid.uuid4()
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            staff_event_notifications=[
                StaffEventNotificationModel(
                    id=notification_id,
                    staff_event_contact_information_id=mock_staff_contact_information_1.id,
                    event_id=mock_event_1.id,
                    event_time=mock_event_1.start_time,
                    case_id=harness.case_0.case_id,
                    message_id="message_1_id",
                    created_time=datetime.datetime(
                        2021, 1, 1, 6, 10, 0, tzinfo=datetime.timezone.utc
                    ),
                    attempts=1,
                    sent_status=SentNotificationStatus.SENT.name,
                )
            ],
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            staff_event_notifications=[
                StaffEventNotificationModel(
                    id=notification_id,
                    staff_event_contact_information_id=mock_staff_contact_information_1.id,
                    event_id=mock_event_1.id,
                    event_time=mock_event_1.start_time,
                    case_id=harness.case_0.case_id,
                    message_id="message_1_id",
                    created_time=datetime.datetime(
                        2021, 1, 1, 6, 10, 0, tzinfo=datetime.timezone.utc
                    ),
                    attempts=2,
                    sent_status=SentNotificationStatus.SENT.name,
                )
            ],
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.check_notifications_errors.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 2, 0, 0, 0, tzinfo=datetime.timezone.utc),
                time_window_to_search=datetime.timedelta(days=1),
            )
        ).select(
            apella_schema.CheckNotificationsErrors.success,
            apella_schema.CheckNotificationsErrors.missing_notifications.select(
                apella_schema.MissingNotification.event.select(apella_schema.Event.id),
            ),
            apella_schema.CheckNotificationsErrors.excess_notifications.select(
                apella_schema.EventNotification.event.select(apella_schema.Event.id),
            ),
            apella_schema.CheckNotificationsErrors.duplicate_notifications.select(
                apella_schema.EventNotification.event.select(apella_schema.Event.id),
            ),
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.check_notifications_errors.success is True
        assert len(result.check_notifications_errors.missing_notifications) == 0
        assert len(result.check_notifications_errors.excess_notifications) == 1
        assert len(result.check_notifications_errors.duplicate_notifications) == 0

        # now we check the results in the db directly (in contrast to checking the result
        # coming from mutation)
        results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            request=EventNotificationQueryDto(
                case_ids=[mock_case_staff_1.case_id],
                event_ids=[mock_event_1.id],
                staff_ids=[str(mock_staff_1.id)],
            ),
        )

        staff_event_result = results[0]
        assert len(results) == 1
        assert staff_event_result.duplicated_id is None
        assert staff_event_result.is_excess is True
        assert staff_event_result.attempts == 2
        assert staff_event_result.sent_status == SentNotificationStatus.SENT

    @pytest.mark.asyncio
    async def test_check_for_one_event_excess(self, async_session: AsyncSession) -> None:
        """Test that when case matching fails and we have an event that corresponds
        to a different case (case_1) than the one we are notifying from (case_0), we label the
        notification we already sent as excess

        In this test, initially phase 1 was matched to case 1, after some other info it arrives
        we rematched the phase to case 0 and therefore the original notification is now an
        erroneous one
        """
        # not that this mocked_event, is for case_1
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 6, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events(
            events=[mock_event_1],
            session=async_session,
        )
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_1.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="1234567890",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            contact_infos=[mock_contact_information_1],
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        # not that this mocked case staff, is for case_0
        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff(
            case_staff=[mock_case_staff_1],
        )

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )
        staff_event_contact_information_list = await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            staff_event_notification_contact_information=[mock_staff_contact_information_1],
        )
        staff_event_contact_information_1 = staff_event_contact_information_list[0]
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            staff_event_notifications=[
                StaffEventNotificationModel(
                    staff_event_contact_information_id=staff_event_contact_information_1.id,
                    event_id=mock_event_1.id,
                    event_time=mock_event_1.start_time,
                    case_id=harness.case_0.case_id,
                    message_id="message_id_1",
                    created_time=datetime.datetime(
                        2021, 1, 1, 6, 10, 0, tzinfo=datetime.timezone.utc
                    ),
                    attempts=1,
                )
            ],
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.check_notifications_errors.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 2, 0, 0, 0, tzinfo=datetime.timezone.utc),
                time_window_to_search=datetime.timedelta(days=1),
            )
        ).select(
            apella_schema.CheckNotificationsErrors.success,
            apella_schema.CheckNotificationsErrors.missing_notifications.select(
                apella_schema.MissingNotification.event.select(apella_schema.Event.id),
            ),
            apella_schema.CheckNotificationsErrors.excess_notifications.select(
                apella_schema.EventNotification.event.select(apella_schema.Event.id),
            ),
            apella_schema.CheckNotificationsErrors.duplicate_notifications.select(
                apella_schema.EventNotification.event.select(apella_schema.Event.id),
            ),
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.check_notifications_errors.success is True
        assert len(result.check_notifications_errors.missing_notifications) == 0
        assert len(result.check_notifications_errors.excess_notifications) == 1
        assert len(result.check_notifications_errors.duplicate_notifications) == 0

        # now we check the results in the db directly (in contrast to checking the result
        # coming from mutation)
        results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            request=EventNotificationQueryDto(
                case_ids=[mock_case_staff_1.case_id],
                event_ids=[mock_event_1.id],
                staff_ids=[str(mock_staff_1.id)],
            ),
        )

        assert len(results) == 1
        staff_event_result = results[0]
        assert staff_event_result.is_excess is True
        assert staff_event_result.attempts == 1
        assert staff_event_result.sent_status == SentNotificationStatus.SENT
        assert staff_event_result.duplicated_id is None

    @pytest.mark.asyncio
    async def test_check_excess_skips_when_mappings_missing(
        self, async_session: AsyncSession
    ) -> None:
        """Test that excess notification is skipped when staff_contact_information,
        staff_model, or contact_information_model is missing.
        """
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 6, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events(events=[mock_event_1], session=async_session)
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_1.case_id)
        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="1234567890",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            contact_infos=[mock_contact_information_1],
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        # not that this mocked case staff, is for case_0
        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff(
            case_staff=[mock_case_staff_1],
        )

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            archived_time=datetime.datetime(2021, 1, 1, 6, 10, 0, tzinfo=datetime.timezone.utc),
        )

        staff_event_contact_information = (
            await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
                [mock_staff_contact_information_1]
            )
        )[0]

        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            [
                StaffEventNotificationModel(
                    staff_event_contact_information_id=staff_event_contact_information.id,
                    event_id=mock_event_1.id,
                    event_time=mock_event_1.start_time,
                    case_id=harness.case_0.case_id,
                    message_id="message_id_1",
                    created_time=datetime.datetime(
                        2021, 1, 1, 6, 10, 0, tzinfo=datetime.timezone.utc
                    ),
                    attempts=1,
                )
            ]
        )

        mutation = (
            ApellaSchema()
            .Mutation.check_notifications_errors.args(
                input=GQLStaffEventsNotificationsInput(
                    site_id=harness.site_0.id,
                    time_to_check=datetime.datetime(
                        2021, 1, 2, 0, 0, 0, tzinfo=datetime.timezone.utc
                    ),
                    time_window_to_search=datetime.timedelta(days=1),
                )
            )
            .select(
                ApellaSchema().CheckNotificationsErrors.success,
                ApellaSchema().CheckNotificationsErrors.excess_notifications.select(
                    ApellaSchema().EventNotification.event.select(ApellaSchema().Event.id)
                ),
            )
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)

        assert result.check_notifications_errors.success is True
        assert len(result.check_notifications_errors.excess_notifications) == 0

        db_results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            request=EventNotificationQueryDto(
                case_ids=[harness.case_0.case_id],
                event_ids=[mock_event_1.id],
            ),
        )

        assert len(db_results) == 1
        assert db_results[0].is_excess is False
        assert db_results[0].sent_status == SentNotificationStatus.SENT

    @pytest.mark.asyncio
    async def test_check_for_one_event_duplicate(self, async_session: AsyncSession) -> None:
        """
        Test that when we send two notifications for the same notification_id,
        the 2nd is labeled as `duplicate`.

        In this test, we assume that we get 2 events of the same type for the same case
        """
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 6, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events(
            events=[mock_event_1],
            session=async_session,
        )
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_0.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="1234567890",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            contact_infos=[mock_contact_information_1],
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff(
            case_staff=[mock_case_staff_1],
        )

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )
        staff_event_contact_information_list = await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            staff_event_notification_contact_information=[mock_staff_contact_information_1],
        )
        staff_event_contact_information_1 = staff_event_contact_information_list[0]
        upserted_notification_list = await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            staff_event_notifications=[
                StaffEventNotificationModel(
                    staff_event_contact_information_id=staff_event_contact_information_1.id,
                    event_id=mock_event_1.id,
                    event_time=mock_event_1.start_time,
                    case_id=harness.case_0.case_id,
                    message_id="message_id_1",
                    created_time=datetime.datetime(
                        2021, 1, 1, 6, 10, 0, tzinfo=datetime.timezone.utc
                    ),
                )
            ],
        )
        upserted_notification = upserted_notification_list[0]
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            staff_event_notifications=[
                StaffEventNotificationModel(
                    id=upserted_notification.id,
                    staff_event_contact_information_id=staff_event_contact_information_1.id,
                    event_id=mock_event_1.id,
                    event_time=mock_event_1.start_time,
                    case_id=harness.case_0.case_id,
                    message_id="message_id_2",
                    sent_status=SentNotificationStatus.SENT.name,
                    created_time=datetime.datetime(
                        2021, 1, 1, 6, 21, 0, tzinfo=datetime.timezone.utc
                    ),
                    duplicated_id=upserted_notification.id,
                )
            ],
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.check_notifications_errors.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 2, 0, 0, 0, tzinfo=datetime.timezone.utc),
                time_window_to_search=datetime.timedelta(days=1),
            )
        ).select(
            apella_schema.CheckNotificationsErrors.success,
            apella_schema.CheckNotificationsErrors.missing_notifications.select(
                apella_schema.MissingNotification.event.select(apella_schema.Event.id),
            ),
            apella_schema.CheckNotificationsErrors.excess_notifications.select(
                apella_schema.EventNotification.event.select(apella_schema.Event.id),
            ),
            apella_schema.CheckNotificationsErrors.duplicate_notifications.select(
                apella_schema.EventNotification.event.select(apella_schema.Event.id),
            ),
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.check_notifications_errors.success is True
        assert len(result.check_notifications_errors.missing_notifications) == 0
        assert len(result.check_notifications_errors.excess_notifications) == 0
        assert len(result.check_notifications_errors.duplicate_notifications) == 1

        # now we check the results in the db directly (in contrast to checking the result
        # coming from mutation)
        results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            request=EventNotificationQueryDto(
                case_ids=[mock_case_staff_1.case_id],
                event_ids=[mock_event_1.id],
                staff_ids=[str(mock_staff_1.id)],
            ),
        )

        assert len(results) == 1
        staff_event_result = results[0]
        assert staff_event_result.is_excess is False
        assert staff_event_result.attempts == 1
        assert staff_event_result.sent_status == SentNotificationStatus.SENT
        assert staff_event_result.duplicated_id == upserted_notification.id

    @pytest.mark.asyncio
    async def test_check_only_checks_for_given_site(self, async_session: AsyncSession) -> None:
        """
        We want to ensure that the check_notifications_errors only checks for the given site
        """
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 6, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events(
            events=[mock_event_1],
            session=async_session,
        )
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_0.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="1234567890",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            contact_infos=[mock_contact_information_1],
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff(
            case_staff=[mock_case_staff_1],
        )

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            staff_event_notification_contact_information=[mock_staff_contact_information_1],
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.check_notifications_errors.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_1.id,
                time_to_check=datetime.datetime(2021, 1, 2, 0, 0, 0, tzinfo=datetime.timezone.utc),
                time_window_to_search=datetime.timedelta(days=1),
            )
        ).select(
            apella_schema.CheckNotificationsErrors.success,
            apella_schema.CheckNotificationsErrors.missing_notifications.select(
                apella_schema.MissingNotification.event.select(apella_schema.Event.id),
            ),
            apella_schema.CheckNotificationsErrors.excess_notifications.select(
                apella_schema.EventNotification.event.select(apella_schema.Event.id),
            ),
            apella_schema.CheckNotificationsErrors.duplicate_notifications.select(
                apella_schema.EventNotification.event.select(apella_schema.Event.id),
            ),
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.check_notifications_errors.success is True
        assert len(result.check_notifications_errors.missing_notifications) == 0
        assert len(result.check_notifications_errors.excess_notifications) == 0
        assert len(result.check_notifications_errors.duplicate_notifications) == 0

        # now we check the results in the db directly (in contrast to checking the result
        # coming from mutation)
        results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            request=EventNotificationQueryDto(
                case_ids=[mock_case_staff_1.case_id],
                event_ids=[mock_event_1.id],
                staff_ids=[str(mock_staff_1.id)],
            ),
        )
        assert len(results) == 0

        notify_mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_1.id,
                time_to_check=datetime.datetime(2021, 1, 1, 6, 10, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )
        notify_results = harness.service_account_client.mutate_graphql_from_schema(notify_mutation)
        assert notify_results.notify_staff_for_events.success is True
        assert notify_results.notify_staff_for_events.sent_count == 0

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.check_notifications_errors.success is True
        assert len(result.check_notifications_errors.missing_notifications) == 0
        assert len(result.check_notifications_errors.excess_notifications) == 0
        assert len(result.check_notifications_errors.duplicate_notifications) == 0
