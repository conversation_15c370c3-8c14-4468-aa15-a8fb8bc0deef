import datetime
from typing import Optional, Callable

import pytest

from apella_cloud_api.api_server_schema import (
    StaffEventNotificationContactInformationSearchInput,
)
from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_client_schema import GQLStaff
from apella_cloud_api.new_input_schema import (
    GQLEventNotficationQueryInput,
    GQLStaffQueryInput,
)
from api_server.services.contact_information.staff_event_notification_contact_information_store import (
    StaffEventNotificationModel,
)
from tests_component import harness
from tests_component.contact_information.graphql.mock_helpers import (
    mock_staff,
    mock_subscriber_input,
    mock_subscription,
)
from utils.helpers import convert_str_to_uuid


def create_notification_sent_input(
    message_id: str,
    contact_information_id: str,
    event_id: str,
    event_time: datetime.datetime,
    case_id: Optional[str] = None,
) -> StaffEventNotificationModel:
    notification_sent = StaffEventNotificationModel()
    notification_sent.message_id = message_id
    notification_sent.staff_event_contact_information_id = convert_str_to_uuid(
        contact_information_id
    )
    notification_sent.event_id = event_id
    notification_sent.event_time = event_time
    if case_id:
        notification_sent.case_id = case_id

    return notification_sent


def query_for_message_sent(event_ids: Optional[list[str]] = None) -> list[GQLStaff]:
    client = harness.service_account_client
    apella_schema = ApellaSchema()

    event_notifications_search_input = {}
    if event_ids:
        event_notifications_search_input["event_ids"] = event_ids
    staff_query_input = GQLStaffQueryInput(
        org_id=harness.org_0.id,
    )

    query = apella_schema.Query.staff.args(query=staff_query_input).select(
        apella_schema.StaffConnection.edges.select(
            apella_schema.StaffEdge.node.select(
                apella_schema.Staff.name,
                apella_schema.Staff.id,
                apella_schema.Staff.staff_event_notification_contact_information.select(
                    apella_schema.StaffEventNotificationContactInformationConnection.edges.select(
                        apella_schema.StaffEventNotificationContactInformationEdge.node.select(
                            apella_schema.StaffEventNotificationContactInformation.event_notifications.args(
                                **event_notifications_search_input
                            ).select(
                                apella_schema.EventNotification.message_id,
                                apella_schema.EventNotification.case.select(
                                    apella_schema.ScheduledCase.id,
                                ),
                                apella_schema.EventNotification.event_time,
                                apella_schema.EventNotification.created_time,
                            )
                        )
                    )
                ),
            )
        )
    )
    results = client.query_graphql_from_schema(query)

    return [staff.node for staff in results.staff.edges]


def query_for_staff_message_sent(
    case_ids: Optional[list[str]] = None, event_type_ids: Optional[list[str]] = None
) -> list[GQLStaff]:
    client = harness.service_account_client
    apella_schema = ApellaSchema()

    event_notifications_search_input = {}
    if case_ids:
        event_notifications_search_input["query"] = GQLEventNotficationQueryInput(case_ids=case_ids)

    if event_type_ids:
        event_notifications_search_input["query"] = GQLEventNotficationQueryInput(
            event_type_ids=event_type_ids
        )

    staff_query_input = GQLStaffQueryInput(
        org_id=harness.org_0.id,
    )

    query = apella_schema.Query.staff.args(query=staff_query_input).select(
        apella_schema.StaffConnection.edges.select(
            apella_schema.StaffEdge.node.select(
                apella_schema.Staff.name,
                apella_schema.Staff.id,
                apella_schema.Staff.staff_event_notification_contact_information.select(
                    apella_schema.StaffEventNotificationContactInformationConnection.edges.select(
                        apella_schema.StaffEventNotificationContactInformationEdge.node.select(
                            apella_schema.StaffEventNotificationContactInformation.staff_event_notifications.args(
                                **event_notifications_search_input
                            ).select(
                                apella_schema.EventNotification.message_id,
                                apella_schema.EventNotification.case.select(
                                    apella_schema.ScheduledCase.id,
                                ),
                            )
                        )
                    )
                ),
            )
        )
    )
    results = client.query_graphql_from_schema(query)

    return [staff.node for staff in results.staff.edges]


class TestNotificationsSent:
    @pytest.fixture(autouse=True)
    def setup_upsert_subscribers(self) -> None:
        """
        Since this test class is only meant to test querying, we can insert a base set of data
        to test against.
        """
        self.staff_1 = mock_staff("staff_1")

        self.contact_information_1 = "0987654321"

        contact_information_1_upsert_input = mock_subscriber_input(
            contact_information_value=self.contact_information_1,
            first_name="Johnny",
            subscriptions=[
                mock_subscription(
                    staff_id=self.staff_1.id,
                )
            ],
        )
        apella_schema = ApellaSchema()
        subscriber_mutation = apella_schema.Mutation.subscribers_upsert.args(
            input=[contact_information_1_upsert_input]
        ).select(apella_schema.SubscribersUpsert.success)
        harness.service_account_client.mutate_graphql_from_schema(subscriber_mutation)

    @pytest.mark.parametrize(
        "get_event_ids_to_search,expected_count",
        [(None, 1), (lambda: [harness.event_3.id], 1), (lambda: ["donkey"], 0)],
    )
    @pytest.mark.asyncio
    async def test_create_notifications_sent(
        self, get_event_ids_to_search: Optional[Callable[[], list[str]]], expected_count: int
    ) -> None:
        event_ids_to_search = get_event_ids_to_search() if get_event_ids_to_search else None

        contact_information_search = StaffEventNotificationContactInformationSearchInput()
        contact_information_search.staff_ids = [
            str(self.staff_1.id),
        ]

        staff_event_notification_contact_informations = (
            harness.service_account_client.search_staff_event_notification_contact_information(
                contact_information_search
            )
        )
        assert len(staff_event_notification_contact_informations) == 1
        contact_information_id = staff_event_notification_contact_informations[0].id
        message_sent = create_notification_sent_input(
            "message_id", contact_information_id, harness.event_3.id, harness.event_3.start_time
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            [message_sent]
        )
        org_1_staff = query_for_message_sent(event_ids_to_search)
        staff_by_name = {staff.name: staff for staff in org_1_staff}
        assert "Johnny Tsunami" in staff_by_name
        johnny = staff_by_name["Johnny Tsunami"]
        assert len(johnny.staff_event_notification_contact_information.edges) == 1
        assert (
            len(
                johnny.staff_event_notification_contact_information.edges[
                    0
                ].node.event_notifications
            )
            == expected_count
        )

    @pytest.mark.asyncio
    async def test_create_notifications_sent_with_case(self) -> None:
        event_ids_to_search = [harness.event_3.id]

        contact_information_search = StaffEventNotificationContactInformationSearchInput()
        contact_information_search.staff_ids = [
            str(self.staff_1.id),
        ]

        staff_event_notification_contact_informations = (
            harness.service_account_client.search_staff_event_notification_contact_information(
                contact_information_search
            )
        )
        assert len(staff_event_notification_contact_informations) == 1
        contact_information_id = staff_event_notification_contact_informations[0].id
        message_sent = create_notification_sent_input(
            "message_id",
            contact_information_id,
            harness.event_3.id,
            harness.event_3.start_time,
            harness.case_1.case_id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            [message_sent]
        )
        org_1_staff = query_for_message_sent(event_ids_to_search)
        staff_by_name = {staff.name: staff for staff in org_1_staff}
        assert "Johnny Tsunami" in staff_by_name
        johnny = staff_by_name["Johnny Tsunami"]
        assert len(johnny.staff_event_notification_contact_information.edges) == 1
        assert (
            len(
                johnny.staff_event_notification_contact_information.edges[
                    0
                ].node.event_notifications
            )
            == 1
        )
        assert (
            johnny.staff_event_notification_contact_information.edges[0]
            .node.event_notifications[0]
            .case.id
            == harness.case_1.case_id
        )

    @pytest.mark.asyncio
    async def test_create_notifications_sent_with_event_time(self) -> None:
        event_ids_to_search = [harness.event_3.id]
        contact_information_search = StaffEventNotificationContactInformationSearchInput()
        contact_information_search.staff_ids = [
            str(self.staff_1.id),
        ]

        staff_event_notification_contact_informations = (
            harness.service_account_client.search_staff_event_notification_contact_information(
                contact_information_search
            )
        )
        assert len(staff_event_notification_contact_informations) == 1
        contact_information_id = staff_event_notification_contact_informations[0].id
        message_sent = create_notification_sent_input(
            "message_id",
            contact_information_id,
            harness.event_3.id,
            event_time=harness.event_3.start_time + datetime.timedelta(minutes=1),
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            [message_sent]
        )
        org_1_staff = query_for_message_sent(event_ids_to_search)
        staff_by_name = {staff.name: staff for staff in org_1_staff}
        assert "Johnny Tsunami" in staff_by_name
        johnny = staff_by_name["Johnny Tsunami"]
        assert len(johnny.staff_event_notification_contact_information.edges) == 1
        assert (
            len(
                johnny.staff_event_notification_contact_information.edges[
                    0
                ].node.event_notifications
            )
            == 1
        )
        assert johnny.staff_event_notification_contact_information.edges[
            0
        ].node.event_notifications[0].event_time == harness.event_3.start_time + datetime.timedelta(
            minutes=1
        )
        assert isinstance(
            johnny.staff_event_notification_contact_information.edges[0]
            .node.event_notifications[0]
            .created_time,
            datetime.datetime,
        )

    @pytest.mark.parametrize(
        "case_ids,event_type_ids,expected_count",
        [
            (None, None, 1),
            (lambda: [harness.case_1.case_id], None, 1),
            (None, lambda: [harness.event_3.event_type_id], 1),
            (lambda: ["donkey"], None, 0),
            (None, lambda: ["donkey"], 0),
        ],
    )
    @pytest.mark.asyncio
    async def test_notifications_query(
        self,
        case_ids: Optional[Callable[[], list[str]]],
        event_type_ids: Optional[Callable[[], list[str]]],
        expected_count: int,
    ) -> None:
        case_ids_to_search = []
        if case_ids:
            case_ids_to_search = case_ids()

        event_type_ids_to_search = []
        if event_type_ids:
            event_type_ids_to_search = event_type_ids()

        contact_information_search = StaffEventNotificationContactInformationSearchInput()
        contact_information_search.staff_ids = [
            str(self.staff_1.id),
        ]

        staff_event_notification_contact_informations = (
            harness.service_account_client.search_staff_event_notification_contact_information(
                contact_information_search
            )
        )
        assert len(staff_event_notification_contact_informations) == 1
        contact_information_id = staff_event_notification_contact_informations[0].id
        message_sent = create_notification_sent_input(
            "message_id",
            contact_information_id,
            harness.event_3.id,
            harness.event_3.start_time,
            harness.case_1.case_id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            [message_sent]
        )
        org_1_staff = query_for_staff_message_sent(case_ids_to_search, event_type_ids_to_search)
        staff_by_name = {staff.name: staff for staff in org_1_staff}
        assert "Johnny Tsunami" in staff_by_name
        johnny = staff_by_name["Johnny Tsunami"]
        assert len(johnny.staff_event_notification_contact_information.edges) == 1
        assert (
            len(
                johnny.staff_event_notification_contact_information.edges[
                    0
                ].node.staff_event_notifications
            )
            == expected_count
        )
