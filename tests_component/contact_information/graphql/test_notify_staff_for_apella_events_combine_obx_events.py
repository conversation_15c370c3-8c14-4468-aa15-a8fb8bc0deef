import datetime
import uuid
from typing import Optional, cast

import pytest

from api_server.services.case_activity.case_activity_store import CaseActivityModel
from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_input_schema import GQLStaffEventsNotificationsInput
from api_server.services.case.case_staff_store import CaseStaffModel
from api_server.services.contact_information.contact_information_store import (
    ContactInformationModel,
    ContactInformationType,
)
from api_server.services.contact_information.graphql.staff_events_notification_data_module import (
    DEFAULT_NOTIFICATION_TIME_WINDOW,
)
from api_server.services.contact_information.staff_event_notification_contact_information_store import (
    StaffEventNotificationContactInformationModel,
    StaffEventNotificationModel,
    SentNotificationStatus,
    EventNotificationQueryDto,
)
from api_server.services.events.event_models import EventModel
from api_server.services.observations.observation_store import ObservationModel
from api_server.services.phases.phase_store import PhaseModel, PhaseStatus
from tests_component import harness
from tests_component.contact_information.graphql.mock_helpers import mock_staff


def mock_event(
    event_id: str,
    start_time: datetime.datetime,
    org_id: str,
    site_id: str,
    room_id: str,
    event_type_id: str = "patient_wheels_in",
    source_type: str = "prediction",
    created_time: datetime.datetime = datetime.datetime.now(),
    confidence: Optional[float] = None,
    deleted_at: Optional[datetime.datetime] = None,
) -> EventModel:
    event = EventModel()
    event.id = event_id
    event.created_time = created_time
    event.event_type_id = event_type_id
    event.start_time = start_time
    event.process_timestamp = start_time
    event.source = "test"
    event.source_type = source_type
    if confidence is not None:
        event.confidence = confidence
    event.org_id = org_id
    event.site_id = site_id
    event.room_id = room_id
    event.deleted_at = deleted_at
    return event


def mock_phase(
    start_event: EventModel,
    case_id: Optional[str] = None,
    type_id: str = "CASE",
    end_event: Optional[EventModel] = None,
    source_type: str = "unified",
) -> PhaseModel:
    phase = PhaseModel()
    phase.id = uuid.uuid4()
    phase.start_event_id = start_event.id
    phase.type_id = type_id
    if end_event:
        phase.end_event_id = end_event.id
    phase.case_id = case_id
    phase.source_type = source_type
    phase.org_id = start_event.org_id
    phase.site_id = start_event.site_id
    phase.room_id = start_event.room_id
    phase.status = PhaseStatus.VALID
    return phase


def mock_observation(
    type_id: str,
    case_id: str,
    org_id: str,
    observation_time: Optional[datetime.datetime] = None,
    recorded_time: Optional[datetime.datetime] = None,
) -> ObservationModel:
    observation_time = observation_time or datetime.datetime.now(tz=datetime.timezone.utc)
    recorded_time = recorded_time or observation_time
    return ObservationModel(
        id=str(uuid.uuid4()),
        type_id=type_id,
        case_id=case_id,
        org_id=org_id,
        observation_time=observation_time,
        recorded_time=recorded_time,
    )


def mock_case_activity(
    org_id: str,
    site_id: str,
    id: str,
    source_type: str,
    room_id: str,
    event_type_id: str,
    source: str,
    case_id: str,
    start_time: datetime.datetime,
    process_timestamp: datetime.datetime,
    deleted_time: Optional[datetime.datetime] = None,
) -> CaseActivityModel:
    case_activity = CaseActivityModel()

    case_activity.org_id = org_id
    case_activity.site_id = site_id
    case_activity.id = id
    case_activity.source_type = source_type
    case_activity.room_id = room_id
    case_activity.deleted_time = cast(datetime.datetime, deleted_time)
    case_activity.event_type_id = event_type_id
    case_activity.source = source
    case_activity.case_id = case_id
    case_activity.start_time = start_time
    case_activity.process_timestamp = process_timestamp
    return case_activity


def mock_contact_information(
    contact_information_value: str,
    contact_information_type: ContactInformationType = ContactInformationType.PHONE_NUMBER,
) -> ContactInformationModel:
    contact_info = ContactInformationModel()
    contact_info.id = uuid.uuid4()
    contact_info.contact_information_value = contact_information_value
    contact_info.type = contact_information_type
    return contact_info


def mock_staff_contact_information(
    contact_information_id: uuid.UUID,
    staff_id: uuid.UUID,
    event_type_id: str = "OBSERVED_IN_PRE_PROCEDURE",
) -> StaffEventNotificationContactInformationModel:
    staff_contact_information = StaffEventNotificationContactInformationModel()
    staff_contact_information.contact_information_id = contact_information_id
    staff_contact_information.staff_id = staff_id
    staff_contact_information.event_type_id = event_type_id
    staff_contact_information.id = uuid.uuid4()
    return staff_contact_information


def mock_case_staff(
    staff_id: uuid.UUID,
    case_id: str,
) -> CaseStaffModel:
    case_staff = CaseStaffModel()
    case_staff.staff_id = staff_id
    case_staff.case_id = case_id
    case_staff.role = "Primary"
    return case_staff


def mock_staff_event_notification(
    staff_event_notification_contact_information_id: uuid.UUID,
    event_id: str,
    event_time: datetime.datetime,
    case_id: str,
    sent_status: SentNotificationStatus = SentNotificationStatus.SENT,
    attempts: int = 1,
    message_id: str = "aaa",
) -> StaffEventNotificationModel:
    staff_event_notification = StaffEventNotificationModel()
    staff_event_notification.message_id = message_id
    staff_event_notification.staff_event_contact_information_id = (
        staff_event_notification_contact_information_id
    )
    staff_event_notification.event_id = event_id
    staff_event_notification.event_time = event_time
    staff_event_notification.case_id = case_id
    staff_event_notification.sent_status = sent_status
    staff_event_notification.attempts = attempts
    return staff_event_notification


class TestNotifyStaffForApellaAndOBXEvents:
    def test_notify_staff_for_events_no_events_component(self) -> None:
        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.case_ca.site_id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 0

    @pytest.mark.asyncio
    async def test_notify_staff_for_one_ehr_and_one_apella_event(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            room_id=harness.case_ca.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_ca.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="patient_wheels_in",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        mock_obx_1 = mock_observation(
            type_id="OBSERVED_IN_PRE_PROCEDURE",
            org_id=harness.case_ca.org_id,
            case_id=harness.case_ca.case_id,
            observation_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            recorded_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
        )
        await harness.observation_store.create_observation(mock_obx_1)

        mock_case_activity_1 = mock_case_activity(
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            id=str(mock_obx_1.id),
            source_type="ehr",
            room_id=harness.case_ca.room_id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
            source="ehr",
            case_id=harness.case_ca.case_id,
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            process_timestamp=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
        )
        await harness.case_activity_test_helper.insert_case_activity(mock_case_activity_1)

        mock_contact_information_2 = mock_contact_information(
            contact_information_value="1234567891",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_2]
        )

        mock_staff_2 = mock_staff(external_staff_id="staff_2")

        mock_case_staff_2 = mock_case_staff(
            staff_id=mock_staff_2.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_2])

        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_2.id,
            staff_id=mock_staff_2.id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_2]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.case_ca.site_id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 2

        notifications_results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                site_ids=[harness.case_ca.site_id],
            )
        )
        assert notifications_results
        assert notifications_results[0].sent_time is not None
        assert notifications_results[1].sent_time is not None

    @pytest.mark.asyncio
    async def test_notify_staff_for_ehr_and_apella_event_multiple_contacts(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            room_id=harness.case_ca.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_ca.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="patient_wheels_in",
        )
        mock_staff_contact_information_11 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1, mock_staff_contact_information_11]
        )

        mock_obx_1 = mock_observation(
            type_id="OBSERVED_IN_PRE_PROCEDURE",
            org_id=harness.case_ca.org_id,
            case_id=harness.case_ca.case_id,
            observation_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            recorded_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
        )
        await harness.observation_store.create_observation(mock_obx_1)

        mock_case_activity_1 = mock_case_activity(
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            id=str(mock_obx_1.id),
            source_type="ehr",
            room_id=harness.case_ca.room_id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
            source="ehr",
            case_id=harness.case_ca.case_id,
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            process_timestamp=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
        )
        await harness.case_activity_test_helper.insert_case_activity(mock_case_activity_1)

        mock_contact_information_2 = mock_contact_information(
            contact_information_value="1234567891",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_2]
        )

        mock_staff_2 = mock_staff(external_staff_id="staff_2")

        mock_case_staff_2 = mock_case_staff(
            staff_id=mock_staff_2.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_2])

        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_2.id,
            staff_id=mock_staff_2.id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_2]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.case_ca.site_id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 3

        notifications_results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                site_ids=[harness.case_ca.site_id],
            )
        )
        assert notifications_results
        assert notifications_results[0].sent_time is not None
        assert notifications_results[1].sent_time is not None

    @pytest.mark.asyncio
    async def test_notify_staff_for_both_ehr_events_and_apella_event_multiple_contacts(
        self,
    ) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            room_id=harness.case_ca.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_ca.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="patient_wheels_in",
        )
        mock_staff_contact_information_11 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1, mock_staff_contact_information_11]
        )

        mock_obx_1 = mock_observation(
            type_id="OBSERVED_IN_PRE_PROCEDURE",
            org_id=harness.case_ca.org_id,
            case_id=harness.case_ca.case_id,
            observation_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            recorded_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
        )
        await harness.observation_store.create_observation(mock_obx_1)

        mock_case_activity_1 = mock_case_activity(
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            id=str(mock_obx_1.id),
            source_type="ehr",
            room_id=harness.case_ca.room_id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
            source="ehr",
            case_id=harness.case_ca.case_id,
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            process_timestamp=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
        )
        await harness.case_activity_test_helper.insert_case_activity(mock_case_activity_1)

        mock_obx_2 = mock_observation(
            type_id="OBSERVED_PRE_PROCEDURE_COMPLETE",
            org_id=harness.case_ca.org_id,
            case_id=harness.case_ca.case_id,
            observation_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            recorded_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
        )
        await harness.observation_store.create_observation(mock_obx_2)

        mock_case_activity_2 = mock_case_activity(
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            id=str(mock_obx_2.id),
            source_type="ehr",
            room_id=harness.case_ca.room_id,
            event_type_id="OBSERVED_PRE_PROCEDURE_COMPLETE",
            source="ehr",
            case_id=harness.case_ca.case_id,
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            process_timestamp=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
        )
        await harness.case_activity_test_helper.insert_case_activity(mock_case_activity_2)

        mock_contact_information_2 = mock_contact_information(
            contact_information_value="1234567891",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_2]
        )

        mock_staff_2 = mock_staff(external_staff_id="staff_2")

        mock_case_staff_2 = mock_case_staff(
            staff_id=mock_staff_2.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_2])

        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_2.id,
            staff_id=mock_staff_2.id,
            event_type_id="OBSERVED_PRE_PROCEDURE_COMPLETE",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_2]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.case_ca.site_id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 3

        notifications_results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                site_ids=[harness.case_ca.site_id],
            )
        )
        assert notifications_results
        assert notifications_results[0].sent_time is not None
        assert notifications_results[1].sent_time is not None

    @pytest.mark.asyncio
    async def test_notify_staff_for_late_ehr_and_apella_event(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            room_id=harness.case_ca.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_ca.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="patient_wheels_in",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        mock_obx_1 = mock_observation(
            type_id="OBSERVED_IN_PRE_PROCEDURE",
            org_id=harness.case_ca.org_id,
            case_id=harness.case_ca.case_id,
            observation_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc)
            - DEFAULT_NOTIFICATION_TIME_WINDOW
            - datetime.timedelta(minutes=1),
            recorded_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
        )
        await harness.observation_store.create_observation(mock_obx_1)

        mock_case_activity_1 = mock_case_activity(
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            id=str(mock_obx_1.id),
            source_type="ehr",
            room_id=harness.case_ca.room_id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
            source="ehr",
            case_id=harness.case_ca.case_id,
            start_time=mock_obx_1.observation_time,
            process_timestamp=mock_obx_1.recorded_time,
        )
        await harness.case_activity_test_helper.insert_case_activity(mock_case_activity_1)

        mock_contact_information_2 = mock_contact_information(
            contact_information_value="1234567891",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_2]
        )

        mock_staff_2 = mock_staff(external_staff_id="staff_2")

        mock_case_staff_2 = mock_case_staff(
            staff_id=mock_staff_2.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_2])

        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_2.id,
            staff_id=mock_staff_2.id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_2]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.case_ca.site_id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 1

        notifications_results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                site_ids=[harness.case_ca.site_id],
            )
        )
        assert notifications_results
        assert notifications_results[0].sent_time is not None

    @pytest.mark.asyncio
    async def test_notify_staff_for_late_ehr_and_apella_event_ignores_deleted(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            room_id=harness.case_ca.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_ca.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="patient_wheels_in",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        mock_obx_1 = mock_observation(
            type_id="OBSERVED_IN_PRE_PROCEDURE",
            org_id=harness.case_ca.org_id,
            case_id=harness.case_ca.case_id,
            observation_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            recorded_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
        )
        await harness.observation_store.create_observation(mock_obx_1)

        mock_case_activity_1 = mock_case_activity(
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            id=str(mock_obx_1.id),
            source_type="ehr",
            room_id=harness.case_ca.room_id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
            source="ehr",
            case_id=harness.case_ca.case_id,
            start_time=mock_obx_1.observation_time,
            process_timestamp=mock_obx_1.recorded_time,
            deleted_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
        )
        await harness.case_activity_test_helper.insert_case_activity(mock_case_activity_1)

        mock_contact_information_2 = mock_contact_information(
            contact_information_value="1234567891",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_2]
        )

        mock_staff_2 = mock_staff(external_staff_id="staff_2")

        mock_case_staff_2 = mock_case_staff(
            staff_id=mock_staff_2.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_2])

        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_2.id,
            staff_id=mock_staff_2.id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_2]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.case_ca.site_id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 1

        notifications_results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                site_ids=[harness.case_ca.site_id],
            )
        )
        assert notifications_results
        assert notifications_results[0].sent_time is not None

    @pytest.mark.asyncio
    async def test_notify_staff_for_ehr_and_apella_event_multiple_surgeon_one_contact(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            room_id=harness.case_ca.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_ca.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="patient_wheels_in",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        mock_obx_1 = mock_observation(
            type_id="OBSERVED_IN_PRE_PROCEDURE",
            org_id=harness.case_ca.org_id,
            case_id=harness.case_ca.case_id,
            observation_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            recorded_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
        )
        await harness.observation_store.create_observation(mock_obx_1)

        mock_case_activity_1 = mock_case_activity(
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            id=str(mock_obx_1.id),
            source_type="ehr",
            room_id=harness.case_ca.room_id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
            source="ehr",
            case_id=harness.case_ca.case_id,
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            process_timestamp=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
        )
        await harness.case_activity_test_helper.insert_case_activity(mock_case_activity_1)

        mock_contact_information_2 = mock_contact_information(
            contact_information_value="1234567891",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_2]
        )

        mock_staff_2 = mock_staff(external_staff_id="staff_2")
        mock_staff_3 = mock_staff(external_staff_id="staff_3")

        mock_case_staff_2 = mock_case_staff(
            staff_id=mock_staff_2.id, case_id=harness.case_ca.case_id
        )
        mock_case_staff_3 = mock_case_staff(
            staff_id=mock_staff_3.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_2, mock_case_staff_3])

        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_2.id,
            staff_id=mock_staff_2.id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
        )
        mock_staff_contact_information_3 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_2.id,
            staff_id=mock_staff_3.id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_2, mock_staff_contact_information_3]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.case_ca.site_id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 2

        notifications_results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                site_ids=[harness.case_ca.site_id],
            )
        )
        assert notifications_results
        assert notifications_results[0].sent_time is not None
        assert notifications_results[1].sent_time is not None

    @pytest.mark.asyncio
    async def test_notify_staff_for_ehr_and_apella_event_no_time_to_check(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc)
            - datetime.timedelta(minutes=4),
            confidence=0.99,
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            room_id=harness.case_ca.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_ca.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="patient_wheels_in",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        mock_obx_1 = mock_observation(
            type_id="OBSERVED_IN_PRE_PROCEDURE",
            org_id=harness.case_ca.org_id,
            case_id=harness.case_ca.case_id,
            observation_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc)
            - datetime.timedelta(minutes=4),
            recorded_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc)
            - datetime.timedelta(minutes=4),
        )
        await harness.observation_store.create_observation(mock_obx_1)

        mock_case_activity_1 = mock_case_activity(
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            id=str(mock_obx_1.id),
            source_type="ehr",
            room_id=harness.case_ca.room_id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
            source="ehr",
            case_id=harness.case_ca.case_id,
            start_time=mock_obx_1.observation_time,
            process_timestamp=mock_obx_1.recorded_time,
        )
        await harness.case_activity_test_helper.insert_case_activity(mock_case_activity_1)

        mock_contact_information_2 = mock_contact_information(
            contact_information_value="1234567891",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_2]
        )

        mock_staff_2 = mock_staff(external_staff_id="staff_2")

        mock_case_staff_2 = mock_case_staff(
            staff_id=mock_staff_2.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_2])

        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_2.id,
            staff_id=mock_staff_2.id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_2]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.case_ca.site_id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 2

        notifications_results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                site_ids=[harness.case_ca.site_id],
            )
        )
        assert notifications_results
        assert notifications_results[0].sent_time is not None
        assert notifications_results[1].sent_time is not None

    @pytest.mark.asyncio
    async def test_notify_staff_for_ehr_and_apella_event_is_idempotent(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            room_id=harness.case_ca.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_ca.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="patient_wheels_in",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        mock_obx_1 = mock_observation(
            type_id="OBSERVED_IN_PRE_PROCEDURE",
            org_id=harness.case_ca.org_id,
            case_id=harness.case_ca.case_id,
            observation_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc)
            - datetime.timedelta(minutes=4),
            recorded_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc)
            - datetime.timedelta(minutes=4),
        )
        await harness.observation_store.create_observation(mock_obx_1)

        mock_case_activity_1 = mock_case_activity(
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            id=str(mock_obx_1.id),
            source_type="ehr",
            room_id=harness.case_ca.room_id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
            source="ehr",
            case_id=harness.case_ca.case_id,
            start_time=mock_obx_1.observation_time,
            process_timestamp=mock_obx_1.recorded_time,
        )
        await harness.case_activity_test_helper.insert_case_activity(mock_case_activity_1)

        mock_contact_information_2 = mock_contact_information(
            contact_information_value="1234567891",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_2]
        )

        mock_staff_2 = mock_staff(external_staff_id="staff_2")

        mock_case_staff_2 = mock_case_staff(
            staff_id=mock_staff_2.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_2])

        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_2.id,
            staff_id=mock_staff_2.id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_2]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.case_ca.site_id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 2

        notifications_results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                site_ids=[harness.case_ca.site_id],
            )
        )
        assert notifications_results
        assert notifications_results[0].sent_time is not None
        assert notifications_results[1].sent_time is not None

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 0

    @pytest.mark.asyncio
    async def test_notify_staff_for_two_events_one_notified(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 3, 3, 12, 15, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            room_id=harness.case_ca.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_ca.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_0")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="patient_wheels_in",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        mock_obx_2 = mock_observation(
            type_id="OBSERVED_IN_PRE_PROCEDURE",
            org_id=harness.case_ca_2.org_id,
            case_id=harness.case_ca_2.case_id,
            observation_time=datetime.datetime(2021, 3, 3, 12, 10, 0, tzinfo=datetime.timezone.utc),
            recorded_time=datetime.datetime(2021, 3, 3, 12, 10, 0, tzinfo=datetime.timezone.utc),
        )
        await harness.observation_store.create_observation(mock_obx_2)
        mock_case_activity_2 = mock_case_activity(
            org_id=mock_obx_2.org_id,
            site_id=harness.case_ca_2.site_id,
            id=str(mock_obx_2.id),
            source_type="ehr",
            room_id=harness.case_ca_2.room_id,
            event_type_id=mock_obx_2.type_id,
            source="ehr",
            case_id=mock_obx_2.case_id,
            start_time=mock_obx_2.observation_time,
            process_timestamp=mock_obx_2.recorded_time,
        )
        await harness.case_activity_test_helper.insert_case_activity(mock_case_activity_2)

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="1234567892",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )
        mock_staff_1 = mock_staff(external_staff_id="staff_1")
        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_ca_2.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="OBSERVED_PRE_PROCEDURE_COMPLETE",
        )
        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1, mock_staff_contact_information_2]
        )
        mock_notification = mock_staff_event_notification(
            event_id=str(mock_obx_2.id),
            event_time=mock_obx_2.observation_time,
            staff_event_notification_contact_information_id=mock_staff_contact_information_2.id,
            case_id=harness.case_ca_2.case_id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            [mock_notification]
        )
        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_ca.id,
                time_to_check=datetime.datetime(
                    2021, 3, 3, 12, 15, 0, tzinfo=datetime.timezone.utc
                ),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )
        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 1

    @pytest.mark.parametrize(
        "notification_time,expected_count",
        [
            (datetime.datetime(2021, 3, 3, 12, 15, 0, tzinfo=datetime.timezone.utc), 1),
            (
                datetime.datetime(2021, 3, 3, 12, 15, 0, tzinfo=datetime.timezone.utc)
                - datetime.timedelta(minutes=15),
                2,
            ),
        ],
    )
    @pytest.mark.asyncio
    async def test_notify_staff_for_one_events_already_notified(
        self,
        notification_time: datetime.datetime,
        expected_count: int,
    ) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 3, 3, 12, 15, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            room_id=harness.case_ca.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_ca.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_0")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="patient_wheels_in",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        mock_obx_1 = mock_observation(
            type_id="OBSERVED_IN_PRE_PROCEDURE",
            org_id=harness.case_ca.org_id,
            case_id=harness.case_ca.case_id,
            observation_time=datetime.datetime(2021, 3, 3, 12, 15, 0, tzinfo=datetime.timezone.utc),
            recorded_time=datetime.datetime(2021, 3, 3, 12, 15, 0, tzinfo=datetime.timezone.utc),
        )
        await harness.observation_store.create_observation(mock_obx_1)
        mock_case_activity_1 = mock_case_activity(
            org_id=mock_obx_1.org_id,
            site_id=harness.case_ca.site_id,
            id=str(mock_obx_1.id),
            source_type="ehr",
            room_id=harness.case_ca.room_id,
            event_type_id=mock_obx_1.type_id,
            source="ehr",
            case_id=mock_obx_1.case_id,
            start_time=mock_obx_1.observation_time,
            process_timestamp=mock_obx_1.recorded_time,
        )
        await harness.case_activity_test_helper.insert_case_activity(mock_case_activity_1)
        mock_contact_information_1 = mock_contact_information(
            contact_information_value="1234567891",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )
        mock_staff_1 = mock_staff(external_staff_id="staff_1")
        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])
        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )
        mock_notification = mock_staff_event_notification(
            event_id=str(mock_obx_1.id),
            event_time=notification_time,
            staff_event_notification_contact_information_id=mock_staff_contact_information_1.id,
            case_id=harness.case_ca.case_id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            [mock_notification]
        )
        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_ca.id,
                time_to_check=datetime.datetime(
                    2021, 3, 3, 12, 15, 0, tzinfo=datetime.timezone.utc
                ),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )
        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == expected_count

    @pytest.mark.asyncio
    async def test_notify_staff_for_ehr_and_apella_event_new_signup(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 3, 3, 12, 15, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            room_id=harness.case_ca.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_ca.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_obx_1 = mock_observation(
            type_id="OBSERVED_IN_PRE_PROCEDURE",
            org_id=harness.case_ca.org_id,
            case_id=harness.case_ca.case_id,
            observation_time=datetime.datetime(2021, 3, 3, 12, 15, 0, tzinfo=datetime.timezone.utc),
            recorded_time=datetime.datetime(2021, 3, 3, 12, 15, 0, tzinfo=datetime.timezone.utc),
        )
        await harness.observation_store.create_observation(mock_obx_1)
        mock_case_activity_1 = mock_case_activity(
            org_id=mock_obx_1.org_id,
            site_id=harness.case_ca.site_id,
            id=str(mock_obx_1.id),
            source_type="ehr",
            room_id=harness.case_ca.room_id,
            event_type_id=mock_obx_1.type_id,
            source="ehr",
            case_id=mock_obx_1.case_id,
            start_time=mock_obx_1.observation_time,
            process_timestamp=mock_obx_1.recorded_time,
        )
        await harness.case_activity_test_helper.insert_case_activity(mock_case_activity_1)

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        mock_contact_information_2 = mock_contact_information(
            contact_information_value="1234567891",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        contact_infos = [mock_contact_information_1, mock_contact_information_2]
        await harness.contact_information_store.upsert_contact_information(contact_infos)

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="patient_wheels_in",
        )
        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1, mock_staff_contact_information_2]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_ca.id,
                time_to_check=datetime.datetime(
                    2021, 3, 3, 12, 15, 0, tzinfo=datetime.timezone.utc
                ),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 2

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 0

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_2.id,
            staff_id=mock_staff_1.id,
            event_type_id="patient_wheels_in",
        )
        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_2.id,
            staff_id=mock_staff_1.id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1, mock_staff_contact_information_2]
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 2

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 0

    @pytest.mark.asyncio
    async def test_notify_staff_for_event_after_failure(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 3, 3, 12, 15, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            room_id=harness.case_ca.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_ca.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_obx_1 = mock_observation(
            type_id="OBSERVED_IN_PRE_PROCEDURE",
            org_id=harness.case_ca.org_id,
            case_id=harness.case_ca.case_id,
            observation_time=datetime.datetime(2021, 3, 3, 12, 15, 0, tzinfo=datetime.timezone.utc),
            recorded_time=datetime.datetime(2021, 3, 3, 12, 15, 0, tzinfo=datetime.timezone.utc),
        )
        await harness.observation_store.create_observation(mock_obx_1)
        mock_case_activity_1 = mock_case_activity(
            org_id=mock_obx_1.org_id,
            site_id=harness.case_ca.site_id,
            id=str(mock_obx_1.id),
            source_type="ehr",
            room_id=harness.case_ca.room_id,
            event_type_id=mock_obx_1.type_id,
            source="ehr",
            case_id=mock_obx_1.case_id,
            start_time=mock_obx_1.observation_time,
            process_timestamp=mock_obx_1.recorded_time,
        )
        await harness.case_activity_test_helper.insert_case_activity(mock_case_activity_1)
        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )
        mock_staff_1 = mock_staff(external_staff_id="staff_1")
        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])
        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
        )
        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="patient_wheels_in",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1, mock_staff_contact_information_2]
        )

        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            [
                mock_staff_event_notification(
                    staff_event_notification_contact_information_id=mock_staff_contact_information_1.id,
                    event_id=str(mock_obx_1.id),
                    event_time=mock_obx_1.observation_time,
                    case_id=harness.case_ca.case_id,
                    sent_status=SentNotificationStatus.FAILED,
                )
            ]
        )
        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_ca.id,
                time_to_check=datetime.datetime(
                    2021, 3, 3, 12, 15, 0, tzinfo=datetime.timezone.utc
                ),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 2

        notifications = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                site_ids=[harness.site_ca.id],
            )
        )
        assert len(notifications) == 2
        assert notifications[1].sent_time is not None
        assert notifications[1].sent_status == SentNotificationStatus.SENT
        assert notifications[1].attempts == 2

    @pytest.mark.asyncio
    async def test_notify_staff_for_event_after_miss(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(minutes=4),
            confidence=0.99,
            org_id=harness.case_ca.org_id,
            site_id=harness.case_ca.site_id,
            room_id=harness.case_ca.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_ca.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_obx_1 = mock_observation(
            type_id="OBSERVED_IN_PRE_PROCEDURE",
            org_id=harness.case_ca.org_id,
            case_id=harness.case_ca.case_id,
            observation_time=datetime.datetime.now(datetime.timezone.utc)
            - datetime.timedelta(minutes=4),
            recorded_time=datetime.datetime.now(datetime.timezone.utc)
            - datetime.timedelta(minutes=4),
        )
        await harness.observation_store.create_observation(mock_obx_1)
        mock_case_activity_1 = mock_case_activity(
            org_id=mock_obx_1.org_id,
            site_id=harness.case_ca.site_id,
            id=str(mock_obx_1.id),
            source_type="ehr",
            room_id=harness.case_ca.room_id,
            event_type_id=mock_obx_1.type_id,
            source="ehr",
            case_id=mock_obx_1.case_id,
            start_time=mock_obx_1.observation_time,
            process_timestamp=mock_obx_1.recorded_time,
        )
        await harness.case_activity_test_helper.insert_case_activity(mock_case_activity_1)
        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )
        mock_staff_1 = mock_staff(external_staff_id="staff_1")
        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_ca.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])
        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="OBSERVED_IN_PRE_PROCEDURE",
        )
        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="patient_wheels_in",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1, mock_staff_contact_information_2]
        )
        mock_notification = mock_staff_event_notification(
            event_id=str(mock_obx_1.id),
            event_time=mock_obx_1.observation_time,
            staff_event_notification_contact_information_id=mock_staff_contact_information_1.id,
            case_id=harness.case_ca.case_id,
            sent_status=SentNotificationStatus.MISSED,
            attempts=0,
            message_id="missed",
        )
        mock_notification_2 = mock_staff_event_notification(
            event_id=str(mock_event_1.id),
            event_time=mock_event_1.start_time,
            staff_event_notification_contact_information_id=mock_staff_contact_information_2.id,
            case_id=harness.case_ca.case_id,
            sent_status=SentNotificationStatus.MISSED,
            attempts=0,
            message_id="missed",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            [mock_notification, mock_notification_2]
        )
        notifications_prior = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                site_ids=[harness.case_ca.site_id],
            )
        )
        assert len(notifications_prior) == 2
        assert notifications_prior[0].sent_time is None
        assert notifications_prior[0].sent_status == SentNotificationStatus.MISSED
        assert notifications_prior[0].attempts == 0
        assert notifications_prior[0].message_id == "missed"
        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.case_ca.site_id,
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 2

        notifications = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                site_ids=[harness.case_ca.site_id],
            )
        )
        assert len(notifications) == 2
        assert notifications[0].sent_time is not None
        assert notifications[0].sent_status == SentNotificationStatus.SENT
        assert notifications[0].attempts == 1
        assert notifications[0].message_id != "missed"
