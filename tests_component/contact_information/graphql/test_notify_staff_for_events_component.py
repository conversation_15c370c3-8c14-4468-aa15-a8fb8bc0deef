import datetime
import uuid
from typing import Optional

import pytest

from api_server.services.contact_information.graphql.staff_events_processor import (
    DEFAULT_NOTIFICATION_TIME_WINDOW,
)
from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_input_schema import GQLStaffEventsNotificationsInput
from api_server.services.case.case_staff_store import CaseStaffModel
from api_server.services.contact_information.contact_information_store import (
    ContactInformationModel,
    ContactInformationType,
)
from api_server.services.contact_information.staff_event_notification_contact_information_store import (
    StaffEventNotificationContactInformationModel,
    StaffEventNotificationModel,
    SentNotificationStatus,
    EventNotificationQueryDto,
)
from api_server.services.events.event_store import EventModel
from api_server.services.phases.phase_store import PhaseModel, PhaseStatus
from tests_component import harness
from tests_component.contact_information.graphql.mock_helpers import mock_staff


def mock_event(
    event_id: str,
    start_time: datetime.datetime,
    org_id: str,
    site_id: str,
    room_id: str,
    event_type_id: str = "patient_wheels_in",
    source_type: str = "prediction",
    created_time: datetime.datetime = datetime.datetime.now(),
    confidence: Optional[float] = None,
    deleted_at: Optional[datetime.datetime] = None,
) -> EventModel:
    event = EventModel()
    event.id = event_id
    event.created_time = created_time
    event.event_type_id = event_type_id
    event.start_time = start_time
    event.process_timestamp = start_time
    event.source = "test"
    event.source_type = source_type
    if confidence is not None:
        event.confidence = confidence
    event.org_id = org_id
    event.site_id = site_id
    event.room_id = room_id
    event.deleted_at = deleted_at
    return event


def mock_phase(
    start_event: EventModel,
    case_id: Optional[str] = None,
    type_id: str = "CASE",
    end_event: Optional[EventModel] = None,
    source_type: str = "unified",
) -> PhaseModel:
    phase = PhaseModel()
    phase.id = uuid.uuid4()
    phase.start_event_id = start_event.id
    phase.type_id = type_id
    if end_event:
        phase.end_event_id = end_event.id
    phase.case_id = case_id
    phase.source_type = source_type
    phase.org_id = start_event.org_id
    phase.site_id = start_event.site_id
    phase.room_id = start_event.room_id
    phase.status = PhaseStatus.VALID
    return phase


def mock_contact_information(
    contact_information_value: str,
    contact_information_type: ContactInformationType = ContactInformationType.PHONE_NUMBER,
) -> ContactInformationModel:
    contact_info = ContactInformationModel()
    contact_info.id = uuid.uuid4()
    contact_info.contact_information_value = contact_information_value
    contact_info.type = contact_information_type
    return contact_info


def mock_staff_contact_information(
    contact_information_id: uuid.UUID, staff_id: uuid.UUID, event_type_id: str = "patient_wheels_in"
) -> StaffEventNotificationContactInformationModel:
    staff_contact_information = StaffEventNotificationContactInformationModel()
    staff_contact_information.contact_information_id = contact_information_id
    staff_contact_information.staff_id = staff_id
    staff_contact_information.event_type_id = event_type_id
    staff_contact_information.id = uuid.uuid4()
    return staff_contact_information


def mock_case_staff(
    staff_id: uuid.UUID,
    case_id: str,
) -> CaseStaffModel:
    case_staff = CaseStaffModel()
    case_staff.staff_id = staff_id
    case_staff.case_id = case_id
    case_staff.role = "Primary"
    return case_staff


def mock_staff_event_notification(
    staff_event_notification_contact_information_id: uuid.UUID,
    event_id: str,
    event_time: datetime.datetime,
    case_id: str,
    sent_status: SentNotificationStatus = SentNotificationStatus.SENT,
    attempts: int = 1,
    message_id: str = "aaa",
) -> StaffEventNotificationModel:
    staff_event_notification = StaffEventNotificationModel()
    staff_event_notification.message_id = message_id
    staff_event_notification.staff_event_contact_information_id = (
        staff_event_notification_contact_information_id
    )
    staff_event_notification.event_id = event_id
    staff_event_notification.event_time = event_time
    staff_event_notification.case_id = case_id
    staff_event_notification.sent_status = sent_status
    staff_event_notification.attempts = attempts
    return staff_event_notification


class TestNotifyStaffForEvents:
    def test_notify_staff_for_events_no_events_component(self) -> None:
        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_1.id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 0

    @pytest.mark.asyncio
    async def test_notify_staff_for_one_event(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_0.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 1

        notifications_results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                site_ids=[harness.site_0.id],
            )
        )
        assert notifications_results
        assert notifications_results[0].sent_time is not None

    @pytest.mark.asyncio
    async def test_notify_staff_for_one_patient_undraped_event(self) -> None:
        """
        This validates that we check for recently concluded INTRA_OPERATIVE phases
        """
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
            event_type_id="patient_draped",
        )
        mock_event_2 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 6, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
            event_type_id="patient_undraped",
        )
        await harness.event_store.upsert_events([mock_event_1, mock_event_2])
        mock_phase_1 = mock_phase(
            start_event=mock_event_1,
            end_event=mock_event_2,
            case_id=harness.case_0.case_id,
            type_id="INTRA_OPERATIVE",
        )

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="patient_undraped",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 1, 6, 5, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 1

        notifications_results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                site_ids=[harness.site_0.id],
            )
        )
        assert notifications_results
        assert notifications_results[0].sent_time is not None

    @pytest.mark.asyncio
    async def test_dont_notify_staff_for_one_late_event(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 30, 0, tzinfo=datetime.timezone.utc)
            - DEFAULT_NOTIFICATION_TIME_WINDOW
            - datetime.timedelta(minutes=1),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        mock_event_2 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 30, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1, mock_event_2])
        mock_phase_1 = mock_phase(
            start_event=mock_event_1, end_event=mock_event_2, case_id=harness.case_0.case_id
        )

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 30, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 1

    @pytest.mark.asyncio
    async def test_notify_staff_for_one_event_multiple_contacts(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_0.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        mock_contact_information_2 = mock_contact_information(
            contact_information_value="0987654321",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1, mock_contact_information_2]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )
        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_2.id,
            staff_id=mock_staff_1.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1, mock_staff_contact_information_2]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 2

    @pytest.mark.asyncio
    async def test_notify_staff_for_one_event_multiple_surgeons_one_contact(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_0.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")
        mock_staff_2 = mock_staff(external_staff_id="staff_2")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        mock_case_staff_2 = mock_case_staff(
            staff_id=mock_staff_2.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1, mock_case_staff_2])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )
        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_2.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1, mock_staff_contact_information_2]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 1

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 0

    @pytest.mark.asyncio
    async def test_notify_staff_for_one_event_ignores_deleted(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
            deleted_at=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_0.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 0

    @pytest.mark.asyncio
    async def test_notify_staff_for_three_events(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_1.org_id,
            site_id=harness.case_1.site_id,
            room_id=harness.case_1.room_id,
        )
        mock_event_2 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2020, 12, 31, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        mock_event_3 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
            event_type_id="patient_wheels_out",
        )
        await harness.event_store.upsert_events([mock_event_1, mock_event_2, mock_event_3])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_1.case_id)

        mock_phase_2 = mock_phase(
            start_event=mock_event_2, end_event=mock_event_3, case_id=harness.case_0.case_id
        )

        await harness.phase_store.upsert_phases([mock_phase_1, mock_phase_2])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        mock_case_staff_2 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_1.case_id
        )

        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1, mock_case_staff_2])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )

        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="patient_wheels_out",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1, mock_staff_contact_information_2]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                time_window_to_search=datetime.timedelta(days=2),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 3

    @pytest.mark.asyncio
    async def test_notify_staff_for_three_events_one_notified(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_1.org_id,
            site_id=harness.case_1.site_id,
            room_id=harness.case_1.room_id,
        )
        mock_event_2 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2020, 12, 31, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        mock_event_3 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
            event_type_id="patient_wheels_out",
        )
        await harness.event_store.upsert_events([mock_event_1, mock_event_2, mock_event_3])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_1.case_id)

        mock_phase_2 = mock_phase(
            start_event=mock_event_2, end_event=mock_event_3, case_id=harness.case_0.case_id
        )

        await harness.phase_store.upsert_phases([mock_phase_1, mock_phase_2])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        mock_case_staff_2 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_1.case_id
        )

        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1, mock_case_staff_2])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )

        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
            event_type_id="patient_wheels_out",
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1, mock_staff_contact_information_2]
        )

        mock_notification_1 = mock_staff_event_notification(
            event_id=mock_event_1.id,
            event_time=mock_event_1.start_time,
            staff_event_notification_contact_information_id=mock_staff_contact_information_1.id,
            case_id=harness.case_1.case_id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            [mock_notification_1]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                time_window_to_search=datetime.timedelta(days=2),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 2

    @pytest.mark.parametrize(
        "notification_time,expected_count",
        [
            (datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc), 0),
            (
                datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc)
                - datetime.timedelta(minutes=15),
                1,
            ),
        ],
    )
    @pytest.mark.asyncio
    async def test_notify_staff_for_one_events_already_notified(
        self,
        notification_time: datetime.datetime,
        expected_count: int,
    ) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_1.org_id,
            site_id=harness.case_1.site_id,
            room_id=harness.case_1.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_1.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_1.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )

        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        mock_notification_1 = mock_staff_event_notification(
            event_id=mock_event_1.id,
            event_time=notification_time,
            staff_event_notification_contact_information_id=mock_staff_contact_information_1.id,
            case_id=harness.case_1.case_id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            [mock_notification_1]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == expected_count

    @pytest.mark.asyncio
    async def test_notify_staff_for_one_event_is_idempotent(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_0.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 1

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 0

    @pytest.mark.asyncio
    @pytest.mark.parametrize("contact_info_idx,expected_count", [(1, 1), (0, 0)])
    async def test_notify_staff_for_one_event_new_signup(
        self, contact_info_idx: int, expected_count: int
    ) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_0.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        mock_contact_information_2 = mock_contact_information(
            contact_information_value="1234567891",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        contact_infos = [mock_contact_information_1, mock_contact_information_2]
        await harness.contact_information_store.upsert_contact_information(contact_infos)

        mock_staff_1 = mock_staff(external_staff_id="staff_1")
        mock_staff_2 = mock_staff(external_staff_id="staff_2")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        mock_case_staff_2 = mock_case_staff(
            staff_id=mock_staff_2.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1, mock_case_staff_2])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 1

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 0

        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=contact_infos[contact_info_idx].id,
            staff_id=mock_staff_2.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_2]
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == expected_count

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 0

    @pytest.mark.asyncio
    async def test_notify_staff_for_one_event_no_time_to_check(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(minutes=4),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_0.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 1

    @pytest.mark.asyncio
    async def test_notify_staff_for_event_after_failure(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(minutes=4),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_0.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            [
                mock_staff_event_notification(
                    staff_event_notification_contact_information_id=mock_staff_contact_information_1.id,
                    event_id=mock_event_1.id,
                    event_time=mock_event_1.start_time,
                    case_id=harness.case_0.case_id,
                    sent_status=SentNotificationStatus.FAILED,
                )
            ]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 1

        notifications = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                site_ids=[harness.site_0.id],
            )
        )
        assert len(notifications) == 1
        assert notifications[0].sent_time is not None
        assert notifications[0].sent_status == SentNotificationStatus.SENT
        assert notifications[0].attempts == 2

    @pytest.mark.asyncio
    async def test_notify_staff_for_event_after_miss(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(minutes=4),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_0.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_1.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notifications(
            [
                mock_staff_event_notification(
                    staff_event_notification_contact_information_id=mock_staff_contact_information_1.id,
                    event_id=mock_event_1.id,
                    event_time=mock_event_1.start_time,
                    case_id=harness.case_0.case_id,
                    sent_status=SentNotificationStatus.MISSED,
                    attempts=0,
                    message_id="missed",
                )
            ]
        )

        notifications_prior = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                site_ids=[harness.site_0.id],
            )
        )
        assert len(notifications_prior) == 1
        assert notifications_prior[0].sent_time is None
        assert notifications_prior[0].sent_status == SentNotificationStatus.MISSED
        assert notifications_prior[0].attempts == 0
        assert notifications_prior[0].message_id == "missed"
        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 1

        notifications = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                site_ids=[harness.site_0.id],
            )
        )
        assert len(notifications) == 1
        assert notifications[0].sent_time is not None
        assert notifications[0].sent_status == SentNotificationStatus.SENT
        assert notifications[0].attempts == 1
        assert notifications[0].message_id != "missed"
