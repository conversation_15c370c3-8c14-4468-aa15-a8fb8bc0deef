import datetime
from typing import Any, Optional, Union
from uuid import UUID
import uuid


from apella_cloud_api.api_server_schema import (
    ContactInformationType,
)
from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_input_schema import (
    GQLStaffEventsNotificationsInput,
    GQLSubscriberEventNotificationUpsertInput,
    GQLSubscriberUpsertInput,
    GQLScheduledCaseQueryInput,
)
from apella_cloud_api.new_schema_generator.schema_generator_base_classes import Empty
from api_server.services.case.case_staff_store import CaseStaffModel
from api_server.services.contact_information.contact_information_store import (
    ContactInformationModel,
    ContactInformationTypeGraphene,
)
from api_server.services.contact_information.staff_event_notification_contact_information_store import (
    EventNotificationQueryDto,
    StaffEventNotificationContactInformationModel,
)
from api_server.services.events.event_models import EventModel
from api_server.services.phases.phase_store import PhaseModel, PhaseStatus
from tests_component import harness
from tests_component.contact_information.graphql.mock_helpers import mock_staff
from utils.helpers import convert_str_to_uuid


def mock_phase(
    start_event: EventModel,
    case_id: Optional[str] = None,
    type_id: str = "CASE",
    end_event: Optional[EventModel] = None,
    source_type: str = "unified",
) -> PhaseModel:
    phase = PhaseModel()
    phase.start_event_id = start_event.id
    phase.type_id = type_id
    if end_event:
        phase.end_event_id = end_event.id
    phase.case_id = case_id
    phase.source_type = source_type
    phase.org_id = start_event.org_id
    phase.site_id = start_event.site_id
    phase.room_id = start_event.room_id
    phase.status = PhaseStatus.VALID
    return phase


def mock_event(
    event_id: str,
    start_time: datetime.datetime,
    org_id: str,
    site_id: str,
    room_id: str,
    event_type_id: str = "patient_wheels_in",
    source_type: str = "prediction",
    model_version: str = "automated/event-transformer-2024-06-15T00:21:18.605713+00:00",
    created_time: datetime.datetime = datetime.datetime(
        2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc
    ),
    confidence: float = 0.5,
    deleted_at: Optional[datetime.datetime] = None,
) -> EventModel:
    event = EventModel()
    event.id = event_id
    event.created_time = created_time
    event.event_type_id = event_type_id
    event.start_time = start_time
    event.process_timestamp = start_time
    event.source = "test"
    event.source_type = source_type
    event.model_version = model_version
    event.confidence = confidence
    event.org_id = org_id
    event.site_id = site_id
    event.room_id = room_id
    event.deleted_at = deleted_at
    return event


def mock_case_staff(
    staff_id: uuid.UUID,
    case_id: str,
) -> CaseStaffModel:
    case_staff = CaseStaffModel()
    case_staff.staff_id = staff_id
    case_staff.case_id = case_id
    case_staff.role = "Primary"
    return case_staff


def mock_contact_information(
    contact_information_value: str,
    contact_information_type: ContactInformationType = ContactInformationType.PHONE_NUMBER,
) -> ContactInformationModel:
    contact_info = ContactInformationModel()
    contact_info.id = uuid.uuid4()
    contact_info.contact_information_value = contact_information_value
    contact_info.type = contact_information_type
    contact_info.first_name = "Johnny"
    contact_info.last_name = "Tsunami"
    contact_info.is_apella_employee = True
    return contact_info


def mock_second_contact_information(
    contact_information_value: str,
    contact_information_type: ContactInformationType = ContactInformationType.PHONE_NUMBER,
) -> ContactInformationModel:
    contact_info = ContactInformationModel()
    contact_info.id = uuid.uuid4()
    contact_info.contact_information_value = contact_information_value
    contact_info.type = contact_information_type
    contact_info.first_name = "John"
    contact_info.last_name = "Doe"
    contact_info.is_apella_employee = True
    return contact_info


def mock_staff_contact_information(
    contact_information_id: Any, staff_id: Any, event_type_id: str = "patient_wheels_in"
) -> StaffEventNotificationContactInformationModel:
    staff_contact_information = StaffEventNotificationContactInformationModel()
    staff_contact_information.contact_information_id = convert_str_to_uuid(contact_information_id)
    staff_contact_information.staff_id = convert_str_to_uuid(staff_id)
    staff_contact_information.event_type_id = event_type_id
    staff_contact_information.id = uuid.uuid4()
    return staff_contact_information


def create_subscriber_event_notification_upsert_input(
    staff_id: UUID,
    event_type: str = "patient_wheels_in",
    archive_raw: Optional[bool] = False,
) -> GQLSubscriberEventNotificationUpsertInput:
    archive: Union[bool, Empty] = archive_raw if archive_raw is not None else Empty()
    return GQLSubscriberEventNotificationUpsertInput(
        staff_id=staff_id, event_type_id=event_type, archive=archive
    )


def create_subscriber_input(
    contact_information_value: str,
    subscriptions: list[GQLSubscriberEventNotificationUpsertInput],
    is_apella_employee: Union[bool, Empty],
    first_name: str = "Johnny",
    last_name: str = "Tsunami",
    contact_information_id: Optional[UUID] = None,
) -> GQLSubscriberUpsertInput:
    return GQLSubscriberUpsertInput(
        contact_information_id=contact_information_id if contact_information_id else Empty(),
        contact_information_value=contact_information_value,
        type=ContactInformationTypeGraphene.PHONE_NUMBER,
        is_apella_employee=is_apella_employee,
        first_name=first_name,
        last_name=last_name,
        subscriptions=subscriptions,
    )


class TestEventContactInformation:
    async def test_staff_event_notification_contains_all_data(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_0.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_member_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_member_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_member_1.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 1

        notifications_results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                case_ids=[harness.case_0.case_id],
            )
        )

        assert notifications_results

        apella_schema = ApellaSchema()

        querySchedule = apella_schema.Query.scheduled_cases.args(
            query=GQLScheduledCaseQueryInput(
                case_ids=[harness.case_0.case_id],
            )
        ).select(
            apella_schema.ScheduledCaseConnection.edges.select(
                apella_schema.ScheduledCaseEdge.node.select(
                    apella_schema.ScheduledCase.event_notifications.select(
                        apella_schema.EventNotification.sent_time,
                        apella_schema.EventNotification.event.select(apella_schema.Event.name),
                        apella_schema.EventNotification.staff_event_contact_information.select(
                            apella_schema.StaffEventNotificationContactInformation.contact_information.select(
                                apella_schema.ContactInformation.contact_information_value,
                                apella_schema.ContactInformation.first_name,
                                apella_schema.ContactInformation.last_name,
                                apella_schema.ContactInformation.is_apella_employee,
                            )
                        ),
                    )
                )
            )
        )
        query_results = harness.service_account_client.query_graphql_from_schema(querySchedule)
        event_notifications = query_results.scheduled_cases.edges[0].node.event_notifications
        if isinstance(event_notifications, list) and event_notifications:
            data = event_notifications[0] if event_notifications[0] else None
            if data is not None:
                assert data.event.name == "patient_wheels_in"
                assert (
                    data.staff_event_contact_information.contact_information.contact_information_value
                    == "**********"
                )
                assert (
                    data.staff_event_contact_information.contact_information.first_name == "Johnny"
                )
                assert (
                    data.staff_event_contact_information.contact_information.last_name == "Tsunami"
                )
                assert (
                    data.staff_event_contact_information.contact_information.is_apella_employee
                    is True
                )
                assert data.sent_time is not None

    async def test_multiple_cases_and_staff_event_notifications(self) -> None:
        mock_event_1 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_0.org_id,
            site_id=harness.case_0.site_id,
            room_id=harness.case_0.room_id,
        )
        await harness.event_store.upsert_events([mock_event_1])
        mock_phase_1 = mock_phase(start_event=mock_event_1, case_id=harness.case_0.case_id)

        await harness.phase_store.upsert_phases([mock_phase_1])

        mock_contact_information_1 = mock_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_1]
        )

        mock_staff_member_1 = mock_staff(external_staff_id="staff_1")

        mock_case_staff_1 = mock_case_staff(
            staff_id=mock_staff_member_1.id, case_id=harness.case_0.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_1])

        mock_staff_contact_information_1 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_1.id,
            staff_id=mock_staff_member_1.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_1]
        )

        mock_event_2 = mock_event(
            event_id=str(uuid.uuid4()),
            start_time=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            confidence=0.99,
            org_id=harness.case_1.org_id,
            site_id=harness.case_1.site_id,
            room_id=harness.case_1.room_id,
        )
        await harness.event_store.upsert_events([mock_event_2])
        mock_phase_2 = mock_phase(start_event=mock_event_2, case_id=harness.case_1.case_id)

        await harness.phase_store.upsert_phases([mock_phase_2])

        mock_contact_information_2 = mock_second_contact_information(
            contact_information_value="**********",
            contact_information_type=ContactInformationType.PHONE_NUMBER,
        )
        await harness.contact_information_store.upsert_contact_information(
            [mock_contact_information_2]
        )

        mock_staff_member_2 = mock_staff(external_staff_id="staff_2")

        mock_case_staff_2 = mock_case_staff(
            staff_id=mock_staff_member_2.id, case_id=harness.case_1.case_id
        )
        await harness.case_staff_store.upsert_case_staff([mock_case_staff_2])

        mock_staff_contact_information_2 = mock_staff_contact_information(
            contact_information_id=mock_contact_information_2.id,
            staff_id=mock_staff_member_2.id,
        )
        await harness.staff_event_notification_contact_information_store.upsert_staff_event_notification_contact_information(
            [mock_staff_contact_information_2]
        )

        apella_schema = ApellaSchema()
        mutation = apella_schema.Mutation.notify_staff_for_events.args(
            input=GQLStaffEventsNotificationsInput(
                site_id=harness.site_0.id,
                time_to_check=datetime.datetime(2021, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            )
        ).select(
            apella_schema.NotifyStaffForEvents.success,
            apella_schema.NotifyStaffForEvents.sent_count,
        )

        result = harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert result.notify_staff_for_events.success is True
        assert result.notify_staff_for_events.sent_count == 2

        notifications_results = await harness.staff_event_notification_contact_information_store.query_staff_event_notifications(
            EventNotificationQueryDto(
                case_ids=[harness.case_0.case_id, harness.case_1.case_id],
            )
        )

        assert notifications_results

        apella_schema = ApellaSchema()

        querySchedule = apella_schema.Query.scheduled_cases.args(
            query=GQLScheduledCaseQueryInput(
                case_ids=[harness.case_0.case_id, harness.case_1.case_id],
            )
        ).select(
            apella_schema.ScheduledCaseConnection.edges.select(
                apella_schema.ScheduledCaseEdge.node.select(
                    apella_schema.ScheduledCase.event_notifications.select(
                        apella_schema.EventNotification.sent_time,
                        apella_schema.EventNotification.event.select(apella_schema.Event.name),
                        apella_schema.EventNotification.staff_event_contact_information.select(
                            apella_schema.StaffEventNotificationContactInformation.contact_information.select(
                                apella_schema.ContactInformation.contact_information_value,
                                apella_schema.ContactInformation.first_name,
                                apella_schema.ContactInformation.last_name,
                                apella_schema.ContactInformation.is_apella_employee,
                            )
                        ),
                    )
                )
            )
        )
        query_results = harness.service_account_client.query_graphql_from_schema(querySchedule)
        event_notifications = query_results.scheduled_cases.edges[0].node.event_notifications
        event_notifications2 = query_results.scheduled_cases.edges[1].node.event_notifications

        if isinstance(event_notifications, list) and event_notifications:
            data = event_notifications[0] if event_notifications[0] else None
            data2 = event_notifications2[0] if event_notifications2[0] else None
            if data is not None and data2 is not None:
                assert data.event.name == "patient_wheels_in"
                assert (
                    data.staff_event_contact_information.contact_information.contact_information_value
                    == "**********"
                )
                assert (
                    data.staff_event_contact_information.contact_information.first_name == "Johnny"
                )
                assert (
                    data.staff_event_contact_information.contact_information.last_name == "Tsunami"
                )
                assert (
                    data.staff_event_contact_information.contact_information.is_apella_employee
                    is True
                )
                assert data.sent_time is not None

                assert data2.event.name == "patient_wheels_in"
                assert (
                    data2.staff_event_contact_information.contact_information.contact_information_value
                    == "**********"
                )
                assert (
                    data2.staff_event_contact_information.contact_information.first_name == "John"
                )
                assert data2.staff_event_contact_information.contact_information.last_name == "Doe"
                assert (
                    data2.staff_event_contact_information.contact_information.is_apella_employee
                    is True
                )
                assert data2.sent_time is not None
