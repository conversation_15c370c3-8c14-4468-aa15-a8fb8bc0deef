import json

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from apella_cloud_api.exceptions import NotAuthorized, NotFound
from tests_component import harness

ROOM_EXTERNAL_ID_TYPE = "redox_room"
ROOM_EXTERNAL_ID = "NB-OR1"
ROOM_ID = "room_0"


class TestMappingGet:
    def test_get_mapping_requires_auth(self) -> None:
        with pytest.raises(NotAuthorized):
            harness.no_auth_client.get_mapping(ROOM_EXTERNAL_ID_TYPE, ROOM_EXTERNAL_ID)

    def test_get_mapping_requires_permissions(self) -> None:
        with pytest.raises(NotAuthorized):
            harness.user_without_permissions_client.get_mapping(
                ROOM_EXTERNAL_ID_TYPE, ROOM_EXTERNAL_ID
            )

    @pytest.mark.asyncio
    async def test_get_mapping_as_service_account(self, async_session: AsyncSession) -> None:
        await harness.mapping_store.set_mapping(
            external_id_type=ROOM_EXTERNAL_ID_TYPE,
            external_id=ROOM_EXTERNAL_ID,
            internal_id=ROOM_ID,
            session=async_session,
        )
        result = harness.service_account_client.get_mapping(ROOM_EXTERNAL_ID_TYPE, ROOM_EXTERNAL_ID)

        assert result == ROOM_ID

    @pytest.mark.asyncio
    async def test_get_nonexistent_mapping_as_service_account(
        self, async_session: AsyncSession
    ) -> None:
        await harness.mapping_store.set_mapping(
            external_id_type=ROOM_EXTERNAL_ID_TYPE,
            external_id=ROOM_EXTERNAL_ID,
            internal_id=ROOM_ID,
            session=async_session,
        )
        with pytest.raises(NotFound):
            harness.service_account_client.get_mapping(ROOM_EXTERNAL_ID_TYPE, "bad id")

    @pytest.mark.asyncio
    async def test_get_nonexistent_room_mapping_as_service_account(
        self, async_session: AsyncSession
    ) -> None:
        await harness.mapping_store.set_mapping(
            external_id_type=ROOM_EXTERNAL_ID_TYPE,
            external_id=ROOM_EXTERNAL_ID,
            internal_id=ROOM_ID,
            session=async_session,
        )
        with pytest.raises(NotFound):
            harness.service_account_client.get_mapping(
                f"{ROOM_EXTERNAL_ID_TYPE}::room_mapping", "bad id"
            )

    @pytest.mark.asyncio
    async def test_get_nonexistent_room_mapping_as_service_account_v2(
        self, async_session: AsyncSession
    ) -> None:
        await harness.mapping_store.set_mapping(
            external_id_type=ROOM_EXTERNAL_ID_TYPE,
            external_id=ROOM_EXTERNAL_ID,
            internal_id=ROOM_ID,
            session=async_session,
        )
        result = json.loads(
            harness.service_account_client.get_mapping_v2(
                f"{ROOM_EXTERNAL_ID_TYPE}::room_mapping", "bad id"
            )
        )
        assert result == {
            "error": "No room mapping found with external id: redox_room::room_mapping bad id",
        }
