from typing import Async<PERSON>enerator
from uuid import uuid4

import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession

from api_server.services.events.event_models import (
    EventLabelOptionModel,
)
from tests_component import harness


@pytest_asyncio.fixture
async def event_label_option_id() -> str:
    return str(uuid4())


@pytest_asyncio.fixture
async def event_label_option_name() -> str:
    return "one_simple_label"


@pytest_asyncio.fixture
async def new_event_label_option(
    event_label_option_id: str,
    event_label_option_name: str,
    async_session: AsyncSession,
) -> AsyncGenerator[EventLabelOptionModel, None]:
    event_label: EventLabelOptionModel = await harness.event_store.create_event_label_option(
        session=async_session,
        id=event_label_option_id,
        name=event_label_option_name,
    )
    yield event_label

    await harness.event_store.delete_event_label_option(
        session=async_session,
        id=event_label_option_id,
    )
