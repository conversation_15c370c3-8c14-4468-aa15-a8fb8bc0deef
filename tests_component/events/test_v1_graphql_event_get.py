import datetime
import uuid

import pytest
from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_input_schema import GQLRoomEventSearchInput
from api_server.services.events.event_store import EventModel
from tests_component import harness


@pytest.mark.asyncio
async def mock_event() -> EventModel:
    new_event = EventModel()
    new_event.id = str(uuid.uuid4())
    new_event.event_type_id = "patient_wheels_out"
    new_event.source_type = "human_gt"
    new_event.source = "component-test"
    new_event.org_id = harness.org_0.id
    new_event.site_id = harness.site_0.id
    new_event.room_id = harness.room_1.id
    new_event.camera_id = harness.camera_0.id
    new_event.start_time = datetime.datetime.fromisoformat("2021-03-01T12:01:02.345+00:00")
    new_event.process_timestamp = new_event.start_time
    new_event.updated_time = new_event.process_timestamp
    new_event.labels = ["Needs Review", "Test"]
    new_event.confidence = 0.7
    await harness.event_store.create_event(new_event)
    return new_event


def test_get_event_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        f"""
    query {{
        event(id: "{harness.event_0.id}") {{
            id
            name
        }}
    }}
    """
    )
    assert result == {
        "data": {"event": None},
        "errors": [
            {
                "path": ["event"],
                "locations": [{"line": 3, "column": 9}],
                "message": "Not Authorized: No authorization found",
            }
        ],
    }


def test_get_room_events() -> None:
    result = harness.hospital_administrator_client.execute_graphql(
        f"""
    query {{
        room(id: "{harness.room_0.id}") {{
            events(minTime: "2021-03-01T00:00:00", maxTime: "2021-03-02T00:00:00") {{
                id
                type
                name
                source
                cameraId
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "room": {
                "events": [
                    {
                        "id": harness.event_3.id,
                        "type": "patient_status",
                        "name": harness.event_3.event_type_id,
                        "source": harness.event_3.source,
                        "cameraId": harness.event_3.camera_id,
                    }
                ]
            }
        }
    }


@pytest.mark.asyncio
async def test_get_rooms_events() -> None:
    new_event = await mock_event()
    apella_schema = ApellaSchema()
    query = apella_schema.Query.rooms.args(site_id=harness.site_0.id).select(
        apella_schema.RoomConnection.edges.select(
            apella_schema.RoomEdge.node.select(
                apella_schema.Room.events.args(
                    min_time=datetime.datetime(2021, 3, 1, 0, 0, 0),
                    max_time=datetime.datetime(2021, 3, 2, 0, 0, 0),
                ).select(
                    apella_schema.Event.id,
                    apella_schema.Event.type,
                    apella_schema.Event.name,
                    apella_schema.Event.source,
                    apella_schema.Event.camera_id,
                    apella_schema.Event.updated_time,
                ),
                apella_schema.Room.id,
            )
        )
    )

    result = harness.hospital_administrator_client.query_graphql_from_schema(query)
    rooms = [edge.node for edge in result.rooms.edges]
    rooms.sort(key=lambda room: room.id)
    assert len(rooms) == 2
    assert rooms[0].events[0].id == harness.event_3.id
    assert rooms[0].events[0].type == "patient_status"
    assert rooms[0].events[0].name == harness.event_3.event_type_id
    assert rooms[0].events[0].source == harness.event_3.source
    assert rooms[0].events[0].camera_id == harness.event_3.camera_id
    assert rooms[0].events[0].updated_time == harness.event_3.updated_time

    assert rooms[1].events[0].id == new_event.id
    assert rooms[1].events[0].type == "patient_status"
    assert rooms[1].events[0].name == new_event.event_type_id
    assert rooms[1].events[0].source == new_event.source
    assert rooms[1].events[0].camera_id == new_event.camera_id
    assert rooms[1].events[0].updated_time == new_event.updated_time


@pytest.mark.asyncio
async def test_get_rooms_events_using_new_interface() -> None:
    new_event = await mock_event()
    apella_schema = ApellaSchema()
    query = apella_schema.Query.rooms.args(site_id=harness.site_0.id).select(
        apella_schema.RoomConnection.edges.select(
            apella_schema.RoomEdge.node.select(
                apella_schema.Room.room_events.args(
                    query=GQLRoomEventSearchInput(
                        min_time=datetime.datetime(2021, 3, 1, 0, 0, 0),
                        max_time=datetime.datetime(2021, 3, 2, 0, 0, 0),
                    )
                ).select(
                    apella_schema.EventConnection.edges.select(
                        apella_schema.EventEdge.node.select(
                            apella_schema.Event.id,
                            apella_schema.Event.type,
                            apella_schema.Event.name,
                            apella_schema.Event.source,
                            apella_schema.Event.camera_id,
                        )
                    )
                ),
                apella_schema.Room.id,
            )
        )
    )

    result = harness.hospital_administrator_client.query_graphql_from_schema(query)
    rooms = [edge.node for edge in result.rooms.edges]
    rooms.sort(key=lambda room: room.id)
    assert len(rooms) == 2
    event_0 = rooms[0].room_events.edges[0].node
    assert event_0.id == harness.event_3.id
    assert event_0.type == "patient_status"
    assert event_0.name == harness.event_3.event_type_id
    assert event_0.source == harness.event_3.source
    assert event_0.camera_id == harness.event_3.camera_id

    event_1 = rooms[1].room_events.edges[0].node
    assert event_1.id == new_event.id
    assert event_1.type == "patient_status"
    assert event_1.name == new_event.event_type_id
    assert event_1.source == new_event.source
    assert event_1.camera_id == new_event.camera_id


def test_get_rooms_events_by_name() -> None:
    result = harness.hospital_administrator_client.execute_graphql(
        f"""
    query {{
        room(id: "{harness.room_0.id}") {{
            events(minTime: "2021-03-01T00:00:00", maxTime: "2021-04-02T00:00:00", eventNames: ["{harness.event_3.event_type_id}"]) {{
                id
                type
                name
                source
                cameraId
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "room": {
                "events": [
                    {
                        "id": harness.event_3.id,
                        "type": "patient_status",
                        "name": harness.event_3.event_type_id,
                        "source": harness.event_3.source,
                        "cameraId": harness.event_3.camera_id,
                    }
                ]
            }
        }
    }
