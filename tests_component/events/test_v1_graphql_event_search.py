from typing import Any, Dict
import pytest

from tests_component import harness


def test_query_event_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        f"""
    query {{
        eventSearch(query: {{ minStartTime: "2021-03-01T00:00:00",
                              maxStartTime: "2021-03-02T00:00:00" }}) {{
            edges {{
                node {{
                    id
                    type
                    name
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": None,
        "errors": [
            {
                "path": ["eventSearch"],
                "locations": [{"line": 3, "column": 9}],
                "message": "Not Authorized: No authorization found",
            }
        ],
    }


def test_query_events_as_phase_slicer() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        eventSearch(query: {{ minStartTime: "2021-03-01T00:00:00",
                              maxStartTime: "2021-03-02T00:00:00" }}) {{
            edges {{
                node {{
                    id
                    type
                    name
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "eventSearch": {
                "edges": [
                    {
                        "node": {
                            "id": harness.event_3.id,
                            "type": "patient_status",
                            "name": harness.event_3.event_type_id,
                        }
                    }
                ]
            }
        }
    }


def test_query_events_as_phase_slicer_using_labels() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        eventSearch(query: {{ minStartTime: "2021-03-01T00:00:00",
                              maxStartTime: "2021-03-02T00:00:00",
                              labels: ["Needs Review"] }}) {{
            edges {{
                node {{
                    id
                    type
                    name
                    confidence
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "eventSearch": {
                "edges": [
                    {
                        "node": {
                            "id": harness.event_3.id,
                            "type": "patient_status",
                            "name": harness.event_3.event_type_id,
                            "confidence": 0.7,
                        }
                    }
                ]
            }
        }
    }


def sort_edges_by_id(edges: list[Dict[str, Dict[str, Any]]]) -> list[Dict[str, Dict[str, Any]]]:
    return sorted(edges, key=lambda edge: edge["node"]["id"])


def test_query_events_as_phase_slicer_using_exclude() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        eventSearch(query: {{ minStartTime: "2021-03-01T00:00:00",
                              maxStartTime: "2021-04-30T00:00:00",
                              excludeEventNames: ["patient_xfer_to_bed"] }}) {{
            edges {{
                node {{
                    id
                    type
                    name
                }}
            }}
        }}
    }}
    """
    )

    actual_edges = sort_edges_by_id(result["data"]["eventSearch"]["edges"])
    expected_edges = sort_edges_by_id(
        [
            {
                "node": {
                    "id": harness.event_3.id,
                    "type": "patient_status",
                    "name": harness.event_3.event_type_id,
                }
            },
            {
                "node": {
                    "id": harness.event_5.id,
                    "type": "patient_status",
                    "name": harness.event_5.event_type_id,
                }
            },
            {
                "node": {
                    "id": harness.event_ca_2.id,
                    "type": "patient_status",
                    "name": harness.event_ca_2.event_type_id,
                }
            },
            {
                "node": {
                    "id": harness.event_ca.id,
                    "type": "patient_status",
                    "name": harness.event_ca.event_type_id,
                }
            },
        ]
    )

    assert actual_edges == expected_edges


@pytest.mark.asyncio
async def test_query_events_dashboard_only() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        eventSearch(query: {{
            minStartTime: "2021-05-01T00:00:00",
            maxStartTime: "2021-05-02T00:00:00",
            includeDashboardEventsOnly: true
        }}) {{
            edges {{
                node {{
                    id
                    name
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "eventSearch": {
                "edges": [
                    {
                        "node": {
                            "id": harness.event_6.id,
                            "name": "patient_wheels_in",
                        }
                    }
                ]
            }
        }
    }


def test_query_events_as_phase_slicer_using_event_names() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        eventSearch(query: {{
            minStartTime: "2021-03-01T00:00:00",
            maxStartTime: "2021-03-02T00:00:00",
            eventNames: ["patient_wheels_out"]
        }}) {{
            edges {{
                node {{
                    id
                    type
                    name
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "eventSearch": {
                "edges": [
                    {
                        "node": {
                            "id": harness.event_3.id,
                            "type": "patient_status",
                            "name": harness.event_3.event_type_id,
                        }
                    }
                ]
            }
        }
    }


def test_query_events_pagination() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
        query {{
            eventSearch(
                query: {{ minStartTime: "2021-03-01T00:00:00",
                          maxStartTime: "2021-04-02T00:00:00",
                          labels: ["Needs Review"] }},
                first: 1
            ) {{
                pageCursors {{
                  previous {{
                    cursor
                  }}
                  first {{
                    cursor
                    page
                  }}
                  around {{
                    cursor
                    isCurrent
                    page
                  }}
                  last {{
                    cursor
                    page
                  }}
                  next {{
                    cursor
                  }}
                }}
                totalRecords
                edges {{
                    node {{
                        id
                        type
                        name
                    }}
                }}
            }}
        }}
        """
    )

    result2 = harness.labeler_client.execute_graphql(
        f"""
        query {{
            eventSearch(
                query: {{ minStartTime: "2021-03-01T00:00:00",
                          maxStartTime: "2021-04-02T00:00:00",
                          labels: ["Needs Review"] }},
                first: 1
                after: "YXJyYXljb25uZWN0aW9uOjA="
            ) {{
                pageCursors {{
                  previous {{
                    cursor
                  }}
                  first {{
                    cursor
                    page
                  }}
                  around {{
                    cursor
                    isCurrent
                    page
                  }}
                  last {{
                    cursor
                    page
                  }}
                  next {{
                    cursor
                  }}
                }}
                totalRecords
                edges {{
                    node {{
                        id
                        type
                        name
                    }}
                }}
            }}
        }}
        """
    )

    assert result == {
        "data": {
            "eventSearch": {
                "edges": [
                    {
                        "node": {
                            "id": "62b798bd-bc10-4df9-bee1-9b2fda2bc0f0",
                            "type": "patient_status",
                            "name": "patient_wheels_out",
                        }
                    }
                ],
                "pageCursors": {
                    "around": [
                        {
                            "cursor": "YXJyYXljb25uZWN0aW9uOi0x",
                            "isCurrent": True,
                            "page": 1,
                        },
                        {
                            "cursor": "YXJyYXljb25uZWN0aW9uOjA=",
                            "isCurrent": False,
                            "page": 2,
                        },
                    ],
                    "first": None,
                    "last": None,
                    "next": {"cursor": "YXJyYXljb25uZWN0aW9uOjA="},
                    "previous": None,
                },
                "totalRecords": 2,
            }
        }
    }

    assert result2 == {
        "data": {
            "eventSearch": {
                "edges": [
                    {
                        "node": {
                            "id": "d61d3c2a-a6d6-4c9b-9d63-ed1175952145",
                            "type": "patient_status",
                            "name": "patient_xfer_to_bed",
                        }
                    }
                ],
                "pageCursors": {
                    "around": [
                        {
                            "cursor": "YXJyYXljb25uZWN0aW9uOi0x",
                            "isCurrent": False,
                            "page": 1,
                        },
                        {
                            "cursor": "YXJyYXljb25uZWN0aW9uOjA=",
                            "isCurrent": True,
                            "page": 2,
                        },
                    ],
                    "first": None,
                    "last": None,
                    "next": None,
                    "previous": {"cursor": "YXJyYXljb25uZWN0aW9uOi0x"},
                },
                "totalRecords": 2,
            }
        }
    }


def test_query_events_infinite_scroll() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
        query {{
            eventSearch(
                query: {{ minStartTime: "2021-03-01T00:00:00",
                          maxStartTime: "2021-04-02T00:00:00",
                          labels: ["Needs Review"] }},
                first: 1
            ) {{
                pageInfo {{
                    hasNextPage
                    startCursor
                    endCursor
                }}
                edges {{
                    node {{
                        id
                        type
                        name
                    }}
                }}
            }}
        }}
        """
    )

    result2 = harness.labeler_client.execute_graphql(
        f"""
        query {{
            eventSearch(
                query: {{ minStartTime: "2021-03-01T00:00:00",
                          maxStartTime: "2021-04-02T00:00:00",
                          labels: ["Needs Review"] }},
                first: 1
                after: "YXJyYXljb25uZWN0aW9uOjA="
            ) {{
                pageInfo {{
                    hasNextPage
                    startCursor
                    endCursor
                }}
                edges {{
                    node {{
                        id
                        type
                        name
                    }}
                }}
            }}
        }}
        """
    )

    assert result == {
        "data": {
            "eventSearch": {
                "pageInfo": {
                    "hasNextPage": True,
                    "startCursor": "YXJyYXljb25uZWN0aW9uOjA=",
                    "endCursor": "YXJyYXljb25uZWN0aW9uOjA=",
                },
                "edges": [
                    {
                        "node": {
                            "id": harness.event_3.id,
                            "type": "patient_status",
                            "name": harness.event_3.event_type_id,
                        }
                    }
                ],
            }
        }
    }

    assert result2 == {
        "data": {
            "eventSearch": {
                "pageInfo": {
                    "hasNextPage": False,
                    "startCursor": "YXJyYXljb25uZWN0aW9uOjE=",
                    "endCursor": "YXJyYXljb25uZWN0aW9uOjE=",
                },
                "edges": [
                    {
                        "node": {
                            "id": harness.event_4.id,
                            "type": "patient_status",
                            "name": harness.event_4.event_type_id,
                        }
                    }
                ],
            }
        }
    }


def test_query_event_types_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        f"""
    query {{
        eventTypes {{
            edges {{
                node {{
                    id
                    type
                    name
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": {"eventTypes": None},
        "errors": [
            {
                "path": ["eventTypes"],
                "locations": [{"column": 9, "line": 3}],
                "message": "Not Authorized: No authorization found",
            }
        ],
    }


def test_query_event_types_without_permissions() -> None:
    result = harness.user_without_permissions_client.execute_graphql(
        f"""
    query {{
        eventTypes {{
            edges {{
                node {{
                    id
                    type
                    name
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": {"eventTypes": None},
        "errors": [
            {
                "path": ["eventTypes"],
                "locations": [{"column": 9, "line": 3}],
                "message": "User does not have permission 'event:read:any'",
            }
        ],
    }


def test_query_event_types_success() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        eventTypes(ids: ["patient_wheels_in"]) {{
            edges {{
                node {{
                    id
                    type
                    name
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "eventTypes": {
                "edges": [
                    {
                        "node": {
                            "id": "patient_wheels_in",
                            "name": "Patient wheels in",
                            "type": "patient_status",
                        }
                    }
                ]
            }
        }
    }

    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        eventTypes(types:["mop_status"]) {{
            edges {{
                node {{
                    id
                    type
                    name
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "eventTypes": {
                "edges": [
                    {
                        "node": {
                            "id": "mop_in",
                            "name": "Mop in",
                            "type": "mop_status",
                        }
                    },
                    {
                        "node": {
                            "id": "mop_out",
                            "name": "Mop out",
                            "type": "mop_status",
                        }
                    },
                ]
            }
        }
    }
