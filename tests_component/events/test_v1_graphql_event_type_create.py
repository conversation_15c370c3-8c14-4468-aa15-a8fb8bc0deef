import pytest

from api_server.services.events.event_models import is_valid_uuid
from api_server.services.events.event_store import EventStore
from tests_component import harness


def _get_event_type_create_mutation(
    name: str = "my_event_type_name",
    description: str = "very helpful description",
    type: str = "uncategorized",
    color: str = "#000000",
    hidden: bool = False,
) -> str:
    return f"""
    mutation {{
      eventTypeCreate(input: {{
                        name: "{name}",
                        type: "{type}",
                        description: "{description}",
                        color: "{color}",
                        hidden: {"false" if hidden else "true"}
                        }}) {{
            success
            createdEventType {{
                id
                name
                type
                description
                color
                hidden
            }}
        }}
    }}
    """


def test_create_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(_get_event_type_create_mutation())

    assert result == {
        "data": {"eventTypeCreate": None},
        "errors": [
            {
                "path": ["eventTypeCreate"],
                "locations": [{"line": 3, "column": 7}],
                "message": "Not Authorized: No authorization found",
            }
        ],
    }


@pytest.mark.asyncio
async def test_create_missing_type() -> None:
    num_event_types_before = len(await EventStore().get_event_types())

    result = harness.labeler_client.execute_graphql(_get_event_type_create_mutation(type=""))

    assert len(result["errors"]) > 0
    assert num_event_types_before == len(await EventStore().get_event_types())


@pytest.mark.asyncio
async def test_create_valid_type() -> None:
    result = harness.label_reviewer_client.execute_graphql(
        _get_event_type_create_mutation(
            name=" My surgery type ", description=" very helpful description "
        )
    )

    event_type_create = result["data"]["eventTypeCreate"]
    assert event_type_create["success"]

    created_event_type = event_type_create["createdEventType"]

    assert len(created_event_type["id"]) > 0
    assert is_valid_uuid(created_event_type["id"])
    assert created_event_type["name"] == "My surgery type"
    assert created_event_type["description"] == "very helpful description"

    event_type = await EventStore().get_event_type(created_event_type["id"])
    assert event_type.id == created_event_type["id"]
