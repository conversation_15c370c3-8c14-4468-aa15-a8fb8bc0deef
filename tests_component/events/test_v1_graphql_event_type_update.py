import pytest

from api_server.services.events.event_store import EventStore
from tests_component import harness


def test_update_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        f"""
    mutation {{
      eventTypeUpdate(input: {{
                        id: "{harness.event_type_0.id}",
                        name: "My event type",
                        description: "very helpful description"
                        }}) {{
            success
            updatedEventType {{
                id
                name
                type
                description
                hidden
                color
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {"eventTypeUpdate": None},
        "errors": [
            {
                "path": ["eventTypeUpdate"],
                "locations": [{"line": 3, "column": 7}],
                "message": "Not Authorized: No authorization found",
            }
        ],
    }


def test_update_missing_id() -> None:
    new_name = "My event type"
    error = harness.labeler_client.execute_graphql(
        f"""
    mutation {{
    eventTypeUpdate(input: {{
                        name: "{new_name}",
                        }}) {{
            success
            updatedEventType {{
                id
                name
                type
                description
                color
                hidden
            }}
        }}
    }}
    """
    )
    assert error == {
        "data": None,
        "errors": [
            {
                "locations": [{"column": 28, "line": 3}],
                "message": "Field 'EventTypeUpdateInput.id' of required type 'ID!' was not provided.",
            }
        ],
    }


@pytest.mark.asyncio
async def test_update() -> None:
    result = harness.label_reviewer_client.execute_graphql(
        f"""
    mutation {{
      eventTypeUpdate(input: {{
                        id: "{harness.event_type_0.id}",
                        name: "Newer, better name",
                        description: "Newer, more helpful description"
                        }}) {{
            success
            updatedEventType {{
                id
                name
                type
                description
                color
                hidden
            }}
        }}
    }}
    """
    )

    event_type_update = result["data"]["eventTypeUpdate"]
    assert event_type_update["success"]

    updated_event_type = event_type_update["updatedEventType"]
    assert updated_event_type["id"] == str(harness.event_type_0.id)
    assert updated_event_type["name"] == "Newer, better name"
    assert updated_event_type["description"] == "Newer, more helpful description"

    event_type = await EventStore().get_event_type(str(harness.event_type_0.id))
    assert event_type.name == "Newer, better name"
    assert event_type.id == harness.event_type_0.id
