from datetime import time
from api_server.services.first_case_config.first_case_config_store import RoomFirstCaseConfig
import pytest


from tests_component import harness


@pytest.mark.asyncio
async def test_room_first_case_config_delete_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        ROOM_FIRST_CASE_CONFIG_DELETE_MUTATION,
        variables={"input": {"roomId": harness.room_0.id}},
    )

    assert result["data"]["roomFirstCaseConfigDelete"] is None
    assert result["errors"][0]["path"] == ["roomFirstCaseConfigDelete"]
    assert result["errors"][0]["message"] == "Not Authorized: No authorization found"


@pytest.mark.asyncio
async def test_room_first_case_config_delete() -> None:
    room_start_time = time(hour=8)
    room_end_time = time(hour=18)

    await harness.first_case_config_store.upsert_room_first_case_config(
        RoomFirstCaseConfig(
            room_id=harness.room_0.id,
            monday_start_time=room_start_time,
            monday_end_time=room_end_time,
        )
    )

    result = harness.field_engineer_client.execute_graphql(
        ROOM_FIRST_CASE_CONFIG_DELETE_MUTATION,
        variables={"input": {"roomId": harness.room_0.id}},
    )

    assert result["data"]["roomFirstCaseConfigDelete"]["success"] is True

    room_data = result["data"]["roomFirstCaseConfigDelete"]["room"]

    assert room_data["id"] == harness.room_0.id
    assert room_data["firstCaseConfig"]["source"] == "DEFAULT"
    assert (
        room_data["firstCaseConfig"]["monday"]["startTime"] == "07:00:00"
    )  # default first case config start
    assert (
        room_data["firstCaseConfig"]["monday"]["endTime"] == "09:00:00"
    )  # default first case config end

    # test idempotence
    result = harness.field_engineer_client.execute_graphql(
        ROOM_FIRST_CASE_CONFIG_DELETE_MUTATION,
        variables={"input": {"roomId": harness.room_0.id}},
    )

    assert result["data"]["roomFirstCaseConfigDelete"]["success"] is True

    room_data = result["data"]["roomFirstCaseConfigDelete"]["room"]

    assert room_data["id"] == harness.room_0.id
    assert room_data["firstCaseConfig"]["source"] == "DEFAULT"
    assert (
        room_data["firstCaseConfig"]["monday"]["startTime"] == "07:00:00"
    )  # default first case config start
    assert (
        room_data["firstCaseConfig"]["monday"]["endTime"] == "09:00:00"
    )  # default first case config end


ROOM_FIRST_CASE_CONFIG_DELETE_MUTATION = """
    mutation RoomFirstCaseConfigDelete($input: RoomFirstCaseConfigDeleteInput!) {
        roomFirstCaseConfigDelete(input: $input) {
            success
            room {
                id
                firstCaseConfig {
                    source
                    monday {
                        startTime
                        endTime
                    }
                }
            }
        }
    }
    """
