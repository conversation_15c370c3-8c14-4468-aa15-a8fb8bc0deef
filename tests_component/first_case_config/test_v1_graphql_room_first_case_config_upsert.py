from datetime import time
from api_server.services.first_case_config.first_case_config_store import RoomFirstCaseConfig
import pytest


from tests_component import harness


@pytest.mark.asyncio
async def test_first_case_config_upsert_requires_auth() -> None:
    start_time = time(hour=7)
    end_time = time(hour=17)

    result = harness.no_auth_client.execute_graphql(
        FIRST_CASE_CONFIG_UPSERT_MUTATION,
        variables={
            "input": {
                "roomId": harness.room_0.id,
                "monday": {
                    "startTime": start_time.isoformat(),
                    "endTime": end_time.isoformat(),
                },
            }
        },
    )

    assert result["data"]["roomFirstCaseConfigUpsert"] is None
    assert result["errors"][0]["path"] == ["roomFirstCaseConfigUpsert"]
    assert result["errors"][0]["message"] == "Not Authorized: No authorization found"


@pytest.mark.asyncio
async def test_first_case_config_upsert__create() -> None:
    start_time = time(hour=7)
    end_time = time(hour=17)

    result = harness.field_engineer_client.execute_graphql(
        FIRST_CASE_CONFIG_UPSERT_MUTATION,
        variables={
            "input": {
                "roomId": harness.room_0.id,
                "monday": {
                    "startTime": start_time.isoformat(),
                    "endTime": end_time.isoformat(),
                },
            }
        },
    )

    assert result["data"]["roomFirstCaseConfigUpsert"]["success"] is True

    first_case_config_data = result["data"]["roomFirstCaseConfigUpsert"]["firstCaseConfig"]

    assert first_case_config_data["monday"]["startTime"] == "07:00:00"
    assert first_case_config_data["monday"]["endTime"] == "17:00:00"
    assert first_case_config_data["mondayStartTime"] == "07:00:00"
    assert first_case_config_data["mondayEndTime"] == "17:00:00"

    assert first_case_config_data["roomId"] == harness.room_0.id
    assert first_case_config_data["room"]["id"] == harness.room_0.id

    room_data = result["data"]["roomFirstCaseConfigUpsert"]["room"]

    assert room_data["id"] == harness.room_0.id
    assert room_data["firstCaseConfig"]["source"] == "ROOM"
    assert room_data["firstCaseConfig"]["monday"]["startTime"] == "07:00:00"
    assert room_data["firstCaseConfig"]["monday"]["endTime"] == "17:00:00"


@pytest.mark.asyncio
async def test_first_case_config_upsert__update() -> None:
    start_time = time(hour=7)
    end_time = time(hour=17)

    await harness.first_case_config_store.upsert_room_first_case_config(
        RoomFirstCaseConfig(
            room_id=harness.room_0.id,
            sunday_start_time=start_time,
            sunday_end_time=end_time,
            tuesday_start_time=start_time,
            tuesday_end_time=end_time,
        )
    )

    result = harness.field_engineer_client.execute_graphql(
        FIRST_CASE_CONFIG_UPSERT_MUTATION,
        variables={
            "input": {
                "roomId": harness.room_0.id,
                "sunday": {
                    "startTime": start_time.isoformat(),
                    "endTime": end_time.isoformat(),
                },
                "monday": {
                    "startTime": start_time.isoformat(),
                    "endTime": end_time.isoformat(),
                },
            }
        },
    )

    assert result["data"]["roomFirstCaseConfigUpsert"]["success"] is True

    first_case_config_data = result["data"]["roomFirstCaseConfigUpsert"]["firstCaseConfig"]

    assert first_case_config_data["sunday"]["startTime"] == "07:00:00"
    assert first_case_config_data["sunday"]["endTime"] == "17:00:00"
    assert first_case_config_data["sundayStartTime"] == "07:00:00"
    assert first_case_config_data["sundayEndTime"] == "17:00:00"

    assert first_case_config_data["monday"]["startTime"] == "07:00:00"
    assert first_case_config_data["monday"]["endTime"] == "17:00:00"
    assert first_case_config_data["mondayStartTime"] == "07:00:00"
    assert first_case_config_data["mondayEndTime"] == "17:00:00"

    assert first_case_config_data["tuesday"] is None
    assert first_case_config_data["tuesdayStartTime"] is None
    assert first_case_config_data["tuesdayEndTime"] is None

    assert first_case_config_data["roomId"] == harness.room_0.id
    assert first_case_config_data["room"]["id"] == harness.room_0.id
    assert first_case_config_data["room"]["id"] == harness.room_0.id

    room_data = result["data"]["roomFirstCaseConfigUpsert"]["room"]

    assert room_data["id"] == harness.room_0.id
    assert room_data["firstCaseConfig"]["source"] == "ROOM"
    assert room_data["firstCaseConfig"]["monday"]["startTime"] == "07:00:00"


FIRST_CASE_CONFIG_UPSERT_MUTATION = """
    mutation RoomFirstCaseConfigUpsert($input: RoomFirstCaseConfigUpsertInput!) {
        roomFirstCaseConfigUpsert(input: $input) {
            success
            room {
                id
                firstCaseConfig {
                    source
                    monday {
                        startTime
                        endTime
                    }
                }
            }
            firstCaseConfig {
                roomId
                room {
                    id
                    name
                }

                sunday {
                    startTime
                    endTime
                }
                sundayStartTime
                sundayEndTime

                monday {
                    startTime
                    endTime
                }
                mondayStartTime
                mondayEndTime

                tuesday {
                    startTime
                    endTime
                }
                tuesdayStartTime
                tuesdayEndTime
            }
        }
    }
    """
