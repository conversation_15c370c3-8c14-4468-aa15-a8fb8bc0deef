import datetime

from tests_component import harness


def test_search_highlight_feedback() -> None:
    today = datetime.datetime.today()
    tomorrow = today + datetime.timedelta(days=1)
    yesterday = today - datetime.timedelta(days=1)
    feedback_created_time = harness.highlight_feedback_1.created_time.isoformat()
    feedback_2_created_time = harness.highlight_feedback_2.created_time.isoformat()
    result = harness.labeler_client.execute_graphql(
        f"""
        query {{
            highlightFeedbackSearch(
                query: {{ minTime: "{yesterday.isoformat()}", maxTime: "{tomorrow.isoformat()}" }},
                first: 10
            ) {{
                edges {{
                    node {{
                        id
                        comment
                        submittedDate
                        highlight {{
                            id
                        }}
                        user {{
                            id
                            name
                        }}
                    }}
                }}
            }}
        }}
        """
    )

    assert result == {
        "data": {
            "highlightFeedbackSearch": {
                "edges": [
                    {
                        "node": {
                            "id": str(harness.highlight_feedback_1.id),
                            "comment": harness.highlight_feedback_1.comment,
                            "submittedDate": feedback_created_time,
                            "highlight": {"id": str(harness.highlight_1.id)},
                            "user": {
                                "id": str(harness.hospital_administrator_user.user_id),
                                "name": harness.hospital_administrator_user.display_name,
                            },
                        }
                    },
                    {
                        "node": {
                            "id": str(harness.highlight_feedback_2.id),
                            "comment": harness.highlight_feedback_2.comment,
                            "submittedDate": feedback_2_created_time,
                            "highlight": {"id": str(harness.highlight_1.id)},
                            "user": {
                                "id": str(harness.surgeon_user.user_id),
                                "name": harness.surgeon_user.display_name,
                            },
                        }
                    },
                ]
            }
        }
    }
