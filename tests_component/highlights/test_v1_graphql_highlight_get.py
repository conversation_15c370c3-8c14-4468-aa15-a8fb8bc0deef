import uuid
from datetime import datetime

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from api_server.services.highlights.highlight_store import Highlight, HighlightAsset, HighlightUser
from auth.permissions import READ_ANY_FEEDBACK
from tests_component import harness


def test_get_highlight_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        f"""
    query {{
        highlights {{
            edges {{
                node {{
                    id
                    description
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": None,
        "errors": [
            {
                "path": ["highlights"],
                "locations": [{"line": 3, "column": 9}],
                "message": "Not Authorized: No authorization found",
            }
        ],
    }


def test_get_highlights_requires_permissions() -> None:
    result = harness.user_without_permissions_client.execute_graphql(
        f"""
    query {{
        highlights {{
            edges {{
                node {{
                    id
                    description
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": None,
        "errors": [
            {
                "message": "Not authorized to read highlight",
                "locations": [{"line": 3, "column": 9}],
                "path": ["highlights"],
            }
        ],
    }


def test_get_highlights_requires_permissions_if_nothing_assigned() -> None:
    result = harness.field_engineer_client.execute_graphql(
        f"""
    query {{
        highlights {{
            edges {{
                node {{
                    id
                    description
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": None,
        "errors": [
            {
                "message": "Not authorized to read highlight",
                "locations": [{"line": 3, "column": 9}],
                "path": ["highlights"],
            }
        ],
    }


def test_get_highlights_for_org_user() -> None:
    result = harness.surgeon_client.execute_graphql(
        f"""
    query {{
        highlights(first: 10) {{
            edges {{
                node {{
                    id
                    description
                    organizationId
                    category
                    hasVideoAvailable
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "highlights": {
                "edges": [
                    {
                        "node": {
                            "id": str(harness.highlight_1.id),
                            "description": harness.highlight_1.description,
                            "organizationId": harness.highlight_1.org_id,
                            "category": harness.highlight_1.category,
                            "hasVideoAvailable": False,
                        }
                    }
                ]
            }
        }
    }


def test_get_highlights_for_user() -> None:
    result = harness.hospital_administrator_client.execute_graphql(
        f"""
    query {{
        highlights(first: 10) {{
            edges {{
                node {{
                    id
                    description
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "highlights": {
                "edges": [
                    {
                        "node": {
                            "id": str(harness.highlight_2.id),
                            "description": harness.highlight_2.description,
                        }
                    },
                    {
                        "node": {
                            "id": str(harness.highlight_1.id),
                            "description": harness.highlight_1.description,
                        }
                    },
                ]
            }
        }
    }


def test_get_incomplete_highlights_for_user() -> None:
    result = harness.hospital_administrator_client.execute_graphql(
        f"""
        query {{
            highlights(query: {{ status: "incomplete" }}, first: 10) {{
                edges {{
                    node {{
                        id
                        description
                    }}
                }}
            }}
        }}
        """
    )

    assert result == {
        "data": {
            "highlights": {
                "edges": [
                    {
                        "node": {
                            "id": str(harness.highlight_2.id),
                            "description": harness.highlight_2.description,
                        }
                    }
                ]
            }
        }
    }


def test_get_complete_highlights_for_user() -> None:
    result = harness.hospital_administrator_client.execute_graphql(
        f"""
        query {{
            highlights(query: {{ status: "complete" }}, first: 10) {{
                edges {{
                    node {{
                        id
                        description
                    }}
                }}
            }}
        }}
        """
    )

    assert result == {
        "data": {
            "highlights": {
                "edges": [
                    {
                        "node": {
                            "id": str(harness.highlight_1.id),
                            "description": harness.highlight_1.description,
                        }
                    }
                ]
            }
        }
    }


def test_get_highlight_for_org_user() -> None:
    result = harness.surgeon_client.execute_graphql(
        f"""
        query {{
            highlight(id: "{harness.highlight_1.id}") {{
                id
                description
                myFeedback {{
                    id
                    rating
                    comment
                }}
            }}
        }}
        """
    )

    assert result == {
        "data": {
            "highlight": {
                "id": str(harness.highlight_1.id),
                "description": harness.highlight_1.description,
                "myFeedback": {
                    "id": str(harness.highlight_feedback_2.id),
                    "rating": harness.highlight_feedback_2.rating,
                    "comment": harness.highlight_feedback_2.comment,
                },
            }
        }
    }


def test_get_highlight() -> None:
    result = harness.hospital_administrator_client.execute_graphql(
        f"""
        query {{
            highlight(id: "{harness.highlight_1.id}") {{
                id
                description
                myFeedback {{
                    id
                    rating
                    comment
                }}
            }}
        }}
        """
    )

    assert result == {
        "data": {
            "highlight": {
                "id": str(harness.highlight_1.id),
                "description": harness.highlight_1.description,
                "myFeedback": {
                    "id": str(harness.highlight_feedback_1.id),
                    "rating": harness.highlight_feedback_1.rating,
                    "comment": harness.highlight_feedback_1.comment,
                },
            }
        }
    }


def test_get_highlight_with_no_feedback() -> None:
    result = harness.hospital_administrator_client.execute_graphql(
        f"""
        query {{
            highlight(id: "{harness.highlight_2.id}") {{
                id
                description
                myFeedback {{
                    id
                    rating
                    comment
                }}
            }}
        }}
        """
    )

    assert result == {
        "data": {
            "highlight": {
                "id": str(harness.highlight_2.id),
                "description": harness.highlight_2.description,
                "myFeedback": None,
            }
        }
    }


def test_get_all_highlight_feedback_requires_permissions() -> None:
    result = harness.hospital_administrator_client.execute_graphql(
        f"""
    query {{
        highlights(first: 10) {{
            edges {{
                node {{
                    id
                    description
                    feedback {{
                        edges {{
                            node {{
                                id
                            }}
                        }}
                    }}
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": None,
        "errors": [
            {
                "path": ["highlights", "edges", 0, "node", "feedback"],
                "locations": [{"line": 8, "column": 21}],
                "message": f"User does not have permission '{READ_ANY_FEEDBACK}'",
            }
        ],
    }


def test_get_all_highlight_users() -> None:
    result = harness.hospital_administrator_client.execute_graphql(
        f"""
    query {{
        highlights(first: 1) {{
            edges {{
                node {{
                    id
                    description
                    users {{
                        edges {{
                            node {{
                                id
                            }}
                        }}
                    }}
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "highlights": {
                "edges": [
                    {
                        "node": {
                            "description": harness.highlight_2.description,
                            "id": str(harness.highlight_2.id),
                            "users": {
                                "edges": [
                                    {"node": {"id": harness.hospital_administrator_user.user_id}}
                                ]
                            },
                        }
                    }
                ]
            }
        }
    }


def test_get_highlights_sorted() -> None:
    result = harness.hospital_administrator_client.execute_graphql(
        f"""
    query {{
        highlights(first: 1, orderBy: [{{ sort: "description" }}]) {{
            edges {{
                node {{
                    id
                    description
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "highlights": {
                "edges": [
                    {
                        "node": {
                            "description": harness.highlight_1.description,
                            "id": str(harness.highlight_1.id),
                        }
                    }
                ]
            }
        }
    }


def test_get_highlights_sorted_desc() -> None:
    result = harness.hospital_administrator_client.execute_graphql(
        f"""
    query {{
        highlights(first: 1, orderBy: [{{ sort: "description", direction: DESC }}]) {{
            edges {{
                node {{
                    id
                    description
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "highlights": {
                "edges": [
                    {
                        "node": {
                            "description": harness.highlight_2.description,
                            "id": str(harness.highlight_2.id),
                        }
                    }
                ]
            }
        }
    }


def test_get_highlights_sorted_multiple_keys() -> None:
    result = harness.hospital_administrator_client.execute_graphql(
        f"""
    query {{
        highlights(first: 1, orderBy: [{{ sort: "description", direction: DESC }},
         {{ sort: "id", direction: DESC }}]) {{
            edges {{
                node {{
                    id
                    description
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "highlights": {
                "edges": [
                    {
                        "node": {
                            "description": harness.highlight_2.description,
                            "id": str(harness.highlight_2.id),
                        }
                    }
                ]
            }
        }
    }


def test_get_highlights_sorted_with_nonexistent_key() -> None:
    result = harness.hospital_administrator_client.execute_graphql(
        f"""
    query {{
        highlights(first: 1, orderBy: [{{ sort: "descriptions" }}]) {{
            edges {{
                node {{
                    id
                    description
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": None,
        "errors": [
            {
                "locations": [{"column": 9, "line": 3}],
                "message": "attempting to sort by a non existent key",
                "path": ["highlights"],
            }
        ],
    }


def test_get_highlights_sorted_with_optional_key() -> None:
    result = harness.hospital_administrator_client.execute_graphql(
        f"""
    query {{
        highlights(first: 1, orderBy: [{{ sort: "organizationId" }}]) {{
            edges {{
                node {{
                    id
                    description
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "highlights": {
                "edges": [
                    {
                        "node": {
                            "description": harness.highlight_2.description,
                            "id": str(harness.highlight_2.id),
                        }
                    }
                ]
            }
        }
    }


def test_get_highlights_date_filter() -> None:
    min_time = harness.highlight_1.start_time
    max_time = harness.highlight_1.end_time
    result = harness.hospital_administrator_client.execute_graphql(
        f"""
    query {{
        highlights(query: {{
            minTime: "{min_time.isoformat()}",
            maxTime: "{max_time.isoformat()}"
        }}) {{
            edges {{
                node {{
                    id
                    description
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "highlights": {
                "edges": [
                    {
                        "node": {
                            "description": harness.highlight_1.description,
                            "id": str(harness.highlight_1.id),
                        }
                    }
                ]
            }
        }
    }


@pytest.mark.asyncio
async def test_get_highlight_nonexistent_user(async_session: AsyncSession) -> None:
    highlight = Highlight()
    highlight.id = uuid.uuid4()
    highlight.org_id = harness.org_0.id
    highlight.site_id = harness.site_0.id
    highlight.room_id = harness.room_0.id
    highlight.start_time = datetime.fromisoformat("2021-03-05T12:01:02.345+00:00")
    highlight.end_time = datetime.fromisoformat("2021-03-05T14:01:02.345+00:00")
    highlight.description = "test highlight nonexistent user description"
    highlight.category = "turnover"

    highlight_asset = HighlightAsset()
    highlight_asset.highlight_id = highlight.id
    highlight_asset.asset_id = harness.media_asset_0_id

    highlight_user_nonexistent = HighlightUser()
    highlight_user_nonexistent.highlight_id = highlight.id
    highlight_user_nonexistent.user_id = "bad user"

    highlight_user_exists = HighlightUser()
    highlight_user_exists.highlight_id = highlight.id
    highlight_user_exists.user_id = harness.labeler_user.user_id

    await harness.highlight_store.create_highlight(
        highlight=highlight,
        highlight_users=[highlight_user_exists, highlight_user_nonexistent],
        session=async_session,
    )

    await harness.highlight_store.create_highlight_assets(
        highlight_assets=[highlight_asset], session=async_session
    )

    min_time = highlight.start_time
    max_time = highlight.end_time
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        highlights(query: {{
            minTime: "{min_time.isoformat()}",
            maxTime: "{max_time.isoformat()}"
        }}) {{
            edges {{
                node {{
                    id
                    users {{
                    edges {{
                    node {{
                        id
                        }}
                        }}
                    }}
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "highlights": {
                "edges": [
                    {
                        "node": {
                            "id": str(highlight.id),
                            "users": {
                                "edges": [{"node": {"id": highlight_user_exists.user_id}}],
                            },
                        }
                    }
                ]
            }
        }
    }
