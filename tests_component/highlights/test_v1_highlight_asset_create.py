# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

import pytest

from apella_cloud_api.dtos import HighlightAssetDto
from apella_cloud_api.exceptions import NotAuthorized
from tests_component import harness


def sample_dto():
    result = HighlightAssetDto()
    result.asset_id = harness.media_asset_1_id
    result.highlight_id = harness.highlight_1.id
    return result


def test_create_highlight_asset_requires_auth() -> None:
    with pytest.raises(NotAuthorized):
        harness.no_auth_client.create_highlight_asset(sample_dto())


def test_create_highlight_asset_with_no_permissions() -> None:
    with pytest.raises(NotAuthorized):
        harness.surgeon_client.create_highlight_asset(sample_dto())


def test_create_highlight_asset_as_service_account() -> None:
    asset = sample_dto()
    highlight_asset_response: HighlightAssetDto = (
        harness.service_account_client.create_highlight_asset(asset)
    )

    expected_dict = asset.to_dict()
    expected_dict["asset_id"] = str(highlight_asset_response.asset_id)
    expected_dict["highlight_id"] = str(highlight_asset_response.highlight_id)
    assert highlight_asset_response.to_dict() == expected_dict
