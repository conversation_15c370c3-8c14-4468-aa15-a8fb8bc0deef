from datetime import date, datetime
from typing import AsyncGenerator
from uuid import UUID, uuid4

import pytest_asyncio
from asyncpg import Range

import pytest

from api_server.services.measurement_periods.measurement_period_store import (
    DayOfWeek,
    MeasurementPeriodModel,
)
from tests_component import harness


@pytest.fixture
def id() -> UUID:
    return uuid4()


@pytest.fixture
def name() -> str:
    return "i_am_a_measurement_period"


@pytest.fixture
def site_id() -> str:
    return harness.site_0.id


@pytest.fixture
def start_date() -> date:
    return datetime(2023, 12, 1).date()


@pytest.fixture
def end_date() -> date:
    return datetime(2023, 12, 31).date()


@pytest.fixture
def room_ids() -> list[str]:
    return [harness.room_0.id, harness.room_1.id]


@pytest.fixture
def days_of_week() -> list[DayOfWeek]:
    return [
        DayOfWeek.MONDAY,
        DayOfWeek.TUESDAY,
        DayOfWeek.WEDNESDAY,
    ]


@pytest_asyncio.fixture
async def mock_measurement_period(
    id: UUID,
    name: str,
    site_id: str,
    start_date: date,
    end_date: date,
    room_ids: list[str],
    days_of_week: list[DayOfWeek],
) -> AsyncGenerator[MeasurementPeriodModel, None]:
    mock_measurement_period = MeasurementPeriodModel(
        id=id,
        name=name,
        site_id=site_id,
        measurement_period=Range(
            start_date,
            end_date,
        ),
        start_date=start_date,
        end_date=end_date,
        room_ids=room_ids,
        days_of_week=days_of_week,
    )

    yield await harness.measurement_period_store.upsert_measurement_period(mock_measurement_period)

    await harness.measurement_period_store.delete_measurement_period(
        id=str(mock_measurement_period.id)
    )
