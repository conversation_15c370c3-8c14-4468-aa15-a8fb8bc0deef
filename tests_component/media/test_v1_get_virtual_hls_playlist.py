from datetime import datetime, timedelta, timezone
from typing import Generator

import pytest

from apella_cloud_api.exceptions import ClientError, NotAuthorized
from tests_component import harness
from tests_component.conftest import MockService
from mocks.mock_media_asset_service.database import Database as MockMediaAssetServiceDatabase


@pytest.fixture()
def camera_0_playlist_end_time() -> Generator[datetime, None, None]:
    yield datetime.now(timezone.utc)


@pytest.fixture()
def camera_0_playlist_start_time(
    camera_0_playlist_end_time: datetime,
) -> Generator[datetime, None, None]:
    yield camera_0_playlist_end_time - timedelta(hours=1)


@pytest.fixture()
def camera_0_playlist(
    mock_media_asset_service: MockService[MockMediaAssetServiceDatabase],
    camera_0_playlist_start_time: datetime,
    camera_0_playlist_end_time: datetime,
) -> Generator[str, None, None]:
    mock_media_asset_service.database.add_playlist(
        harness.camera_0.org_id,
        harness.camera_0.site_id,
        harness.camera_0.room_id,
        harness.camera_0.id,
        camera_0_playlist_start_time,
        camera_0_playlist_end_time,
    )
    playlist_tuple = mock_media_asset_service.database.get_playlist(
        harness.camera_0.org_id,
        harness.camera_0.site_id,
        harness.camera_0.room_id,
        harness.camera_0.id,
        camera_0_playlist_start_time,
        camera_0_playlist_end_time,
    )
    playlist, _, _ = (
        playlist_tuple
        if playlist_tuple is not None
        else (
            "",
            datetime.now(),
            datetime.now(),
        )
    )
    yield playlist


def test_get_virtual_hls_playlist_requires_auth() -> None:
    with pytest.raises(NotAuthorized):
        harness.no_auth_client.get_virtual_hls_playlist(
            camera_id="test_camera_id",
            start_time="2021-12-01 00:00:00-07:00",
            end_time="2021-12-02 00:00:00-07:00",
        )


def test_get_virtual_hls_playlist_requires_permissions() -> None:
    with pytest.raises(NotAuthorized):
        harness.user_without_permissions_client.get_virtual_hls_playlist(
            camera_id="test_camera_id",
            start_time="2021-12-01 00:00:00-07:00",
            end_time="2021-12-02 00:00:00-07:00",
        )


def test_get_virtual_hls_playlist_segment_requires_auth() -> None:
    with pytest.raises(NotAuthorized):
        harness.no_auth_client.get_virtual_hls_playlist_segment(
            camera_id="test_camera_id",
            start_time="2021-12-01 00:00:00-07:00",
            playlist_name="at-gar-cam1-720p__2021-12-20T00:46:56-08:00_0",
            segment_file="segment_0",
        )


def test_get_virtual_hls_playlist_segment_requires_permissions() -> None:
    with pytest.raises(NotAuthorized):
        harness.user_without_permissions_client.get_virtual_hls_playlist_segment(
            camera_id="test_camera_id",
            start_time="2021-12-01 00:00:00-07:00",
            playlist_name="at-gar-cam1-720p__2021-12-20T00:46:56-08:00_0",
            segment_file="segment_0",
        )


def test_get_virtual_hls_playlist_segment_rejects_special_characters() -> None:
    with pytest.raises(ClientError):
        harness.labeler_client.get_virtual_hls_playlist_segment(
            camera_id="test_camera_id",
            start_time="2021-12-01 00:00:00-07:00",
            playlist_name="at-gar-cam1-720p__2021-12-20T00:46:56-08:00_0../",
            segment_file="segment_0",
        )


def test_get_virtual_hls_playlist(
    camera_0_playlist: str,
    camera_0_playlist_start_time: datetime,
    camera_0_playlist_end_time: datetime,
) -> None:
    playlist: str = harness.labeler_client.get_virtual_hls_playlist(
        camera_id=harness.camera_0.id,
        start_time=camera_0_playlist_start_time.isoformat(),
        end_time=camera_0_playlist_end_time.isoformat(),
    )

    assert playlist == camera_0_playlist
