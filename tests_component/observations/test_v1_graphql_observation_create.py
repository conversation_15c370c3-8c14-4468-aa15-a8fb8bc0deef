# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

import uuid

from tests_component import harness

test_observation_id = str(uuid.uuid4())
no_recorded_time_observation_id = str(uuid.uuid4())


def mutation_observation_input(
    observation_time: str = "2021-03-01T15:00:00+00:00",
    recorded_time: str = "2021-03-01T15:00:00+00:00",
):
    return f"""
        mutation {{
          observationCreate(input: {{
            id: "{test_observation_id}",
            typeId: "OBSERVED_PREP_START",
            observationTime: "{observation_time}",
            recordedTime: "{recorded_time}",
            organizationId: "{harness.org_0.id}",
            caseId: "{harness.case_0.case_id}"
          }}) {{
            success
            createdObservation {{
              id
              type
            }}
          }}
        }}
    """


def mutation_observation_input_no_recorded_time(
    observation_time: str = "2021-03-01T15:00:00+00:00",
):
    return f"""
        mutation {{
          observationCreate(input: {{
            id: "{no_recorded_time_observation_id}",
            typeId: "OBSERVED_PREP_START",
            observationTime: "{observation_time}",
            organizationId: "{harness.org_0.id}",
            caseId: "{harness.case_0.case_id}"
          }}) {{
            success
            createdObservation {{
              id
              type
            }}
          }}
        }}
    """


def test_create_observation_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        f"""
    {mutation_observation_input()}
    """
    )
    assert result == {
        "data": {"observationCreate": None},
        "errors": [
            {
                "path": ["observationCreate"],
                "locations": [{"line": 4, "column": 11}],
                "message": "Not Authorized: No authorization found",
            }
        ],
    }


def test_create_observation_return() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    {mutation_observation_input()}
    """
    )

    assert result == {
        "data": {
            "observationCreate": {
                "success": True,
                "createdObservation": {"id": test_observation_id, "type": "Observed Prep Start"},
            }
        }
    }


def test_create_observation_duplicate_fails() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    {mutation_observation_input(observation_time=harness.observation_0.observation_time.isoformat())}
    """
    )

    assert result == {
        "data": {"observationCreate": None},
        "errors": [
            {
                "path": ["observationCreate"],
                "locations": [{"line": 4, "column": 11}],
                "message": f"""observation id "{test_observation_id}" already exists""",
            }
        ],
    }


def test_missing_input_causes_error() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    mutation {{
      observationCreate {{
        success
      }}
    }}
    """
    )

    assert result == {
        "errors": [
            {
                "message": "ObservationCreate.mutate() missing 1 required positional argument: 'input'",
                "locations": [{"line": 3, "column": 7}],
                "path": ["observationCreate"],
            }
        ],
        "data": {"observationCreate": None},
    }


def test_create_observation_with_null_recorded_time_succeeds() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
        {mutation_observation_input_no_recorded_time()}
        """
    )

    assert result == {
        "data": {
            "observationCreate": {
                "success": True,
                "createdObservation": {
                    "id": no_recorded_time_observation_id,
                    "type": "Observed Prep Start",
                },
            }
        }
    }
