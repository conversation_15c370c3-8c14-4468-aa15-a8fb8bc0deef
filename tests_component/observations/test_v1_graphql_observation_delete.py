# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

from tests_component import harness


def mutation_observation_input():
    return f"""
        mutation {{
          observationDelete(input: {{
            id: "{harness.observation_0.id}"
          }}) {{
            success
          }}
        }}
    """


def test_delete_observation_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        f"""
    {mutation_observation_input()}
    """
    )
    assert result == {
        "data": {"observationDelete": None},
        "errors": [
            {
                "path": ["observationDelete"],
                "locations": [{"line": 4, "column": 11}],
                "message": "Not Authorized: No authorization found",
            }
        ],
    }


def test_create_observation_return() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    {mutation_observation_input()}
    """
    )

    assert result == {
        "data": {
            "observationDelete": {
                "success": True,
            }
        }
    }
