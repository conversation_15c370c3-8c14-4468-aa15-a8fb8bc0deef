# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

from tests_component import harness


def mutation_observation_input(
    observation_time_1: str = "2021-03-01T15:00:00+00:00",
    observation_time_2: str = "2021-03-01T15:05:00+00:00",
):
    return f"""
        mutation {{
          observationsUpsert(observations: [{{
            typeId: "OBSERVED_PREP_START",
            observationTime: "{observation_time_1}",
            recordedTime: "2021-03-01T15:00:00+00:00",
            organizationId: "{harness.org_0.id}",
            caseId: "{harness.case_0.case_id}"
          }}, {{
            typeId: "OBSERVED_PREP_COMPLETE",
            observationTime: "{observation_time_2}",
            recordedTime: "2021-03-01T15:00:00+00:00",
            organizationId: "{harness.org_0.id}",
            caseId: "{harness.case_0.case_id}"
          }}, {{
            typeId: "OBSERVED_IN_ROOM",
            observationTime: "2021-03-01T15:06:00+00:00",
            recordedTime: "2021-03-01T15:00:00+00:00",
            organizationId: "{harness.org_0.id}",
            caseId: "{harness.case_0.case_id}"
          }}, {{
            typeId: "OBSERVED_OUT_OF_ROOM",
            observationTime: "2021-03-01T15:10:00+00:00",
            organizationId: "{harness.org_0.id}",
            caseId: "{harness.case_0.case_id}"
          }}]) {{
            success
            createdObservations {{
                edges {{
                    node {{
                      type
                  }}
              }}
            }}
          }}
        }}
    """


def test_create_observation_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        f"""
    {mutation_observation_input()}
    """
    )
    assert result == {
        "data": {"observationsUpsert": None},
        "errors": [
            {
                "path": ["observationsUpsert"],
                "locations": [{"line": 4, "column": 11}],
                "message": "Not Authorized: No authorization found",
            }
        ],
    }


def test_upsert_observation_return() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    {mutation_observation_input()}
    """
    )

    assert result == {
        "data": {
            "observationsUpsert": {
                "success": True,
                "createdObservations": {
                    "edges": [
                        {
                            "node": {"type": "Observed Prep Start"},
                        },
                        {"node": {"type": "Observed Prep Complete"}},
                        {"node": {"type": "Observed In Room"}},
                        {"node": {"type": "Observed Out of Room"}},
                    ]
                },
            }
        }
    }


def test_upsert_observation_duplicate_skips() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    {mutation_observation_input(observation_time_1=harness.observation_0.observation_time.isoformat())}
    """
    )

    assert result == {
        "data": {
            "observationsUpsert": {
                "success": True,
                "createdObservations": {
                    "edges": [
                        {"node": {"type": "Observed Prep Complete"}},
                        {"node": {"type": "Observed In Room"}},
                        {"node": {"type": "Observed Out of Room"}},
                    ]
                },
            }
        }
    }


def test_missing_input_causes_error() -> None:
    error = harness.service_account_client.execute_graphql(
        f"""
        mutation {{
          observationsUpsert {{
            success
          }}
        }}
        """
    )
    assert error == {
        "data": None,
        "errors": [
            {
                "message": "Field 'observationsUpsert' argument 'observations' of type '[ObservationUpsertInput!]!' is required, but it was not provided.",
                "locations": [{"line": 3, "column": 11}],
            }
        ],
    }
