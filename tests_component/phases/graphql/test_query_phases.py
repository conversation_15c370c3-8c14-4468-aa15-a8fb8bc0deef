# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

import uuid
from datetime import datetime, timedelta, timezone
from typing import Optional
from zoneinfo import ZoneInfo

import isodate
import pytest
from graphql import GraphQLError
from sgqlc.endpoint.http import HTTPEndpoint
from sgqlc.operation import Operation
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from apella_cloud_api.api_server_schema import (
    EventDeleteInput,
    PhaseConnection,
    PhaseCreateInput,
    PhaseDeleteInput,
    PhaseQueryInput,
    PhaseRelationshipCreateInput,
    PhaseRelationshipDeleteInput,
    PhaseUpdateInput,
    Query,
)
from apella_cloud_api.api_server_schema import PhaseStatus as SGQLPhaseStatus
from apella_cloud_api.exceptions import ClientError
from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_client_schema import GQLDirection
from apella_cloud_api.new_input_schema import GQLOrderBy, GQLPhaseQueryInput
from apella_cloud_api.new_schema_generator.schema_generator_base_classes import NotFound
from api_server.graphql.partial_updates import get_field_value_from_request_input
from api_server.services.case.case_status import SCHEDULED
from api_server.services.case.case_store import Case
from api_server.services.events.event_store import EventModel
from api_server.services.events.source_type import FORECASTING
from api_server.services.phases.phase_store import (
    PhaseModel,
    PhaseRelationshipModel,
    PhaseStatus,
)
from api_server.services.phases.phases_models import PhaseType
from tests_component import harness


async def setup_future_and_elapsed_phases(async_session: AsyncSession):
    setattr(harness, "elapsed_case", Case())
    harness.elapsed_case.external_case_id = "ext_case_id_elapsed"
    harness.elapsed_case.case_id = "65e53d52-8d55-41e5-a48b-2126852889de"
    harness.elapsed_case.org_id = harness.org_0.id
    harness.elapsed_case.site_id = harness.site_0.id
    harness.elapsed_case.room_id = harness.room_0.id
    harness.elapsed_case.case_classification_types_id = None
    harness.elapsed_case.scheduled_start_time = datetime.now(tz=timezone.utc) - timedelta(days=50)
    harness.elapsed_case.scheduled_end_time = harness.elapsed_case.scheduled_start_time + timedelta(
        minutes=100
    )
    harness.elapsed_case.is_add_on = False
    harness.elapsed_case.status = SCHEDULED
    await harness.case_store.create_case(case=harness.elapsed_case, session=async_session)

    if True:  # For arbitrary nesting
        setattr(harness, "elapsed_event", EventModel())
        harness.elapsed_event.id = "c8f58669-d892-4e66-85a5-f6e3f31c7644"
        harness.elapsed_event.event_type_id = "patient_wheels_in"
        harness.elapsed_event.case_id = harness.elapsed_case.case_id
        harness.elapsed_event.room_id = harness.elapsed_case.room_id
        harness.elapsed_event.camera_id = harness.camera_0.id
        harness.elapsed_event.site_id = harness.elapsed_case.site_id
        harness.elapsed_event.org_id = harness.elapsed_case.org_id
        harness.elapsed_event.start_time = harness.elapsed_case.scheduled_start_time - timedelta(
            minutes=50
        )
        harness.elapsed_event.process_timestamp = datetime.now(tz=timezone.utc)
        harness.elapsed_event.source = "component-tests"
        harness.elapsed_event.source_type = FORECASTING
        harness.elapsed_event.labels = ["Test"]
        await harness.event_store.create_event(
            session=async_session,
            event=harness.elapsed_event,
        )

    setattr(harness, "future_case", Case())
    harness.future_case.external_case_id = "ext_case_id_future"
    harness.future_case.case_id = "0def802e-c496-4b55-b747-087fbca0556b"
    harness.future_case.org_id = harness.org_0.id
    harness.future_case.site_id = harness.site_0.id
    harness.future_case.room_id = harness.room_0.id
    harness.future_case.case_classification_types_id = None
    harness.future_case.scheduled_start_time = datetime.now(tz=timezone.utc) + timedelta(
        minutes=100
    )
    harness.future_case.scheduled_end_time = harness.future_case.scheduled_start_time + timedelta(
        minutes=100
    )
    harness.future_case.is_add_on = False
    harness.future_case.status = SCHEDULED
    await harness.case_store.create_case(case=harness.future_case, session=async_session)

    if True:  # For arbitrary nesting
        setattr(harness, "future_event", EventModel())
        harness.future_event.id = "f8045e16-d55e-4829-9dbe-7c74e555512f"
        harness.future_event.event_type_id = "patient_wheels_in"
        harness.future_event.case_id = harness.future_case.case_id
        harness.future_event.room_id = harness.future_case.room_id
        harness.future_event.camera_id = harness.camera_0.id
        harness.future_event.site_id = harness.future_case.site_id
        harness.future_event.org_id = harness.future_case.org_id
        harness.future_event.start_time = harness.future_case.scheduled_start_time + timedelta(
            minutes=50
        )
        harness.future_event.process_timestamp = datetime.now(tz=timezone.utc)
        harness.future_event.source = "component-tests"
        harness.future_event.source_type = FORECASTING
        harness.future_event.labels = ["Test"]
        await harness.event_store.create_event(
            session=async_session,
            event=harness.future_event,
        )


class TestQueryPhases:
    async def _run_filter_test(self, async_session: AsyncSession, phase_1, phase_2, **filters):
        # This function assumes that phase_1 will not be filtered out and phase_2 will be
        await harness.phase_store.insert_phase(phase_1)
        await harness.phase_store.insert_phase(phase_2)

        query = PhaseQueryInput(type=PhaseType.TURNOVER.name, **filters)

        phases = harness.labeler_client.query_phases(query)

        assert len(phases) == 1
        assert phases[0].id == str(phase_1.id)

    # I'm not including time range verified in the default query phases api
    # because it is a niche use case
    def query_phases_verified_time_range(self, query: PhaseQueryInput):
        # Setup
        endpoint = HTTPEndpoint(
            harness.labeler_client.graphql_url, harness.labeler_client.headers()
        )
        operation = Operation(Query)

        # Generate the graphql query
        phases: PhaseConnection = operation.phases(query=query)
        phases.edges.node.id()
        phases.edges.node.room.id()
        phases.edges.node.time_range_verified()
        data = endpoint(operation)
        harness.labeler_client.assert_successful_graphql_request(data)

        # Parse the response to a python type
        result = [phase.node for phase in (operation + data).phases.edges]

        return result

    def _create_mock_phase(
        self,
        start_event_id: str,
        end_event_id: Optional[str],
        org_id: Optional[str] = None,
        site_id: Optional[str] = None,
        room_id: Optional[str] = None,
        case_id: Optional[str] = None,
        source_type: str = "human_gt",
        phase_type: PhaseType = PhaseType.TURNOVER,
    ) -> PhaseModel:
        phase = PhaseModel()
        phase.id = uuid.uuid4()
        phase.org_id = org_id if org_id is not None else harness.org_0.id
        phase.site_id = site_id if site_id is not None else harness.site_0.id
        phase.room_id = room_id if room_id is not None else harness.room_0.id
        phase.type_id = phase_type.name
        phase.start_event_id = start_event_id
        phase.end_event_id = end_event_id
        phase.case_id = case_id
        phase.source_type = source_type
        phase.status = PhaseStatus.VALID
        return phase

    def _create_mock_relationship(
        self, parent_phase_id: uuid.UUID, child_phase_id: uuid.UUID, org_id: Optional[str] = None
    ):
        phase_relationship = PhaseRelationshipModel()
        phase_relationship.parent_phase_id = parent_phase_id
        phase_relationship.child_phase_id = child_phase_id
        phase_relationship.org_id = org_id if org_id is not None else harness.org_0.id
        return phase_relationship

    @pytest.mark.asyncio
    async def test_query_phases_returns_all_fields(self, async_session: AsyncSession) -> None:
        before_event_0 = harness.event_0.start_time - timedelta(minutes=1)
        after_event_0 = harness.event_0.start_time + timedelta(minutes=1)
        _phase_1 = self._create_mock_phase(harness.event_0.id, harness.event_0.id)
        phase_2 = self._create_mock_phase(harness.event_1.id, harness.event_1.id)
        phase_3 = self._create_mock_phase(harness.event_2.id, harness.event_2.id)
        phase_relationship_1 = self._create_mock_relationship(_phase_1.id, phase_2.id)
        phase_relationship_2 = self._create_mock_relationship(phase_3.id, _phase_1.id)
        phase_1 = await harness.phase_store.insert_phase(_phase_1)
        await harness.phase_store.insert_phase(phase_2)
        await harness.phase_store.insert_phase(phase_3)
        await harness.phase_store.insert_relationship(phase_relationship_1)
        await harness.phase_store.insert_relationship(phase_relationship_2)
        query = PhaseQueryInput(
            min_time=before_event_0, max_time=after_event_0, type=PhaseType.TURNOVER.name
        )

        phases = harness.labeler_client.query_phases(query)

        assert len(phases) == 1
        assert phases[0].id == str(phase_1.id)
        assert phases[0].organization.id == phase_1.org_id
        assert phases[0].site.id == phase_1.site_id
        assert phases[0].room.id == phase_1.room_id
        assert phases[0].type_id == phase_1.type_id
        assert phases[0].start_event.id == phase_1.start_event_id
        assert phases[0].end_event.id == phase_1.end_event_id
        assert phases[0].source_type == phase_1.source_type
        assert phases[0].start_time == phase_1.start_time()
        assert phases[0].end_time == phase_1.end_time()
        assert phases[0].duration == isodate.duration_isoformat(phase_1.duration())
        assert phases[0].status == "VALID"
        assert phases[0].invalidation_reason == phase_1.invalidation_reason
        assert phases[0].updated_time == phase_1.updated_time
        assert phases[0].created_time == phase_1.created_time

        assert len(phases[0].child_phases) == 1
        assert phases[0].child_phases[0].id == str(phase_2.id)

        assert len(phases[0].parent_phases) == 1
        assert phases[0].parent_phases[0].id == str(phase_3.id)

    @pytest.mark.asyncio
    async def test_query_phases_all_types(self, async_session: AsyncSession) -> None:
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_1 = harness.event_1.start_time + timedelta(hours=1)
        phase_1 = self._create_mock_phase(
            harness.event_0.id, harness.event_1.id, phase_type=PhaseType.TURNOVER
        )
        phase_2 = self._create_mock_phase(
            harness.event_0.id, harness.event_1.id, phase_type=PhaseType.PRE_OPERATIVE
        )
        await harness.phase_store.insert_phase(phase_1)
        await harness.phase_store.insert_phase(phase_2)
        query = PhaseQueryInput(min_time=before_event_0, max_time=after_event_1)

        phases = harness.labeler_client.query_phases(query)

        phases.sort(key=lambda phase: phase.type_id, reverse=True)

        assert len(phases) == 2
        phase_ids = [phase.id for phase in phases]
        assert str(phase_1.id) in phase_ids
        assert str(phase_2.id) in phase_ids

    @pytest.mark.asyncio
    async def test_query_phases_date_filters_contains(self, async_session: AsyncSession) -> None:
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_0 = harness.event_0.start_time + timedelta(minutes=1)

        phase_1 = self._create_mock_phase(harness.event_0.id, harness.event_0.id)
        phase_2 = self._create_mock_phase(harness.event_1.id, harness.event_1.id)

        await self._run_filter_test(
            async_session,
            phase_1,
            phase_2,
            min_time=before_event_0,
            max_time=after_event_0,
        )

    @pytest.mark.asyncio
    async def test_query_phases_date_filters_overlaps(self, async_session: AsyncSession) -> None:
        after_event_0 = harness.event_0.start_time + timedelta(minutes=1)
        after_event_1 = harness.event_1.start_time + timedelta(minutes=1)

        phase_1 = self._create_mock_phase(harness.event_0.id, harness.event_1.id)

        await harness.phase_store.insert_phase(phase_1)

        query = PhaseQueryInput(
            min_time=after_event_0, max_time=after_event_1, type=PhaseType.TURNOVER.name
        )

        phases = harness.labeler_client.query_phases(query)

        assert len(phases) == 1

    @pytest.mark.asyncio
    async def test_query_phases_types_filters(self, async_session: AsyncSession) -> None:
        phase_1 = self._create_mock_phase(
            harness.event_0.id, harness.event_0.id, phase_type=PhaseType.TURNOVER
        )
        phase_2 = self._create_mock_phase(
            harness.event_0.id, harness.event_0.id, phase_type=PhaseType.PRE_OPERATIVE
        )
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_0 = harness.event_0.start_time + timedelta(minutes=1)

        await self._run_filter_test(
            async_session,
            phase_1,
            phase_2,
            min_time=before_event_0,
            max_time=after_event_0,
        )

    @pytest.mark.asyncio
    async def test_query_phases_types_org_id(self, async_session: AsyncSession) -> None:
        phase_1 = self._create_mock_phase(
            harness.event_0.id, harness.event_0.id, org_id=harness.org_0.id
        )
        phase_2 = self._create_mock_phase(
            harness.event_0.id, harness.event_0.id, org_id=harness.org_1.id
        )
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_0 = harness.event_0.start_time + timedelta(minutes=1)
        await self._run_filter_test(
            async_session,
            phase_1,
            phase_2,
            min_time=before_event_0,
            max_time=after_event_0,
            organization_id=harness.org_0.id,
        )

    @pytest.mark.asyncio
    async def test_query_phases_types_site_id(self, async_session: AsyncSession) -> None:
        phase_1 = self._create_mock_phase(
            harness.event_0.id, harness.event_0.id, site_id=harness.site_0.id
        )
        phase_2 = self._create_mock_phase(
            harness.event_0.id, harness.event_0.id, site_id=harness.site_1.id
        )
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_0 = harness.event_0.start_time + timedelta(minutes=1)
        await self._run_filter_test(
            async_session,
            phase_1,
            phase_2,
            min_time=before_event_0,
            max_time=after_event_0,
            site_ids=[harness.site_0.id],
        )

    @pytest.mark.asyncio
    async def test_query_phases_types_room_id(self, async_session: AsyncSession) -> None:
        phase_1 = self._create_mock_phase(
            harness.event_0.id, harness.event_0.id, room_id=harness.room_0.id
        )
        phase_2 = self._create_mock_phase(
            harness.event_0.id, harness.event_0.id, room_id=harness.room_1.id
        )
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_0 = harness.event_0.start_time + timedelta(minutes=1)
        await self._run_filter_test(
            async_session,
            phase_1,
            phase_2,
            min_time=before_event_0,
            max_time=after_event_0,
            room_ids=[harness.room_0.id],
        )

    @pytest.mark.asyncio
    async def test_query_phases_types_case_id(self, async_session: AsyncSession) -> None:
        phase_1 = self._create_mock_phase(
            harness.event_0.id, harness.event_0.id, case_id=harness.case_0.case_id
        )
        phase_2 = self._create_mock_phase(
            harness.event_0.id, harness.event_0.id, case_id=harness.case_1.case_id
        )
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_0 = harness.event_0.start_time + timedelta(minutes=1)
        await self._run_filter_test(
            async_session,
            phase_1,
            phase_2,
            min_time=before_event_0,
            max_time=after_event_0,
            case_ids=[harness.case_0.case_id],
        )

    @pytest.mark.asyncio
    async def test_query_phases_source_type(self, async_session: AsyncSession) -> None:
        phase_1 = self._create_mock_phase(
            harness.event_0.id, harness.event_0.id, source_type="prediction"
        )
        phase_2 = self._create_mock_phase(
            harness.event_0.id, harness.event_0.id, source_type="human_gt"
        )
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_0 = harness.event_0.start_time + timedelta(minutes=1)
        await self._run_filter_test(
            async_session,
            phase_1,
            phase_2,
            min_time=before_event_0,
            max_time=after_event_0,
            show_human_ground_truth_data=False,
        )

    @pytest.mark.asyncio
    async def test_query_phases_source_type_forecasted(self, async_session: AsyncSession) -> None:
        phase_1 = self._create_mock_phase(
            harness.event_0.id, harness.event_0.id, source_type="forecasted"
        )
        phase_2 = self._create_mock_phase(
            harness.event_0.id, harness.event_0.id, source_type="human_gt"
        )
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_0 = harness.event_0.start_time + timedelta(minutes=1)
        await self._run_filter_test(
            async_session,
            phase_1,
            phase_2,
            min_time=before_event_0,
            max_time=after_event_0,
            source_type="forecasted",
        )

    @pytest.mark.asyncio
    async def test_query_phases_source_type_forecasted_ensures_start_time_has_not_elapsed(
        self,
        async_session: AsyncSession,
    ) -> None:
        await setup_future_and_elapsed_phases(async_session)

        elapsed_phase = self._create_mock_phase(
            harness.elapsed_event.id,
            harness.elapsed_event.id,
            source_type=FORECASTING,
            phase_type=PhaseType.CASE,
        )
        not_elapsed_phase = self._create_mock_phase(
            harness.future_event.id,
            harness.future_event.id,
            source_type=FORECASTING,
            phase_type=PhaseType.CASE,
        )
        phase_to_filter = self._create_mock_phase(
            harness.event_1.id, harness.event_1.id, source_type="human_gt"
        )
        query_min_time = harness.elapsed_event.start_time - timedelta(hours=1)
        query_max_time = harness.future_event.start_time + timedelta(hours=1)

        elapsed_phase = await harness.phase_store.insert_phase(
            phase=elapsed_phase,
        )
        not_elapsed_phase = await harness.phase_store.insert_phase(
            phase=not_elapsed_phase,
        )
        await harness.phase_store.insert_phase(phase_to_filter)

        # assert that this phase's start time has elapsed
        assert elapsed_phase.start_time().astimezone(ZoneInfo("UTC")) < datetime.now(timezone.utc)
        assert not_elapsed_phase.start_time().astimezone(ZoneInfo("UTC")) > datetime.now(
            timezone.utc
        )

        query = PhaseQueryInput(
            type=PhaseType.CASE.name,
            min_time=query_min_time,
            max_time=query_max_time,
            source_type=FORECASTING,
            ensure_phase_start_time_has_not_elapsed=True,
        )

        phases = harness.labeler_client.query_phases(query)
        assert len(phases) == 2
        phase_ids = [phase.id for phase in phases]
        assert str(elapsed_phase.id) in phase_ids
        assert str(not_elapsed_phase.id) in phase_ids

        # check that the time is reasonably equal to utc now
        start_time_after_validation = phases[0].start_time.astimezone(ZoneInfo("UTC"))
        assert start_time_after_validation >= datetime.now(timezone.utc) - timedelta(seconds=30)

    @pytest.mark.asyncio
    async def test_query_phases_source_type_forecasted_ensures_start_time_has_not_elapsed_is_none(
        self,
        async_session: AsyncSession,
    ) -> None:
        await setup_future_and_elapsed_phases(async_session)

        elapsed_phase = self._create_mock_phase(
            harness.elapsed_event.id,
            harness.elapsed_event.id,
            source_type=FORECASTING,
            phase_type=PhaseType.CASE,
        )
        not_elapsed_phase = self._create_mock_phase(
            harness.future_event.id,
            harness.future_event.id,
            source_type=FORECASTING,
            phase_type=PhaseType.CASE,
        )
        phase_to_filter = self._create_mock_phase(
            harness.event_1.id, harness.event_1.id, source_type="human_gt"
        )
        query_min_time = harness.elapsed_event.start_time - timedelta(hours=1)
        query_max_time = harness.future_event.start_time + timedelta(hours=1)

        elapsed_phase = await harness.phase_store.insert_phase(
            phase=elapsed_phase,
        )
        not_elapsed_phase = await harness.phase_store.insert_phase(
            phase=not_elapsed_phase,
        )
        await harness.phase_store.insert_phase(
            phase=phase_to_filter,
        )

        # assert that this phase's start time has elapsed
        assert elapsed_phase.start_time().astimezone(ZoneInfo("UTC")) < datetime.now(timezone.utc)
        assert not_elapsed_phase.start_time().astimezone(ZoneInfo("UTC")) > datetime.now(
            timezone.utc
        )

        query = PhaseQueryInput(
            type=PhaseType.CASE.name,
            min_time=query_min_time,
            max_time=query_max_time,
            source_type=FORECASTING,
        )

        phases = harness.labeler_client.query_phases(query)

        assert len(phases) == 2
        phase_ids = [phase.id for phase in phases]
        assert str(elapsed_phase.id) in phase_ids
        assert str(not_elapsed_phase.id) in phase_ids

        # check that the time is still before utc now
        retrieved_elapsed_phase = next(
            (phase for phase in phases if phase.id == str(elapsed_phase.id)), None
        )
        assert retrieved_elapsed_phase is not None
        start_time_after_validation = retrieved_elapsed_phase.start_time.astimezone(ZoneInfo("UTC"))
        assert start_time_after_validation < datetime.now(timezone.utc)

    @pytest.mark.asyncio
    async def test_query_phases_source_type_forecasted_ensures_start_time_has_not_elapsed_is_false(
        self,
        async_session: AsyncSession,
    ) -> None:
        await setup_future_and_elapsed_phases(async_session)

        elapsed_phase = self._create_mock_phase(
            harness.elapsed_event.id,
            harness.elapsed_event.id,
            source_type=FORECASTING,
            phase_type=PhaseType.CASE,
        )
        not_elapsed_phase = self._create_mock_phase(
            harness.future_event.id,
            harness.future_event.id,
            source_type=FORECASTING,
            phase_type=PhaseType.CASE,
        )
        phase_to_filter = self._create_mock_phase(
            harness.event_1.id, harness.event_1.id, source_type="human_gt"
        )
        query_min_time = harness.elapsed_event.start_time - timedelta(hours=1)
        query_max_time = harness.future_event.start_time + timedelta(hours=1)

        elapsed_phase = await harness.phase_store.insert_phase(
            phase=elapsed_phase,
        )
        not_elapsed_phase = await harness.phase_store.insert_phase(
            phase=not_elapsed_phase,
        )
        await harness.phase_store.insert_phase(
            phase=phase_to_filter,
        )

        # assert that this phase's start time has elapsed
        assert elapsed_phase.start_time().astimezone(ZoneInfo("UTC")) < datetime.now(timezone.utc)
        assert not_elapsed_phase.start_time().astimezone(ZoneInfo("UTC")) > datetime.now(
            timezone.utc
        )

        query = PhaseQueryInput(
            type=PhaseType.CASE.name,
            min_time=query_min_time,
            max_time=query_max_time,
            source_type=FORECASTING,
            ensure_phase_start_time_has_not_elapsed=False,
        )

        phases = harness.labeler_client.query_phases(query)

        assert len(phases) == 2
        phase_ids = [phase.id for phase in phases]
        assert str(elapsed_phase.id) in phase_ids
        assert str(not_elapsed_phase.id) in phase_ids

        # check that the time is still before utc now
        retrieved_elapsed_phase = next(
            (phase for phase in phases if phase.id == str(elapsed_phase.id)), None
        )
        assert retrieved_elapsed_phase is not None
        start_time_after_validation = retrieved_elapsed_phase.start_time.astimezone(ZoneInfo("UTC"))
        assert start_time_after_validation < datetime.now(timezone.utc)

    @pytest.mark.asyncio
    async def test_query_phases_max_duration_timedelta(self, async_session: AsyncSession) -> None:
        phase_1 = self._create_mock_phase(
            harness.event_0.id,
            harness.event_1.id,
            source_type="human_gt",
        )
        phase_2 = self._create_mock_phase(
            harness.event_1.id, harness.event_2.id, source_type="human_gt"
        )
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_2 = harness.event_2.start_time + timedelta(minutes=1)

        await self._run_filter_test(
            async_session,
            phase_1,
            phase_2,
            min_time=before_event_0,
            max_time=after_event_2,
            max_duration=isodate.duration_isoformat(
                harness.event_1.start_time - harness.event_0.start_time + timedelta(minutes=1)
            ),
        )

    @pytest.mark.asyncio
    async def test_query_phases_max_duration_long_duration(
        self, async_session: AsyncSession
    ) -> None:
        phase_1 = self._create_mock_phase(
            harness.event_0.id,
            harness.event_1.id,
            source_type="human_gt",
        )
        phase_2 = self._create_mock_phase(
            harness.event_1.id, harness.event_2.id, source_type="human_gt"
        )
        await harness.phase_store.insert_phase(
            phase=phase_1,
        )
        await harness.phase_store.insert_phase(
            phase=phase_2,
        )

        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_2 = harness.event_2.start_time + timedelta(minutes=1)

        query = PhaseQueryInput(
            type=PhaseType.TURNOVER.name,
            min_time=before_event_0,
            max_time=after_event_2,
            max_duration=isodate.duration_isoformat(isodate.Duration(months=2)),
        )

        with pytest.raises(GraphQLError):
            harness.labeler_client.query_phases(query)

    @pytest.mark.asyncio
    async def test_delete_phases(self, async_session: AsyncSession) -> None:
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_1 = harness.event_1.start_time + timedelta(minutes=1)

        phase_1 = self._create_mock_phase(harness.event_0.id, harness.event_0.id)
        phase_2 = self._create_mock_phase(harness.event_1.id, harness.event_1.id)

        phase_1 = await harness.phase_store.insert_phase(
            phase=phase_1,
        )
        phase_2 = await harness.phase_store.insert_phase(
            phase=phase_2,
        )

        delete_input = PhaseDeleteInput(id=phase_1.id)
        harness.labeler_client.delete_phase(delete_input)

        query = PhaseQueryInput(
            min_time=before_event_0, max_time=after_event_1, type=PhaseType.TURNOVER.name
        )

        phases = harness.labeler_client.query_phases(query)

        assert len(phases) == 1
        assert phases[0].id == str(phase_2.id)

    @pytest.mark.asyncio
    async def test_delete_phases_invalid_id(self) -> None:
        # sqlalchemy does not throw errors when phase does not exist
        delete_input = PhaseDeleteInput(id=uuid.uuid4())
        harness.labeler_client.delete_phase(delete_input)

        delete_input = PhaseDeleteInput(id="not a valid uuid")
        with pytest.raises(GraphQLError):
            harness.labeler_client.delete_phase(delete_input)

    @pytest.mark.asyncio
    async def test_update_phases(self, async_session: AsyncSession) -> None:
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_1 = harness.event_1.start_time + timedelta(minutes=1)

        phase_1 = self._create_mock_phase(
            harness.event_0.id, harness.event_0.id, case_id=harness.case_0.case_id
        )

        phase_1 = await harness.phase_store.insert_phase(
            phase=phase_1,
        )

        update_input = PhaseUpdateInput(
            id=phase_1.id,
            case_id=None,
            type_id=PhaseType.PRE_OPERATIVE.name,
            start_event_id=harness.event_1.id,
            end_event_id=harness.event_1.id,
            source_type="prediction",
            status=PhaseStatus.INVALID.name,
            invalidation_reason="Duplicate phase",
        )
        harness.labeler_client.update_phase(update_input)

        query = PhaseQueryInput(
            min_time=before_event_0,
            max_time=after_event_1,
            type=PhaseType.PRE_OPERATIVE.name,
            show_human_ground_truth_data=False,
            statuses=[PhaseStatus.INVALID.name],
        )
        phases = harness.labeler_client.query_phases(query)

        assert len(phases) == 1
        assert phases[0].id == str(phase_1.id)
        assert phases[0].start_event.id == harness.event_1.id
        assert phases[0].end_event.id == harness.event_1.id
        assert phases[0].source_type == "prediction"
        # The sgqlc library has a bug where a result of case = None, creates an empty ScheduledCase() rather than returning None.
        assert str(phases[0].case) == "ScheduledCase()"
        assert phases[0].type_id == PhaseType.PRE_OPERATIVE.name
        assert phases[0].status == PhaseStatus.INVALID.name
        assert phases[0].invalidation_reason == "Duplicate phase"

    @pytest.mark.asyncio
    async def test_update_phases_to_valid(self, async_session: AsyncSession) -> None:
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_1 = harness.event_1.start_time + timedelta(minutes=1)

        phase_1 = self._create_mock_phase(
            harness.event_0.id, harness.event_0.id, case_id=harness.case_0.case_id
        )

        phase_1 = await harness.phase_store.insert_phase(
            phase=phase_1,
        )

        update_input = PhaseUpdateInput(
            id=phase_1.id,
            case_id=None,
            type_id=PhaseType.PRE_OPERATIVE.name,
            start_event_id=harness.event_1.id,
            end_event_id=harness.event_1.id,
            source_type="prediction",
            status=PhaseStatus.VALID.name,
            invalidation_reason=None,
        )
        harness.labeler_client.update_phase(update_input)

        query = PhaseQueryInput(
            min_time=before_event_0,
            max_time=after_event_1,
            type=PhaseType.PRE_OPERATIVE.name,
            show_human_ground_truth_data=False,
            statuses=[PhaseStatus.VALID.name],
        )
        phases = harness.labeler_client.query_phases(query)

        assert len(phases) == 1
        assert phases[0].id == str(phase_1.id)
        assert phases[0].start_event.id == harness.event_1.id
        assert phases[0].end_event.id == harness.event_1.id
        assert phases[0].source_type == "prediction"
        # The sgqlc library has a bug where a result of case = None, creates an empty ScheduledCase() rather than returning None.
        assert str(phases[0].case) == "ScheduledCase()"
        assert phases[0].type_id == PhaseType.PRE_OPERATIVE.name
        assert phases[0].status == PhaseStatus.VALID.name
        assert phases[0].invalidation_reason is None

    @pytest.mark.asyncio
    async def test_update_phases_invalid_id(self, async_session: AsyncSession) -> None:
        phase_1 = self._create_mock_phase(
            harness.event_0.id, harness.event_0.id, case_id=harness.case_0.case_id
        )
        await harness.phase_store.insert_phase(
            phase=phase_1,
        )

        input = PhaseUpdateInput(
            id=uuid.uuid4(),
            case_id=None,
            type_id=PhaseType.PRE_OPERATIVE.name,
            start_event_id=harness.event_1.id,
            end_event_id=harness.event_1.id,
            source_type="prediction",
            status=PhaseStatus.VALID.name,
            invalidation_reason=None,
        )
        # phase_store should return clientError
        with pytest.raises(ClientError):
            await harness.phase_store.update_phase(
                phase_id=input.id,
                case_id=get_field_value_from_request_input(input, "case_id"),
                type_id=input.type_id,
                start_event_id=input.start_event_id,
                end_event_id=input.end_event_id,
                source_type=input.source_type,
                status=PhaseStatus.VALID,
                invalidation_reason=get_field_value_from_request_input(
                    input, "invalidation_reason"
                ),
                etag=None,
            )

        with pytest.raises(GraphQLError):
            harness.labeler_client.update_phase(input)

    @pytest.mark.asyncio
    async def test_update_phases_invalid_event_id(self, async_session: AsyncSession) -> None:
        phase_1 = self._create_mock_phase(
            harness.event_0.id, harness.event_0.id, case_id=harness.case_0.case_id
        )
        await harness.phase_store.insert_phase(
            phase=phase_1,
        )

        input = PhaseUpdateInput(
            id=phase_1.id,
            case_id=None,
            type_id=PhaseType.PRE_OPERATIVE.name,
            start_event_id=harness.event_1.id,
            end_event_id="invalid-event-id",
            source_type="prediction",
            status=PhaseStatus.VALID.name,
            invalidation_reason=None,
        )
        # phase_store should return IntegrityError
        with pytest.raises(IntegrityError):
            await harness.phase_store.update_phase(
                phase_id=input.id,
                case_id=get_field_value_from_request_input(input, "case_id"),
                type_id=input.type_id,
                start_event_id=input.start_event_id,
                end_event_id=input.end_event_id,
                source_type=input.source_type,
                status=PhaseStatus.VALID,
                invalidation_reason=get_field_value_from_request_input(
                    input, "invalidation_reason"
                ),
                etag=None,
            )

        with pytest.raises(GraphQLError):
            harness.labeler_client.update_phase(input)

    @pytest.mark.asyncio
    async def test_create_phases(self, async_session: AsyncSession) -> None:
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_0 = harness.event_0.start_time + timedelta(minutes=1)

        phase_id = uuid.uuid4()

        create_input = PhaseCreateInput(
            id=phase_id,
            org_id=harness.org_0.id,
            site_id=harness.site_0.id,
            room_id=harness.room_0.id,
            case_id=harness.case_0.case_id,
            type_id=PhaseType.PRE_OPERATIVE.name,
            start_event_id=harness.event_0.id,
            end_event_id=harness.event_0.id,
            source_type="prediction",
            status=PhaseStatus.VALID.name,
            invalidation_reason=None,
        )

        create_result = harness.labeler_client.create_phase(create_input)
        created_phase = create_result.created_phase

        query = PhaseQueryInput(
            min_time=before_event_0,
            max_time=after_event_0,
            type=PhaseType.PRE_OPERATIVE.name,
            show_human_ground_truth_data=False,
        )
        phases = harness.labeler_client.query_phases(query)
        assert len(phases) == 1
        assert phases[0].id == created_phase.id
        assert phases[0].start_event.id == created_phase.start_event.id
        assert phases[0].end_event.id == created_phase.end_event.id
        assert phases[0].source_type == created_phase.source_type
        assert phases[0].case.id == created_phase.case.id
        assert phases[0].type_id == created_phase.type_id
        assert phases[0].status == PhaseStatus.VALID.name
        assert phases[0].invalidation_reason is None

    @pytest.mark.asyncio
    async def test_create_phases_duplicate_phases(self, async_session: AsyncSession) -> None:
        phase_id = uuid.uuid4()

        # Both phases share same ID
        phase_1 = PhaseCreateInput(
            id=phase_id,
            org_id=harness.org_0.id,
            site_id=harness.site_0.id,
            room_id=harness.room_0.id,
            case_id=harness.case_0.case_id,
            type_id=PhaseType.PRE_OPERATIVE.name,
            start_event_id=harness.event_0.id,
            end_event_id=harness.event_0.id,
            source_type="prediction",
            status=PhaseStatus.VALID.name,
            invalidation_reason=None,
        )
        phase_2 = PhaseCreateInput(
            id=phase_id,
            org_id=harness.org_0.id,
            site_id=harness.site_0.id,
            room_id=harness.room_0.id,
            case_id=harness.case_0.case_id,
            type_id=PhaseType.PRE_OPERATIVE.name,
            start_event_id=harness.event_0.id,
            end_event_id=harness.event_0.id,
            source_type="prediction",
            status=PhaseStatus.VALID.name,
            invalidation_reason=None,
        )

        harness.labeler_client.create_phase(phase_1)
        with pytest.raises(GraphQLError):
            harness.labeler_client.create_phase(phase_2)

    @pytest.mark.asyncio
    async def test_create_phases_with_invalid_status(self, async_session: AsyncSession) -> None:
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_0 = harness.event_0.start_time + timedelta(minutes=1)

        phase_id = uuid.uuid4()

        create_input = PhaseCreateInput(
            id=phase_id,
            org_id=harness.org_0.id,
            site_id=harness.site_0.id,
            room_id=harness.room_0.id,
            case_id=harness.case_0.case_id,
            type_id=PhaseType.PRE_OPERATIVE.name,
            start_event_id=harness.event_0.id,
            end_event_id=harness.event_0.id,
            source_type="prediction",
            status=PhaseStatus.INVALID.name,
            invalidation_reason="For test",
        )

        create_result = harness.labeler_client.create_phase(create_input)
        created_phase = create_result.created_phase

        query = PhaseQueryInput(
            min_time=before_event_0,
            max_time=after_event_0,
            type=PhaseType.PRE_OPERATIVE.name,
            show_human_ground_truth_data=False,
        )
        phases = harness.labeler_client.query_phases(query)

        assert len(phases) == 1
        assert phases[0].id == created_phase.id
        assert phases[0].start_event.id == created_phase.start_event.id
        assert phases[0].end_event.id == created_phase.end_event.id
        assert phases[0].source_type == created_phase.source_type
        assert phases[0].case.id == created_phase.case.id
        assert phases[0].type_id == created_phase.type_id
        assert phases[0].status == PhaseStatus.INVALID.name
        assert phases[0].invalidation_reason == "For test"

    @pytest.mark.asyncio
    async def test_create_phases_without_end_event(self, async_session: AsyncSession) -> None:
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_0 = harness.event_0.start_time + timedelta(minutes=1)

        phase_id = uuid.uuid4()

        create_input = PhaseCreateInput(
            id=phase_id,
            org_id=harness.org_0.id,
            site_id=harness.site_0.id,
            room_id=harness.room_0.id,
            case_id=harness.case_0.case_id,
            type_id=PhaseType.PRE_OPERATIVE.name,
            start_event_id=harness.event_0.id,
            end_event_id=None,
            source_type="prediction",
            status=SGQLPhaseStatus.VALID,
        )

        create_result = harness.labeler_client.create_phase(create_input)
        created_phase = create_result.created_phase

        query = PhaseQueryInput(
            min_start_time=before_event_0,
            max_start_time=after_event_0,
            type=PhaseType.PRE_OPERATIVE.name,
            show_human_ground_truth_data=False,
        )
        phases = harness.labeler_client.query_phases(query)

        assert len(phases) == 1
        assert phases[0].id == created_phase.id
        assert phases[0].start_event.id == created_phase.start_event.id
        assert phases[0].end_event is None
        assert phases[0].source_type == created_phase.source_type
        assert phases[0].case.id == created_phase.case.id
        assert phases[0].type_id == created_phase.type_id

    @pytest.mark.asyncio
    async def test_create_relationship(self, async_session: AsyncSession) -> None:
        before_event_0 = harness.event_0.start_time - timedelta(minutes=1)
        after_event_1 = harness.event_1.start_time + timedelta(minutes=1)
        phase_1 = self._create_mock_phase(harness.event_0.id, harness.event_1.id)
        phase_2 = self._create_mock_phase(harness.event_2.id, harness.event_2.id)
        await harness.phase_store.insert_phase(phase_1)

        await harness.phase_store.insert_phase(phase_2)

        relationship_input = PhaseRelationshipCreateInput(
            parent_phase_id=phase_1.id, child_phase_id=phase_2.id, org_id=phase_1.org_id
        )

        create_result = harness.labeler_client.create_phase_relationship(relationship_input)
        assert create_result.success is True

        query = PhaseQueryInput(
            min_time=before_event_0, max_time=after_event_1, type=PhaseType.TURNOVER.name
        )

        phases = harness.labeler_client.query_phases(query)

        assert len(phases) == 1
        assert phases[0].id == str(phase_1.id)

        assert len(phases[0].child_phases) == 1
        assert phases[0].child_phases[0].id == str(phase_2.id)

        # Try to create again
        with pytest.raises(GraphQLError):
            harness.labeler_client.create_phase_relationship(relationship_input)

    @pytest.mark.asyncio
    async def test_delete_relationship(self, async_session: AsyncSession) -> None:
        before_event_0 = harness.event_0.start_time - timedelta(minutes=1)
        after_event_1 = harness.event_1.start_time + timedelta(minutes=1)
        phase_1 = self._create_mock_phase(harness.event_0.id, harness.event_1.id)
        phase_2 = self._create_mock_phase(harness.event_2.id, harness.event_2.id)
        phase_relationship = self._create_mock_relationship(phase_1.id, phase_2.id)
        await harness.phase_store.insert_phase(phase_1)

        await harness.phase_store.insert_phase(phase_2)

        await harness.phase_store.insert_relationship(phase_relationship)

        delete_input = PhaseRelationshipDeleteInput(
            parent_phase_id=phase_1.id,
            child_phase_id=phase_2.id,
        )

        delete_result = harness.labeler_client.delete_phase_relationship(delete_input)
        assert delete_result.success is True

        query = PhaseQueryInput(
            min_time=before_event_0, max_time=after_event_1, type=PhaseType.TURNOVER.name
        )

        phases = harness.labeler_client.query_phases(query)

        assert len(phases) == 1
        assert phases[0].id == str(phase_1.id)

        assert len(phases[0].child_phases) == 0

        # Try to delete again
        with pytest.raises(GraphQLError):
            harness.labeler_client.delete_phase_relationship(delete_input)

    @pytest.mark.asyncio
    async def test_event_delete_cascade(self, async_session: AsyncSession) -> None:
        before_event_0 = harness.event_0.start_time - timedelta(minutes=1)
        after_event_1 = harness.event_1.start_time + timedelta(minutes=1)
        phase_1 = self._create_mock_phase(harness.event_0.id, harness.event_1.id)
        phase_2 = self._create_mock_phase(harness.event_2.id, harness.event_2.id)
        phase_relationship = self._create_mock_relationship(phase_1.id, phase_2.id)
        await harness.phase_store.insert_phase(phase_1)

        await harness.phase_store.insert_phase(phase_2)

        await harness.phase_store.insert_relationship(phase_relationship)

        harness.labeler_client.delete_event(
            EventDeleteInput(
                id=harness.event_0.id,
                source=harness.event_0.source,
                source_type=harness.event_0.source_type,
            )
        )

        query = PhaseQueryInput(
            min_time=before_event_0, max_time=after_event_1, type=PhaseType.TURNOVER.name
        )

        phases = harness.labeler_client.query_phases(query)

        # Now the event never actually gets deleted, so this cascade doesn't happen, and the
        # phase continues to exist.
        assert len(phases) == 1

    @pytest.mark.asyncio
    async def test_relationship_integrity_error(self, async_session: AsyncSession) -> None:
        phase_1 = self._create_mock_phase(harness.event_0.id, harness.event_1.id)
        phase_relationship = self._create_mock_relationship(phase_1.id, uuid.uuid4())
        await harness.phase_store.insert_phase(phase_1)

        with pytest.raises(ClientError):
            await harness.phase_store.insert_relationship(phase_relationship)

    @pytest.mark.asyncio
    async def test_query_phases_verified(self, async_session: AsyncSession) -> None:
        phase_1 = self._create_mock_phase(
            harness.event_0.id, harness.event_1.id, room_id=harness.room_0.id
        )
        phase_2 = self._create_mock_phase(
            harness.event_0.id, harness.event_1.id, room_id=harness.room_1.id
        )
        phase_3 = self._create_mock_phase(
            harness.event_0.id, harness.event_1.id, room_id=harness.room_2.id
        )
        phase_4 = self._create_mock_phase(
            harness.event_0.id, harness.event_1.id, room_id=harness.room_3.id
        )
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_0 = harness.event_0.start_time + timedelta(minutes=1)

        await harness.phase_store.insert_phase(phase_1)

        await harness.phase_store.insert_phase(phase_2)

        await harness.phase_store.insert_phase(phase_3)

        await harness.phase_store.insert_phase(phase_4)

        query = PhaseQueryInput(min_time=before_event_0, max_time=after_event_0)

        phases = self.query_phases_verified_time_range(query)
        phases.sort(key=lambda phase: phase.room.id)
        assert len(phases) == 4
        assert phases[0].id == str(phase_1.id)
        assert phases[1].id == str(phase_2.id)
        assert phases[2].id == str(phase_3.id)
        assert phases[3].id == str(phase_4.id)

        assert phases[0].time_range_verified is False
        assert phases[1].time_range_verified is False
        assert phases[2].time_range_verified is False
        assert phases[3].time_range_verified is True

    @pytest.mark.asyncio
    async def test_query_phases_verified_no_annotation_tasks(
        self, async_session: AsyncSession
    ) -> None:
        phase_1 = self._create_mock_phase(
            harness.event_3.id, harness.event_3.id, room_id=harness.room_0.id
        )
        before_event_3 = harness.event_3.start_time - timedelta(hours=1)
        after_event_3 = harness.event_3.start_time + timedelta(minutes=1)

        await harness.phase_store.insert_phase(phase_1)

        query = PhaseQueryInput(
            min_time=before_event_3 - timedelta(days=1), max_time=after_event_3 + timedelta(days=1)
        )

        phases = self.query_phases_verified_time_range(query)
        phases.sort(key=lambda phase: phase.room.id)
        assert len(phases) == 1
        assert phases[0].id == str(phase_1.id)

        assert phases[0].time_range_verified is False

    @pytest.mark.asyncio
    async def test_query_partial_phases_verified_large_time_range_check(
        self, async_session: AsyncSession
    ) -> None:
        phase_1 = self._create_mock_phase(harness.event_0.id, None, room_id=harness.room_0.id)
        phase_2 = self._create_mock_phase(harness.event_0.id, None, room_id=harness.room_1.id)
        phase_3 = self._create_mock_phase(harness.event_0.id, None, room_id=harness.room_2.id)
        phase_4 = self._create_mock_phase(harness.event_0.id, None, room_id=harness.room_3.id)
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_0 = harness.event_0.start_time + timedelta(minutes=1)

        await harness.phase_store.insert_phase(phase_1)

        await harness.phase_store.insert_phase(phase_2)

        await harness.phase_store.insert_phase(phase_3)

        await harness.phase_store.insert_phase(phase_4)

        query = PhaseQueryInput(
            min_time=before_event_0 - timedelta(days=1), max_time=after_event_0 + timedelta(days=1)
        )

        phases = self.query_phases_verified_time_range(query)
        phases.sort(key=lambda phase: phase.room.id)
        assert len(phases) == 0

    @pytest.mark.asyncio
    async def test_query_phases_no_results(self, async_session: AsyncSession) -> None:
        before_event_0 = harness.event_0.start_time - timedelta(hours=1)
        after_event_1 = harness.event_1.start_time + timedelta(minutes=1)

        query = PhaseQueryInput(
            organization_id="invalid_org_id", min_time=before_event_0, max_time=after_event_1
        )

        # no errors are expected for a query with no results
        phases = harness.labeler_client.query_phases(query)
        assert len(phases) == 0
        await harness.phase_store.get_parent_phases(str(uuid.uuid4()))

    @pytest.mark.asyncio
    async def test_query_phases_by_updated_time(self, async_session: AsyncSession) -> None:
        phase_1 = self._create_mock_phase(harness.event_0.id, None, room_id=harness.room_0.id)
        phase_1.updated_time = datetime.now()

        phase_2 = self._create_mock_phase(harness.event_0.id, None, room_id=harness.room_0.id)

        await harness.phase_store.insert_phase(phase_1)

        await harness.phase_store.insert_phase(phase_2)

        phase_2 = await harness.phase_store._get_phase(str(phase_2.id), async_session)
        phase_2.updated_time = datetime.now() + timedelta(days=2)
        await async_session.commit()

        query = PhaseQueryInput(
            min_updated_time=datetime.now() - timedelta(days=1),
            max_updated_time=datetime.now() + timedelta(days=1),
        )

        phases = harness.service_account_client.query_phases(query)
        assert len(phases) == 1
        assert phases[0].id == str(phase_1.id)

    @pytest.mark.asyncio
    async def test_query_phases_by_created_time(self, async_session: AsyncSession) -> None:
        phase_1 = self._create_mock_phase(harness.event_0.id, None, room_id=harness.room_0.id)
        phase_1.created_time = datetime.now()

        phase_2 = self._create_mock_phase(harness.event_0.id, None, room_id=harness.room_0.id)

        await harness.phase_store.insert_phase(phase_1)

        await harness.phase_store.insert_phase(phase_2)

        phase_2 = await harness.phase_store._get_phase(str(phase_2.id), async_session)
        phase_2.created_time = datetime.now() + timedelta(days=2)
        await async_session.commit()

        query = PhaseQueryInput(
            min_created_time=datetime.now() - timedelta(days=1),
            max_created_time=datetime.now() + timedelta(days=1),
        )

        phases = harness.service_account_client.query_phases(query)
        assert len(phases) == 1
        assert phases[0].id == str(phase_1.id)

    @pytest.mark.asyncio
    async def test_query_phases_gql(self, async_session: AsyncSession):
        phase_1 = self._create_mock_phase(harness.event_0.id, None, room_id=harness.room_0.id)
        phase_1.created_time = datetime.now()

        phase_2 = self._create_mock_phase(
            harness.event_0.id, harness.event_1.id, room_id=harness.room_0.id
        )
        phase_2.created_time = datetime.now() + timedelta(days=2)

        await harness.phase_store.insert_phase(phase_1)

        await harness.phase_store.insert_phase(phase_2)

        query = {
            "min_created_time": datetime.now() - timedelta(days=1),
            "max_created_time": datetime.now() + timedelta(days=3),
        }
        apella_schema = ApellaSchema()
        query_dsl_gql = apella_schema.Query.phases.args(
            query=GQLPhaseQueryInput(
                min_created_time=query["min_created_time"],
                max_created_time=query["max_created_time"],
            ),
            order_by=[
                GQLOrderBy(
                    sort=apella_schema.Phase.created_time.name,
                    direction=GQLDirection.DESC,
                )
            ],
        ).select(
            apella_schema.PhaseConnection.edges.select(
                apella_schema.PhaseEdge.node.select(
                    apella_schema.Phase.id,
                    apella_schema.Phase.created_time,
                    apella_schema.Phase.start_event.select(
                        apella_schema.Event.id,
                        apella_schema.Event.start_time,
                    ),
                    apella_schema.Phase.end_event.select(
                        apella_schema.Event.id,
                        apella_schema.Event.organization.select(
                            apella_schema.Organization.id,
                            apella_schema.Organization.sites.select(
                                apella_schema.SiteConnection.edges.select(
                                    apella_schema.SiteEdge.node.select(
                                        apella_schema.Site.id,
                                        apella_schema.Site.organization.select(
                                            apella_schema.Organization.id,
                                        ),
                                    )
                                ),
                            ),
                        ),
                    ),
                    apella_schema.Phase.duration,
                    apella_schema.Phase.organization.select(
                        apella_schema.Organization.id,
                    ),
                )
            )
        )
        results = harness.service_account_client.query_graphql_from_schema(query_dsl_gql)
        phases = [edge.node for edge in results.phases.edges]

        assert len(phases) == 2
        assert phases[0].id == str(phase_2.id)
        assert phases[0].duration == harness.event_1.start_time - harness.event_0.start_time
        assert phases[1].id == str(phase_1.id)
        assert isinstance(phases[1].duration, NotFound)

    @pytest.mark.asyncio
    async def test_query_phases_gql_with_alias(self, async_session: AsyncSession):
        phase_1 = self._create_mock_phase(harness.event_0.id, None, room_id=harness.room_0.id)

        phase_1 = await harness.phase_store.insert_phase(phase_1)

        query = {
            "min_created_time": datetime.now() - timedelta(days=1),
            "max_created_time": datetime.now() + timedelta(days=3),
        }
        apella_schema = ApellaSchema()
        query_dsl_gql = apella_schema.Query.phases.args(
            query=GQLPhaseQueryInput(
                min_created_time=query["min_created_time"],
                max_created_time=query["max_created_time"],
            ),
            order_by=[
                GQLOrderBy(
                    sort=apella_schema.Phase.created_time.name,
                    direction=GQLDirection.DESC,
                )
            ],
        ).select(
            apella_schema.PhaseConnection.edges.select(
                apella_schema.PhaseEdge.node.select(
                    apella_schema.Phase.id,
                    apella_schema.Phase.created_time.alias("test_created_time"),
                    apella_schema.Phase.start_event.alias("test_start_event").select(  # type: ignore [attr-defined]
                        apella_schema.Event.id,
                    ),
                    apella_schema.Phase.start_event.select(
                        apella_schema.Event.id,
                        apella_schema.Event.start_time,
                    ),
                )
            )
        )
        results = harness.service_account_client.query_graphql_from_schema(query_dsl_gql)
        phases = [edge.node for edge in results.phases.edges]

        assert len(phases) == 1
        assert phases[0].id == str(phase_1.id)
        assert phases[0].get_aliased_field("test_created_time") == phase_1.created_time
        assert phases[0].get_aliased_field("test_start_event").id == str(phase_1.start_event.id)
        assert phases[0].start_event.start_time == phase_1.start_event.start_time
        with pytest.raises(ValueError):
            phases[0].get_aliased_field("test_start_event").start_time

    @pytest.mark.asyncio
    async def test_query_phases_gql_with_label(self, async_session: AsyncSession):
        phase_1 = self._create_mock_phase(harness.event_0.id, None, room_id=harness.room_0.id)

        phase_1 = await harness.phase_store.insert_phase(phase_1)

        query = {
            "min_created_time": datetime.now() - timedelta(days=1),
            "max_created_time": datetime.now() + timedelta(days=3),
        }
        apella_schema = ApellaSchema()
        query_dsl_gql = apella_schema.Query.phases.args(
            query=GQLPhaseQueryInput(
                min_created_time=query["min_created_time"],
                max_created_time=query["max_created_time"],
            ),
            order_by=[
                GQLOrderBy(
                    sort=apella_schema.Phase.created_time.name,
                    direction=GQLDirection.DESC,
                )
            ],
        ).select(
            apella_schema.PhaseConnection.edges.select(
                apella_schema.PhaseEdge.node.select(
                    apella_schema.Phase.id,
                    apella_schema.Phase.created_time,
                    apella_schema.Phase.start_event.select(
                        apella_schema.Event.id,
                        apella_schema.Event.start_time,
                    ),
                )
            )
        )
        results = harness.service_account_client.query_graphql_from_schema(
            query_dsl_gql, "test_label"
        )
        phases = [edge.node for edge in results.phases.edges]

        assert len(phases) == 1
        assert phases[0].id == str(phase_1.id)
        assert phases[0].created_time == phase_1.created_time
        assert phases[0].start_event.start_time == phase_1.start_event.start_time

    @pytest.mark.asyncio
    async def test_query_phases_case_not_found_gql(self, async_session: AsyncSession):
        phase_1 = self._create_mock_phase(harness.event_0.id, None, room_id=harness.room_0.id)
        phase_1.created_time = datetime.now()

        await harness.phase_store.insert_phase(phase_1)

        query = {
            "min_created_time": datetime.now() - timedelta(days=1),
            "max_created_time": datetime.now() + timedelta(days=3),
        }
        apella_schema = ApellaSchema()
        query_dsl_gql = apella_schema.Query.phases.args(
            query=GQLPhaseQueryInput(
                min_created_time=query["min_created_time"],
                max_created_time=query["max_created_time"],
            ),
            order_by=[
                GQLOrderBy(
                    sort=apella_schema.Phase.created_time.name,
                    direction=GQLDirection.DESC,
                )
            ],
        ).select(
            apella_schema.PhaseConnection.edges.select(
                apella_schema.PhaseEdge.node.select(
                    apella_schema.Phase.id,
                    apella_schema.Phase.case.select(
                        apella_schema.ScheduledCase.id,
                    ),
                )
            )
        )
        results = harness.service_account_client.query_graphql_from_schema(query_dsl_gql)
        phases = [edge.node for edge in results.phases.edges]

        assert phases[0].id == str(phase_1.id)
        assert isinstance(phases[0].case, NotFound)
        assert isinstance(phases[0].case.id, NotFound)
