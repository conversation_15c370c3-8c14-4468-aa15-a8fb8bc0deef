# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

import uuid
from typing import Optional

import isodate
import pytest
from graphql import GraphQLError

from apella_cloud_api.api_server_schema import PhaseQueryInput, PhaseUpsertInput
from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_client_schema import GQLPhaseStatus, GQLEventMatchingStatus
from apella_cloud_api.new_input_schema import GQLPhaseUpsertInput
from apella_cloud_api.new_schema_generator.schema_generator_base_classes import NotFound
from api_server.services.phases.phase_store import (
    PhaseModel,
    PhaseRelationshipModel,
    PhaseStatus,
)
from api_server.services.phases.phases_models import PhaseType
from tests_component import harness

apella_schema = ApellaSchema()


class TestUpsertPhases:
    def _run_filter_test(self, phase_1, phase_2, **filters):
        # This function assumes that phase_1 will not be filtered out and phase_2 will be

        harness.phase_store.insert_phase(phase_1)
        harness.phase_store.insert_phase(phase_2)

        query = PhaseQueryInput(type=PhaseType.TURNOVER.name, **filters)

        phases = harness.labeler_client.query_phases(query)

        assert len(phases) == 1
        assert phases[0].id == str(phase_1.id)

    def _create_mock_phase(
        self,
        start_event_id: str,
        end_event_id: str,
        org_id: Optional[str] = None,
        site_id: Optional[str] = None,
        room_id: Optional[str] = None,
        case_id: Optional[str] = None,
        source_type: str = "human_gt",
        phase_type: PhaseType = PhaseType.TURNOVER,
    ) -> PhaseModel:
        phase = PhaseModel()
        phase.id = uuid.uuid4()
        phase.org_id = org_id if org_id is not None else harness.org_0.id
        phase.site_id = site_id if site_id is not None else harness.site_0.id
        phase.room_id = room_id if room_id is not None else harness.room_0.id
        phase.type_id = phase_type.name
        phase.start_event_id = start_event_id
        phase.end_event_id = end_event_id
        phase.case_id = case_id
        phase.source_type = source_type
        phase.status = PhaseStatus.VALID
        return phase

    def _create_mock_relationship(
        self, parent_phase_id: str, child_phase_id: str, org_id: Optional[str] = None
    ):
        phase_relationship = PhaseRelationshipModel()
        phase_relationship.parent_phase_id = parent_phase_id
        phase_relationship.child_phase_id = child_phase_id
        phase_relationship.org_id = org_id if org_id is not None else harness.org_0.id
        return phase_relationship

    def test_query_upsert_phases_creates_phases(self) -> None:
        phase_1 = self._create_mock_phase(harness.event_0.id, harness.event_1.id)

        input = [
            PhaseUpsertInput(
                org_id=phase_1.org_id,
                site_id=phase_1.site_id,
                room_id=phase_1.room_id,
                type_id=phase_1.type_id,
                start_event_id=phase_1.start_event_id,
                end_event_id=phase_1.end_event_id,
                source_type=phase_1.source_type,
                status=PhaseStatus.VALID.name,
                invalidation_reason=None,
            )
        ]

        phases = harness.labeler_client.upsert_phases(input)

        assert len(phases) == 1
        assert phases[0].id
        assert phases[0].type_id == phase_1.type_id
        assert phases[0].start_event.id == phase_1.start_event_id
        assert phases[0].end_event.id == phase_1.end_event_id
        assert phases[0].source_type == phase_1.source_type
        assert phases[0].start_time
        assert phases[0].end_time
        assert phases[0].duration
        assert phases[0].status == phase_1.status.name
        assert phases[0].invalidation_reason is None

    @pytest.mark.asyncio
    async def test_query_upsert_phases_updates_phases(self) -> None:
        phase_1 = await harness.phase_store.insert_phase(
            self._create_mock_phase(harness.event_0.id, harness.event_1.id)
        )

        input = [
            PhaseUpsertInput(
                id=phase_1.id,
                org_id=phase_1.org_id,
                site_id=phase_1.site_id,
                room_id=phase_1.room_id,
                type_id=phase_1.type_id,
                start_event_id=phase_1.start_event_id,
                end_event_id=phase_1.end_event_id,
                case_id=harness.case_0.case_id,
                source_type=phase_1.source_type,
                status=PhaseStatus.VALID.name,
                invalidation_reason=None,
            )
        ]

        phases = harness.labeler_client.upsert_phases(input)

        assert len(phases) == 1
        assert str(phases[0].id) == str(phase_1.id)
        assert phases[0].type_id == phase_1.type_id
        assert phases[0].start_event.id == phase_1.start_event_id
        assert phases[0].end_event.id == phase_1.end_event_id
        assert phases[0].source_type == phase_1.source_type
        assert phases[0].case.id == harness.case_0.case_id
        assert phases[0].start_time == phase_1.start_time()
        assert phases[0].end_time == phase_1.end_time()
        assert phases[0].duration == isodate.duration_isoformat(phase_1.duration())
        assert phases[0].status == phase_1.status.name
        assert phases[0].invalidation_reason == phase_1.invalidation_reason

    @pytest.mark.asyncio
    async def test_query_upsert_phases_updates_phases_gql(self) -> None:
        phase_1 = self._create_mock_phase(harness.event_0.id, harness.event_1.id)
        phase_1 = await harness.phase_store.insert_phase(phase_1)
        upsert_phases_mutation = apella_schema.Mutation.phase_upsert.args(
            input=[
                GQLPhaseUpsertInput(
                    id=str(phase_1.id),
                    org_id=phase_1.org_id,
                    site_id=phase_1.site_id,
                    room_id=phase_1.room_id,
                    type_id=phase_1.type_id,
                    start_event_id=phase_1.start_event_id,
                    end_event_id=phase_1.end_event_id,
                    case_id=harness.case_0.case_id,
                    source_type=phase_1.source_type,
                    status=GQLPhaseStatus.VALID,
                    invalidation_reason=None,
                    event_matching_status=GQLEventMatchingStatus.OVERRIDE,
                )
            ]
        ).select(
            apella_schema.PhaseUpsert.created_phases.select(
                apella_schema.Phase.id,
                apella_schema.Phase.organization.select(
                    apella_schema.Organization.id,
                ),
                apella_schema.Phase.site.select(
                    apella_schema.Site.id,
                ),
                apella_schema.Phase.room.select(
                    apella_schema.Room.id,
                ),
                apella_schema.Phase.type_id,
                apella_schema.Phase.start_event.select(
                    apella_schema.Event.id,
                ),
                apella_schema.Phase.end_event.select(
                    apella_schema.Event.id,
                ),
                apella_schema.Phase.invalidation_reason,
                apella_schema.Phase.source_type,
                apella_schema.Phase.case.select(
                    apella_schema.ScheduledCase.id,
                ),
                apella_schema.Phase.start_time,
                apella_schema.Phase.end_time,
                apella_schema.Phase.duration,
                apella_schema.Phase.status,
                apella_schema.Phase.event_matching_status,
            )
        )
        phases = harness.labeler_client.mutate_graphql_from_schema(
            upsert_phases_mutation
        ).phase_upsert.created_phases
        assert len(phases) == 1
        assert str(phases[0].id) == str(phase_1.id)
        assert phases[0].organization.id == phase_1.org_id
        assert phases[0].site.id == phase_1.site_id
        assert phases[0].room.id == phase_1.room_id
        assert phases[0].type_id == phase_1.type_id
        assert phases[0].start_event.id == phase_1.start_event_id
        assert phases[0].end_event.id == phase_1.end_event_id
        assert phases[0].source_type == phase_1.source_type
        assert phases[0].case.id == harness.case_0.case_id
        assert phases[0].start_time == phase_1.start_time()
        assert phases[0].end_time == phase_1.end_time()
        assert phases[0].duration == phase_1.duration()
        assert phases[0].status.name == phase_1.status.name
        assert phases[0].invalidation_reason == phase_1.invalidation_reason
        assert phase_1.invalidation_reason == phases[0].invalidation_reason
        assert phases[0].event_matching_status == GQLEventMatchingStatus.OVERRIDE

    @pytest.mark.asyncio
    async def test_query_upsert_phases_updates_phases_gql_labeled(self) -> None:
        phase_1 = self._create_mock_phase(harness.event_0.id, harness.event_1.id)
        phase_1 = await harness.phase_store.insert_phase(phase_1)
        upsert_phases_mutation = apella_schema.Mutation.phase_upsert.args(
            input=[
                GQLPhaseUpsertInput(
                    id=str(phase_1.id),
                    org_id=phase_1.org_id,
                    site_id=phase_1.site_id,
                    room_id=phase_1.room_id,
                    type_id=phase_1.type_id,
                    start_event_id=phase_1.start_event_id,
                    end_event_id=phase_1.end_event_id,
                    case_id=harness.case_0.case_id,
                    source_type=phase_1.source_type,
                    status=GQLPhaseStatus.VALID,
                    invalidation_reason=None,
                    event_matching_status=GQLEventMatchingStatus.OVERRIDE,
                )
            ]
        ).select(
            apella_schema.PhaseUpsert.created_phases.select(
                apella_schema.Phase.id,
                apella_schema.Phase.organization.select(
                    apella_schema.Organization.id,
                ),
                apella_schema.Phase.site.select(
                    apella_schema.Site.id,
                ),
                apella_schema.Phase.room.select(
                    apella_schema.Room.id,
                ),
                apella_schema.Phase.type_id,
                apella_schema.Phase.start_event.select(
                    apella_schema.Event.id,
                ),
                apella_schema.Phase.end_event.select(
                    apella_schema.Event.id,
                ),
                apella_schema.Phase.invalidation_reason,
                apella_schema.Phase.source_type,
                apella_schema.Phase.case.select(
                    apella_schema.ScheduledCase.id,
                ),
                apella_schema.Phase.start_time,
                apella_schema.Phase.end_time,
                apella_schema.Phase.duration,
                apella_schema.Phase.status,
                apella_schema.Phase.event_matching_status,
            )
        )
        phases = harness.labeler_client.mutate_graphql_from_schema(
            upsert_phases_mutation, "test_label"
        ).phase_upsert.created_phases
        assert len(phases) == 1
        assert str(phases[0].id) == str(phase_1.id)
        assert phases[0].organization.id == phase_1.org_id
        assert phases[0].site.id == phase_1.site_id
        assert phases[0].room.id == phase_1.room_id
        assert phases[0].type_id == phase_1.type_id
        assert phases[0].start_event.id == phase_1.start_event_id
        assert phases[0].end_event.id == phase_1.end_event_id
        assert phases[0].source_type == phase_1.source_type
        assert phases[0].case.id == harness.case_0.case_id
        assert phases[0].start_time == phase_1.start_time()
        assert phases[0].end_time == phase_1.end_time()
        assert phases[0].duration == phase_1.duration()
        assert phases[0].status.name == phase_1.status.name
        assert phases[0].invalidation_reason == phase_1.invalidation_reason
        assert phase_1.invalidation_reason == phases[0].invalidation_reason
        assert phases[0].event_matching_status == GQLEventMatchingStatus.OVERRIDE

    @pytest.mark.asyncio
    async def test_query_upsert_phases_updates_phases_gql_unmatch_case(self) -> None:
        phase_1 = self._create_mock_phase(harness.event_0.id, harness.event_1.id)
        phase_1 = await harness.phase_store.insert_phase(phase_1)
        upsert_phases_mutation_1 = apella_schema.Mutation.phase_upsert.args(
            input=[
                GQLPhaseUpsertInput(
                    id=str(phase_1.id),
                    org_id=phase_1.org_id,
                    site_id=phase_1.site_id,
                    room_id=phase_1.room_id,
                    type_id=phase_1.type_id,
                    start_event_id=phase_1.start_event_id,
                    end_event_id=phase_1.end_event_id,
                    case_id=harness.case_0.case_id,
                    source_type=phase_1.source_type,
                    status=GQLPhaseStatus.VALID,
                    invalidation_reason=None,
                    event_matching_status=GQLEventMatchingStatus.OVERRIDE,
                )
            ]
        ).select(
            apella_schema.PhaseUpsert.created_phases.select(
                apella_schema.Phase.id,
                apella_schema.Phase.organization.select(
                    apella_schema.Organization.id,
                ),
                apella_schema.Phase.site.select(
                    apella_schema.Site.id,
                ),
                apella_schema.Phase.room.select(
                    apella_schema.Room.id,
                ),
                apella_schema.Phase.type_id,
                apella_schema.Phase.start_event.select(
                    apella_schema.Event.id,
                ),
                apella_schema.Phase.end_event.select(
                    apella_schema.Event.id,
                ),
                apella_schema.Phase.invalidation_reason,
                apella_schema.Phase.source_type,
                apella_schema.Phase.case.select(
                    apella_schema.ScheduledCase.id,
                ),
                apella_schema.Phase.start_time,
                apella_schema.Phase.end_time,
                apella_schema.Phase.duration,
                apella_schema.Phase.status,
                apella_schema.Phase.event_matching_status,
            )
        )
        phases_1 = harness.labeler_client.mutate_graphql_from_schema(
            upsert_phases_mutation_1
        ).phase_upsert.created_phases
        assert len(phases_1) == 1
        assert str(phases_1[0].id) == str(phase_1.id)
        assert phases_1[0].organization.id == phase_1.org_id
        assert phases_1[0].site.id == phase_1.site_id
        assert phases_1[0].room.id == phase_1.room_id
        assert phases_1[0].type_id == phase_1.type_id
        assert phases_1[0].start_event.id == phase_1.start_event_id
        assert phases_1[0].end_event.id == phase_1.end_event_id
        assert phases_1[0].source_type == phase_1.source_type
        assert phases_1[0].case.id == harness.case_0.case_id
        assert phases_1[0].start_time == phase_1.start_time()
        assert phases_1[0].end_time == phase_1.end_time()
        assert phases_1[0].duration == phase_1.duration()
        assert phases_1[0].status.name == phase_1.status.name
        assert phases_1[0].invalidation_reason == phase_1.invalidation_reason
        assert phase_1.invalidation_reason == phases_1[0].invalidation_reason
        assert phases_1[0].event_matching_status == GQLEventMatchingStatus.OVERRIDE

        upsert_phases_mutation_2 = apella_schema.Mutation.phase_upsert.args(
            input=[
                GQLPhaseUpsertInput(
                    id=str(phase_1.id),
                    org_id=phase_1.org_id,
                    site_id=phase_1.site_id,
                    room_id=phase_1.room_id,
                    type_id=phase_1.type_id,
                    start_event_id=phase_1.start_event_id,
                    end_event_id=phase_1.end_event_id,
                    case_id=None,
                    source_type=phase_1.source_type,
                    status=GQLPhaseStatus.VALID,
                    invalidation_reason=None,
                    event_matching_status=GQLEventMatchingStatus.OVERRIDE,
                )
            ]
        ).select(
            apella_schema.PhaseUpsert.created_phases.select(
                apella_schema.Phase.id,
                apella_schema.Phase.organization.select(
                    apella_schema.Organization.id,
                ),
                apella_schema.Phase.site.select(
                    apella_schema.Site.id,
                ),
                apella_schema.Phase.room.select(
                    apella_schema.Room.id,
                ),
                apella_schema.Phase.type_id,
                apella_schema.Phase.start_event.select(
                    apella_schema.Event.id,
                ),
                apella_schema.Phase.end_event.select(
                    apella_schema.Event.id,
                ),
                apella_schema.Phase.invalidation_reason,
                apella_schema.Phase.source_type,
                apella_schema.Phase.case.select(
                    apella_schema.ScheduledCase.id,
                ),
                apella_schema.Phase.start_time,
                apella_schema.Phase.end_time,
                apella_schema.Phase.duration,
                apella_schema.Phase.status,
                apella_schema.Phase.event_matching_status,
            )
        )
        phases_2 = harness.labeler_client.mutate_graphql_from_schema(
            upsert_phases_mutation_2
        ).phase_upsert.created_phases
        assert len(phases_2) == 1
        assert str(phases_2[0].id) == str(phase_1.id)
        assert phases_2[0].organization.id == phase_1.org_id
        assert phases_2[0].site.id == phase_1.site_id
        assert phases_2[0].room.id == phase_1.room_id
        assert phases_2[0].type_id == phase_1.type_id
        assert phases_2[0].start_event.id == phase_1.start_event_id
        assert phases_2[0].end_event.id == phase_1.end_event_id
        assert phases_2[0].source_type == phase_1.source_type
        assert phases_2[0].case.id == NotFound()
        assert phases_2[0].start_time == phase_1.start_time()
        assert phases_2[0].end_time == phase_1.end_time()
        assert phases_2[0].duration == phase_1.duration()
        assert phases_2[0].status.name == phase_1.status.name
        assert phases_2[0].invalidation_reason == phase_1.invalidation_reason
        assert phase_1.invalidation_reason == phases_2[0].invalidation_reason
        assert phases_2[0].event_matching_status == GQLEventMatchingStatus.OVERRIDE

    @pytest.mark.asyncio
    async def test_query_upsert_phases_raise_value_error_empty_gql(self) -> None:
        phase_1 = self._create_mock_phase(harness.event_0.id, harness.event_1.id)
        phase_1 = await harness.phase_store.insert_phase(phase_1)
        upsert_phases_mutation = apella_schema.Mutation.phase_upsert.args(
            input=[
                GQLPhaseUpsertInput(
                    id=str(phase_1.id),
                    org_id=phase_1.org_id,
                    site_id=phase_1.site_id,
                    room_id=phase_1.room_id,
                    type_id=phase_1.type_id,
                    start_event_id=phase_1.start_event_id,
                    end_event_id=phase_1.end_event_id,
                    case_id=harness.case_0.case_id,
                    source_type=phase_1.source_type,
                    status=GQLPhaseStatus.VALID,
                    invalidation_reason=None,
                )
            ]
        ).select(
            apella_schema.PhaseUpsert.created_phases.select(
                apella_schema.Phase.id,
            )
        )
        phases = harness.labeler_client.mutate_graphql_from_schema(
            upsert_phases_mutation
        ).phase_upsert.created_phases
        assert len(phases) == 1
        assert str(phases[0].id) == str(phase_1.id)
        # check that accessing other fields raises an error
        with pytest.raises(ValueError):
            assert phases[0].organization.id == phase_1.org_id

    @pytest.mark.asyncio
    async def test_query_upsert_phases_updates_phases_with_invalid(self) -> None:
        phase_1 = self._create_mock_phase(harness.event_0.id, harness.event_1.id)
        phase_1 = await harness.phase_store.insert_phase(phase_1)
        input = [
            PhaseUpsertInput(
                id=phase_1.id,
                org_id=phase_1.org_id,
                site_id=phase_1.site_id,
                room_id=phase_1.room_id,
                type_id=phase_1.type_id,
                start_event_id=phase_1.start_event_id,
                end_event_id=phase_1.end_event_id,
                case_id=harness.case_0.case_id,
                source_type=phase_1.source_type,
                status=PhaseStatus.INVALID.name,
                invalidation_reason="For test",
            )
        ]

        phases = harness.labeler_client.upsert_phases(input)

        assert len(phases) == 1
        assert str(phases[0].id) == str(phase_1.id)
        assert phases[0].type_id == phase_1.type_id
        assert phases[0].start_event.id == phase_1.start_event_id
        assert phases[0].end_event.id == phase_1.end_event_id
        assert phases[0].source_type == phase_1.source_type
        assert phases[0].case.id == harness.case_0.case_id
        assert phases[0].start_time == phase_1.start_time()
        assert phases[0].end_time == phase_1.end_time()
        assert phases[0].duration == isodate.duration_isoformat(phase_1.duration())
        assert phases[0].status == PhaseStatus.INVALID.name
        assert phases[0].invalidation_reason == "For test"

    @pytest.mark.asyncio
    async def test_query_upsert_phases_updates_and_creates_phases(self) -> None:
        phase_1 = self._create_mock_phase(harness.event_0.id, harness.event_1.id)
        phase_2 = self._create_mock_phase(harness.event_2.id, harness.event_3.id)
        phase_1 = await harness.phase_store.insert_phase(phase_1)
        phase_list = [phase_1, phase_2]
        input = [
            PhaseUpsertInput(
                org_id=phase.org_id,
                site_id=phase.site_id,
                room_id=phase.room_id,
                type_id=phase.type_id,
                start_event_id=phase.start_event_id,
                end_event_id=phase.end_event_id,
                source_type=phase.source_type,
                status=PhaseStatus.VALID.name,
                invalidation_reason=None,
            )
            for phase in phase_list
        ]
        input[0].id = str(phase_1.id)
        input[0].case_id = harness.case_0.case_id
        input[1].case_id = harness.case_1.case_id
        phases = harness.labeler_client.upsert_phases(input)
        phases = sorted(phases, key=lambda phase: phase.start_time)
        assert len(phases) == 2
        assert str(phases[0].id) == str(phase_1.id)
        assert phases[0].type_id == phase_1.type_id
        assert phases[0].start_event.id == phase_1.start_event_id
        assert phases[0].end_event.id == phase_1.end_event_id
        assert phases[0].source_type == phase_1.source_type
        assert phases[0].case.id
        assert phases[0].start_time == phase_1.start_time()
        assert phases[0].end_time == phase_1.end_time()
        assert phases[0].duration == isodate.duration_isoformat(phase_1.duration())
        assert phases[0].status == phase_1.status.name
        assert phases[0].invalidation_reason == phase_1.invalidation_reason

        assert phases[1].id
        assert phases[1].type_id == phase_2.type_id
        assert phases[1].start_event.id == phase_2.start_event_id
        assert phases[1].end_event.id == phase_2.end_event_id
        assert phases[1].source_type == phase_2.source_type
        assert phases[1].case.id
        assert phases[1].start_time
        assert phases[1].end_time
        assert phases[1].duration

    @pytest.mark.asyncio
    async def test_update_phase_with_etags_gql(self):
        phase_1 = self._create_mock_phase(harness.event_0.id, harness.event_1.id)
        phase_1 = await harness.phase_store.insert_phase(phase_1)
        mutation = apella_schema.Mutation.phase_upsert.args(
            input=[
                GQLPhaseUpsertInput(
                    id=phase_1.id,
                    org_id=phase_1.org_id,
                    site_id=phase_1.site_id,
                    room_id=phase_1.room_id,
                    type_id=phase_1.type_id,
                    start_event_id=phase_1.start_event_id,
                    end_event_id=phase_1.end_event_id,
                    case_id=harness.case_0.case_id,
                    source_type=phase_1.source_type,
                    status=GQLPhaseStatus.VALID,
                    etag=phase_1.etag,
                )
            ]
        ).select(apella_schema.PhaseUpsert.success)
        harness.service_account_client.mutate_graphql_from_schema(mutation)
        with pytest.raises(GraphQLError) as concurrency_error:
            mutation = apella_schema.Mutation.phase_upsert.args(
                input=[
                    GQLPhaseUpsertInput(
                        id=phase_1.id,
                        org_id=phase_1.org_id,
                        site_id=phase_1.site_id,
                        room_id=phase_1.room_id,
                        type_id=phase_1.type_id,
                        start_event_id=phase_1.start_event_id,
                        end_event_id=phase_1.end_event_id,
                        case_id=harness.case_0.case_id,
                        source_type=phase_1.source_type,
                        status=GQLPhaseStatus.VALID,
                        etag=str(uuid.uuid4()),
                    )
                ]
            ).select(apella_schema.PhaseUpsert.success)
            harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert "etag mismatch for PhaseModel with provided etag" in str(
            concurrency_error.value.args
        )

    @pytest.mark.asyncio
    async def test_update_phase_etag_unset(self):
        phase_1 = self._create_mock_phase(harness.event_0.id, harness.event_1.id)
        phase_1 = await harness.phase_store.insert_phase(phase_1)
        mutation = apella_schema.Mutation.phase_upsert.args(
            input=[
                GQLPhaseUpsertInput(
                    id=phase_1.id,
                    org_id=phase_1.org_id,
                    site_id=phase_1.site_id,
                    room_id=phase_1.room_id,
                    type_id=phase_1.type_id,
                    start_event_id=phase_1.start_event_id,
                    end_event_id=phase_1.end_event_id,
                    case_id=harness.case_0.case_id,
                    source_type=phase_1.source_type,
                    status=GQLPhaseStatus.VALID,
                    etag=phase_1.etag,
                )
            ]
        ).select(apella_schema.PhaseUpsert.success)
        harness.service_account_client.mutate_graphql_from_schema(mutation)
        with pytest.raises(GraphQLError) as concurrency_error:
            mutation = apella_schema.Mutation.phase_upsert.args(
                input=[
                    GQLPhaseUpsertInput(
                        id=phase_1.id,
                        org_id=phase_1.org_id,
                        site_id=phase_1.site_id,
                        room_id=phase_1.room_id,
                        type_id=phase_1.type_id,
                        start_event_id=phase_1.start_event_id,
                        end_event_id=phase_1.end_event_id,
                        case_id=harness.case_0.case_id,
                        source_type=phase_1.source_type,
                        status=GQLPhaseStatus.VALID,
                        etag=None,
                    )
                ]
            ).select(apella_schema.PhaseUpsert.success)
            harness.service_account_client.mutate_graphql_from_schema(mutation)
        assert "Etag cannot be unset" in str(concurrency_error.value.args)
