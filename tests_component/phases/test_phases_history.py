from datetime import datetime, timedelta, timezone
from typing import Any, Sequence
from uuid import uuid4

import pytest
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from api_server.services.phases.phase_store import PhaseModel, PhaseStatus
from tests_component import harness
from tests_component.case.graphql.test_query_cases_history import mock_case


def copy_phase(phase: PhaseModel) -> PhaseModel:
    new_phase = PhaseModel()
    new_phase.id = phase.id
    new_phase.org_id = phase.org_id
    new_phase.site_id = phase.site_id
    new_phase.room_id = phase.room_id
    new_phase.type_id = phase.type_id
    new_phase.start_event_id = phase.start_event_id
    new_phase.end_event_id = phase.end_event_id
    new_phase.case_id = phase.case_id
    new_phase.source_type = phase.source_type
    new_phase.status = phase.status
    return new_phase


@pytest.mark.asyncio
async def test_phase_history(async_session: AsyncSession) -> None:
    """Test that when we modify a phase, we are increasing the version number in
    phases_history and the id of the modified `phase` remains constant"""
    start_time = datetime(2020, 10, 10, 7, 0, 0, tzinfo=timezone.utc)
    case_0 = mock_case(start_time=start_time, end_time=start_time + timedelta(hours=1))

    await harness.case_store.create_case(case=case_0, session=async_session)
    phase_0 = PhaseModel()
    phase_0.id = uuid4()
    phase_0.org_id = harness.org_0.id
    phase_0.site_id = harness.site_0.id
    phase_0.room_id = harness.room_0.id
    phase_0.type_id = "CASE"
    phase_0.start_event_id = harness.event_0.id
    phase_0.end_event_id = harness.event_3.id
    phase_0.case_id = case_0.case_id
    phase_0.source_type = "some source"
    phase_0.status = PhaseStatus.VALID
    await harness.phase_store.insert_phase(phase_0)

    found = await phase_history_query_helper(async_session)

    # first check that we only have one version
    assert len(found) == 1
    first_phase = found[0]
    assert first_phase.version == 1
    await harness.phase_store.get_phases_by_ids([str(phase_0.id)])

    # now modify the phase and upsert it
    updated_phase = copy_phase(phase_0)
    updated_phase.end_event_id = harness.event_2.id
    await harness.phase_store.upsert_phases([updated_phase])

    # query again, and make sure version was updated and id is the same
    found = await phase_history_query_helper(async_session)
    found = sorted(found, key=lambda x: x.version)
    assert len(found) == 2
    assert found[1].version == 2
    assert found[0].id == found[1].id


@pytest.mark.asyncio
async def phase_history_query_helper(session: AsyncSession) -> Sequence[Any]:
    phase_history_class = PhaseModel.__history_mapper__.class_  # type: ignore [attr-defined]
    query = select(phase_history_class)
    result_set = await session.execute(query)
    return result_set.scalars().all()
