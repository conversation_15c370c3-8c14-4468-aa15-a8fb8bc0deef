from datetime import timedelta, datetime
from uuid import uuid4

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_input_schema import GQLCaseStaffPlanInput, GQLScheduledCaseQueryInput
from api_server.services.plan.case_staff_plan_store import CaseStaffPlanModel
from api_server.services.staff.staff_store import StaffModel
from tests_component import harness


def mock_staff(external_staff_id: str) -> StaffModel:
    staff = StaffModel()
    staff.first_name = "Johnny"
    staff.last_name = "Tsunami"
    staff.org_id = harness.case_0.org_id
    staff.external_staff_id = external_staff_id
    return staff


class TestQueryCaseStaffPlan:
    @pytest.mark.asyncio
    async def test_query_case_staff_plan_all(self) -> None:
        apella_schema = ApellaSchema()
        case = harness.case_0
        mock_staff_1 = mock_staff(external_staff_id="foo_id")
        mock_staff_2 = mock_staff(external_staff_id="foo_id_2")
        [db_staff_1, db_staff_2] = await harness.staff_store.upsert_staff(
            staff=[mock_staff_1, mock_staff_2]
        )

        case_staff_plan = CaseStaffPlanModel()

        case_staff_plan.case_id = case.case_id
        case_staff_plan.staff_id = db_staff_1.id
        case_staff_plan.role = "role"
        case_staff_plan.id = uuid4()
        case_staff_plan.org_id = case.org_id
        case_staff_plan.site_id = case.site_id

        case_staff_plan_2 = CaseStaffPlanModel()
        case_staff_plan_2.case_id = case.case_id
        case_staff_plan_2.staff_id = db_staff_2.id
        case_staff_plan_2.role = "role_b"
        case_staff_plan_2.id = uuid4()
        case_staff_plan_2.org_id = case.org_id
        case_staff_plan_2.site_id = case.site_id

        await harness.case_staff_plan_store.upsert([case_staff_plan, case_staff_plan_2])

        query = apella_schema.Query.scheduled_cases.args(
            query=GQLScheduledCaseQueryInput(
                min_start_time=case.scheduled_start_time,
                max_start_time=case.scheduled_start_time + timedelta(hours=1),
            ),
        ).select(
            apella_schema.ScheduledCaseConnection.edges.select(
                apella_schema.ScheduledCaseEdge.node.select(
                    apella_schema.ScheduledCase.id,
                    apella_schema.ScheduledCase.case_staff_plan.args(
                        query=GQLCaseStaffPlanInput()
                    ).select(
                        apella_schema.CaseStaffPlanConnection.edges.select(
                            apella_schema.CaseStaffPlanEdge.node.select(
                                apella_schema.CaseStaffPlan.role,
                                apella_schema.CaseStaffPlan.staff.select(apella_schema.Staff.id),
                            )
                        ),
                    ),
                )
            )
        )
        results = harness.service_account_client.query_graphql_from_schema(query)

        found_case = next(
            case_node
            for case_node in results.scheduled_cases.edges
            if case_node.node.id == case.case_id
        )

        assert len(found_case.node.case_staff_plan.edges) == 2

        cs_plans = [cs.node for cs in found_case.node.case_staff_plan.edges]

        assert any(str(db_staff_1.id) == cs.staff.id and "role" == cs.role for cs in cs_plans)
        assert any(str(db_staff_2.id) == cs.staff.id and "role_b" == cs.role for cs in cs_plans)

    @pytest.mark.asyncio
    async def query_case_staff_plan_by_staff(self, async_session: AsyncSession) -> None:
        apella_schema = ApellaSchema()
        case = harness.case_0
        mock_staff_1 = mock_staff(external_staff_id="foo_id")
        mock_staff_2 = mock_staff(external_staff_id="foo_id_2")
        [db_staff_1, db_staff_2] = await harness.staff_store.upsert_staff(
            staff=[mock_staff_1, mock_staff_2], session=async_session
        )

        case_staff_plan = CaseStaffPlanModel()

        case_staff_plan.case_id = case.case_id
        case_staff_plan.staff_id = db_staff_1.id
        case_staff_plan.role = "role"
        case_staff_plan.id = uuid4()
        case_staff_plan.org_id = case.org_id
        case_staff_plan.site_id = case.site_id

        case_staff_plan_2 = CaseStaffPlanModel()
        case_staff_plan_2.case_id = case.case_id
        case_staff_plan_2.staff_id = db_staff_2.id
        case_staff_plan_2.role = "role_b"
        case_staff_plan_2.id = uuid4()
        case_staff_plan_2.org_id = case.org_id
        case_staff_plan_2.site_id = case.site_id

        await harness.case_staff_plan_store.upsert([case_staff_plan, case_staff_plan_2])

        query = apella_schema.Query.scheduled_cases.args(
            query=GQLScheduledCaseQueryInput(
                min_start_time=case.scheduled_start_time,
                max_start_time=case.scheduled_start_time + timedelta(hours=1),
            ),
        ).select(
            apella_schema.ScheduledCaseConnection.edges.select(
                apella_schema.ScheduledCaseEdge.node.select(
                    apella_schema.ScheduledCase.id,
                    apella_schema.ScheduledCase.case_staff_plan.args(
                        query=GQLCaseStaffPlanInput(staff_ids=[str(db_staff_2.id)])
                    ).select(
                        apella_schema.CaseStaffPlanConnection.edges.select(
                            apella_schema.CaseStaffPlanEdge.node.select(
                                apella_schema.CaseStaffPlan.role,
                                apella_schema.CaseStaffPlan.staff.select(apella_schema.Staff.id),
                            )
                        ),
                    ),
                )
            )
        )
        results = harness.service_account_client.query_graphql_from_schema(query)

        found_case = next(
            case_node
            for case_node in results.scheduled_cases.edges
            if case_node.node.id == case.case_id
        )

        assert len(found_case.node.case_staff_plan.edges) == 1

        cs_plans = [cs.node for cs in found_case.node.case_staff_plan.edges]

        assert all(str(db_staff_1.id) != cs.staff.id for cs in cs_plans)
        assert any(str(db_staff_2.id) == cs.staff.id and "role_b" == cs.role for cs in cs_plans)

    @pytest.mark.asyncio
    async def query_case_staff_plan_by_archived_false(self, async_session: AsyncSession) -> None:
        apella_schema = ApellaSchema()
        case = harness.case_0
        mock_staff_1 = mock_staff(external_staff_id="foo_id")
        mock_staff_2 = mock_staff(external_staff_id="foo_id_2")
        [db_staff_1, db_staff_2] = await harness.staff_store.upsert_staff(
            staff=[mock_staff_1, mock_staff_2],
            session=async_session,
        )

        case_staff_plan = CaseStaffPlanModel()

        case_staff_plan.case_id = case.case_id
        case_staff_plan.staff_id = db_staff_1.id
        case_staff_plan.role = "role"
        case_staff_plan.id = uuid4()
        case_staff_plan.archived_time = datetime.now()
        case_staff_plan.org_id = case.org_id
        case_staff_plan.site_id = case.site_id

        case_staff_plan_2 = CaseStaffPlanModel()
        case_staff_plan_2.case_id = case.case_id
        case_staff_plan_2.staff_id = db_staff_2.id
        case_staff_plan_2.role = "role_b"
        case_staff_plan_2.id = uuid4()
        case_staff_plan_2.archived_time = datetime.now()
        case_staff_plan_2.org_id = case.org_id
        case_staff_plan_2.site_id = case.site_id

        await harness.case_staff_plan_store.upsert([case_staff_plan, case_staff_plan_2])

        query = apella_schema.Query.scheduled_cases.args(
            query=GQLScheduledCaseQueryInput(
                min_start_time=case.scheduled_start_time,
                max_start_time=case.scheduled_start_time + timedelta(hours=1),
            ),
        ).select(
            apella_schema.ScheduledCaseConnection.edges.select(
                apella_schema.ScheduledCaseEdge.node.select(
                    apella_schema.ScheduledCase.id,
                    apella_schema.ScheduledCase.case_staff_plan.args(
                        query=GQLCaseStaffPlanInput(include_archived=False)
                    ).select(
                        apella_schema.CaseStaffPlanConnection.edges.select(
                            apella_schema.CaseStaffPlanEdge.node.select(
                                apella_schema.CaseStaffPlan.role,
                                apella_schema.CaseStaffPlan.staff.select(apella_schema.Staff.id),
                            )
                        ),
                    ),
                )
            )
        )
        results = harness.service_account_client.query_graphql_from_schema(query)

        found_case = next(
            case_node
            for case_node in results.scheduled_cases.edges
            if case_node.node.id == case.case_id
        )

        assert len(found_case.node.case_staff_plan.edges) == 0

    @pytest.mark.asyncio
    async def query_case_staff_plan_by_archived_true(self, async_session: AsyncSession) -> None:
        apella_schema = ApellaSchema()
        case = harness.case_0
        mock_staff_1 = mock_staff(external_staff_id="foo_id")
        mock_staff_2 = mock_staff(external_staff_id="foo_id_2")
        [db_staff_1, db_staff_2] = await harness.staff_store.upsert_staff(
            staff=[mock_staff_1, mock_staff_2], session=async_session
        )

        case_staff_plan = CaseStaffPlanModel()

        case_staff_plan.case_id = case.case_id
        case_staff_plan.staff_id = db_staff_1.id
        case_staff_plan.role = "role"
        case_staff_plan.id = uuid4()
        case_staff_plan.archived_time = datetime.now()
        case_staff_plan.org_id = case.org_id
        case_staff_plan.site_id = case.site_id

        case_staff_plan_2 = CaseStaffPlanModel()
        case_staff_plan_2.case_id = case.case_id
        case_staff_plan_2.staff_id = db_staff_2.id
        case_staff_plan_2.role = "role_b"
        case_staff_plan_2.id = uuid4()
        case_staff_plan_2.archived_time = datetime.now()
        case_staff_plan_2.org_id = case.org_id
        case_staff_plan_2.site_id = case.site_id

        await harness.case_staff_plan_store.upsert([case_staff_plan, case_staff_plan_2])

        query = apella_schema.Query.scheduled_cases.args(
            query=GQLScheduledCaseQueryInput(
                min_start_time=case.scheduled_start_time,
                max_start_time=case.scheduled_start_time + timedelta(hours=1),
            ),
        ).select(
            apella_schema.ScheduledCaseConnection.edges.select(
                apella_schema.ScheduledCaseEdge.node.select(
                    apella_schema.ScheduledCase.id,
                    apella_schema.ScheduledCase.case_staff_plan.args(
                        query=GQLCaseStaffPlanInput(include_archived=True)
                    ).select(
                        apella_schema.CaseStaffPlanConnection.edges.select(
                            apella_schema.CaseStaffPlanEdge.node.select(
                                apella_schema.CaseStaffPlan.role,
                                apella_schema.CaseStaffPlan.staff.select(apella_schema.Staff.id),
                            )
                        ),
                    ),
                )
            )
        )
        results = harness.service_account_client.query_graphql_from_schema(query)

        found_case = next(
            case_node
            for case_node in results.scheduled_cases.edges
            if case_node.node.id == case.case_id
        )

        assert len(found_case.node.case_staff_plan.edges) == 2

        cs_plans = [cs.node for cs in found_case.node.case_staff_plan.edges]

        assert any(str(db_staff_1.id) == cs.staff.id and "role" == cs.role for cs in cs_plans)
        assert any(str(db_staff_2.id) == cs.staff.id and "role_b" == cs.role for cs in cs_plans)
