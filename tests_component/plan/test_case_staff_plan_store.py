from uuid import uuid4

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from api_server.services.plan.case_staff_plan_store import CaseStaffPlanModel
from api_server.services.staff.staff_store import StaffModel
from tests_component import harness


def mock_staff(external_staff_id: str) -> StaffModel:
    staff = StaffModel()
    staff.first_name = "Johnny"
    staff.last_name = "Tsunami"
    staff.org_id = harness.case_0.org_id
    staff.external_staff_id = external_staff_id
    return staff


class TestQueryCaseStaffPlanStore:
    @pytest.mark.asyncio
    async def upsert_with_ids(self, async_session: AsyncSession) -> None:
        case = harness.case_0
        mock_staff_1 = mock_staff(external_staff_id="foo_id")

        [db_staff_1] = await harness.staff_store.upsert_staff(
            staff=[mock_staff_1], session=async_session
        )

        id_1 = uuid4()
        id_2 = uuid4()

        case_staff_plan_1 = CaseStaffPlanModel()
        case_staff_plan_1.case_id = case.case_id
        case_staff_plan_1.staff_id = db_staff_1.id
        case_staff_plan_1.role = "role"
        case_staff_plan_1.id = id_1
        case_staff_plan_1.org_id = case.org_id
        case_staff_plan_1.site_id = case.site_id

        case_staff_plan_2 = CaseStaffPlanModel()
        case_staff_plan_2.case_id = case.case_id
        case_staff_plan_2.staff_id = db_staff_1.id
        case_staff_plan_2.role = "role"
        case_staff_plan_2.id = id_2
        case_staff_plan_2.org_id = case.org_id
        case_staff_plan_2.site_id = case.site_id

        cases_to_upsert = [case_staff_plan_2, case_staff_plan_1]
        case_staff_plans = await harness.case_staff_plan_store.upsert(cases_to_upsert)

        assert len(case_staff_plans) == len(cases_to_upsert)
        ids = [str(cs.id) for cs in case_staff_plans if cs.id is not None]
        assert len(ids) == len(cases_to_upsert)
        assert str(id_1) in ids
        assert str(id_2) in ids

    @pytest.mark.asyncio
    async def upsert_with_no_ids(self, async_session: AsyncSession) -> None:
        case = harness.case_0
        mock_staff_1 = mock_staff(external_staff_id="foo_id")

        [db_staff_1] = await harness.staff_store.upsert_staff(
            staff=[mock_staff_1], session=async_session
        )

        case_staff_plan_1 = CaseStaffPlanModel()
        case_staff_plan_1.case_id = case.case_id
        case_staff_plan_1.staff_id = db_staff_1.id
        case_staff_plan_1.role = "role"
        case_staff_plan_1.org_id = case.org_id
        case_staff_plan_1.site_id = case.site_id

        case_staff_plan_2 = CaseStaffPlanModel()
        case_staff_plan_2.case_id = case.case_id
        case_staff_plan_2.staff_id = db_staff_1.id
        case_staff_plan_2.role = "role"
        case_staff_plan_2.org_id = case.org_id
        case_staff_plan_2.site_id = case.site_id

        cases_to_upsert = [case_staff_plan_2, case_staff_plan_1]
        case_staff_plans = await harness.case_staff_plan_store.upsert(cases_to_upsert)

        assert len(case_staff_plans) == len(cases_to_upsert)
        ids = [str(cs.id) for cs in case_staff_plans if cs.id is not None]
        assert len(ids) == len(cases_to_upsert)

    @pytest.mark.asyncio
    async def upsert_with_and_without_ids(self, async_session: AsyncSession) -> None:
        case = harness.case_0
        mock_staff_1 = mock_staff(external_staff_id="foo_id")

        [db_staff_1] = await harness.staff_store.upsert_staff(
            staff=[mock_staff_1], session=async_session
        )

        id = uuid4()

        case_staff_plan_1 = CaseStaffPlanModel()
        case_staff_plan_1.case_id = case.case_id
        case_staff_plan_1.staff_id = db_staff_1.id
        case_staff_plan_1.role = "role"
        case_staff_plan_1.org_id = case.org_id
        case_staff_plan_1.site_id = case.site_id

        case_staff_plan_2 = CaseStaffPlanModel()
        case_staff_plan_2.case_id = case.case_id
        case_staff_plan_2.staff_id = db_staff_1.id
        case_staff_plan_2.role = "role"
        case_staff_plan_2.id = id
        case_staff_plan_2.org_id = case.org_id
        case_staff_plan_2.site_id = case.site_id

        cases_to_upsert = [case_staff_plan_2, case_staff_plan_1]
        case_staff_plans = await harness.case_staff_plan_store.upsert(cases_to_upsert)

        assert len(case_staff_plans) == len(cases_to_upsert)
        ids = [str(cs.id) for cs in case_staff_plans if cs.id is not None]
        assert len(ids) == len(cases_to_upsert)
        assert str(id) in ids
