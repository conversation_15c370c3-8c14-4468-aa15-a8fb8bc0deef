# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

from datetime import datetime
from uuid import uuid4

from apella_cloud_api import (
    CaseInfoDto,
    CaseRawDto,
    CaseUpdateDto,
    ProcedureUpsertInput,
)
from apella_cloud_api.api_server_schema import (
    CaseProceduresUpsertInput,
    ProcedureQueryInput,
    Procedure,
)
from api_server.services.case.case_status import SCHEDULED
from api_server.services.room.room_store import RoomModel
from tests_component import harness


def mock_procedure(name: str):
    procedure = ProcedureUpsertInput()
    procedure.name = name
    procedure.org_id = harness.org_0.id

    return procedure


def mock_case(room: RoomModel):
    return CaseInfoDto(
        case_id=str(uuid4()),
        external_case_id=str(uuid4()),
        organization_id=room.org_id,
        site_id=room.site_id,
        room_id=room.id,
        status=SCHEDULED,
        scheduled_start_time=datetime(2022, 1, 1, 12, 0, 0),
        scheduled_end_time=datetime(2022, 1, 1, 13, 0, 0),
    )


def mock_case_raw():
    raw_id = str(uuid4())
    return CaseRawDto(
        raw={"raw_for": "case 0"},
        raw_id=raw_id,
        external_message_id=raw_id,
        organization_id=harness.case_0.org_id,
    )


def test_query_procedures_by_names() -> None:
    procedure_1 = mock_procedure(name="heart")
    procedure_2 = mock_procedure(name="endo")
    procedure_3 = mock_procedure(name="knee")

    db_procedures = harness.service_account_client.upsert_procedures(
        procedures=[procedure_1, procedure_2, procedure_3]
    )

    query = ProcedureQueryInput(names=[procedure_1.name, procedure_2.name])
    result = harness.service_account_client.query_procedures(procedure_query_input=query)

    # Assert the procedure
    assert len(result) == 2
    for result_procedure in result:
        assert any(result_procedure.name == db_procedure.name for db_procedure in db_procedures)


def test_query_primary_procedures() -> None:
    procedure_1 = mock_procedure(name="heart")
    procedure_2 = mock_procedure(name="endo")
    procedure_3 = mock_procedure(name="knee")

    db_procedures = harness.service_account_client.upsert_procedures(
        procedures=[procedure_1, procedure_2, procedure_3]
    )

    query = ProcedureQueryInput(names=[procedure_1.name, procedure_2.name])
    result = harness.service_account_client.query_procedures(procedure_query_input=query)

    # Assert the procedure
    assert len(result) == 2
    for result_procedure in result:
        assert any(result_procedure.name == db_procedure.name for db_procedure in db_procedures)


def test_query_procedures_from_site_restricted_user() -> None:
    # Create procedures
    procedure_1 = mock_procedure(name="heart")
    procedure_2 = mock_procedure(name="endo")
    db_procedure_1 = harness.service_account_client.upsert_procedures(procedures=[procedure_1])[0]
    db_procedure_2 = harness.service_account_client.upsert_procedures(procedures=[procedure_2])[0]

    # Create cases
    case_1 = mock_case(harness.room_0)
    case_2 = mock_case(harness.room_2)
    case_raw_1 = mock_case_raw()
    case_raw_2 = mock_case_raw()
    harness.service_account_client.create_raw_case_event(case_raw_1)
    harness.service_account_client.create_raw_case_event(case_raw_2)
    harness.service_account_client.upsert_case(
        CaseUpdateDto(case_1, case_raw_1.external_message_id)
    )
    harness.service_account_client.upsert_case(
        CaseUpdateDto(case_2, case_raw_2.external_message_id)
    )

    # Create case procedures
    harness.service_account_client.upsert_case_procedures(
        [
            CaseProceduresUpsertInput(case_id=case_1.case_id, procedure_id=db_procedure_1.id),
            CaseProceduresUpsertInput(case_id=case_2.case_id, procedure_id=db_procedure_2.id),
        ]
    )

    query = ProcedureQueryInput()
    result = harness.site_0_scoped_client.query_procedures(query)

    # Assert only procedure_1 is returned. Procedure_2 is assigned to case_2 which is from a site
    # the user doesn't have access to.
    assert len(result) == 1
    assert result[0].name == procedure_1.name


def test_query_procedures_returns_unique_procedures() -> None:
    def assert_unique_procedure_ids(procedures: list[Procedure]) -> None:
        procedure_ids = list(map(lambda procedure: procedure.id, procedures))
        assert len(procedures) == len(set(procedure_ids))

    # Given procedures exist
    created_procedures = harness.service_account_client.upsert_procedures(
        procedures=[
            mock_procedure(name="heart"),
            mock_procedure(name="endo"),
            mock_procedure(name="knee"),
        ]
    )
    procedure_1 = next(p for p in created_procedures if p.name == "heart")
    procedure_2 = next(p for p in created_procedures if p.name == "endo")
    procedure_3 = next(p for p in created_procedures if p.name == "knee")

    # Given cases exist
    case_1 = mock_case(harness.room_0)
    case_2 = mock_case(harness.room_2)
    case_raw_1 = mock_case_raw()
    case_raw_2 = mock_case_raw()
    harness.service_account_client.create_raw_case_event(case_raw_1)
    harness.service_account_client.create_raw_case_event(case_raw_2)

    harness.service_account_client.upsert_case(
        CaseUpdateDto(case_1, case_raw_1.external_message_id)
    )
    harness.service_account_client.upsert_case(
        CaseUpdateDto(case_2, case_raw_2.external_message_id)
    )

    # Given case procedures exist
    harness.service_account_client.upsert_case_procedures(
        [
            CaseProceduresUpsertInput(
                case_id=case_1.case_id, procedure_id=procedure_1.id, hierarchy=1
            ),
            CaseProceduresUpsertInput(
                case_id=case_1.case_id, procedure_id=procedure_2.id, hierarchy=0
            ),
            CaseProceduresUpsertInput(
                case_id=case_1.case_id, procedure_id=procedure_3.id, hierarchy=1
            ),
            CaseProceduresUpsertInput(
                case_id=case_2.case_id, procedure_id=procedure_1.id, hierarchy=1
            ),
            CaseProceduresUpsertInput(
                case_id=case_2.case_id, procedure_id=procedure_2.id, hierarchy=0
            ),
            CaseProceduresUpsertInput(
                case_id=case_2.case_id, procedure_id=procedure_3.id, hierarchy=1
            ),
        ]
    )

    # When
    actual_procedures_queried_by_name = harness.service_account_client.query_procedures(
        procedure_query_input=ProcedureQueryInput(names=[procedure_1.name, procedure_2.name])
    )
    actual_procedures_queried_by_org_id = harness.service_account_client.query_procedures(
        procedure_query_input=ProcedureQueryInput(org_id=harness.org_0.id)
    )
    actual_procedures_queried_by_case_id = harness.service_account_client.query_procedures(
        procedure_query_input=ProcedureQueryInput(case_id=case_1.case_id)
    )
    actual_procedures_queried_by_hierarchy = harness.service_account_client.query_procedures(
        procedure_query_input=ProcedureQueryInput(hierarchy=1)
    )

    # Then
    assert_unique_procedure_ids(actual_procedures_queried_by_name)
    assert_unique_procedure_ids(actual_procedures_queried_by_org_id)
    assert_unique_procedure_ids(actual_procedures_queried_by_case_id)
    assert_unique_procedure_ids(actual_procedures_queried_by_hierarchy)


def test_upsert_procedures_with_duplicates() -> None:
    procedure_1 = mock_procedure(name="heart")
    procedure_2 = mock_procedure(name="heart")
    procedure_3 = mock_procedure(name="endo")
    procedure_4 = mock_procedure(name="endo")

    db_procedures = harness.service_account_client.upsert_procedures(
        procedures=[procedure_1, procedure_2, procedure_3, procedure_4]
    )

    query = ProcedureQueryInput(
        names=[procedure_1.name, procedure_2.name, procedure_3.name, procedure_4.name],
        org_id=harness.org_0.id,
    )
    result = harness.service_account_client.query_procedures(procedure_query_input=query)

    # Assert the procedure
    assert len(result) == 2
    assert result[0].name == db_procedures[0].name
    assert result[1].name == db_procedures[1].name


def test_upsert_procedures_already_exists() -> None:
    procedure_1 = mock_procedure(name="heart")

    harness.service_account_client.upsert_procedures(procedures=[procedure_1])

    db_procedures = harness.service_account_client.upsert_procedures(procedures=[procedure_1])

    # Assert the procedure is returned
    assert len(db_procedures) == 1
