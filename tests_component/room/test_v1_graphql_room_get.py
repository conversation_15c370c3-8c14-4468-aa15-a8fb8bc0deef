import pytest

from datetime import timedelta

from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_client_schema import GQLRoomStatusName
from auth.permissions import READ_ANY_ROOM
from tests_component import harness


@pytest.mark.asyncio
async def test_get_room_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        f"""
    query {{
        room(id: "{harness.room_0.id}") {{
            id
            name
        }}
    }}
    """
    )
    assert result == {
        "data": {"room": None},
        "errors": [
            {
                "locations": [{"column": 9, "line": 3}],
                "message": "Not Authorized: No authorization found",
                "path": ["room"],
            }
        ],
    }


@pytest.mark.asyncio
async def test_get_room_no_permissions() -> None:
    result = harness.user_without_permissions_client.execute_graphql(
        f"""
        query {{
          room(id: "{harness.room_0.id}") {{
              id
              name
          }}
        }}
        """
    )
    assert result == {
        "data": {"room": None},
        "errors": [
            {
                "locations": [{"column": 11, "line": 3}],
                "message": f"User does not have permission '{READ_ANY_ROOM}'",
                "path": ["room"],
            }
        ],
    }


def test_get_room_turnover() -> None:
    miss = harness.labeler_client.execute_graphql(
        f"""
    query {{
        room(id: "{harness.room_0.id}") {{
            turnovers(
                query: {{
                    minEndTime: "{(harness.case_0.scheduled_end_time - timedelta(days=1)).isoformat()}",
                    maxStartTime: "{(harness.case_0.scheduled_start_time + timedelta(days=1)).isoformat()}",
                    turnoverId: "not-a-real-id"
                }}
            ) {{
                precedingCase {{
                    id
                }}
                followingCase {{
                    id
                }}
            }}
        }}
    }}
    """
    )
    assert miss == {
        "data": {
            "room": {
                "turnovers": [],
            }
        }
    }

    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        room(id: "{harness.room_0.id}") {{
            turnovers(
                query: {{
                    minEndTime: "{(harness.case_0.scheduled_end_time - timedelta(days=1)).isoformat()}",
                    maxStartTime: "{(harness.case_0.scheduled_start_time + timedelta(days=1)).isoformat()}",
                    turnoverId: "turnover-case:{harness.case_0.case_id}-case:{harness.case_1.case_id}"
                }}
            ) {{
                status {{
                    name
                }}
                precedingCase {{
                    id
                }}
                followingCase {{
                    id
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": {
            "room": {
                "turnovers": [
                    {
                        "precedingCase": {"id": "case:" + harness.case_0.case_id},
                        "followingCase": {"id": "case:" + harness.case_1.case_id},
                        "status": {"name": "CLEANING"},
                    }
                ],
            }
        }
    }


@pytest.mark.asyncio
async def test_get_room_turnovers() -> None:
    miss_result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        room(id: "{harness.room_0.id}") {{
            apellaCases(
                query: {{
                    minEndTime: "{(harness.case_0.scheduled_end_time + timedelta(hours=1)).isoformat()}",
                    maxStartTime: "{(harness.case_1.scheduled_end_time + timedelta(hours=1)).isoformat()}"
                }}
            ) {{
                edges {{
                    node {{
                        id
                    }}
                }}
            }}
            turnovers(
                query: {{
                    minEndTime: "{(harness.case_0.scheduled_end_time + timedelta(hours=1)).isoformat()}",
                    maxStartTime: "{(harness.case_1.scheduled_end_time + timedelta(hours=1)).isoformat()}"
                }}
            ) {{
                precedingCase {{
                    id
                }}
                followingCase {{
                    id
                }}
            }}
        }}
    }}
    """
    )
    assert len(miss_result["data"]["room"]["apellaCases"]["edges"]) == 1
    assert len(miss_result["data"]["room"]["turnovers"]) == 0
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        room(id: "{harness.room_0.id}") {{
            turnovers(
                query: {{
                    minEndTime: "{(harness.case_0.scheduled_end_time - timedelta(days=1)).isoformat()}",
                    maxStartTime: "{(harness.case_0.scheduled_start_time + timedelta(days=1)).isoformat()}"
                }}
            ) {{
                precedingCase {{
                    id
                }}
                followingCase {{
                    id
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": {
            "room": {
                "turnovers": [
                    {
                        "precedingCase": {"id": "case:" + harness.case_0.case_id},
                        "followingCase": {"id": "case:" + harness.case_1.case_id},
                    }
                ],
            }
        }
    }


@pytest.mark.asyncio
async def test_get_room() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        room(id: "{harness.room_0.id}") {{
            id
            name
            organizationId
            organization {{
                id
            }}
            siteId
            site {{
                id
            }}
            cameras {{
                edges {{
                    node {{
                        id
                    }}
                }}
            }}
            defaultCamera {{
              name
            }}
            primeTimeConfig {{
                roomId
            }}
            privacyEnabled
            isForecastingEnabled
            apellaCases(
                query: {{
                    minEndTime: "{(harness.case_0.scheduled_end_time - timedelta(days=1)).isoformat()}",
                    maxStartTime: "{(harness.case_0.scheduled_start_time + timedelta(days=1)).isoformat()}"
                }}
            ) {{
                edges {{
                    node {{
                        id
                    }}
                }}
            }}
            turnovers(
                query: {{
                    minEndTime: "{(harness.case_0.scheduled_end_time - timedelta(days=1)).isoformat()}",
                    maxStartTime: "{(harness.case_0.scheduled_start_time + timedelta(days=1)).isoformat()}"
                }}
            ) {{
                precedingCase {{
                    id
                }}
                followingCase {{
                    id
                }}
                type
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": {
            "room": {
                "id": harness.room_0.id,
                "name": harness.room_0.name,
                "organizationId": harness.room_0.org_id,
                "organization": {"id": harness.room_0.org_id},
                "siteId": harness.room_0.site_id,
                "site": {"id": harness.room_0.site_id},
                "cameras": {
                    "edges": [
                        {
                            "node": {
                                "id": harness.camera_0.id,
                            }
                        },
                        {
                            "node": {
                                "id": harness.camera_1.id,
                            }
                        },
                    ]
                },
                "defaultCamera": {
                    "name": harness.camera_1.name,
                },
                "primeTimeConfig": {"roomId": harness.room_0.id},
                "privacyEnabled": False,
                "isForecastingEnabled": True,
                "apellaCases": {
                    "edges": [
                        {"node": {"id": "case:" + harness.case_0.case_id}},
                        {"node": {"id": "case:" + harness.case_1.case_id}},
                    ]
                },
                "turnovers": [
                    {
                        "precedingCase": {"id": "case:" + harness.case_0.case_id},
                        "followingCase": {"id": "case:" + harness.case_1.case_id},
                        "type": "LIVE",
                    }
                ],
            }
        }
    }


@pytest.mark.asyncio
async def test_get_room_dashboard_events() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        room(id: "{harness.room_0.id}") {{
            roomEvents(query: {{
                minStartTime: "2021-05-01T00:00:00",
                maxStartTime: "2021-05-02T00:00:00",
                includeDashboardEventsOnly: true
            }}) {{
                edges {{
                    node {{
                        id
                    }}
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": {"room": {"roomEvents": {"edges": [{"node": {"id": harness.event_6.id}}]}}}
    }


async def test_get_room_as_org_user_requires_same_org() -> None:
    result = harness.surgeon_client.execute_graphql(
        f"""
        query {{
            room(id: "{harness.room_3.id}") {{
                name
            }}
        }}
        """
    )
    assert result == {"data": {"room": None}}


@pytest.mark.asyncio
async def test_get_room_status() -> None:
    apella_schema = ApellaSchema()
    query = apella_schema.Query.room.args(id=harness.room_0.id).select(
        apella_schema.Room.id,
        apella_schema.Room.status.select(
            apella_schema.RoomStatus.name,
            apella_schema.RoomStatus.since,
        ),
    )
    results = harness.service_account_client.query_graphql_from_schema(query)

    assert results.room.status.name == GQLRoomStatusName.IDLE
