import pytest

# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs
from datetime import timed<PERSON><PERSON>
from uuid import uuid4

from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_input_schema import (
    GQLApellaCaseBaseQueryInput,
    GQLRoomEventSearchInput,
)
from api_server.services.events.event_store import EventModel
from api_server.services.events.source_type import FORECASTING
from api_server.services.phases.phase_store import PhaseModel, PhaseStatus
from auth.permissions import READ_ANY_ROOM
from tests_component import harness


@pytest.mark.asyncio
async def test_get_rooms_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        f"""
    query {{
        rooms {{
            edges {{
                node {{
                    id
                    name
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": None,
        "errors": [
            {
                "locations": [{"column": 9, "line": 3}],
                "message": "Not Authorized: No authorization found",
                "path": ["rooms"],
            }
        ],
    }


@pytest.mark.asyncio
async def test_get_rooms_no_permissions() -> None:
    result = harness.user_without_permissions_client.execute_graphql(
        f"""
        query {{
            rooms {{
                edges {{
                    node {{
                        id
                        name
                    }}
                }}
            }}
        }}
        """
    )
    assert result == {
        "data": None,
        "errors": [
            {
                "locations": [{"column": 13, "line": 3}],
                "message": f"User does not have permission '{READ_ANY_ROOM}'",
                "path": ["rooms"],
            }
        ],
    }


@pytest.mark.asyncio
async def test_get_rooms_by_org() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        rooms(orgId: "{harness.room_0.org_id}") {{
            edges {{
                node {{
                    id
                    name
                    organizationId
                    sortKey
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": {
            "rooms": {
                "edges": [
                    {
                        "node": {
                            "id": harness.room_1.id,
                            "name": harness.room_1.name,
                            "organizationId": harness.room_1.org_id,
                            "sortKey": None,
                        },
                    },
                    {
                        "node": {
                            "id": harness.room_2.id,
                            "name": harness.room_2.name,
                            "organizationId": harness.room_2.org_id,
                            "sortKey": None,
                        }
                    },
                    {
                        "node": {
                            "id": harness.room_ca.id,
                            "name": harness.room_ca.name,
                            "organizationId": harness.room_ca.org_id,
                            "sortKey": None,
                        },
                    },
                    {
                        "node": {
                            "id": harness.room_0.id,
                            "name": harness.room_0.name,
                            "organizationId": harness.room_0.org_id,
                            "sortKey": harness.room_0.sort_key,
                        },
                    },
                ]
            }
        }
    }


@pytest.mark.asyncio
async def test_get_rooms_for_org_user() -> None:
    result = harness.surgeon_client.execute_graphql(
        f"""
    query {{
        rooms {{
            edges {{
                node {{
                    id
                    name
                    organizationId
                }}
            }}
        }}
    }}
    """
    )

    assert result["data"]["rooms"]["edges"] == [
        {
            "node": {
                "id": harness.room_1.id,
                "name": harness.room_1.name,
                "organizationId": harness.room_1.org_id,
            },
        },
        {
            "node": {
                "id": harness.room_2.id,
                "name": harness.room_2.name,
                "organizationId": harness.room_2.org_id,
            }
        },
        {
            "node": {
                "id": harness.room_ca.id,
                "name": harness.room_ca.name,
                "organizationId": harness.room_ca.org_id,
            },
        },
        {
            "node": {
                "id": harness.room_0.id,
                "name": harness.room_0.name,
                "organizationId": harness.room_0.org_id,
            },
        },
    ]


@pytest.mark.asyncio
async def test_get_multiple_room_dashboard_events() -> None:
    result = harness.surgeon_client.execute_graphql(
        f"""
    query {{
        rooms {{
            edges {{
                node {{
                    id
                    name
                    organizationId
                    roomEvents(query: {{
                minStartTime: "2021-05-01T00:00:00",
                maxStartTime: "2021-05-02T00:00:00",
                includeDashboardEventsOnly: true
            }}) {{
                edges {{
                    node {{
                        id
                    }}
                }}
            }}
                }}
            }}
        }}
    }}
    """
    )

    assert result["data"]["rooms"]["edges"] == [
        {
            "node": {
                "id": harness.room_1.id,
                "name": harness.room_1.name,
                "organizationId": harness.room_1.org_id,
                "roomEvents": {"edges": []},
            },
        },
        {
            "node": {
                "id": harness.room_2.id,
                "name": harness.room_2.name,
                "organizationId": harness.room_2.org_id,
                "roomEvents": {"edges": []},
            }
        },
        {
            "node": {
                "id": harness.room_ca.id,
                "name": harness.room_ca.name,
                "organizationId": harness.room_ca.org_id,
                "roomEvents": {"edges": []},
            },
        },
        {
            "node": {
                "id": harness.room_0.id,
                "name": harness.room_0.name,
                "organizationId": harness.room_0.org_id,
                "roomEvents": {
                    "edges": [
                        {
                            "node": {
                                "id": harness.event_6.id,
                            }
                        },
                    ]
                },
            },
        },
    ]


@pytest.mark.asyncio
async def test_get_rooms_by_site() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        rooms(siteId: "{harness.room_0.site_id}") {{
            edges {{
                node {{
                    id
                    name
                    siteId
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": {
            "rooms": {
                "edges": [
                    {
                        "node": {
                            "id": harness.room_1.id,
                            "name": harness.room_1.name,
                            "siteId": harness.room_1.site_id,
                        },
                    },
                    {
                        "node": {
                            "id": harness.room_0.id,
                            "name": harness.room_0.name,
                            "siteId": harness.room_0.site_id,
                        },
                    },
                ]
            }
        }
    }


@pytest.mark.asyncio
async def test_get_rooms_by_ids() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        rooms(ids: ["{harness.room_0.id}", "{harness.room_1.id}"]) {{
            edges {{
                node {{
                    id
                    name
                    siteId
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": {
            "rooms": {
                "edges": [
                    {
                        "node": {
                            "id": harness.room_1.id,
                            "name": harness.room_1.name,
                            "siteId": harness.room_1.site_id,
                        },
                    },
                    {
                        "node": {
                            "id": harness.room_0.id,
                            "name": harness.room_0.name,
                            "siteId": harness.room_0.site_id,
                        },
                    },
                ]
            }
        }
    }


@pytest.mark.asyncio
async def test_get_rooms_with_events_by_site() -> None:
    apella_schema = ApellaSchema()
    room_query = apella_schema.Query.rooms.args(site_id=harness.room_0.site_id).select(
        apella_schema.RoomConnection.edges.select(
            apella_schema.RoomEdge.node.select(
                apella_schema.Room.id,
                apella_schema.Room.room_events.args(
                    query=GQLRoomEventSearchInput(
                        min_start_time=harness.case_0.scheduled_start_time - timedelta(days=1),
                        max_start_time=harness.case_0.scheduled_start_time + timedelta(days=1),
                    )
                ).select(
                    apella_schema.EventConnection.edges.select(
                        apella_schema.EventEdge.node.select(
                            apella_schema.Event.id,
                        )
                    )
                ),
                apella_schema.Room.apella_cases.args(
                    query=GQLApellaCaseBaseQueryInput(
                        min_end_time=harness.case_0.scheduled_start_time - timedelta(days=1),
                        max_start_time=harness.case_0.scheduled_start_time + timedelta(days=1),
                    )
                ).select(
                    apella_schema.ApellaCaseConnection.edges.select(
                        apella_schema.ApellaCaseEdge.node.select(
                            apella_schema.ApellaCase.id,
                        )
                    )
                ),
            )
        )
    )
    results = harness.labeler_client.query_graphql_from_schema(room_query)
    rooms = [result.node for result in results.rooms.edges]
    rooms.sort(key=lambda room: len(room.room_events.edges))
    assert len(rooms) == 2
    assert rooms[0].id == harness.room_1.id
    assert len(rooms[0].room_events.edges) == 0
    assert len(rooms[0].apella_cases.edges) == 0

    assert rooms[1].id == harness.room_0.id
    assert len(rooms[1].room_events.edges) == 3
    assert len(rooms[1].apella_cases.edges) == 2


@pytest.mark.asyncio
async def test_get_rooms_with_apella_cases_by_site() -> None:
    apella_schema = ApellaSchema()

    start_event_id = str(uuid4())
    end_event_id = str(uuid4())
    await harness.event_store.create_event(
        EventModel(
            event_type_id="patient_wheels_in",
            id=start_event_id,
            start_time=harness.case_0.scheduled_start_time - timedelta(minutes=30),
            room_id=harness.room_1.id,
            org_id=harness.room_1.org_id,
            site_id=harness.room_1.site_id,
            source_type=FORECASTING,
            source="testing",
            process_timestamp=harness.case_0.scheduled_start_time - timedelta(minutes=30),
        )
    )

    await harness.event_store.create_event(
        EventModel(
            event_type_id="patient_wheels_out",
            id=end_event_id,
            start_time=harness.case_0.scheduled_end_time - timedelta(minutes=15),
            room_id=harness.room_1.id,
            org_id=harness.room_1.org_id,
            site_id=harness.room_1.site_id,
            source_type=FORECASTING,
            source="testing",
            process_timestamp=harness.case_0.scheduled_start_time - timedelta(minutes=30),
        )
    )

    await harness.phase_store.upsert_phases(
        [
            PhaseModel(
                id=str(uuid4()),
                case_id=harness.case_0.case_id,
                start_event_id=start_event_id,
                end_event_id=end_event_id,
                room_id=harness.room_1.id,
                org_id=harness.room_1.org_id,
                site_id=harness.room_1.site_id,
                source_type=FORECASTING,
                type_id="CASE",
                status=PhaseStatus.VALID,
            )
        ]
    )

    room_query = apella_schema.Query.rooms.args(site_id=harness.room_0.site_id).select(
        apella_schema.RoomConnection.edges.select(
            apella_schema.RoomEdge.node.select(
                apella_schema.Room.id,
                apella_schema.Room.apella_cases.args(
                    query=GQLApellaCaseBaseQueryInput(
                        min_end_time=harness.case_0.scheduled_start_time - timedelta(days=1),
                        max_start_time=harness.case_0.scheduled_start_time + timedelta(days=1),
                    )
                ).select(
                    apella_schema.ApellaCaseConnection.edges.select(
                        apella_schema.ApellaCaseEdge.node.select(
                            apella_schema.ApellaCase.id,
                        )
                    )
                ),
            )
        )
    )
    results = harness.labeler_client.query_graphql_from_schema(room_query)
    rooms = [result.node for result in results.rooms.edges]
    rooms.sort(key=lambda room: len(room.apella_cases.edges))
    assert len(rooms) == 2
    assert rooms[0].id == harness.room_1.id
    assert len(rooms[0].apella_cases.edges) == 0

    assert rooms[1].id == harness.room_0.id
    assert len(rooms[1].apella_cases.edges) == 2
