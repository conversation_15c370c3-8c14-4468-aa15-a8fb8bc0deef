import pytest
from datetime import datetime, timezone

from graphql import GraphQLError

from tests_component import harness

from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_input_schema import GQLSiteLaunchUpsertInput


class TestSiteLaunches:
    @pytest.mark.asyncio
    async def test_upsert_site_launches(self) -> None:
        """Test upserting site launches."""
        schema = ApellaSchema()

        # Create a site launch for site_0
        launch_date = datetime(2023, 10, 1, 12, 0, tzinfo=timezone.utc).date()

        # Construct the GraphQL mutation
        mutation = schema.Mutation.site_launches_upsert.args(
            input=[
                GQLSiteLaunchUpsertInput(
                    site_id=harness.site_0.id,
                    site_name=harness.site_0.name,
                    anticipated_launch_date=launch_date,
                    notion_id="notion-id-1",
                )
            ]
        ).select(schema.SiteLaunchesUpsert.success)

        # Execute the mutation using the authenticated client
        result = harness.service_account_client.mutate_graphql_from_schema(mutation)

        # Assert that the mutation was successful
        assert result.site_launches_upsert.success is True

        # Verify the site launch was added to the database
        site_launches = await harness.site_store.get_site_launches([harness.site_0.name])
        assert len(site_launches) == 1
        assert site_launches[0].site_id == harness.site_0.id
        assert site_launches[0].anticipated_launch_date == launch_date
        assert site_launches[0].actual_launch_date is None

        # Update the existing site launch with a new date
        updated_launch_date = datetime(2023, 11, 1, 12, 0, tzinfo=timezone.utc).date()

        update_mutation = schema.Mutation.site_launches_upsert.args(
            input=[
                GQLSiteLaunchUpsertInput(
                    site_name=harness.site_0.name,
                    site_id=harness.site_0.id,
                    actual_launch_date=updated_launch_date,
                    notion_id="notion-id-1",
                )
            ]
        ).select(schema.SiteLaunchesUpsert.success)

        # Execute the mutation using the authenticated client
        result = harness.service_account_client.mutate_graphql_from_schema(update_mutation)

        # Assert that the mutation was successful
        assert result.site_launches_upsert.success is True

        # Verify the site launch was updated in the database
        updated_site_launches = await harness.site_store.get_site_launches([harness.site_0.name])
        assert len(updated_site_launches) == 1
        assert updated_site_launches[0].site_id == harness.site_0.id
        assert updated_site_launches[0].site_name == harness.site_0.name
        assert updated_site_launches[0].actual_launch_date == updated_launch_date
        assert updated_site_launches[0].anticipated_launch_date is None

    @pytest.mark.asyncio
    async def test_upsert_multiple_site_launches(self) -> None:
        """Test upserting multiple site launches at once."""
        schema = ApellaSchema()

        # Create launch dates for multiple sites
        launch_date_1 = datetime(2023, 10, 1, 12, 0, tzinfo=timezone.utc).date()
        launch_date_2 = datetime(2023, 11, 15, 12, 0, tzinfo=timezone.utc).date()

        # Construct the GraphQL mutation for multiple sites
        mutation = schema.Mutation.site_launches_upsert.args(
            input=[
                GQLSiteLaunchUpsertInput(
                    site_name=harness.site_0.name,
                    site_id=harness.site_0.id,
                    actual_launch_date=launch_date_1,
                    notion_id="notion-id-1",
                ),
                GQLSiteLaunchUpsertInput(
                    site_name=harness.site_1.name,
                    anticipated_launch_date=launch_date_2,
                    notion_id="notion-id-2",
                ),
            ]
        ).select(schema.SiteLaunchesUpsert.success)

        # Execute the mutation using the authenticated client
        result = harness.service_account_client.mutate_graphql_from_schema(mutation)

        # Assert that the mutation was successful
        assert result.site_launches_upsert.success is True

        # Verify both site launches were added to the database
        site_launches = await harness.site_store.get_site_launches(
            [harness.site_0.name, harness.site_1.name]
        )
        assert len(site_launches) == 2

        # Create a map of site_id to launch_date for easier verification
        site_launch_map = {
            launch.site_name: launch.actual_launch_date or launch.anticipated_launch_date
            for launch in site_launches
        }

        assert site_launch_map[harness.site_0.name] == launch_date_1
        assert site_launch_map[harness.site_1.name] == launch_date_2

    @pytest.mark.asyncio
    async def test_upsert_site_launches_permissions(self) -> None:
        """Test that only users with proper permissions can upsert site launches."""
        schema = ApellaSchema()

        launch_date = datetime(2023, 10, 1, 12, 0, tzinfo=timezone.utc).date()

        # Construct the GraphQL mutation
        mutation = schema.Mutation.site_launches_upsert.args(
            input=[
                GQLSiteLaunchUpsertInput(
                    site_name=harness.site_0.name,
                    site_id=harness.site_0.id,
                    actual_launch_date=launch_date,
                    notion_id="notion-id-1",
                )
            ]
        ).select(schema.SiteLaunchesUpsert.success)

        # Try with a user without proper permissions
        with pytest.raises(GraphQLError) as excinfo:
            harness.surgeon_client.mutate_graphql_from_schema(mutation)
        assert "User does not have permission 'site:write:launch'" in str(excinfo.value)
