from typing import List

import pytest

from tests_component import harness
from api_server.services.case.case_staff_store import CaseStaffModel
from api_server.services.staff.staff_store import StaffModel


@pytest.mark.asyncio
async def test_query_staff(db_staff: List[StaffModel], db_case_staff: List[CaseStaffModel]) -> None:
    staff_info = harness.service_account_client.execute_graphql(
        f"""
    query {{
        staff(query: {{ name: "<PERSON><PERSON>, <PERSON>"}}) {{
            edges {{
                node {{
                    id
                    firstName
                    lastName
                    organization {{
                        id
                        name
                        auth0OrgId
                    }}
                }}
            }}
        }}
    }}
        """
    )["data"]["staff"]["edges"]

    assert staff_info[0]["node"] == {
        "id": f"{db_staff[0].id}",
        "firstName": "<PERSON>",
        "lastName": "Doe",
        "organization": {
            "id": "org_id_0",
            "name": "Org 0",
            "auth0OrgId": f"{harness.org_0.auth0_org_id}",
        },
    }


@pytest.mark.asyncio
async def test_query_staff_no_appearance(
    db_staff: List[StaffModel], db_case_staff: List[CaseStaffModel]
) -> None:
    staff_info = harness.service_account_client.execute_graphql(
        f"""
    query {{
        staff(query: {{ name: "Doer, Joer"}}) {{
            edges {{
                node {{
                    id
                    firstName
                    lastName
                    organization {{
                        id
                        name
                        auth0OrgId
                    }}
                }}
            }}
        }}
    }}
        """
    )["data"]["staff"]["edges"]

    assert staff_info[0]["node"] == {
        "id": f"{db_staff[2].id}",
        "firstName": "Joer",
        "lastName": "Doer",
        "organization": {
            "id": "org_id_0",
            "name": "Org 0",
            "auth0OrgId": f"{harness.org_0.auth0_org_id}",
        },
    }
