from datetime import date
from api_server.services.terminal_cleans.dtos import CleanScoreEnum
import pytest

from tests_component import harness


GET_TERMINAL_CLEAN_SCORE = """
  query GetTerminalCleanScore($roomId: ID!, $date: Date!) {
    terminalCleanScore(roomId: $roomId, date: $date) {
        id
        roomId
        date

        room {
            id
            name
        }

        score
        comments
    }
  }
"""


@pytest.mark.asyncio
async def test_get__no_terminal_clean() -> None:
    tc_date = date(2025, 4, 7)

    result = harness.label_reviewer_client.execute_graphql(
        GET_TERMINAL_CLEAN_SCORE, {"roomId": str(harness.room_0.id), "date": tc_date.isoformat()}
    )

    terminal_clean_score_data = result["data"]["terminalCleanScore"]

    assert terminal_clean_score_data is None


@pytest.mark.asyncio
async def test_get__existing_terminal_clean() -> None:
    tc_date = date(2025, 4, 7)

    await harness.terminal_cleans_store.upsert_terminal_clean_score(
        room_id=harness.room_0.id,
        date=tc_date,
        comments="Test comment",
        score=CleanScoreEnum.PARTIAL,
    )

    result = harness.label_reviewer_client.execute_graphql(
        GET_TERMINAL_CLEAN_SCORE, {"roomId": str(harness.room_0.id), "date": tc_date.isoformat()}
    )

    terminal_clean_score_data = result["data"]["terminalCleanScore"]

    assert terminal_clean_score_data["roomId"] == harness.room_0.id
    assert terminal_clean_score_data["room"]["id"] == harness.room_0.id
    assert terminal_clean_score_data["room"]["name"] == harness.room_0.name
    assert terminal_clean_score_data["date"] == tc_date.isoformat()
    assert terminal_clean_score_data["comments"] == "Test comment"
    assert terminal_clean_score_data["score"] == "PARTIAL"
