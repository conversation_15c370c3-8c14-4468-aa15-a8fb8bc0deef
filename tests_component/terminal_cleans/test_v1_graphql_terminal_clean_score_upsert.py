from datetime import date
from api_server.services.terminal_cleans.dtos import CleanScoreEnum
import pytest


from tests_component import harness


@pytest.mark.asyncio
async def test_terminal_clean_score_upsert_requires_auth() -> None:
    tc_date = date(2025, 4, 7)

    result = harness.no_auth_client.execute_graphql(
        TERMINAL_CLEAN_SCORE_UPSERT_MUTATION,
        variables={
            "input": {
                "roomId": harness.room_0.id,
                "date": tc_date.isoformat(),
                "comments": "Test comment",
                "score": "COMPLETE",
            }
        },
    )

    assert result["data"]["terminalCleanScoreUpsert"] is None
    assert result["errors"][0]["path"] == ["terminalCleanScoreUpsert"]
    assert result["errors"][0]["message"] == "Not Authorized: No authorization found"


@pytest.mark.asyncio
async def test_terminal_clean_score_upsert__create() -> None:
    tc_date = date(2025, 4, 7)

    result = harness.field_engineer_client.execute_graphql(
        TERMINAL_CLEAN_SCORE_UPSERT_MUTATION,
        variables={
            "input": {
                "roomId": harness.room_0.id,
                "date": tc_date.isoformat(),
                "comments": "Test comment",
                "score": "COMPLETE",
            }
        },
    )

    assert result["data"]["terminalCleanScoreUpsert"]["success"] is True

    terminal_clean_score_data = result["data"]["terminalCleanScoreUpsert"]["terminalCleanScore"]

    assert terminal_clean_score_data["roomId"] == harness.room_0.id
    assert terminal_clean_score_data["room"]["id"] == harness.room_0.id
    assert terminal_clean_score_data["room"]["name"] == harness.room_0.name
    assert terminal_clean_score_data["date"] == tc_date.isoformat()
    assert terminal_clean_score_data["comments"] == "Test comment"
    assert terminal_clean_score_data["score"] == "COMPLETE"

    terminal_cleans_score = await harness.terminal_cleans_store.get_terminal_clean_score(
        room_id=harness.room_0.id, date=tc_date
    )
    assert terminal_cleans_score is not None
    assert terminal_cleans_score.room_id == harness.room_0.id
    assert terminal_cleans_score.date == tc_date
    assert terminal_cleans_score.comments == "Test comment"
    assert terminal_cleans_score.score == CleanScoreEnum.COMPLETE


@pytest.mark.asyncio
async def test_terminal_clean_score_upsert__update() -> None:
    tc_date = date(2025, 4, 7)

    await harness.terminal_cleans_store.upsert_terminal_clean_score(
        room_id=harness.room_0.id,
        date=tc_date,
        comments="Test comment",
        score=CleanScoreEnum.PARTIAL,
    )

    result = harness.field_engineer_client.execute_graphql(
        TERMINAL_CLEAN_SCORE_UPSERT_MUTATION,
        variables={
            "input": {
                "roomId": harness.room_0.id,
                "date": tc_date.isoformat(),
                "comments": "Test comment",
                "score": "COMPLETE",
            }
        },
    )

    assert result["data"]["terminalCleanScoreUpsert"]["success"] is True

    terminal_clean_score_data = result["data"]["terminalCleanScoreUpsert"]["terminalCleanScore"]

    assert terminal_clean_score_data["roomId"] == harness.room_0.id
    assert terminal_clean_score_data["room"]["id"] == harness.room_0.id
    assert terminal_clean_score_data["room"]["name"] == harness.room_0.name
    assert terminal_clean_score_data["date"] == tc_date.isoformat()
    assert terminal_clean_score_data["comments"] == "Test comment"
    assert terminal_clean_score_data["score"] == "COMPLETE"

    terminal_cleans_score = await harness.terminal_cleans_store.get_terminal_clean_score(
        room_id=harness.room_0.id, date=tc_date
    )
    assert terminal_cleans_score is not None
    assert terminal_cleans_score.room_id == harness.room_0.id
    assert terminal_cleans_score.date == tc_date
    assert terminal_cleans_score.comments == "Test comment"
    assert terminal_cleans_score.score == CleanScoreEnum.COMPLETE


TERMINAL_CLEAN_SCORE_UPSERT_MUTATION = """
    mutation TerminalCleanScoreUpsert($input: TerminalCleanScoreUpsertInput!) {
        terminalCleanScoreUpsert(input: $input) {
            success
            terminalCleanScore {
                id
                roomId
                date

                room {
                    id
                    name
                }

                score
                comments
            }
        }
    }
    """
