from api_server.services.case_activity.case_activity_store import CaseActivityModel
from databases.sql import new_async_session
from sqlalchemy import insert, delete
from sqlalchemy.sql import func


class CaseActivityTestHelper:
    """Helper class for inserting and deleting case_activity records during tests."""

    @staticmethod
    async def insert_case_activity(case_activity: CaseActivityModel) -> None:
        """Test-only helper to insert a case activity into the database."""
        async with new_async_session() as session:
            stmt = insert(CaseActivityModel).values(
                id=case_activity.id,
                source_type=case_activity.source_type,
                site_id=case_activity.site_id,
                org_id=case_activity.org_id,
                updated_time=func.now(),
                room_id=case_activity.room_id,
                deleted_time=case_activity.deleted_time,
                event_type_id=case_activity.event_type_id,
                source=case_activity.source,
                case_id=case_activity.case_id,
                start_time=case_activity.start_time,
                process_timestamp=case_activity.process_timestamp,
            )
            await session.execute(stmt)
            await session.commit()

    @staticmethod
    async def delete_case_activity(case_activity_id: str, source_type: str) -> None:
        """Test-only helper to delete a case activity record by id and source_type."""
        async with new_async_session() as session:
            stmt = delete(CaseActivityModel).where(
                CaseActivityModel.id == case_activity_id,
                CaseActivityModel.source_type == source_type,
            )
            await session.execute(stmt)
            await session.commit()
