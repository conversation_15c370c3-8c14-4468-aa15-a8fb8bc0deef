from contextlib import asynccontextmanager
from typing import Any, AsyncGenerator

import asyncpg
import pytest

from apella_cloud_api import Client
from api_server.services.users.user_store import User
from mocks.mock_auth0.database import Database as MockAuth0Database
from mocks.mock_auth0.jwt_tokens import make_jwt
from tests_component import harness
from tests_component.conftest import MockService


def reference_query(client: Client) -> Any:
    return client.execute_graphql(
        f"""
        query {{
            room(id: "{harness.room_0.id}") {{
                name
                events(minTime: "2021-01-01T12:00:00Z", maxTime: "2021-01-01T12:01:00Z") {{
                    name
                }}
            }}
        }}
        """
    )


def test_reference_query_doesnt_error() -> None:
    result = reference_query(harness.service_account_client)
    assert result == {
        "data": {
            "room": {
                "name": harness.room_0.name,
                "events": [{"name": "back_table_open"}],
            }
        }
    }


@asynccontextmanager
async def events_table_renamed(postgres_container_port: int) -> AsyncGenerator[None, None]:
    conn = await asyncpg.connect(
        host="localhost",
        port=postgres_container_port,
        user="postgres",
        password="postgres",
        database="postgres",
    )
    await conn.execute("ALTER TABLE events RENAME to events_backup;")

    try:
        yield
    finally:
        await conn.execute("ALTER TABLE events_backup RENAME TO events;")


@pytest.mark.asyncio
async def test_graphql_internal_errors_on_broken_database(postgres_container: int) -> None:
    # There is no way to cause a server error from normal request inputs alone
    # (that we know of, because if there was, we would fix that bug)
    # So, instead we modify the infrastructure by renaming the events table
    async with events_table_renamed(postgres_container):
        # And then we query for events in a room
        result = reference_query(harness.service_account_client)
        # There should be nothing in the data
        assert result["data"] == {"room": None}
        # And just 1 error
        assert len(result["errors"]) == 1
        # Pointing to the right place
        assert result["errors"][0]["locations"] == [{"column": 17, "line": 5}]
        assert result["errors"][0]["path"] == ["room", "events"]
        # And the message should just be "Internal Server Error: xxx" with a uuid.
        # We could validate the uuid, but just checking the length is sufficient
        assert result["errors"][0]["message"].startswith("Internal Server Error: ")
        assert len(result["errors"][0]["message"]) == len(
            "Internal Server Error: 4f71eb19595e4518b192b421e39b5348"
        )


@pytest.mark.asyncio
async def test_graphql_logs_stacktrace(api_server_log: str, postgres_container: int) -> None:
    # There is no way to cause a server error from normal request inputs alone
    # (that we know of, because if there was, we would fix that bug)
    # So, instead we modify the infrastructure by renaming the events table
    async with events_table_renamed(postgres_container):
        reference_query(harness.service_account_client)

        with open(api_server_log) as f:
            logs = f.readlines()

        found = False
        for line in logs:
            if "Traceback (most recent call last)" in line:
                found = True
                break

        assert found


def test_graphql_internal_errors_on_broken_auth(
    mock_auth0_server: MockService[MockAuth0Database],
) -> None:
    # One way to cause an internal error is to break auth0

    # Since our Auth0 calls are cached, but the API server under test is not reset,
    # this test is dependent on other tests that ran before it, and when the cache ttl boundary
    # comes.  So we create a new user that is only used for this test
    broken_user = User("auth0|broken", "Broken User", "<EMAIL>")
    # By providing "permissions=1", that causes our mock auth0 to error when resolving permissions
    # for this broken user
    mock_auth0_server.database.add_user(broken_user, permissions=1)  # type: ignore[arg-type]

    # and we need a client for this user
    broken_client = Client(
        server_url=harness.server_url,
        jwt=make_jwt(harness.server_url, harness.auth0_url, broken_user),
    )

    # And then we query for events in a room
    result = reference_query(broken_client)
    # There should be nothing in the data
    assert result["data"] == {"room": None}
    # And just 1 error
    assert len(result["errors"]) == 1
    # Pointing to the right place
    # And the message should just be "Internal Server Error: xxx" with a uuid.
    # We could validate the uuid, but just checking the length is sufficient
    assert result["errors"][0]["message"].startswith("Internal Server Error: ")
    assert len(result["errors"][0]["message"]) == len(
        "Internal Server Error: 4f71eb19595e4518b192b421e39b5348"
    )
