# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

from contextlib import asynccontextmanager
from datetime import datetime

import asyncpg
import pytest
from tenacity import RetryError

from tests_component import harness


def reference_request():
    return harness.service_account_client.query_events(
        room_id=harness.room_0.id,
        min_time=datetime.fromisoformat("2021-01-01T12:00:00-00:00"),
        max_time=datetime.fromisoformat("2021-01-01T12:01:00"),
    )


def test_reference_query_doesnt_error() -> None:
    result = reference_request()
    assert len(result.events) == 1


@asynccontextmanager
async def events_table_renamed(postgres_container_port: int):
    conn = await asyncpg.connect(
        host="localhost",
        port=postgres_container_port,
        user="postgres",
        password="postgres",
        database="postgres",
    )
    await conn.execute("ALTER TABLE events RENAME to events_backup;")
    try:
        yield
    finally:
        await conn.execute("ALTER TABLE events_backup RENAME TO events;")


@pytest.mark.asyncio
async def test_graphql_internal_errors(postgres_container: int) -> None:
    # There is no way to cause a server error from normal request inputs alone
    # (that we know of, because if there was, we would fix that bug)
    # So, instead we modify the infrastructure by renaming the events table
    async with events_table_renamed(postgres_container):
        # And then we query for events in a room
        with pytest.raises(RetryError):
            reference_request()
