from api_server.services.user_filter_views.user_filter_view_model import (
    UserFilterViewModel,
)
from tests_component import harness


def test_get_filter_view_no_auth(
    new_hospital_admin_user_filter_view: UserFilterViewModel,
    filter_view_name: str,
    filter_view_url: str,
    hospital_admin_org_id: str,
) -> None:
    response = harness.no_auth_client.execute_graphql(
        f"""
    query {{
        userFilterViews(query: {{ids: ["{new_hospital_admin_user_filter_view.id}"]}}) {{
            id
            name
            url
            userId
            orgId
        }}
    }}
        """
    )

    assert response == {
        "data": {"userFilterViews": None},
        "errors": [
            {
                "message": "Not Authorized: No authorization found",
                "locations": [{"line": 3, "column": 9}],
                "path": ["userFilterViews"],
            }
        ],
    }


def test_query_filter_views(
    new_hospital_admin_user_filter_view: User<PERSON>ilter<PERSON>iewModel,
    hospital_admin_user_id: str,
) -> None:
    filter_views = harness.surgeon_client.execute_graphql(
        f"""
    query {{
        userFilterViews(query: {{}}) {{
            id
            name
            url
            userId
            orgId
        }}
    }}
    """
    )["data"]["userFilterViews"]

    # No result because surgeon_client can't see hospital_admin_user's filter views
    assert len(filter_views) == 0

    filter_views = harness.hospital_administrator_client.execute_graphql(
        f"""
    query {{
        userFilterViews(query: {{}}) {{
            id
            name
            url
            userId
            orgId
        }}
    }}
    """
    )["data"]["userFilterViews"]

    assert len(filter_views) == 1
    assert filter_views[0]["id"] == str(new_hospital_admin_user_filter_view.id)
    assert filter_views[0]["name"] == new_hospital_admin_user_filter_view.name
    assert filter_views[0]["url"] == new_hospital_admin_user_filter_view.url
    assert filter_views[0]["userId"] == new_hospital_admin_user_filter_view.user_id
    assert filter_views[0]["orgId"] == new_hospital_admin_user_filter_view.org_id
