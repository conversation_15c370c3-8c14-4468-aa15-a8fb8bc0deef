import pytest

from auth.permissions import READ_ANY_USER
from tests_component import harness


@pytest.mark.asyncio
async def test_get_user_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        f"""
    query {{
        user(id: "{harness.surgeon_user.user_id}") {{
            name,
            email
        }}
    }}
    """
    )
    assert result == {
        "data": {"user": None},
        "errors": [
            {
                "path": ["user"],
                "locations": [{"line": 3, "column": 9}],
                "message": "Not Authorized: No authorization found",
            }
        ],
    }


@pytest.mark.asyncio
async def test_get_self_by_id() -> None:
    result = harness.surgeon_client.execute_graphql(
        f"""
    query {{
        user(id: "{harness.surgeon_user.user_id}") {{
            id
            name
            email
        }}
    }}
    """
    )
    assert result == {
        "data": {
            "user": {
                "id": harness.surgeon_user.user_id,
                "name": harness.surgeon_user.display_name,
                "email": harness.surgeon_user.email,
            }
        }
    }


@pytest.mark.asyncio
async def test_get_other_user() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
        query {{
            user(id: "{harness.surgeon_user.user_id}") {{
                name,
                email
                organizations {{
                    edges {{
                        node {{
                            id
                            name
                        }}
                    }}
                }}
            }}
        }}
        """
    )
    assert result == {
        "data": {
            "user": {
                "email": harness.surgeon_user.email,
                "name": harness.surgeon_user.display_name,
                "organizations": {
                    "edges": [
                        {"node": {"id": harness.org_0.id, "name": harness.org_0.name}},
                        {"node": {"id": harness.org_1.id, "name": harness.org_1.name}},
                    ]
                },
            }
        }
    }


@pytest.mark.asyncio
async def test_get_other_user_no_permissions() -> None:
    result = harness.surgeon_client.execute_graphql(
        f"""
        query {{
            user(id: "{harness.user_without_permissions.user_id}") {{
                name,
                email
            }}
        }}
        """
    )
    assert result == {
        "data": {"user": None},
        "errors": [
            {
                "path": ["user"],
                "locations": [{"line": 3, "column": 13}],
                "message": f"User does not have permission '{READ_ANY_USER}'",
            }
        ],
    }


@pytest.mark.asyncio
async def test_get_nonexistent_user() -> None:
    bad_user_id = "bad user"
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        user(id: "{bad_user_id}") {{
            name,
            email
        }}
    }}
    """
    )
    assert result == {
        "data": {"user": None},
        "errors": [
            {
                "path": ["user"],
                "locations": [{"line": 3, "column": 9}],
                "message": f"No user found with id: {bad_user_id}",
            }
        ],
    }


@pytest.mark.asyncio
async def test_get_self_by_id_with_no_permissions() -> None:
    result = harness.user_without_permissions_client.execute_graphql(
        f"""
        query {{
            user(id: "{harness.user_without_permissions.user_id}") {{
                name,
                email
            }}
        }}
        """
    )
    assert result == {
        "data": {
            "user": {
                "name": harness.user_without_permissions.display_name,
                "email": harness.user_without_permissions.email,
            }
        }
    }


@pytest.mark.asyncio
async def test_get_user_query() -> None:
    result = harness.surgeon_client.execute_graphql(
        f"""
        query {{
            user(id: "{harness.surgeon_user.user_id}") {{
                id
            }}
        }}
    """
    )

    assert result == {
        "data": {
            "user": {
                "id": harness.surgeon_user.user_id,
            }
        }
    }
