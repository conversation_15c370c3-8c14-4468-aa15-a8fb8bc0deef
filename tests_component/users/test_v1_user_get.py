import pytest

from apella_cloud_api.exceptions import NotAuthorized
from tests_component import harness


def test_get_user_requires_auth() -> None:
    with pytest.raises(NotAuthorized):
        harness.no_auth_client.get_user(harness.surgeon_user.user_id)


def test_get_self_by_id() -> None:
    user = harness.surgeon_client.get_user(harness.surgeon_user.user_id)
    assert user.user_id == harness.surgeon_user.user_id
    assert user.display_name == harness.surgeon_user.display_name
    assert user.email == harness.surgeon_user.email


def test_get_other_user() -> None:
    with pytest.raises(NotAuthorized):
        harness.surgeon_client.get_user(harness.user_without_permissions.user_id)


def test_get_nonexistent_user() -> None:
    with pytest.raises(NotAuthorized):
        harness.surgeon_client.get_user("bad-user")


def test_get_self_by_id_with_no_permissions() -> None:
    user = harness.user_without_permissions_client.get_user(
        harness.user_without_permissions.user_id
    )
    assert user.user_id == harness.user_without_permissions.user_id
    assert user.display_name == harness.user_without_permissions.display_name
    assert user.email == harness.user_without_permissions.email
