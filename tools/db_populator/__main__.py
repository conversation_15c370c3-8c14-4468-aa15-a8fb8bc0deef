import argparse
import sys
from typing import Any, Dict

import asyncio
import google
import yaml
from sqlalchemy.ext.asyncio import AsyncSession

import config
from apella_cloud_api.exceptions import ClientError
from api_server.services.case.case_store import CaseStore  # noqa: F401
from api_server.services.ehr_interfaces.mapping_store import MappingStore
from api_server.services.organization.organization_db import Organization
from api_server.services.service_lines.service_line_store import (
    ServiceLineModel,
    ServiceLineStore,
)
from api_server.services.observations.observation_store import (
    DEFAULT_COLOR,
    ObservationTypeNameModel,
    ObservationTypeNameStore,
)
from databases.secret_store import SecretStore
from databases.sql import engine_singleton, get_session_maker


async def push_config(filename: str, environment_name: str, verbose: bool, dry_run: bool) -> None:
    # Read the config
    print(f"Loading {filename}")
    with open(filename, "r") as config_file:
        config_data = yaml.load(config_file, Loader=yaml.FullLoader)
        if verbose:
            print("")
            print(" Loaded config:")
            print(config_data)

    # The config files are written to be more human readable, and so links between objects are
    # implied through yaml nesting.  But in the DB each leaf object is separate, so those need
    # to be added in
    add_ids_to_config(config_data=config_data, environment_name=environment_name, verbose=verbose)

    if dry_run:
        print("Dry run.. not pushing to gcp")
    else:
        print("Pushing to gcp")
        engine = engine_singleton()
        if engine is None:
            raise RuntimeError(
                "Attempted to initialize engine singleton before configuration finished."
            )
        session_maker = get_session_maker(engine)
        if session_maker is None:
            raise RuntimeError(
                "Attempted to initialize session maker before configuration finished."
            )
        # We need to pass in a session maker to the async function
        # If we try to just use a single session for the whole function any errors
        # are carried over to the next operation
        async with session_maker() as session:
            await push_to_gcp(config_data=config_data, session=session)
        await engine.dispose(close=False)
        print("Done!")


def add_ids_to_config(config_data: Dict[str, Any], environment_name: str, verbose: bool) -> None:
    org_id = config_data["org_id"]

    # Get the Auth0 Org Id based on the environment
    config_data["auth0_org_id"] = config_data["auth0_org_ids_per_environment"][environment_name]

    room_mappings = config_data.get("room_mappings")
    if room_mappings is not None:
        for room_mapping in room_mappings:
            room_mapping["external_id_type"] = f"{org_id}::room_mapping"

    service_lines = config_data.get("service_lines")
    if service_lines is not None:
        for service_line in service_lines:
            service_line["org_id"] = org_id

    if verbose:
        print("")
        print("After add_ids_to_config")
        print(config_data)


async def push_to_gcp(config_data: Dict[str, Any], session: AsyncSession) -> None:
    mapping_store = MappingStore()
    service_line_store = ServiceLineStore()
    observation_type_name_store = ObservationTypeNameStore()

    # Orgs
    org = Organization()
    org.id = config_data["org_id"]
    org.name = config_data["name"]
    org.auth0_org_id = config_data["auth0_org_id"]

    # Room Mappings
    room_mappings = config_data.get("room_mappings")
    if room_mappings is not None:
        for rm_data in room_mappings:
            nested_mapping_session = await session.begin_nested()
            try:
                print(f"Adding mapping: {rm_data['external_id_type']} for {rm_data['internal_id']}")
                await mapping_store.set_mapping(
                    external_id_type=rm_data["external_id_type"],
                    external_id=rm_data["external_id"],
                    internal_id=rm_data["internal_id"],
                    session=session,
                )
                print("Mapping added successfully!")
            except ClientError as err:
                await nested_mapping_session.rollback()
                raise err

    # Service Lines
    service_lines = config_data.get("service_lines")
    if service_lines is not None:
        for sl_data in service_lines:
            service_line: ServiceLineModel = ServiceLineModel()
            service_line.org_id = config_data["org_id"]
            service_line.name = sl_data["name"]
            service_line.external_service_line_id = sl_data["external_service_line_id"]
            nested_service_line_session = await session.begin_nested()
            try:
                print(f"Adding service line: {service_line.external_service_line_id}")
                await service_line_store.create_or_update_service_line(
                    service_line=service_line, session=session
                )
                print("Service Lines added successfully!")
            except ClientError as err:
                await nested_service_line_session.rollback()
                raise err

    # Observation Type Names
    observation_type_names = config_data.get("observation_type_names")
    if observation_type_names is not None:
        for otn_data in observation_type_names:
            observation_type_name: ObservationTypeNameModel = ObservationTypeNameModel()
            observation_type_name.org_id = config_data["org_id"]
            observation_type_name.type_id = otn_data["type_id"]
            observation_type_name.name = otn_data["name"]
            observation_type_name.color = otn_data.get("color", DEFAULT_COLOR)
            observation_type_name.is_custom_phase_end_point = otn_data.get(
                "is_custom_phase_end_point", False
            )
            nested_observation_type_name_session = await session.begin_nested()
            try:
                print(
                    f"Adding observation type name: {observation_type_name.org_id} {observation_type_name.type_id} {observation_type_name.name}"
                )
                await observation_type_name_store.create_or_update_observation_type_name(
                    observation_type_name=observation_type_name, session=session
                )
                print("Observation Type Name added successfully!")
            except ClientError as err:
                await nested_observation_type_name_session.rollback()
                raise err


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Manage configurations for Apella system")
    parser.add_argument(
        "--push-config", type=str, help="the config yml file to push", required=True
    )
    parser.add_argument(
        "--dry-run", action="store_true", help="prevents any action from being taken"
    )
    parser.add_argument("--verbose", action="store_true", help="sets log level to verbose")

    args = parser.parse_args()
    credentials, gcp_project_id = google.auth.default()

    if config.environment_name() not in ["dev", "staging", "prod"]:
        print("Set the SQL credentials as environment variables.")
        sys.exit()

    # Ensure environment is expected one
    is_correct_environment = input(
        f"Data will be added for the following environment: {config.environment_name()}. "
        f"Is this environment correct (Y/n)? "
    ).lower()
    if is_correct_environment != "y":
        print("Aborted!")
        sys.exit()

    # Get SQL database password
    secret_store = SecretStore(gcp_project_id, f"{config.environment_name()}_")
    if args.push_config is not None:
        asyncio.run(
            push_config(
                filename=args.push_config,
                environment_name=config.environment_name(),
                verbose=args.verbose,
                dry_run=args.dry_run,
            )
        )
