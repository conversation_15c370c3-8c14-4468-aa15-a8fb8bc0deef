name: Hackensack Meridian
org_id: hackensack_meridian
# Populate auth0 org id from Auth0 Management page
auth0_org_ids_per_environment:
  dev: na
  staging: na
  prod: na

room_mappings:
# site: HUMC CAS
  - external_id: 'CAS ROOM 01'
    internal_id: 'HMHN-HUMC-CAS-OR01'
  - external_id: 'CAS ROOM 02'
    internal_id: 'HMHN-HUMC-CAS-OR02'
  - external_id: 'CAS ROOM 03'
    internal_id: 'HMHN-HUMC-CAS-OR03'
  - external_id: 'CAS ROOM 04'
    internal_id: 'HMHN-HUMC-CAS-OR04'
  - external_id: 'CAS ROOM 05'
    internal_id: 'HMHN-HUMC-CAS-OR05'
  - external_id: 'CAS ROOM 06'
    internal_id: 'HMHN-HUMC-CAS-OR06'
  - external_id: 'CAS ROOM 07'
    internal_id: 'HMHN-HUMC-CAS-OR07'
  - external_id: 'CAS ROOM 08'
    internal_id: 'HMHN-HUMC-CAS-OR08'
  - external_id: 'CAS ROOM 09'
    internal_id: 'HMHN-HUMC-CAS-OR09'
  - external_id: 'CAS ROOM 10'
    internal_id: 'HMHN-HUMC-CAS-OR10'
# site: HUMC HTP
  - external_id: 'HTP OR ROOM 1'
    internal_id: 'HMHN-HUMC-HTP-OR01'
  - external_id: 'HTP OR ROOM 2'
    internal_id: 'HMHN-HUMC-HTP-OR02'
  - external_id: 'HTP OR ROOM 3'
    internal_id: 'HMHN-HUMC-HTP-OR03'
  - external_id: 'HTP OR ROOM 4'
    internal_id: 'HMHN-HUMC-HTP-OR04'
  - external_id: 'HTP OR ROOM 5'
    internal_id: 'HMHN-HUMC-HTP-OR05'
  - external_id: 'HTP OR ROOM 6'
    internal_id: 'HMHN-HUMC-HTP-OR06'
  - external_id: 'HTP OR ROOM 7'
    internal_id: 'HMHN-HUMC-HTP-OR07'
  - external_id: 'HTP OR ROOM 8'
    internal_id: 'HMHN-HUMC-HTP-OR08'
  - external_id: 'HTP OR ROOM 9'
    internal_id: 'HMHN-HUMC-HTP-OR09'
  - external_id: 'HTP OR ROOM 10'
    internal_id: 'HMHN-HUMC-HTP-OR10'
  - external_id: 'HTP OR ROOM 11'
    internal_id: 'HMHN-HUMC-HTP-OR11'
  - external_id: 'HTP OR ROOM 12'
    internal_id: 'HMHN-HUMC-HTP-OR12'
  - external_id: 'HTP OR ROOM 14'
    internal_id: 'HMHN-HUMC-HTP-OR14'
  - external_id: 'HTP OR ROOM 15'
    internal_id: 'HMHN-HUMC-HTP-OR15'
  - external_id: 'HTP OR ROOM 16'
    internal_id: 'HMHN-HUMC-HTP-OR16'
  - external_id: 'HTP OR ROOM 17'
    internal_id: 'HMHN-HUMC-HTP-OR17'
  - external_id: 'HTP OR ROOM 18'
    internal_id: 'HMHN-HUMC-HTP-OR18'
  - external_id: 'HTP OR ROOM 19'
    internal_id: 'HMHN-HUMC-HTP-OR19'
  - external_id: 'HTP OR ROOM 20'
    internal_id: 'HMHN-HUMC-HTP-OR20'
  - external_id: 'HTP OR ROOM 21'
    internal_id: 'HMHN-HUMC-HTP-OR21'
  - external_id: 'HTP OR ROOM 22'
    internal_id: 'HMHN-HUMC-HTP-OR22'
  - external_id: 'HTP OR ROOM 23'
    internal_id: 'HMHN-HUMC-HTP-OR23'
  - external_id: 'HTP OR ROOM 24'
    internal_id: 'HMHN-HUMC-HTP-OR24'
  - external_id: 'HTP OR ROOM 25'
    internal_id: 'HMHN-HUMC-HTP-OR25'
# site: JFK MOR
  - external_id: 'HMH JFK MOR 01'
    internal_id: 'HMHN-JFK-MOR-OR01'
  - external_id: 'HMH JFK MOR 02'
    internal_id: 'HMHN-JFK-MOR-OR02'
  - external_id: 'HMH JFK MOR 03'
    internal_id: 'HMHN-JFK-MOR-OR03'
  - external_id: 'HMH JFK MOR 04'
    internal_id: 'HMHN-JFK-MOR-OR04'
  - external_id: 'HMH JFK MOR 05'
    internal_id: 'HMHN-JFK-MOR-OR05'
  - external_id: 'HMH JFK MOR 06'
    internal_id: 'HMHN-JFK-MOR-OR06'
  - external_id: 'HMH JFK MOR 07'
    internal_id: 'HMHN-JFK-MOR-OR07'
  - external_id: 'HMH JFK MOR 08'
    internal_id: 'HMHN-JFK-MOR-OR08'
  - external_id: 'HMH JFK MOR 09'
    internal_id: 'HMHN-JFK-MOR-OR09'
  - external_id: 'HMH JFK MOR 10'
    internal_id: 'HMHN-JFK-MOR-OR10'
  - external_id: 'HMH JFK MOR 11'
    internal_id: 'HMHN-JFK-MOR-OR11'
  - external_id: 'HMH JFK MOR 12'
    internal_id: 'HMHN-JFK-MOR-OR12'
# site: JSUMC OR
  - external_id: 'NB JSUMC OR 01'
    internal_id: 'HMHN-JSUMC-OR-OR01'
  - external_id: 'NB JSUMC OR 02'
    internal_id: 'HMHN-JSUMC-OR-OR02'
  - external_id: 'NB JSUMC OR 03'
    internal_id: 'HMHN-JSUMC-OR-OR03'
  - external_id: 'NB JSUMC OR 04'
    internal_id: 'HMHN-JSUMC-OR-OR04'
  - external_id: 'NB JSUMC OR 05'
    internal_id: 'HMHN-JSUMC-OR-OR05'
  - external_id: 'NB JSUMC OR 06'
    internal_id: 'HMHN-JSUMC-OR-OR06'
  - external_id: 'NB JSUMC OR 07'
    internal_id: 'HMHN-JSUMC-OR-OR07'
  - external_id: 'NB JSUMC OR 08'
    internal_id: 'HMHN-JSUMC-OR-OR08'
  - external_id: 'NB JSUMC OR 09'
    internal_id: 'HMHN-JSUMC-OR-OR09'
  - external_id: 'NB JSUMC OR 10'
    internal_id: 'HMHN-JSUMC-OR-OR10'
  - external_id: 'NB JSUMC OR 11'
    internal_id: 'HMHN-JSUMC-OR-OR11'
  - external_id: 'NB JSUMC OR 12'
    internal_id: 'HMHN-JSUMC-OR-OR12'
  - external_id: 'NB JSUMC OR 13'
    internal_id: 'HMHN-JSUMC-OR-OR13'
  - external_id: 'NB JSUMC OR 14'
    internal_id: 'HMHN-JSUMC-OR-OR14'
  - external_id: 'NB JSUMC OR 15'
    internal_id: 'HMHN-JSUMC-OR-OR15'
  - external_id: 'NB JSUMC OR 16'
    internal_id: 'HMHN-JSUMC-OR-OR16'
  - external_id: 'NB JSUMC OR 17'
    internal_id: 'HMHN-JSUMC-OR-OR17'
  - external_id: 'NB JSUMC OR 18'
    internal_id: 'HMHN-JSUMC-OR-OR18'

service_lines:
  - external_service_line_id: 'ANES'
    name : 'Anesthesiology'
  - external_service_line_id: 'ANGI'
    name : 'Angio Interventional'
  - external_service_line_id: 'CARD'
    name : 'Cardiac Surgery'
  - external_service_line_id: 'CTH'
    name : 'Cardiothoracic'
  - external_service_line_id: 'CV'
    name : 'Cardiovascular'
  - external_service_line_id: 'CFAC'
    name : 'Craniofacial'
  - external_service_line_id: 'DERM'
    name : 'Dermatology'
  - external_service_line_id: 'ECT'
    name : 'ECT'
  - external_service_line_id: 'ENT'
    name : 'Otolaryngology'
  - external_service_line_id: 'GI'
    name : 'Gastroenterology'
  - external_service_line_id: 'GEN'
    name : 'General Surgery'
  - external_service_line_id: 'OB/GYN'
    name : 'OB/GYN'
  - external_service_line_id: 'GYNONC'
    name : 'Gynecology Oncology'
  - external_service_line_id: 'GYNURO'
    name : 'Gynecology Urology'
  - external_service_line_id: 'HAND'
    name : 'Hand'
  - external_service_line_id: 'HANEC'
    name : 'Head and Neck'
  - external_service_line_id: 'IR'
    name : 'Interventional Radiology'
  - external_service_line_id: 'MAX'
    name : 'Maxillofacial'
  - external_service_line_id: 'NEUR'
    name : 'Neurosurgery'
  - external_service_line_id: 'OB'
    name : 'Obstetrics'
  - external_service_line_id: 'OCPLA'
    name : 'Oculoplastic'
  - external_service_line_id: 'ONC'
    name : 'Oncology'
  - external_service_line_id: 'OPHTH'
    name : 'Ophthalmology'
  - external_service_line_id: 'OS'
    name : 'Oral Surgery'
  - external_service_line_id: 'ORTH'
    name : 'Orthopedics'
  - external_service_line_id: 'PEDCARD'
    name : 'Pediatric Cardiology'
  - external_service_line_id: 'PEDGI'
    name : 'Pediatric Gastrointestinal'
  - external_service_line_id: 'PEDGEN'
    name : 'Pediatric General'
  - external_service_line_id: 'PEDNEU'
    name : 'Pediatric Neurosurgery'
  - external_service_line_id: 'PEDONC'
    name : 'Pediatric Oncology'
  - external_service_line_id: 'PEDORTH'
    name : 'Pediatric Orthopedics'
  - external_service_line_id: 'PEDURO'
    name : 'Pediatric Urology'
  - external_service_line_id: 'PEDS'
    name : 'Pediatrics'
  - external_service_line_id: 'PLAS'
    name : 'Plastic/Reconstruction'
  - external_service_line_id: 'PODI'
    name : 'Podiatry'
  - external_service_line_id: 'PULM'
    name : 'Pulmonary'
  - external_service_line_id: 'RONC'
    name : 'Radiation Oncology'
  - external_service_line_id: 'THOR'
    name : 'Thoracic'
  - external_service_line_id: 'TRANS'
    name : 'Transplant'
  - external_service_line_id: 'TRAU'
    name : 'Trauma'
  - external_service_line_id: 'GU'
    name : 'Urology'
  - external_service_line_id: 'VASC'
    name : 'Vascular Surgery'
  - external_service_line_id: 'CARY'
    name : 'Cardiology'
  - external_service_line_id: 'DENT'
    name : 'Dental'
  - external_service_line_id: 'EYE'
    name : 'Eye'
  - external_service_line_id: 'ENDO'
    name : 'Endoscopy'
  - external_service_line_id: 'PATH'
    name : 'Pathology'
  - external_service_line_id: 'PSYC'
    name : 'Psychiatry'
  - external_service_line_id: 'RADI'
    name : 'Radiology'
  - external_service_line_id: 'HMH OR MARKE'
    name : 'HMH OR MARKETPLACE'
  - external_service_line_id: 'CV - Neuro'
    name : 'Neurointerventional'
  - external_service_line_id: 'CV - EP'
    name : 'Cardiology - EP'

# Populate OBX event type names
observation_type_names:
  - type_id: 'OBSERVED_ANESTHESIA_READY'
    name: 'Anes Ready'
    color: "#FFC0CB"
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_ANESTHESIA_START'
    name: 'Anes Start'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_ANESTHESIA_FINISH'
    name: 'Anes Stop'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_PROCEDURAL_CARE_COMPLETE'
    name: 'End Care'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_EPIDURAL_TO_C_SECTION'
    name: 'Epidural to'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_IN_EXTENDED_RECOVERY'
    name: 'Extended Car'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_IN_FACILITY'
    name: 'In Facility'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_IN_HOLDING_AREA'
    name: 'In Holding'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_TO_PHASE_II'
    name: 'In Ph II'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_IN_PRE_PROCEDURE'
    name: 'In Pre-Proce'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_IN_PROCEDURAL_RECOVERY'
    name: 'In Recov'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_IN_ROOM'
    name: 'In Room'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_INDUCTION'
    name: 'Induction'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_OUT_OF_PHASE_II_2ND_TIME'
    name: 'Out of Recov'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_OUT_OF_PHASE_II'
    name: 'Out Ph II'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_OUT_OF_PROCEDURAL_RECOVERY'
    name: 'Out Recov'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_OUT_OF_ROOM'
    name: 'Out Room'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_PHYSICIAN_AVAILABLE'
    name: 'Physician Ar'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_PROCEDURE_FINISH'
    name: 'Proc Finish'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_CASE_START'
    name: 'Proc Start'
    color: "#008000"
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_PRE_PROCEDURE_COMPLETE'
    name: 'Ready for OR'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_RETURN_TO_PACU'
    name: 'Return to PA'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_SEDATION_END'
    name: 'Sedation Fin'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_SEDATION_START'
    name: 'Sedation Sta'
    is_custom_phase_end_point: true
