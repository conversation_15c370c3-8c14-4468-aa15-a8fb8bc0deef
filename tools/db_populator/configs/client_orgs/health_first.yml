name: Health First
org_id: health_first

# Populate auth0 org id from Auth0 Management page
auth0_org_ids_per_environment:
  dev: na
  staging: tbd
  prod: org_CLmQ9BrAnAHJ6pyN

service_lines:
  - external_service_line_id: "Medical"
    name: "Medical"
  - external_service_line_id: "Surgery: General SUR"
    name: "General Surgery"
  - external_service_line_id: "Gastroenterology"
    name: "Gastroenterology"
  - external_service_line_id: "USS"
    name: "USS"
  - external_service_line_id: "Bariatrics"
    name: "Bariatrics"
  - external_service_line_id: "Emergency Service"
    name: "Emergency Service"
  - external_service_line_id: "Gynecology"
    name: "Gynecology"
  - external_service_line_id: "General Surgery"
    name: "General Surgery"
  - external_service_line_id: "Orthopaedic Surgery"
    name: "Orthopaedic Surgery"
  - external_service_line_id: "Urology"
    name: "Urology"
  - external_service_line_id: "OB/GYN"
    name: "OB/GYN"
  - external_service_line_id: "Pulmonology"
    name: "Pulmonology"
  - external_service_line_id: "Diagnostic Radiology"
    name: "Diagnostic Radiology"
  - external_service_line_id: "LAB"
    name: "Lab"
  - external_service_line_id: "MRI"
    name: "MRI"
  - external_service_line_id: "Echocardiogram"
    name: "Echocardiogram"
  - external_service_line_id: "Stroke"
    name: "Stroke"
  - external_service_line_id: "MAM"
    name: "MAM"
  - external_service_line_id: "Surgery:Same Day SDS"
    name: "Same Day Surgery"
  - external_service_line_id: "CT Scan"
    name: "CT Scan"
  - external_service_line_id: "Podiatry"
    name: "Podiatry"
  - external_service_line_id: "Aging Life Clinic"
    name: "Aging Life Clinic"
  - external_service_line_id: "PICC Lines at HRMC"
    name: "PICC Lines at HRMC"
  - external_service_line_id: "Zero Credit Bal"
    name: "Zero Credit Bal"
  - external_service_line_id: "Oral Maxillofacial Surgery"
    name: "Oral Maxillofacial Surgery"
  - external_service_line_id: "NMS"
    name: "NMS"
  - external_service_line_id: "Hospital at Home"
    name: "Hospital at Home"
  - external_service_line_id: "General Surgery-"
    name: "General Surgery"

# Populate OBX event type names
observation_type_names:
  - type_id: "OBSERVED_ANESTHESIA_FINISH"
    name: "ANESTHESIASTOP"
    is_custom_phase_end_point: true
  - type_id: "OBSERVED_ANESTHESIA_START"
    name: "ANESTHESIASTART"
    is_custom_phase_end_point: true
  - type_id: "OBSERVED_CASE_CLOSING"
    name: "CLOSURETIME"
    color: "#FFA500"
    is_custom_phase_end_point: true
  - type_id: "OBSERVED_CASE_FINISH"
    name: "SURGERYSTOP"
    is_custom_phase_end_point: true
  - type_id: "OBSERVED_CASE_START"
    name: "SURGERYSTART"
    color: "#008000"
    is_custom_phase_end_point: true
  - type_id: "OBSERVED_IN_PACU"
    name: "PACUSTART"
    is_custom_phase_end_point: true
  - type_id: "OBSERVED_IN_ROOM"
    name: "PATIENTIN"
    is_custom_phase_end_point: true
  - type_id: "OBSERVED_OUT_OF_PACU"
    name: "PACUSTOP"
    is_custom_phase_end_point: true
  - type_id: "OBSERVED_OUT_OF_ROOM"
    name: "PATIENTOUT"
    is_custom_phase_end_point: true
