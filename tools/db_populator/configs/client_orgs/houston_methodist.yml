name: Houston Methodist
org_id: houston_methodist
# Populate auth0 org id from Auth0 Management page
auth0_org_ids_per_environment:
  dev: na
  staging: org_wJ6AOWEXVNCL0dYT
  prod: org_Xj37YbZTAOh54edr

room_mappings:
  - external_id: 'DUNN 6 OB 03'
    internal_id: 'HMH-DUNN06-LD03'
  - external_id: 'DUNN 6 OR 01'
    internal_id: 'HMH-DUNN06-OR01'
  - external_id: 'DUNN 6 OR 02'
    internal_id: 'HMH-DUNN06-OR02'
  - external_id: 'DUNN 6 OR 03'
    internal_id: 'HMH-DUNN06-OR03'
  - external_id: 'DUNN 6 OR 04'
    internal_id: 'HMH-DUNN06-OR04'
  - external_id: 'DUNN OR 01'
    internal_id: 'HMH-DUNN03-OR01'
  - external_id: 'DUNN OR 02'
    internal_id: 'HMH-DUNN03-OR02'
  - external_id: 'DUNN OR 03'
    internal_id: 'HMH-DUNN03-OR03'
  - external_id: 'DUNN OR 04'
    internal_id: 'HMH-DUNN03-OR04'
  - external_id: 'DUNN OR 05'
    internal_id: 'HMH-DUNN03-OR05'
  - external_id: 'DUNN OR 06'
    internal_id: 'HMH-DUNN03-OR06'
  - external_id: 'DUNN OR 07'
    internal_id: 'HMH-DUNN03-OR07'
  - external_id: 'DUNN OR 08'
    internal_id: 'HMH-DUNN03-OR08'
  - external_id: 'DUNN OR 09'
    internal_id: 'HMH-DUNN03-OR09'
  - external_id: 'DUNN OR 10'
    internal_id: 'HMH-DUNN03-OR10'
  - external_id: 'DUNN OR 11'
    internal_id: 'HMH-DUNN03-OR11'
  - external_id: 'DUNN OR 12'
    internal_id: 'HMH-DUNN03-OR12'
  - external_id: 'DUNN OR 14'
    internal_id: 'HMH-DUNN03-OR14'
  - external_id: 'DUNN OR 15'
    internal_id: 'HMH-DUNN03-OR15'
  - external_id: 'DUNN OR 16'
    internal_id: 'HMH-DUNN03-OR16'
  - external_id: 'DUNN OR 17'
    internal_id: 'HMH-DUNN03-OR17'
  - external_id: 'DUNN OR 18'
    internal_id: 'HMH-DUNN03-OR18'
  - external_id: 'DUNN OR 19'
    internal_id: 'HMH-DUNN03-OR19'
  - external_id: 'DUNN OR 20'
    internal_id: 'HMH-DUNN03-OR20'
  - external_id: 'DUNN OR NEURO 1'
    internal_id: 'HMH-DUNN03-NEURO01'
  - external_id: 'DUNN OR NEURO 2'
    internal_id: 'HMH-DUNN03-NEURO02'
  - external_id: "HMH L\\T\\D OB 01"
    internal_id: 'HMH-LD06-LD01'
  - external_id: 'OPC 18 VIRTUAL ROOM 1 (DOR14)'
    internal_id: 'HMH-DUNN03-OR14'
  - external_id: 'OPC 18 VIRTUAL ROOM 2 (DOR17)'
    internal_id: 'HMH-DUNN03-OR17'
  - external_id: 'OPC 18 VIRTUAL ROOM 3 (DOR20)'
    internal_id: 'HMH-DUNN03-OR20'
  - external_id: "HMH L\\T\\D OB 02"
    internal_id: 'HMH-LD06-LD02'
  - external_id: 'MAIN OR 01'
    internal_id: 'HMH-MAIN03-OR01'
  - external_id: 'MAIN OR 02'
    internal_id: 'HMH-MAIN03-OR02'
  - external_id: 'MAIN OR 03'
    internal_id: 'HMH-MAIN03-OR03'
  - external_id: 'MAIN OR 04'
    internal_id: 'HMH-MAIN03-OR04'
  - external_id: 'OPC 18 OR 01'
    internal_id: 'HMH-OPC18-OR01'
  - external_id: 'OPC 18 OR 02'
    internal_id: 'HMH-OPC18-OR02'
  - external_id: 'OPC 18 OR 03'
    internal_id: 'HMH-OPC18-OR03'
  - external_id: 'OPC 18 OR 04'
    internal_id: 'HMH-OPC18-OR04'
  - external_id: 'OPC 18 OR 05'
    internal_id: 'HMH-OPC18-OR05'
  - external_id: 'OPC 18 OR 06'
    internal_id: 'HMH-OPC18-OR06'
  - external_id: 'OPC 18 OR 07'
    internal_id: 'HMH-OPC18-OR07'
  - external_id: 'OPC 18 OR 08'
    internal_id: 'HMH-OPC18-OR08'
  - external_id: 'OPC 18 OR 09'
    internal_id: 'HMH-OPC18-OR09'
  - external_id: 'OPC 18 OR 10'
    internal_id: 'HMH-OPC18-OR10'
  - external_id: 'OPC 18 OR 11'
    internal_id: 'HMH-OPC18-OR11'
  - external_id: 'OPC 18 OR 12'
    internal_id: 'HMH-OPC18-OR12'
  - external_id: 'OPC 18 OR 14'
    internal_id: 'HMH-OPC18-OR14'
  - external_id: 'OPC 18 OR 15'
    internal_id: 'HMH-OPC18-OR15'
  - external_id: 'OPC 18 OR 16'
    internal_id: 'HMH-OPC18-OR16'
  - external_id: 'OPC 18 OR 17'
    internal_id: 'HMH-OPC18-OR17'
  - external_id: 'OPC 19 OR 01'
    internal_id: 'HMH-OPC19-OR01'
  - external_id: 'OPC 19 OR 02'
    internal_id: 'HMH-OPC19-OR02'
  - external_id: 'OPC 19 OR 03'
    internal_id: 'HMH-OPC19-OR03'
  - external_id: 'OPC 19 OR 04'
    internal_id: 'HMH-OPC19-OR04'
  - external_id: 'OPC 19 OR 05'
    internal_id: 'HMH-OPC19-OR05'
  - external_id: 'OPC 19 OR 06'
    internal_id: 'HMH-OPC19-OR06'
  - external_id: 'OPC 19 OR 07'
    internal_id: 'HMH-OPC19-OR07'
  - external_id: 'OPC 19 OR 08'
    internal_id: 'HMH-OPC19-OR08'
  - external_id: 'OPC 19 OR 09'
    internal_id: 'HMH-OPC19-OR09'
  - external_id: 'OPC 19 OR 10'
    internal_id: 'HMH-OPC19-OR10'
  - external_id: 'OPC 19 OR 11'
    internal_id: 'HMH-OPC19-OR11'
  - external_id: 'OPC 19 OR 12'
    internal_id: 'HMH-OPC19-OR12'
  - external_id: 'OPC 19 OR 14'
    internal_id: 'HMH-OPC19-OR14'
  - external_id: 'OPC 19 OR 15'
    internal_id: 'HMH-OPC19-OR15'
  - external_id: 'OPC 19 ORAL 01'
    internal_id: 'HMH-OPC18-OR14'
  - external_id: 'OPC 19 ORAL 02'
    internal_id: 'HMH-OPC18-OR16'
  - external_id: 'OPC19 VIRTUAL OR (OPC18-OR 16)'
    internal_id: 'HMH-OPC18-OR16'
  - external_id: 'OPC18OR-15 (VOPC19OR)'
    internal_id: 'HMH-OPC18-OR15'
  - external_id: 'OPC18OR-17 (VOPC19OR)'
    internal_id: 'HMH-OPC18-OR17'
  - external_id: 'HMH OPC 18 OR-10(VOPC19OR)'
    internal_id: 'HMH-OPC18-OR10'
  - external_id: 'HMH OPC 18 OR-12(VOPC19OR)'
    internal_id: 'HMH-OPC18-OR12'
  - external_id: 'WALTER OR 01'
    internal_id: 'HMH-WT03-OR01'
  - external_id: 'WALTER OR 02'
    internal_id: 'HMH-WT03-OR02'
  - external_id: 'WALTER OR 03'
    internal_id: 'HMH-WT03-OR03'
  - external_id: 'WALTER OR 04'
    internal_id: 'HMH-WT03-OR04'
  - external_id: 'WALTER OR 05'
    internal_id: 'HMH-WT03-OR05'
  - external_id: 'WALTER OR 06'
    internal_id: 'HMH-WT03-OR06'
  - external_id: 'WALTER OR 07'
    internal_id: 'HMH-WT03-OR07'
  - external_id: 'WALTER OR 08'
    internal_id: 'HMH-WT03-OR08'
  - external_id: 'WALTER OR 09'
    internal_id: 'HMH-WT03-OR09'
  - external_id: 'WALTER OR 10'
    internal_id: 'HMH-WT03-OR10'
  - external_id: 'WALTER OR 11'
    internal_id: 'HMH-WT03-OR11'
  - external_id: 'WALTER OR 12'
    internal_id: 'HMH-WT03-OR12'
  - external_id: 'WALTER OR 14'
    internal_id: 'HMH-WT03-OR14'
  - external_id: 'WALTER OR 15'
    internal_id: 'HMH-WT03-OR15'
  - external_id: 'WALTER OR 16'
    internal_id: 'HMH-WT03-OR16'
  - external_id: 'WALTER OR 17'
    internal_id: 'HMH-WT03-OR17'
  - external_id: "MAIN OR CYSTO 01"
    internal_id: "HMH-MAIN03-CY01"
  - external_id: "MAIN OR CYSTO 02"
    internal_id: "HMH-MAIN03-CY02"
  - external_id: "HMSL CYSTO ROOM"
    internal_id: "HMSL-OR-CY01"
  - external_id: "HMSL OR 01"
    internal_id: "HMH-HMSL-OR-OR01"
  - external_id: "HMSL OR 02"
    internal_id: "HMH-HMSL-OR-OR02"
  - external_id: "HMSL OR 03"
    internal_id: "HMH-HMSL-OR-OR03"
  - external_id: "HMSL OR 04"
    internal_id: "HMH-HMSL-OR-OR04"
  - external_id: "HMSL OR 05"
    internal_id: "HMH-HMSL-OR-OR05"
  - external_id: "HMSL OR 06"
    internal_id: "HMH-HMSL-OR-OR06"
  - external_id: "HMSL OR 07"
    internal_id: "HMH-HMSL-OR-OR07"
  - external_id: "HMSL OR 08"
    internal_id: "HMH-HMSL-OR-OR08"
  - external_id: "HMSL OR 09"
    internal_id: "HMH-HMSL-OR-OR09"
  - external_id: "HMSL OR 10"
    internal_id: "HMH-HMSL-OR-OR10"
  - external_id: "HMSL OR 11"
    internal_id: "HMH-HMSL-OR-OR11"
  - external_id: "HMSL OR 12"
    internal_id: "HMH-HMSL-OR-OR12"
  - external_id: "HMSL OR 14"
    internal_id: "HMH-HMSL-OR-OR14"
  - external_id: "HMSL OR 15"
    internal_id: "HMH-HMSL-OR-OR15"
  - external_id: "HMSL OR 16"
    internal_id: "HMH-HMSL-OR-OR16"
  - external_id: "HMSL OR 17"
    internal_id: "HMH-HMSL-OR-OR17"
  - external_id: "HMSL OR 18"
    internal_id: "HMH-HMSL-OR-OR18"
  - external_id: "HMSL OR 19"
    internal_id: "HMH-HMSL-OR-OR19"
  - external_id: "HMSL OR 20"
    internal_id: "HMH-HMSL-OR-OR20"
  - external_id: "HMSL OR 21"
    internal_id: "HMH-HMSL-OR-OR21"
  - external_id: "HMSL OR 22"
    internal_id: "HMH-HMSL-OR-OR22"
  - external_id: "HMSL OR 23"
    internal_id: "HMH-HMSL-OR-OR23"
  - external_id: "HMSL OR 24"
    internal_id: "HMH-HMSL-OR-OR24"
  - external_id: "HMSL OR 25"
    internal_id: "HMH-HMSL-OR-OR25"
  - external_id: "HMSL L&D OR 01"
    internal_id: "HMH-HMSL-LD-LD01"
  - external_id: "HMSL L&D OR 02"
    internal_id: "HMH-HMSL-LD-LD02"
  - external_id: "OBOR VIRTUAL ROOM 2"
    internal_id: "HMH-HMSL-LD-LD02"
  - external_id: "HMSL L&D OR 03"
    internal_id: "HMH-HMSL-LD-LD03"
  - external_id: "OBOR VIRTUAL ROOM 3"
    internal_id: "HMH-HMSL-LD-LD03"
  - external_id: "L&D OR 1"
    internal_id: "HMH-HMSL-LD-LD01"
  - external_id: "L&D OR 2"
    internal_id: "HMH-HMSL-LD-LD02"
  - external_id: "L&D OR 3"
    internal_id: "HMH-HMSL-LD-LD03"
  - external_id: "OR01"
    internal_id: "HMH-HMSL-OR-OR01"
  - external_id: "OR02"
    internal_id: "HMH-HMSL-OR-OR02"
  - external_id: "OR03"
    internal_id: "HMH-HMSL-OR-OR03"
  - external_id: "OR04"
    internal_id: "HMH-HMSL-OR-OR04"
  - external_id: "OR05"
    internal_id: "HMH-HMSL-OR-OR05"
  - external_id: "OR06"
    internal_id: "HMH-HMSL-OR-OR06"
  - external_id: "OR07"
    internal_id: "HMH-HMSL-OR-OR07"
  - external_id: "OR08"
    internal_id: "HMH-HMSL-OR-OR08"
  - external_id: "OR09"
    internal_id: "HMH-HMSL-OR-OR09"
  - external_id: "OR10"
    internal_id: "HMH-HMSL-OR-OR10"
  - external_id: "OR11"
    internal_id: "HMH-HMSL-OR-OR11"
  - external_id: "OR12"
    internal_id: "HMH-HMSL-OR-OR12"
  - external_id: "OR14"
    internal_id: "HMH-HMSL-OR-OR14"
  - external_id: "OR15"
    internal_id: "HMH-HMSL-OR-OR15"
  - external_id: "OR16"
    internal_id: "HMH-HMSL-OR-OR16"
  - external_id: "OR17"
    internal_id: "HMH-HMSL-OR-OR17"
  - external_id: "OR18"
    internal_id: "HMH-HMSL-OR-OR18"
  - external_id: "OR19"
    internal_id: "HMH-HMSL-OR-OR19"
  - external_id: "OR20"
    internal_id: "HMH-HMSL-OR-OR20"
  - external_id: "OR21"
    internal_id: "HMH-HMSL-OR-OR21"
  - external_id: "OR22"
    internal_id: "HMH-HMSL-OR-OR22"
  - external_id: "OR23"
    internal_id: "HMH-HMSL-OR-OR23"
  - external_id: "OR24"
    internal_id: "HMH-HMSL-OR-OR24"
  - external_id: "OR25"
    internal_id: "HMH-HMSL-OR-OR25"
  - external_id: "HMTW OR 01"
    internal_id: "HMH-HMTW-OR01"
  - external_id: "HMTW OR 02"
    internal_id: "HMH-HMTW-OR02"
  - external_id: "HMTW OR 03"
    internal_id: "HMH-HMTW-OR03"
  - external_id: "HMTW OR 04"
    internal_id: "HMH-HMTW-OR04"
  - external_id: "HMTW OR 05"
    internal_id: "HMH-HMTW-OR05"
  - external_id: "HMTW OR 06"
    internal_id: "HMH-HMTW-OR06"
  - external_id: "HMTW OR 07"
    internal_id: "HMH-HMTW-OR07"
  - external_id: "HMTW OR 08"
    internal_id: "HMH-HMTW-OR08"
  - external_id: "HMTW OR 09 HYBRID"
    internal_id: "HMH-HMTW-OR09"
  - external_id: "HMTW OR 10"
    internal_id: "HMH-HMTW-OR10"
  - external_id: "HMTW OR 11"
    internal_id: "HMH-HMTW-OR11"
  - external_id: "HMTW OR 12"
    internal_id: "HMH-HMTW-OR12"
  - external_id: "HMTW OR 14"
    internal_id: "HMH-HMTW-OR14"
  - external_id: "HMTW OR 15 CYSTO"
    internal_id: "HMH-HMTW-OR15"
  - external_id: "HMTW OR 16"
    internal_id: "HMH-HMTW-OR16"
  - external_id: "HMTW OR 17"
    internal_id: "HMH-HMTW-OR17"
  - external_id: "HMTW OR HYBRID 17"
    internal_id: "HMH-HMTW-OR17"
  - external_id: "HMTW OR 18"
    internal_id: "HMH-HMTW-OR18"
  - external_id: "HMTW OR 19"
    internal_id: "HMH-HMTW-OR19"
  - external_id: "HMTW OR 20"
    internal_id: "HMH-HMTW-OR20"
  - external_id: "HMTW OR 21"
    internal_id: "HMH-HMTW-OR21"
  - external_id: "HMTW OR 22"
    internal_id: "HMH-HMTW-OR22"
  - external_id: "HMTW OR 23"
    internal_id: "HMH-HMTW-OR23"
  - external_id: "HMTW OR 24"
    internal_id: "HMH-HMTW-OR24"
  - external_id: "HMTW OR 25"
    internal_id: "HMH-HMTW-OR25"
  - external_id: "HMWB NORTH OR 01"
    internal_id: "HMH-HMWB-N-OR01"
  - external_id: "HMWB NORTH OR 02"
    internal_id: "HMH-HMWB-N-OR02"
  - external_id: "HMWB NORTH OR 03"
    internal_id: "HMH-HMWB-N-OR03"
  - external_id: "HMWB NORTH OR 04"
    internal_id: "HMH-HMWB-N-OR04"
  - external_id: "HMWB NORTH OR 05"
    internal_id: "HMH-HMWB-N-OR05"
  - external_id: "HMWB NORTH OR 06"
    internal_id: "HMH-HMWB-N-OR06"
  - external_id: "HMWB NORTH OR 07"
    internal_id: "HMH-HMWB-N-OR07"
  - external_id: "HMWB NORTH OR 08"
    internal_id: "HMH-HMWB-N-OR08"
  - external_id: "HMWB NORTH OR 09"
    internal_id: "HMH-HMWB-N-OR09"
  - external_id: "HMWB NORTH OR 10"
    internal_id: "HMH-HMWB-N-OR10"
  - external_id: "HMWB CYSTO ROOM"
    internal_id: "HMH-HMWB-N-OR11"
  - external_id: "HMWB SOUTH OR 02"
    internal_id: "HMH-HMWB-S-OR02"
  - external_id: "HMWB SOUTH OR 03"
    internal_id: "HMH-HMWB-S-OR03"
  - external_id: "HMWB SOUTH OR 04"
    internal_id: "HMH-HMWB-S-OR04"
  - external_id: "HMWB CENTERFIELD OR 01"
    internal_id: "HMH-HMWB-C-OR01"
  - external_id: "HMWB CENTERFIELD OR 02"
    internal_id: "HMH-HMWB-C-OR02"
  - external_id: "HMWB CENTERFIELD OR 03"
    internal_id: "HMH-HMWB-C-OR03"
  - external_id: "HMWB CENTERFIELD OR 04"
    internal_id: "HMH-HMWB-C-OR04"
  - external_id: "HMWB CENTERFIELD OR 05"
    internal_id: "HMH-HMWB-C-OR05"
  - external_id: "HMWB CENTERFIELD OR 06"
    internal_id: "HMH-HMWB-C-OR06"
  - external_id: "HMSL ENDO OR 04"
    internal_id: "HMH-HMSL-OR-OR04"
  # Historical data support
  - external_id: "DN6OR02"
    internal_id: "HMH-DUNN06-OR02"
  - external_id: "DN3OR 11"
    internal_id: "HMH-DUNN03-OR11"
  - external_id: "DN3OR 15"
    internal_id: "HMH-DUNN03-OR15"
  - external_id: "DN3OR 16"
    internal_id: "HMH-DUNN03-OR16"
  - external_id: "DN3OR 10"
    internal_id: "HMH-DUNN03-OR10"
  - external_id: "DN3OR 20"
    internal_id: "HMH-DUNN03-OR20"
  - external_id: "DN3OR 02"
    internal_id: "HMH-DUNN03-OR02"
  - external_id: "DN3OR 05"
    internal_id: "HMH-DUNN03-OR05"
  - external_id: "DN3OR NEURO 01"
    internal_id: "HMH-DUNN03-NEURO01"
  - external_id: "DN3OR 17"
    internal_id: "HMH-DUNN03-OR17"
  - external_id: "DN3OR 09"
    internal_id: "HMH-DUNN03-OR09"
  - external_id: "DN6OR03"
    internal_id: "HMH-DUNN06-OR03"
  - external_id: "DN3OR 12"
    internal_id: "HMH-DUNN03-OR12"
  - external_id: "DN6OR01"
    internal_id: "HMH-DUNN06-OR01"
  - external_id: "DN3OR 18"
    internal_id: "HMH-DUNN03-OR18"
  - external_id: "DN3OR 03"
    internal_id: "HMH-DUNN03-OR03"
  - external_id: "DN6OR OB Robotics"
    internal_id: "HMH-DUNN06-LD03"
  - external_id: "DN3OR 14"
    internal_id: "HMH-DUNN03-OR14"
  - external_id: "DN3OR 08"
    internal_id: "HMH-DUNN03-OR08"
  - external_id: "DN3OR 07"
    internal_id: "HMH-DUNN03-OR07"
  - external_id: "DN3OR 06"
    internal_id: "HMH-DUNN03-OR06"
  - external_id: "DN3OR 04"
    internal_id: "HMH-DUNN03-OR04"
  - external_id: "DN3OR 19"
    internal_id: "HMH-DUNN03-OR19"
  - external_id: "DN6OR04"
    internal_id: "HMH-DUNN06-OR04"
  - external_id: "DN3OR 01"
    internal_id: "HMH-DUNN03-OR01"
  - external_id: "DN3OR NEURO 02"
    internal_id: "HMH-DUNN03-NEURO02"
  - external_id: "MOR01"
    internal_id: "HMH-MAIN03-OR01"
  - external_id: "MOR02"
    internal_id: "HMH-MAIN03-OR02"
  - external_id: "MOR03"
    internal_id: "HMH-MAIN03-OR03"
  - external_id: "MOR04"
    internal_id: "HMH-MAIN03-OR04"
  - external_id: "HMH L&D OB 01"
    internal_id: "HMH-LD06-LD01"
  - external_id: "HMH L&D OB 02"
    internal_id: "HMH-LD06-LD02"
  - external_id: "OPC18OR01"
    internal_id: "HMH-OPC18-OR01"
  - external_id: "OPC18OR02"
    internal_id: "HMH-OPC18-OR02"
  - external_id: "OPC18OR03"
    internal_id: "HMH-OPC18-OR03"
  - external_id: "OPC18OR04"
    internal_id: "HMH-OPC18-OR04"
  - external_id: "OPC18OR05"
    internal_id: "HMH-OPC18-OR05"
  - external_id: "OPC18OR06"
    internal_id: "HMH-OPC18-OR06"
  - external_id: "OPC18OR07"
    internal_id: "HMH-OPC18-OR07"
  - external_id: "OPC18OR08"
    internal_id: "HMH-OPC18-OR08"
  - external_id: "OPC18OR09"
    internal_id: "HMH-OPC18-OR09"
  - external_id: "OPC18OR10"
    internal_id: "HMH-OPC18-OR10"
  - external_id: "OPC18OR11"
    internal_id: "HMH-OPC18-OR11"
  - external_id: "OPC18OR12"
    internal_id: "HMH-OPC18-OR12"
  - external_id: "OPC18OR14"
    internal_id: "HMH-OPC18-OR14"
  - external_id: "OPC19OR01"
    internal_id: "HMH-OPC19-OR01"
  - external_id: "OPC19OR02"
    internal_id: "HMH-OPC19-OR02"
  - external_id: "OPC19OR03"
    internal_id: "HMH-OPC19-OR03"
  - external_id: "OPC19OR04"
    internal_id: "HMH-OPC19-OR04"
  - external_id: "OPC19OR05"
    internal_id: "HMH-OPC19-OR05"
  - external_id: "OPC19OR06"
    internal_id: "HMH-OPC19-OR06"
  - external_id: "OPC19OR07"
    internal_id: "HMH-OPC19-OR07"
  - external_id: "OPC19OR08"
    internal_id: "HMH-OPC19-OR08"
  - external_id: "OPC19OR09"
    internal_id: "HMH-OPC19-OR09"
  - external_id: "OPC19OR10"
    internal_id: "HMH-OPC19-OR10"
  - external_id: "OPC19OR11"
    internal_id: "HMH-OPC19-OR11"
  - external_id: "OPC19OR12"
    internal_id: "HMH-OPC19-OR12"
  - external_id: "OPC19OR14"
    internal_id: "HMH-OPC19-OR14"
  - external_id: "OPC19OR15"
    internal_id: "HMH-OPC19-OR15"
  - external_id: "OPC 19 VIRTUAL OR (DOR-NEURO1)"
    internal_id: "HMH-DUNN03-NEURO01"
  - external_id: "OPC 19 VIRTUAL OR (DOR-NEURO2)"
    internal_id: "HMH-DUNN03-NEURO02"
  - external_id: "OPC 19 VIRTUAL OR 02 (DOR12)"
    internal_id: "HMH-DUNN03-OR12"
  - external_id: "OPC 19 VIRTUAL OR 03 (DOR14)"
    internal_id: "HMH-DUNN03-OR14"
  - external_id: "OPC 19 VIRTUAL OR 04 (DOR16)"
    internal_id: "HMH-DUNN03-OR16"
  - external_id: "OPC 19 VIRTUAL OR 05 (DORWT2)"
    internal_id: "HMH-WT03-OR02"
  - external_id: "OPC 19 VIRTUAL OR 07 (DOR07)"
    internal_id: "HMH-DUNN03-OR07"
  - external_id: "OPC 19 VIRTUAL OR 08 (DOR15)"
    internal_id: "HMH-DUNN03-OR15"
  - external_id: "OPC18OR-14 ORAL (vOPC19OR)"
    internal_id: "HMH-OPC18-OR14"
  - external_id: "OPC18OR-16 ORAL (vOPC19OR)"
    internal_id: "HMH-OPC18-OR16"
  - external_id: "OPC18OR-15 ORTHO (vOPC19OR)"
    internal_id: "HMH-OPC18-OR15"
  - external_id: "OPC18OR-17 ORTHO (vOPC19OR)"
    internal_id: "HMH-OPC18-OR17"
  - external_id: "WT3OR 01"
    internal_id: "HMH-WT03-OR01"
  - external_id: "WT3OR 02"
    internal_id: "HMH-WT03-OR02"
  - external_id: "WT3OR 03"
    internal_id: "HMH-WT03-OR03"
  - external_id: "WT3OR 04"
    internal_id: "HMH-WT03-OR04"
  - external_id: "WT3OR 05"
    internal_id: "HMH-WT03-OR05"
  - external_id: "WT3OR-05 (vOPC18 OR)"
    internal_id: "HMH-WT03-OR05"
  - external_id: "WT3OR 06"
    internal_id: "HMH-WT03-OR06"
  - external_id: "WT3OR 07"
    internal_id: "HMH-WT03-OR07"
  - external_id: "WT3OR 08"
    internal_id: "HMH-WT03-OR08"
  - external_id: "WT3OR 09"
    internal_id: "HMH-WT03-OR09"
  - external_id: "WT3OR 10"
    internal_id: "HMH-WT03-OR10"
  - external_id: "WT3OR 11"
    internal_id: "HMH-WT03-OR11"
  - external_id: "WT3OR 12"
    internal_id: "HMH-WT03-OR12"
  - external_id: "WT3OR 15 (H)"
    internal_id: "HMH-WT03-OR15"
  - external_id: "WT3OR 16 (H)"
    internal_id: "HMH-WT03-OR16"
  - external_id: "WT3OR 17 (H)"
    internal_id: "HMH-WT03-OR17"
  - external_id: "STJOR 01"
    internal_id: "HMH-HMCL-OR01"
  - external_id: "STJOR 02"
    internal_id: "HMH-HMCL-OR02"
  - external_id: "STJOR 03"
    internal_id: "HMH-HMCL-OR03"
  - external_id: "STJOR 04"
    internal_id: "HMH-HMCL-OR04"
  - external_id: "STJOR 05"
    internal_id: "HMH-HMCL-OR05"
  - external_id: "STJOR 06"
    internal_id: "HMH-HMCL-OR06"
  - external_id: "STJOR 07"
    internal_id: "HMH-HMCL-OR07"
  - external_id: "STJOR 08"
    internal_id: "HMH-HMCL-OR08"
  - external_id: "L&D OR01"
    internal_id: "HMH-HMCL-LD1"
  - external_id: "L&D OR02"
    internal_id: "HMH-HMCL-LD2"
  - external_id: "ASC 0R 01"
    internal_id: "HMH-HMCL-ASC1"
  - external_id: "ASC OR 02"
    internal_id: "HMH-HMCL-ASC2"
  - external_id: "ASC OR 04"
    internal_id: "HMH-HMCL-ASC4"
  - external_id: "ASU 1"
    internal_id: "HMH-HMBT-ASC01"
  - external_id: "ASU 2"
    internal_id: "HMH-HMBT-ASC02"
  - external_id: "ASU 3"
    internal_id: "HMH-HMBT-ASC03"
  - external_id: "BOR01"
    internal_id: "HMH-HMBT-OR01"
  - external_id: "BOR02"
    internal_id: "HMH-HMBT-OR02"
  - external_id: "BOR04"
    internal_id: "HMH-HMBT-OR04"
  - external_id: "BOR05"
    internal_id: "HMH-HMBT-OR05"
  - external_id: "BOR06"
    internal_id: "HMH-HMBT-OR06"
  - external_id: "BOR09"
    internal_id: "HMH-HMBT-OR09"
  - external_id: "BOR10"
    internal_id: "HMH-HMBT-OR10"
  - external_id: "BOR11"
    internal_id: "HMH-HMBT-OR11"
  - external_id: "BOR12"
    internal_id: "HMH-HMBT-OR12"
  - external_id: "BOR14"
    internal_id: "HMH-HMBT-OR14"
  - external_id: "BOR15"
    internal_id: "HMH-HMBT-OR15"
  - external_id: "HMSJ L&D OR L&D OR01"
    internal_id: "HMH-HMBT-LD01"
  - external_id: "HMSJ L&D OR L&D OR02"
    internal_id: "HMH-HMBT-LD02"
  - external_id: "HMW L\\T\\D OR 01"
    internal_id: "HMH-HMW-LD01"
  - external_id: "HMW L&D OR 01"
    internal_id: "HMH-HMW-LD01"
  - external_id: "HMW L\\T\\D OR 02"
    internal_id: "HMH-HMW-LD02"
  - external_id: "HMW L&D OR 02"
    internal_id: "HMH-HMW-LD02"
  - external_id: "HMW L\\T\\D OR 03"
    internal_id: "HMH-HMW-LD03"
  - external_id: "HMW L&D OR 03"
    internal_id: "HMH-HMW-LD03"
  - external_id: "HMW OR 01"
    internal_id: "HMH-HMW-OR01"
  - external_id: "HMW OR 02"
    internal_id: "HMH-HMW-OR02"
  - external_id: "HMW OR 03"
    internal_id: "HMH-HMW-OR03"
  - external_id: "HMW OR 04"
    internal_id: "HMH-HMW-OR04"
  - external_id: "HMW OR 05"
    internal_id: "HMH-HMW-OR05"
  - external_id: "HMW OR 06"
    internal_id: "HMH-HMW-OR06"
  - external_id: "HMW OR 07"
    internal_id: "HMH-HMW-OR07"
  - external_id: "HMW OR 08"
    internal_id: "HMH-HMW-OR08"
  - external_id: "HMW OR 09"
    internal_id: "HMH-HMW-OR09"
  - external_id: "HMW OR 10"
    internal_id: "HMH-HMW-OR10"
  - external_id: "HMW OR 11"
    internal_id: "HMH-HMW-OR11"
  - external_id: "HMW OR 12"
    internal_id: "HMH-HMW-OR12"
  - external_id: "HMW OR 14"
    internal_id: "HMH-HMW-OR14"
  - external_id: "HMW OR 15"
    internal_id: "HMH-HMW-OR15"
  - external_id: "HMW OR 16"
    internal_id: "HMH-HMW-OR16"
  - external_id: "HMW OR 17"
    internal_id: "HMH-HMW-OR17"
  - external_id: "HMW OR 18"
    internal_id: "HMH-HMW-OR18"
  - external_id: "HMW OR 19"
    internal_id: "HMH-HMW-OR19"
  - external_id: "HMW OR 20"
    internal_id: "HMH-HMW-OR20"
  - external_id: "HMW OR 21"
    internal_id: "HMH-HMW-OR21"
  - external_id: "HMW OR 22"
    internal_id: "HMH-HMW-OR22"
  - external_id: "HMW OR 23"
    internal_id: "HMH-HMW-OR23"
  - external_id: "HMW OR 24"
    internal_id: "HMH-HMW-OR24"
  - external_id: "HMW OR 25"
    internal_id: "HMH-HMW-OR25"
  - external_id: "HMSTJ ASC OR 01"
    internal_id: "HMH-HMCL-ASC1"
  - external_id: "HMSTJ ASC OR 02"
    internal_id: "HMH-HMCL-ASC2"
  - external_id: "HMSTJ ASC OR 03"
    internal_id: "HMH-HMCL-ASC3"
  - external_id: "HMSTJ ASC OR 04"
    internal_id: "HMH-HMCL-ASC4"
  - external_id: "HMSTJ L\\T\\D OR 01"
    internal_id: "HMH-HMCL-LD1"
  - external_id: "HMSTJ L&D OR 01"
    internal_id: "HMH-HMCL-LD1"
  - external_id: "HMSTJ L\\T\\D OR 02"
    internal_id: "HMH-HMCL-LD2"
  - external_id: "HMSTJ L&D OR 02"
    internal_id: "HMH-HMCL-LD2"
  - external_id: "HMSTJ OR 01"
    internal_id: "HMH-HMCL-OR01"
  - external_id: "HMSTJ OR 02"
    internal_id: "HMH-HMCL-OR02"
  - external_id: "HMSTJ OR 03"
    internal_id: "HMH-HMCL-OR03"
  - external_id: "HMSTJ OR 04"
    internal_id: "HMH-HMCL-OR04"
  - external_id: "HMSTJ OR 05"
    internal_id: "HMH-HMCL-OR05"
  - external_id: "HMSTJ OR 06"
    internal_id: "HMH-HMCL-OR06"
  - external_id: "HMSTJ OR 07"
    internal_id: "HMH-HMCL-OR07"
  - external_id: "HMSTJ OR 08"
    internal_id: "HMH-HMCL-OR08"
  - external_id: "HMB ASU OR 01"
    internal_id: "HMH-HMBT-ASC01"
  - external_id: "HMB ASU OR 02"
    internal_id: "HMH-HMBT-ASC02"
  - external_id: "HMB ASU OR 03"
    internal_id: "HMH-HMBT-ASC03"
  - external_id: "HMB L\\T\\D OR 01"
    internal_id: "HMH-HMBT-LD01"
  - external_id: "HMB L&D OR 01"
    internal_id: "HMH-HMBT-LD01"
  - external_id: "HMB L\\T\\D OR 02"
    internal_id: "HMH-HMBT-LD02"
  - external_id: "HMB L&D OR 02"
    internal_id: "HMH-HMBT-LD02"
  - external_id: "HMB OR 01"
    internal_id: "HMH-HMBT-OR01"
  - external_id: "HMB OR 02"
    internal_id: "HMH-HMBT-OR02"
  - external_id: "HMB OR 03"
    internal_id: "HMH-HMBT-OR03"
  - external_id: "HMB OR 04"
    internal_id: "HMH-HMBT-OR04"
  - external_id: "HMB OR 05"
    internal_id: "HMH-HMBT-OR05"
  - external_id: "HMB OR 06"
    internal_id: "HMH-HMBT-OR06"
  - external_id: "HMB OR 07"
    internal_id: "HMH-HMBT-OR07"
  - external_id: "HMB OR 08"
    internal_id: "HMH-HMBT-OR08"
  - external_id: "HMB OR 09"
    internal_id: "HMH-HMBT-OR09"
  - external_id: "HMB OR 10"
    internal_id: "HMH-HMBT-OR10"
  - external_id: "HMB OR 11"
    internal_id: "HMH-HMBT-OR11"
  - external_id: "HMB OR 12"
    internal_id: "HMH-HMBT-OR12"
  - external_id: "HMB OR 14"
    internal_id: "HMH-HMBT-OR14"
  - external_id: "HMB OR 15"
    internal_id: "HMH-HMBT-OR15"
  - external_id: "HMW L&D OR L&D OR01"
    internal_id: "HMH-HMW-LD01"
  - external_id: "HMW L&D OR L&D OR02"
    internal_id: "HMH-HMW-LD02"
  - external_id: "HMW L&D OR L&D OR03"
    internal_id: "HMH-HMW-LD03"
  - external_id: "WOR01HYBRID"
    internal_id: "HMH-HMW-OR01"
  - external_id: "WOR02"
    internal_id: "HMH-HMW-OR02"
  - external_id: "WOR03"
    internal_id: "HMH-HMW-OR03"
  - external_id: "WOR04"
    internal_id: "HMH-HMW-OR04"
  - external_id: "WOR05"
    internal_id: "HMH-HMW-OR05"
  - external_id: "WOR06"
    internal_id: "HMH-HMW-OR06"
  - external_id: "WOR07"
    internal_id: "HMH-HMW-OR07"
  - external_id: "WOR08"
    internal_id: "HMH-HMW-OR08"
  - external_id: "WOR09"
    internal_id: "HMH-HMW-OR09"
  - external_id: "WOR10"
    internal_id: "HMH-HMW-OR10"
  - external_id: "WOR11"
    internal_id: "HMH-HMW-OR11"
  - external_id: "WOR12"
    internal_id: "HMH-HMW-OR12"
  - external_id: "WOR14"
    internal_id: "HMH-HMW-OR14"
  - external_id: "WOR15"
    internal_id: "HMH-HMW-OR15"
  - external_id: "WOR16"
    internal_id: "HMH-HMW-OR16"
  - external_id: "WOR17"
    internal_id: "HMH-HMW-OR17"
  - external_id: "WOR18"
    internal_id: "HMH-HMW-OR18"
  - external_id: "WOR19"
    internal_id: "HMH-HMW-OR19"
  - external_id: "WOR20"
    internal_id: "HMH-HMW-OR20"
  - external_id: "WOR21"
    internal_id: "HMH-HMW-OR21"
  - external_id: "WOR22"
    internal_id: "HMH-HMW-OR22"
  - external_id: "WOR23"
    internal_id: "HMH-HMW-OR23"
  - external_id: "WOR24"
    internal_id: "HMH-HMW-OR24"
  - external_id: "WOR25"
    internal_id: "HMH-HMW-OR25"
  - external_id: "HMPO HMSC BELLAIRE OR 01"
    internal_id: "HMH-HMBA-OR01"
  - external_id: "HMPO HMSC BELLAIRE OR 02"
    internal_id: "HMH-HMBA-OR02"
  - external_id: "HMPO HMSC BELLAIRE OR 03"
    internal_id: "HMH-HMBA-OR03"
  - external_id: "HMPO HMSC BELLAIRE OR 04"
    internal_id: "HMH-HMBA-OR04"
  - external_id: "HMCY OR 01"
    internal_id: "HMH-HMCY-OR-OR01"
  - external_id: "HMCY OR 02"
    internal_id: "HMH-HMCY-OR-OR02"
  - external_id: "HMCY OR 03"
    internal_id: "HMH-HMCY-OR-OR03"
  - external_id: "HMCY OR 04"
    internal_id: "HMH-HMCY-OR-OR04"
  - external_id: "HMCY OR 05"
    internal_id: "HMH-HMCY-OR-OR05"
  - external_id: "HMCY OR 06"
    internal_id: "HMH-HMCY-OR-OR06"
  - external_id: "HMCY OR 07"
    internal_id: "HMH-HMCY-OR-OR07"
  - external_id: "HMCY OR 08 CYSTO"
    internal_id: "HMH-HMCY-OR-OR08"
  - external_id: "HMCY OR 10 HYBRID"
    internal_id: "HMH-HMCY-OR-OR09"
  - external_id: "HMCY L&D OR 01"
    internal_id: "HMH-HMCY-LD-OR01"
  - external_id: "HMCY L&D OR 02"
    internal_id: "HMH-HMCY-LD-OR02"
  - external_id: "HMCY L&D OR 03"
    internal_id: "HMH-HMCY-LD-OR03"
  - external_id: "HMCY ENDO 01"
    internal_id: "HMH-HMCY-ENDO-OR01"
  - external_id: "HMCY ENDO 02"
    internal_id: "HMH-HMCY-ENDO-OR02"
  - external_id: "HMCY ENDO BRONCH 01"
    internal_id: "HMH-HMCY-ENDO-BRON01"
  - external_id: "HMCY CATH LAB RM 1"
    internal_id: "HMH-HMCY-CATH-CATH01"
  - external_id: "HMCY CATH LAB RM 2"
    internal_id: "HMH-HMCY-CATH-CATH02"
  - external_id: "HMCY CATH LAB RM 3"
    internal_id: "HMH-HMCY-CATH-CATH03"

service_lines:
  - external_service_line_id: 'ORT'
    name: 'Orthopedic'
  - external_service_line_id: 'OPH'
    name: 'Opthamalogy'
  - external_service_line_id: 'ENT'
    name: 'Ear, Nose, Throat'
  - external_service_line_id: 'PLA'
    name: 'Plastic'
  - external_service_line_id: 'CTH'
    name: 'Cardiothoracic'
  - external_service_line_id: 'VAS'
    name: 'Vascular'
  - external_service_line_id: 'THO'
    name: 'Thoracic'
  - external_service_line_id: 'PAI'
    name: 'Pain Management'
  - external_service_line_id: 'HAN'
    name: 'Hand'
  - external_service_line_id: 'OS'
    name: 'Oral Services'
  - external_service_line_id: 'CV'
    name: 'Cardiovascular'
  - external_service_line_id: 'POD'
    name: 'Podiatry'
  - external_service_line_id: 'GEN'
    name: 'General'
  - external_service_line_id: 'ONC'
    name: 'Oncology'
  - external_service_line_id: 'PUL'
    name: 'Pulmonary'
  - external_service_line_id: 'CAR'
    name: 'Cardiology'
  - external_service_line_id: 'OBG'
    name: 'Obstetrics and Gynecology'
  - external_service_line_id: 'COL'
    name: 'Colorectal'
  - external_service_line_id: 'URO'
    name: 'Urology'
  - external_service_line_id: 'PED'
    name: 'Pediatric'
  - external_service_line_id: 'MAX'
    name: 'Maxillofacial'
  - external_service_line_id: 'GI'
    name: 'Gastrointestinal'
  - external_service_line_id: 'OB'
    name: 'Obstetrics'
  - external_service_line_id: 'ORTHO'
    name: 'Orthopedics'
  - external_service_line_id: 'NEU'
    name: 'Neurosurgical'
  - external_service_line_id: 'GYN'
    name: 'Gynecology'

# Populate OBX event type names
observation_type_names:
  - type_id: 'OBSERVED_ANESTHESIA_AVAILABLE'
    name: 'Anesthesia Available'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_ANESTHESIA_FINISH'
    name: 'Anesthesia Finish'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_ANESTHESIA_READY'
    name: 'Anesthesia Ready'
    color: "#FFC0CB"
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_ANESTHESIA_START'
    name: 'Anesthesia Start'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_AT_CECUM'
    name: 'At Cecum'
  - type_id: 'OBSERVED_CASE_CLOSING'
    name: 'Case Closing'
    color: "#FFA500"
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_CASE_FINISH'
    name: 'Case Finish'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_CASE_START'
    name: 'Case Start'
    color: "#008000"
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_CATHLAB_TO_OR'
    name: 'Cathlab to OR'
  - type_id: 'OBSERVED_CLEANUP_COMPLETE'
    name: 'Cleanup Complete'
  - type_id: 'OBSERVED_CLEANUP_START'
    name: 'Cleanup Start'
  - type_id: 'OBSERVED_DECISION_TIME'
    name: 'Decision Time'
  - type_id: 'OBSERVED_DISCHARGE_CRITERIA_MET'
    name: 'Discharge Criteria Met'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_EPIDURAL_TO_C_SECTION'
    name: 'Epidural to C-section'
  - type_id: 'OBSERVED_HYSTEROTOMY'
    name: 'Hysterotomy'
  - type_id: 'OBSERVED_IN_EXTENDED_RECOVERY'
    name: 'In Extended Recovery'
  - type_id: 'OBSERVED_IN_FACILITY'
    name: 'In Facility'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_IN_HOLDING_AREA'
    name: 'In Holding Area'
  - type_id: 'OBSERVED_IN_PACU'
    name: 'In PACU'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_IN_PHASE_II'
    name: 'In Phase II'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_IN_PRE_PROCEDURE'
    name: 'In Pre-Procedure'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_IN_PROCEDURAL_RECOVERY'
    name: 'In Procedural Recovery'
  - type_id: 'OBSERVED_IN_ROOM'
    name: 'In Room'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_INDUCTION'
    name: 'Induction'
  - type_id: 'OBSERVED_OPNOTE_VERIFIED'
    name: 'OpNote Verified'
  - type_id: 'OBSERVED_OUT_OF_EXTENDED_RECOVERY'
    name: 'Out of Extended Recovery'
  - type_id: 'OBSERVED_OUT_OF_HOLDING_AREA'
    name: 'Out of Holding Area'
  - type_id: 'OBSERVED_OUT_OF_PACU_2ND_TIME'
    name: 'Out of PACU (2nd Time)'
  - type_id: 'OBSERVED_OUT_OF_PACU_3RD_TIME'
    name: 'Out of PACU (3rd Time)'
  - type_id: 'OBSERVED_OUT_OF_PACU'
    name: 'Out of PACU'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_OUT_OF_PHASE_II_2ND_TIME'
    name: 'Out of Phase II (2nd Time)'
  - type_id: 'OBSERVED_OUT_OF_PHASE_II'
    name: 'Out of Phase II'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_OUT_OF_PRE_PROCEDURE'
    name: 'Out of Pre-Procedure'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_OUT_OF_PROCEDURAL_RECOVERY'
    name: 'Out of Procedural Recovery'
  - type_id: 'OBSERVED_OUT_OF_ROOM'
    name: 'Out of Room'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_PATIENT_MOVED_TO_ANOTHER_OR_ROOM'
    name: 'Patient moved to another OR room'
  - type_id: 'OBSERVED_PATIENT_SENT_FOR'
    name: 'Patient Sent For'
  - type_id: 'OBSERVED_PATIENT_TRANSFER_TO_HOSPITAL_ROOM'
    name: 'Patient transfer to a hospital room'
  - type_id: 'OBSERVED_PHASE_II_CARE_COMPLETE'
    name: 'Phase II Care Complete'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_PHYSICIAN_AVAILABLE'
    name: 'Physician Available'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_PRE_PROCEDURE_COMPLETE'
    name: 'Pre-Procedure Complete'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_PREP_START'
    name: 'Prep Start'
  - type_id: 'OBSERVED_PROCEDURAL_CARE_COMPLETE'
    name: 'Procedural Care Complete'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_PROCEDURE_FINISH'
    name: 'Procedure Finish'
  - type_id: 'OBSERVED_PREP_COMPLETE'
    name: 'Procedure Prep Complete'
  - type_id: 'OBSERVED_RETURN_TO_OR'
    name: 'Return to OR'
  - type_id: 'OBSERVED_RETURN_TO_PACU_3RD_TIME'
    name: 'Return to PACU (3rd Time)'
  - type_id: 'OBSERVED_RETURN_TO_PACU'
    name: 'Return to PACU'
  - type_id: 'OBSERVED_RETURN_TO_PHASE_II'
    name: 'Return to Phase II'
  - type_id: 'OBSERVED_SEDATION_END'
    name: 'Sedation End'
  - type_id: 'OBSERVED_SEDATION_START'
    name: 'Sedation Start'
  - type_id: 'OBSERVED_SETUP_COMPLETE'
    name: 'Setup Complete'
  - type_id: 'OBSERVED_SETUP_START'
    name: 'Setup Start'
  - type_id: 'OBSERVED_TO_PHASE_II'
    name: 'To Phase II'
  - type_id: 'OBSERVED_TIMEOUT_ANESTHESIA'
    name: 'TIMEOUT_ANESTHESIA'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_TIMEOUT_DEBRIEF'
    name: 'TIMEOUT_DEBRIEF'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_TIMEOUT_FIRE_SAFETY'
    name: 'TIMEOUT_FIRE_SAFETY'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_TIMEOUT_PREINCISION'
    name: 'TIMEOUT_PREINCISION'
    is_custom_phase_end_point: true
  - type_id: 'OBSERVED_TIMEOUT_PREPROCEDURE'
    name: 'TIMEOUT_PREPROCEDURE'
    is_custom_phase_end_point: true
