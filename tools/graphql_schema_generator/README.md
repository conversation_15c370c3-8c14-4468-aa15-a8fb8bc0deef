# GraphQL Schema Generator

This tool is used to generate and sdl representation of our current GraphQL Schema as defined by Graphene.

# Prerequisites

This tool requires Graphene and other Python packages, so install the requirements in the virtual environment

This is python, so install the top level requirements
```
poetry install
```
Activate the virtual environment in a new shell.
```
poetry shell
```

# Run

```
make generate-graphql
```
