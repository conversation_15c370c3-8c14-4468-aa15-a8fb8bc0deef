import hashlib
import uuid
from typing import Union


from sqlalchemy.sql import sqltypes


def convert_to_uuid(value: Union[bytes, int, str, sqltypes.UUID[str], uuid.UUID]) -> uuid.UUID:
    """
    Typing helper function for converting valid uuid strings and sql UUID types
    into str
    """
    if isinstance(value, bytes):
        value = uuid.UUID(bytes=value)
    elif isinstance(value, int):
        value = uuid.UUID(int=value)
    elif isinstance(value, uuid.UUID):
        value = value
    else:
        value = uuid.UUID(str(value))
    return value


def _check_if_valid_uuid(id: str) -> bool:
    try:
        return True if uuid.UUID(id) else False
    except Exception:
        return False


def convert_str_to_uuid(id: Union[str, sqltypes.UUID[str], uuid.UUID]) -> uuid.UUID:
    """
    Typing helper function for str/UUID fields in sqlalchemy. We interchangeably use uuid/str fields in our tests.
    This converts an invalid uuid str to an uuid
    """
    if isinstance(id, uuid.UUID):
        return id
    elif isinstance(id, str) and _check_if_valid_uuid(id):
        return uuid.UUID(id)
    hex_string = hashlib.md5(str(id).encode("UTF-8")).hexdigest()
    return uuid.UUID(hex=hex_string)
