# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

"""
Versioned mixin class and other utilities.
From https://docs.sqlalchemy.org/en/14/_modules/examples/versioned_history/history_meta.html

Modified to store the current version instead of the previous version in the history table.
"""

from sqlalchemy import (
    Column,
    ForeignKeyConstraint,
    Integer,
    event,
    text,
    util,
    PrimaryKeyConstraint,
)
from sqlalchemy import inspect
from sqlalchemy.orm.session import sessionmaker, Session
from sqlalchemy.orm import attributes, object_mapper, configure_mappers
from sqlalchemy.orm.exc import UnmappedColumnError
from sqlalchemy.orm.relationships import RelationshipProperty


def col_references_table(col, table):
    for fk in col.foreign_keys:
        if fk.references(table):
            return True
    return False


def _is_versioning_col(col):
    return "version_meta" in col.info


def _update_history_col(orignal_column, history_column):
    orignal_column.info["history_copy"] = history_column
    history_column.unique = False
    # Removed these so that we keep the server defaults from our timestamp mixin
    # history_column.default = history_column.server_default = None
    history_column.autoincrement = False
    return history_column


def _history_mapper(local_mapper):
    # Necessary to force loading of all of our attributes in the table
    configure_mappers()
    cls = local_mapper.class_

    if cls.__dict__.get("_history_mapper_configured", False):
        return

    cls._history_mapper_configured = True

    super_mapper = local_mapper.inherits
    polymorphic_on = None
    super_fks = []
    properties = util.OrderedDict()

    if super_mapper:
        super_history_mapper = super_mapper.class_.__history_mapper__
    else:
        super_history_mapper = None

    if not super_mapper or local_mapper.local_table is not super_mapper.local_table:
        version_meta = {"version_meta": True}  # add column.info to identify
        # columns specific to versioning

        history_table = local_mapper.local_table.to_metadata(
            local_mapper.local_table.metadata,
            name=local_mapper.local_table.name + "_history",
        )
        cols = []
        for orig_c, history_c in zip(local_mapper.local_table.c, history_table.c):
            history_c = _update_history_col(orig_c, history_c)
            cols.append(history_c)

            if super_mapper and col_references_table(orig_c, super_mapper.local_table):
                super_fks.append(
                    (
                        history_c.key,
                        list(super_history_mapper.local_table.primary_key)[0],
                    )
                )

            if orig_c is local_mapper.polymorphic_on:
                polymorphic_on = history_c

            orig_prop = local_mapper.get_property_by_column(orig_c)
            # carry over column re-mappings
            if len(orig_prop.columns) > 1 or orig_prop.columns[0].key != orig_prop.key:
                properties[orig_prop.key] = tuple(
                    col.info["history_copy"] for col in orig_prop.columns
                )

        for const in list(history_table.constraints):
            if not isinstance(const, (PrimaryKeyConstraint)):
                history_table.constraints.discard(const)

        # "version" stores the integer version id.  This column is
        # required.
        history_table.append_column(
            Column(
                "version",
                Integer,
                primary_key=True,
                autoincrement=False,
                info=version_meta,
                # Added so that we have a version in python before we store it the first time
                default=1,
                # Added so that when we add this column to a table, they all start at version 1
                server_default=text("1"),
            )
        )

        if super_mapper:
            super_fks.append(("version", super_history_mapper.local_table.c.version))

        if super_fks:
            history_table.append_constraint(ForeignKeyConstraint(*zip(*super_fks)))
    else:
        history_table = None
        super_history_table = super_mapper.local_table.metadata.tables[
            super_mapper.local_table.name + "_history"
        ]
        # single table inheritance.  take any additional columns that may have
        # been added and add them to the history table.
        for column in local_mapper.local_table.c:
            if column.key not in super_history_mapper.local_table.c:
                history_column = Column(
                    column.name, column.type, index=column.index, nullable=column.nullable
                )
                history_column = _update_history_col(column, history_column)
                super_history_table.append_column(history_column)

    if super_history_mapper:
        bases = (super_history_mapper.class_,)
        if history_table is not None:
            properties["changed"] = (history_table.c.changed,) + tuple(
                super_history_mapper.attrs.changed.columns
            )
    else:
        bases = local_mapper.base_mapper.class_.__bases__

    # set the "active_history" flag
    # on on column-mapped attributes so that the old version
    # of the info is always loaded (currently sets it on all attributes)
    new_foreign_keys = set()
    for prop in local_mapper.iterate_properties:
        prop.active_history = True
        if isinstance(prop, RelationshipProperty):
            columns = [fk.key for fk in getattr(prop, "_user_defined_foreign_keys")]
            foreign_key_columns = []
            for fk_key in columns:
                for col in cols:
                    if hasattr(col, "key") and getattr(col, "key", None) == fk_key:
                        foreign_key_columns.append(col)
                        new_foreign_keys = new_foreign_keys.union(col.foreign_keys)
    history_table.foreign_keys = new_foreign_keys
    versioned_cls = type.__new__(
        type,
        "%sHistory" % cls.__name__,
        bases,
        {
            "_history_mapper_configured": True,
            "__table__": history_table,
            "__mapper_args__": dict(
                inherits=super_history_mapper,
                polymorphic_identity=local_mapper.polymorphic_identity,
                polymorphic_on=polymorphic_on,
                properties=properties,
            ),
        },
    )

    cls.__history_mapper__ = versioned_cls.__mapper__

    if not super_history_mapper:
        local_mapper.local_table.append_column(
            Column(
                "version",
                Integer,
                default=1,
                nullable=False,
                # Added so that when we add this column to a table, they all start at version 1
                server_default=text("1"),
            ),
            replace_existing=True,
        )
        local_mapper.add_property("version", local_mapper.local_table.c.version)
        if cls.use_mapper_versioning:
            local_mapper.version_id_col = local_mapper.local_table.c.version


class Versioned:
    use_mapper_versioning = False
    """if True, also assign the version column to be tracked by the mapper"""

    __table_args__ = {"sqlite_autoincrement": True}
    """Use sqlite_autoincrement, to ensure unique integer values
    are used for new rows even for rows that have been deleted."""

    def __init_subclass__(cls) -> None:
        insp = inspect(cls, raiseerr=False)

        if insp is not None:
            _history_mapper(insp)
        else:

            @event.listens_for(cls, "after_mapper_constructed")
            def _mapper_constructed(mapper, class_):
                _history_mapper(mapper)

        super().__init_subclass__()


def versioned_objects(iter_):
    for obj in iter_:
        if hasattr(obj, "__history_mapper__"):
            yield obj


def create_version(obj, session, deleted=False):
    obj_mapper = object_mapper(obj)
    history_mapper = obj.__history_mapper__
    history_cls = history_mapper.class_

    obj_state = attributes.instance_state(obj)

    attr = {}

    obj_changed = False

    for om, hm in zip(obj_mapper.iterate_to_root(), history_mapper.iterate_to_root()):
        if hm.single:
            continue

        for hist_col in hm.local_table.c:
            if _is_versioning_col(hist_col):
                continue

            obj_col = om.local_table.c[hist_col.key]

            # get the value of the
            # attribute based on the MapperProperty related to the
            # mapped column.  this will allow usage of MapperProperties
            # that have a different keyname than that of the mapped column.
            try:
                prop = obj_mapper.get_property_by_column(obj_col)
            except UnmappedColumnError:
                # in the case of single table inheritance, there may be
                # columns on the mapped table intended for the subclass only.
                # the "unmapped" status of the subclass column on the
                # base class is a feature of the declarative module.
                continue

            # expired object attributes and also deferred cols might not
            # be in the dict.  force it to load no matter what by
            # using getattr().
            if prop.key not in obj_state.dict:
                getattr(obj, prop.key)
            attr[prop.key] = getattr(obj, prop.key)
            a, u, d = attributes.get_history(obj, prop.key)

            if d or a:
                # changed
                obj_changed = True

    if not obj_changed:
        # not changed, but we have relationships.  OK
        # check those too
        for prop in obj_mapper.iterate_properties:
            if (
                isinstance(prop, RelationshipProperty)
                and attributes.get_history(
                    obj, prop.key, passive=attributes.PASSIVE_NO_INITIALIZE
                ).has_changes()
            ):
                for p in prop.local_columns:
                    if p.foreign_keys:
                        obj_changed = True
                        break
                if obj_changed is True:
                    break

    if not obj_changed and not deleted:
        return

    # Increment to new version
    if obj.version is None:
        obj.version = 1
    else:
        obj.version += 1
    attr["version"] = obj.version
    # Instantiate new row
    hist = history_cls()
    # Original code had this store the previous_attrs, but we want it to store the current_attrs
    for key, value in attr.items():
        if key == "updated_time":  # will be updated by the database
            value = None
        setattr(hist, key, value)
    session.add(hist)


def versioned_session(session: sessionmaker[Session]) -> None:
    @event.listens_for(session, "before_flush")
    def before_flush(session: Session, flush_context: object, instances: None) -> None:
        for obj in versioned_objects(session.new):
            create_version(obj, session)
        for obj in versioned_objects(session.dirty):
            create_version(obj, session)
        for obj in versioned_objects(session.deleted):
            create_version(obj, session, deleted=True)
